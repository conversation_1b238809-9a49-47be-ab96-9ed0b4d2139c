"use strict";
const path = require("path");
const defaultSettings = require("./src/settings.js");
const packageName = require("./package").name;
function resolve(dir) {
  return path.join(__dirname, dir);
}

const UselessFile = require("useless-files-webpack-plugin");

const name = defaultSettings.title || "管理平台"; // 标题
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const TerserPlugin = require("terser-webpack-plugin");
const productionGzipExtensions = ["js", "css", "svg", "gif"];
// vue.config.js 配置说明
// 官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径
  publicPath: "/charging-maintenance-ui",
  // publicPath: `/${packageName}`,
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: "dist",
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  // lintOnSave: process.env.NODE_ENV === 'development',
  lintOnSave: false,
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    port: 2266,
    compress: true, //启动devServer的Gzip压缩
    headers: {
      "Access-Control-Allow-Origin": "*",
    },
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target:
          "https://test-napi.bangdao-tech.com/charging-maintenance-server", //测试环境
        // target:
        // "http://localhost:9000/charging-maintenance-server",
        changeOrigin: true,
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: "",
        },
      },
    },
    disableHostCheck: true,
  },
  transpileDependencies: ["/@bangdao/buse-components-element/"],
  configureWebpack: (config) => {
    config.devtool = "source-map";
    config.name = name;
    config.resolve.alias["@"] = resolve("src");
    //  配置productionGzip-高级的方式
    // 配置参数详解
    // asset： 目标资源名称。 [file] 会被替换成原始资源。[path] 会被替换成原始资源的路径， [query] 会被替换成查询字符串。默认值是 "[path].gz[query]"。
    // algorithm： 可以是 function(buf, callback) 或者字符串。对于字符串来说依照 zlib 的算法(或者 zopfli 的算法)。默认值是 "gzip"。
    // test： 所有匹配该正则的资源都会被处理。默认值是全部资源。
    // threshold： 只有大小大于该值的资源会被处理。单位是 bytes。默认值是 0。
    // minRatio： 只有压缩率小于这个值的资源才会被处理。默认值是 0.8。
    config.plugins.push(
      new CompressionWebpackPlugin({
        filename: "[path].gz[query]",
        algorithm: "gzip",
        test: new RegExp("\\.(" + productionGzipExtensions.join("|") + ")$"),
        threshold: 0,
        minRatio: 0.8,
      })
    );
    config.optimization = {
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true,
              pure_funcs: ["console.log"], // 移除console
            },
          },
        }),
      ],
    };

    config.output.library = packageName;
    config.output.libraryTarget = "umd";
    config.output.jsonpFunction = `webpackJsonp_${packageName}`;
  },
  chainWebpack(config) {
    config.plugins["delete"]("preload"); // TODO: need test
    config.plugins["delete"]("prefetch"); // TODO: need test

    config.plugin("uselessFile").use(
      new UselessFile({
        root: "./src", // 项目目录
        out: "./fileList.json", // 输出文件列表
        clean: false, // 是否删除文件,
        exclude: /node_modules/, // 排除文件列表
      })
    );
    // set svg-sprite-loader
    config.module
      .rule("svg")
      .exclude.add(resolve("src/assets/icons"))
      .end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    // set preserveWhitespace
    config.module
      .rule("vue")
      .use("vue-loader")
      .loader("vue-loader")
      .tap((options) => {
        options.compilerOptions.preserveWhitespace = true;
        return options;
      })
      .end();

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === "development", (config) =>
        config.devtool("cheap-source-map")
      );

    config.when(process.env.NODE_ENV !== "development", (config) => {
      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial", // only package third parties that are initially dependent
          },
          elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      config.optimization.runtimeChunk("single");
    });
  },
  pluginOptions: {
    "style-resources-loader": {
      preProcessor: "less",
      patterns: [
        path.resolve(__dirname, "./src/assets/styles/element-variables.less"),
      ],
    },
  },
};
