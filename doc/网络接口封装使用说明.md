# 网络接口封装使用说明

## 概述

本项目基于Axios封装了统一的网络请求模块，提供了完整的请求拦截、响应处理、错误处理机制，支持数据加密解密、文件下载、权限验证等功能。

## 核心文件

### 主要文件结构
```
src/
├── utils/
│   ├── request.js          # 主要的Axios封装
│   ├── request_blob.js     # Blob文件请求封装
│   ├── auth.js            # 认证相关工具
│   └── encrypt/           # 加密解密工具
├── api/
│   ├── common.js          # 通用API接口
│   ├── login.js           # 登录相关接口
│   └── [业务模块]/        # 各业务模块API
```

## 请求封装配置

### 基础配置
```javascript
// src/utils/request.js
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,  // API基础路径
  timeout: 100000,                        // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
});
```

### 环境变量配置
```javascript
// 开发环境
VUE_APP_BASE_API = '/api'

// 生产环境  
VUE_APP_BASE_API = '/charging-maintenance-server'
```

## 请求拦截器

### 认证处理
```javascript
service.interceptors.request.use(config => {
  // 添加认证token
  if (getToken()) {
    config.headers.Authorization = 'Bearer ' + getToken();
  }
  
  // 处理加密请求
  if (config.headers.bdgatewayencryptiontype) {
    config.data = encyptData(config.data);
  }
  
  return config;
});
```

### 特殊请求处理
- **文件上传**: FormData类型请求不进行加密
- **文件下载**: responseType为blob的请求特殊处理
- **加密请求**: 根据header标识进行数据加密

## 响应拦截器

### 统一响应格式
```javascript
// 标准响应格式
{
  code: 10000,        // 业务状态码
  message: "success", // 响应消息
  data: {},          // 响应数据
  traceId: "xxx"     // 链路追踪ID
}
```

### 错误处理机制
```javascript
// 登录过期处理
if (code == 40000) {
  MessageBox.confirm('登录状态已过期，请重新登录', '系统提示')
    .then(() => {
      store.dispatch('FedLogOut').then(() => {
        location.reload();
      });
    });
}

// 业务错误处理
if (code != 10000) {
  Notification.error({
    title: content.message,
    message: 'traceId:' + content.traceId
  });
  return Promise.reject(content.message);
}
```

## API接口定义规范

### 基本结构
```javascript
// src/api/[模块名]/index.js
import request from '@/utils/request';

/**
 * 查询列表数据
 * @param {Object} query - 查询参数
 * @returns {Promise} 返回Promise对象
 */
export function getList(query) {
  return request({
    url: '/module/list',
    method: 'post',
    data: query
  });
}

/**
 * 新增数据
 * @param {Object} data - 新增数据
 */
export function addData(data) {
  return request({
    url: '/module/add',
    method: 'post',
    data
  });
}

/**
 * 更新数据
 * @param {Object} data - 更新数据
 */
export function updateData(data) {
  return request({
    url: '/module/update',
    method: 'put',
    data
  });
}

/**
 * 删除数据
 * @param {string|number} id - 数据ID
 */
export function deleteData(id) {
  return request({
    url: `/module/delete/${id}`,
    method: 'delete'
  });
}
```

### 命名规范
- **文件命名**: 使用kebab-case，如`user-manage.js`
- **函数命名**: 使用camelCase，动词+名词形式
  - `getList` - 获取列表
  - `getDetail` - 获取详情
  - `addData` - 新增数据
  - `updateData` - 更新数据
  - `deleteData` - 删除数据
  - `exportData` - 导出数据

## 特殊请求类型

### 文件下载
```javascript
// 普通文件下载
export function downloadFile(params) {
  return request({
    url: '/file/download',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// 需要获取文件名的下载
export function downloadWithFileName(params) {
  return request({
    url: '/file/download',
    method: 'get',
    params,
    responseType: 'blob',
    needFileName: true  // 特殊标识
  });
}
```

### 文件上传
```javascript
export function uploadFile(formData) {
  return request({
    url: '/file/upload',
    method: 'post',
    data: formData,  // FormData对象
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
```

### 跳过拦截器
```javascript
export function getDataWithoutIntercept(params) {
  return request({
    url: '/data/get',
    method: 'get',
    params,
    skipIntercept: true  // 跳过响应拦截器
  });
}
```

## 加密解密机制

### 请求加密
```javascript
// 需要加密的请求
export function secureRequest(data) {
  return request({
    url: '/secure/api',
    method: 'post',
    data,
    headers: {
      'bdgatewayencryptiontype': 'AES',           // 加密类型
      'bdgatewayresponseneedencryptiontype': 'AES' // 响应解密类型
    }
  });
}
```

### 加密工具
```javascript
// src/utils/encrypt/index.js
export const encryptHeader = {
  'bdgatewayencryptiontype': 'AES'
};

export const decryptHeader = {
  'bdgatewayresponseneedencryptiontype': 'AES'
};

export function encyptData(data) {
  // 数据加密逻辑
}

export function decyptData(data) {
  // 数据解密逻辑
}
```

## 错误处理

### 网络错误
```javascript
service.interceptors.response.use(
  response => {
    // 成功响应处理
  },
  error => {
    console.log('网络错误:', error);
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);
```

### 业务错误
- **40000**: 登录过期，自动跳转登录页
- **非10000**: 业务错误，显示错误提示
- **网络错误**: 显示网络异常提示

## 使用示例

### 在组件中使用
```javascript
// 在Vue组件中使用API
import { getList, addData, updateData, deleteData } from '@/api/user';

export default {
  methods: {
    async loadData() {
      try {
        this.loading = true;
        const res = await getList(this.queryParams);
        this.tableData = res.data;
        this.total = res.total;
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async handleAdd(formData) {
      try {
        await addData(formData);
        this.$message.success('新增成功');
        this.loadData();
      } catch (error) {
        // 错误已在拦截器中处理
      }
    }
  }
};
```

### 全局方法
```javascript
// main.js中挂载的全局方法
Vue.prototype.download = download;  // 文件下载方法

// 在组件中使用
this.download('/api/export', params, '导出文件.xlsx');
```

## 最佳实践

### 1. API模块化
- 按业务模块组织API文件
- 每个模块独立的API文件
- 统一的导入导出方式

### 2. 错误处理
- 在API层面进行基础错误处理
- 在组件层面进行业务错误处理
- 提供用户友好的错误提示

### 3. 加载状态
- 合理使用loading状态
- 避免重复请求
- 提供请求取消机制

### 4. 缓存策略
- 合理使用请求缓存
- 避免不必要的重复请求
- 及时清理过期缓存

### 5. 安全考虑
- 敏感数据请求使用加密
- 合理设置请求超时时间
- 避免在URL中传递敏感信息
