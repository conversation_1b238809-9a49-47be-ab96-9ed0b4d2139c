# 数据流和状态管理使用说明

## 概述

本项目采用Vuex作为状态管理方案，实现了模块化的状态管理架构。通过合理的数据流设计，确保了应用状态的可预测性和可维护性。

## Vuex架构

### 目录结构
```
src/store/
├── index.js              # Store主入口
├── getters.js           # 全局getters
└── modules/             # 状态模块
    ├── app.js          # 应用基础状态
    ├── user.js         # 用户状态
    ├── dataDict.js     # 数据字典状态
    ├── permission.js   # 权限状态
    ├── tagsView.js     # 标签页状态
    ├── settings.js     # 系统设置状态
    ├── scanCode.js     # 扫码状态
    └── logicFlow.js    # 逻辑流程状态
```

### Store配置
```javascript
// src/store/index.js
import Vue from 'vue';
import Vuex from 'vuex';
import app from './modules/app';
import user from './modules/user';
import dataDict from './modules/dataDict';
import tagsView from './modules/tagsView';
import permission from './modules/permission';
import scanCode from './modules/scanCode';
import settings from './modules/settings';
import logicFlow from './modules/logicFlow';
import getters from './getters';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    scanCode,
    logicFlow,
    dataDict
  },
  getters
});

export default store;
```

## 核心状态模块

### 1. 用户状态模块 (user.js)
管理用户登录状态、用户信息、权限等。

```javascript
// 主要状态
state: {
  token: getToken(),           // 用户token
  name: '',                   // 用户名
  avatar: '',                 // 用户头像
  roles: [],                  // 用户角色
  permissions: []             // 用户权限
}

// 主要mutations
SET_TOKEN,                    // 设置token
SET_NAME,                     // 设置用户名
SET_AVATAR,                   // 设置头像
SET_ROLES,                    // 设置角色
SET_PERMISSIONS               // 设置权限

// 主要actions
Login,                        // 用户登录
GetInfo,                      // 获取用户信息
LogOut,                       // 用户登出
FedLogOut                     // 前端登出
```

### 2. 应用状态模块 (app.js)
管理应用的基础状态，如侧边栏、设备类型等。

```javascript
state: {
  sidebar: {
    opened: true,             // 侧边栏是否展开
    withoutAnimation: false   // 是否禁用动画
  },
  device: 'desktop',          // 设备类型
  size: 'medium'              // 组件尺寸
}
```

### 3. 权限状态模块 (permission.js)
管理路由权限和菜单权限。

```javascript
state: {
  routes: [],                 // 所有路由
  addRoutes: [],             // 动态添加的路由
  defaultRoutes: [],         // 默认路由
  topbarRouters: [],         // 顶部路由
  sidebarRouters: []         // 侧边栏路由
}
```

### 4. 数据字典模块 (dataDict.js)
管理系统数据字典缓存。

```javascript
state: {
  dict: new Array()           // 字典数据缓存
}

// 主要actions
loadDict,                     // 加载字典
cleanDict                     // 清理字典
```

### 5. 标签页模块 (tagsView.js)
管理页面标签页状态。

```javascript
state: {
  visitedViews: [],          // 已访问的视图
  cachedViews: []            // 缓存的视图
}

// 主要actions
addView,                      // 添加视图
addVisitedView,              // 添加已访问视图
delView,                     // 删除视图
delOthersViews,              // 删除其他视图
delAllViews                  // 删除所有视图
```

## 全局Getters

```javascript
// src/store/getters.js
const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  permission_routes: state => state.permission.routes,
  topbarRouters: state => state.permission.topbarRouters,
  defaultRoutes: state => state.permission.defaultRoutes,
  sidebarRouters: state => state.permission.sidebarRouters,
};

export default getters;
```

## 数据流向

### 1. 用户登录流程
```
用户输入 → Login Action → API请求 → 设置Token → 获取用户信息 → 设置权限 → 生成路由
```

### 2. 权限验证流程
```
路由跳转 → 权限守卫 → 检查Token → 获取用户权限 → 生成可访问路由 → 渲染页面
```

### 3. 数据字典流程
```
组件需要字典 → 检查缓存 → 缓存不存在则请求API → 存储到Store → 返回数据
```

## 组件间数据传递

### 1. 父子组件通信
```javascript
// 父组件传递数据给子组件
<child-component :prop-data="parentData" @child-event="handleChildEvent" />

// 子组件接收数据并触发事件
props: ['propData'],
methods: {
  handleClick() {
    this.$emit('child-event', data);
  }
}
```

### 2. 兄弟组件通信
```javascript
// 使用EventBus
// main.js
Vue.prototype.$EventBus = new Vue();

// 组件A发送事件
this.$EventBus.$emit('event-name', data);

// 组件B监听事件
this.$EventBus.$on('event-name', (data) => {
  // 处理数据
});

// 组件销毁时移除监听
beforeDestroy() {
  this.$EventBus.$off('event-name');
}
```

### 3. 跨层级组件通信
```javascript
// 使用Vuex
// 在组件中使用mapState, mapGetters, mapActions
import { mapState, mapGetters, mapActions } from 'vuex';

computed: {
  ...mapState(['user', 'app']),
  ...mapGetters(['sidebar', 'token'])
},
methods: {
  ...mapActions(['Login', 'LogOut'])
}
```

## 状态管理最佳实践

### 1. 模块化设计
- 按功能领域划分模块
- 每个模块职责单一
- 避免模块间的强耦合

### 2. 状态设计原则
- 保持状态的最小化
- 避免冗余状态
- 状态结构扁平化

### 3. 异步操作处理
```javascript
// 在actions中处理异步操作
actions: {
  async fetchUserInfo({ commit }) {
    try {
      const response = await getUserInfo();
      commit('SET_USER_INFO', response.data);
      return response.data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }
}
```

### 4. 错误处理
```javascript
// 统一的错误处理
actions: {
  async login({ commit }, userInfo) {
    try {
      const response = await login(userInfo);
      commit('SET_TOKEN', response.token);
      setToken(response.token);
      return response;
    } catch (error) {
      // 清理状态
      commit('SET_TOKEN', '');
      removeToken();
      throw error;
    }
  }
}
```

## 性能优化

### 1. 计算属性缓存
```javascript
// 使用computed缓存复杂计算
computed: {
  filteredList() {
    return this.list.filter(item => item.status === 'active');
  }
}
```

### 2. 避免不必要的响应式
```javascript
// 对于不需要响应式的数据，使用Object.freeze
data() {
  return {
    staticData: Object.freeze(largeArray)
  }
}
```

### 3. 合理使用watch
```javascript
// 使用immediate和deep选项
watch: {
  searchQuery: {
    handler(newVal) {
      this.fetchData(newVal);
    },
    immediate: true,
    deep: true
  }
}
```

## 调试工具

### 1. Vue DevTools
- 安装Vue DevTools浏览器扩展
- 查看组件树和状态
- 时间旅行调试

### 2. Vuex调试
```javascript
// 开发环境启用严格模式
const store = new Vuex.Store({
  strict: process.env.NODE_ENV !== 'production',
  // ...
});
```

### 3. 日志记录
```javascript
// 在mutations中添加日志
mutations: {
  SET_USER_INFO(state, userInfo) {
    console.log('Setting user info:', userInfo);
    state.userInfo = userInfo;
  }
}
```

## 常见问题和解决方案

### 1. 状态丢失问题
- 页面刷新导致状态丢失：使用localStorage持久化关键状态
- 路由跳转状态丢失：检查是否正确使用Vuex

### 2. 性能问题
- 状态更新频繁：合并多个状态更新
- 组件重复渲染：使用computed属性缓存

### 3. 内存泄漏
- 事件监听器未移除：在beforeDestroy中清理
- 定时器未清理：及时清理setTimeout和setInterval
