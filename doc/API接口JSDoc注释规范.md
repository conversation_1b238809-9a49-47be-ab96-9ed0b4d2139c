# API接口JSDoc注释规范

## 概述

本文档规范了项目中API接口函数的JSDoc注释标准，确保代码文档的一致性和可读性，提升开发效率和代码质量。

## 基本原则

1. **中文注释**：所有注释内容使用中文描述
2. **字段一致性**：字段名称必须与后端API接口文档完全一致
3. **类型安全**：使用TypeScript风格的类型注解
4. **完整性**：包含函数描述、参数、返回值和使用示例

## JSDoc注释格式标准

### 基本结构

```javascript
/**
 * 函数功能描述（中文）
 * @param {类型} 参数名 - 参数描述
 * @param {类型} [可选参数名] - 可选参数描述
 * @returns {Promise<Object>} 返回值描述
 * @returns {类型} returns.字段名 - 字段描述
 * @example
 * // 使用示例
 * const result = await apiFunction(params);
 */
```

### 函数描述规范

- 使用中文简洁描述函数的主要功能
- 说明函数的业务用途和应用场景
- 对于复杂功能，可以分行详细说明

```javascript
/**
 * 分页查询服务商信息
 * 支持多种筛选条件的组合查询，包括服务商名称、商户号等
 */
```

### 参数注释规范

#### 参数类型标注

- `{string}` - 字符串类型
- `{number}` - 数字类型
- `{boolean}` - 布尔类型
- `{Object}` - 对象类型
- `{Array<类型>}` - 数组类型
- `{FormData}` - 表单数据类型
- `{File}` - 文件类型

#### 必填/可选字段标记

- 必填参数：`@param {string} fieldName - 字段描述`
- 可选参数：`@param {string} [fieldName] - 字段描述`
- 带默认值：`@param {number} [pageNum=1] - 页码`

#### 参数描述格式

```javascript
/**
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {string} [data.serviceProvider] - 服务商名称（模糊查询）
 * @param {Array<number>} [data.orgNoList] - 机构编号列表
 * @param {string} [data.commissionStartTime] - 佣金开始时间（格式：YYYY-MM-DD）
 */
```

### 返回值注释规范

#### 标准响应格式

所有API接口都应遵循统一的返回值格式：

```javascript
/**
 * @returns {Promise<Object>} 返回查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>|Object|string} returns.data - 业务数据
 * @returns {number} [returns.pageNum] - 当前页码（分页接口）
 * @returns {number} [returns.pageSize] - 每页条数（分页接口）
 * @returns {number} [returns.total] - 总记录数（分页接口）
 * @returns {string} returns.traceId - 请求追踪ID
 */
```

#### 枚举值文档化

对于有固定取值的字段，应列出所有可能的值：

```javascript
/**
 * @param {string} [data.status] - 状态（正常/暂停/停用）
 * @returns {string} returns.data.status - 状态值
 *   - '0': 正常
 *   - '1': 暂停  
 *   - '2': 停用
 */
```

### 使用示例规范

每个API函数都应提供实际的使用示例：

```javascript
/**
 * @example
 * // 查询第一页数据
 * const result = await serviceProviderApi.list({
 *   pageNum: 1,
 *   pageSize: 10,
 *   serviceProvider: '支付宝'
 * });
 * 
 * @example
 * // 批量删除
 * const result = await serviceProviderApi.delete({ 
 *   id: [1, 2, 3] 
 * });
 */
```

## 常见API操作注释模板

### 分页查询接口

```javascript
/**
 * 分页查询[业务对象]信息
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {string} [data.关键字段] - 关键字段描述（模糊查询）
 * @returns {Promise<Object>} 返回分页查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>} returns.data - [业务对象]信息列表
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 查询第一页数据
 * const result = await api.list({
 *   pageNum: 1,
 *   pageSize: 10
 * });
 */
```

### 新增接口

```javascript
/**
 * 新增[业务对象]信息
 * @param {Object} data - [业务对象]信息数据
 * @param {string} data.必填字段 - 必填字段描述（必填）
 * @param {string} [data.可选字段] - 可选字段描述
 * @returns {Promise<Object>} 返回新增结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string|number} returns.data - 新增记录的ID或操作结果
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 新增[业务对象]信息
 * const result = await api.add({
 *   必填字段: '值',
 *   可选字段: '值'
 * });
 */
```

### 编辑接口

```javascript
/**
 * 编辑[业务对象]信息
 * @param {Object} data - [业务对象]信息数据
 * @param {number} data.id - [业务对象]信息ID（必填）
 * @param {string} [data.字段名] - 字段描述
 * @returns {Promise<Object>} 返回编辑结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 操作结果信息
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 编辑[业务对象]信息
 * const result = await api.update({
 *   id: 1,
 *   字段名: '新值'
 * });
 */
```

### 删除接口

```javascript
/**
 * 删除[业务对象]信息
 * @param {Object} data - 删除参数
 * @param {number|Array<number>} data.id - 要删除的[业务对象]信息ID，支持单个ID或ID数组
 * @returns {Promise<Object>} 返回删除结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 删除结果信息
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 删除单个[业务对象]信息
 * const result = await api.delete({ id: 1 });
 * 
 * // 批量删除[业务对象]信息
 * const result = await api.delete({ id: [1, 2, 3] });
 */
```

### 导出接口

```javascript
/**
 * 导出[业务对象]信息Excel文件
 * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
 * @param {string} [data.筛选字段] - 筛选字段描述
 * @returns {Promise<Object>} 返回导出结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 导出文件下载链接或文件内容
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 导出所有[业务对象]信息
 * const result = await api.export({
 *   筛选字段: '筛选值'
 * });
 */
```

### 导入接口

```javascript
/**
 * 导入[业务对象]信息Excel文件
 * @param {FormData} data - 包含文件的FormData对象
 * @param {File} data.file - 要导入的Excel文件（必填）
 * @returns {Promise<Object>} 返回导入结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
 * @returns {string} returns.data - 导入结果详情
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 导入Excel文件
 * const formData = new FormData();
 * formData.append('file', file);
 * const result = await api.import(formData);
 */
```

### 获取下拉列表接口

```javascript
/**
 * 获取[业务对象]相关的下拉列表数据
 * 用于表单选择器的数据源
 * @returns {Promise<Object>} 返回下拉列表数据
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
 * @returns {Array<string>} [returns.data.字段名1] - 字段1选项列表
 * @returns {Array<string>} [returns.data.字段名2] - 字段2选项列表
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 获取下拉列表数据
 * const result = await api.getDropLists();
 * // result.data = {
 * //   字段名1: ['选项1', '选项2'],
 * //   字段名2: ['选项A', '选项B']
 * // }
 */
```

## 注意事项

1. **字段命名一致性**：确保注释中的字段名与后端API文档完全一致
2. **类型准确性**：根据实际数据类型选择正确的JSDoc类型标注
3. **示例实用性**：提供真实可用的代码示例，避免虚假示例
4. **更新及时性**：API接口变更时及时更新对应的JSDoc注释
5. **格式统一性**：严格按照本规范的格式编写注释，保持项目代码风格一致

## IDE支持

正确的JSDoc注释可以为开发者提供：
- 智能代码提示
- 参数类型检查
- 自动文档生成
- 更好的代码可读性

遵循本规范可以最大化发挥IDE的智能提示功能，提升开发效率。
