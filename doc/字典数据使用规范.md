# 字典数据使用规范

## 概述

本文档规范了项目中字典数据的使用方式，包括`getDicts`方法的调用规范、字典数据的命名约定和最佳实践，确保字典数据使用的一致性和规范性。

## 字典系统架构

### 核心组件

1. **API 层**：`src/api/system/dict/data.js` - 字典数据接口
2. **Store 层**：`src/store/modules/dataDict.js` - 字典数据缓存
3. **全局方法**：`main.js` - 全局挂载`getDicts`方法
4. **工具函数**：`src/utils/comm.js` - `selectDictLabel`字典翻译方法

### 数据流程

```
Vue组件 → getDicts() → Store缓存检查 → API请求 → 数据缓存 → 组件使用
```

## getDicts 方法使用规范

### 基本调用格式

```javascript
// 标准调用格式
this.getDicts("字典类型").then((response) => {
  this.字典变量名 = response.data;
});

// 使用async/await
async created() {
  const response = await this.getDicts("字典类型");
  this.字典变量名 = response.data;
}
```

### 字典数据结构

字典数据返回的标准格式：

```javascript
// response.data 结构
[
  {
    dictLabel: "显示文本", // 用于界面显示
    dictValue: "存储值", // 用于数据存储和传输
    dictCode: "字典编码", // 字典项唯一标识
    status: "0", // 状态：0-正常，1-停用
    remark: "备注信息", // 备注说明
  },
];
```

### 命名约定

#### 字典变量命名规范

- **状态类字典**：`statusOptions`、`orderStatusOptions`
- **类型类字典**：`typeOptions`、`businessTypeOptions`
- **来源类字典**：`sourceOptions`、`originOptions`
- **级别类字典**：`levelOptions`、`riskLevelOptions`
- **部门类字典**：`deptOptions`、`supportDeptOptions`

#### 字典类型命名规范

- 使用下划线分隔：`sys_normal_status`
- 业务前缀 + 具体类型：`ledger_order_status`
- 模块前缀 + 业务类型：`cm_station_type`

## 在 Vue 组件中的使用方式

### 1. 在 created 生命周期中获取

```javascript
export default {
  data() {
    return {
      statusOptions: [], // 状态字典
      typeOptions: [], // 类型字典
      sourceOptions: [], // 来源字典
    };
  },
  created() {
    // 获取状态字典
    this.getDicts("sys_normal_status").then((response) => {
      this.statusOptions = response.data;
    });

    // 获取业务类型字典
    this.getDicts("business_type").then((response) => {
      this.typeOptions = response.data;
    });

    // 获取来源字典
    this.getDicts("data_source").then((response) => {
      this.sourceOptions = response.data;
    });
  },
};
```

### 2. 使用 Promise.all 并行获取多个字典

```javascript
created() {
  Promise.all([
    this.getDicts("sys_normal_status"),
    this.getDicts("business_type"),
    this.getDicts("data_source")
  ]).then(([statusRes, typeRes, sourceRes]) => {
    this.statusOptions = statusRes.data;
    this.typeOptions = typeRes.data;
    this.sourceOptions = sourceRes.data;

    // 字典加载完成后执行其他初始化操作
    this.initConfig();
    this.getList();
  });
}
```

### 3. 在表单配置中使用字典

```javascript
// 筛选条件配置
initConfig() {
  this.config = [
    {
      key: "status",
      title: "状态",
      type: "select",
      placeholder: "请选择状态",
      options: this.statusOptions  // 使用字典数据
    },
    {
      key: "businessType",
      title: "业务类型",
      type: "select",
      placeholder: "请选择业务类型",
      options: this.typeOptions,
      optionValue: "dictValue",    // 指定值字段
      optionLabel: "dictLabel"     // 指定显示字段
    }
  ];
}
```

### 4. 在表格列中使用字典翻译

```javascript
// 表格列配置
columns: [
  {
    field: "status",
    title: "状态",
    formatter: this.statusFormat  // 使用格式化函数
  }
],

// 字典翻译方法
methods: {
  statusFormat(row) {
    return this.selectDictLabel(this.statusOptions, row.status);
  }
}
```

## 字典数据在不同组件中的应用

### 1. AutoFilters 组件中使用

```javascript
// 筛选条件配置
filterOptions: {
  config: [
    {
      key: "status",
      title: "状态",
      type: "select",
      options: [], // 在created中赋值
      placeholder: "请选择状态"
    }
  ]
},

created() {
  this.getDicts("sys_normal_status").then((response) => {
    this.filterOptions.config[0].options = response.data;
  });
}
```

### 2. DynamicForm 组件中使用

```javascript
// 表单配置
modalConfig: {
  formConfig: [
    {
      key: "status",
      title: "状态",
      type: "select",
      options: [], // 在created中赋值
      rules: [{ required: true, message: "请选择状态", trigger: "change" }]
    }
  ]
},

created() {
  this.getDicts("sys_normal_status").then((response) => {
    this.modalConfig.formConfig[0].options = response.data;
  });
}
```

### 3. Element UI 组件中使用

```vue
<template>
  <!-- 下拉选择器 -->
  <el-select v-model="form.status" placeholder="请选择状态">
    <el-option
      v-for="item in statusOptions"
      :key="item.dictValue"
      :label="item.dictLabel"
      :value="item.dictValue"
    >
    </el-option>
  </el-select>

  <!-- 单选按钮组 -->
  <el-radio-group v-model="form.type">
    <el-radio
      v-for="item in typeOptions"
      :key="item.dictValue"
      :label="item.dictValue"
    >
      {{ item.dictLabel }}
    </el-radio>
  </el-radio-group>
</template>
```

## 字典翻译和格式化

### 使用 selectDictLabel 方法

```javascript
// 在methods中定义格式化方法
methods: {
  // 状态格式化
  statusFormat(row) {
    return this.selectDictLabel(this.statusOptions, row.status);
  },

  // 类型格式化
  typeFormat(row) {
    return this.selectDictLabel(this.typeOptions, row.businessType);
  },

  // 通用格式化方法
  formatDictValue(dictOptions, value) {
    return this.selectDictLabel(dictOptions, value);
  }
}
```

### 在计算属性中使用

```javascript
computed: {
  // 格式化后的状态文本
  statusText() {
    return this.selectDictLabel(this.statusOptions, this.form.status);
  },

  // 格式化表格数据
  formattedTableData() {
    return this.tableData.map(item => ({
      ...item,
      statusText: this.selectDictLabel(this.statusOptions, item.status),
      typeText: this.selectDictLabel(this.typeOptions, item.type)
    }));
  }
}
```

## 字典数据缓存和性能优化

### 1. Store 缓存机制

项目使用 Vuex Store 自动缓存字典数据：

```javascript
// src/store/modules/dataDict.js
async getDicts({ commit, state }, codeTypes) {
  let needRequest = false;
  if (!state[codeTypes]) {
    needRequest = true; // 缓存中不存在才请求
  }
  if (needRequest) {
    const res = await getDicts(codeTypes);
    commit('SET_DICTIONARY', { name: codeTypes, data: res?.data });
  }
  return Promise.resolve(state[codeTypes]);
}
```

### 2. 组件级缓存优化

```javascript
// 使用mixin统一管理字典
// src/views/engineerProject/dictMixin.js
const dictMaps = {
  project_business_type: [],
  project_status: [],
  work_apply_status: [],
};

export default {
  data() {
    return {
      allOptions: {},
    };
  },
  mounted() {
    const dictsArr = Object.keys(dictMaps);
    for (let index = 0; index < dictsArr.length; index++) {
      const dictName = dictsArr[index];
      this.getDicts(dictName).then((response) => {
        this.$set(this.allOptions, dictName, response.data);
      });
    }
  },
};
```

## 错误处理和异常情况

### 1. 网络请求失败处理

```javascript
created() {
  this.getDicts("sys_normal_status")
    .then((response) => {
      this.statusOptions = response.data;
    })
    .catch((error) => {
      console.error("获取状态字典失败:", error);
      this.$message.error("获取字典数据失败，请刷新页面重试");
      // 设置默认值或空数组
      this.statusOptions = [];
    });
}
```

### 2. 字典数据为空的处理

```javascript
methods: {
  statusFormat(row) {
    if (!this.statusOptions || this.statusOptions.length === 0) {
      return row.status; // 返回原始值
    }
    return this.selectDictLabel(this.statusOptions, row.status);
  }
}
```

### 3. 字典值不存在的处理

```javascript
// 增强版selectDictLabel
selectDictLabelSafe(datas, value, defaultText = '未知') {
  if (!datas || !Array.isArray(datas)) {
    return defaultText;
  }

  const found = datas.find(item => item.dictValue === String(value));
  return found ? found.dictLabel : defaultText;
}
```

## 与 API 接口 JSDoc 注释的配合使用

### 在 API 注释中说明字典字段

```javascript
/**
 * 查询工单列表
 * @param {Object} data - 查询参数
 * @param {string} [data.status] - 工单状态（使用字典：ledger_order_status）
 * @param {string} [data.source] - 工单来源（使用字典：ledger_demand_source）
 * @returns {Promise<Object>} 返回查询结果
 * @returns {Array<Object>} returns.data - 工单列表
 * @returns {string} returns.data[].status - 工单状态（对应字典：ledger_order_status）
 */
```

## 常见字典类型和用法

### 系统级字典

```javascript
// 通用状态字典
this.getDicts("sys_normal_status").then((response) => {
  this.statusOptions = response.data;
  // 数据格式：[{dictLabel: "正常", dictValue: "0"}, {dictLabel: "停用", dictValue: "1"}]
});

// 显示隐藏字典
this.getDicts("sys_show_hide").then((response) => {
  this.visibleOptions = response.data;
  // 数据格式：[{dictLabel: "显示", dictValue: "0"}, {dictLabel: "隐藏", dictValue: "1"}]
});
```

### 业务级字典

```javascript
// 工单相关字典
this.getDicts("ledger_order_status").then((response) => {
  this.orderStatusOptions = response.data;
});

this.getDicts("ledger_demand_source").then((response) => {
  this.demandSourceOptions = response.data;
});

// 项目相关字典
this.getDicts("cm_project_status").then((response) => {
  this.projectStatusOptions = response.data;
});

this.getDicts("cm_station_type").then((response) => {
  this.stationTypeOptions = response.data;
});

// 结算相关字典
this.getDicts("st_diff_result").then((response) => {
  this.diffResultOptions = response.data;
});

this.getDicts("st_bd_bill_status").then((response) => {
  this.billStatusOptions = response.data;
});
```

### 风险管理字典

```javascript
// 风险类型和级别
this.getDicts("risk_type").then((response) => {
  this.riskTypeOptions = response.data;
});

this.getDicts("risk_level").then((response) => {
  this.riskLevelOptions = response.data;
});

this.getDicts("risk_status").then((response) => {
  this.riskStatusOptions = response.data;
});
```

## 高级用法和技巧

### 1. 字典数据预处理

```javascript
created() {
  this.getDicts("sys_normal_status").then((response) => {
    // 添加"全部"选项
    this.statusOptions = [
      { dictLabel: "全部", dictValue: "" },
      ...response.data
    ];
  });
}
```

### 2. 字典数据转换

```javascript
created() {
  this.getDicts("business_type").then((response) => {
    // 转换为Element UI需要的格式
    this.typeOptions = response.data.map(item => ({
      label: item.dictLabel,
      value: item.dictValue,
      disabled: item.status === '1' // 停用状态禁选
    }));
  });
}
```

### 3. 条件字典加载

```javascript
methods: {
  async loadDictsByCondition(condition) {
    if (condition === 'project') {
      await this.getDicts("cm_project_status");
      await this.getDicts("project_business_type");
    } else if (condition === 'order') {
      await this.getDicts("ledger_order_status");
      await this.getDicts("ledger_demand_source");
    }
  }
}
```

### 4. 字典数据联动

```javascript
watch: {
  'form.parentType': {
    handler(newVal) {
      if (newVal) {
        // 根据父级类型加载子级字典
        this.getDicts(`sub_type_${newVal}`).then((response) => {
          this.subTypeOptions = response.data;
        });
      } else {
        this.subTypeOptions = [];
      }
    },
    immediate: true
  }
}
```

## 字典数据在复杂场景中的应用

### 1. 多级联动选择器

```javascript
// 省市区联动示例
data() {
  return {
    provinceOptions: [],
    cityOptions: [],
    areaOptions: [],
    form: {
      province: '',
      city: '',
      area: ''
    }
  };
},

watch: {
  'form.province'(newVal) {
    this.form.city = '';
    this.form.area = '';
    this.cityOptions = [];
    this.areaOptions = [];

    if (newVal) {
      this.getDicts(`city_${newVal}`).then((response) => {
        this.cityOptions = response.data;
      });
    }
  },

  'form.city'(newVal) {
    this.form.area = '';
    this.areaOptions = [];

    if (newVal) {
      this.getDicts(`area_${newVal}`).then((response) => {
        this.areaOptions = response.data;
      });
    }
  }
}
```

### 2. 动态表单字典

```javascript
// 根据表单类型动态加载字典
methods: {
  async initFormDicts(formType) {
    const dictMap = {
      'project': ['cm_project_status', 'project_business_type'],
      'order': ['ledger_order_status', 'ledger_demand_source'],
      'settlement': ['st_diff_result', 'st_bd_bill_status']
    };

    const dictTypes = dictMap[formType] || [];
    const promises = dictTypes.map(type => this.getDicts(type));

    try {
      const results = await Promise.all(promises);
      dictTypes.forEach((type, index) => {
        this[`${type}Options`] = results[index].data;
      });
    } catch (error) {
      console.error('加载字典失败:', error);
    }
  }
}
```

### 3. 字典数据国际化

```javascript
// 支持多语言的字典处理
computed: {
  localizedStatusOptions() {
    return this.statusOptions.map(item => ({
      ...item,
      dictLabel: this.$t(`status.${item.dictValue}`) || item.dictLabel
    }));
  }
}
```

## 性能优化进阶技巧

### 1. 懒加载字典

```javascript
// 只在需要时加载字典
methods: {
  async ensureDictLoaded(dictType) {
    if (!this[`${dictType}Options`] || this[`${dictType}Options`].length === 0) {
      const response = await this.getDicts(dictType);
      this[`${dictType}Options`] = response.data;
    }
    return this[`${dictType}Options`];
  },

  async openDialog() {
    // 打开对话框时才加载字典
    await this.ensureDictLoaded('sys_normal_status');
    this.dialogVisible = true;
  }
}
```

### 2. 字典数据预加载

```javascript
// 在应用启动时预加载常用字典
// src/store/modules/dataDict.js
const actions = {
  async preloadCommonDicts({ dispatch }) {
    const commonDicts = [
      "sys_normal_status",
      "sys_show_hide",
      "ledger_order_status",
    ];

    await Promise.all(
      commonDicts.map((dictType) => dispatch("getDicts", dictType))
    );
  },
};
```

## 最佳实践总结

1. **统一命名**：字典变量使用`xxxOptions`格式命名
2. **及时加载**：在`created`生命周期中获取字典数据
3. **错误处理**：添加适当的错误处理和默认值
4. **性能优化**：利用 Store 缓存机制，避免重复请求
5. **代码复用**：使用 mixin 统一管理相关字典
6. **文档说明**：在 API 注释中标明字典类型和用途
7. **类型安全**：在 TypeScript 项目中定义字典数据类型
8. **懒加载**：对于不常用的字典采用懒加载策略
9. **数据预处理**：根据业务需要对字典数据进行适当转换
10. **联动处理**：合理处理字典间的联动关系

## 常见问题和解决方案

### 1. 字典数据加载时机问题

**问题**：组件渲染时字典数据还未加载完成
**解决方案**：使用 v-if 控制组件渲染时机

```javascript
<template>
  <el-select v-if="statusOptions.length > 0" v-model="form.status">
    <el-option v-for="item in statusOptions" :key="item.dictValue"
               :label="item.dictLabel" :value="item.dictValue">
    </el-option>
  </el-select>
</template>
```

### 2. 字典翻译显示空值问题

**问题**：selectDictLabel 返回空字符串
**解决方案**：检查数据类型匹配

```javascript
// 确保类型一致
statusFormat(row) {
  return this.selectDictLabel(this.statusOptions, String(row.status));
}
```

### 3. 字典缓存更新问题

**问题**：字典数据更新后页面显示旧数据
**解决方案**：清除缓存或强制刷新

```javascript
methods: {
  async refreshDict(dictType) {
    // 清除Store缓存
    this.$store.commit('dataDict/CLEAR_DICT', dictType);
    // 重新获取
    const response = await this.getDicts(dictType);
    this[`${dictType}Options`] = response.data;
  }
}
```

遵循这些规范和最佳实践可以确保字典数据使用的一致性，提升代码质量和维护效率。
