# BusCrud 组件使用指南

## 组件概述

BusCrud 是项目中最核心的业务组件，来自`@bangdao/buse-components-element`包。它集成了表格展示、筛选查询、分页、弹窗表单等功能，是后台管理系统中增删改查操作的标准解决方案。

## 核心特性

- **一体化 CRUD 操作**: 集成增删改查所有功能
- **灵活的配置系统**: 通过配置对象控制组件行为
- **丰富的插槽支持**: 支持自定义内容和布局
- **完整的事件系统**: 提供生命周期事件钩子
- **权限控制集成**: 内置权限验证机制
- **批量操作支持**: 支持多选和批量处理
- **响应式设计**: 适配不同屏幕尺寸

## 基础使用

### 1. 组件引入

```javascript
// 已在main.js中全局注册，可直接使用
import { BuseCrud } from "@bangdao/buse-components-element";
Vue.use(BuseCrud);
```

### 2. 基础模板

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :tableProps="tableProps"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @loadData="loadData"
  >
    <template #toolbar_buttons>
      <el-button icon="el-icon-plus" type="primary" @click="rowAdd">
        新增
      </el-button>
    </template>
  </BuseCrud>
</template>
```

## 核心属性配置

### 1. 表格配置 (tableColumn)

```javascript
computed: {
  tableColumn() {
    return [
      {
        field: "id",              // 字段名
        title: "ID",              // 列标题
        width: 100,               // 列宽度
        align: "center",          // 对齐方式
        fixed: "left",            // 固定列
        sortable: true,           // 是否可排序
        formatter: ({ cellValue }) => {  // 格式化函数
          return cellValue;
        }
      },
      {
        field: "name",
        title: "名称",
        minWidth: 120
      },
      {
        field: "status",
        title: "状态",
        formatter: ({ cellValue }) => {
          return cellValue === "1" ? "启用" : "停用";
        }
      }
    ];
  }
}
```

### 2. 筛选配置 (filterOptions)

```javascript
computed: {
  filterOptions() {
    return {
      showCount: 3,             // 默认显示的筛选项数量
      layout: "right",          // 布局方式
      inline: true,             // 是否行内布局
      labelWidth: "100px",      // 标签宽度
      config: [
        {
          field: "name",        // 字段名
          title: "名称",         // 标签文本
          element: "el-input",  // 使用的元素
          props: {              // 传递给元素的属性
            placeholder: "请输入名称"
          }
        },
        {
          field: "status",
          title: "状态",
          element: "el-select",
          props: {
            options: [
              { value: "1", label: "启用" },
              { value: "0", label: "停用" }
            ]
          }
        }
      ],
      params: this.params       // 筛选参数对象
    };
  }
}
```

### 3. 弹窗配置 (modalConfig)

```javascript
computed: {
  modalConfig() {
    return {
      modalFullScreen: false,   // 是否全屏显示
      submitBtn: true,          // 是否显示提交按钮
      okText: "确定",           // 确认按钮文本
      cancelText: "取消",       // 取消按钮文本
      addBtn: true,             // 是否显示新增按钮
      editBtn: true,            // 是否显示编辑按钮
      delBtn: true,             // 是否显示删除按钮
      viewBtn: true,            // 是否显示查看按钮
      menu: true,               // 是否显示菜单
      menuWidth: 200,           // 菜单宽度
      modalWidth: "800px",      // 弹窗宽度
      formConfig: [             // 表单配置
        {
          field: "name",
          title: "名称",
          element: "el-input",
          rules: [
            { required: true, message: "请输入名称" }
          ],
          props: {
            placeholder: "请输入名称"
          }
        },
        {
          field: "status",
          title: "状态",
          element: "el-select",
          props: {
            options: [
              { value: "1", label: "启用" },
              { value: "0", label: "停用" }
            ]
          },
          defaultValue: "1"
        }
      ]
    };
  }
}
```

## 事件处理

### 1. 核心事件

```javascript
methods: {
  // 加载数据
  async loadData() {
    const params = {
      ...this.params,
      pageNum: this.tablePage.currentPage,
      pageSize: this.tablePage.pageSize
    };
    this.loading = true;
    try {
      const res = await api.getList(params);
      this.tableData = res.data;
      this.tablePage.total = res.total;
    } finally {
      this.loading = false;
    }
  },

  // 新增操作
  rowAdd() {
    this.$refs.crud.switchModalView(true, "add");
  },

  // 弹窗确认处理
  async modalConfirmHandler({ crudOperationType, ...formParams }) {
    try {
      if (crudOperationType === "add") {
        await api.add(formParams);
        this.$message.success("新增成功");
      } else if (crudOperationType === "edit") {
        await api.update(formParams);
        this.$message.success("更新成功");
      }
      this.loadData();
    } catch (error) {
      console.error("操作失败:", error);
    }
  },

  // 删除操作
  async deleteRowHandler(row) {
    try {
      await this.$confirm("确定要删除这条记录吗？", "提示");
      await api.delete({ id: row.id });
      this.$message.success("删除成功");
      this.loadData();
    } catch (error) {
      if (error !== "cancel") {
        console.error("删除失败:", error);
      }
    }
  }
}
```

### 2. 生命周期事件

```javascript
methods: {
  // 重置筛选条件
  handleReset() {
    this.params = initParams(this.filterOptions.config);
    this.loadData();
  },

  // 编辑行数据
  rowEdit(row) {
    this.$refs.crud.switchModalView(true, "edit", row);
  },

  // 弹窗取消
  modalCancelHandler() {
    // 取消操作的处理逻辑
  }
}
```

## 高级功能

### 1. 自定义操作按钮

```javascript
modalConfig: {
  customOperationTypes: [
    {
      title: "查看日志",
      typeName: "log",
      slotName: "log",
      event: (row) => {
        return this.$refs.crud.switchModalView(true, "log", row);
      },
    },
    {
      title: "配置权限",
      typeName: "permission",
      event: (row) => {
        return this.handlePermissionConfig(row);
      },
      condition: (row) => {
        return this.checkPermission(["system:user:permission"]);
      },
    },
  ];
}
```

### 2. 批量操作

```vue
<template>
  <BuseCrud
    selectStyleType="infoTip"
    @handleBatchSelect="handleBatchSelect"
    :showSelectNum="showSelectNum"
  >
    <template #toolbar_buttons>
      <el-button
        type="danger"
        @click="batchDelete"
        :disabled="selectedRows.length === 0"
      >
        批量删除
      </el-button>
    </template>
  </BuseCrud>
</template>

<script>
export default {
  data() {
    return {
      selectedRows: [],
      showSelectNum: true,
    };
  },
  methods: {
    handleBatchSelect(rows) {
      this.selectedRows = rows;
    },

    async batchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      try {
        await this.$confirm(
          `确定要删除选中的${this.selectedRows.length}条记录吗？`
        );
        const ids = this.selectedRows.map((row) => row.id);
        await api.batchDelete({ ids });
        this.$message.success("批量删除成功");
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          console.error("批量删除失败:", error);
        }
      }
    },
  },
};
</script>
```

### 3. Tab 切换功能

```vue
<template>
  <BuseCrud
    :tabRadioList="tabRadioList"
    @tabRadioChange="tabRadioChange"
    tabType="card"
  />
</template>

<script>
export default {
  data() {
    return {
      tabRadioList: [
        { value: "0", label: "全部", number: 10 },
        { value: "1", label: "待处理", number: 5 },
        { value: "2", label: "已处理", number: 5 },
      ],
    };
  },
  methods: {
    tabRadioChange(val) {
      this.params.status = val;
      this.loadData();
    },
  },
};
</script>
```

## 插槽使用

### 1. 工具栏插槽

```vue
<template #toolbar_buttons>
  <el-button icon="el-icon-plus" type="primary" @click="rowAdd">
    新增
  </el-button>
  <el-button icon="el-icon-download" @click="handleExport">
    导出
  </el-button>
</template>
```

### 2. 筛选区域插槽

```vue
<template #filterCustomBtn>
  <div class="btn-wrap">
    <el-button type="primary" @click="handleQuery">查询</el-button>
    <el-button @click="resetQuery">重置</el-button>
  </div>
</template>
```

### 3. 自定义表单项插槽

```vue
<template #customField="{ params }">
  <el-cascader
    v-model="params.region"
    :options="regionOptions"
    :props="{ checkStrictly: true }"
  />
</template>
```

### 4. 自定义弹窗内容

```vue
<template #customModal="{ row, operationType }">
  <div v-if="operationType === 'log'">
    <Timeline :list="logList" />
  </div>
</template>

<template #modalFooter="{ row, crudOperationType }">
  <div v-if="crudOperationType === 'log'">
    <el-button @click="handleClose">关闭</el-button>
  </div>
</template>
```

## 工具函数

### 1. 参数初始化

```javascript
import { initParams } from "@/utils/buse.js";

export default {
  created() {
    this.params = initParams(this.filterOptions.config);
  },
};
```

### 2. 配置生成器

```javascript
import { generateConfigItems } from "@/utils/buse.js";

export default {
  computed: {
    configs() {
      const fieldsMap = {
        name: "名称",
        status: {
          title: "状态",
          element: "el-select",
          props: {
            options: [
              { value: "1", label: "启用" },
              { value: "0", label: "停用" },
            ],
          },
        },
      };

      const scope = {
        tableColumn: ["name", "status"],
        formConfig: ["name", "status"],
      };

      return generateConfigItems({ fieldsMap, scope });
    },
  },
};
```

## 完整属性列表

### Props 属性

| 属性名          | 类型    | 默认值    | 说明             |
| --------------- | ------- | --------- | ---------------- |
| loading         | Boolean | false     | 加载状态         |
| title           | String  | ""        | 组件标题         |
| filterOptions   | Object  | {}        | 筛选配置对象     |
| tablePage       | Object  | {}        | 分页配置对象     |
| tableColumn     | Array   | []        | 表格列配置       |
| tableData       | Array   | []        | 表格数据         |
| tableProps      | Object  | {}        | 表格属性配置     |
| modalConfig     | Object  | {}        | 弹窗配置对象     |
| tabRadioList    | Array   | []        | Tab 标签配置     |
| tabType         | String  | "default" | Tab 类型         |
| selectStyleType | String  | ""        | 选择样式类型     |
| showSelectNum   | Boolean | false     | 是否显示选择数量 |

### 事件列表

| 事件名            | 参数                               | 说明         |
| ----------------- | ---------------------------------- | ------------ |
| loadData          | -                                  | 加载数据事件 |
| handleReset       | -                                  | 重置筛选条件 |
| handleCreate      | -                                  | 新增按钮点击 |
| rowEdit           | row                                | 编辑按钮点击 |
| rowDel            | row                                | 删除按钮点击 |
| modalCancel       | -                                  | 弹窗取消     |
| modalSubmit       | formParams                         | 弹窗提交     |
| modalConfirm      | {crudOperationType, ...formParams} | 弹窗确认     |
| handleBatchSelect | rows                               | 批量选择     |
| tabRadioChange    | value                              | Tab 切换     |

### 插槽列表

| 插槽名          | 作用域参数               | 说明               |
| --------------- | ------------------------ | ------------------ |
| toolbar_buttons | -                        | 工具栏按钮区域     |
| filterCustomBtn | -                        | 筛选区域自定义按钮 |
| [field]         | -                        | 筛选项自定义内容   |
| [slotName]      | {params}                 | 表单项自定义内容   |
| modalFooter     | {row, crudOperationType} | 弹窗底部自定义内容 |
| modalDefault    | {row, operationType}     | 弹窗默认内容       |

## 实际应用示例

### 1. 用户管理页面

```vue
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['system:user:add']"
        >
          新增用户
        </el-button>
        <el-button
          icon="el-icon-download"
          @click="handleExport"
          v-has-permi="['system:user:export']"
        >
          导出
        </el-button>
      </template>

      <template #deptSelect>
        <TreeSelect
          v-model="params.deptId"
          :options="deptOptions"
          placeholder="请选择部门"
        />
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import { getList, addUser, updateUser, deleteUser } from "@/api/system/user";
import { getDeptTree } from "@/api/system/dept";

export default {
  name: "UserManage",
  data() {
    return {
      loading: false,
      tableData: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      params: {},
      deptOptions: [],
    };
  },

  computed: {
    tableColumn() {
      return [
        { field: "userId", title: "用户ID", width: 100 },
        { field: "userName", title: "用户名", minWidth: 120 },
        { field: "nickName", title: "昵称", minWidth: 120 },
        { field: "deptName", title: "部门", minWidth: 120 },
        { field: "email", title: "邮箱", minWidth: 150 },
        { field: "phonenumber", title: "手机号", width: 120 },
        {
          field: "status",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            return cellValue === "0" ? "正常" : "停用";
          },
        },
        { field: "createTime", title: "创建时间", width: 160 },
      ];
    },

    filterOptions() {
      return {
        showCount: 4,
        config: [
          { field: "userName", title: "用户名" },
          { field: "phonenumber", title: "手机号" },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { value: "0", label: "正常" },
                { value: "1", label: "停用" },
              ],
            },
          },
          {
            field: "deptId",
            title: "部门",
            slotName: "deptSelect",
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        modalWidth: "600px",
        formConfig: [
          {
            field: "userName",
            title: "用户名",
            rules: [{ required: true, message: "请输入用户名" }],
          },
          {
            field: "nickName",
            title: "昵称",
            rules: [{ required: true, message: "请输入昵称" }],
          },
          {
            field: "deptId",
            title: "部门",
            element: "tree-select",
            props: {
              options: this.deptOptions,
            },
            rules: [{ required: true, message: "请选择部门" }],
          },
          {
            field: "email",
            title: "邮箱",
            rules: [
              { required: true, message: "请输入邮箱" },
              { type: "email", message: "请输入正确的邮箱格式" },
            ],
          },
          {
            field: "phonenumber",
            title: "手机号",
            rules: [
              { required: true, message: "请输入手机号" },
              { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号" },
            ],
          },
          {
            field: "status",
            title: "状态",
            element: "el-radio-group",
            props: {
              options: [
                { value: "0", label: "正常" },
                { value: "1", label: "停用" },
              ],
            },
            defaultValue: "0",
          },
        ],
      };
    },

    tableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
      };
    },
  },

  created() {
    this.loadDeptTree();
    this.loadData();
  },

  methods: {
    async loadData() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      try {
        const res = await getList(params);
        this.tableData = res.rows;
        this.tablePage.total = res.total;
      } finally {
        this.loading = false;
      }
    },

    async loadDeptTree() {
      const res = await getDeptTree();
      this.deptOptions = res.data;
    },

    rowAdd() {
      this.$refs.crud.switchModalView(true, "add");
    },

    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      try {
        if (crudOperationType === "add") {
          await addUser(formParams);
          this.$message.success("新增成功");
        } else if (crudOperationType === "edit") {
          await updateUser(formParams);
          this.$message.success("更新成功");
        }
        this.loadData();
      } catch (error) {
        console.error("操作失败:", error);
      }
    },

    async deleteRowHandler(row) {
      try {
        await this.$confirm("确定要删除该用户吗？", "提示");
        await deleteUser(row.userId);
        this.$message.success("删除成功");
        this.loadData();
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败:", error);
        }
      }
    },

    async handleExport() {
      // 导出逻辑
    },
  },
};
</script>
```

## 最佳实践

### 1. 配置对象管理

- 使用 computed 属性定义配置对象，便于响应式更新
- 将复杂配置拆分为多个 computed 属性
- 使用工具函数生成重复性配置

### 2. 数据加载优化

- 合理使用 loading 状态
- 实现防抖搜索功能
- 缓存不经常变化的数据

### 3. 权限控制

- 使用 v-has-permi 指令控制按钮显示
- 在 customOperationTypes 中使用 condition 函数
- 结合后端权限验证

### 4. 错误处理

- 统一的错误处理机制
- 用户友好的错误提示
- 日志记录和错误上报

### 5. 性能优化

- 合理使用 Object.freeze 冻结静态数据
- 避免在模板中使用复杂计算
- 使用 v-show 替代 v-if（频繁切换场景）

## 常见问题解决

### 1. 表格数据不更新

- 检查 tableData 是否正确赋值
- 确认 Vue 响应式系统是否正常工作
- 使用 this.\$forceUpdate()强制更新（不推荐）

### 2. 弹窗表单验证失败

- 检查 formConfig 中的 rules 配置
- 确认字段名与后端接口一致
- 使用 ref 获取表单实例进行手动验证

### 3. 权限控制不生效

- 检查权限字符串是否正确
- 确认用户权限是否已正确获取
- 验证 v-has-permi 指令是否正确使用

### 4. 插槽内容不显示

- 检查插槽名是否正确
- 确认作用域参数是否正确使用
- 验证插槽内容是否有条件渲染

### 5. 分页功能异常

- 检查 tablePage 对象结构是否正确
- 确认分页参数是否正确传递给后端
- 验证 total 总数是否正确设置

## 组件方法

### 通过 ref 调用的方法

```javascript
// 切换弹窗显示状态
this.$refs.crud.switchModalView(visible, type, row);

// 获取表格实例
const tableRef = this.$refs.crud.getVxeTableRef();

// 获取表单字段值
const formFields = this.$refs.crud.getFormFields();

// 设置表单字段值
this.$refs.crud.setFormFields(fieldData);
```

## 扩展功能

### 1. 自定义表格列渲染

```javascript
tableColumn: [
  {
    field: "status",
    title: "状态",
    slots: {
      default: ({ row }) => {
        return [
          <el-switch
            value={row.status}
            active-value="1"
            inactive-value="0"
            onChange={(val) => this.handleStatusChange(row, val)}
          />,
        ];
      },
    },
  },
];
```

### 2. 复杂筛选条件

```javascript
filterOptions: {
  config: [
    {
      field: "dateRange",
      title: "时间范围",
      element: "el-date-picker",
      props: {
        type: "datetimerange",
        rangeSeparator: "至",
        startPlaceholder: "开始日期",
        endPlaceholder: "结束日期",
      },
    },
    {
      field: "cascaderField",
      title: "级联选择",
      element: "el-cascader",
      props: {
        options: this.cascaderOptions,
        props: { checkStrictly: true },
      },
    },
  ];
}
```

### 3. 动态表单验证

```javascript
modalConfig: {
  formConfig: [
    {
      field: "email",
      title: "邮箱",
      rules: [
        { required: true, message: "请输入邮箱" },
        {
          validator: (rule, value, callback) => {
            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              callback(new Error("请输入正确的邮箱格式"));
            } else {
              callback();
            }
          },
          trigger: "blur",
        },
      ],
    },
  ];
}
```

## 总结

BusCrud 组件是项目中最重要的业务组件之一，通过合理的配置和使用，可以大大提高开发效率。在使用过程中，需要注意：

1. **配置的响应式**: 使用 computed 属性确保配置的响应式更新
2. **权限控制**: 合理使用权限指令和条件判断
3. **错误处理**: 完善的错误处理机制
4. **性能优化**: 避免不必要的重复渲染
5. **代码复用**: 使用工具函数提高代码复用性

通过掌握这些使用方法和最佳实践，可以更好地利用 BusCrud 组件构建高质量的后台管理界面。
