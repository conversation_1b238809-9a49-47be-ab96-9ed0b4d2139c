# 接口模拟系统使用说明

## 系统概述

接口模拟系统是一套基于ServiceWorker的完整API模拟解决方案，专为充电运维管理系统设计。该系统能够完全替代真实API进行前端开发和测试，同时不影响生产环境的正常运行。

## 系统架构

### 核心组件

```
接口模拟系统
├── mock.config.js              # 全局配置管理
├── public/
│   ├── sw-mock-manager.js      # ServiceWorker核心处理模块
│   └── mock/                   # Mock数据文件目录
│       ├── st-lifePay-providerInfo-queryPage.json
│       ├── st-lifePay-providerInfo-getDropLists.json
│       └── ...
└── src/
    └── utils/
        └── mock-system.js      # 系统初始化和管理工具
```

### 工作原理

1. **应用启动** → 根据配置决定是否注册ServiceWorker
2. **API请求** → Axios发起请求 → ServiceWorker拦截
3. **模拟检查** → 检查配置决定是否模拟
4. **数据处理** → 从JSON文件读取数据 → 模拟网络延迟
5. **响应返回** → 返回标准格式响应 → 前端接收处理

## 安装配置

### 1. 环境变量配置

在项目根目录的 `.env.development` 文件中添加：

```bash
# 启用Mock系统（开发环境）
VUE_APP_ENABLE_MOCK=true
```

在 `.env.production` 文件中：

```bash
# 生产环境禁用Mock系统
VUE_APP_ENABLE_MOCK=false
```

### 2. 全局配置

编辑 `mock.config.js` 文件：

```javascript
const MOCK_CONFIG = {
  // 全局开关
  enabled: process.env.VUE_APP_ENABLE_MOCK === 'true',
  
  // 模块控制
  modules: {
    settlement: {
      enabled: true,
      subModules: {
        lifePay: {
          enabled: true,
          features: {
            serviceProvider: true,  // 启用服务商信息模拟
            bankFlow: true,         // 启用银行流水模拟
            // ... 其他功能
          }
        }
      }
    }
  },
  
  // 接口精确控制
  apis: {
    '/st/lifePay/providerInfo/queryPage': true,
    '/st/lifePay/providerInfo/getDropLists': true,
    // ... 其他接口
  }
};
```

### 3. 项目集成

系统已自动集成到 `src/main.js` 中：

```javascript
// Mock 系统初始化
import mockSystem from "@/utils/mock-system";

// 系统会在DOM加载完成后自动初始化
```

## Mock数据文件编写

### 文件命名规范

```
{模块名}-{功能名}-{操作名}.json
```

示例：
- `st-lifePay-providerInfo-queryPage.json` - 服务商信息分页查询
- `st-lifePay-providerInfo-getDropLists.json` - 服务商信息下拉列表
- `st-lifePay-providerInfo-add.json` - 新增服务商信息

### 数据文件模板

#### 分页查询模板

```json
{
  "data": [
    {
      "id": 1,
      "serviceProvider": "支付宝（中国）网络技术有限公司",
      "agreementType": "框架协议",
      "status": "正常",
      "createTime": "2023-01-10 09:00:00",
      "updateTime": "2024-01-15 10:30:00"
    }
  ],
  "total": 25,
  "pageNum": 1,
  "pageSize": 10,
  "message": "查询成功"
}
```

#### 下拉列表模板

```json
{
  "data": {
    "status": ["正常", "暂停", "停用"],
    "agreementType": ["框架协议", "合作协议", "战略合作协议"],
    "serviceProvider": ["支付宝", "微信支付", "银联"]
  },
  "message": "获取下拉列表成功"
}
```

#### 操作结果模板

```json
{
  "data": {
    "id": 11,
    "message": "操作成功"
  },
  "message": "新增成功"
}
```

### 动态数据生成

对于需要动态数据的场景，可以在Mock数据中使用占位符：

```json
{
  "data": [
    {
      "id": "{{$randomInt}}",
      "name": "模拟数据 {{$index}}",
      "createTime": "{{$now}}",
      "status": "{{$randomChoice(['正常', '停用'])}}"
    }
  ]
}
```

## 为新页面添加接口模拟

### 步骤1：分析页面接口需求

以新增用户管理页面为例：

```javascript
// 分析页面使用的API接口
const apis = [
  'POST /system/user/queryPage',    // 分页查询
  'GET /system/user/getDropLists',  // 下拉列表
  'POST /system/user/add',          // 新增用户
  'POST /system/user/edit',         // 编辑用户
  'POST /system/user/delete',       // 删除用户
  'POST /system/user/exportExcel'   // 导出Excel
];
```

### 步骤2：更新配置文件

在 `mock.config.js` 中添加配置：

```javascript
// 模块配置
modules: {
  system: {
    enabled: true,
    subModules: {
      user: {
        enabled: true,
        features: {
          userManage: true
        }
      }
    }
  }
},

// 接口配置
apis: {
  '/system/user/*': true,  // 通配符匹配
  // 或者精确配置
  '/system/user/queryPage': true,
  '/system/user/getDropLists': true,
  '/system/user/add': true,
  '/system/user/edit': true,
  '/system/user/delete': true,
  '/system/user/exportExcel': true
}
```

### 步骤3：创建Mock数据文件

在 `public/mock/` 目录下创建对应的JSON文件：

```bash
public/mock/
├── system-user-queryPage.json
├── system-user-getDropLists.json
├── system-user-add.json
├── system-user-edit.json
├── system-user-delete.json
└── system-user-exportExcel.json
```

### 步骤4：编写Mock数据

参考现有的数据文件格式，编写符合业务需求的模拟数据。

### 步骤5：测试验证

```javascript
// 在浏览器控制台中测试
MockSystem.test('/system/user/queryPage');  // 检查接口是否被模拟
MockSystem.status();                        // 查看系统状态
```

## 运行时控制

### 控制台命令

在浏览器开发者工具控制台中，可以使用以下命令：

```javascript
// 查看系统状态
MockSystem.status();

// 启用Mock系统
MockSystem.enable();

// 禁用Mock系统
MockSystem.disable();

// 清除缓存
MockSystem.clearCache();

// 查看配置
MockSystem.config();

// 查看所有模拟的接口
MockSystem.apis();

// 测试特定接口是否被模拟
MockSystem.test('/st/lifePay/providerInfo/queryPage');
```

### 动态切换

```javascript
// 临时启用某个接口的模拟
MockSystem.enable();

// 临时禁用某个模块
// 需要修改配置后调用 clearCache()
```

## 调试和故障排除

### 常见问题

#### 1. ServiceWorker未注册

**症状**: 控制台显示 "ServiceWorker not supported" 或注册失败

**解决方案**:
- 确保在HTTPS环境或localhost下运行
- 检查浏览器是否支持ServiceWorker
- 查看浏览器控制台的错误信息

#### 2. Mock数据未生效

**症状**: 请求仍然发送到真实API

**解决方案**:
```javascript
// 检查配置
MockSystem.status();

// 检查特定接口
MockSystem.test('/your/api/path');

// 清除缓存重试
MockSystem.clearCache();
```

#### 3. Mock数据格式错误

**症状**: 前端接收到的数据格式不正确

**解决方案**:
- 检查JSON文件语法是否正确
- 确保数据格式符合项目的响应格式规范
- 查看ServiceWorker的调试信息

### 调试技巧

#### 1. 启用调试模式

在 `mock.config.js` 中设置：

```javascript
serviceWorker: {
  debug: true  // 启用详细日志
}
```

#### 2. 查看ServiceWorker状态

在Chrome开发者工具中：
1. 打开 Application 标签
2. 选择 Service Workers
3. 查看注册状态和日志

#### 3. 网络请求监控

在Network标签中：
- 查看请求是否被拦截
- 检查响应头中的标识
- 确认响应数据格式

### 性能优化

#### 1. 缓存策略

```javascript
// 清理过期缓存
setInterval(() => {
  MockSystem.clearCache();
}, 30 * 60 * 1000); // 30分钟清理一次
```

#### 2. 数据文件优化

- 避免单个JSON文件过大（建议<1MB）
- 使用合理的分页数据量
- 定期清理不需要的Mock数据

## 与现有技术栈集成

### Vue 2.6.14 兼容性

系统完全兼容Vue 2.6.14，不需要额外配置。

### Element UI 集成

Mock数据格式完全兼容Element UI的表格、表单等组件要求。

### BuseCrud 组件支持

系统特别针对BuseCrud组件进行了优化：

```javascript
// 支持BuseCrud的标准配置
const mockData = {
  data: [...],      // 表格数据
  total: 100,       // 总数
  pageNum: 1,       // 当前页
  pageSize: 10      // 页大小
};
```

### 权限控制兼容

Mock系统保持与现有权限控制系统的兼容性：

```javascript
// 在Mock数据中可以模拟权限相关的响应
{
  "code": "40000",  // 权限不足
  "message": "无权限访问",
  "data": null
}
```

## 最佳实践

### 1. 数据真实性

- Mock数据应尽可能接近真实业务数据
- 包含各种边界情况和异常场景
- 保持数据的一致性和关联性

### 2. 版本管理

- Mock数据文件纳入版本控制
- 定期同步真实API的数据结构变化
- 建立Mock数据的更新机制

### 3. 团队协作

- 建立Mock数据的命名和格式规范
- 定期review和更新Mock数据
- 文档化特殊的Mock场景

### 4. 测试覆盖

- 为不同的业务场景准备对应的Mock数据
- 测试成功和失败的响应场景
- 验证分页、排序、筛选等功能

## 生产环境注意事项

### 安全检查

在部署到生产环境前，确保：

```bash
# 检查环境变量
VUE_APP_ENABLE_MOCK=false

# 检查构建产物中不包含Mock相关代码
npm run build
# 检查 dist 目录中是否包含 sw-mock-manager.js
```

### 性能影响

Mock系统在生产环境禁用时，对性能无任何影响：
- ServiceWorker不会注册
- Mock相关代码不会执行
- 不会增加包体积

## 总结

接口模拟系统提供了一套完整的API模拟解决方案，能够：

1. **提高开发效率** - 前端开发不依赖后端接口
2. **改善测试质量** - 可控的测试数据和场景
3. **降低开发成本** - 减少环境依赖和数据准备工作
4. **增强团队协作** - 前后端并行开发

通过合理使用该系统，可以显著提升项目的开发效率和代码质量。
