# 🔧 Mock系统调试指南

## 🎯 问题解决方案

我已经修复了Mock系统无法拦截真实API请求的问题。主要修复内容包括：

### 1. **API路径匹配问题修复**
- **问题**: ServiceWorker无法正确匹配API路径
- **原因**: 实际请求路径是 `/charging-maintenance-server/st/lifePay/providerInfo/queryPage`，但配置中只有 `/st/lifePay/providerInfo/queryPage`
- **解决方案**: 在ServiceWorker中添加了路径前缀处理逻辑

### 2. **配置传递问题修复**
- **问题**: Mock配置没有正确传递给ServiceWorker
- **解决方案**: 在Mock系统初始化时自动发送配置到ServiceWorker

### 3. **响应格式标准化**
- **问题**: Mock数据格式与项目API响应格式不匹配
- **解决方案**: 更新所有Mock数据文件，确保包含 `success`、`code`、`message` 等标准字段

## 🧪 验证步骤

### 步骤1: 检查Mock系统状态

在浏览器控制台中执行：

```javascript
// 1. 检查系统状态
MockSystem.status()
// 预期输出: { enabled: true, initialized: true, serviceWorker: true }

// 2. 查看详细调试信息
MockSystem.debug()

// 3. 测试特定接口
MockSystem.test('/st/lifePay/providerInfo/queryPage')
// 预期输出: "API /st/lifePay/providerInfo/queryPage is MOCKED"

// 4. 手动发送配置（如果需要）
MockSystem.sendConfig()
```

### 步骤2: 检查ServiceWorker状态

1. 打开浏览器开发者工具
2. 转到 **Application** 标签
3. 选择 **Service Workers**
4. 确认看到 `sw-mock-manager.js` 已注册且状态为 "activated"

### 步骤3: 监控网络请求

1. 打开 **Network** 标签
2. 访问服务商信息页面：`http://localhost:2267/charging-maintenance-ui/#/settlement/lifePay/serviceProvider`
3. 查看API请求：
   - 请求URL应该是：`/charging-maintenance-server/st/lifePay/providerInfo/queryPage`
   - 响应应该来自ServiceWorker（会有特殊标识）
   - 响应数据应该是Mock数据

### 步骤4: 验证Mock数据

访问服务商信息页面，确认：
- ✅ 表格显示10条模拟数据
- ✅ 下拉列表正确填充（PID、MID、出账机构等）
- ✅ 分页功能正常工作
- ✅ 新增、编辑、删除操作返回成功响应

## 🔍 常见问题排查

### 问题1: MockSystem对象不可用

**症状**: 控制台显示 "MockSystem is not defined"

**解决方案**:
```javascript
// 等待系统初始化完成
setTimeout(() => {
  console.log('MockSystem available:', typeof MockSystem !== 'undefined');
}, 2000);
```

### 问题2: ServiceWorker未注册

**症状**: Application标签中看不到ServiceWorker

**解决方案**:
1. 确认环境变量：`VUE_APP_ENABLE_MOCK=true`
2. 刷新页面
3. 检查控制台错误信息

### 问题3: 请求未被拦截

**症状**: Network标签显示请求发送到真实API

**解决方案**:
```javascript
// 检查配置
MockSystem.debug();

// 手动发送配置
MockSystem.sendConfig();

// 清除缓存
MockSystem.clearCache();
```

### 问题4: Mock数据格式错误

**症状**: 前端显示错误或数据格式不正确

**解决方案**:
1. 检查Mock数据文件的JSON格式
2. 确认响应包含必要字段：`success`、`code`、`data`
3. 验证分页数据包含：`total`、`pageNum`、`pageSize`

## 🛠️ 高级调试技巧

### 1. 启用ServiceWorker调试

在 `src/utils/mock-init.js` 中临时修改：

```javascript
const MOCK_CONFIG = {
  enabled: true,
  serviceWorker: {
    scriptPath: '/sw-mock-manager.js',
    debug: true  // 启用详细日志
  }
  // ...
};
```

### 2. 监控ServiceWorker消息

```javascript
// 在控制台中监听ServiceWorker消息
navigator.serviceWorker.addEventListener('message', event => {
  console.log('SW Message:', event.data);
});
```

### 3. 手动测试API拦截

```javascript
// 直接发送请求测试
fetch('/charging-maintenance-server/st/lifePay/providerInfo/queryPage', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ pageNum: 1, pageSize: 10 })
})
.then(response => response.json())
.then(data => console.log('Mock Response:', data));
```

## 📊 预期结果

### 正常工作的Mock系统应该显示：

1. **控制台输出**:
   ```
   [MockInit] Starting Mock System initialization...
   [MockSystem] ServiceWorker registered: ...
   [MockSystem] Configuration sent to ServiceWorker
   [MockInit] Mock System ready!
   ```

2. **Network标签**:
   - 请求被ServiceWorker拦截
   - 响应时间很短（<50ms）
   - 响应数据是Mock数据

3. **服务商信息页面**:
   - 表格显示模拟的服务商数据
   - 下拉列表正确填充
   - 所有CRUD操作正常工作

## 🚀 下一步操作

如果Mock系统正常工作：

1. **扩展到其他页面**:
   ```bash
   node scripts/generate-mock-template.js system user --all
   ```

2. **自定义Mock数据**:
   - 编辑 `public/mock/` 目录下的JSON文件
   - 根据业务需求调整数据内容

3. **团队协作**:
   - 分享Mock系统使用方法
   - 建立Mock数据维护流程

## 📞 技术支持

如果遇到问题：

1. 查看浏览器控制台的详细错误信息
2. 检查ServiceWorker的注册状态
3. 验证Mock配置和数据文件
4. 使用提供的调试命令进行排查

Mock系统现在应该能够正常拦截API请求并返回模拟数据！🎉
