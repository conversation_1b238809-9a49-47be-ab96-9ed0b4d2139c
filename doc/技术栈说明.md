# 充电运维管理系统 - 技术栈说明

## 项目概述

**项目名称**: charging-maintenance-ui  
**项目描述**: 邦道后台管理系统 - 充电运维管理前端  
**版本**: 0.0.1  
**作者**: dingxingxing <<EMAIL>>  

## 核心技术栈

### 前端框架
- **Vue.js**: 2.6.14 - 渐进式JavaScript框架，项目主框架
- **Vue Router**: 3.0.7 - Vue.js官方路由管理器
- **Vuex**: 3.1.2 - Vue.js状态管理模式

### UI组件库
- **Element UI**: 2.15.7 - 基于Vue 2.0的桌面端组件库
- **@bangdao/buse-components-element**: 1.1.7 - 邦道自研业务组件库
  - 包含BuseCrud、DynamicForm、AutoFilters等核心业务组件

### 构建工具
- **Vue CLI**: 4.1.0 - Vue.js开发的标准工具
- **Webpack**: 通过Vue CLI集成 - 模块打包器
- **Babel**: 7.12.16 - JavaScript编译器

### 开发工具
- **ESLint**: 5.16.0 - JavaScript代码检查工具
- **Prettier**: 1.19.1 - 代码格式化工具
- **Jest**: 通过Vue CLI集成 - JavaScript测试框架

### 网络请求
- **Axios**: 0.21.1 - 基于Promise的HTTP库
- **qs**: 6.10.3 - 查询字符串解析和序列化库

### 工具库
- **Lodash**: 4.17.21 - JavaScript实用工具库
- **Moment.js**: 2.29.1 - 日期处理库
- **dayjs**: 1.11.9 - 轻量级日期库
- **js-cookie**: 2.2.0 - 简单的cookie操作库
- **uuid**: 9.0.0 - UUID生成器

### 表格组件
- **VXE Table**: 3.8.28 - 基于Vue的表格组件
- **vxe-table-plugin-export-xlsx**: 2.2.2 - Excel导出插件

### 图表组件
- **ECharts**: 5.4.3 - 数据可视化图表库

### 地图组件
- **@amap/amap-jsapi-loader**: 1.0.1 - 高德地图JS API加载器
- **@amap/amap-vue**: 2.0.13 - 高德地图Vue组件

### 文件处理
- **ExcelJS**: 4.3.0 - Excel文件处理库
- **file-saver**: 2.0.5 - 文件保存库
- **XLSX**: 0.18.2 - Excel文件读写库
- **@vue-office/docx**: 1.6.2 - Word文档预览组件
- **@vue-office/excel**: 1.7.11 - Excel文档预览组件
- **@vue-office/pdf**: 2.0.2 - PDF文档预览组件

### 表单组件
- **@form-create/element-ui**: 2.5.26 - 动态表单生成器
- **@form-create/designer**: 1.0.7 - 表单设计器
- **@riophae/vue-treeselect**: 0.4.0 - 树形选择器

### 其他功能组件
- **vue-quill-editor**: 3.0.6 - 富文本编辑器
- **vue-cropper**: 0.4.9 - 图片裁剪组件
- **screenfull**: 4.2.0 - 全屏API封装
- **print-js**: 1.6.0 - 打印功能库
- **video.js**: 8.9.0 - HTML5视频播放器

## 项目结构

```
charging-maintenance-ui/
├── public/                 # 静态资源目录
│   ├── index.html         # HTML模板
│   └── static/            # 静态文件
├── src/                   # 源代码目录
│   ├── api/              # API接口定义
│   ├── assets/           # 资源文件
│   │   ├── icons/        # 图标文件
│   │   ├── images/       # 图片文件
│   │   └── styles/       # 样式文件
│   ├── components/       # 公共组件
│   ├── directive/        # 自定义指令
│   ├── layout/          # 布局组件
│   ├── router/          # 路由配置
│   ├── store/           # Vuex状态管理
│   ├── utils/           # 工具函数
│   ├── views/           # 页面组件
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── dist/                # 构建输出目录
├── node_modules/        # 依赖包目录
├── babel.config.js      # Babel配置
├── vue.config.js        # Vue CLI配置
├── package.json         # 项目配置文件
└── README.md           # 项目说明
```

## 开发环境配置

### 环境要求
- **Node.js**: >= 12.0.0
- **npm**: >= 6.0.0 或 **yarn**: >= 1.22.0

### 开发服务器配置
- **端口**: 2266
- **代理目标**: https://test-napi.bangdao-tech.com/charging-maintenance-server
- **启用Gzip压缩**: 是
- **热重载**: 是

### 构建配置
- **输出目录**: dist
- **静态资源目录**: static
- **公共路径**: /charging-maintenance-ui
- **Source Map**: 开发环境启用，生产环境禁用

## 核心特性

### 1. 组件化开发
- 基于Vue.js组件化开发模式
- 使用Element UI提供基础UI组件
- 集成邦道自研BuseCrud等业务组件

### 2. 状态管理
- 使用Vuex进行全局状态管理
- 模块化状态管理结构
- 支持数据字典、用户信息、权限等状态管理

### 3. 路由管理
- 基于Vue Router的单页面应用
- 支持动态路由和权限控制
- 面包屑导航和标签页功能

### 4. 网络请求
- 基于Axios的HTTP请求封装
- 统一的请求拦截器和响应拦截器
- 支持请求加密和响应解密

### 5. 权限控制
- 基于角色的权限控制系统
- 自定义权限指令v-has-permi
- 菜单和按钮级别的权限控制

## 开发规范

### 代码规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 遵循Vue.js官方风格指南

### 命名规范
- 组件名使用PascalCase
- 文件名使用kebab-case
- 变量和方法名使用camelCase

### 目录规范
- 按功能模块组织代码结构
- API接口按业务模块分类
- 组件按通用性分类

## 部署配置

### 生产环境
- 启用代码压缩和混淆
- 启用Gzip压缩
- 移除console.log输出
- 分包优化，提高加载性能

### Docker支持
- 提供Dockerfile和Dockerfile-prod
- 支持容器化部署
- 集成nginx配置

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60
- Safari >= 12
- Edge >= 79
- IE >= 11（部分功能可能受限）

## 性能优化

### 构建优化
- 代码分割和懒加载
- 静态资源压缩
- Tree Shaking去除无用代码

### 运行时优化
- 组件懒加载
- 图片懒加载
- 虚拟滚动（大数据量表格）

## 安全特性

- XSS防护
- CSRF防护
- 请求数据加密
- 权限验证
- 安全的Cookie配置
