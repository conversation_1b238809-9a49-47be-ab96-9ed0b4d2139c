<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock系统验证页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .test-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .test-card h3 {
            margin-top: 0;
            color: #555;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn.secondary {
            background: #2196F3;
        }
        .btn.secondary:hover {
            background: #1976D2;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.danger:hover {
            background: #d32f2f;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            color: #2e7d32;
        }
        .result.error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .result.info {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            color: #1565c0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success {
            background: #4CAF50;
        }
        .status-indicator.error {
            background: #f44336;
        }
        .status-indicator.warning {
            background: #ff9800;
        }
        .quick-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .quick-links a {
            background: #673ab7;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .quick-links a:hover {
            background: #5e35b1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Mock系统验证中心</h1>
            <p>全面测试和验证Mock系统功能</p>
        </div>
        
        <div class="content">
            <!-- 快速链接 -->
            <div class="section">
                <h2>🚀 快速导航</h2>
                <div class="quick-links">
                    <a href="/charging-maintenance-ui/#/settlement/lifePay/serviceProvider" target="_blank">服务商信息页面</a>
                    <a href="/charging-maintenance-ui/mock-test.html" target="_blank">基础测试页面</a>
                    <a href="javascript:void(0)" onclick="openDevTools()">打开开发者工具</a>
                    <a href="javascript:void(0)" onclick="refreshPage()">刷新页面</a>
                </div>
            </div>

            <!-- 系统状态检查 -->
            <div class="section">
                <h2>📊 系统状态检查</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>Mock系统状态</h3>
                        <button class="btn" onclick="checkMockSystemStatus()">检查系统状态</button>
                        <button class="btn secondary" onclick="checkServiceWorkerStatus()">检查ServiceWorker</button>
                        <div id="systemStatus" class="result info">点击按钮开始检查...</div>
                    </div>
                    
                    <div class="test-card">
                        <h3>配置验证</h3>
                        <button class="btn" onclick="validateConfiguration()">验证配置</button>
                        <button class="btn secondary" onclick="sendConfigToSW()">发送配置到SW</button>
                        <div id="configStatus" class="result info">点击按钮开始验证...</div>
                    </div>
                </div>
            </div>

            <!-- API测试 -->
            <div class="section">
                <h2>🔌 API接口测试</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>服务商信息接口</h3>
                        <button class="btn" onclick="testProviderAPI()">测试分页查询</button>
                        <button class="btn secondary" onclick="testDropListAPI()">测试下拉列表</button>
                        <button class="btn secondary" onclick="testCRUDAPIs()">测试CRUD操作</button>
                        <div id="apiTestResult" class="result info">点击按钮开始测试...</div>
                    </div>
                    
                    <div class="test-card">
                        <h3>网络请求监控</h3>
                        <button class="btn" onclick="monitorNetworkRequests()">开始监控</button>
                        <button class="btn danger" onclick="stopMonitoring()">停止监控</button>
                        <div id="networkMonitor" class="result info">点击开始监控网络请求...</div>
                    </div>
                </div>
            </div>

            <!-- 调试工具 -->
            <div class="section">
                <h2>🛠️ 调试工具</h2>
                <div class="test-grid">
                    <div class="test-card">
                        <h3>Mock系统控制</h3>
                        <button class="btn" onclick="enableMockSystem()">启用Mock</button>
                        <button class="btn danger" onclick="disableMockSystem()">禁用Mock</button>
                        <button class="btn secondary" onclick="clearMockCache()">清除缓存</button>
                        <div id="controlResult" class="result info">使用按钮控制Mock系统...</div>
                    </div>
                    
                    <div class="test-card">
                        <h3>详细调试信息</h3>
                        <button class="btn" onclick="showDebugInfo()">显示调试信息</button>
                        <button class="btn secondary" onclick="exportLogs()">导出日志</button>
                        <div id="debugInfo" class="result info">点击显示详细调试信息...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let networkMonitorActive = false;
        let originalFetch = window.fetch;
        let requestLogs = [];

        // 等待Mock系统加载
        function waitForMockSystem() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50;
                
                function check() {
                    if (typeof window.MockSystem !== 'undefined') {
                        resolve(window.MockSystem);
                    } else if (attempts < maxAttempts) {
                        attempts++;
                        setTimeout(check, 100);
                    } else {
                        reject(new Error('MockSystem not available after 5 seconds'));
                    }
                }
                check();
            });
        }

        // 检查Mock系统状态
        async function checkMockSystemStatus() {
            const resultDiv = document.getElementById('systemStatus');
            try {
                const mockSystem = await waitForMockSystem();
                const status = mockSystem.status();
                
                let statusText = '✅ Mock系统状态检查结果:\n\n';
                statusText += `启用状态: ${status.enabled ? '✅ 已启用' : '❌ 未启用'}\n`;
                statusText += `初始化状态: ${status.initialized ? '✅ 已初始化' : '❌ 未初始化'}\n`;
                statusText += `ServiceWorker: ${status.serviceWorker ? '✅ 已注册' : '❌ 未注册'}\n`;
                
                resultDiv.className = 'result success';
                resultDiv.textContent = statusText;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 错误: ${error.message}`;
            }
        }

        // 检查ServiceWorker状态
        async function checkServiceWorkerStatus() {
            const resultDiv = document.getElementById('systemStatus');
            try {
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    let statusText = '🔧 ServiceWorker状态:\n\n';
                    
                    if (registration) {
                        statusText += `✅ ServiceWorker已注册\n`;
                        statusText += `脚本URL: ${registration.scope}\n`;
                        statusText += `状态: ${registration.active ? registration.active.state : '未激活'}\n`;
                        statusText += `更新时间: ${new Date(registration.updateTime || Date.now()).toLocaleString()}\n`;
                    } else {
                        statusText += `❌ ServiceWorker未注册\n`;
                    }
                    
                    resultDiv.className = 'result info';
                    resultDiv.textContent = statusText;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 浏览器不支持ServiceWorker';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 检查ServiceWorker时出错: ${error.message}`;
            }
        }

        // 验证配置
        async function validateConfiguration() {
            const resultDiv = document.getElementById('configStatus');
            try {
                const mockSystem = await waitForMockSystem();
                mockSystem.debug();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 配置验证完成，请查看浏览器控制台获取详细信息';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 配置验证失败: ${error.message}`;
            }
        }

        // 发送配置到ServiceWorker
        async function sendConfigToSW() {
            const resultDiv = document.getElementById('configStatus');
            try {
                const mockSystem = await waitForMockSystem();
                mockSystem.sendConfig();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 配置已发送到ServiceWorker';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 发送配置失败: ${error.message}`;
            }
        }

        // 测试服务商API
        async function testProviderAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            try {
                const response = await fetch('/charging-maintenance-server/st/lifePay/providerInfo/queryPage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pageNum: 1,
                        pageSize: 10
                    })
                });
                
                const data = await response.json();
                
                let resultText = '✅ 服务商分页查询测试结果:\n\n';
                resultText += `响应状态: ${response.status}\n`;
                resultText += `成功标识: ${data.success}\n`;
                resultText += `响应代码: ${data.code}\n`;
                resultText += `数据条数: ${data.data ? data.data.length : 0}\n`;
                resultText += `总记录数: ${data.total}\n`;
                resultText += `TraceID: ${data.traceId}\n\n`;
                resultText += `响应数据预览:\n${JSON.stringify(data, null, 2).substring(0, 500)}...`;
                
                resultDiv.className = 'result success';
                resultDiv.textContent = resultText;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ API测试失败: ${error.message}`;
            }
        }

        // 测试下拉列表API
        async function testDropListAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            try {
                const response = await fetch('/charging-maintenance-server/st/lifePay/providerInfo/getDropLists', {
                    method: 'GET'
                });
                
                const data = await response.json();
                
                let resultText = '✅ 下拉列表API测试结果:\n\n';
                resultText += `响应状态: ${response.status}\n`;
                resultText += `成功标识: ${data.success}\n`;
                resultText += `响应代码: ${data.code}\n`;
                resultText += `数据内容:\n${JSON.stringify(data.data, null, 2)}`;
                
                resultDiv.className = 'result success';
                resultDiv.textContent = resultText;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 下拉列表API测试失败: ${error.message}`;
            }
        }

        // 测试CRUD操作
        async function testCRUDAPIs() {
            const resultDiv = document.getElementById('apiTestResult');
            let results = [];
            
            try {
                // 测试新增
                const addResponse = await fetch('/charging-maintenance-server/st/lifePay/providerInfo/add', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ serviceProvider: '测试服务商' })
                });
                const addData = await addResponse.json();
                results.push(`新增: ${addData.success ? '✅' : '❌'} ${addData.message}`);
                
                // 测试编辑
                const editResponse = await fetch('/charging-maintenance-server/st/lifePay/providerInfo/edit', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: 1, serviceProvider: '更新的服务商' })
                });
                const editData = await editResponse.json();
                results.push(`编辑: ${editData.success ? '✅' : '❌'} ${editData.message}`);
                
                // 测试删除
                const deleteResponse = await fetch('/charging-maintenance-server/st/lifePay/providerInfo/delete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: 1 })
                });
                const deleteData = await deleteResponse.json();
                results.push(`删除: ${deleteData.success ? '✅' : '❌'} ${deleteData.message}`);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ CRUD操作测试结果:\n\n' + results.join('\n');
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ CRUD测试失败: ${error.message}`;
            }
        }

        // 网络请求监控
        function monitorNetworkRequests() {
            if (networkMonitorActive) return;
            
            networkMonitorActive = true;
            requestLogs = [];
            
            window.fetch = function(...args) {
                const url = args[0];
                const options = args[1] || {};
                
                requestLogs.push({
                    timestamp: new Date().toLocaleTimeString(),
                    method: options.method || 'GET',
                    url: url,
                    intercepted: url.includes('/st/lifePay/providerInfo/')
                });
                
                updateNetworkMonitor();
                return originalFetch.apply(this, args);
            };
            
            document.getElementById('networkMonitor').textContent = '🔍 网络监控已启动，开始记录请求...';
        }

        function stopMonitoring() {
            networkMonitorActive = false;
            window.fetch = originalFetch;
            document.getElementById('networkMonitor').textContent = '⏹️ 网络监控已停止';
        }

        function updateNetworkMonitor() {
            const resultDiv = document.getElementById('networkMonitor');
            let monitorText = '🔍 网络请求监控 (最近10条):\n\n';
            
            const recentLogs = requestLogs.slice(-10);
            recentLogs.forEach(log => {
                const status = log.intercepted ? '🟢 已拦截' : '🔴 未拦截';
                monitorText += `${log.timestamp} [${log.method}] ${status}\n${log.url}\n\n`;
            });
            
            resultDiv.className = 'result info';
            resultDiv.textContent = monitorText;
        }

        // Mock系统控制
        async function enableMockSystem() {
            try {
                const mockSystem = await waitForMockSystem();
                mockSystem.enable();
                document.getElementById('controlResult').className = 'result success';
                document.getElementById('controlResult').textContent = '✅ Mock系统已启用';
            } catch (error) {
                document.getElementById('controlResult').className = 'result error';
                document.getElementById('controlResult').textContent = `❌ 启用失败: ${error.message}`;
            }
        }

        async function disableMockSystem() {
            try {
                const mockSystem = await waitForMockSystem();
                mockSystem.disable();
                document.getElementById('controlResult').className = 'result success';
                document.getElementById('controlResult').textContent = '✅ Mock系统已禁用';
            } catch (error) {
                document.getElementById('controlResult').className = 'result error';
                document.getElementById('controlResult').textContent = `❌ 禁用失败: ${error.message}`;
            }
        }

        async function clearMockCache() {
            try {
                const mockSystem = await waitForMockSystem();
                mockSystem.clearCache();
                document.getElementById('controlResult').className = 'result success';
                document.getElementById('controlResult').textContent = '✅ Mock缓存已清除';
            } catch (error) {
                document.getElementById('controlResult').className = 'result error';
                document.getElementById('controlResult').textContent = `❌ 清除缓存失败: ${error.message}`;
            }
        }

        // 显示调试信息
        async function showDebugInfo() {
            try {
                const mockSystem = await waitForMockSystem();
                const status = mockSystem.status();
                
                let debugText = '🔧 详细调试信息:\n\n';
                debugText += `浏览器信息:\n`;
                debugText += `- User Agent: ${navigator.userAgent}\n`;
                debugText += `- ServiceWorker支持: ${('serviceWorker' in navigator) ? '✅' : '❌'}\n\n`;
                
                debugText += `Mock系统状态:\n`;
                debugText += `- 启用: ${status.enabled}\n`;
                debugText += `- 初始化: ${status.initialized}\n`;
                debugText += `- ServiceWorker: ${status.serviceWorker}\n\n`;
                
                debugText += `环境变量:\n`;
                debugText += `- 当前域名: ${window.location.origin}\n`;
                debugText += `- 当前路径: ${window.location.pathname}\n\n`;
                
                debugText += `请查看浏览器控制台获取更多详细信息`;
                
                mockSystem.debug();
                
                document.getElementById('debugInfo').className = 'result info';
                document.getElementById('debugInfo').textContent = debugText;
            } catch (error) {
                document.getElementById('debugInfo').className = 'result error';
                document.getElementById('debugInfo').textContent = `❌ 获取调试信息失败: ${error.message}`;
            }
        }

        // 导出日志
        function exportLogs() {
            const logs = {
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                url: window.location.href,
                networkLogs: requestLogs,
                mockSystemAvailable: typeof window.MockSystem !== 'undefined'
            };
            
            const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mock-system-logs-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            document.getElementById('debugInfo').className = 'result success';
            document.getElementById('debugInfo').textContent = '✅ 日志已导出到下载文件';
        }

        // 工具函数
        function openDevTools() {
            alert('请按 F12 或右键选择"检查元素"打开开发者工具');
        }

        function refreshPage() {
            window.location.reload();
        }

        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkMockSystemStatus();
            }, 2000);
        });
    </script>
</body>
</html>
