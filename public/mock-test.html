<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock系统测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #e8e8e8;
            padding-bottom: 16px;
            margin-bottom: 24px;
        }
        .test-section {
            margin-bottom: 32px;
        }
        .test-section h3 {
            color: #1890ff;
            margin-bottom: 16px;
        }
        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        button.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        button.primary:hover {
            background: #40a9ff;
        }
        .result-area {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 4px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #52c41a; }
        .status-error { background-color: #ff4d4f; }
        .status-warning { background-color: #faad14; }
        .api-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .api-item {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 12px;
            background: #fafafa;
        }
        .api-method {
            font-weight: bold;
            color: #1890ff;
        }
        .api-url {
            font-family: monospace;
            color: #666;
            margin: 4px 0;
        }
        .api-status {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Mock系统测试页面</h1>
            <p>用于测试和验证接口模拟系统的功能</p>
        </div>

        <div class="test-section">
            <h3>系统状态</h3>
            <div class="button-group">
                <button class="primary" onclick="checkSystemStatus()">检查系统状态</button>
                <button onclick="enableMockSystem()">启用Mock系统</button>
                <button onclick="disableMockSystem()">禁用Mock系统</button>
                <button onclick="clearCache()">清除缓存</button>
            </div>
            <div id="systemStatus" class="result-area">点击"检查系统状态"查看当前状态...</div>
        </div>

        <div class="test-section">
            <h3>接口测试</h3>
            <div class="button-group">
                <button onclick="testServiceProviderAPI()">测试服务商信息接口</button>
                <button onclick="testBankFlowAPI()">测试银行流水接口</button>
                <button onclick="testDropListsAPI()">测试下拉列表接口</button>
                <button onclick="testCRUDOperations()">测试CRUD操作</button>
            </div>
            <div id="apiTestResult" class="result-area">选择要测试的接口...</div>
        </div>

        <div class="test-section">
            <h3>已配置的Mock接口</h3>
            <div id="mockApiList" class="api-list">
                <div class="api-item">
                    <div class="api-method">POST</div>
                    <div class="api-url">/st/lifePay/providerInfo/queryPage</div>
                    <div class="api-status">
                        <span class="status-indicator status-success"></span>
                        服务商信息分页查询
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-method">GET</div>
                    <div class="api-url">/st/lifePay/providerInfo/getDropLists</div>
                    <div class="api-status">
                        <span class="status-indicator status-success"></span>
                        服务商信息下拉列表
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-method">POST</div>
                    <div class="api-url">/st/lifePay/bankFlow/queryPage</div>
                    <div class="api-status">
                        <span class="status-indicator status-success"></span>
                        银行流水分页查询
                    </div>
                </div>
                <div class="api-item">
                    <div class="api-method">GET</div>
                    <div class="api-url">/st/lifePay/bankFlow/getDropLists</div>
                    <div class="api-status">
                        <span class="status-indicator status-success"></span>
                        银行流水下拉列表
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查系统状态
        function checkSystemStatus() {
            const statusDiv = document.getElementById('systemStatus');
            
            try {
                // 检查MockSystem是否可用
                if (typeof window.MockSystem !== 'undefined') {
                    const status = window.MockSystem.status();
                    statusDiv.innerHTML = `
                        <div style="color: #52c41a;">✅ Mock系统可用</div>
                        <pre>${JSON.stringify(status, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `
                        <div style="color: #faad14;">⚠️ MockSystem对象不可用</div>
                        <div>可能原因：</div>
                        <ul>
                            <li>Mock系统未初始化</li>
                            <li>ServiceWorker未注册成功</li>
                            <li>当前环境不支持Mock系统</li>
                        </ul>
                    `;
                }
            } catch (error) {
                statusDiv.innerHTML = `
                    <div style="color: #ff4d4f;">❌ 检查状态时出错</div>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // 启用Mock系统
        function enableMockSystem() {
            try {
                if (window.MockSystem) {
                    window.MockSystem.enable();
                    updateStatus('Mock系统已启用', 'success');
                } else {
                    updateStatus('MockSystem对象不可用', 'error');
                }
            } catch (error) {
                updateStatus(`启用失败: ${error.message}`, 'error');
            }
        }

        // 禁用Mock系统
        function disableMockSystem() {
            try {
                if (window.MockSystem) {
                    window.MockSystem.disable();
                    updateStatus('Mock系统已禁用', 'warning');
                } else {
                    updateStatus('MockSystem对象不可用', 'error');
                }
            } catch (error) {
                updateStatus(`禁用失败: ${error.message}`, 'error');
            }
        }

        // 清除缓存
        function clearCache() {
            try {
                if (window.MockSystem) {
                    window.MockSystem.clearCache();
                    updateStatus('缓存已清除', 'success');
                } else {
                    updateStatus('MockSystem对象不可用', 'error');
                }
            } catch (error) {
                updateStatus(`清除缓存失败: ${error.message}`, 'error');
            }
        }

        // 测试服务商信息接口
        async function testServiceProviderAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '正在测试服务商信息接口...';

            try {
                const response = await fetch('/st/lifePay/providerInfo/queryPage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pageNum: 1,
                        pageSize: 10
                    })
                });

                const data = await response.json();
                resultDiv.innerHTML = `
                    <div style="color: #52c41a;">✅ 服务商信息接口测试成功</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="color: #ff4d4f;">❌ 服务商信息接口测试失败</div>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // 测试银行流水接口
        async function testBankFlowAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '正在测试银行流水接口...';

            try {
                const response = await fetch('/st/lifePay/bankFlow/queryPage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pageNum: 1,
                        pageSize: 10
                    })
                });

                const data = await response.json();
                resultDiv.innerHTML = `
                    <div style="color: #52c41a;">✅ 银行流水接口测试成功</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="color: #ff4d4f;">❌ 银行流水接口测试失败</div>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // 测试下拉列表接口
        async function testDropListsAPI() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '正在测试下拉列表接口...';

            try {
                const response = await fetch('/st/lifePay/providerInfo/getDropLists', {
                    method: 'GET'
                });

                const data = await response.json();
                resultDiv.innerHTML = `
                    <div style="color: #52c41a;">✅ 下拉列表接口测试成功</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="color: #ff4d4f;">❌ 下拉列表接口测试失败</div>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // 测试CRUD操作
        async function testCRUDOperations() {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '正在测试CRUD操作...';

            const operations = [
                { name: '新增', url: '/st/lifePay/providerInfo/add', method: 'POST' },
                { name: '编辑', url: '/st/lifePay/providerInfo/edit', method: 'POST' },
                { name: '删除', url: '/st/lifePay/providerInfo/delete', method: 'POST' }
            ];

            let results = [];

            for (const op of operations) {
                try {
                    const response = await fetch(op.url, {
                        method: op.method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ id: 1 })
                    });

                    const data = await response.json();
                    results.push(`✅ ${op.name}操作成功: ${data.message}`);
                } catch (error) {
                    results.push(`❌ ${op.name}操作失败: ${error.message}`);
                }
            }

            resultDiv.innerHTML = `
                <div>CRUD操作测试结果:</div>
                ${results.map(result => `<div>${result}</div>`).join('')}
            `;
        }

        // 更新状态显示
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('systemStatus');
            const color = type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#faad14';
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : '⚠️';
            
            statusDiv.innerHTML = `<div style="color: ${color};">${icon} ${message}</div>`;
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
