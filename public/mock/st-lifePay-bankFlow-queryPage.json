{"data": [{"id": 1, "industry": "电力行业", "isNucleated": "是", "nucleationSubmitTime": "2024-01-15 10:30:00", "reasonClassification": "正常交易", "subReasonClassification": "充电费用", "cleanupClassification": "已清理", "remark": "充电桩收费", "matchedBillMonth": "2024-01", "unNucleatedAmount": 0, "ourAccountNumber": "1234567890123456789", "bankTransactionDate": "2024-01-15", "currency": "CNY", "debitAmount": 0, "creditAmount": 1500.0, "treasuryFlowType": "收入", "bankRemark": "充电服务费", "counterpartyBankName": "中国工商银行", "openingBank": "工商银行北京分行", "counterpartyAccountNumber": "9876543210987654321", "supplementaryNotes": "", "flowStatus": "已匹配", "flowDestination": "充电收入", "flowDestinationAccount": "主营业务收入", "bankFlowNumber": "BF202401150001", "createBy": "系统", "createTime": "2024-01-15 10:30:00", "tenantId": 1, "updateTime": "2024-01-15 10:30:00", "updateBy": "系统", "dataSource": "银行接口"}, {"id": 2, "industry": "电力行业", "isNucleated": "否", "nucleationSubmitTime": "", "reasonClassification": "异常交易", "subReasonClassification": "退款", "cleanupClassification": "待清理", "remark": "充电退款", "matchedBillMonth": "2024-01", "unNucleatedAmount": 200.0, "ourAccountNumber": "1234567890123456789", "bankTransactionDate": "2024-01-16", "currency": "CNY", "debitAmount": 200.0, "creditAmount": 0, "treasuryFlowType": "支出", "bankRemark": "充电退款", "counterpartyBankName": "中国建设银行", "openingBank": "建设银行上海分行", "counterpartyAccountNumber": "5432167890543216789", "supplementaryNotes": "用户申请退款", "flowStatus": "待匹配", "flowDestination": "退款支出", "flowDestinationAccount": "营业外支出", "bankFlowNumber": "BF202401160001", "createBy": "系统", "createTime": "2024-01-16 14:20:00", "tenantId": 1, "updateTime": "2024-01-16 14:20:00", "updateBy": "系统", "dataSource": "银行接口"}], "total": 15, "pageNum": 1, "pageSize": 10, "message": "查询成功"}