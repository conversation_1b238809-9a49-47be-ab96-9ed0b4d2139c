{"success": true, "code": "10000", "message": "查询成功", "data": [{"id": 1, "serviceProvider": "支付宝（中国）网络技术有限公司", "agreementType": "框架协议", "billingInstitution": "蚂蚁科技集团股份有限公司", "mid": "2088123456789012", "pid": "2088987654321098", "goLiveTime": "2023-01", "commissionStartTime": "2023-01", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.15", "rate": "0.006", "isDiscounted": "是", "currCommissionRatio": "0.18", "remark": "主要服务商，业务量大", "status": "正常", "archiveTime": "2024-01", "originProvider": "", "billingInstitutionCode": "ANT001", "updateBy": "张三", "updateTime": "2024-01-15 10:30:00", "createBy": "李四", "createTime": "2023-01-10 09:00:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 2, "serviceProvider": "腾讯科技（深圳）有限公司", "agreementType": "合作协议", "billingInstitution": "财付通支付科技有限公司", "mid": "1234567890123456", "pid": "9876543210987654", "goLiveTime": "2023-03", "commissionStartTime": "2023-03", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.12", "rate": "0.005", "isDiscounted": "否", "currCommissionRatio": "0.15", "remark": "微信支付合作伙伴", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "TEN001", "updateBy": "王五", "updateTime": "2024-01-20 14:20:00", "createBy": "赵六", "createTime": "2023-03-05 11:15:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 3, "serviceProvider": "银联商务股份有限公司", "agreementType": "战略合作协议", "billingInstitution": "中国银联股份有限公司", "mid": "5678901234567890", "pid": "0987654321098765", "goLiveTime": "2023-02", "commissionStartTime": "2023-02", "commissionEndTime": "2025-01", "lastCommissionRatio": "0.10", "rate": "0.004", "isDiscounted": "是", "currCommissionRatio": "0.12", "remark": "银联支付渠道", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "UNI001", "updateBy": "孙七", "updateTime": "2024-01-18 16:45:00", "createBy": "周八", "createTime": "2023-02-15 13:30:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 4, "serviceProvider": "京东数字科技控股股份有限公司", "agreementType": "合作协议", "billingInstitution": "网银在线（北京）科技有限公司", "mid": "9012345678901234", "pid": "5432109876543210", "goLiveTime": "2023-04", "commissionStartTime": "2023-04", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.08", "rate": "0.0055", "isDiscounted": "否", "currCommissionRatio": "0.10", "remark": "京东支付合作", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "JD001", "updateBy": "吴九", "updateTime": "2024-01-22 09:15:00", "createBy": "郑十", "createTime": "2023-04-08 15:20:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 5, "serviceProvider": "度小满金融（北京）科技有限公司", "agreementType": "框架协议", "billingInstitution": "百度金融科技有限公司", "mid": "3456789012345678", "pid": "8765432109876543", "goLiveTime": "2023-05", "commissionStartTime": "2023-05", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.06", "rate": "0.007", "isDiscounted": "是", "currCommissionRatio": "0.08", "remark": "百度支付渠道", "status": "暂停", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "BD001", "updateBy": "冯十一", "updateTime": "2024-01-25 11:30:00", "createBy": "陈十二", "createTime": "2023-05-12 10:45:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 6, "serviceProvider": "苏宁易付宝网络科技股份有限公司", "agreementType": "合作协议", "billingInstitution": "苏宁金融服务有限公司", "mid": "7890123456789012", "pid": "2109876543210987", "goLiveTime": "2023-06", "commissionStartTime": "2023-06", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.05", "rate": "0.0065", "isDiscounted": "否", "currCommissionRatio": "0.07", "remark": "苏宁支付合作", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "SN001", "updateBy": "褚十三", "updateTime": "2024-01-28 14:10:00", "createBy": "卫十四", "createTime": "2023-06-20 16:25:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 7, "serviceProvider": "拉卡拉支付股份有限公司", "agreementType": "战略合作协议", "billingInstitution": "拉卡拉支付股份有限公司", "mid": "1357924680135792", "pid": "9753186420975318", "goLiveTime": "2023-07", "commissionStartTime": "2023-07", "commissionEndTime": "2025-06", "lastCommissionRatio": "0.04", "rate": "0.0045", "isDiscounted": "是", "currCommissionRatio": "0.06", "remark": "线下支付主要渠道", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "LKL001", "updateBy": "蒋十五", "updateTime": "2024-01-30 12:50:00", "createBy": "沈十六", "createTime": "2023-07-03 08:40:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 8, "serviceProvider": "汇付天下有限公司", "agreementType": "合作协议", "billingInstitution": "汇付数据有限公司", "mid": "2468013579246801", "pid": "8642097531864209", "goLiveTime": "2023-08", "commissionStartTime": "2023-08", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.03", "rate": "0.008", "isDiscounted": "否", "currCommissionRatio": "0.05", "remark": "B端支付服务", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "HF001", "updateBy": "韩十七", "updateTime": "2024-02-01 15:35:00", "createBy": "杨十八", "createTime": "2023-08-15 12:10:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 9, "serviceProvider": "快钱支付清算信息有限公司", "agreementType": "框架协议", "billingInstitution": "快钱支付清算信息有限公司", "mid": "9876543210987654", "pid": "1234567890123456", "goLiveTime": "2023-09", "commissionStartTime": "2023-09", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.025", "rate": "0.0075", "isDiscounted": "是", "currCommissionRatio": "0.04", "remark": "企业支付解决方案", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "KQ001", "updateBy": "朱十九", "updateTime": "2024-02-03 10:20:00", "createBy": "秦二十", "createTime": "2023-09-28 14:55:00", "tenantId": 1, "dataSource": "系统录入"}, {"id": 10, "serviceProvider": "易宝支付有限公司", "agreementType": "合作协议", "billingInstitution": "易宝支付有限公司", "mid": "5432167890543216", "pid": "0987654321098765", "goLiveTime": "2023-10", "commissionStartTime": "2023-10", "commissionEndTime": "2024-12", "lastCommissionRatio": "0.02", "rate": "0.009", "isDiscounted": "否", "currCommissionRatio": "0.035", "remark": "行业支付专家", "status": "正常", "archiveTime": "", "originProvider": "", "billingInstitutionCode": "YB001", "updateBy": "尤二一", "updateTime": "2024-02-05 13:40:00", "createBy": "许二二", "createTime": "2023-10-10 11:25:00", "tenantId": 1, "dataSource": "系统录入"}], "total": 25, "pageNum": 1, "pageSize": 10, "traceId": "mock-1704067200000-abc123"}