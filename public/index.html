<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title>能源维保通</title>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but back-manage-vue doesn't work properly without
        JavaScript enabled. Please enable it to continue.</strong
      >
    </noscript>
    <script>
      !(function(win, b, d, a) {
        win[a] || (win[a] = {});
        win[a].config = {
          project: "<%= process.env.VUE_APP_TRACK_PROJECT_ID %>",
          console: true, //是否开启控制台打印
          reportUrl: '<%= process.env.VUE_APP_TRACK_REPORT_URL %>',//配置项目数据接收地址
          source: 'web'
          //sourceQueryString: 'chInfo'//自定义页面来源query参数
          //enableVTrack:true,//是否开启可视化埋点
          //enableAppJSBridge:true,//是否通过App上报
        };
        with (b) {
          with (body) {
            with (insertBefore(createElement("script"), firstChild)) {
              setAttribute("crossorigin", "", (src = d));

              // 给script标签新增onload事件监听
              setAttribute("onload", "handleSDKLoaded");
              // sdk初始化代码完成
              window.handleSDKLoaded = function() {
                console.log("window.handleSDKLoaded");
              };
            }
          }
        }
      })(
        window,
        document,
        "https://oss-static.bangdao-tech.com/biz-track/2.2.13/biz-track.min.js",
        "_bzt"
      );
    </script>
    <div id="app"></div>
  </body>
</html>
