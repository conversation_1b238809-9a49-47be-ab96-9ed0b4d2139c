# 接口模拟系统 - 快速开始指南

## 🚀 快速开始

### 1. 启用Mock系统

在项目根目录的 `.env.development` 文件中添加：

```bash
VUE_APP_ENABLE_MOCK=true
```

### 2. 启动项目

```bash
npm run dev
```

### 3. 验证系统运行

访问测试页面：`http://localhost:2266/mock-test.html`

或在浏览器控制台中执行：

```javascript
// 检查系统状态
MockSystem.status();

// 测试特定接口
MockSystem.test('/st/lifePay/providerInfo/queryPage');
```

## 📁 文件结构

```
项目根目录/
├── mock.config.js                    # 全局配置文件
├── public/
│   ├── sw-mock-manager.js            # ServiceWorker核心模块
│   ├── mock-test.html                # 测试页面
│   └── mock/                         # Mock数据文件目录
│       ├── st-lifePay-providerInfo-queryPage.json
│       ├── st-lifePay-providerInfo-getDropLists.json
│       ├── st-lifePay-bankFlow-queryPage.json
│       └── ...
├── src/
│   └── utils/
│       └── mock-system.js            # 系统初始化工具
├── scripts/
│   └── generate-mock-template.js     # 数据模板生成工具
└── doc/
    └── 接口模拟系统使用说明.md        # 详细使用说明
```

## 🛠️ 常用操作

### 为新页面添加Mock数据

#### 方法1：使用生成工具（推荐）

```bash
# 生成完整模块的Mock文件
node scripts/generate-mock-template.js system user --all

# 生成单个接口的Mock文件
node scripts/generate-mock-template.js system user queryPage
```

#### 方法2：手动创建

1. 在 `public/mock/` 目录下创建JSON文件
2. 按照命名规范：`{模块名}-{功能名}-{操作名}.json`
3. 参考现有文件格式编写数据

### 配置接口模拟

在 `mock.config.js` 中添加配置：

```javascript
// 启用特定接口
apis: {
  '/system/user/queryPage': true,
  '/system/user/*': true,  // 通配符匹配
}

// 启用模块
modules: {
  system: {
    enabled: true,
    subModules: {
      user: { enabled: true }
    }
  }
}
```

### 运行时控制

```javascript
// 启用/禁用Mock系统
MockSystem.enable();
MockSystem.disable();

// 清除缓存
MockSystem.clearCache();

// 查看配置
MockSystem.config();

// 测试接口
MockSystem.test('/your/api/path');
```

## 📋 Mock数据格式规范

### 分页查询响应

```json
{
  "data": [...],
  "total": 50,
  "pageNum": 1,
  "pageSize": 10,
  "message": "查询成功"
}
```

### 下拉列表响应

```json
{
  "data": {
    "status": ["启用", "停用"],
    "type": ["类型1", "类型2"]
  },
  "message": "获取下拉列表成功"
}
```

### 操作结果响应

```json
{
  "data": {
    "id": 1,
    "message": "操作成功"
  },
  "message": "新增成功"
}
```

## 🔧 故障排除

### 常见问题

1. **Mock系统未生效**
   - 检查环境变量：`VUE_APP_ENABLE_MOCK=true`
   - 确认在HTTPS或localhost环境下运行
   - 查看浏览器控制台是否有错误

2. **ServiceWorker注册失败**
   - 检查浏览器是否支持ServiceWorker
   - 确认文件路径正确：`/sw-mock-manager.js`
   - 查看Application标签中的Service Workers状态

3. **Mock数据格式错误**
   - 验证JSON文件语法
   - 确认响应格式符合项目规范
   - 检查字段名是否与前端组件匹配

### 调试技巧

1. **启用调试模式**
   ```javascript
   // 在mock.config.js中设置
   serviceWorker: {
     debug: true
   }
   ```

2. **查看网络请求**
   - 打开开发者工具Network标签
   - 查看请求是否被拦截
   - 检查响应头和数据

3. **ServiceWorker调试**
   - 打开Application → Service Workers
   - 查看注册状态和日志
   - 使用Update和Unregister按钮

## 🎯 最佳实践

### 1. 数据真实性
- Mock数据应接近真实业务场景
- 包含各种边界情况
- 保持数据的一致性

### 2. 性能优化
- 避免单个JSON文件过大
- 合理设置网络延迟
- 定期清理缓存

### 3. 团队协作
- 建立统一的命名规范
- 定期同步Mock数据
- 文档化特殊场景

### 4. 版本管理
- Mock数据文件纳入版本控制
- 及时更新数据结构
- 保持与API文档同步

## 📚 相关文档

- [详细使用说明](doc/接口模拟系统使用说明.md)
- [技术栈说明](doc/技术栈说明.md)
- [网络接口封装使用说明](doc/网络接口封装使用说明.md)
- [BusCrud组件使用指南](doc/BusCrud组件使用指南.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**注意**: 确保在生产环境中禁用Mock系统（`VUE_APP_ENABLE_MOCK=false`）
