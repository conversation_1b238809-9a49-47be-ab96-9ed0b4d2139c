/**
 * Mock 接口模拟系统配置文件
 *
 * 该配置文件用于控制接口模拟系统的行为，支持环境变量控制、
 * 细粒度的模块和接口控制，以及模拟数据的存放路径配置。
 */

// 环境变量配置
const isDevelopment = process.env.NODE_ENV === "development";
const isProduction = process.env.NODE_ENV === "production";

// 全局模拟开关
const MOCK_CONFIG = {
  // 是否启用模拟系统（可通过环境变量 VUE_APP_ENABLE_MOCK 控制）
  // 确保只在开发环境且明确启用时才开启Mock系统
  enabled:
    process.env.VUE_APP_ENABLE_MOCK === "true" &&
    isDevelopment &&
    process.env.NODE_ENV === "development",

  // ServiceWorker 配置
  serviceWorker: {
    // ServiceWorker 文件路径
    scriptPath: "/sw-mock-manager.js",
    // 是否在控制台显示调试信息
    debug: isDevelopment,
    // 模拟网络延迟（毫秒）
    networkDelay: {
      min: 100,
      max: 800,
    },
  },

  // Mock 数据文件配置
  mockData: {
    // Mock 数据文件根目录
    basePath: "/mock/",
    // 文件命名规范：[模块名]-[功能名].json
    fileNamingPattern: "{module}-{action}.json",
    // 默认分页配置
    pagination: {
      defaultPageSize: 10,
      maxPageSize: 100,
    },
  },

  // 模块级别控制
  modules: {
    // 结算管理模块
    settlement: {
      enabled: false,
      // 子模块控制
      subModules: {
        lifePay: {
          enabled: false,
          // 具体功能控制
          features: {
            serviceProvider: true, // 服务商信息
            bankFlow: true, // 银行流水
            alipayBd: true, // 支付宝与邦道账单
            aliPayOrg: true, // 支付宝与机构账单
            bdProvider: true, // 邦道与服务商账单
            institInfo: true, // 机构信息
            providerInfo: true, // 服务商信息
            publicService: true, // 公服相关
          },
        },
        destinationCharge: {
          enabled: false,
        },
        sellElectricity: {
          enabled: false,
        },
      },
    },

    // 系统管理模块
    system: {
      enabled: false,
    },

    // 用户管理模块
    user: {
      enabled: false,
    },
  },

  // 接口级别精确控制
  apis: {
    // 支持通配符匹配
    "/st/lifePay/providerInfo/*": true,
    "/st/lifePay/bankFlow/*": true,
    "/st/lifePay/alipayBd/*": true,
    "/st/lifePay/aliPayOrg/*": true,
    "/st/lifePay/bdProvider/*": true,
    "/st/lifePay/institInfo/*": true,
    "/st/lifePay/public/*": true,

    // 具体接口控制
    "/st/lifePay/providerInfo/queryPage": true,
    "/st/lifePay/providerInfo/exportExcel": true,
    "/st/lifePay/providerInfo/importExcel": true,
    "/st/lifePay/providerInfo/getDropLists": true,
    "/st/lifePay/providerInfo/add": true,
    "/st/lifePay/providerInfo/edit": true,
    "/st/lifePay/providerInfo/delete": true,

    // 禁用的接口示例
    "/system/user/login": false,
    "/system/user/logout": false,
  },

  // 响应格式配置
  response: {
    // 标准成功响应格式
    successFormat: {
      success: true,
      code: "10000",
      message: "success",
      data: null,
      traceId: () =>
        "mock-" +
        Date.now() +
        "-" +
        Math.random()
          .toString(36)
          .substr(2, 9),
    },

    // 标准错误响应格式
    errorFormat: {
      success: false,
      code: "50000",
      message: "Internal Server Error",
      data: null,
      traceId: () =>
        "mock-" +
        Date.now() +
        "-" +
        Math.random()
          .toString(36)
          .substr(2, 9),
    },

    // 分页响应格式
    paginationFormat: {
      success: true,
      code: "10000",
      message: "success",
      data: [],
      traceId: () =>
        "mock-" +
        Date.now() +
        "-" +
        Math.random()
          .toString(36)
          .substr(2, 9),
      pageNum: 1,
      pageSize: 10,
      total: 0,
    },
  },

  // 特殊处理配置
  special: {
    // 文件下载接口
    downloadApis: [
      "/st/lifePay/providerInfo/exportExcel",
      "/st/lifePay/bankFlow/exportExcel",
      "/st/lifePay/alipayBd/exportExcel",
    ],

    // 文件上传接口
    uploadApis: [
      "/st/lifePay/providerInfo/importExcel",
      "/st/lifePay/bankFlow/importExcel",
      "/st/lifePay/alipayBd/importExcel",
    ],

    // 需要加密的接口
    encryptedApis: [],

    // 需要特殊权限的接口
    permissionApis: [
      "/st/lifePay/providerInfo/delete",
      "/st/lifePay/providerInfo/edit",
    ],
  },

  // 运行时控制
  runtime: {
    // 是否允许运行时切换模拟状态
    allowToggle: isDevelopment,
    // 控制台命令前缀
    consolePrefix: "MockSystem",
    // 存储模拟状态的 localStorage key
    storageKey: "mock-system-state",
  },
};

// 工具函数
const MockUtils = {
  /**
   * 检查接口是否启用模拟
   * @param {string} url - 接口URL
   * @returns {boolean}
   */
  isApiEnabled(url) {
    if (!MOCK_CONFIG.enabled) return false;

    // 检查精确匹配
    if (MOCK_CONFIG.apis.hasOwnProperty(url)) {
      return MOCK_CONFIG.apis[url];
    }

    // 检查通配符匹配
    for (const pattern in MOCK_CONFIG.apis) {
      if (pattern.includes("*")) {
        const regex = new RegExp(pattern.replace(/\*/g, ".*"));
        if (regex.test(url) && MOCK_CONFIG.apis[pattern]) {
          return true;
        }
      }
    }

    return false;
  },

  /**
   * 检查模块是否启用
   * @param {string} modulePath - 模块路径，如 'settlement.lifePay.serviceProvider'
   * @returns {boolean}
   */
  isModuleEnabled(modulePath) {
    if (!MOCK_CONFIG.enabled) return false;

    const pathParts = modulePath.split(".");
    let current = MOCK_CONFIG.modules;

    for (const part of pathParts) {
      if (!current[part] || !current[part].enabled) {
        return false;
      }
      current = current[part].subModules || current[part].features || {};
    }

    return true;
  },

  /**
   * 获取Mock数据文件路径
   * @param {string} module - 模块名
   * @param {string} action - 操作名
   * @returns {string}
   */
  getMockDataPath(module, action) {
    const fileName = MOCK_CONFIG.mockData.fileNamingPattern
      .replace("{module}", module)
      .replace("{action}", action);
    return MOCK_CONFIG.mockData.basePath + fileName;
  },

  /**
   * 生成标准响应
   * @param {any} data - 响应数据
   * @param {boolean} isSuccess - 是否成功
   * @param {string} message - 响应消息
   * @returns {object}
   */
  generateResponse(data, isSuccess = true, message = "success") {
    const format = isSuccess
      ? MOCK_CONFIG.response.successFormat
      : MOCK_CONFIG.response.errorFormat;

    return {
      ...format,
      data,
      message,
      traceId:
        typeof format.traceId === "function"
          ? format.traceId()
          : format.traceId,
    };
  },

  /**
   * 生成分页响应
   * @param {array} data - 数据数组
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 页大小
   * @param {number} total - 总数
   * @returns {object}
   */
  generatePaginationResponse(data, pageNum = 1, pageSize = 10, total = 0) {
    return {
      ...MOCK_CONFIG.response.paginationFormat,
      data,
      pageNum,
      pageSize,
      total,
      traceId:
        typeof MOCK_CONFIG.response.paginationFormat.traceId === "function"
          ? MOCK_CONFIG.response.paginationFormat.traceId()
          : MOCK_CONFIG.response.paginationFormat.traceId,
    };
  },
};

// 运行时控制函数（仅在开发环境可用）
if (MOCK_CONFIG.runtime.allowToggle && typeof window !== "undefined") {
  window[MOCK_CONFIG.runtime.consolePrefix] = {
    enable: () => {
      localStorage.setItem(MOCK_CONFIG.runtime.storageKey, "true");
      console.log("Mock system enabled");
    },
    disable: () => {
      localStorage.setItem(MOCK_CONFIG.runtime.storageKey, "false");
      console.log("Mock system disabled");
    },
    status: () => {
      console.log("Mock system status:", MOCK_CONFIG.enabled);
    },
    config: () => {
      console.log("Mock system config:", MOCK_CONFIG);
    },
  };
}

// 兼容CommonJS和ES6模块
if (typeof module !== "undefined" && module.exports) {
  module.exports = { MOCK_CONFIG, MockUtils };
  module.exports.default = MOCK_CONFIG;
} else {
  window.MOCK_CONFIG = MOCK_CONFIG;
  window.MockUtils = MockUtils;
}

export { MOCK_CONFIG, MockUtils };
export default MOCK_CONFIG;
