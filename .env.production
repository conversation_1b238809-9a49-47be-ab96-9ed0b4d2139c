# 生产环境配置
ENV = 'production'

VUE_APP_NAME = "production"

VUE_APP_BASE_API_URL = '/charging-maintenance-flow'
VUE_APP_BASE_API = 'https://napi.bangdao-tech.com/charging-maintenance-server'

VUE_APP_BASE_API_PROCESS = "https://napi.bangdao-tech.com/charging-maintenance-flow"

VUE_APP_WHALE_FLOW_KEY = 'SF-CM'

VUE_APP_BASE_UPLOAD_URL = "https://napi.bangdao-tech.com/charging-maintenance-server/flow-docking/api/v1/files/upload"

VUE_APP_TRACK_PROJECT_ID = 'PJ1711334488002'
VUE_APP_TRACK_REPORT_URL = "https://napi.bangdao-tech.com/monitor/collect"

# Mock系统配置 - 生产环境禁用
VUE_APP_ENABLE_MOCK = false