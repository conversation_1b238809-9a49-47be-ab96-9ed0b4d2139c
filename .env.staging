NODE_ENV = staging

# 测试环境配置
ENV = 'staging'

VUE_APP_NAME = "staging"

VUE_APP_BASE_API_URL = '/charging-maintenance-flow'
VUE_APP_BASE_API = 'https://test-napi.bangdao-tech.com/charging-maintenance-server'

VUE_APP_BASE_API_PROCESS = "https://test-napi.bangdao-tech.com/charging-maintenance-flow"

VUE_APP_WHALE_FLOW_KEY = 'SF-CM'

VUE_APP_BASE_UPLOAD_URL = "https://test-napi.bangdao-tech.com/charging-maintenance-server/flow-docking/api/v1/files/upload"

VUE_APP_TRACK_PROJECT_ID = 'PJ1712628237001'
VUE_APP_TRACK_REPORT_URL = "https://test-napi.bangdao-tech.com/monitor/collect"

# Mock系统配置 - 测试环境禁用
VUE_APP_ENABLE_MOCK = false