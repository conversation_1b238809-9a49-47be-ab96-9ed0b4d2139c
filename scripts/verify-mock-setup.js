/**
 * Mock系统设置验证脚本
 * 
 * 简单验证Mock系统的文件和配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 验证Mock系统设置...\n');

// 检查关键文件
const files = [
  { path: 'mock.config.js', name: 'Mock配置文件' },
  { path: '.env.development', name: '开发环境配置' },
  { path: 'public/sw-mock-manager.js', name: 'ServiceWorker文件' },
  { path: 'src/utils/mock-system.js', name: 'Mock系统初始化文件' },
  { path: 'public/mock/st-lifePay-providerInfo-queryPage.json', name: '分页查询Mock数据' },
  { path: 'public/mock/st-lifePay-providerInfo-getDropLists.json', name: '下拉列表Mock数据' },
  { path: 'public/mock/st-lifePay-providerInfo-add.json', name: '新增操作Mock数据' },
  { path: 'public/mock/st-lifePay-providerInfo-edit.json', name: '编辑操作Mock数据' },
  { path: 'public/mock/st-lifePay-providerInfo-delete.json', name: '删除操作Mock数据' }
];

console.log('📋 检查关键文件:');
files.forEach(file => {
  const fullPath = path.join(__dirname, '..', file.path);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${file.name}: ${file.path}`);
  } else {
    console.log(`❌ ${file.name}: ${file.path} (文件不存在)`);
  }
});

// 检查环境变量
console.log('\n📋 检查环境变量:');
const envPath = path.join(__dirname, '../.env.development');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  if (envContent.includes('VUE_APP_ENABLE_MOCK = true')) {
    console.log('✅ VUE_APP_ENABLE_MOCK = true');
  } else {
    console.log('❌ VUE_APP_ENABLE_MOCK 未设置为 true');
  }
} else {
  console.log('❌ .env.development 文件不存在');
}

// 检查Mock数据格式
console.log('\n📋 检查Mock数据格式:');
const mockFiles = [
  'st-lifePay-providerInfo-queryPage.json',
  'st-lifePay-providerInfo-getDropLists.json',
  'st-lifePay-providerInfo-add.json'
];

mockFiles.forEach(fileName => {
  const filePath = path.join(__dirname, '../public/mock', fileName);
  if (fs.existsSync(filePath)) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(content);
      
      if (data.success !== undefined && data.code !== undefined) {
        console.log(`✅ ${fileName}: 格式正确`);
      } else {
        console.log(`⚠️  ${fileName}: 格式可能不正确 (缺少success或code字段)`);
      }
    } catch (error) {
      console.log(`❌ ${fileName}: JSON格式错误`);
    }
  } else {
    console.log(`❌ ${fileName}: 文件不存在`);
  }
});

// 检查main.js集成
console.log('\n📋 检查main.js集成:');
const mainJsPath = path.join(__dirname, '../src/main.js');
if (fs.existsSync(mainJsPath)) {
  const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
  if (mainJsContent.includes('mock-system')) {
    console.log('✅ main.js已集成Mock系统');
  } else {
    console.log('❌ main.js未集成Mock系统');
  }
} else {
  console.log('❌ main.js文件不存在');
}

console.log('\n🎉 验证完成！');
console.log('\n📝 下一步操作:');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 访问测试页面: http://localhost:2266/mock-test.html');
console.log('3. 访问服务商信息页面: http://localhost:2266/#/settlement/lifePay/serviceProvider');
console.log('4. 在浏览器控制台使用以下命令测试:');
console.log('   MockSystem.status()');
console.log('   MockSystem.test("/st/lifePay/providerInfo/queryPage")');
