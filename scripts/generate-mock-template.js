/**
 * Mock数据模板生成工具
 * 
 * 用于快速生成标准格式的Mock数据文件模板
 * 使用方法：node scripts/generate-mock-template.js [模块名] [功能名] [操作名]
 */

const fs = require('fs');
const path = require('path');

// 模板配置
const templates = {
  // 分页查询模板
  queryPage: {
    data: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    message: "查询成功"
  },
  
  // 下拉列表模板
  getDropLists: {
    data: {
      status: ["启用", "停用"],
      type: ["类型1", "类型2", "类型3"]
    },
    message: "获取下拉列表成功"
  },
  
  // 新增操作模板
  add: {
    data: {
      id: 1,
      message: "新增成功"
    },
    message: "新增成功"
  },
  
  // 编辑操作模板
  edit: {
    data: {
      message: "编辑成功"
    },
    message: "编辑成功"
  },
  
  // 删除操作模板
  delete: {
    data: {
      message: "删除成功"
    },
    message: "删除成功"
  },
  
  // 导出操作模板
  exportExcel: {
    data: "mock-export-url",
    message: "导出成功",
    fileName: "导出文件.xlsx"
  },
  
  // 导入操作模板
  importExcel: {
    data: "导入成功，共处理 0 条数据",
    message: "导入完成",
    details: {
      total: 0,
      success: 0,
      failed: 0,
      errors: []
    }
  }
};

// 字段类型映射
const fieldTypes = {
  id: () => Math.floor(Math.random() * 1000) + 1,
  name: (prefix = "名称") => `${prefix}${Math.floor(Math.random() * 100) + 1}`,
  status: () => Math.random() > 0.5 ? "启用" : "停用",
  createTime: () => new Date().toISOString().replace('T', ' ').substring(0, 19),
  updateTime: () => new Date().toISOString().replace('T', ' ').substring(0, 19),
  createBy: () => "系统",
  updateBy: () => "系统",
  tenantId: () => 1,
  dataSource: () => "系统录入",
  remark: () => "备注信息"
};

/**
 * 生成Mock数据文件
 * @param {string} module - 模块名
 * @param {string} feature - 功能名
 * @param {string} action - 操作名
 * @param {object} options - 选项
 */
function generateMockFile(module, feature, action, options = {}) {
  const fileName = `${module}-${feature}-${action}.json`;
  const filePath = path.join(__dirname, '../public/mock', fileName);
  
  // 获取模板
  let template = templates[action];
  if (!template) {
    console.warn(`未找到操作 "${action}" 的模板，使用默认模板`);
    template = {
      data: null,
      message: "操作成功"
    };
  }
  
  // 深拷贝模板
  const mockData = JSON.parse(JSON.stringify(template));
  
  // 如果是分页查询，生成示例数据
  if (action === 'queryPage') {
    mockData.data = generateListData(options.fields || [], options.count || 10);
    mockData.total = options.total || 50;
  }
  
  // 确保目录存在
  const mockDir = path.dirname(filePath);
  if (!fs.existsSync(mockDir)) {
    fs.mkdirSync(mockDir, { recursive: true });
  }
  
  // 写入文件
  fs.writeFileSync(filePath, JSON.stringify(mockData, null, 2), 'utf8');
  console.log(`✅ 已生成Mock数据文件: ${fileName}`);
  
  return filePath;
}

/**
 * 生成列表数据
 * @param {array} fields - 字段配置
 * @param {number} count - 数据条数
 * @returns {array}
 */
function generateListData(fields, count) {
  const data = [];
  
  for (let i = 0; i < count; i++) {
    const item = {};
    
    // 默认字段
    item.id = i + 1;
    item.createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      .toISOString().replace('T', ' ').substring(0, 19);
    item.updateTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
    
    // 自定义字段
    fields.forEach(field => {
      if (typeof field === 'string') {
        // 简单字段名
        if (fieldTypes[field]) {
          item[field] = fieldTypes[field]();
        } else {
          item[field] = `${field}${i + 1}`;
        }
      } else if (typeof field === 'object') {
        // 复杂字段配置
        const { name, type, values, generator } = field;
        
        if (generator && typeof generator === 'function') {
          item[name] = generator(i);
        } else if (values && Array.isArray(values)) {
          item[name] = values[Math.floor(Math.random() * values.length)];
        } else if (fieldTypes[type]) {
          item[name] = fieldTypes[type](name);
        } else {
          item[name] = `${name}${i + 1}`;
        }
      }
    });
    
    data.push(item);
  }
  
  return data;
}

/**
 * 生成完整模块的Mock文件
 * @param {string} module - 模块名
 * @param {string} feature - 功能名
 * @param {object} config - 配置
 */
function generateModuleMockFiles(module, feature, config = {}) {
  const actions = ['queryPage', 'getDropLists', 'add', 'edit', 'delete', 'exportExcel', 'importExcel'];
  
  console.log(`🚀 开始生成 ${module}-${feature} 模块的Mock文件...`);
  
  actions.forEach(action => {
    try {
      generateMockFile(module, feature, action, config[action] || {});
    } catch (error) {
      console.error(`❌ 生成 ${action} 文件失败:`, error.message);
    }
  });
  
  console.log(`✅ ${module}-${feature} 模块Mock文件生成完成`);
}

/**
 * 命令行接口
 */
function cli() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
Mock数据模板生成工具

使用方法:
  node scripts/generate-mock-template.js <模块名> <功能名> [操作名]
  node scripts/generate-mock-template.js <模块名> <功能名> --all

示例:
  # 生成单个文件
  node scripts/generate-mock-template.js st-lifePay providerInfo queryPage
  
  # 生成完整模块
  node scripts/generate-mock-template.js st-lifePay providerInfo --all
  
  # 生成用户管理模块
  node scripts/generate-mock-template.js system user --all

支持的操作名:
  - queryPage: 分页查询
  - getDropLists: 下拉列表
  - add: 新增
  - edit: 编辑
  - delete: 删除
  - exportExcel: 导出Excel
  - importExcel: 导入Excel
    `);
    return;
  }
  
  const [module, feature, actionOrFlag] = args;
  
  if (!module || !feature) {
    console.error('❌ 请提供模块名和功能名');
    return;
  }
  
  if (actionOrFlag === '--all') {
    // 生成完整模块
    const config = {
      queryPage: {
        fields: [
          'name',
          'status',
          { name: 'type', values: ['类型A', '类型B', '类型C'] },
          'remark'
        ],
        count: 10,
        total: 50
      }
    };
    
    generateModuleMockFiles(module, feature, config);
  } else if (actionOrFlag) {
    // 生成单个文件
    generateMockFile(module, feature, actionOrFlag);
  } else {
    console.error('❌ 请提供操作名或使用 --all 标志');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cli();
}

module.exports = {
  generateMockFile,
  generateModuleMockFiles,
  generateListData,
  templates,
  fieldTypes
};
