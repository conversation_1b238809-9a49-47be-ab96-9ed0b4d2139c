/**
 * Mock系统测试脚本
 * 
 * 用于验证Mock系统的配置和数据文件是否正确
 */

const fs = require('fs');
const path = require('path');

// 导入配置文件
const mockConfigPath = path.join(__dirname, '../mock.config.js');
let MOCK_CONFIG;

try {
  // 由于配置文件使用ES6模块，我们需要手动解析
  const configContent = fs.readFileSync(mockConfigPath, 'utf8');
  // 简单的配置提取（生产环境应该使用更robust的方法）
  eval(configContent.replace('export { MOCK_CONFIG, MockUtils };', '').replace('export default MOCK_CONFIG;', ''));
} catch (error) {
  console.error('❌ 无法加载Mock配置文件:', error.message);
  process.exit(1);
}

console.log('🚀 开始测试Mock系统配置...\n');

// 测试1: 检查环境变量
console.log('📋 1. 检查环境变量配置');
const envPath = path.join(__dirname, '../.env.development');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  if (envContent.includes('VUE_APP_ENABLE_MOCK = true')) {
    console.log('✅ 环境变量配置正确: VUE_APP_ENABLE_MOCK = true');
  } else {
    console.log('❌ 环境变量配置错误: 未找到 VUE_APP_ENABLE_MOCK = true');
  }
} else {
  console.log('❌ 未找到 .env.development 文件');
}

// 测试2: 检查Mock配置
console.log('\n📋 2. 检查Mock系统配置');
console.log(`✅ Mock系统启用状态: ${MOCK_CONFIG.enabled}`);
console.log(`✅ ServiceWorker脚本路径: ${MOCK_CONFIG.serviceWorker.scriptPath}`);
console.log(`✅ Mock数据目录: ${MOCK_CONFIG.mockData.basePath}`);

// 测试3: 检查服务商信息接口配置
console.log('\n📋 3. 检查服务商信息接口配置');
const providerApis = [
  '/st/lifePay/providerInfo/queryPage',
  '/st/lifePay/providerInfo/getDropLists',
  '/st/lifePay/providerInfo/add',
  '/st/lifePay/providerInfo/edit',
  '/st/lifePay/providerInfo/delete',
  '/st/lifePay/providerInfo/exportExcel',
  '/st/lifePay/providerInfo/importExcel'
];

providerApis.forEach(api => {
  const isEnabled = MOCK_CONFIG.apis[api] || MOCK_CONFIG.apis['/st/lifePay/providerInfo/*'];
  console.log(`${isEnabled ? '✅' : '❌'} ${api}: ${isEnabled ? '已启用' : '未启用'}`);
});

// 测试4: 检查Mock数据文件
console.log('\n📋 4. 检查Mock数据文件');
const mockDataDir = path.join(__dirname, '../public/mock');
const expectedFiles = [
  'st-lifePay-providerInfo-queryPage.json',
  'st-lifePay-providerInfo-getDropLists.json',
  'st-lifePay-providerInfo-add.json',
  'st-lifePay-providerInfo-edit.json',
  'st-lifePay-providerInfo-delete.json',
  'st-lifePay-providerInfo-exportExcel.json',
  'st-lifePay-providerInfo-importExcel.json'
];

expectedFiles.forEach(fileName => {
  const filePath = path.join(mockDataDir, fileName);
  if (fs.existsSync(filePath)) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(content);
      
      // 检查响应格式
      if (data.success !== undefined && data.code !== undefined) {
        console.log(`✅ ${fileName}: 文件存在，格式正确`);
      } else {
        console.log(`⚠️  ${fileName}: 文件存在，但格式可能不正确`);
      }
    } catch (error) {
      console.log(`❌ ${fileName}: JSON格式错误 - ${error.message}`);
    }
  } else {
    console.log(`❌ ${fileName}: 文件不存在`);
  }
});

// 测试5: 检查ServiceWorker文件
console.log('\n📋 5. 检查ServiceWorker文件');
const swPath = path.join(__dirname, '../public/sw-mock-manager.js');
if (fs.existsSync(swPath)) {
  console.log('✅ ServiceWorker文件存在: sw-mock-manager.js');
} else {
  console.log('❌ ServiceWorker文件不存在: sw-mock-manager.js');
}

// 测试6: 检查Mock系统初始化文件
console.log('\n📋 6. 检查Mock系统初始化文件');
const mockSystemPath = path.join(__dirname, '../src/utils/mock-system.js');
if (fs.existsSync(mockSystemPath)) {
  console.log('✅ Mock系统初始化文件存在: src/utils/mock-system.js');
} else {
  console.log('❌ Mock系统初始化文件不存在: src/utils/mock-system.js');
}

// 测试7: 检查main.js集成
console.log('\n📋 7. 检查main.js集成');
const mainJsPath = path.join(__dirname, '../src/main.js');
if (fs.existsSync(mainJsPath)) {
  const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
  if (mainJsContent.includes('mock-system')) {
    console.log('✅ main.js已集成Mock系统');
  } else {
    console.log('❌ main.js未集成Mock系统');
  }
} else {
  console.log('❌ main.js文件不存在');
}

// 测试8: 验证分页数据格式
console.log('\n📋 8. 验证分页数据格式');
const queryPageFile = path.join(mockDataDir, 'st-lifePay-providerInfo-queryPage.json');
if (fs.existsSync(queryPageFile)) {
  try {
    const data = JSON.parse(fs.readFileSync(queryPageFile, 'utf8'));
    const requiredFields = ['success', 'code', 'message', 'data', 'total', 'pageNum', 'pageSize'];
    const missingFields = requiredFields.filter(field => !(field in data));
    
    if (missingFields.length === 0) {
      console.log('✅ 分页数据格式完整');
      console.log(`   - 数据条数: ${data.data ? data.data.length : 0}`);
      console.log(`   - 总数: ${data.total}`);
      console.log(`   - 页码: ${data.pageNum}`);
      console.log(`   - 页大小: ${data.pageSize}`);
    } else {
      console.log(`❌ 分页数据格式不完整，缺少字段: ${missingFields.join(', ')}`);
    }
  } catch (error) {
    console.log(`❌ 分页数据文件解析错误: ${error.message}`);
  }
}

// 测试9: 验证下拉列表数据格式
console.log('\n📋 9. 验证下拉列表数据格式');
const dropListFile = path.join(mockDataDir, 'st-lifePay-providerInfo-getDropLists.json');
if (fs.existsSync(dropListFile)) {
  try {
    const data = JSON.parse(fs.readFileSync(dropListFile, 'utf8'));
    if (data.success && data.code && data.data) {
      const dropListKeys = Object.keys(data.data);
      console.log('✅ 下拉列表数据格式正确');
      console.log(`   - 包含下拉列表: ${dropListKeys.join(', ')}`);
    } else {
      console.log('❌ 下拉列表数据格式不正确');
    }
  } catch (error) {
    console.log(`❌ 下拉列表数据文件解析错误: ${error.message}`);
  }
}

console.log('\n🎉 Mock系统配置检查完成！');
console.log('\n📝 下一步操作:');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 访问测试页面: http://localhost:2266/mock-test.html');
console.log('3. 访问服务商信息页面测试功能');
console.log('4. 在浏览器控制台使用 MockSystem.test() 命令测试');

// 生成测试命令
console.log('\n🔧 测试命令:');
console.log('// 在浏览器控制台中执行以下命令:');
console.log('MockSystem.status()  // 查看系统状态');
console.log('MockSystem.test("/st/lifePay/providerInfo/queryPage")  // 测试分页接口');
console.log('MockSystem.test("/st/lifePay/providerInfo/getDropLists")  // 测试下拉列表接口');
