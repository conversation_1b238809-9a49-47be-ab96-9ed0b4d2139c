import store from './store'

function scanCode() {
    let str = ''
    document.onkeydown = function() {
        debugger;
        if (event.target.tagName !== 'BODY') {
            str = ''
        } else {
            if (event.keyCode !== 13) {
                if (event.keyCode >= 32 && event.keyCode <= 126) {
                    let k = event.key
                    str += k

                    // alert("7777===="+ str);
                    store.dispatch('scanCode/setScan', str)
                }

            } else {
                if (str) {
                    // store.commit('SET_SCANSTRING', str)
                    store.dispatch('scanCode/setScan',str)
                    alert("获得缓存"+ store.getters.scanString )
                }
                str = ''
            }


        }
    }
}

export default scanCode()