import { getToken } from "@/utils/auth";
import axios from "axios";
import { value } from "lodash-es";
export default {
  methods: {
    handlePreviewFormRule(item) {
      let that = this;
      // console.log("item", item);
      if (item.type == "upload") {
        // console.log("item", item);
        //添加请求头
        item.props.headers = {
          Authorization: "Bearer " + this.$store.state.user.token,
        };
        if (item.props?.uploadType == "image") {
          item.props.listType = "image";
        } else if (item.props?.uploadType == "file") {
          item.props.listType = "text";
          item.showfileName = true;
        } else {
          item.props.uploadType = "image";
          item.props.listType = "image";
        }
        this.$set(item.props, "onSuccess", function(res, file, fileList) {
          file.url = res.data;
          // item.fileList = fileList;
          that.$set(item, "fileList", fileList);
          // console.log(this);
          this.$set(item, "previewSrcList", fileList);
        });
        this.$set(item.props, "onRemove", function(file, fileList) {
          that.$set(item, "fileList", fileList);
        });
      }
      //select接口请求添加token
      if (item.type == "select" && item?.effect?.fetch) {
        const { action, method, data, headers } = item?.effect?.fetch;
        axios({
          method: method,
          url: action,
          data: data,
          headers: headers,
        }).then((res) => {
          item.options = res.data?.data?.map((x) => {
            return { value: x.dictValue, label: x.dictLabel };
          });
        });
        this.$set(
          item.effect.fetch.headers,
          "Authorization",
          "Bearer " + getToken()
        );

        //过滤桩信息列表接口，修改入参
        let stationListApi = item?.effect?.fetch?.action;
        // if (stationListApi && stationListApi.includes("business/pileList")) {
        //   //如果两个节点配置了同一个表单则会在taskForm接口中携带stationId, 为了防止重复拼接stationId，每次需做下截取
        //   stationListApi = stationListApi.slice(
        //     0,
        //     stationListApi.indexOf("business/pileList") + 17
        //   );
        //   stationListApi += `?stationId=${this.projectForm.stationId}`;
        // }
        item.effect.fetch.action = stationListApi;
        item.effect.fetch.parse = (res) => {
          if (res?.data?.length > 0) {
            return res.data.map((item) => {
              return {
                label: item.dictLabel,
                value: item.dictValue,
              };
            });
          } else {
            return [];
          }
        };
      }
      // 递归设置表单规则
      if (
        (item.type == "FcRow" || item.type == "col") &&
        item.children &&
        item.children.length > 0
      ) {
        item.children.map((object) => this.handleFormRule(object));
      }

      //栅格布局:设置子组件的disabled属性
      if (item.type == "FcRow") {
        if (item?.children?.length > 0) {
          item.children.map((item) => {
            if (item?.children?.length > 0) {
              item.children.map((child) => {
                // //未开始|已完成 不可操作,详情 不可操作
                // let nodeDisabled = !!(
                //   !this.currentNode.taskId || this.currentNode.endTime
                // );
                if (child.props) {
                  child.props.disabled = true;
                } else {
                  child.props = {
                    disabled: true,
                  };
                }

                return child;
              });
            }

            return item;
          });
        }
      }
      // console.log(item, "====item");
      //非处理情况下表单禁用
      // let nodeDisabled = !!(
      //   !this.currentNode.taskId || this.currentNode.endTime
      // ); //未开始|已完成 不可操作,详情 不可操作
      if (item.props) {
        item.props.disabled = true;
      } else {
        item.props = { disabled: true };
      }
    },
    handleFormRule(item) {
      let that = this;
      // console.log("item", item);
      if (item.type == "upload") {
        // console.log("item", item);
        //添加请求头
        item.props.headers = {
          Authorization: "Bearer " + this.$store.state.user.token,
        };
        if (item.props?.uploadType == "image") {
          item.props.listType = "image";
        } else if (item.props?.uploadType == "file") {
          item.props.listType = "text";
          that.showfileName = true;
        } else {
          item.props.uploadType = "image";
          item.props.listType = "image";
        }
        this.$set(item.props, "onSuccess", function(res, file, fileList) {
          file.url = res.data;
          // item.fileList = fileList;
          that.$set(item, "fileList", fileList);
          // console.log(this);
          this.$set(item, "previewSrcList", fileList);
        });
        this.$set(item.props, "onRemove", function(file, fileList) {
          that.$set(item, "fileList", fileList);
        });
      }
      //select接口请求添加token
      if (item.type == "select" && item?.effect?.fetch) {
        this.$set(
          item.effect.fetch.headers,
          "Authorization",
          "Bearer " + getToken()
        );

        //过滤桩信息列表接口，修改入参
        let stationListApi = item?.effect?.fetch?.action;
        // if (stationListApi && stationListApi.includes("business/pileList")) {
        //   //如果两个节点配置了同一个表单则会在taskForm接口中携带stationId, 为了防止重复拼接stationId，每次需做下截取
        //   stationListApi = stationListApi.slice(
        //     0,
        //     stationListApi.indexOf("business/pileList") + 17
        //   );
        //   stationListApi += `?stationId=${this.projectForm.stationId}`;
        // }
        item.effect.fetch.action = stationListApi;
        item.effect.fetch.parse = (res) => {
          if (res?.data?.length > 0) {
            return res.data.map((item) => {
              return {
                label: item.dictLabel,
                value: item.dictValue,
              };
            });
          } else {
            return [];
          }
        };
      }
      // 递归设置表单规则
      if (
        (item.type == "FcRow" || item.type == "col") &&
        item.children &&
        item.children.length > 0
      ) {
        item.children.map((object) => this.handleFormRule(object));
      }

      //栅格布局:设置子组件的disabled属性
      if (item.type == "FcRow") {
        if (item?.children?.length > 0) {
          item.children.map((item) => {
            if (item?.children?.length > 0) {
              item.children.map((child) => {
                // //未开始|已完成 不可操作,详情 不可操作
                // let nodeDisabled = !!(
                //   !this.currentNode.taskId || this.currentNode.endTime
                // );
                // if (child.props) {
                //   child.props.disabled = nodeDisabled;
                // } else {
                //   child.props = {
                //     disabled: nodeDisabled,
                //   };
                // }

                return child;
              });
            }

            return item;
          });
        }
      }
      if (item.type === "ProcessTableDesigner") {
        item.props = item.props || {};
        item.props.isDesignMode = false;
      }
      // console.log(item, "====item");
      //非处理情况下表单禁用
      // let nodeDisabled = !!(
      //   !this.currentNode.taskId || this.currentNode.endTime
      // ); //未开始|已完成 不可操作,详情 不可操作
      // if (item.props) {
      //   item.props.disabled = nodeDisabled;
      // } else {
      //   item.props = { disabled: nodeDisabled };
      // }
    },
    handleDownload(f) {
      const a = document.createElement("a"); // 创建一个HTML 元素
      a.setAttribute("download", f.name); //download属性
      a.setAttribute("href", f.url); // href链接
      a.click(); // 自执行点击事件
    },
  },
};
