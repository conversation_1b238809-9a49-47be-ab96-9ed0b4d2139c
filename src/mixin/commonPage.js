import request from "@/utils/request";
const mixin = {
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      pageTotal: 0,
      tableData: [],
      form: {},
      single: true,
      pageInitQuery: true,
      loading: false,
      multiple: true,
      dialogVisible: false,
      dialogTitle: "",
      multipleSelection: [],
      rules: {
        orgNo: [{ required: true, message: "请选择公司", trigger: "change" }],
        orgNoList: [
          { required: true, message: "请选择公司", trigger: "change" }
        ],
        releaseDates: [
          { required: true, message: "请出账日期", trigger: "change" }
        ]
      }
    };
  },
  created: function() {},
  mounted() {
    this.queryParamsClone = this._.cloneDeep(this.queryParams);
    this.pageInitQuery && this.getList();
  },
  methods: {
    handleAdd() {
      this.dialogTitle = this.dialogName + "新增";
      this.pageState = "add";
      this.form = {};
      this.$refs.form && this.$refs.form.resetFields();
      this.dialogVisible = true;
      setTimeout(()=>{
        if (this.$refs["formTreeSelect"]) {
          this.$refs["formTreeSelect"].clear();
        }
      });
    },
    handleUpdate() {
      if (this.multipleSelection.length == 0) {
        this.$message.error("请选择编辑项");
        return false;
      } else if (this.multipleSelection.length > 1) {
        this.$message.error("只能选择一条数据");
        return false;
      } else {
        this.form = {};
        this.$refs.form && this.$refs.form.resetFields();
        this.dialogVisible = true;
        this.dialogTitle = this.dialogName + "编辑";
        this.pageState = "update";
        this.form = this._.cloneDeep(this.multipleSelection[0]);
      }
    },
    handleDelete() {
      let _this = this;
      if (this.multipleSelection.length == 0) {
        this.$message.error("请选择编辑项");
        return false;
      } else {
        this.$confirm("确定删除选择数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          _this
            .deleteApi(
              this.multipleSelection
                .map(item => item[this.deleteByKey])
                .join(",")
            )
            .then(err => {
              _this.$message.success("删除成功");
              _this.getList();
            });
        });
      }
    },
    cancelDialog() {
      this.dialogVisible = false;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.single = this.multipleSelection.length != 1;
      this.multiple = !this.multipleSelection.length;
      this.multipleSelectioned = this.multipleSelectioned.concat(val);
    },
    resetQuery() {
      this.queryParams = this._.cloneDeep(this.queryParamsClone);
      this.pageNum = 1;
      if (this.$refs["treeSelect"]) {
        this.$refs["treeSelect"].reset();
      }
      setTimeout(() => {
        this.getList();
      });
    },
    handleQuery(param) {
      this.pageNum = 1;
      this.getList(param);
    },
    getList(param) {
      this.loading = true;
      let queryParams = this._.cloneDeep(this.queryParams);
      if (param) {
        queryParams = Object.assign({}, param, queryParams);
      }

      if (this.queryParamsFormat) {
        queryParams = this.queryParamsFormat(queryParams);
      }

      let searchParams = {
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        ...queryParams
      };
      console.log(this.pageNum)
      this.queryApi(searchParams)
        .then(response => {
          this.tableData = response.data;
          this.pageTotal = response.total;
          this.loading = false;
          this.queryCallback && this.queryCallback();
        })
        .catch(err => {
          console.log("调取查询接口错误" + err);
          this.loading = false;
        });
    },
    submitForm() {
      let requestData = this._.cloneDeep(this.form);

      if (this.pageState == "add") {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.addApi(requestData)
              .then(response => {
                this.$message.success("保存成功");
                this.dialogVisible = false;
                this.getList();
              })
              .catch(err => {
                // this.$message.error(err);
              });
          }
        });
      } else if (this.pageState == "update") {
        this.$refs["form"].validate(valid => {
          if (valid) {
            this.updateApi(requestData)
              .then(response => {
                this.$message.success("编辑成功");
                this.dialogVisible = false;
                this.getList();
              })
              .catch(err => {
                // this.$message.error(err);
              });
          }
        });
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      let _this = this;
      let queryParams = this._.cloneDeep(this.queryParams);
      this.$confirm("是否确认导出所有数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        queryParams = _this.queryParamsFormat
          ? _this.queryParamsFormat(queryParams)
          : queryParams;
        console.log(queryParams);
        _this.exportData(queryParams).then(res => {
          console.log(res);
          _this.download(res.msg);
        });
      });
    }
  }
};

export default mixin;
