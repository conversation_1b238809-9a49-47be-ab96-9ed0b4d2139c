import request from "@/utils/request";

// 新增
export function pileGunList(data) {
  return request({
    url: "/platform/pile/gun",
    method: "post",
    data: data,
  });
}

// 列表查询
export function queryList(data) {
  return request({
    url: "/maintenance/queryList",
    method: "post",
    data: data,
  });
}

export function saveOrder(data) {
  return request({
    url: "/maintenance/saveOrder",
    method: "post",
    data: data,
  });
}

export function detail(data) {
  return request({
    url: "/maintenance/detail",
    method: "post",
    data: data,
  });
}
export function exportExcel(data) {
  return request({
    url: "/maintenance/export",
    method: "post",
    data: data,
  });
}
export function check(data) {
  return request({
    url: "/maintenance/auditOrder",
    method: "post",
    data: data,
  });
}

export function transferOrder(data) {
  return request({
    url: "/maintenance/transferOrder",
    method: "post",
    data: data,
  });
}

export function pushMsg(data) {
  return request({
    url: "/maintenance/pushMsg",
    method: "post",
    data: data,
  });
}

export function stopOrder(data) {
  return request({
    url: "/maintenance/stopOrder",
    method: "post",
    data: data,
  });
}

export function updateOrderHandleTag(data) {
  return request({
    url: "/maintenance/updateOrderHandleTag",
    method: "post",
    data: data,
  });
}

export function orderRecord(data) {
  return request({
    url: "/maintenance/orderRecord",
    method: "post",
    data: data,
  });
}

export function handleOrder(data) {
  return request({
    url: "/maintenance/handleOrder",
    method: "post",
    data: data,
  });
}
export function handleDetail(data) {
  return request({
    url: "/maintenance/handleDetail",
    method: "post",
    data: data,
  });
}

export function createErrorOrder(data) {
  return request({
    url: "/maintenance/createErrorOrder",
    method: "post",
    data: data,
  });
}
export function fistLevelList(data) {
  return request({
    url: "/orderType/fistLevelList",
    method: "get",
    params: data,
  });
}
export function childrenList(data) {
  return request({
    url: "/orderType/childrenList",
    method: "get",
    params: data,
  });
}
//修改备注
export function submitRemark(data) {
  return request({
    url: "/maintenance/remarkOrder",
    method: "post",
    data: data,
  });
}

export function getDeptList(data) {
  return request({
    url: "/dept/list",
    method: "get",
    params: data,
  });
}
export function getAllDeptList(data) {
  return request({
    url: "/dept/all/list",
    method: "get",
    params: data,
  });
}
//故障详情接口
export function faultDetail(data) {
  return request({
    url: "/maintenance/getDeviceAlarmDetail",
    method: "get",
    params: data,
  });
}
//提交半径
export function submitRadius(data) {
  return request({
    url: "/maintenance/getDeviceAlarmDetail",
    method: "get",
    params: data,
  });
}
//接单
export function receiveOrder(data) {
  return request({
    url: "/maintenance/getDeviceAlarmDetail",
    method: "get",
    params: data,
  });
}
//取消
export function submitCancel(data) {
  return request({
    url: "/maintenance/getDeviceAlarmDetail",
    method: "get",
    params: data,
  });
}
//获取权限范围的一级工单类型
export function queryBindOrderTypeList(data) {
  return request({
    url: "/user/permission/order/getBindOrderTypeOneList",
    method: "get",
    params: data,
  });
}
//获取工单编号
export function getNewOrgNo(data) {
  return request({
    url: "/maintenance/orderNo",
    method: "get",
    params: data,
  });
}

// 订单成功率
export function getOrderSuccessRate(data) {
  return request({
    url: "/statistics/platform/orderSuccessRate",
    method: "post",
    data: data,
  });
}
// 大区、资产单位、运营单位列表
export function getOperatorList(data) {
  return request({
    url: "/platform/operator/list",
    method: "post",
    data,
  });
}
// 订单成功率排名
export function getOrderSuccessRank(data) {
  return request({
    url: "/statistics/platform/orderSuccessTop",
    method: "post",
    data: data,
  });
}

// 订单成功率报表
export function getOrderSuccessRateList(data) {
  return request({
    url: "/statistics/platform/successRate/list",
    method: "post",
    data: data,
  });
}
// 导出成功率报表
export function exportReport(data) {
  return request({
    url: "/statistics/platform/export",
    method: "post",
    data: data,
  });
}
