import request from "@/utils/request";

// 查询列表
export function list(data) {
  return request({
    url: "/acceptanceTemplate/getList",
    method: "post",
    data: data
  });
}

// 新增
export function add(data) {
  return request({
    url: "/acceptanceTemplate/add",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/acceptanceTemplate/update",
    method: "post",
    data: data
  });
}

// 启用/禁用
export function changeStatus(data) {
  return request({
    url: "/acceptanceTemplate/changeStatus",
    method: "post",
    data: data
  });
}

// 查看详情
export function detail(data) {
  return request({
    url: "/acceptanceTemplate/detail",
    method: "post",
    data: data
  });
}
