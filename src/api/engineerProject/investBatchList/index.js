import request from "@/utils/request";
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/project/batch/list",
      method: "post",
      data: data,
    });
  },
  //备注
  remark(data) {
    return request({
      url: "/project/batch/remark/save",
      method: "post",
      data: data,
    });
  },
  materialList(data) {
    return request({
      url: "/project/material/list",
      method: "get",
      params: data,
    });
  },
  //指定施工队
  setTeam(data) {
    return request({
      url: "/project/batch/designateConsTeam",
      method: "post",
      data: data,
    });
  },
  //投建协议已盖章
  stamp(data) {
    return request({
      url: "/project/batch/consAgreementStamp",
      method: "get",
      params: data,
    });
  },
  //进度查询
  record(data) {
    return request({
      url: "/demand/poll/getApproveRecord",
      method: "get",
      params: data,
    });
  },
  //查询运管申请单号
  omApplyNo(data) {
    return request({
      url: "/project/batch/omApplyNo",
      method: "get",
      params: data,
    });
  },
  //审核
  check(data) {
    return request({
      url: "/project/audit",
      method: "post",
      data: data,
    });
  },
  //根据工序项类型查询工序模版名称
  workType(data) {
    return request({
      url: "/porject/work/queryWorkByType",
      method: "get",
      params: data,
    });
  },
  workTime(data) {
    return request({
      url: "/porject/work/queryWorkTimeByBatchId",
      method: "get",
      params: data,
    });
  },
  //转派
  transfer(data) {
    return request({
      url: "/project/batch/transferManager",
      method: "post",
      data: data,
    });
  },
  //   采购确认结果
  confirm(data) {
    return request({
      url: "/project/confirm",
      method: "post",
      data: data,
    });
  },
  //采购确认设备总数获取
  pileCount(data) {
    return request({
      url: "/project/query/pileCount",
      method: "get",
      params: data,
    });
  },
  //   施工供应商下单通知
  orderNotification(data) {
    return request({
      url: "/project/batch/supplierNotify",
      method: "post",
      data: data,
    });
  },
  //上传报告
  upload(data) {
    return request({
      url: "/doc/saveCmDoc",
      method: "post",
      data: data,
    });
  },
  //处理半径
  radius(data) {
    return request({
      url: "/project/batch/handleRadius",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/project/batch/export",
      method: "post",
      data: query,
    });
  },
  //竣工报告-获取初始电子版报告-首页
  exportHomePage(data) {
    return request({
      url: "/export/report/homePage",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-目录
  exportCatalogue(data) {
    return request({
      url: "/export/report/catalogue",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-动态工序项
  exportItem(data) {
    return request({
      url: "/export/report/workingProcedure",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-接地电阻测试报表导出\电气接地电阻测试记录
  exportWorkResistance(data) {
    return request({
      url: "/export/report/workResistance",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-电缆绝缘测试记录报表导出\电气绝缘电阻测试记录
  exportWorkCable(data) {
    return request({
      url: "/export/report/workCable",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-安全交底
  exportSafety(data) {
    return request({
      url: "/export/report/safetyReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-竣工图
  exportProjectComplete(data) {
    return request({
      url: "/export/report/projectComplete",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-资产验收单
  exportFixedAssets(data) {
    return request({
      url: "/export/report/fixedAssets",
      method: "get",
      params: data,
      // responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-决算单
  exportBalanceReport(data) {
    return request({
      url: "/export/report/balanceReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-变更项
  exportChangeReport(data) {
    return request({
      url: "/export/report/changeReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //竣工报告-获取初始电子版报告-进场验收单
  exportAcceptanceReport(data) {
    return request({
      url: "/export/report/projectAcceptanceReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },

  //竣工报告-下载初始版压缩包
  exportAllOriginal(data) {
    return request({
      url: "/export/report/exportAllReport",
      method: "get",
      params: data,
      // responseType: "blob",
      // needFileName: true,
    });
  },
  //竣工报告-下载用户上传版压缩包
  exportAllUploaded(data) {
    return request({
      url: "/export/report/exportAllUploadedReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },

  //竣工报告-获取已盖章报告url
  uploadReport(data) {
    return request({
      url: "/doc/listProjectFile",
      method: "get",
      params: data,
    });
  },
  //记录下载了竣工报告 用于操作记录
  recordExport(data) {
    return request({
      url: "/project/record/saveRecord",
      method: "post",
      data: data,
    });
  },
  //竣工验收-提交
  completeAcceptance(data) {
    return request({
      url: "/project/batch/completionAcceptance",
      method: "post",
      data: data,
    });
  },
  //开工申请
  //开工申请-详情
  applyDetail(data) {
    return request({
      url: "/project/query/applyInfo",
      method: "get",
      params: data,
    });
  },
  //开工申请-保存
  saveApply(data) {
    return request({
      url: "/project/applyOperation",
      method: "post",
      data: data,
    });
  },
  //项目详情
  //详情-基础信息tab+工程信息tab+设备信息tab
  baseInfoDetail(data) {
    return request({
      url: "/project/batch/basicDetail",
      method: "get",
      params: data,
    });
  },
  //详情-付款信息tab 付款详情
  getPayDescription(data) {
    return request({
      url: "/jdy/getPayment",
      method: "get",
      params: data,
    });
  },
  //踏勘信息抽屉
  getExploreDetail(data) {
    return request({
      url: "/construct/manager/businessDetails",
      method: "get",
      params: data,
    });
  },
  //踏勘信息抽屉-储能
  getEnergyStorageDetail(data) {
    return request({
      url: "/project/batch/energySurveyData",
      method: "get",
      params: data,
    });
  },
  //投建协议信息抽屉
  getAgreementDetail(data) {
    return request({
      url: "/construct/manager/constAgreeDtl",
      method: "get",
      params: data,
    });
  },

  //投建协议信息列表抽屉
  getAgreementDetailList(data) {
    return request({
      url: "/construct/manager/constAgreeDetailListDtl",
      method: "get",
      params: data,
    });
  },

  //投建协议基本信息抽屉-储能
  getEnergyAgreementList(data) {
    return request({
      url: "/project/batch/storageConstructProtocolData",
      method: "get",
      params: data,
    });
  },

  //采购单详情抽屉
  getPurchaseOrderDetail(data) {
    return request({
      url: "/construct/manager/purOrderDtl",
      method: "get",
      params: data,
    });
  },
  //采购单详情抽屉-固定资产明细
  getPurchaseAsset(data) {
    return request({
      url: "/gudingzichan/list",
      method: "get",
      params: data,
    });
  },
  //采购单框架协议详情抽屉
  getPurchaseAgreementDetail(data) {
    return request({
      url: "/construct/manager/purFrameDtl",
      method: "get",
      params: data,
    });
  },

  //付款单详情抽屉
  getPayDetail(data) {
    return request({
      url: "/construct/manager/paymentOrderDtl",
      method: "get",
      params: data,
    });
  },

  //付款单详情抽屉中的 采购明细List
  getPayDetailList(data) {
    return request({
      url: "/construct/manager/paymentOrderDtlList",
      method: "get",
      params: data,
    });
  },

  // //详情-工程信息tab
  // engineerInfoDetail(data) {
  //   return request({
  //     url: "/aging/config/page",
  //     method: "post",
  //     data: data,
  //   });
  // },
  // //详情-设备信息tab
  // deviceInfoDetail(data) {
  //   return request({
  //     url: "/aging/config/page",
  //     method: "post",
  //     data: data,
  //   });
  // },
  // 详情-设备信息tab-设备明细列表
  deviceList(data) {
    return request({
      url: "/project/batch/deviceDetail",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-基本信息
  applyInfoDetail(data) {
    return request({
      url: "/project/query/basicInfo",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-施工队信息和进度计划
  applyInfoTeam(data) {
    return request({
      url: "/project/query/applyInfo",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-审核信息
  applyInfoCheck(data) {
    return request({
      url: "/project/query/auditInfo",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-确认信息
  applyInfoConfirm(data) {
    return request({
      url: "/project/query/confirmInfo",
      method: "get",
      params: data,
    });
  },
  //详情-施工信息tab
  constructionInfoDetail(data) {
    return request({
      url: "/porject/work/queryConstructionBasicInfo",
      method: "get",
      params: data,
    });
  },
  //详情-施工信息tab-施工工序完成情况
  constructionWorkDetail(data) {
    return request({
      url: "/porject/work/queryWorkByProject",
      method: "get",
      params: data,
    });
  },
  commonWorkDetail(data) {
    return request({
      url: "/porject/work/queryFixFormByProject",
      method: "get",
      params: data,
    });
  },
  //详情-操作记录
  recordInfo(data) {
    return request({
      url: "/project/record/queryRecord",
      method: "get",
      params: data,
    });
  },

  //开工申请-工程经理下拉选项
  permissionUserList(data) {
    return request({
      url: "/construct/manager/getPermissUserList",
      method: "get",
      params: data,
    });
  },
  // 查询角色账号列表
  listRoleAccount(query) {
    return request({
      url: "/construct/manager/listProjectManager",
      method: "get",
      params: query,
    });
  },

  // 场站项目列表
  queryStationProject(data) {
    return request({
      url: "/project/station/list",
      method: "post",
      data,
    });
  },
  // 场站项目列表-导出
  exportStationProject(data) {
    return request({
      url: "/project/station/export",
      method: "post",
      data,
    });
  },
  // 待办左侧列表
  queryTodoStatistics(data) {
    return request({
      url: "/project/batch/todoStatistics",
      method: "get",
      params: data,
    });
  },

  // 查询场站项目详情
  queryStationProjectDetail(data) {
    return request({
      url: "/project/station/detail",
      method: "get",
      params: data,
    });
  },

  // 查询数据看板
  queryStatisticsData(data) {
    return request({
      url: "/project/batch/statistics",
      method: "post",
      data,
    });
  },

  // 以下是储能新增接口
  // 整改通知单
  // 查询 最新的 工程整改通知
  getNoticeInfo(data) {
    return request({
      url: "/project/queryLastRectification",
      method: "get",
      params: data,
    });
  },
  // 工程整改通知
  saveRectification(data) {
    return request({
      url: "/project/saveRectification",
      method: "post",
      data: data,
    });
  },
  //工程整改记录
  queryRectificationRecord(data) {
    return request({
      url: "/project/queryAllRectification",
      method: "get",
      params: data,
    });
  },
  //下载整改通知单
  exportRectification(data) {
    return request({
      url: "/export/report/rectificationReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  // 项目投运确认单
  // 查询 最新的 项目投运确认单
  getOperateConfirmInfo(data) {
    return request({
      url: "/confirm/operation/detail",
      method: "get",
      params: data,
    });
  },
  //提交投运确认单
  saveOperateConfirm(data) {
    return request({
      url: "/confirm/operation/saveOrUpdate",
      method: "post",
      data: data,
    });
  },
  //下载项目投运确认单
  exportOperateConfirm(data) {
    return request({
      url: "/export/report/confirmOperationReport",
      method: "get",
      params: data,
      responseType: "blob",
      needFileName: true,
    });
  },
  //储能 - 详情-设备信息tab-已/未安装设备
  energyDeviceList(data) {
    return request({
      url: "/project/batch/deviceDetail",
      method: "get",
      params: data,
    });
  },
  //储能 - 详情-设备信息tab-到货签收信息
  signDeviceList(data) {
    return request({
      url: "/porject/work/queryAcceptanceDetail",
      method: "get",
      params: data,
    });
  },
};
