import request from "@/utils/request";

// 查询工序列表
export const queryWorkCheckList = (data) => {
  return request({
    url: "/porject/work/queryWorkByProject",
    method: "get",
    params: data,
  });
};
// 查询固定项信息列表
export const queryFixedItems = (data) => {
  return request({
    url: "/porject/work/queryFixFormByProject",
    method: "get",
    params: data,
  });
};
// 查询施工验收信息
export const queryWorkCheck = (data) => {
  return request({
    url: "/porject/work/queryPcWorkCheckById",
    method: "get",
    params: data,
  });
};
//  查询进场验收单
export const queryAcceptanceDetail = (data) => {
  return request({
    url: "/porject/work/queryAcceptanceDetail",
    method: "get",
    params: data,
  });
};
// 进场验收单-删除设备
export const deleteAcceptanceItem = (data) => {
  return request({
    url: "/project/acceptance/deleteAcceptance",
    method: "get",
    params: data,
  });
};
// 进场验收单-全部清空设备
export const deleteAllAcceptance = (data) => {
  return request({
    url: "/project/acceptance/deleteAllAcceptance",
    method: "get",
    params: data,
  });
};
// 进场验收单-编辑表格
export const updateAcceptance = (data) => {
  return request({
    url: "/project/acceptance/updateAcceptance",
    method: "post",
    data: data,
  });
};

//  查询接地电阻工序项
export const queryProjectWorkResistance = (data) => {
  return request({
    url: "/porject/work/queryProjectWorkResistance",
    method: "get",
    params: data,
  });
};

//  查询绝缘电阻工序项
export const queryProjectWorkCable = (data) => {
  return request({
    url: "/porject/work/queryProjectWorkCable",
    method: "get",
    params: data,
  });
};

// 保存施工验收信息
export const saveWorkCheck = (data) => {
  return request({
    url: "/porject/work/savePcProjectWork",
    method: "post",
    data,
  });
};

// 固定资产工序
export const queryAssetDetail = (data) => {
  return request({
    url: "/porject/work/queryAssetDetail",
    method: "get",
    params: data,
  });
};
// 资产验收单-设备分页列表
export const queryAssetDeviceList = (data) => {
  return request({
    url: "/project/fixedAssets/queryAssetList",
    method: "post",
    data: data,
  });
};
// 资产验收单-删除设备
export const deleteDeviceItem = (data) => {
  return request({
    url: "/project/fixedAssets/deleteAsset",
    method: "get",
    params: data,
  });
};
// 资产验收单-全部清空设备
export const deleteAllDevice = (data) => {
  return request({
    url: "/project/fixedAssets/deleteAssetsByProject",
    method: "get",
    params: data,
  });
};
// 资产验收单-导入进场验收单
export const importAcceptanceToAsset = (data) => {
  return request({
    url: "/porject/work/importAcceptanceToAsset",
    method: "get",
    params: data,
  });
};
// 资产验收单-编辑表格
export const updateDevice = (data) => {
  return request({
    url: "/project/fixedAssets/updateAsset",
    method: "post",
    data: data,
  });
};
// 查询项目变更申请详情
export const queryChangeDetail = (data) => {
  return request({
    url: "/porject/work/queryChangeDetail",
    method: "get",
    params: data,
  });
};

// 查询决算
export const queryBalanceDetail = (data) => {
  return request({
    url: "/porject/work/queryBalanceDetail",
    method: "get",
    params: data,
  });
};

// 竣工
export const queryCompleteDetail = (data) => {
  return request({
    url: "/porject/work/queryCompleteDetail",
    method: "get",
    params: data,
  });
};
// 路书
export const queryRoadDetail = (data) => {
  return request({
    url: "/porject/work/queryCmProjectRoadItem",
    method: "get",
    params: data,
  });
};
// 固定检查项保存
export const saveFixedFormAuditInfo = (data) => {
  return request({
    url: "/porject/work/saveFixedFormAuditInfo",
    method: "post",
    data: data,
  });
};

// 提交审核
export const submitPcWorkByProject = (data) => {
  return request({
    url: "/export/report/submitPcWorkByProject",
    method: "post",
    data: data,
  });
};

// 回显审核即过
export const queryPcWorkByProject = (data) => {
  return request({
    url: "/porject/work/querySubmitResultById",
    method: "get",
    params: data,
  });
};
// 根据项目id查询工序数量统计
export const queryItemCount = (data) => {
  return request({
    url: "/porject/work/queryProjectWorkByProjectId",
    method: "get",
    params: data,
  });
};
