import request from "@/utils/request";
//时效配置
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/construct/aging/page",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/construct/aging/delete",
      method: "post",
      data: query,
    });
  },
  //保存/新增
  update(query) {
    return request({
      url: "/construct/aging/save",
      method: "post",
      data: query,
    });
  },
};
