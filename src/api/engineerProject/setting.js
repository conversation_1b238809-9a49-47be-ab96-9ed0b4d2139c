import request from "@/utils/request";

// 查询设置的城市权限
export function loadCityList(data) {
  return request({
    url: "/charging/user/getUserCityList",
    method: "get",
    params: data,
  });
}
// 查询未设置的城市权限
export function loadUnbindCityList(data) {
  return request({
    url: "/charging/user/getUnBindUserCityList",
    method: "get",
    params: data,
  });
}
// 查询设置的组织权限
export function loadOrgList(data) {
  return request({
    url: "/charging/user/getUserOrgList",
    method: "get",
    params: data,
  });
}

// 查询业务类型权限
export function loadBusinessList(data) {
  return request({
    url: "/charging/user/getUserBusinessTypeList",
    method: "get",
    params: data,
  });
}

// 获取工程项目-账号权限
export function getUserList(data) {
  return request({
    url: "/charging/user/pageUserInfo",
    method: "post",
    data,
  });
}

// 保存城市权限
export function saveCityPerms(data) {
  return request({
    url: "/charging/user/saveUserCity",
    method: "post",
    data,
  });
}

// 保存组织权限
export function saveOrgPerms(data) {
  return request({
    url: "/charging/user/saveUserOrg",
    method: "post",
    data,
  });
}

// 保存业务类型权限
export function saveBusinessTypePerms(data) {
  return request({
    url: "/charging/user/saveUserBusinessType",
    method: "post",
    data,
  });
}

// 查询权限操作日志
export function queryLog(data) {
  return request({
    url: "/charging/user/record",
    method: "post",
    data,
  });
}

// 查询权限状态
export function queryPermsStatus(userIdList) {
  return request({
    url: `/charging/user/listUserStatus?userIdList=${userIdList}`,
    method: "get",
  });
}

// 修改状态
export function setPermsStatus(data) {
  return request({
    url: "/charging/user/enablePermiss",
    method: "post",
    data,
  });
}
