import request from "@/utils/request";

// 充电订单接口

//充电订单 列表查询
export function queryOrderList(data) {
  return request({
    url: "/charge/order/pageList",
    method: "get",
    params: data,
  });
}
//充电订单 导出
export function exportExcel(data) {
  return request({
    url: "/charge/order/pageList",
    method: "get",
    params: data,
  });
}
//充电订单详情-订单详情
export function queryOrderInfo(data) {
  return request({
    url: "/charge/order/detail",
    method: "get",
    params: data,
  });
}
//充电订单详情-异常记录
export function queryOrderRecord(data) {
  return request({
    url: "/charge/order/getOrderErrorList",
    method: "get",
    params: data,
  });
}
//充电订单详情-充电数据
export function queryChargingInfo(data) {
  return request({
    url: "/charge/order/getChargingData",
    method: "get",
    params: data,
  });
}
//充电订单详情-操作记录
export function queryHandleRecord(data) {
  return request({
    url: "/charge/order/getControlLog",
    method: "get",
    params: data,
  });
}
