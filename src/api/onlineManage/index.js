import request from "@/utils/request";

// 查询项目信息(上线管理)
export function queryOnlineProjectInfoByPage(data) {
  return request({
    url: "/online/queryOnlineProjectInfoByPage",
    method: "post",
    data: data
  });
}

// 发起上线
export function startOnline(data) {
  return request({
    url: "/online/startOnline",
    method: "post",
    data: data
  });
}

// 新增上线工单
export function addOnlineFlow(data) {
  return request({
    url: "/online/addOnlineFlow",
    method: "post",
    data: data
  });
}