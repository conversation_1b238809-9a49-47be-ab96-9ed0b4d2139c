import request from "@/utils/request";

// 查询站点列表
export function queryPileInfoByPage(data) {
  return request({
    url: "/chargingPile/queryPileInfoByPage",
    method: "post",
    data: data
  });
}

// 新增充电桩
export function addPile(data) {
  return request({
    url: "/chargingPile/addPile",
    method: "post",
    data: data
  });
}

// 修改充电桩
export function updatePile(data) {
  return request({
    url: "/chargingPile/updatePile",
    method: "post",
    data: data
  });
}

// 同步充电平台
export function queryPlatformPileInfo(data) {
  return request({
    url: "/chargingPile/queryPlatformPileInfo",
    method: "post",
    data: data
  });
}

//获取充电桩品牌
export function brandList(params) {
  return request({
    url: "/brand/list",
    method: "get",
    params: params
  });
}
//获取充电桩型号
export function modelList(params) {
  return request({
    url: "/model/list",
    method: "get",
    params: params
  });
}

