import request from "@/utils/request";

// 用户列表查询列表
export function premiseList(data) {
  return request({
    url: "/crm/premise/premiseList",
    method: "post",
    data: data
  });
}

/** 开户清单查询 */
export function consOpeningList(data) {
  return request({
    url: "/crm/premise/consOpeningList",
    method: "post",
    data: data
  });
}

// 用户列表查询列表导出
export function exportPremiseList(data) {
  return request({
      url: "/crm/premise/exportPremiseList",
      method: "post",
      data: data
  });
}

// 用户列表查询列表导出
export function exportConsOpeningList(data) {
  return request({
      url: "/crm/premise/exportConsOpeningList",
      method: "post",
      data: data
  });
}

//暂存用户列表查询
export function esAppList(data) {
  return request({
    url: "/crm/esApp/esAppList",
    method: "post",
    data: data
  });
}

/** 删除暂存用户 */
export function deleteEsApp(data) {
  return request({
    url: "/crm/esApp/deleteEsApp",
    method: "post",
    data: data
  });
}

//查询水价
export function prcList(data) {
  return request({
    url: "/billing/priceManage/queryEcPrcList",
    method: "post",
    data: data
  });
}

//用户暂存
export function addEsAppInfo(data) {
  return request({
    url: "/crm/esApp/addEsAppInfo",
    method: "post",
    data: data
  });
}

//用户建档
export function addPremise(data) {
  return request({
    url: "/crm/premise/addPremise",
    method: "post",
    data: data
  });
}

//用户建档---暂存
export function commitPremise(data) {
  return request({
    url: "/crm/esApp/commitPremise",
    method: "post",
    data: data
  });
}

//用户建档---暂存提交流程
export function commitAddPremiseFlow(data) {
  return request({
    url: "/crm/esApp/commitAddPremiseFlow",
    method: "post",
    data: data
  });
}

//水表型号列表选择
export function mtrTypeCdList(data) {
  return request({
    url: "/meter/model/list",
    method: "post",
    data: data
  });
}
//违约金列表
export function queryList(data) {
  return request({
    url: "/delay/payment/queryList",
    method: "post",
    data: data
  });
}

//用户档案详情
export function premiseDetail(data) {
  return request({
    url: "/crm/premise/premiseDetail",
    method: "get",
    params: data
  });
}

//暂存用户详情
export function esAppDetail(data) {
  return request({
    url: "/crm/esApp/esAppDetail",
    method: "get",
    params: data
  });
}

//暂存用户详情
export function esAppConsList(data) {
  return request({
    url: "/crm/esApp/esAppConsList",
    method: "post",
    data: data
  });
}

//暂存用户修改
export function updateEsAppInfo(data) {
  return request({
    url: "/crm/esApp/updateEsAppInfo",
    method: "put",
    data: data
  });
}

//用户修改
export function updatePremise(data) {
  return request({
    url: "/crm/premise/updatePremise",
    method: "put",
    data: data
  });
}

//换表
export function changeMtr(data) {
  return request({
    url: "/crm/premise/changeMtr",
    method: "post",
    data: data
  });
}

//提交换表流程
export function commitChangeMtr(data) {
  return request({
    url: "/crm/premise/commitChangeMtrFlow",
    method: "post",
    data: data
  });
}

//用户拓展信息查询
export function queryEcPremiseExtends(data) {
  return request({
    url: "/crm/premiseExtends/queryInfo",
    method: "get",
    params: data
  });
}

//用户拓展信息存储
export function savePremiseExtendsInfo(data) {
  return request({
    url: "/crm/premiseExtends/saveInfo",
    method: "post",
    data: data
  });
}

//团缴户查询
export function queryMainAndSonPremise(data) {
  return request({
    url: "/crm/premise/queryMainAndSonPremise",
    method: "get",
    params: data
  });
}

//团缴户用户列表查询
export function queryGroupPremiseList(data) {
  return request({
    url: "/crm/premise/queryGroupPremiseList",
    method: "get",
    params: data
  });
}

//团缴户绑定子户
export function bindSonCons(data) {
  return request({
    url: "/crm/premise/bindSonCons",
    method: "post",
    data: data
  });
}
//子户解绑
export function unBindSonCons(data) {
  return request({
    url: "/crm/premise/unBindSonCons",
    method: "post",
    data: data
  });
}

//删除用户档案
export function deletePremise(data) {
  return request({
    url: "/crm/premise/deletePremise",
    method: "post",
    data: data
  });
}

//销户流程发起
export function commitDeletePremiseFlow(data) {
  return request({
    url: "/crm/premise/commitDeletePremiseFlow",
    method: "post",
    data: data
  });
}

//改价流程发起
export function commitChangePrcFlow(data) {
  return request({
    url: "/crm/premise/commitChangePrcFlow",
    method: "post",
    data: data
  });
}

//改价流程发起
export function updatePrc(data) {
  return request({
    url: "/crm/premise/updatePrc",
    method: "post",
    data: data
  });
}

// 查询表具编号是否存在
export function queryExistCount(data) {
  return request({
    url: "/crm/premise/queryExistCount",
    method: "get",
    params: data
  });
}

//修改手机号
export function updatePremiseInfo(data) {
  return request({
    url: "/crm/premise/updatePremiseInfo",
    method: "post",
    data: data
  });
}

//提交开户流程
export function submitAddPremiseFlow(data) {
  return request({
    url: "/crm/premise/submitAddPremiseFlow",
    method: "post",
    data: data
  });
}

//查询销户信息
export function queryDelPremiseInfo(data) {
  return request({
    url: "/crm/premise/queryDelPremiseInfo",
    method: "post",
    data: data
  });
}
