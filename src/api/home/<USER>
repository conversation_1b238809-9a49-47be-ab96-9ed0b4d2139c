import request from "@/utils/request";

// 获取项目列表
export function getAllMenuList(data) {
  return request({
    url: "/sys/menu/allMenuTree",
    method: "get",
    params: data,
  });
} // 查询自定义菜单
export function getCheckedList(data) {
  return request({
    url: "/sys/menu/menuByUser",
    method: "get",
    params: data,
  });
} // 保存用户快捷菜单
export function saveMenuList(data) {
  return request({
    url: "/sys/menu/saveUserMenu",
    method: "post",
    data: data,
  });
}
