import request from "@/utils/request";
import BusinessType from "@/views/demandPoolManage/demandPool/businessType.vue";
//合作商
export default {
  // 列表
  list(data) {
    return request({
      url: "/archive/partner/list",
      method: "post",
      data,
    });
  },
  //打标签
  tag(data) {
    return request({
      url: "/archive/partner/operateTag",
      method: "post",
      data,
    });
  },
  //日志
  log(data) {
    return request({
      url: "/archive/partner/queryTag",
      method: "get",
      params: data,
    });
  },
  //导出
  export(data) {
    return request({
      url: "/archive/partner/export",
      method: "post",
      data,
    });
  },
  //查询业务类型
  businessType(data) {
    return request({
      url: "/archive/partner/queryBusiness",
      method: "get",
      params: data,
    });
  },
};
