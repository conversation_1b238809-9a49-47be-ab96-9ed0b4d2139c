import request from "@/utils/request";
//合同检测
export default {
  // 模版列表
  templateList(data) {
    return request({
      url: "/contractual/check/queryCheckItemsList",
      method: "get",
      params: data,
    });
  },
  //模版日志
  templateLog(data) {
    return request({
      url: "/contractual/check/queryLog",
      method: "get",
      params: data,
    });
  },
  //上传合同协议
  uploadTemplate(data) {
    return request({
      url: "/contractual/check/item",
      method: "get",
      params: data,
    });
  },
  //新增模板
  saveTemplate(data) {
    return request({
      url: "/contractual/check/saveCheckItems",
      method: "post",
      data,
    });
  },
  //修改模版
  updateTemplate(data) {
    return request({
      url: "/contractual/check/updateCheckItems",
      method: "post",
      data,
    });
  },
  //删除模版
  delete(data) {
    return request({
      url: "/contractual/check/removeCheckItems",
      method: "get",
      params: data,
    });
  },
  //在线检测
  //第一次检测
  checkFilesStepOne(data) {
    return request({
      url: "/contractual/check/checkFilesStepOne",
      method: "post",
      data,
    });
  },
  //第二次检测
  checkFilesStepTwo(data) {
    return request({
      url: "/contractual/check/checkFilesStepTwo",
      method: "post",
      data,
    });
  },
  //检测记录列表
  recordPageList(data) {
    return request({
      url: "/archive/record/list",
      method: "get",
      params: data,
    });
  },
  //检测记录日志
  recordLog(data) {
    return request({
      url: "/archive/record/queryLog",
      method: "get",
      params: data,
    });
  },
  //保存下载日志
  saveDownload(data) {
    return request({
      url: "/archive/record/downloadLog",
      method: "get",
      params: data,
    });
  },
  //发送邮件
  sendMail(data) {
    return request({
      url: "/contractual/check/sendMail",
      method: "post",
      data,
      skipIntercept: true, //跳过拦截 弹窗展示错误信息
    });
  },
  //查看检查项配置
  queryCheckItems(data) {
    return request({
      url: "/contractual/check/queryCheckItemsDetailByAttrAndType",
      method: "get",
      params: data,
    });
  },
};
