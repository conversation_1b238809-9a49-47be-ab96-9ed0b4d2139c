import request from "@/utils/request";
//数据统计
export default {
  // 数据分析
  queryStatisticData(data) {
    return request({
      url: "/data/statistics/dataAnalysis",
      method: "post",
      data: data,
    });
  },
  //监控预警
  queryWarnData(data) {
    return request({
      url: "/data/statistics/recentMonths",
      method: "get",
      params: data,
    });
  },
  //监控预警-前两个表格
  queryWarnTable(data) {
    return request({
      url: "/data/statistics/monitor/contract",
      method: "post",
      data: data,
    });
  },
  //监控预警-变更表格
  queryChangedTable(data) {
    return request({
      url: "/data/statistics/monitor/contractChange",
      method: "post",
      data: data,
    });
  },
  export(data) {
    return request({
      url: "/data/statistics/export",
      method: "post",
      data: data,
    });
  },
  exportChanged(data) {
    return request({
      url: "/data/statistics/export/contractChange",
      method: "post",
      data: data,
    });
  },
};
