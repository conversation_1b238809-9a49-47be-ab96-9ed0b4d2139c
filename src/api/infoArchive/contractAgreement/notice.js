import request from "@/utils/request";
//预警管理
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/alarmManage/queryList",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/alarmManage/delete",
      method: "get",
      params: query,
    });
  },

  //查询日志
  queryLog(data) {
    return request({
      url: "/alarmManage/log",
      method: "get",
      params: data,
    });
  },
  //保存/新增
  update(query) {
    return request({
      url: "/alarmManage/save",
      method: "post",
      data: query,
    });
  },
  //状态切换
  changeStatus(query) {
    return request({
      url: "/alarmManage/onOrOff",
      method: "post",
      data: query,
    });
  },
  //通知触发条件列表
  queryCondition(data) {
    return request({
      url: "/alarmManage/condition",
      method: "get",
      params: data,
    });
  },
};
