import request from "@/utils/request";
// 站点清分规则列表
export function queryRuleList(data) {
  return request({
    url: "/apply/rule/list",
    method: "post",
    data: data,
  });
}
//删除
export function deleteApply(data) {
  return request({
    url: "/apply/rule/delete",
    method: "get",
    params: data,
  });
}
//导出
export function exportExcel(data) {
  return request({
    url: "/apply/rule/export",
    method: "post",
    data: data,
  });
}
//确认
export function submitConfirmResult(data) {
  return request({
    url: "/apply/rule/verify",
    method: "post",
    data: data,
  });
}
//详情
export function queryDetail(data) {
  return request({
    url: "/apply/rule/detail",
    method: "post",
    data: data,
  });
}
//新增/修改
export function submitForm(data) {
  return request({
    url: "/apply/rule/save",
    method: "post",
    data: data,
  });
}
