import request from "@/utils/request";
import qs from "qs";

// 获取项目列表
export function projectInfoList(data) {
  return request({
    url: "/project/projectInfoList",
    method: "get",
    params: data,
  });
}

// 获取项目列表
export function queryAllProjectFlow(data) {
  return request({
    url: "/project/queryAllProjectFlow",
    method: "post",
    data: data,
  });
}

// 新增项目
export function addProjectInfo(data) {
  return request({
    url: "/project/addProjectInfo",
    method: "post",
    data: data,
  });
}

// 修改项目
export function updateProjectInfo(data) {
  return request({
    url: "/project/updateProjectInfo",
    method: "post",
    data: data,
  });
}

// 查询重复项目
export function queryRepeatProject(data) {
  return request({
    url: "/project/queryRepeatProject",
    method: "post",
    data: data,
  });
}

// 多文件上传
export function uploadFile(data) {
  return request({
    url: "/doc/upload",
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data;",
    },
    data: data,
  });
}

// 获取项目列表--查询(项目管理)
export function queryProjectInfoByPage(data) {
  return request({
    url: "/project/queryProjectInfoByPage",
    method: "post",
    data: data,
  });
}

// 获取项目列表--查询(施工管理)
export function queryConstructingProjectInfoByPage(data) {
  return request({
    url: "/project/queryConstructingProjectInfoByPage",
    method: "post",
    data: data,
  });
}

export function queryRoadTestProjectInfoByPage(data) {
  return request({
    url: "/project/queryRoadTestProjectInfoByPage",
    method: "post",
    data: data,
  });
}

// 获取项目详情--查询
export function queryProjectInfoById(data) {
  return request({
    url: "/project/queryProjectInfoById",
    method: "post",
    data: data,
  });
}

// 添加工单
export function addProjectConstructionFlow(data) {
  return request({
    url: "/project/addProjectConstructionFlow",
    method: "post",
    data: data,
  });
}

export function addErrorMsgFlow(data) {
  return request({
    url: "/project/addErrorMsgFlow",
    method: "post",
    data: data,
  });
}

export function addErrorMsgFlowList(data) {
  return request({
    url: "/project/addErrorMsgFlowList",
    method: "post",
    data: data,
  });
}

export function freezeProject(data) {
  return request({
    url: "/project/freezeProject",
    method: "post",
    data: data,
  });
}
export function unfreezeProject(data) {
  return request({
    url: "/project/unfreezeProject",
    method: "post",
    data: data,
  });
}

export function deleteProject(data) {
  return request({
    url: "/project/deleteProject",
    method: "post",
    data: data,
  });
}

export function logList(data) {
  return request({
    url: "/operate/record/detail?projectId=" + data,
    method: "get",
  });
}
// 导出
export function exportProjectList(query) {
  return request({
    url: "/project/export",
    method: "post",
    data: query,
  });
}
//站点编号模糊搜索
export function queryStationCode(data) {
  return request({
    url: "/station/stationList",
    method: "get",
    params: data,
  });
} //根据站点id 获取站点详细信息
export function queryStationDetail(data) {
  return request({
    url: "/station/queryStationInfoById",
    method: "get",
    params: data,
  });
}
//复制
export function copyProject(data) {
  return request({
    url: "/project/cpoyProjectInfo",
    method: "post",
    data: data,
  });
}
