import processRequest from "@/utils/processRequest";
import request from "@/utils/request";


export function updateVariables(query){
  return request({
    url: "/project/updateVariables",
    method: "post",
    data: query
  });
}


// 全部列表
export function listAll(query) {
  return processRequest({
    url: "/api/v1/process/tasks/listAll",
    method: "get",
    params: query
  });
}

//报装全部列表
export function listInstallAll(query) {
  return processRequest({
    url: "/api/v1/process/install/tasks/listAll",
    method: "get",
    params: query
  });
}

// 我发起列表
export function listISponsor(query) {
  return processRequest({
    url: "/api/v1/process/tasks/listISponsor",
    method: "get",
    params: query
  });
}

//报装我发起的
export function listInstallISponsor(query) {
  return processRequest({
    url: "/api/v1/process/install/tasks/listISponsor",
    method: "get",
    params: query
  });
}

// 代办列表
export function listTodo(query) {
  return processRequest({
    url: "/api/v1/process/tasks/listTodo",
    method: "get",
    params: query
  });
}

// 报装待办列表
export function listInstallTodo(query) {
  return processRequest({
    url: "/api/v1/process/install/tasks/listTodo",
    method: "get",
    params: query
  });
}

// 待处理列表
export function listToDeal(query) {
  return processRequest({
    url: "/api/v1/process/tasks/listToDeal",
    method: "get",
    params: query
  });
}

// 报装待处理列表
export function listInstallToDeal(query) {
  return processRequest({
    url: "/api/v1/process/install/tasks/listToDeal",
    method: "get",
    params: query
  });
}

//报装调度台工单类型列表
export function workOrderTypeListByPack(query) {
  return processRequest({
    url: "/external/api/v1/flwExtendFlowType/workOrderTypeListByPack",
    method: "get",
    params: query
  });
}

//工单类型列表
export function workOrderTypeList(query) {
  return processRequest({
    url: "/external/api/v1/flwExtendFlowType/bindingFlowTypeList",
    method: "get",
    params: query
  });
}

//工单类型列表
export function useFlowList(query) {
  return processRequest({
    url: "/external/api/v1/flowdef/listByBusinessType",
    method: "get",
    params: query
  });
}

//工单类型列表- 过滤无启用流程实例类型
export function planFlowTypeList(query) {
  return processRequest({
    url: "/external/api/v1/flwExtendFlowType/planFlowTypeList",
    method: "get",
    params: query
  });
}

//工单流程列表
export function getProcessStartInfo(query) {
  return processRequest({
    url: "/external/api/v1/flwExtendFlowType/getProcessStartInfo",
    method: "get",
    params: query
  });
}

//创建工单获取表单接口
export function taskFormsFirstDeal(query) {
  return processRequest({
    url: "/api/v1/process/form/taskFormsFirstDeal",
    method: "get",
    params: query
  });
}

//获取工单流程接口
export function getNodeInfos(query) {
  return processRequest({
    url: "/api/v1/process/node/getNodeInfos",
    method: "get",
    params: query
  });
}

//创建工单提交
export function startWithForm(data) {
  return processRequest({
    url: "/api/v1/process/insts/startWithForm",
    method: "post",
    data: data
  });
}

// 处理订单数据获取接口
export function taskForms(query) {
  return processRequest({
    url: "/api/v1/process/form/taskForms",
    method: "get",
    params: query
  });
}

//处理的操作按钮列表
export function taskConfigs(query) {
  return processRequest({
    url: "/api/v1/process/config/taskConfigs",
    method: "get",
    params: query
  });
}

//处理提交按钮
export function dealComplete(data) {
  return processRequest({
    url: "/api/v1/process/tasks/complete",
    method: "post",
    data: data
  });
}

export function complete(data) {
  return request({
    url: "/business/complete",
    method: "post",
    data: data
  });
}

//工单转换: transferType  1=申领;2=改派
export function transferOrder(data) {
  return processRequest({
    url: "/api/v1/process/tasks/transfer",
    method: "post",
    data: data
  });
}

//工单查看
export function getNodeHisInfoInfos(query) {
  return processRequest({
    url: "/api/v1/process/node/getNodeHisInfoInfos",
    method: "get",
    params: query
  });
}

//已完成列表
export function listIFinished(query) {
  return processRequest({
    url: "/api/v1/process/tasks/listIFinished",
    method: "get",
    params: query
  });
}

//报装已完成列表
export function listInstallIFinished(query) {
  return processRequest({
    url: "/api/v1/process/install/tasks/listIFinished",
    method: "get",
    params: query
  });
}

//计划工单新增
export function orderPlanAdd(data) {
  return request({
    url: "/api/work/order/plan/add",
    method: "post",
    data: data
  });
}

//计划工单-状态修改
export function orderPlanEditStatus(data) {
  return request({
    url: "/api/work/order/plan/editStatus",
    method: "post",
    data: data
  });
}

//计划工单编辑
export function orderPlanEdit(data) {
  return request({
    url: "/api/work/order/plan/edit",
    method: "post",
    data: data
  });
}
//计划工单查看
export function orderPlanQueryById(query) {
  return request({
    url: "/api/work/order/plan/queryById",
    method: "get",
    params: query
  });
}
//计划工单删除
export function orderPlanDeleteById(data) {
  return request({
    url: "/api/work/order/plan/deleteById",
    method: "post",
    data: data
  });
}
//计划工单列表
export function orderPlanList(query) {
  return request({
    url: "/api/work/order/plan/list",
    method: "get",
    params: query
  });
}

//
export function businessFlowParaQueryById(query) {
  return request({
    url: "/crm/businessFlowPara/queryById",
    method: "post",
    data: query
  });
}

export function businessFlowParaQueryByProcessId(query) {
  return request({
    url: "/crm/businessFlowPara/getByProcessInstId",
    method: "post",
    data: query
  });
}

export function queryByProcessInstId(query) {
  return request({
    url: "/crm/businessFlowRela/queryByProcessInstId",
    method: "post",
    data: query
  });
}

export function queryRelaList(query) {
  return request({
    url: "/crm/businessFlowRela/queryList",
    method: "post",
    data: query
  });
}

//业务办理列表
export function queryBusinessList(query) {
  return processRequest({
    url: "/api/v1/process/businessHandle/queryBusinessList",
    method: "post",
    data: query
  });
}

// 获取账户下拉数据
export function listUser(data) {
  return request({
    url: "/system/user/getUserList",
    method: "post",
    data: data
  });
}

// 工作台-（审批，作废）
export function dynamicCompleteTask(data) {
  return processRequest({
    url: "/api/v1/process/tasks/dynamicCompleteTask",
    method: "post",
    data: data
  });
}

//查询流程实例当前节点任务信息
export function processTaskInfo(data) {
  return processRequest({
    url: "/external/api/v1/task/processTaskInfo",
    method: "post",
    data: data
  });
}
