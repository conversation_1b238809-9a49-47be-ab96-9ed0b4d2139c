import request from "@/utils/request";
export function getDeviceAlert(query) {
    return request({
      url: '/crm/asset/sync/list',
      method: "post",
      data: query
    });
  }
  
  export function getAlertItem() {
    return request({
      url: '/crm/asset/alarm/alarms',
      method: "get"
    });
  }
  
  export function handleAlert(data) {
    return request({
      url: '/crm/asset/alarm/save',
      method: 'post',
      data: data
    });
  }

 //导出
 export function exportData(query) {
   return request({
     url: "/crm/asset/sync/export",
     method: "get",
     params: query
   });
 }