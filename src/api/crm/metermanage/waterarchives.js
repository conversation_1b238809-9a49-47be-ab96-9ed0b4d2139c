import request from "@/utils/request";

// 查询用户列表
export function list(query) {
  return request({
    url: "/crm/eamMtr/queryList",
    method: "post",
    data: query
  });
}

// 查询远传表数据
export function getEamMtrs(query) {
  return request({
    url: "/crm/asset/eamMtrController/getEamMtrs",
    method: "post",
    data: query
  });
}

// 新增用户
export function add(data) {
  return request({
    url: "/crm/eamMtr/addEamMtr",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/crm/eamMtr/updateEamMtr",
    method: "post",
    data: data
  });
}

// 删除
export function del(data) {
  return request({
    url: "/crm/eamMtr/delEamMtr",
    method: "get",
    params: data
  });
}

// 远抄表档案导出
export function farSpreadMeterExport(query) {
  return request({
    url: "/crm/asset/eamMtrController/farSpreadMeterExport",
    method: "get",
    params: query
  });
}

// 开阀
export function mandatoryOpen(data) {
  return request({
    url: "/meter/mandatoryOpen",
    method: "post",
    data: data
  });
}

// 关阀
export function mandatoryClose(data) {
  return request({
    url: "/meter/mandatoryClose",
    method: "post",
    data: data
  });
}

export function getMtrTypeNames(){
  return request({
    url: "/crm/asset/eamMtrController/getMtrTypeNames",
    method: "get"
  });
}

export function getMtrTypeNamesByOrgNoList(query){
  return request({
    url: "/crm/asset/eamMtrController/getMtrTypeNamesByOrgNoList",
    method: "get",
    params: query
  });
}

// 批量开阀
export function batchOpenValve(data) {
  return request({
    url: "/meter/batchOpenValve",
    method: "post",
    data: data
  });
}

// 批量关阀
export function batchCloseValve(data) {
  return request({
    url: "/meter/batchCloseValve",
    method: "post",
    data: data
  });
}

// 批量注册
export function batchRegister(data) {
  return request({
    url: "/meter/batchRegister",
    method: "post",
    data: data
  });
}

// 余额同步
export function send(data) {
  return request({
    url: "/meter/send",
    method: "post",
    data: data
  });
}

// 重新下发指令
export function resendInstruction(data) {
  return request({
    url: "/meter/resendInstruction",
    method: "post",
    data: data
  });
}
export function getAllList(params) {
  return request({
    url: "/meter/model/getAllList",
    method: "get",
    params:params
  });
}
