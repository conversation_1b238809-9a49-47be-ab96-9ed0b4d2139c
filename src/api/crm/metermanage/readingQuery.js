import request from "@/utils/request";
export function getDeviceAlert(query) {
    return request({
      url: '/crm/asset/reading/list',
      method: "post",
      data: query
    });
  }

export function getMeterBalance(query) {
  return request({
    url: '/crm/asset/reading/getMeterBalance',
    method: "post",
    data: query
  });
}

export function exportBalanceData(query) {
  return request({
    url: "/crm/asset/reading/getMeterBalance/export",
    method: "post",
    data: query,
    responseType: 'blob'
  });
}

  
  export function getAlertItem() {
    return request({
      url: '/crm/asset/alarm/alarms',
      method: "get"
    });
  }
  
  export function handleAlert(data) {
    return request({
      url: '/crm/asset/alarm/save',
      method: 'post',
      data: data
    });
  }

  //导出
  export function exportData(query) {
    return request({
      url: "/crm/asset/reading/export",
      method: "get",
      params: query
    });
  }