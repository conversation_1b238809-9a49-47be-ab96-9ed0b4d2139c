import request from "@/utils/request";

// 查询气表目录列表
export function waterListMenu(query) {
  return request({
    url: "/meter/model/list",
    method: "post",
    data: query
  });
}

// 新增气表目录
export function addWaterList(data) {
  return request({
    url: "/meter/model",
    method: "post",
    data: data
  });
}

// 修改气表目录
export function updateMenu(data) {
  return request({
    url: "/meter/model",
    method: "put",
    data: data
  });
}

// 删除公告
export function delWaterList(listId) {
  return request({
    url: "/meter/model/" + listId,
    method: "delete"
  });
}

// 查询公告详细
export function getWaterList(listId) {
  return request({
    url: "/meter/model/" + listId,
    method: "get"
  });
}


// 查询公告详细
export function getWaterListByNo(listNo) {
  return request({
    url: "/meter/model/getMeterListByNo",
    method: "get",
    params:{
      listNo : listNo
    }
  });
}

//查询厂家设备id
export function getDeviceId(query){
  return request({
    url: "/meter/register",
    method: "post",
    data: query
  })
}

// 气表导出
export function exportData(query) {
  return request({
      url: "/meter/model/meterListExport",
      method: "get",
      params: query,
      responseType: 'blob',
  });
}

export function uploadAvatar(data) {
  return request({
    url: '/meter/model/upload',
    method: 'post',
    data: data
  });
}
