import request from "@/utils/request";

export function list(query) {
    return request({
        url: '/meter/archives/list',
        method: "post",
        data: query
    });
}

export function exportData(query) {
    return request({
        url: '/meter/archives/exportData',
        method: "post",
        data: query
    });
}

export function meterBindUser(query) {
    return request({
        url: '/meter/archives/meterBindUser',
        method: "post",
        data: query
    });
}

export function deleteByMeterId(query) {
    return request({
        url: '/meter/archives/deleteByMeterId',
        method: "post",
        data: query
    });
}

export function add(query) {
    return request({
        url: '/meter/archives/add',
        method: "post",
        data: query
    });
}


export function update(query) {
    return request({
        url: '/meter/archives/update',
        method: "post",
        data: query
    });
}
