import request from "@/utils/request";
export function getNotreport(query) {
    return request({
      url: '/crm/asset/notreport/list',
      method: "get",
      params: query
    });
}

  // 查询用户列表
export function exportData(query) {
  return request({
    url: "/crm/asset/notreport/export",
    method: "get",
    params: query
  });
}

/**
 * 查询近期未用气气表
 * @param query
 * @returns {AxiosPromise}
 */
export function notUseList(query) {
    return request({
        url: '/crm/asset/notreport/notUseList',
        method: "get",
        params: query
    });
}

// 导出
export function exportNotUse(query) {
    return request({
        url: "/crm/asset/notreport/exportNotUse",
        method: "get",
        params: query
    });
}