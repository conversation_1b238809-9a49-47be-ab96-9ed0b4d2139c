import request from "@/utils/request";
import qs from 'qs';

// 查询用户列表
export function list(query) {
  return request({
    url: "/system/doc/list",
    method: "get",
    params: query
  });
}

// 新增用户
export function add(file,data) {
  return request({
    url: "/system/doc/testM?"+qs.stringify(data),
    method: "post",
    headers:{
        'Content-Type': 'multipart/form-data',
    },
    data: file
  });
}

// 删除
export function del(ids) {
  return request({
    url: "/system/doc/" + ids,
    method: "delete"
  });
}

// 查询用户列表
export function getPath(query) {
  return request({
    url: "/common/getPath",
    method: "get"
  });
}
