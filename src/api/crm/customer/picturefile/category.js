import request from "@/utils/request";

// 查询用户列表
export function list(query) {
  return request({
    url: "/system/docType/list",
    method: "get",
    params: query
  });
}

// 新增用户
export function add(data) {
  return request({
    url: "/system/docType",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/system/docType",
    method: "put",
    data: data
  });
}

// 删除
export function del(ids) {
  return request({
    url: "/system/docType/" + ids,
    method: "delete"
  });
}

