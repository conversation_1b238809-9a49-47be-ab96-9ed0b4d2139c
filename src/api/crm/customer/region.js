import request from "@/utils/request";

// 查询区域列表
export function list(query) {
  return request({
    url: "/system/region/list",
    method: "post",
    data: query
  });
}

// 新增用户
export function add(data) {
  return request({
    url: "/system/region",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/system/region",
    method: "put",
    data: data
  });
}

// 删除
export function del(ids) {
  return request({
    url: "/system/region/" + ids,
    method: "delete"
  });
}

//获取系统用户
export function getUserList(data) {
  return request({
      url: "/system/user/getUserList",
      method: "post",
      data: data
  });
}

// 查询当前用户的所有区域
export function allList(query) {
  return request({
    url: "/system/region/all",
    method: "get",
    params: query
  });
}
