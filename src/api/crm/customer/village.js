import request from "@/utils/request";

// 查询小区列表
export function list(query) {
  return request({
    url: "/system/community/list",
    method: "post",
    data: query
  });
}

//查询抄表员
export function getOperatorInfo(query) {
  return request({
    url: "/report/collection/getOperatorInfo",
    method: "get",
    params: query
  });
}

// 新增
export function add(data) {
  return request({
    url: "/system/community",
    method: "post",
    data: data
  });
}

// 修改
export function update(data) {
  return request({
    url: "/system/community",
    method: "put",
    data: data
  });
}

// 删除
export function del(ids) {
  return request({
    url: "/system/community/" + ids,
    method: "delete"
  });
}

// 查询
export function queryVillageList(query) {
  return request({
    url: "/system/community/queryVillageList",
    method: "get",
    params: query
  });
}
