import request from "@/utils/request";

// 获取项目列表
export function queryErrorInfoByPage(data) {
  return request({
    url: "/error/push/queryErrorInfoByPage",
    method: "get",
    params: data,
  });
}

// 获取项目列表
export function ignore(data) {
  return request({
    url: "/error/push/ignore",
    method: "post",
    data: data,
  });
}
//批量忽略
export function batchIgnore(data) {
  return request({
    url: "/error/push/batchIgnore",
    method: "post",
    data: data,
  });
}
// 异常信息-黑名单站点
//列表查询
export function queryBlackStation(data) {
  return request({
    url: "/blacklist/station/list",
    method: "post",
    data: data,
  });
}
// 获取站点列表
export function stationList(data) {
  return request({
    url: "/station/stationList",
    method: "get",
    params: data,
  });
}
// 新增黑名单站点
export function submitAddStation(data) {
  return request({
    url: "/blacklist/station/save",
    method: "post",
    data: data,
  });
}
// 修改黑名单站点
export function submitEditStation(data) {
  return request({
    url: "/blacklist/station/update",
    method: "post",
    data: data,
  });
}
// 解除黑名单站点
export function removeBlackList(data) {
  return request({
    url: "/blacklist/station/remove",
    method: "post",
    data: data,
  });
}
// 异常信息-黑名单用户
//列表查询
export function queryBlackUser(data) {
  return request({
    url: "/blacklist/user/list",
    method: "post",
    data: data,
  });
}
// 获取用户列表
export function userList(data) {
  return request({
    url: "/station/stationList",
    method: "get",
    params: data,
  });
}
// 新增黑名单用户
export function submitAddUser(data) {
  return request({
    url: "/blacklist/user/save",
    method: "post",
    data: data,
  });
}
// 修改黑名单用户
export function submitEditUser(data) {
  return request({
    url: "/blacklist/user/update",
    method: "post",
    data: data,
  });
}
// 解除黑名单用户
export function removeBlackUser(data) {
  return request({
    url: "/blacklist/user/remove",
    method: "post",
    data: data,
  });
}

// 异常信息-设置分组
//列表查询
export function queryGroupList(data) {
  return request({
    url: "/handle/group/list",
    method: "post",
    data: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "/handle/group/changeStatus",
    method: "post",
    data: data,
  });
}
//查看组内成员列表
export function queryGroupMemberList(data) {
  return request({
    url: "/handle/group/user/list",
    method: "post",
    data: data,
  });
}
//操作日志
export function queryLogList(data) {
  return request({
    url: "/handle/group/log/list",
    method: "post",
    data: data,
  });
}
//查询所有组织-用户 树结构
export function queryTreeList(data) {
  return request({
    url: "/handle/group/deptUserList",
    method: "get",
    params: data,
  });
}
//查询已绑定的用户（修改时）
export function queryGroupUser(data) {
  return request({
    url: "/handle/group/groupUserList",
    method: "get",
    params: data,
  });
}
//修改
export function submitAdd(data) {
  return request({
    url: "/handle/group/save",
    method: "post",
    data: data,
  });
}
//修改
export function submitEdit(data) {
  return request({
    url: "/handle/group/update",
    method: "post",
    data: data,
  });
}
//建单规则
//列表查询
export function queryOrderRuleList(data) {
  return request({
    url: "/orderRule/list",
    method: "post",
    data: data,
  });
}
//规则配置
export function updateRuleConfig(data) {
  return request({
    url: "/orderRule/config",
    method: "post",
    data: data,
  });
}
//查看日志
export function queryRuleLog(data) {
  return request({
    url: "/orderRule/log/list",
    method: "get",
    params: data,
  });
}
//刷新
export function refreshRules(data) {
  return request({
    url: "/orderRule/refresh",
    method: "get",
    params: data,
  });
}
//查看故障详情
export function queryFaultDetail(data) {
  return request({
    url: "/error/push/errorDetail",
    method: "get",
    params: data,
  });
}
