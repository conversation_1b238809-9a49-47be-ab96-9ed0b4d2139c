import request from "@/utils/request";

// 分组管理接口
//列表查询
export function queryGroupList(data) {
  return request({
    url: "/group/manage/list",
    method: "post",
    data: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "group/manage/changeStatus",
    method: "post",
    data: data,
  });
}
//操作日志
export function queryLogList(data) {
  return request({
    url: "/group/manage/record",
    method: "get",
    params: data,
  });
}
//查询所有组织-用户 树结构
export function queryTreeList(data) {
  return request({
    url: "/handle/group/deptUserList",
    method: "get",
    params: data,
  });
}
//查询详情（修改时）
export function queryGroupUser(data) {
  return request({
    url: "/group/manage/detail",
    method: "get",
    params: data,
  });
}
//修改
export function submitSave(data) {
  return request({
    url: "/group/manage/save",
    method: "post",
    data: data,
  });
}
//获取一级需求类型
export function queryFirstDemandType(data) {
  return request({
    url: "/demandType/fistLevelList",
    method: "get",
    params: data,
  });
} //根据一级获取二级需求类型
export function queryChildDemandType(data) {
  return request({
    url: "/demandType/childrenList",
    method: "get",
    params: data,
  });
}
//获取所有二级需求类型
export function querySecondDemandType(data) {
  return request({
    url: "/demandType/secondLevelList",
    method: "get",
    params: data,
  });
}
