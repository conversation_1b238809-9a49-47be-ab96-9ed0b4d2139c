import request from "@/utils/request";

// 业务类型接口
//获取树结构
export function queryTreeList(data) {
  return request({
    url: "/businessType/list",
    method: "get",
    params: data,
  });
}
//删除
export function deleteType(data) {
  return request({
    url: "/businessType/remove",
    method: "post",
    data: data,
  });
}
//新增
export function addType(data) {
  return request({
    url: "/businessType/save",
    method: "post",
    data: data,
  });
}
//修改
export function editType(data) {
  return request({
    url: "/businessType/update",
    method: "post",
    data: data,
  });
}
