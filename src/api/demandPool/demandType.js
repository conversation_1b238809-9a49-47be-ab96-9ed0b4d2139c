import request from "@/utils/request";

// 需求类型接口
//获取树结构
export function queryTreeList(data) {
  return request({
    url: "/demandType/list",
    method: "get",
    params: data,
  });
}
//删除
export function deleteType(data) {
  return request({
    url: "/demandType/remove",
    method: "post",
    data: data,
  });
}
//新增
export function addType(data) {
  return request({
    url: "/demandType/save",
    method: "post",
    data: data,
  });
}
//修改
export function editType(data) {
  return request({
    url: "/demandType/update",
    method: "post",
    data: data,
  });
}
//排序
export function sortTree(data) {
  return request({
    url: "/demandType/order",
    method: "get",
    params: data,
  });
}
//获取业务类型树
export function queryBusinessList(data) {
  return request({
    url: "/businessType/list",
    method: "get",
    params: data,
  });
}
//关联分组
export function bindGroups(data) {
  return request({
    url: "/demandType/bindGroup",
    method: "post",
    data: data,
  });
}
