import request from "@/utils/request";

export default {
  /**
   * 分页查询合作商列表
   * @param {*} partnerName  合作商
   * @param {*} contact 联系人
   * @param {*} status 状态，0：启用，1：禁用
   * @returns
   */
  queryPartnerList(data) {
    return request({
      url: "/partner/list",
      method: "post",
      data
    });
  },
  /**
   * 编辑或保存
   * @param {*} data
   * @returns
   */
  savePartnerInfo(data) {
    return request({
      url: "/partner/save",
      method: "post",
      data
    });
  },
  /**
   * 启停状态
   * @param {*} partnerId 主键id	（必填）
   * @param {*} status 状态，0：启用，1：禁用   （必填）
   * @returns
   */
  changeStatus(data) {
    return request({
      url: "/partner/changeStatus",
      method: "post",
      data
    });
  },
  /**
   * 详情
   * @param {*} partnerId 主键id	（必填）
   * @returns
   */
  getPartnerDetail(data) {
    return request({
      url: "/partner/detail",
      method: "post",
      data
    });
  },
  /**
   * 删除
   * @param {*} partnerId 主键id	（必填）
   * @returns
   */
  removePartner(data) {
    return request({
      url: "/partner/remove",
      method: "post",
      data
    });
  }
};
