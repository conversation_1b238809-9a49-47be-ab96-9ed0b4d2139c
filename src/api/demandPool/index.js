import request from "@/utils/request";

// 需求池接口
//列表查询
export function queryDemandList(data) {
  return request({
    url: "/demand/poll/list",
    method: "post",
    data: data,
  });
}
//导出
export function exportExcel(data) {
  return request({
    url: "/demand/poll/export",
    method: "post",
    data: data,
  });
}
//删除
export function deleteDemand(data) {
  return request({
    url: "/demand/poll/remove",
    method: "get",
    params: data,
  });
}
//撤销
export function withdrawDemand(data) {
  return request({
    url: "/demand/poll/revoke",
    method: "get",
    params: data,
  });
}
//接收
export function acceptDemand(data) {
  return request({
    url: "/demand/poll/receive",
    method: "get",
    params: data,
  });
}
//恢复
export function restoreDemand(data) {
  return request({
    url: "/demand/poll/recovery",
    method: "get",
    params: data,
  });
}
//转派
export function transferDemand(data) {
  return request({
    url: "/demand/poll/transfer",
    method: "get",
    params: data,
  });
}
//确认
export function confirmDemand(data) {
  return request({
    url: "/demand/poll/confirm",
    method: "get",
    params: data,
  });
}
//审批进度
export function queryProcess(data) {
  return request({
    url: "/demand/poll/getApproveRecord",
    method: "get",
    params: data,
  });
}
//关联单号
export function associateDemand(data) {
  return request({
    url: "/demand/poll/omNo",
    method: "get",
    params: data,
  });
}
//邮寄时间
export function submitMailTime(data) {
  return request({
    url: "/demand/poll/mailTime",
    method: "post",
    data: data,
  });
}
//归档
export function submitDocumentation(data) {
  return request({
    url: "/demand/poll/file",
    method: "get",
    params: data,
  });
}
//备注
export function submitRemark(data) {
  return request({
    url: "/demand/poll/remark",
    method: "post",
    data: data,
  });
}
//完结
export function submitComplete(data) {
  return request({
    url: "/demand/poll/finish",
    method: "post",
    data: data,
  });
}
//加补材料
export function submitSupple(data) {
  return request({
    url: "/demand/poll/addMaterial",
    method: "post",
    data: data,
  });
}
//复制需求
export function copyDemand(data) {
  return request({
    url: "/demand/poll/copy",
    method: "get",
    params: data,
  });
}

//获取工时编码
export function queryWorkHourList(data) {
  return request({
    url: "/demand/poll/jobHour",
    method: "get",
    params: data,
  });
}
//获取踏勘编号
export function queryStationInfo(data) {
  return request({
    // url: "/demand/poll/getStepNo",
    url: "/middle/business/list",
    method: "post",
    data: data,
  });
}
//详情
export function queryDetail(data) {
  return request({
    url: "/demand/poll/detail",
    method: "get",
    params: data,
  });
}
//新增/修改
export function submitForm(data) {
  return request({
    url: "/demand/poll/save",
    method: "post",
    data: data,
  });
}
//流转记录
export function queryRecord(data) {
  return request({
    url: "/demand/poll/demandRecord",
    method: "get",
    params: data,
  });
}
//根据二级需求获取表单
export function queryFormJson(data) {
  return request({
    url: "/formdef/enabledFormData",
    method: "get",
    params: data,
  });
}

//获取金蝶审批信息
export function queryJdApproveInfo(data) {
  return request({
    url: "/formdef/enabledFormData",
    method: "get",
    params: data,
  });
}
