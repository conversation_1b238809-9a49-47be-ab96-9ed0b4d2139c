import request from "@/utils/request";

// 数据统计
//数据概览
export function queryOrderData(data) {
  return request({
    url: "/demand/statistics/data",
    method: "post",
    data: data,
  });
}
//需求单完成趋势
export function queryHandleTend(data) {
  return request({
    url: "/demand/statistics/tendency",
    method: "post",
    data: data,
  });
}
// 需求类型
export function queryOrderType(data) {
  return request({
    url: "/demand/statistics/demandType",
    method: "post",
    data: data,
  });
}
// 受理超时情况
export function queryAcceptTimeout(data) {
  return request({
    url: "/demand/statistics/receiveTimeout",
    method: "post",
    data: data,
  });
}
// 处理超时情况
export function queryHandleTimeout(data) {
  return request({
    url: "/demand/statistics/handleTimeout",
    method: "post",
    data: data,
  });
}
// 提交超时情况
export function querySubmitTimeout(data) {
  return request({
    url: "/demand/statistics/submitOmTimeout",
    method: "post",
    data: data,
  });
}
//处理人需求接受数排名
export function queryAcceptNumRate(data) {
  return request({
    url: "/demand/statistics/handlerReceiveRanking",
    method: "post",
    data: data,
  });
}
//处理人及时率排名
export function queryTimeRate(data) {
  return request({
    url: "/demand/statistics/handlerTimelyRateRanking",
    method: "post",
    data: data,
  });
} //处理人完成率排名
export function queryCompleteRate(data) {
  return request({
    url: "/demand/statistics/handlerCompleteRateRanking",
    method: "post",
    data: data,
  });
}
//处理人能效排名
export function queryEfficientRate(data) {
  return request({
    url: "/demand/statistics/handlerEnergyEffRanking",
    method: "post",
    data: data,
  });
}
//一级业务类型
export function queryBusinessFirstLevel(data) {
  return request({
    url: "/businessType/fistLevelList",
    method: "get",
    params: data,
  });
}
//二级业务类型
export function queryBusinessChildrenList(data) {
  return request({
    url: "/businessType/childrenList",
    method: "get",
    params: data,
  });
}
//一级需求类型
export function queryDemandFirstLevel(data) {
  return request({
    url: "/demandType/fistLevelList",
    method: "get",
    params: data,
  });
}
//二级需求类型
export function queryDemandChildrenList(data) {
  return request({
    url: "/demandType/childrenList",
    method: "get",
    params: data,
  });
}
//根据二级业务类型查询需求类型树
export function queryDemandTree(data) {
  return request({
    url: "/demandType/listByBusinessType",
    method: "get",
    params: data,
  });
}
//明细列表查询
export function queryDetailList(data) {
  return request({
    url: "/demand/poll/detailList",
    method: "post",
    data: data,
  });
}
//明细列表导出
export function exportExcel(data) {
  return request({
    url: "/demand/poll/detailList/export",
    method: "post",
    data: data,
  });
}
