import request from "@/utils/request";

// 账号数据权限
//列表查询
export function queryPermissionList(data) {
  return request({
    url: "/user/demand/relation/list",
    method: "post",
    data: data,
  });
}
//可查看的需求数据
export function queryDemandList(data) {
  return request({
    url: "/user/demand/relation/demandDataList",
    method: "post",
    data: data,
  });
}
//查询所有需求 树结构
export function queryDemandTree(data) {
  return request({
    url: "/demandType/list",
    method: "get",
    params: data,
  });
}
//查询已绑定的需求（修改时）
export function queryConfigDemand(data) {
  return request({
    url: "/user/demand/relation/detail",
    method: "get",
    params: data,
  });
}
//配置
export function submitEdit(data) {
  return request({
    url: "/user/demand/relation/config",
    method: "post",
    data: data,
  });
}
//获取部门树结构
export function queryDeptTree(data) {
  return request({
    url: "/user/demand/relation/org/getOrgTreeList",
    method: "get",
    params: data,
  });
}
//获取已勾选部门
export function queryCheckedDept(data) {
  return request({
    url: "/user/demand/relation/org/getBindOrgTreeList",
    method: "get",
    params: data,
  });
}
//配置部门
export function submitDept(data) {
  return request({
    url: "/user/demand/relation/org/save",
    method: "post",
    data: data,
  });
}
//获取角色树
export function queryRoleTree(data) {
  return request({
    url: "/user/demand/relation/role/getRoleList",
    method: "get",
    params: data,
  });
}
//获取已勾选角色
export function queryCheckedRole(data) {
  return request({
    url: "/user/demand/relation/role/getBindRoleList",
    method: "get",
    params: data,
  });
}
//配置角色
export function submitRole(data) {
  return request({
    url: "/user/demand/relation/role/save",
    method: "post",
    data: data,
  });
}
