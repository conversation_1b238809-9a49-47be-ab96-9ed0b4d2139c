import request from "@/utils/request";
//施工队
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/construct/manager/page",
      method: "post",
      data: data,
    });
  },
  // 绑定施工队负责人
  submitAccount(data) {
    return request({
      url: "/construct/manager/bindPrincipal",
      method: "post",
      data: data,
    });
  },
  //解绑施工负责人
  submitUnbindAccount(data) {
    return request({
      url: "/construct/manager/unbindPrincipal",
      method: "post",
      data: data,
    });
  },
  //绑定施工队项目经理
  submitManager(data) {
    return request({
      url: "/construct/manager/bindProjectManager",
      method: "post",
      data: data,
    });
  },
  //全部解绑项目经理
  unbindAllManager(data) {
    return request({
      url: "/construct/manager/unbindAll",
      method: "post",
      data: data,
    });
  },
  //查询已绑定项目经理
  bindManagerList(data) {
    return request({
      url: "/construct/manager/listBindManager",
      method: "get",
      params: data,
    });
  },
  //编辑状态
  update(query) {
    return request({
      url: "/construct/manager/save",
      method: "post",
      data: query,
    });
  },
  // 查询角色账号列表
  listRoleAccount(query) {
    return request({
      url: "/construct/manager/listProjectManager",
      method: "get",
      params: query,
    });
  },

  //以下本期不做-2407
  // 删除
  deleteData(query) {
    return request({
      url: "/construct/manager/del",
      method: "get",
      params: query,
    });
  },
  // 查询施工队信息
  queryBuildTeamById(data) {
    return request({
      url: "/construct/manager/detail",
      method: "get",
      params: data,
    });
  },
  // 修改施工队
  updateBuildTeam(data) {
    return request({
      url: "/construct/manager/save",
      method: "post",
      data: data,
    });
  },
  // 查询采购单号列表
  queryApplyNoList(data) {
    return request({
      url: "/construct/manager/listApplyNo",
      method: "get",
      params: data,
    });
  },
};
