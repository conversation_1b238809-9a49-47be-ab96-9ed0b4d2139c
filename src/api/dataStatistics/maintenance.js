import request from "@/utils/request";

// 数据统计
//运维工单数据概览
export function queryOrderData(data) {
  return request({
    url: "/statistics/orderData",
    method: "post",
    data: data,
  });
}
//工单完成趋势
export function queryHandleTend(data) {
  return request({
    url: "/statistics/tendency",
    method: "post",
    data: data,
  });
} //工单来源
export function queryOrderChannel(data) {
  return request({
    url: "/statistics/orderChannel",
    method: "post",
    data: data,
  });
}
// 能投大区工单占比;
export function queryOrgOrder(data) {
  return request({
    url: "/statistics/orgOrder",
    method: "post",
    data: data,
  });
}
// 工单类型
export function queryOrderType(data) {
  return request({
    url: "/statistics/orderType",
    method: "post",
    data: data,
  });
}
// 故障类别
export function queryFaultType(data) {
  return request({
    url: "/statistics/faultType",
    method: "post",
    data: data,
  });
}
// 超时情况
export function queryTimeout(data) {
  return request({
    url: "/statistics/timeout",
    method: "post",
    data: data,
  });
}
// 紧急程度
export function queryUrgencyLevel(data) {
  return request({
    url: "/statistics/urgencyLevel",
    method: "post",
    data: data,
  });
}
//站点工单数排名
export function queryStationOrder(data) {
  return request({
    url: "/statistics/stationOrder",
    method: "post",
    data: data,
  });
}
