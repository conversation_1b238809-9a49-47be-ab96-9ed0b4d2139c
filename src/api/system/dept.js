import request from "@/utils/request";

// 查询部门列表
export function listDept(query) {
  return request({
    url: "/system/dept/list",
    method: "get",
    params: query,
  });
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: "/system/dept/getDeptById",
    method: "get",
    params: { deptId: deptId },
  });
}

// 查询部门下拉树结构
export function treeselect() {
  return request({
    url: "/system/dept/treeSelect",
    method: "get",
  });
}

// 查询部门下拉树结构
export function permsTree(query) {
  return request({
    url: "/system/dept/permsTree",
    method: "get",
    params: {
      perms: query,
    },
  });
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: "/system/dept/roleDeptTreeSelect/" + roleId,
    method: "get",
  });
}

// 新增部门
export function addDept(data) {
  return request({
    url: "/system/dept/add",
    method: "post",
    data: data,
  });
}

// 修改部门
export function updateDept(data) {
  return request({
    url: "/system/dept/update",
    method: "post",
    data: data,
  });
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: "/system/dept/remove/" + deptId,
    method: "post",
  });
}

export function deptTreeSelect(data) {
  return request({
    url: "/system/dept/deptTreeSelect",
    method: "get",
    params: data,
  });
}
export function deptAllTreeSelect(data) {
  return request({
    url: "dept/all/Treelist",
    method: "get",
    params: data,
  });
}

export function permsTreeSelect(data) {
  return request({
    url: "/system/dept/permsTreeSelect",
    method: "get",
    params: { isOpenPrivilege: data },
  });
}

// 根据orgNo获取部门信息
export function queryDeptByOrgNo(query) {
  return request({
    url: "/system/dept/queryDeptByOrgNo",
    method: "get",
    params: {
      orgNo: query,
    },
  });
}
