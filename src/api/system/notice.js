import request from "@/utils/request";

/**
 * 查找网厅公告
 * @param query
 * @returns {AxiosPromise}
 */
export function queryNotice(query) {
    return request({
        url: "/system/notice/queryNotice",
        method: "get",
        params: query
    });
}

/**
 * 保存网厅公告
 * @param data
 * @returns {AxiosPromise}
 */
export function saveNotice(data) {
    return request({
        url: "/system/notice/addNotice",
        method: "post",
        params: data
    });
}

/**
 * 更新网厅公告
 * @param data
 * @returns {AxiosPromise}
 */
export function updateNotice(data) {
    return request({
        url: "/system/notice/updateNotice",
        method: "put",
        params: data
    });
}

/**
 * 删除网厅公告
 * @param data
 * @returns {AxiosPromise}
 */
export function deleteNotice(data) {
    return request({
        url: "/system/notice/deleteNotice",
        method: "delete",
        params: data
    });
}

/**
 * 判断网厅公告唯一性
 * @param data
 * @returns {AxiosPromise}
 */
export function findNotice(data) {
    return request({
        url: "/system/notice/findNotice",
        method: "get",
        params: data
    });
}
