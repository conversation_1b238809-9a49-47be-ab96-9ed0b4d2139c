import request from "@/utils/request";

// 查询标签列表
export function getTagList(data) {
  return request({
    url: "/tag/list",
    method: "post",
    data: data,
  });
}

// 新增/编辑标签
export function saveTag(data) {
  return request({
    url: "/tag/save",
    method: "post",
    data: data,
  });
}

// 删除标签
export function removeTag(data) {
  return request({
    url: "/tag/remove",
    method: "get",
    params: data,
  });
}

// 导出标签
export function exportTag(data) {
  return request({
    url: "/tag/export",
    method: "post",
    data: data,
  });
}

// 获取标签分类列表
export function getTagTypeList(data) {
  return request({
    url: "/tag/tagType",
    method: "get",
    params: data,
  });
}

// 获取标签分类列表（自定义维护）
export function listTagCategory(data) {
  return request({
    url: "/tag/category/list",
    method: "get",
    params: data,
  });
}
