import request from "@/utils/request";

export function getCurrentData(query) {
  return request({
    url: "/index/currentData",
    method: "get",
    params: query
  });
}

export function getDayPayData(query) {
  return request({
    url: "/index/dayPayData",
    method: "get",
    params: query
  });
}

export function getMonthPayData(query) {
  return request({
    url: "/index/monthPayData",
    method: "get",
    params: query
  });
}

export function getPayModelData(query) {
  return request({
    url: "/index/payModeData",
    method: "get",
    params: query
  });
}

export function getSalesData(query) {
  return request({
    url: "/index/salesData",
    method: "get",
    params: query
  });
}

export function getUserData(query) {
  return request({
    url: "/index/userData",
    method: "get",
    params: query
  });
}

export function  getFundData(query){
  return request({
    url: "/index/fundData",
    method: "get",
    params: query
  });
}

export function  getReportData(query){
  return request({
    url: "/index/getReportData",
    method: "get",
    params: query
  });
}


export function  usageData(query){
  return request({
    url: "/index/usageData",
    method: "get",
    params: query
  });
}

export function getPasswordChangeDate() {
  return request({
    url: "/index/getPasswordChangeDate",
    method: "get"
  });
}


export function getRevenueData() {
  return request({
    url: "/index/getRevenueData",
    method: "get"
  });
}


export function getMeterReadingData() {
  return request({
    url: "/index/getMeterReadingData",
    method: "get"
  });
}


export function getChargeWayData() {
  return request({
    url: "/index/getChargeWayData",
    method: "get"
  });
}


export function getRevenueStatistics() {
  return request({
    url: "/index/getRevenueStatistics",
    method: "get"
  });
}
