import request from '@/utils/request';

// 查询角色列表
export function listRole(query) {
  return request({
    url: '/system/role/list',
    method: 'get',
    params: query
  });
}

export function allList() {
  return request({
    url: '/system/role/allList',
    method: 'get',
  });
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: '/system/role/' + roleId,
    method: 'get'
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/system/role',
    method: 'post',
    data: data
  });
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/system/role/updateRole',
    method: 'post',
    data: data
  });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status
  };
  return request({
    url: '/system/role/changeStatus',
    method: 'post',
    data: data
  });
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/system/role/deleteRoleIds',
    method: 'post',
    data: roleId
  });
}

// 导出角色
export function exportRole(query) {
  return request({
    url: '/system/role/export',
    method: 'get',
    params: query
  });
}

export function getUserRole() {
  return request({
    url: '/system/role/getUserRole',
    method: 'get',
  });
}
