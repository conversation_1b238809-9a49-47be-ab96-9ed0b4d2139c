import request from "@/utils/request";
//demo
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/lianxi/list",
      method: "post",
      data: data,
    });
  },
  // 新增
  add(data) {
    return request({
      url: "/lianxi/add",
      method: "post",
      data: data,
    });
  },
  //编辑
  update(data) {
    return request({
      url: "/lianxi/update",
      method: "post",
      data: data,
    });
  },
  //删除
  delete(data) {
    return request({
      url: "/lianxi/remove",
      method: "get",
      params: data,
    });
  },
};
