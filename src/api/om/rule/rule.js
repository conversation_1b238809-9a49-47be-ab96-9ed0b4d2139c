import request from "@/utils/request";

// 查询站点列表
export function queryRuleByPage(data) {
  return request({
    url: "/om/rule/queryRuleByPage",
    method: "get",
    params: data,
  });
}

export function queryStationRuleByPage(data) {
  return request({
    url: "/om/rule/queryStationRuleByPage",
    method: "get",
    params: data,
  });
}

export function saveRule(data) {
  return request({
    url: "/om/rule/saveRule",
    method: "post",
    data: data,
  });
}

export function editRule(data) {
  return request({
    url: "/om/rule/editRule",
    method: "post",
    data: data,
  });
}

export function editRuleEnabled(data) {
  return request({
    url: "/om/rule/editRuleEnabled",
    method: "post",
    data: data,
  });
}

export function getStationRule(data) {
  return request({
    url: "/om/rule/getStationTreeRule",
    method: "get",
    params: data,
  });
}

export function saveStationRule(data) {
  return request({
    url: "/om/rule/saveStationRule",
    method: "post",
    data: data,
  });
}

export function unbind(data) {
  return request({
    url: "/om/rule/unbind",
    method: "post",
    data: data,
  });
}

export function coverStationRule(data) {
  return request({
    url: "/om/rule/coverStationRule",
    method: "post",
    data: data,
  });
}
export function queryTaskByPage(data) {
  return request({
    url: "/om/task/queryTaskByPage",
    method: "get",
    params: data,
  });
}
export function queryTaskDetail(data) {
  return request({
    url: "/om/task/queryTaskDetail",
    method: "get",
    params: data,
  });
}

export function transfer(data) {
  return request({
    url: "/om/task/transfer",
    method: "post",
    data: data,
  });
}

export function exportOmData(data) {
  return request({
    url: "/om/task/export",
    method: "post",
    data: data,
    responseType: "blob",
  });
}
