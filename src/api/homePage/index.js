//门户首页
import request from "@/utils/request";
//卡片选中回显
export function queryUserBindBoards(query) {
    return request({
        url: "/home/<USER>/queryUserBindBoards",
        method: "post",
        data: query
    });
}

//卡片保存
export function batchAddBindBoards(query) {
  return request({
      url: "/home/<USER>/batchAddBindBoards",
      method: "post",
      data: query
  });
}

// 获取本月营收统计
export function getRevenueByDate(query) {
  return request({
      url: "/home/<USER>/getRevenueByDate",
      method: "get",
      params: query
  });
}
//获取本月营收统计-自然年
export function getRevenueByYm(query) {
  return request({
      url: "/home/<USER>/getRevenueByYm",
      method: "get",
      params: query
  });
}

// 获取本年抄收统计 -账期
export function getYearFeeRecycle(query) {
  return request({
      url: "/home/<USER>/getYearFeeRecycle",
      method: "get",
      params: query
  });
}

// 水费回收本年
export function getFeeRecycleYm(query) {
  return request({
      url: "/home/<USER>/getFeeRecycleYm",
      method: "get",
      params: query
  });
}

//小卡片----
// 获取今日营收
export function getTodayRevenue(query) {
  return request({
      url: "/home/<USER>/getTodayRevenue",
      method: "get",
      params: query
  });
}

// 水费回收 -账期
export function getMonthFeeRecycle(query) {
  return request({
      url: "/home/<USER>/getMonthFeeRecycle",
      method: "get",
      params: query
  });
}

// 本月累计预存
export function getMonthPrepaySale(query) {
  return request({
      url: "/home/<USER>/getMonthPrepaySale",
      method: "get",
      params: query
  });
}

// 本月售水
export function getMonthSaleWater(query) {
  return request({
      url: "/home/<USER>/getMonthSaleWater",
      method: "get",
      params: query
  });
}

// 本月抄表
export function getMonthMeterReadSale(query) {
  return request({
      url: "/home/<USER>/getMonthMeterReadSale",
      method: "get",
      params: query
  });
}

// 本月工单统计
export function getMonthWorkFlow(query) {
  return request({
      url: "/home/<USER>/getMonthWorkFlow",
      method: "get",
      params: query
  });
}

// 刷新数据看板
export function refreshBoardData(query) {
  return request({
      url: "/home/<USER>/refreshBoardData",
      method: "get",
      params: query,
      timeout: 20000
  });
}

// 刷新数据看板是否成功
export function refreshBoardDataOk(query) {
  return request({
      url: "/home/<USER>/refreshBoardDataOk",
      method: "get",
      params: query
  });
}

// 首页数量
export function queryMainPageCount(query) {
  return request({
    url: "/mainPage/queryMainPageCount",
    method: "post",
    data: query
  });
}

// 代办流程列表接口
export function queryWaitApproveList(query) {
  return request({
    url: "/mainPage/queryWaitApproveList",
    method: "post",
    data: query
  });
}

// 公告列表接口
export function getBoardArticle(query) {
  return request({
      url: "/home/<USER>/getBoardArticle",
      method: "get",
      params: query
  });
}

// 标记文章已读
export function queryTimeOutList(query) {
  return request({
    url: "/mainPage/queryTimeOutList",
    method: "post",
    data: query
  });
}

// ---------------------------------------------

// 年度项目类型占比
export function projectTypeStatistics(query) {
  return request({
      url: "/project/projectTypeStatistics",
      method: "post",
      data: query
  });
}

// 年度项目完工情况
export function projectCompleteStatistics(query) {
  return request({
      url: "/project/projectCompleteStatistics",
      method: "post",
      data: query
  });
}

// 年度项目完成统计
export function projectStatisticsByMonth(query) {
  return request({
      url: "/project/projectStatisticsByMonth",
      method: "post",
      data: query
  });
}

// 我的关注
export function focusListPage(query) {
  return request({
      url: "/focus/listPage",
      method: "get",
      params: query
  });
}

// 取消关注、关注
export function focusOrNot(query) {
  return request({
      url: "/focus/focusOrNot",
      method: "get",
      params: query
  });
}