import request from "@/utils/request";

// 查询用户列表
export function listUser(query) {
  return request({
    url: "/common/user/list",
    method: "post",
    data: query,
  });
}
export function listAllUser(query) {
  return request({
    url: "/common/all/user",
    method: "post",
    data: query,
  });
}
// 查询部门列表
export function listDept(query) {
  return request({
    url: "/common/dept/list",
    method: "post",
    data: query,
  });
}
//下载（根据url转blob下载）
export function downLoadUrl2Blob(data) {
  return request({
    url: "/upload/getFileStream",
    method: "get",
    params: data,
    responseType: "blob",
    needFileName: true,
  });
}
// 查询省市树
export function queryCityTree(query) {
  return request({
    url: "user/permission/city/getAllCityTreeList",
    method: "get",
    params: query,
  });
}
// 根据主键id 查看日志
export function queryLog(data) {
  return request({
    url: "/orderRecord/getByBusinessId",
    method: "get",
    params: data,
  });
}

// 查询自定义列偏好设置
export function queryCustomColumn(query) {
  return request({
    url: "/ledger/order/queryPreference",
    method: "get",
    params: query,
  });
}

// 保存自定义列偏好设置
export function saveCustomColumn(query) {
  return request({
    url: "/ledger/order/savePreference",
    method: "post",
    data: query,
  });
}

// 删除自定义列偏好设置
export function cancelCustomColumn(query) {
  return request({
    url: "/ledger/order/removePreference",
    method: "get",
    params: query,
  });
}
