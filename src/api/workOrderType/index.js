import request from "@/utils/request";

// 工单类型接口
//获取树结构
export function queryTreeList(data) {
  return request({
    url: "/orderType/list",
    method: "get",
    params: data,
  });
}
//删除
export function deleteType(data) {
  return request({
    url: "/orderType/remove",
    method: "post",
    data: data,
  });
}
//新增
export function addType(data) {
  return request({
    url: "/orderType/save",
    method: "post",
    data: data,
  });
}
//修改
export function editType(data) {
  return request({
    url: "/orderType/update",
    method: "post",
    data: data,
  });
}
//保存配置检查项
export function saveCheckItems(data) {
  return request({
    url: "/orderType/configItem",
    method: "post",
    data: data,
  });
}
//切换自定义类型
export function customChange(data) {
  return request({
    url: "/orderType/changeCustomize",
    method: "post",
    data: data,
  });
}
//获取已配置检查项
export function getConfiguredItem(data) {
  return request({
    url: "/orderType/configuredItemIds",
    method: "get",
    params: data,
  });
}
