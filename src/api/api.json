[{"index": 0, "name": "标签信息", "desc": "标签信息", "add_time": 1746586467, "up_time": 1746586467, "list": [{"query_path": {"path": "/tag/list", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 144498, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"tagAttributeName\":{\"type\":\"string\",\"description\":\"标签属性名称\"},\"tagType\":{\"type\":\"string\",\"description\":\"标签分类\"},\"tagName\":{\"type\":\"string\",\"description\":\"标签名称\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "分页查询", "path": "/tag/list", "catid": 19513, "markdown": "", "req_headers": [{"required": "1", "_id": "681acb64e3889084ff285943", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"tagId\":{\"type\":\"integer\",\"description\":\"主键\"},\"tagAttributeCode\":{\"type\":\"integer\",\"description\":\"标签属性字典编码\"},\"tagAttributeValue\":{\"type\":\"string\",\"description\":\"标签属性字典键值\"},\"tagAttributeName\":{\"type\":\"string\",\"description\":\"标签属性名称\"},\"tagType\":{\"type\":\"string\",\"description\":\"标签分类\"},\"tagName\":{\"type\":\"string\",\"description\":\"标签名称\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"createBy\":{\"type\":\"integer\",\"description\":\"创建人\"},\"createByName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}}}},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"},\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"total\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "<p></p>", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1746586468, "up_time": 1746586468, "__v": 0}, {"query_path": {"path": "/tag/remove", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 144510, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "删除", "path": "/tag/remove", "catid": 19513, "markdown": "", "req_headers": [], "req_query": [{"required": "1", "_id": "681acb64e388903f90285945", "name": "tagId", "value": "0", "desc": ""}], "desc": "<p></p>", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1746586468, "up_time": 1746586468, "req_body_form": [], "__v": 0}, {"query_path": {"path": "/tag/export", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 144522, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"pageNum\":{\"type\":\"integer\",\"description\":\"\"},\"pageSize\":{\"type\":\"integer\",\"description\":\"\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"\"},\"orgNo\":{\"type\":\"integer\",\"description\":\"\"},\"orgNoList\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"},\"operatorName\":{\"type\":\"string\",\"description\":\"\"},\"tagAttributeName\":{\"type\":\"string\",\"description\":\"标签属性名称\"},\"tagType\":{\"type\":\"string\",\"description\":\"标签分类\"},\"tagName\":{\"type\":\"string\",\"description\":\"标签名称\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "导出", "path": "/tag/export", "catid": 19513, "markdown": "", "req_headers": [{"required": "1", "_id": "681acb64e388908b98285947", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"string\",\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "<p></p>", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1746586468, "up_time": 1746586468, "__v": 0}, {"query_path": {"path": "/tag/save", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": true, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 144504, "req_body_type": "json", "res_body_type": "json", "req_body_other": "{\"type\":\"object\",\"properties\":{\"tagId\":{\"type\":\"integer\",\"description\":\"主键\"},\"tagAttributeCode\":{\"type\":\"integer\",\"description\":\"标签属性字典编码\"},\"tagAttributeValue\":{\"type\":\"string\",\"description\":\"标签属性字典键值\"},\"tagAttributeName\":{\"type\":\"string\",\"description\":\"标签属性名称\"},\"tagType\":{\"type\":\"string\",\"description\":\"标签分类\"},\"tagName\":{\"type\":\"string\",\"description\":\"标签名称\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"createBy\":{\"type\":\"integer\",\"description\":\"创建人\"},\"createByName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}},\"tagList\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"tagId\":{\"type\":\"integer\",\"description\":\"主键\"},\"tagAttributeCode\":{\"type\":\"integer\",\"description\":\"标签属性字典编码\"},\"tagAttributeValue\":{\"type\":\"string\",\"description\":\"标签属性字典键值\"},\"tagAttributeName\":{\"type\":\"string\",\"description\":\"标签属性名称\"},\"tagType\":{\"type\":\"string\",\"description\":\"标签分类\"},\"tagName\":{\"type\":\"string\",\"description\":\"标签名称\"},\"tenantId\":{\"type\":\"integer\",\"description\":\"租户号\"},\"createBy\":{\"type\":\"integer\",\"description\":\"创建人\"},\"createByName\":{\"type\":\"string\",\"description\":\"创建人名称\"},\"createTime\":{\"type\":\"string\",\"description\":\"创建时间\",\"mock\":{\"mock\":\"@datetime\"}},\"updateTime\":{\"type\":\"string\",\"description\":\"更新时间\",\"mock\":{\"mock\":\"@datetime\"}}}},\"description\":\"\"},\"operatorId\":{\"type\":\"integer\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "title": "新增/编辑", "path": "/tag/save", "catid": 19513, "markdown": "", "req_headers": [{"required": "1", "_id": "681acb64e38890517c285944", "name": "Content-Type", "value": "application/json", "example": "application/json"}], "req_query": [], "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"description\":\"\",\"type\":\"null\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "POST", "req_body_form": [], "desc": "<p></p>", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1746586468, "up_time": 1746586468, "__v": 0}, {"query_path": {"path": "/tag/tagType", "params": []}, "edit_uid": 0, "status": "done", "type": "static", "req_body_is_json_schema": false, "res_body_is_json_schema": true, "api_opened": false, "index": 0, "tag": [], "_id": 144516, "res_body": "{\"type\":\"object\",\"properties\":{\"success\":{\"type\":\"boolean\",\"description\":\"\"},\"code\":{\"type\":\"string\",\"description\":\"\"},\"message\":{\"type\":\"string\",\"description\":\"\"},\"data\":{\"type\":\"array\",\"items\":{\"type\":\"string\"},\"description\":\"\"},\"traceId\":{\"type\":\"string\",\"description\":\"\"}},\"$schema\":\"http://json-schema.org/draft-04/schema#\"}", "method": "GET", "res_body_type": "json", "title": "标签分类列表", "path": "/tag/tagType", "catid": 19513, "markdown": "", "req_headers": [], "req_query": [{"required": "0", "_id": "681acb64e388906327285946", "name": "name", "value": "", "desc": ""}], "desc": "<p></p>", "project_id": 1124, "req_params": [], "uid": 1665, "add_time": 1746586468, "up_time": 1746586468, "req_body_form": [], "__v": 0}]}]