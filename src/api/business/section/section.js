import request from "@/utils/request";

// 查询列表-抄表册list
export function list(data) {
  return request({
    url: "/crm/section/list",
    method: "post",
    data: data
  });
}
// 
export function userList(data) {
  return request({
    url: "/crm/section/getMrSectInfo",
    method: "post",
    data: data
  });
}
// 抄表册list用于下拉组件
export function sectionList(data) {
  return request({
    url: "/crm/section/listAll",
    method: "post",
    data: data
  });
}

//抄表员list----另一处见src/api/components/index.js
export function sectUserList(data) {
  return request({
    url: "/crm/section/listSectReader",
    method: "post",
    data: data
  });
}

//获取抄表段下面用户
export function consList(query) {
  return request({
    url: "/crm/section/consList",
    method: "get",
    params: query
  });
}

// 修改用户抄表册
export function updatePremiseMrSectNo(data) {
  return request({
    url: "/crm/section/updatePremiseMrSectNo",
    method: "post",
    data: data
  });
}

// 更改抄表顺序
export function updateMrSn(data) {
  return request({
    url: "/crm/section/updateMrSn",
    method: "post",
    data: data
  });
}

// 更改手动输入的抄表顺序
export function updateHandImportMrSn(data) {
  return request({
    url: "/crm/section/updateHandImportMrSn",
    method: "post",
    data: data
  });
}

export function getMrSectNo() {
  return request({
    url: "/crm/section/getMrSectNo",
    method: "get",
    params:{}
  });
}

export function add(data) {
  return request({
    url: "/crm/section/add",
    method: "post",
    data: data
  });
}

export function update(data) {
  return request({
    url: "/crm/section/update",
    method: "post",
    data: data
  });
}

export function deleteItem(id) {
  return request({
    url: "/crm/section/delete/" + id,
    method: "get"
  });
}
//导出抄表册明细数据
export function exportData(query) {
  return request({
    url: "/crm/section/exportMrSectDetail",
    method: "post",
    responseType:'blob',
    data: query
  });
}

export function listPremise(query) {
  return request({
    url: "/crm/section/listPremise",
    method: "get",
    params: query
  });
}
