import request from "@/utils/request";

// 查询列表-抄表册list
export function list(data) {
  return request({
    url: "/crm/section/list",
    method: "post",
    data: data,
  });
}

export function businessList(query) {
  return request({
    url: "/business/list",
    method: "get",
    params: query,
  });
}
// 工单工作台（我创建的）列表查询
export function createBusinessList(query) {
  return request({
    url: "/business/create/list",
    method: "get",
    params: query,
  });
}
// 工单工作台（我转派的）列表查询
export function transferBusinessList(query) {
  return request({
    url: "/business/transfer/list",
    method: "get",
    params: query,
  });
}
export function allBusinessList(query) {
  return request({
    url: "/business/all/list",
    method: "get",
    params: query,
  });
}
export function handleBusinessList(query) {
  return request({
    url: "/business/handle/list",
    method: "get",
    params: query,
  });
}

export function getBusinessInfo(data) {
  return request({
    url: "/business/getBusinessInfo",
    method: "get",
    params: data,
  });
}

export function businessTransfer(data) {
  return request({
    url: "/business/transfer",
    method: "post",
    data: data,
  });
}

export function invalidFlow(data) {
  return request({
    url: "/business/invalid",
    method: "post",
    data: data,
  });
}

export function invalidAllFlow(data) {
  return request({
    url: "/business/invalidAllFlow",
    method: "post",
    data: data,
  });
}

export function saveApproveConfig(data) {
  return request({
    url: "/node/config/saveApproveConfig",
    method: "post",
    data: data,
  });
}
export function getFlowConfigs(data) {
  return request({
    url: "/node/config/getFlowConfigs",
    method: "post",
    data: data,
  });
}

//查询工单操作记录
export function logList(data) {
  return request({
    url: "/business/record",
    method: "get",
    params: data,
  });
}

// 导出
export function exportBusinessList(query) {
  return request({
    url: "/business/export",
    method: "post",
    data: query,
  });
}

//桩列表
export function pileList(query) {
  return request({
    url: "/business/pileList",
    method: "get",
    params: query,
  });
}
