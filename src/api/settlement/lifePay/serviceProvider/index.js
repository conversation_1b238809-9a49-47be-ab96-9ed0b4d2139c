import request from "@/utils/request";

/**
 * 服务商信息控制层API接口
 * 提供服务商信息的增删改查、导入导出等功能
 */
export default {
  /**
   * 分页查询服务商信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员姓名
   * @param {string} [data.serviceProvider] - 服务商名称（模糊查询）
   * @param {string} [data.mid] - 商户号（模糊查询）
   * @param {string} [data.pid] - 合作伙伴身份ID（模糊查询）
   * @param {string} [data.commissionStartTime] - 佣金开始时间（格式：YYYY-MM-DD）
   * @param {string} [data.commissionEndTime] - 佣金结束时间（格式：YYYY-MM-DD）
   * @param {string} [data.billingOrg] - 出账机构（模糊查询）
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 服务商信息列表
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await serviceProviderApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   serviceProvider: '支付宝'
   * });
   */
  list(data) {
    return request({
      url: "/st/lifePay/providerInfo/queryPage",
      method: "post",
      data: data
    });
  },

  /**
   * 导出服务商信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {number} [data.pageNum] - 页码
   * @param {number} [data.pageSize] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员姓名
   * @param {string} [data.serviceProvider] - 服务商名称（模糊查询）
   * @param {string} [data.mid] - 商户号（模糊查询）
   * @param {string} [data.pid] - 合作伙伴身份ID（模糊查询）
   * @param {string} [data.commissionStartTime] - 佣金开始时间（格式：YYYY-MM-DD）
   * @param {string} [data.commissionEndTime] - 佣金结束时间（格式：YYYY-MM-DD）
   * @param {string} [data.billingOrg] - 出账机构（模糊查询）
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有服务商信息
   * const result = await serviceProviderApi.export({
   *   serviceProvider: '支付宝'
   * });
   */
  export(data) {
    return request({
      url: "/st/lifePay/providerInfo/exportExcel",
      method: "post",
      data: data
    });
  },

  /**
   * 导入服务商信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await serviceProviderApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/lifePay/providerInfo/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data"
      },
      skipIntercept: true
    });
  },

  /**
   * 获取服务商信息相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.serviceProvider] - 服务商名称选项列表
   * @returns {Array<string>} [returns.data.billingOrg] - 出账机构选项列表
   * @returns {Array<string>} [returns.data.status] - 状态选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await serviceProviderApi.getDropLists();
   * // result.data = {
   * //   serviceProvider: ['支付宝', '微信支付', '银联'],
   * //   billingOrg: ['蚂蚁科技', '财付通', '银联商务'],
   * //   status: ['正常', '暂停', '停用']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/lifePay/providerInfo/getDropLists",
      method: "get"
    });
  },

  /**
   * 新增服务商信息
   * @param {Object} data - 服务商信息数据
   * @param {string} data.serviceProvider - 服务商名称（必填）
   * @param {string} [data.mid] - 商户号
   * @param {string} [data.pid] - 合作伙伴身份ID
   * @param {string} [data.commissionStartTime] - 佣金开始时间（格式：YYYY-MM-DD）
   * @param {string} [data.commissionEndTime] - 佣金结束时间（格式：YYYY-MM-DD）
   * @param {string} [data.billingOrg] - 出账机构
   * @param {string} [data.status] - 状态（正常/暂停/停用）
   * @param {string} [data.remark] - 备注
   * @returns {Promise<Object>} 返回新增结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string|number} returns.data - 新增记录的ID或操作结果
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增服务商信息
   * const result = await serviceProviderApi.add({
   *   serviceProvider: '支付宝（中国）网络技术有限公司',
   *   mid: '2088123456789012',
   *   pid: '2088123456789012',
   *   billingOrg: '蚂蚁科技',
   *   status: '正常'
   * });
   */
  add(data) {
    return request({
      url: "/st/lifePay/providerInfo/add",
      method: "post",
      data: data
    });
  },

  /**
   * 编辑服务商信息
   * @param {Object} data - 服务商信息数据
   * @param {number} data.id - 服务商信息ID（必填）
   * @param {string} [data.serviceProvider] - 服务商名称
   * @param {string} [data.mid] - 商户号
   * @param {string} [data.pid] - 合作伙伴身份ID
   * @param {string} [data.commissionStartTime] - 佣金开始时间（格式：YYYY-MM-DD）
   * @param {string} [data.commissionEndTime] - 佣金结束时间（格式：YYYY-MM-DD）
   * @param {string} [data.billingOrg] - 出账机构
   * @param {string} [data.status] - 状态（正常/暂停/停用）
   * @param {string} [data.remark] - 备注
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑服务商信息
   * const result = await serviceProviderApi.update({
   *   id: 1,
   *   serviceProvider: '支付宝（中国）网络技术有限公司',
   *   status: '暂停'
   * });
   */
  update(data) {
    return request({
      url: "/st/lifePay/providerInfo/edit",
      method: "post",
      data: data
    });
  },

  /**
   * 删除服务商信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的服务商信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个服务商信息
   * const result = await serviceProviderApi.delete({ id: 1 });
   *
   * // 批量删除服务商信息
   * const result = await serviceProviderApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/lifePay/providerInfo/delete",
      method: "post",
      data: data
    });
  }
};
