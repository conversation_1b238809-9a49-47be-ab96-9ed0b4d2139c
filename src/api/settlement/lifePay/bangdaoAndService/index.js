import request from "@/utils/request";

// 邦道与服务商账单
export default {
  // 分页查询
  list(data) {
    return request({
      url: "/st/lifePay/bdProvider/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出Excel
  export(data) {
    return request({
      url: "/st/lifePay/bdProvider/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 导入Excel
  import(data) {
    return request({
      url: "/st/lifePay/bdProvider/importExcel",
      method: "post",
      data: data,
    });
  },
  // 批量标记结算状态
  batchMark(data) {
    return request({
      url: "/st/lifePay/bdProvider/batchMark",
      method: "post",
      data: data,
    });
  },
  // 获取下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/bdProvider/getDropLists",
      method: "get",
    });
  },
  // 修改结算状态
  editStatus(data) {
    return request({
      url: "/st/lifePay/bdProvider/batchMark",
      method: "post",
      data: data,
    });
  },
};
