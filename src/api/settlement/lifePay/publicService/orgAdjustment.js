import request from "@/utils/request";

// 公服机构调账控制层
export default {
  // 分页查询
  list(data) {
    return request({
      url: "/st/lifePay/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出Excel
  export(data) {
    return request({
      url: "/st/lifePay/exportExcel",
      method: "post",
      data: data,
    });
  },

  // 获取下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/getDropLists",
      method: "get",
    });
  },
};
