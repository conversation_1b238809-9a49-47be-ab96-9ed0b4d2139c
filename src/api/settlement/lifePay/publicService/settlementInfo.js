import request from "@/utils/request";

// 公服结算信息控制层
export default {
  // 分页查询结算信息
  list(data) {
    return request({
      url: "/st/lifePay/public/settle/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出结算信息Excel
  export(data) {
    return request({
      url: "/st/lifePay/public/settle/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 获取结算信息下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/public/settle/getDropLists",
      method: "get",
    });
  },
};
