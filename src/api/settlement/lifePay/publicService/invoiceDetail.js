import request from "@/utils/request";

// 公服发票信息管理控制层
export default {
  // 分页查询发票信息
  list(data) {
    return request({
      url: "/st/lifePay/public/invoice/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出发票信息Excel
  export(data) {
    return request({
      url: "/st/lifePay/public/invoice/exportExcel",
      method: "post",
      data: data,
    });
  },

  // 获取发票信息下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/public/invoice/getDropLists",
      method: "get",
    });
  },
};
