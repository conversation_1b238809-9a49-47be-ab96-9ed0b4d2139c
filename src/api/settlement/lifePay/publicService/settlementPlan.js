import request from "@/utils/request";

// 公服结算计划管理控制层
export default {
  // 分页查询结算计划
  list(data) {
    return request({
      url: "/st/lifePay/public/settlePlan/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出结算计划Excel
  export(data) {
    return request({
      url: "/st/lifePay/public/settlePlan/exportExcel",
      method: "post",
      data: data,
    });
  },

  // 获取结算计划下拉列表
  getDropLists() {
    return request({
      url: "/st/lifePay/public/settlePlan/getDropLists",
      method: "get",
    });
  },
};
