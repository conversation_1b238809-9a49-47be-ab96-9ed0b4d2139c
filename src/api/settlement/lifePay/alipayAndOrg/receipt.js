import request from "@/utils/request";

// 支付宝与机构账单-收据单
export default {
  // 列表查询
  list(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 编辑
  update(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/edit",
      method: "post",
      data: data,
    });
  },
  // 发送邮件
  sendMail(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/sendEmail",
      method: "post",
      data: data,
    });
  },
  // 查看收据函
  preview(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/preview",
      method: "post",
      data: data,
    });
  },
  // 下载
  download(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/download",
      method: "post",
      data: data,
    });
  },
  // 日志
  log(data) {
    return request({
      url: "/st/lifePay/aliPayOrg/receipt/log",
      method: "post",
      data: data,
    });
  },

  // 获取下拉列表数据 - 用于筛选条件的下拉选项
  getDropLists() {
    return request({
      url: "/st/lifePay/aliPayOrg/getDropLists",
      method: "get",
    });
  },
};
