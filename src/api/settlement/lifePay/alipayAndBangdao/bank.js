import request from "@/utils/request";

// 支付宝与邦道账单-银行流水
export default {
  // 分页查询
  list(data) {
    return request({
      url: "/st/lifePay/bankFlow/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出Excel
  export(data) {
    return request({
      url: "/st/lifePay/bankFlow/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 导入Excel
  batchImport(data) {
    return request({
      url: "/st/lifePay/bankFlow/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },
  // 获取下拉列表
  getDropLists(data) {
    return request({
      url: "/st/lifePay/bankFlow/getDropLists",
      method: "get",
      params: data,
    });
  },
};
