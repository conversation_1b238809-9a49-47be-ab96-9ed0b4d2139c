import request from "@/utils/request";

// 支付宝与邦道账单-支付宝明细账单
export default {
  // 分页查询
  list(data) {
    return request({
      url: "/st/lifePay/alipayBd/queryPage",
      method: "post",
      data: data,
    });
  },
  // 导出Excel
  export(data) {
    return request({
      url: "/st/lifePay/alipayBd/exportExcel",
      method: "post",
      data: data,
    });
  },
  // 导入Excel
  batchImport(data) {
    return request({
      url: "/st/lifePay/alipayBd/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },
  // 与银行流水核对
  checkWithBank(data) {
    return request({
      url: "/st/lifePay/alipayBd/checkWithBank",
      method: "post",
      data: data,
    });
  },
  // 获取下拉列表
  getDropLists(data) {
    return request({
      url: "/st/lifePay/alipayBd/getDropLists",
      method: "get",
      params: data,
    });
  },
};
