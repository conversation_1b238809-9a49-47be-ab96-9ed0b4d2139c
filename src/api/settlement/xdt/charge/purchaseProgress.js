import request from "@/utils/request";

/**
 * 新电途充电购电进度管理API接口
 * 提供购电进度信息的增删改查功能
 */
export default {
  /**
   * 分页查询购电进度信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.operator] - 运营商（模糊查询）
   * @param {string} [data.counterpartyConfirmation] - 对方确认状态
   * @param {string} [data.ourSeal] - 我方盖章状态
   * @param {string} [data.billYearMonthStart] - 账单年月开始
   * @param {string} [data.billYearMonthEnd] - 账单年月结束
   * @param {string} [data.billCheck] - 账单核对状态
   * @param {string} [data.returnComplete] - 退回完成状态
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 购电进度信息列表
   * @returns {number} returns.data[].id - 记录ID
   * @returns {string} returns.data[].billYearMonth - 账单年月
   * @returns {string} returns.data[].operator - 运营商
   * @returns {number} returns.data[].returnAmount - 退回金额
   * @returns {string} returns.data[].reconciliationPerson - 对账人
   * @returns {string} returns.data[].billCheck - 账单核对
   * @returns {string} returns.data[].counterpartyConfirmation - 对方确认
   * @returns {string} returns.data[].counterpartySeal - 对方盖章
   * @returns {string} returns.data[].ourSeal - 我方盖章
   * @returns {string} returns.data[].returnComplete - 退回完成
   * @returns {string} returns.data[].remarks - 备注
   * @returns {string} returns.data[].mode - 模式
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateBy - 更新人
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await purchaseProgressApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   operator: '运营商名称'
   * });
   */
  list(data) {
    return request({
      url: "/st/newCharge/power/queryPage",
      method: "post",
      data: data,
    });
  },

  /**
   * 编辑购电进度信息
   * @param {Object} data - 购电进度信息数据
   * @param {number} data.id - 购电进度信息ID（必填）
   * @param {string} [data.billYearMonth] - 账单年月
   * @param {string} [data.operator] - 运营商
   * @param {number} [data.returnAmount] - 退回金额
   * @param {string} [data.reconciliationPerson] - 对账人
   * @param {string} [data.billCheck] - 账单核对
   * @param {string} [data.counterpartyConfirmation] - 对方确认
   * @param {string} [data.counterpartySeal] - 对方盖章
   * @param {string} [data.ourSeal] - 我方盖章
   * @param {string} [data.returnComplete] - 退回完成
   * @param {string} [data.remarks] - 备注
   * @param {string} [data.mode] - 模式
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑购电进度信息
   * const result = await purchaseProgressApi.update({
   *   id: 1,
   *   returnAmount: 950.00,
   *   billCheck: '已核对'
   * });
   */
  update(data) {
    return request({
      url: "/st/newCharge/power/edit",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除购电进度信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的购电进度信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个购电进度信息
   * const result = await purchaseProgressApi.delete({ id: 1 });
   *
   * // 批量删除购电进度信息
   * const result = await purchaseProgressApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/newCharge/power/delete",
      method: "get",
      params: data,
    });
  },

  /**
   * 导出购电进度信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.operator] - 运营商筛选条件
   * @param {string} [data.counterpartyConfirmation] - 对方确认状态筛选条件
   * @param {string} [data.ourSeal] - 我方盖章状态筛选条件
   * @param {string} [data.billYearMonthStart] - 账单年月开始筛选条件
   * @param {string} [data.billYearMonthEnd] - 账单年月结束筛选条件
   * @param {string} [data.billCheck] - 账单核对状态筛选条件
   * @param {string} [data.returnComplete] - 退回完成状态筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有购电进度信息
   * const result = await purchaseProgressApi.export({
   *   operator: '运营商名称'
   * });
   */
  export(data) {
    return request({
      url: "/st/newCharge/power/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 导入购电进度信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await purchaseProgressApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/newCharge/power/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  /**
   * 新增购电进度信息
   * @param {Object} data - 购电进度信息数据
   * @param {string} data.billYearMonth - 账单年月（必填）
   * @param {string} data.operator - 运营商（必填）
   * @param {number} [data.returnAmount] - 退回金额
   * @param {string} [data.reconciliationPerson] - 对账人
   * @param {string} [data.billCheck] - 账单核对
   * @param {string} [data.counterpartyConfirmation] - 对方确认
   * @param {string} [data.counterpartySeal] - 对方盖章
   * @param {string} [data.ourSeal] - 我方盖章
   * @param {string} [data.returnComplete] - 退回完成
   * @param {string} [data.remarks] - 备注
   * @param {string} [data.mode] - 模式
   * @returns {Promise<Object>} 返回新增结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增购电进度信息
   * const result = await purchaseProgressApi.add({
   *   billYearMonth: '2024-01',
   *   operator: '运营商名称',
   *   returnAmount: 1000.00
   * });
   */
  add(data) {
    return request({
      url: "/st/newCharge/power/add",
      method: "post",
      data: data,
    });
  },

  /**
   * 获取购电进度相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.operators] - 运营商选项列表
   * @returns {Array<string>} [returns.data.reconciliationPersons] - 对账人选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await purchaseProgressApi.getDropLists();
   * // result.data = {
   * //   operators: ['运营商A', '运营商B'],
   * //   reconciliationPersons: ['张三', '李四']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/newCharge/power/getDropLists",
      method: "get",
    });
  },

  /**
   * 批量操作购电进度信息
   * @param {Object} data - 批量操作参数
   * @param {Array<number>} data.idList - 要操作的购电进度信息ID列表（必填）
   * @param {string} [data.selectPage] - 选择页面类型：'1'-当前页，'2'-全部页
   * @param {string} [data.billCheck] - 账单核对状态
   * @param {string} [data.counterpartyConfirmation] - 对方确认状态
   * @param {string} [data.counterpartySeal] - 对方盖章状态
   * @param {string} [data.ourSeal] - 我方盖章状态
   * @param {string} [data.returnComplete] - 退回完成状态
   * @param {string} [data.remarks] - 备注
   * @returns {Promise<Object>} 返回批量操作结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 批量更新购电进度信息
   * const result = await purchaseProgressApi.batchOperation({
   *   idList: [1, 2, 3],
   *   billCheck: '已核对',
   *   selectPage: '1'
   * });
   */
  batchOperation(data) {
    return request({
      url: "/st/newCharge/power/batchOperation",
      method: "post",
      data: data,
    });
  },
};
