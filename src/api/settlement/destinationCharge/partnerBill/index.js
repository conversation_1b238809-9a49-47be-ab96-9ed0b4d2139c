import request from "@/utils/request";

/**
 * 合伙人账单管理API接口
 * 提供合伙人账单信息的增删改查功能
 */
export default {
  /**
   * 分页查询合伙人账单信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.salesContractCode] - 销售合同编码（模糊查询）
   * @param {string} [data.feeType] - 费用类型（字典：fee_type）
   * @param {string} [data.settlementStatus] - 结算状态（字典：settlement_status）
   * @param {string} [data.startBillingPeriod] - 账单周期开始时间
   * @param {string} [data.endBillingPeriod] - 账单周期结束时间
   * @param {string} [data.customerName] - 客户名称（模糊查询）
   * @param {string} [data.serviceInvoiceCategory] - 平台服务费开票类目（字典：service_invoice_category）
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 合伙人账单信息列表
   * @returns {number} returns.data[].partnerBillId - 主键ID
   * @returns {string} returns.data[].salesContractCode - 销售合同编码
   * @returns {string} returns.data[].customerName - 客户名称
   * @returns {string} returns.data[].billingPeriod - 账单周期
   * @returns {number} returns.data[].totalPeriods - 总期数
   * @returns {number} returns.data[].paymentPeriods - 支付期数
   * @returns {number} returns.data[].gunCount - 枪数
   * @returns {number} returns.data[].unitPrice - 单价（元/枪）
   * @returns {number} returns.data[].amount - 金额（元）
   * @returns {string} returns.data[].feeType - 费用类型（字典：fee_type）
   * @returns {string} returns.data[].feeTypeName - 费用类型名称
   * @returns {string} returns.data[].settlementStatus - 结算状态（字典：settlement_status）
   * @returns {string} returns.data[].settlementStatusName - 结算状态名称
   * @returns {string} returns.data[].serviceInvoiceCategory - 平台服务费开票类目（字典：service_invoice_category）
   * @returns {string} returns.data[].serviceInvoiceCategoryName - 平台服务费开票类目名称
   * @returns {string} returns.data[].serviceInvoiceRate - 平台服务费开票税率（字典：service_invoice_rate）
   * @returns {string} returns.data[].serviceInvoiceRateName - 平台服务费开票税率名称
   * @returns {string} returns.data[].remarks - 备注
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.data[].tenantId - 租户ID
   * @returns {number} returns.data[].orgNo - 机构编号
   * @returns {number} returns.data[].operatorId - 操作员ID
   * @returns {number} returns.data[].creator - 创建人
   * @returns {string} returns.data[].creatorName - 创建人名称
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await partnerBillApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   customerName: '测试客户'
   * });
   */
  list(data) {
    return request({
      url: "/partner/bill/queryList",
      method: "post",
      data: data
    });
  },

  /**
   * 分页查询客户名称列表
   * 用于客户名称选择器的数据源
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {Array<number>} [data.orgNoList] - 机构编号列表
   * @param {number} [data.operatorId] - 操作员ID
   * @param {string} [data.operatorName] - 操作员名称
   * @param {string} [data.salesContractCode] - 销售合同编码
   * @param {string} [data.feeType] - 费用类型（字典：fee_type）
   * @param {string} [data.settlementStatus] - 结算状态（字典：settlement_status）
   * @param {string} [data.startBillingPeriod] - 账单周期开始时间
   * @param {string} [data.endBillingPeriod] - 账单周期结束时间
   * @param {string} [data.customerName] - 客户名称
   * @param {string} [data.serviceInvoiceCategory] - 平台服务费开票类目（字典：service_invoice_category）
   * @returns {Promise<Object>} 返回客户名称列表查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<string>} returns.data - 客户名称列表
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询客户名称列表
   * const result = await partnerBillApi.queryCustomerNameList({
   *   pageNum: 1,
   *   pageSize: 20,
   *   customerName: '测试'
   * });
   */
  queryCustomerNameList(data) {
    return request({
      url: "/partner/bill/queryCustomerNameList",
      method: "post",
      data: data
    });
  },

  /**
   * 新增/编辑合伙人账单信息
   * @param {Object} data - 合伙人账单信息数据
   * @param {number} [data.partnerBillId] - 主键ID（编辑时必填）
   * @param {string} data.salesContractCode - 销售合同编码（必填）
   * @param {string} data.customerName - 客户名称（必填）
   * @param {string} data.billingPeriod - 账单周期（必填）
   * @param {number} [data.totalPeriods] - 总期数
   * @param {number} [data.paymentPeriods] - 支付期数
   * @param {number} [data.gunCount] - 枪数
   * @param {number} [data.unitPrice] - 单价（元/枪）
   * @param {number} [data.amount] - 金额（元）
   * @param {string} [data.feeType] - 费用类型（字典：fee_type）
   * @param {string} [data.feeTypeName] - 费用类型名称
   * @param {string} [data.settlementStatus] - 结算状态（字典：settlement_status）
   * @param {string} [data.settlementStatusName] - 结算状态名称
   * @param {string} [data.serviceInvoiceCategory] - 平台服务费开票类目（字典：service_invoice_category）
   * @param {string} [data.serviceInvoiceCategoryName] - 平台服务费开票类目名称
   * @param {string} [data.serviceInvoiceRate] - 平台服务费开票税率（字典：service_invoice_rate）
   * @param {string} [data.serviceInvoiceRateName] - 平台服务费开票税率名称
   * @param {string} [data.remarks] - 备注
   * @param {number} [data.tenantId] - 租户ID
   * @param {number} [data.orgNo] - 机构编号
   * @param {number} [data.operatorId] - 操作员ID
   * @param {number} [data.creator] - 创建人
   * @param {string} [data.creatorName] - 创建人名称
   * @returns {Promise<Object>} 返回保存结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增合伙人账单信息
   * const result = await partnerBillApi.update({
   *   salesContractCode: 'SC001',
   *   customerName: '测试客户',
   *   billingPeriod: '2024-01',
   *   gunCount: 10,
   *   unitPrice: 100.00
   * });
   */
  update(data) {
    return request({
      url: "/partner/bill/saveInfo",
      method: "post",
      data: data
    });
  },

  /**
   * 删除合伙人账单信息
   * @param {Object} params - 删除参数
   * @param {number} params.partnerBillId - 要删除的合伙人账单信息ID（必填）
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除合伙人账单信息
   * const result = await partnerBillApi.remove({ partnerBillId: 1 });
   */
  remove(params) {
    return request({
      url: "/partner/bill/remove",
      method: "get",
      params: params
    });
  },

  /**
   * 导出合伙人账单信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.salesContractCode] - 销售合同编码筛选条件
   * @param {string} [data.feeType] - 费用类型筛选条件
   * @param {string} [data.settlementStatus] - 结算状态筛选条件
   * @param {string} [data.startBillingPeriod] - 账单周期开始时间筛选条件
   * @param {string} [data.endBillingPeriod] - 账单周期结束时间筛选条件
   * @param {string} [data.customerName] - 客户名称筛选条件
   * @param {string} [data.serviceInvoiceCategory] - 平台服务费开票类目筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有合伙人账单信息
   * const result = await partnerBillApi.exportData({
   *   customerName: '测试客户'
   * });
   */
  exportData(data) {
    return request({
      url: "/partner/bill/export",
      method: "post",
      data: data
    });
  }
};
