import request from "@/utils/request";

// 故障类别接口
//获取树结构
export function queryTreeList(data) {
  return request({
    url: "/faultType/list",
    method: "get",
    params: data,
  });
}
//删除
export function deleteType(data) {
  return request({
    url: "/faultType/remove",
    method: "post",
    data: data,
  });
}
//新增
export function addType(data) {
  return request({
    url: "/faultType/save",
    method: "post",
    data: data,
  });
}
//修改
export function editType(data) {
  return request({
    url: "/faultType/update",
    method: "post",
    data: data,
  });
}
//查询一级故障列表
export function firstFaultList(data) {
  return request({
    url: "/faultType/fistLevelList",
    method: "get",
    params: data,
  });
}
//查询子级故障列表
export function childFaultList(data) {
  return request({
    url: "/faultType/childrenList",
    method: "get",
    params: data,
  });
}
