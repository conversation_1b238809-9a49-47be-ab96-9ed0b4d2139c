import request from "@/utils/request";
//人员档案
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/staff/list",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/staff/remove",
      method: "get",
      params: query,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/staff/export",
      method: "post",
      data: query,
    });
  },
  //查询日志
  queryLog(data) {
    return request({
      url: "/staff/log",
      method: "get",
      params: data,
    });
  },
  //详情回显
  getDetail(data) {
    return request({
      url: "/staff/detail",
      method: "get",
      params: data,
    });
  },
  //新增/编辑保存
  submitForm(query) {
    return request({
      url: "/staff/save",
      method: "post",
      data: query,
    });
  },
  //职位名称下拉列表
  queryPosition(data) {
    return request({
      url: "/staff/positionList",
      method: "get",
      params: data,
    });
  },
  //公司名称下拉列表
  queryCompany(data) {
    return request({
      url: "/staff/companyList",
      method: "get",
      params: data,
    });
  },
  //行业类型下拉列表
  queryIndustryType(data) {
    return request({
      url: "/staff/industryTypeList",
      method: "get",
      params: data,
    });
  },
};
