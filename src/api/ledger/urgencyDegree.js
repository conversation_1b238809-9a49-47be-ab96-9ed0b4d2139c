import request from "@/utils/request";
//紧急程度
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/ledger/urgencyDegree/list",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/aging/config/delete",
      method: "post",
      data: query,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/checkItemDef/update",
      method: "post",
      data: query,
    });
  },
  //查询日志
  queryLog(data) {
    return request({
      url: "/ledger/urgencyDegree/log/list",
      method: "get",
      params: data,
    });
  },
  //保存/新增
  update(query) {
    return request({
      url: "/ledger/urgencyDegree/save",
      method: "post",
      data: query,
    });
  },
  //状态切换
  changeStatus(query) {
    return request({
      url: "/ledger/urgencyDegree/changeStatus",
      method: "post",
      data: query,
    });
  },
};
