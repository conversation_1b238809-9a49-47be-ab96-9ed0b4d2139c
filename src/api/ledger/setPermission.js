import request from "@/utils/request";

// 账号数据权限
//列表查询
export function getUserList(data) {
  return request({
    url: "/ledger/permission/getUserList",
    method: "get",
    params: data,
  });
}
// 修改状态
export function setPermsStatus(data) {
  return request({
    url: "/ledger/permission/updateEnableFlag",
    method: "post",
    data,
  });
}

//查询所有工单类型 树结构
export function queryOrderTree(data) {
  return request({
    url: "/ledger/orderType/list",
    method: "post",
    data: data,
  });
}
//查询已绑定的工单类型（修改时）
export function queryConfigOrder(data) {
  return request({
    url: "/ledger/permission/getBindOrderType",
    method: "post",
    data: data,
  });
}
//配置工单类型
export function submitOrder(data) {
  return request({
    url: "/ledger/permission/order/saveOrUpdate",
    method: "post",
    data: data,
  });
}
//获取业务类型树结构
export function queryBusinessTree(data) {
  return request({
    url: "/ledger/businessType/list",
    method: "get",
    params: data,
  });
}
//获取已勾选业务类型
export function queryCheckedBusiness(data) {
  return request({
    url: "/ledger/permission/getBindBusinessType",
    method: "post",
    data: data,
  });
}
//配置业务类型
export function submitBusiness(data) {
  return request({
    url: "/ledger/permission/business/saveOrUpdate",
    method: "post",
    data: data,
  });
}
//获取已勾选部门
export function queryCheckedDept(data) {
  return request({
    url: "/ledger/permission/getBindSupportDept",
    method: "post",
    data: data,
  });
}
//配置部门
export function submitDept(data) {
  return request({
    url: "/ledger/permission/dept/saveOrUpdate",
    method: "post",
    data: data,
  });
}
// 查询权限操作日志
export function queryLog(data) {
  return request({
    url: "/ledger/permission/log",
    method: "get",
    params: data,
  });
}
// 设置最高权限
export function changeRoot(data) {
  return request({
    url: "/ledger/permission/updateMaxEnableFlag",
    method: "post",
    data: data,
  });
}
