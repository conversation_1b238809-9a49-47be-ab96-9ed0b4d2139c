import request from "@/utils/request";
//部门管理
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/ledger/dept/queryList",
      method: "post",
      data: data,
    });
  },
  // 删除
  deleteData(query) {
    return request({
      url: "/ledger/dept/remove",
      method: "get",
      params: query,
    });
  },
  //查询日志
  queryLog(data) {
    return request({
      url: "/ledger/dept/log",
      method: "get",
      params: data,
    });
  },
  //保存/新增
  update(query) {
    return request({
      url: "/ledger/dept/save",
      method: "post",
      data: query,
    });
  },
};
