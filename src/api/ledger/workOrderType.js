import request from "@/utils/request";

// 工单类型接口
//获取树结构
export function queryTreeList(data) {
  return request({
    url: "/ledger/orderType/list",
    method: "post",
    data: data,
  });
}
//删除
export function deleteType(data) {
  return request({
    url: "/ledger/orderType/remove",
    method: "post",
    data: data,
  });
}
//新增
export function addType(data) {
  return request({
    url: "/ledger/orderType/save",
    method: "post",
    data: data,
  });
}
//修改
export function editType(data) {
  return request({
    url: "/ledger/orderType/save",
    method: "post",
    data: data,
  });
}
//排序
export function sortTree(data) {
  return request({
    url: "/ledger/orderType/order",
    method: "get",
    params: data,
  });
}
//节点时效列表
export function queryNodeTimeList(data) {
  return request({
    url: "/ledger/flow/getFlowNodeAging",
    method: "get",
    params: data,
  });
}
//导出
export function exportApi(data) {
  return request({
    url: "/ledger/orderType/export",
    method: "post",
    data: data,
  });
}
//获取流程下拉选项
export function queryProcessOptions(data) {
  return request({
    url: "/ledger/flow/list",
    method: "get",
    params: data,
  });
}
export function queryDeptOrderTree(data) {
  return request({
    url: "/ledger/orderType/listByDeptBusinessType",
    method: "get",
    params: data,
  });
}
//切换是否标准工单
export function changeOrderAttr(data) {
  return request({
    url: "/ledger/orderType/editOrderAttr",
    method: "post",
    data: data,
  });
}
//批量打标
export function batchMark(data) {
  return request({
    url: "/ledger/orderType/batchMark",
    method: "post",
    data: data,
  });
}

/**
 * 批量配置流程
 * @param {Object} data 请求参数
 * @param {Array} data.ids 工单类型主键id数组
 * @param {Number} data.handleTotalValidTime 处理总时效
 * @param {String} data.flowKey 配置流程key
 * @returns {Promise} 返回Promise对象
 */
export function batchConfigProcess(data) {
  return request({
    url: "/ledger/orderType/batchConfigProcess",
    method: "post",
    data: data,
  });
}
