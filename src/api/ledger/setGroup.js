import request from "@/utils/request";

// 分组管理接口
//列表查询
export function queryGroupList(data) {
  return request({
    url: "/ledger/group/list",
    method: "post",
    data: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "/ledger/group/changeStatus",
    method: "post",
    data: data,
  });
}
//操作日志
export function queryLogList(data) {
  return request({
    url: "/ledger/group/log/list",
    method: "get",
    params: data,
  });
}
//查询所有组织-用户 树结构
export function queryTreeList(data) {
  return request({
    url: "/handle/group/deptUserList",
    method: "get",
    params: data,
  });
}
//查询详情（修改时）
export function queryGroupUser(data) {
  return request({
    url: "/ledger/group/groupUserList",
    method: "get",
    params: data,
  });
}
//修改
export function submitSave(data) {
  return request({
    url: "/ledger/group/save",
    method: "post",
    data: data,
  });
}
