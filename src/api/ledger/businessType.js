import request from "@/utils/request";

// 业务类型接口
//获取树结构
export function queryTreeList(data) {
  return request({
    url: "/ledger/businessType/list",
    method: "get",
    params: data,
  });
}
//删除
export function deleteType(data) {
  return request({
    url: "/ledger/businessType/remove",
    method: "post",
    data: data,
  });
}
//新增
export function addType(data) {
  return request({
    url: "/ledger/businessType/save",
    method: "post",
    data: data,
  });
}
//修改
export function editType(data) {
  return request({
    url: "/ledger/businessType/save",
    method: "post",
    data: data,
  });
}
//工单类型树选项
export function queryOrderTree(data) {
  return request({
    url: "/ledger/orderType/listByDept",
    method: "get",
    params: data,
  });
}
