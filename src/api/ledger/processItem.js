import request from "@/utils/request";

// 自定义工序项统计
export default {
  // 列表查询
  getProcessItemTableData(data) {
    return request({
      url: "/ledger/statistics/processItem",
      method: "post",
      data: data,
    });
  },
  // 汇总数据
  getProcessItemSummary(data) {
    return request({
      url: "/ledger/statistics/processItem/topic",
      method: "post",
      data: data,
    });
  },
  // 导出
  exportProcessItem(data) {
    return request({
      url: "/ledger/statistics/processItem/export",
      method: "post",
      data: data,
    });
  },
};
