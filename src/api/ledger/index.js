import request from "@/utils/request";
//工单台账
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/ledger/order/queryList",
      method: "post",
      data: data,
    });
  },
  // 删除
  delete(data) {
    return request({
      url: "/ledger/order/removeOrder",
      method: "get",
      params: data,
    });
  },
  // 撤回
  withdraw(data) {
    return request({
      url: "/ledger/order/reCallTask",
      method: "post",
      data: data,
    });
  },
  //转派
  transfer(data) {
    return request({
      url: "/ledger/order/transferTask",
      method: "post",
      data: data,
    });
  },
  //复制
  copy(data) {
    return request({
      url: "/ledger/order/copy",
      method: "post",
      data: data,
    });
  },
  // 备注
  remark(data) {
    return request({
      url: "/ledger/order/remark",
      method: "post",
      data: data,
    });
  },
  //完结
  end(data) {
    return request({
      url: "/ledger/order/finish",
      method: "post",
      data: data,
    });
  },
  //关联申请单号
  associate(data) {
    return request({
      url: "/ledger/order/omNo",
      method: "get",
      params: data,
    });
  },
  //查看审批进度
  queryProcess(data) {
    return request({
      url: "/ledger/order/getApproveRecord",
      method: "get",
      params: data,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/ledger/order/export",
      method: "post",
      data: query,
    });
  },
  // 接单
  acceptOrder(query) {
    return request({
      url: "/ledger/order/takeOrder",
      method: "get",
      params: query,
    });
  },
  // 设为偏好
  saveCustomColumn(query) {
    return request({
      url: "/ledger/order/savePreference",
      method: "post",
      data: query,
    });
  },
  // 取消偏好
  cancelCustomColumn(query) {
    return request({
      url: "/ledger/order/removePreference",
      method: "get",
      params: query,
    });
  },
  // 查询偏好
  queryCustomColumn(query) {
    return request({
      url: "/ledger/order/queryPreference",
      method: "get",
      params: query,
    });
  },

  // 查询筛选条件偏好
  queryFilterPreference(query) {
    return request({
      url: "/ledger/order/queryFilterPreference",
      method: "get",
      params: query,
    });
  },
  // 批量完结
  batchEnd(data) {
    return request({
      url: "/ledger/order/finishBatch",
      method: "post",
      data: data,
    });
  },
  //钉钉工单活动城市列表
  getActCityList(data) {
    return request({
      url: "/ledger/order/actCityList",
      method: "get",
      params: data,
    });
  },

  //新增页
  //根据业务类型获取工单类型
  queryOrderOptionsByBusiness(data) {
    return request({
      url: "/ledger/orderType/listByBusinessType",
      method: "get",
      params: data,
    });
  },
  //根据支持部门获取业务类型
  queryBusinessByDept(data) {
    return request({
      url: "/ledger/businessType/listBySupportDept",
      method: "get",
      params: data,
    });
  },

  //根据工单类型获取表单
  queryFormJson(data) {
    return request({
      url: "/ledger/order/getBindFlow",
      method: "get",
      params: data,
    });
  },
  //编辑详情
  queryDetail(data) {
    return request({
      url: "/ledger/order/basicDetail",
      method: "post",
      data: data,
    });
  },
  //新增/修改
  submitForm(data) {
    return request({
      url: "/ledger/order/save",
      method: "post",
      data: data,
    });
  },
  //已配置权限的支持部门
  permissionDept(data) {
    return request({
      url: "/ledger/order/permissionSupportDept",
      method: "get",
      params: data,
    });
  },

  //详情页
  //基本信息
  queryLedgerBaseInfo(data) {
    return request({
      url: "/ledger/order/basicDetail",
      method: "post",
      data: data,
    });
  },
  //从加单记录进入基本信息
  queryAddOrderBaseInfo(data) {
    return request({
      url: "/add/order/basicDetail",
      method: "get",
      params: data,
    });
  },
  //处理-是否展示驳回、作废按钮
  queryBtnShow(data) {
    return request({
      url: "/ledger/order/nodeBtn",
      method: "get",
      params: data,
    });
  },
  //处理信息-获取节点步骤条
  queryHandleSteps(data) {
    return request({
      url: "/ledger/order/getNodeHandleDetail",
      method: "get",
      params: data,
    });
  },
  //处理信息-点击节点获取表单信息
  getFormJsonByNode(data) {
    return request({
      url: "/ledger/order/getNodeTaskForms",
      method: "get",
      params: data,
    });
  },

  // 驳回下拉列表 节点
  getBackNodes(data) {
    return request({
      url: "/ledger/order/getBackNodes",
      method: "get",
      params: data,
    });
  },

  //处理信息-点击节点获取节点处理人信息
  getTaskNodeInfo(data) {
    return request({
      url: "/ledger/order/getTaskNodeInfo",
      method: "get",
      params: data,
    });
  },
  //时效统计
  queryProcessNode(data) {
    return request({
      url: "/ledger/order/getNodeCostTimeDetail",
      method: "get",
      params: data,
    });
  },
  //操作记录
  queryLedgerRecord(data) {
    return request({
      url: "/ledger/operation/record/detail",
      method: "get",
      params: data,
    });
  },
  //处理页
  //作废
  cancel(data) {
    return request({
      url: "/ledger/order/invalidTask",
      method: "post",
      data: data,
    });
  },
  //驳回
  reject(data) {
    return request({
      url: "/ledger/order/backTask",
      method: "post",
      data: data,
    });
  },
  //处理
  handle(data) {
    return request({
      url: "/ledger/order/submitTask",
      method: "post",
      data: data,
    });
  },

  //开工申请
  //开工申请-详情
  applyDetail(data) {
    return request({
      url: "/project/query/applyInfo",
      method: "get",
      params: data,
    });
  },
  //开工申请-保存
  saveApply(data) {
    return request({
      url: "/project/applyOperation",
      method: "post",
      data: data,
    });
  },
  //项目详情
  //详情-基础信息tab+工程信息tab+设备信息tab
  baseInfoDetail(data) {
    return request({
      url: "/project/batch/basicDetail",
      method: "get",
      params: data,
    });
  },
  //详情-付款信息tab 付款详情
  getPayDescription(data) {
    return request({
      url: "/jdy/getPayment",
      method: "get",
      params: data,
    });
  },
  //踏勘信息抽屉
  getExploreDetail(data) {
    return request({
      url: "/construct/manager/businessDetails",
      method: "get",
      params: data,
    });
  },
  //投建协议信息抽屉
  getAgreementDetail(data) {
    return request({
      url: "/construct/manager/constAgreeDtl",
      method: "get",
      params: data,
    });
  },

  //投建协议信息列表抽屉
  getAgreementDetailList(data) {
    return request({
      url: "/construct/manager/constAgreeDetailListDtl",
      method: "get",
      params: data,
    });
  },

  //采购单详情抽屉
  getPurchaseOrderDetail(data) {
    return request({
      url: "/construct/manager/purOrderDtl",
      method: "get",
      params: data,
    });
  },
  //采购单详情抽屉-固定资产明细
  getPurchaseAsset(data) {
    return request({
      url: "/gudingzichan/list",
      method: "get",
      params: data,
    });
  },
  //采购单框架协议详情抽屉
  getPurchaseAgreementDetail(data) {
    return request({
      url: "/construct/manager/purFrameDtl",
      method: "get",
      params: data,
    });
  },

  //付款单详情抽屉
  getPayDetail(data) {
    return request({
      url: "/construct/manager/paymentOrderDtl",
      method: "get",
      params: data,
    });
  },

  //付款单详情抽屉中的 采购明细List
  getPayDetailList(data) {
    return request({
      url: "/construct/manager/paymentOrderDtlList",
      method: "get",
      params: data,
    });
  },

  // //详情-工程信息tab
  // engineerInfoDetail(data) {
  //   return request({
  //     url: "/aging/config/page",
  //     method: "post",
  //     data: data,
  //   });
  // },
  // //详情-设备信息tab
  // deviceInfoDetail(data) {
  //   return request({
  //     url: "/aging/config/page",
  //     method: "post",
  //     data: data,
  //   });
  // },
  // 详情-设备信息tab-设备明细列表
  deviceList(data) {
    return request({
      url: "/project/batch/deviceDetail",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-基本信息
  applyInfoDetail(data) {
    return request({
      url: "/project/query/basicInfo",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-施工队信息和进度计划
  applyInfoTeam(data) {
    return request({
      url: "/project/query/applyInfo",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-审核信息
  applyInfoCheck(data) {
    return request({
      url: "/project/query/auditInfo",
      method: "get",
      params: data,
    });
  },
  //详情-开工申请tab-确认信息
  applyInfoConfirm(data) {
    return request({
      url: "/project/query/confirmInfo",
      method: "get",
      params: data,
    });
  },
  //详情-施工信息tab
  constructionInfoDetail(data) {
    return request({
      url: "/porject/work/queryConstructionBasicInfo",
      method: "get",
      params: data,
    });
  },
  //详情-施工信息tab-施工工序完成情况
  constructionWorkDetail(data) {
    return request({
      url: "/porject/work/queryWorkByProject",
      method: "get",
      params: data,
    });
  },
  commonWorkDetail(data) {
    return request({
      url: "/porject/work/queryFixFormByProject",
      method: "get",
      params: data,
    });
  },
  //详情-操作记录
  recordInfo(data) {
    return request({
      url: "/project/record/queryRecord",
      method: "get",
      params: data,
    });
  },

  //开工申请-工程经理下拉选项
  permissionUserList(data) {
    return request({
      url: "/construct/manager/getPermissUserList",
      method: "get",
      params: data,
    });
  },
  // 查询角色账号列表
  listRoleAccount(query) {
    return request({
      url: "/construct/manager/listProjectManager",
      method: "get",
      params: query,
    });
  },

  // 场站项目列表
  queryStationProject(data) {
    return request({
      url: "/project/station/list",
      method: "post",
      data,
    });
  },
  // 场站项目列表-导出
  exportStationProject(data) {
    return request({
      url: "/project/station/export",
      method: "post",
      data,
    });
  },
  // 待办左侧列表
  queryTodoStatistics(data) {
    return request({
      url: "/project/batch/todoStatistics",
      method: "get",
      params: data,
    });
  },

  // 查询场站项目详情
  queryStationProjectDetail(data) {
    return request({
      url: "/project/station/detail",
      method: "get",
      params: data,
    });
  },

  // 查询数据看板
  queryProjectStatisticsData(data) {
    return request({
      url: "/project/batch/statistics",
      method: "post",
      data,
    });
  },
  //查询指定处理组
  getGroupList(query) {
    return request({
      url: "/ledger/group/list",
      method: "post",
      data: query,
    });
  },
  //查询指定处理角色
  getRoleList(query) {
    return request({
      url: "/system/role/list",
      method: "get",
      params: query,
    });
  },
  //紧急程度
  getUrgencyDegree(data) {
    return request({
      url: "/ledger/urgencyDegree/list",
      method: "post",
      data: data,
    });
  },
  //加单抽屉
  //该工单所有已处理节点的节点名称
  queryProcessOptions(data) {
    return request({
      url: "/add/ledger/order/queryNodeValidTime",
      method: "get",
      params: data,
    });
  },
  //加单
  addOrder(data) {
    return request({
      url: "/add/ledger/order/save",
      method: "post",
      data: data,
    });
  },
  //加单审核
  checkAddOrder(data) {
    return request({
      url: "/add/ledger/order/auditApplyAddOrder",
      method: "post",
      data: data,
    });
  },
  //查询加单详情
  checkInfo(data) {
    return request({
      url: "/add/ledger/order/queryApplyInfo",
      method: "get",
      params: data,
    });
  },
  //加单-获取账号权限下的启用用户
  addUserList(data) {
    return request({
      url: "/ledger/permission/getUserList",
      method: "get",
      params: data,
    });
  },
  //加单记录-编辑加单
  editAddOrder(data) {
    return request({
      url: "/add/ledger/order/edit",
      method: "post",
      data: data,
    });
  },
  getBdNames(data) {
    return request({
      url: "/ledger/order/bdNameList",
      method: "get",
      params: data,
    });
  },
};
