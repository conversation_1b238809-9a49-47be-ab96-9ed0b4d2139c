import request from "@/utils/request";

// 工时管理-工时核算
export default {
  // 列表
  list(data) {
    return request({
      url: "/workHour/compute/pageList",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/workHour/compute/export",
      method: "post",
      data: data,
    });
  },
  //工时核算填报工时详情
  fillDetailList(data) {
    return request({
      url: "/workHour/compute/fillDetail",
      method: "post",
      data: data,
    });
  },
  //工时核算填报工时详情-统计数据
  fillSumData(data) {
    return request({
      url: "/workHour/compute/fillDetailSummary",
      method: "post",
      data: data,
    });
  },
  //工时核算订单制工时详情
  orderDetailList(data) {
    return request({
      url: "/workHour/compute/orderDetail",
      method: "post",
      data: data,
    });
  },
  //工时核算订单制工时详情-统计数据
  orderSumData(data) {
    return request({
      url: "/workHour/compute/orderDetailSummary",
      method: "post",
      data: data,
    });
  },
  // 下拉菜单类型，00工时归属，01对应业务类型
  getDropdownList(data) {
    return request({
      url: "/workHour/code/dropDownList",
      method: "post",
      data: data,
    });
  },
};
