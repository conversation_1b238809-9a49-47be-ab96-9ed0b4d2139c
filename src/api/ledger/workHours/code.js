import request from "@/utils/request";

// 工时管理-填报工时
export default {
  // 列表
  list(data) {
    return request({
      url: "/workHour/code/pageList",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/workHour/code/export",
      method: "post",
      data: data,
    });
  },
  // 导入
  batchImport(data) {
    return request({
      url: "/workHour/code/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },
  //编辑
  update(data) {
    return request({
      url: "/workHour/code/edit",
      method: "post",
      data: data,
    });
  },

  // 批量标记
  batchMark(data) {
    return request({
      url: "/workHour/code/batchMark",
      method: "post",
      data: data,
    });
  },
  // 日志
  queryLog(data) {
    return request({
      url: "/orderRecord/getByBusinessId",
      method: "get",
      params: data,
    });
  },
  // 下拉菜单类型，00工时归属，01对应业务类型
  getDropdownList(data) {
    return request({
      url: "/workHour/code/dropDownList",
      method: "post",
      data: data,
    });
  },
};
