import request from "@/utils/request";

// 工时管理-填报工时
export default {
  // 列表
  list(data) {
    return request({
      url: "/workHour/record/pageList",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(data) {
    return request({
      url: "/workHour/record/export",
      method: "post",
      data: data,
    });
  },
  // 导入
  batchImport(data) {
    return request({
      url: "/workHour/record/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  // 工时明细
  detail(data) {
    return request({
      url: "/workHour/record/detail",
      method: "post",
      data: data,
    });
  },
};
