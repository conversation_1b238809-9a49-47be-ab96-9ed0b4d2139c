import request from "@/utils/request";

// 工时管理-数据分析
export default {
  // 图表-数据概览
  queryStatisticsData(data) {
    return request({
      url: "/workHour/dataAnalysis/dataOverview",
      method: "post",
      data: data,
    });
  },
  // 图表-各部门人员统计
  queryPersonData(data) {
    return request({
      url: "/workHour/dataAnalysis/employeeBar",
      method: "post",
      data: data,
    });
  },
  // 图表-各业务人力成本对比
  queryBusinessChartData(data) {
    return request({
      url: "/workHour/dataAnalysis/businessBar",
      method: "post",
      data: data,
    });
  },

  // 图表-各部门人力成本对比
  queryDeptChartData(data) {
    return request({
      url: "/workHour/dataAnalysis/departmentBar",
      method: "post",
      data: data,
    });
  },

  //明细-列表
  detailList(data) {
    return request({
      url: "/workHour/compute/computeList",
      method: "post",
      data: data,
    });
  },
  //人月-各业务投入分析
  monthBusinessData(data) {
    return request({
      url: "/workHour/dataAnalysis/bizMouthDataStatistics",
      method: "get",
      params: data,
    });
  },
  //人月-各部门投入分析
  monthDeptData(data) {
    return request({
      url: "/workHour/dataAnalysis/depMouthDataStatistics",
      method: "get",
      params: data,
    });
  },
  //人月-各部门人力趋势
  monthPersonData(data) {
    return request({
      url: "/workHour/dataAnalysis/trends",
      method: "get",
      params: data,
    });
  },
};
