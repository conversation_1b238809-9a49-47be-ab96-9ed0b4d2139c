// import request from "@/utils/processRequest";
import request from "@/utils/request";
// ----------------------------表单设计接口
//列表
export function list(query) {
  return request({
    url: "/ledger/form/list",
    method: "get",
    params: query,
  });
}

//表单设计-新增
export function formAdd(query) {
  return request({
    url: "/ledger/form/saveForm",
    method: "post",
    data: query,
  });
}
//表单设计-编辑
export function formUpdate(query) {
  return request({
    url: "/ledger/form/editForm",
    method: "post",
    data: query,
  });
}

//复制
export function copyForm(query) {
  return request({
    url: "/ledger/form/copyForm",
    method: "post",
    data: query,
  });
}

//外层删除
export function deleteForm(query) {
  return request({
    url: "/ledger/form/deleteForm",
    method: "get",
    params: query,
  });
}

//获取表单数据
export function getFormData(query) {
  return request({
    url: "/ledger/form/formDetail",
    method: "get",
    params: query,
  });
}

//表单设计-复制(复制多版本)
export function copyFormDef(query) {
  return request({
    url: "/formdef/copyFormDef",
    method: "post",
    data: query,
  });
}
//获取表单数据
export function queryVersionList(query) {
  return request({
    url: "/form/list",
    method: "get",
    params: query,
  });
}
//获取表单图片上传地址
export function getAppUploadPath() {
  let baseUrl = window.location.hostname.includes(
    process.env.VUE_APP_BASE_API_PREFIX_INTERNAL
  )
    ? process.env.VUE_APP_BASE_API_INTERNAL
    : process.env.VUE_APP_BASE_API_EXTERNAL;
  return { data: baseUrl + process.env.VUE_APP_BASE_UPLOAD_URL };
}
//获取表单属性下拉选项
export function queryPropOptions(query) {
  return request({
    url: "/ledger/form/formAttr",
    method: "get",
    params: query,
  });
}
//列表数据切换状态
export function changeStatus(query) {
  return request({
    url: "/ledger/form/editFormStatus",
    method: "post",
    data: query,
  });
}
