import request from "@/utils/request";
//数据统计
export default {
  //数据看版
  //数据概览
  queryOrderData(data) {
    return request({
      url: "/ledger/statistics/data",
      method: "post",
      data: data,
    });
  },
  //工单完成趋势
  queryHandleTend(data) {
    return request({
      url: "/ledger/statistics/tendency",
      method: "post",
      data: data,
    });
  },
  //业务类型
  queryBusinessTypeRank(data) {
    return request({
      url: "/ledger/statistics/businessType",
      method: "post",
      data: data,
    });
  },
  //工单类型
  queryOrderTypeRank(data) {
    return request({
      url: "/ledger/statistics/orderType",
      method: "post",
      data: data,
    });
  },
  // 支持部门工单占比
  queryDeptPieList(data) {
    return request({
      url: "/ledger/statistics/supportDept",
      method: "post",
      data: data,
    });
  },
  // 工单来源占比
  queryOriginPieList(data) {
    return request({
      url: "/ledger/statistics/source",
      method: "post",
      data: data,
    });
  },
  // 超时工单占比
  queryExceedPieList(data) {
    return request({
      url: "/ledger/statistics/timeout",
      method: "post",
      data: data,
    });
  },
  // 紧急程度占比
  queryUrgencyPieList(data) {
    return request({
      url: "/ledger/statistics/urgencyLevel",
      method: "post",
      data: data,
    });
  },
  //部门汇总
  //部门-列表
  getDeptTableData(data) {
    return request({
      url: "/ledger/statistics/dept",
      method: "post",
      data: data,
    });
  },
  //部门-汇总数据
  getDeptSummary(data) {
    return request({
      url: "/ledger/statistics/dept/topic",
      method: "post",
      data: data,
    });
  },
  //部门-导出
  exportDept(data) {
    return request({
      url: "/ledger/statistics/dept/export",
      method: "post",
      data: data,
    });
  },
  //人员汇总
  //人员-列表
  getPersonTableData(data) {
    return request({
      url: "/ledger/statistics/user",
      method: "post",
      data: data,
    });
  },
  //人员-汇总数据
  getPersonSummary(data) {
    return request({
      url: "/ledger/statistics/user/topic",
      method: "post",
      data: data,
    });
  },
  //人员-导出
  exportPerson(data) {
    return request({
      url: "/ledger/statistics/user/export",
      method: "post",
      data: data,
    });
  },
  // 列表查询
  getTableData(data) {
    return request({
      url: "/ledger/order/getNodeInfos",
      method: "post",
      data: data,
    });
  },
  export(data) {
    return request({
      url: "/ledger/order/export/node",
      method: "post",
      data: data,
    });
  },
  getUrgencyDegree(data) {
    return request({
      url: "/ledger/urgencyDegree/list",
      method: "post",
      data: data,
    });
  },

  /**
   * 获取自定义工序项统计数据
   * @param {Object} data 查询参数
   * @returns {Promise} 返回Promise对象
   */
  getProcessItemTableData(data) {
    return request({
      url: "/ledger/statistics/process/list",
      method: "post",
      data: data,
    });
  },

  /**
   * 获取自定义工序项统计汇总数据
   * @param {Object} data 查询参数
   * @returns {Promise} 返回Promise对象
   */
  getProcessItemSummary(data) {
    return request({
      url: "/ledger/statistics/process/summary",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出自定义工序项统计数据
   * @param {Object} data 查询参数
   * @returns {Promise} 返回Promise对象
   */
  exportProcessItem(data) {
    return request({
      url: "/ledger/statistics/process/export",
      method: "post",
      data: data,
    });
  },

  /**
   * 获取节点名称下拉选项
   * @param {Object} data 查询参数
   * @returns {Promise} 返回Promise对象，包含节点名称列表
   */
  getNodeNameOptions(data) {
    return request({
      url: "/ledger/statistics/process/nodeName",
      method: "post",
      data: data,
    });
  },

  /**
   * 获取工序项名称下拉选项
   * @param {Object} data 查询参数
   * @returns {Promise} 返回Promise对象，包含工序项名称列表
   */
  getProcessNameOptions(data) {
    return request({
      url: "/ledger/statistics/process/ProcessName",
      method: "post",
      data: data,
    });
  },
};
