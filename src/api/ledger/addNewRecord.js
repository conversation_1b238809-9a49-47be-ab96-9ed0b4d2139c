import request from "@/utils/request";
//xin加单记录
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/add/ledger/order/list",
      method: "post",
      data: data,
    });
  },
  // 删除
  delete(data) {
    return request({
      url: "/add/order/delete",
      method: "get",
      params: data,
    });
  },
  // 审核
  check(data) {
    return request({
      url: "/add/ledger/order/auditApplyAddOrder",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/add/ledger/order/export",
      method: "post",
      data: query,
    });
  },
};
