import request from "@/utils/request";
//加单记录
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/add/order/queryList",
      method: "post",
      data: data,
    });
  },
  // 删除
  delete(data) {
    return request({
      url: "/add/order/delete",
      method: "get",
      params: data,
    });
  },
  // 审核
  check(data) {
    return request({
      url: "/add/order/audit",
      method: "post",
      data: data,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "add/order/export",
      method: "post",
      data: query,
    });
  },
};
