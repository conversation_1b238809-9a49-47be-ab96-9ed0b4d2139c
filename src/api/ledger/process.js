import request from "@/utils/request";
//列表
export function list(query) {
  return request({
    url: "/ledger/flow/list",
    method: "get",
    params: query,
  });
}
//修改状态
export function changeStatus(query) {
  return request({
    url: "/ledger/flow/editFlowStatus",
    method: "post",
    data: query,
  });
}
//删除
export function deleteItem(query) {
  return request({
    url: "/ledger/flow/deleteFlow",
    method: "get",
    params: query,
  });
}
//复制
export function copyFlow(query) {
  return request({
    url: "/ledger/flow/copyFlow",
    method: "post",
    data: query,
  });
}
//新增时-生成flowKey
export function createFlowKey(query) {
  return request({
    url: "/ledger/flow/key",
    method: "get",
    params: query,
  });
}
//流程设计-新增
export function addFlow(query) {
  return request({
    url: "/ledger/flow/saveFlow",
    method: "post",
    data: query,
  });
}
//流程设计-修改
export function updateFlow(query) {
  return request({
    url: "/ledger/flow/editFlow",
    method: "post",
    data: query,
  });
}

//获取key
export function getKey(query) {
  return request({
    url: "/external/api/v1/flowdef/key",
    method: "get",
    params: query,
  });
}

//根据业务类型获取数据
export function listByBusinessType(query) {
  return request({
    url: "/external/api/v1/flowdef/listByBusinessType",
    method: "get",
    params: query,
  });
}

export function handleDelete(query) {
  return request({
    url: "/external/api/v1/flow/delete",
    method: "get",
    params: query,
  });
}

export function handleCopy(query) {
  return request({
    url: "/external/api/v1/flow/copy",
    method: "get",
    params: query,
  });
}

export function modifyStatus(query) {
  return request({
    url: "/external/api/v1/flow/modifyStatus",
    method: "get",
    params: query,
  });
}

//处理设置绑定
export function handleBindTab(query) {
  return request({
    url: "/external/api/v1/flowFormBind/handleBind",
    method: "post",
    data: query,
  });
}
//审批绑定
export function approveConfigBind(query) {
  return request({
    url: "/external/api/v1/flowFormBind/approveConfigBind",
    method: "post",
    data: query,
  });
}
// 超时告警规则绑定
export function reminderConfigBind(query) {
  return request({
    url: "/external/api/v1/flowFormBind/reminderConfigBind",
    method: "post",
    data: query,
  });
}
//业务配置绑定
export function bizConfigBind(query) {
  return request({
    url: "/external/api/v1/flowFormBind/bizConfigBind",
    method: "post",
    data: query,
  });
}
//业务list
export function flwExtendTaskCompensationList(query) {
  return request({
    url: "/external/api/v1/flwExtendTaskCompensation/list",
    method: "get",
    params: query,
  });
}

//获取绑定的表单
export function getAllBindForms(query) {
  return request({
    url: "/external/api/v1/flowFormBind/getAllBindForms",
    method: "post",
    data: query,
  });
}

//获取绑定的表单
export function getReminderConfig(query) {
  return request({
    url: "/external/api/v1/flowFormBind/getReminderConfig",
    method: "post",
    data: query,
  });
}

//获取业务回显
export function getBizConfigs(query) {
  return request({
    url: "/external/api/v1/flowFormBind/getBizConfigs",
    method: "post",
    data: query,
  });
}

// 获取节点配置的业务参数
export function getTaskBizParams(query) {
  return request({
    url: "/external/api/v1/flowFormBind/getTaskBizParams",
    method: "post",
    data: query,
  });
}

//获取业务回显
export function basicConfigBind(query) {
  return request({
    url: "/external/api/v1/flowFormBind/basicConfigBind",
    method: "post",
    data: query,
  });
}

//获取人员列表
export function getUserAvailUsers(query) {
  return request({
    url: "/external/api/v1/flowFormBind/getUserAvailUsers",
    method: "get",
    params: query,
  });
}
//获取组织列表
export function getUserAvailGroups(query) {
  return request({
    url: "/external/api/v1/flowFormBind/getUserAvailOrgs",
    method: "get",
    params: query,
  });
}

//获取判断对象
export function queryFormVariables(query) {
  return request({
    url: "/api/v1/process/form/queryFormVariables",
    method: "get",
    params: query,
  });
}

//获取已完成的节点
export function queryHisTaskNodes(query) {
  return request({
    url: "/api/v1/process/business/queryHisTaskNodes",
    method: "post",
    data: query,
  });
}

// ----------------------------------------测试网厅接口
//获取空表单
export function taskFormsFirstDeal(query) {
  return request({
    url: "/api/v1/process/form/taskFormsFirstDeal",
    method: "get",
    params: query,
  });
}

//按钮类型
export function taskConfigs(query) {
  return request({
    url: "/api/v1/process/config/taskConfigs",
    method: "get",
    params: query,
  });
}
//表单提交
export function startWithForm(query) {
  return request({
    url: "/api/v1/process/insts/startWithForm",
    method: "post",
    data: query,
  });
}

//查询流程详情
export function queryByFlowId(query) {
  return request({
    url: "/external/api/v1/flow/queryByFlowId",
    method: "get",
    params: query,
  });
}

//查询流程详情
export function queryByBusinessNo(query) {
  return request({
    url: "/external/api/v1/flow/queryByBusinessNo",
    method: "get",
    params: query,
  });
}
//下面是新加的
//查询指定处理人
export function getUserList(query) {
  return request({
    url: "/common/all/user",
    method: "post",
    data: query,
  });
}
//查询指定处理组
export function getGroupList(query) {
  return request({
    url: "/ledger/group/list",
    method: "post",
    data: query,
  });
}
//查询指定处理角色
export function getRoleList(query) {
  return request({
    url: "/system/role/list",
    method: "get",
    params: query,
  });
}
//流程设计-详情
export function queryDetail(query) {
  return request({
    url: "/ledger/flow/flowDetail",
    method: "get",
    params: query,
  });
}
//流程设计-配置详情
export function queryNodeConfig(query) {
  return request({
    url: "/ledger/flow/getNodeBind",
    method: "get",
    params: query,
  });
}
//流程设计-保存配置
export function saveNodeConfig(query) {
  return request({
    url: "/ledger/flow/nodeBind",
    method: "post",
    data: query,
  });
}
//流程设计-获取已绑定表单
export function queryBindForm(query) {
  return request({
    url: "/ledger/flow/getAllBindForms",
    method: "get",
    params: query,
  });
}
