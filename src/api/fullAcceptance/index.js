import request from "@/utils/request";
export function queryAcceptanceProjectInfoByPage(data) {
    return request({
        url: "/acceptance/queryAcceptanceProjectInfoByPage",
        method: "post",
        data
    });
}
export function queryProjectInfoByPagee(data) {
    return request({
        url: "/project/queryProjectInfoByPage",
        method: "post",
        data
    });
}
export function acceptanceAdd(data) {
    return request({
        url: "/acceptance/add",
        method: "post",
        data
    });
}

export function commitAccept(data) {
    return request({
        url: "/acceptance/commitAccept",
        method: "post",
        data
    });
}