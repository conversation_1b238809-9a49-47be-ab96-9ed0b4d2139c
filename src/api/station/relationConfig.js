import request from "@/utils/request";

// 站点责任人配置
//未配置列表查询
export function queryLeftList(data) {
  return request({
    url: "/relation/getNotConfigByUserId",
    method: "post",
    data: data,
  });
}
//已配置列表查询
export function queryRightList(data) {
  return request({
    url: "/relation/getConfigByUserId",
    method: "post",
    data: data,
  });
}

//配置站点
export function setStationConfig(data) {
  return request({
    url: "/relation/addStationRelation",
    method: "post",
    data: data,
  });
}

//配置全部站点
export function setAllConfig(data) {
  return request({
    url: "/relation/addAll",
    method: "post",
    data: data,
  });
}

//清空全部站点
export function clearAllConfig(data) {
  return request({
    url: "/relation/removeAll",
    method: "post",
    data: data,
  });
}

//移除站点
export function clearConfig(data) {
  return request({
    url: "/relation/removeStationRelation",
    method: "post",
    data: data,
  });
}
