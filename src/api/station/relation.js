import request from "@/utils/request";

// 查询站点列表
export function queryStationRelationPage(data) {
  return request({
    url: "/relation/queryStationRelationPage",
    method: "post",
    data: data,
  });
}

export function getStationRelationsByUserId(data) {
  return request({
    url: "/relation/getTreeStationRelationsByUserId",
    method: "get",
    params: data,
  });
}

export function saveStationRelation(data) {
  return request({
    url: "/relation/saveStationRelation",
    method: "post",
    data: data,
  });
}
