import request from "@/utils/request";

// 查询站点列表
export function saveProvider(data) {
  return request({
    url: "/provider/add",
    method: "post",
    data: data
  });
}


export function providerList(data) {
  return request({
    url: "/provider/list",
    method: "get",
    params: data
  });
}

export function handleDel(data) {
  return request({
    url: "/provider/remove",
    method: "post",
    data: data
  });
}



export function providerDetail(data) {
  return request({
    url: "/provider/detail",
    method: "get",
    params: data
  });
}