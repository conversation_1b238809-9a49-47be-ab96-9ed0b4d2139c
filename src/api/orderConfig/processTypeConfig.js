import request from "@/utils/processRequest";

// 新增
export function add(data) {
  return request({
    url: "/external/api/v1/flwExtendFlowType/add",
    method: "post",
    data: data
  });
}

export function update(data) {
  return request({
    url: "/external/api/v1/flwExtendFlowType/update",
    method: "post",
    data: data
  });
}

export function deleteFunc(query) {
  return request({
    url: "/external/api/v1/flwExtendFlowType/delete",
    method: "get",
    params: query
  });
}

export function list(query) {
  return request({
    url: "/external/api/v1/flwExtendFlowType/list",
    method: "get",
    params: query
  });
}
