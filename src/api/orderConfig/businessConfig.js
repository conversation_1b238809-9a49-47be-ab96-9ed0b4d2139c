import request from "@/utils/processRequest";

// 新增
export function add(data) {
  return request({
    url: "/external/api/v1/flwExtendTaskCompensation/add",
    method: "post",
    data: data
  });
}

export function update(data) {
  return request({
    url: "/external/api/v1/flwExtendTaskCompensation/update",
    method: "post",
    data: data
  });
}

export function deleteFunc(query) {
  return request({
    url: "/external/api/v1/flwExtendTaskCompensation/delete",
    method: "get",
    params: query
  });
}

export function list(query) {
  return request({
    url: "/external/api/v1/flwExtendTaskCompensation/list",
    method: "get",
    params: query
  });
}

