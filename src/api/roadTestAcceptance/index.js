import request from "@/utils/request";

// 查询待路测列表
export function queryProjectInfoByPage(data) {
  return request({
    url: "/project/queryProjectInfoByPage",
    method: "post",
    data: data
  });
}
export function getUserList(data) {
  return request({
    url: "/system/user/list",
    method: "post",
    data: data
  });
}
export function queryPileInfoByPage(data) {
  return request({
    url: "/chargingPile/queryPileInfoNotInFlowByPage",
    method: "post",
    data: data
  });
}
export function editPileInfo(data) {
  return request({
    url: "/chargingPile/editPileInfo",
    method: "post",
    data: data
  });
}
/**
 * 开始路测
 */
export function addRoadTest(data) {
  return request({
    url: "/roadtest/add",
    method: "post",
    data: data
  });
}
export function businessList(query) {
  return request({
    url: "/business/list",
    method: "get",
    params: query
  });
}
