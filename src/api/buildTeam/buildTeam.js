import request from "@/utils/request";

// 查询施工队列表
export function queryBuildTeamByPage(data) {
  return request({
    // url: "/buildTeam/queryBuildTeamByPage",
    url: "/construct/manager/page",
    method: "post",
    data: data,
  });
}

// 查询施工队信息
export function queryBuildTeamById(data) {
  return request({
    url: "/buildTeam/queryBuildTeamById",
    method: "post",
    data: data,
  });
}

// 新增施工队
export function addBuildTeam(data) {
  return request({
    url: "/buildTeam/addBuildTeam",
    method: "post",
    data: data,
  });
}

// 修改施工队
export function updateBuildTeam(data) {
  return request({
    url: "/buildTeam/updateBuildTeam",
    method: "post",
    data: data,
  });
}

// 删除施工队
export function deleteBuildTeam(data) {
  return request({
    url: "/buildTeam/deleteBuildTeam",
    method: "post",
    data: data,
  });
}
