import request from "@/utils/request";
//计划配置
// 列表查询
export function queryPlanList(data) {
  return request({
    url: "/plan/queryConfigList",
    method: "post",
    data: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "/plan/updateConfigStatus",
    method: "post",
    data: data,
  });
}
//日志
export function queryLog(data) {
  return request({
    url: "group/manage/changeStatus",
    method: "post",
    data: data,
  });
}
//导出
export function exportExcel(data) {
  return request({
    url: "/plan/exportConfig",
    method: "post",
    params: data,
  });
}

//配置站点
//获取已选择站点
export function querySelectedList(data) {
  return request({
    url: "/plan/selectStationByIds",
    method: "post",
    data: data,
  });
}
//获取未选择站点
export function queryUnselectedStation(data) {
  return request({
    url: "/plan/queryStationByCounty",
    method: "post",
    data: data,
  });
}
//配置站点
export function submitStation(data) {
  return request({
    url: "/plan/saveStationOrUpdate",
    method: "post",
    data: data,
  });
}
//删除站点
export function deleteStation(data) {
  return request({
    url: "/plan/deleteStationByIds",
    method: "post",
    data: data,
  });
}
//配置检查组
//获取所有检查组
export function queryAllCheckList(data) {
  return request({
    url: "/checkGroup/list",
    method: "post",
    data: data,
  });
}
//获取已配置检查组
export function queryDetail(data) {
  return request({
    url: "/plan/configList",
    method: "get",
    params: data,
  });
}
//保存配置
export function submitPlanConfig(data) {
  return request({
    url: "/plan/saveConfigOrUpdate",
    method: "post",
    data: data,
  });
}
