import request from "@/utils/request";
//检查组
// 列表查询
export function queryGroupList(data) {
  return request({
    url: "/checkGroup/list",
    method: "post",
    data: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "/checkGroup/changeStatus",
    method: "post",
    data: data,
  });
}
//删除
export function deleteGroup(data) {
  return request({
    url: "/checkGroup/delete",
    method: "get",
    params: data,
  });
}
//复制
export function copyGroup(data) {
  return request({
    url: "/checkGroup/copy",
    method: "post",
    data: data,
  });
}
//检查项明细
export function queryItemDetail(data) {
  return request({
    url: "/checkGroup/getConfig",
    method: "post",
    data: data,
  });
}
//详情
export function queryDetail(data) {
  return request({
    url: "/checkGroup/detail",
    method: "get",
    params: data,
  });
}
//新增/修改详情
export function submitForm(data) {
  return request({
    url: "/checkGroup/save",
    method: "post",
    data: data,
  });
}
//获取业务类型下的一级工单类型（下拉选项）
export function queryFirstOrderType(data) {
  return request({
    url: "/orderType/fistLevelList",
    method: "get",
    params: data,
  });
}
// 检查项配置
//未配置列表查询
export function queryLeftList(data) {
  return request({
    url: "/checkGroup/getNotConfig",
    method: "post",
    data: data,
  });
}
//已配置列表查询
export function queryRightList(data) {
  return request({
    url: "/checkGroup/getConfig",
    method: "post",
    data: data,
  });
}

//配置
export function setStationConfig(data) {
  return request({
    url: "/checkGroup/addCheckItem",
    method: "post",
    data: data,
  });
}

//配置全部
export function setAllConfig(data) {
  return request({
    url: "/checkGroup/addAllCheckItem",
    method: "post",
    data: data,
  });
}

//清空全部
export function clearAllConfig(data) {
  return request({
    url: "/checkGroup/removeAllCheckItem",
    method: "post",
    data: data,
  });
}

//移除
export function clearConfig(data) {
  return request({
    url: "/checkGroup/removeCheckItem",
    method: "post",
    data: data,
  });
}
//设置排序
export function setSequence(data) {
  return request({
    url: "/checkGroup/sortCheckItem",
    method: "post",
    data: data,
  });
}
