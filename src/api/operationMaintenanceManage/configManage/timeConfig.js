import request from "@/utils/request";
//时效配置
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/aging/config/page",
      method: "post",
      data: data,
    });
  },

  // 删除
  deleteData(query) {
    return request({
      url: "/aging/config/delete",
      method: "post",
      data: query,
    });
  },
  // 导出
  export(query) {
    return request({
      url: "/checkItemDef/update",
      method: "post",
      data: query,
    });
  },
  //保存/新增
  update(query) {
    return request({
      url: "/aging/config/save",
      method: "post",
      data: query,
    });
  },
  //站点详情
  stationDetailList(query) {
    return request({
      url: "/app/station/queryStationDetail",
      method: "post",
      data: query,
    });
  },
  //配置站点
  //查询左侧
  queryLeftList(query) {
    return request({
      url: "/aging/config/pageUnallocatedStation",
      method: "post",
      data: query,
    });
  },
  //查询右侧
  queryRightList(query) {
    return request({
      url: "/aging/config/pageAssignedStation",
      method: "post",
      data: query,
    });
  },
  //一键清空
  clearAllConfig(query) {
    return request({
      url: "/aging/config/removeSiteAll",
      method: "post",
      data: query,
    });
  },
  //一键配置
  setAllConfig(query) {
    return request({
      url: "/aging/config/configureSiteAll",
      method: "post",
      data: query,
    });
  },
  //配置站点
  setStationConfig(query) {
    return request({
      url: "/aging/config/configureSite",
      method: "post",
      data: query,
    });
  },
  //清除站点
  clearConfig(query) {
    return request({
      url: "/aging/config/removeSite",
      method: "post",
      data: query,
    });
  },
};
