// import request from "@/utils/processRequest";
import request from "@/utils/request";
// ----------------------------表单设计接口
// 新增
export function add(query) {
  return request({
    url: "/checkItemDef/add",
    method: "post",
    data: query,
  });
}

// 编辑
export function update(query) {
  return request({
    url: "/checkItemDef/update",
    method: "post",
    data: query,
  });
}

//获取key
export function getKey(query) {
  return request({
    url: "/formdef/key",
    method: "get",
    params: query,
  });
}
//列表
export function list(query) {
  return request({
    url: "/checkItemDef/list",
    method: "get",
    params: query,
  });
}

//表单设计-保存
export function formSave(query) {
  return request({
    url: "/form/save",
    method: "post",
    data: query,
  });
}

//修改状态
export function modifyStatus(query) {
  return request({
    url: "/form/modifyStatus",
    method: "get",
    params: query,
  });
}

//复制
export function copy(query) {
  return request({
    url: "/form/copy",
    method: "get",
    params: query,
  });
}

//删除
export function deleteItem(query) {
  return request({
    url: "/form/delete",
    method: "get",
    params: query,
  });
}

//外层删除
export function deleteformdef(query) {
  return request({
    url: "/checkItemDef/delete",
    method: "get",
    params: query,
  });
}

//获取表单数据
export function getFormData(query) {
  return request({
    url: "/form/data",
    method: "get",
    params: query,
  });
}

//表单设计-复制(复制多版本)
export function copyFormDef(query) {
  return request({
    url: "/checkItemDef/copyFormDef",
    method: "post",
    data: query,
  });
}
//获取表单数据
export function queryVersionList(query) {
  return request({
    url: "/form/list",
    method: "get",
    params: query,
  });
}
//获取表单图片上传地址
export function getAppUploadPath() {
  let baseUrl = window.location.hostname.includes(
    process.env.VUE_APP_BASE_API_PREFIX_INTERNAL
  )
    ? process.env.VUE_APP_BASE_API_INTERNAL
    : process.env.VUE_APP_BASE_API_EXTERNAL;
  return { data: baseUrl + process.env.VUE_APP_BASE_UPLOAD_URL };
}
//表单设计-业务属性
export function submitBusiness(query) {
  return request({
    url: "/checkItemDef/saveBusinessAttribute",
    method: "post",
    data: query,
  });
}
//获取检查项属性下啦选项
export function queryPropOptions(query) {
  return request({
    url: "/check/item/attributeNameList",
    method: "get",
    params: query,
  });
}
