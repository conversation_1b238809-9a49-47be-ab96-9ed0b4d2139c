import request from "@/utils/request";
//区域配置
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/region/org/list",
      method: "post",
      data: data,
    });
  },
  //编辑时根据能投大区获取未配置的城市树
  queryFilterCity(data) {
    return request({
      url: "/region/org/regionTree",
      method: "get",
      params: data,
    });
  },
  //查看已绑定的省市区详情
  cityDetail(data) {
    return request({
      url: "/region/org/detail",
      method: "get",
      params: data,
    });
  },
  //新增
  add(query) {
    return request({
      url: "/region/org/add",
      method: "post",
      data: query,
    });
  },
  //编辑
  update(query) {
    return request({
      url: "/region/org/edit",
      method: "post",
      data: query,
    });
  },
  getPermiDeptList(data) {
    return request({
      url: "/region/org/deptList",
      method: "get",
      params: data,
    });
  },
};
