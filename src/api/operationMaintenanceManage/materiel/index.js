import request from "@/utils/request";
//物料管理
export default {
  // 列表查询
  getTableData(data) {
    return request({
      url: "/material/list",
      method: "post",
      data: data,
    });
  },
  // 修改
  update(data) {
    return request({
      url: "/material/saveMaterialOrUpdate",
      method: "post",
      data: data,
    });
  },
  //状态切换
  changeStatus(data) {
    return request({
      url: "/material/updateMaterialStatus",
      method: "post",
      data: data,
    });
  },
  // 删除
  delete(data) {
    return request({
      url: "/material/deleteMaterialInfo",
      method: "get",
      params: data,
    });
  },
  //导出
  exportReport(data) {
    return request({
      url: "/material/export",
      method: "post",
      data: data,
    });
  },
};
