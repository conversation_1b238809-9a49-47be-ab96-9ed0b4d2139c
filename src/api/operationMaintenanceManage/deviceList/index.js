import request from "@/utils/request";
//设备列表查询
export function queryGunList(data) {
  return request({
    url: "/om/platform/gun/list",
    method: "get",
    params: data,
  });
}
//导出
export function exportGunExcel(data) {
  return request({
    url: "/om/platform/gun/list/export",
    method: "post",
    data: data,
  });
}
//充电平台品牌列表
export function queryBrandOpts(data) {
  return request({
    url: "/platform/brandList",
    method: "get",
    params: data,
  });
}
//充电平台设备型号列表接口
export function queryModelOpts(data) {
  return request({
    url: "/platform/gun/modelList",
    method: "get",
    params: data,
  });
}
//充电平台字典接口
export function queryChargingDict(data) {
  return request({
    url: "/platform/dictData",
    method: "get",
    params: data,
  });
}
// 状态日志列表
export function queryStatusLog(data) {
  return request({
    url: "/platform/gun/runLog",
    method: "post",
    data: data,
  });
}
// 状态日志导出
export function exportLogExcel(data) {
  return request({
    url: "/platform/gun/runLog/export",
    method: "post",
    data: data,
  });
}
// 修改质保
export function saveWarranty(data) {
  return request({
    url: "/brand/warranty/updateBrandWarrantyDevice",
    method: "post",
    data: data,
  });
}
//质保供应商查询（修改质保）
export function queryDeviceModelDict(data) {
  return request({
    url: "/brand/warranty/queryWarrantyBusinessList",
    method: "get",
    params: data,
  });
}
//充电枪详情-基本信息
export function queryGunInfo(data) {
  return request({
    url: "/platform/gun/detail",
    method: "post",
    data: data,
  });
}
//充电枪详情-日志记录
export function queryGunRecord(data) {
  return request({
    url: "/platform/gun/runLog",
    method: "post",
    data: data,
  });
}
