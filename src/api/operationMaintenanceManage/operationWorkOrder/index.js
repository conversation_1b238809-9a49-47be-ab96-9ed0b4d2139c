import request from "@/utils/request";

// 新增
export function pileGunList(data) {
  return request({
    url: "/platform/pile/gun",
    method: "post",
    data: data,
  });
}

// 列表查询
export function queryList(data) {
  return request({
    url: "/maintenance/queryList",
    method: "post",
    data: data,
  });
}
//新增/编辑工单
export function saveOrder(data) {
  return request({
    url: "/maintenance/saveOrderInfo",
    method: "post",
    data: data,
  });
}
//编辑工单-详情
export function queryDetail(data) {
  return request({
    url: "/maintenance/getById",
    method: "get",
    params: data,
  });
}
//工单详情页
export function orderDetail(data) {
  return request({
    url: "/maintenance/orderDetail",
    method: "get",
    params: data,
  });
}
export function exportExcel(data) {
  return request({
    url: "/maintenance/export",
    method: "post",
    data: data,
  });
}
export function check(data) {
  return request({
    url: "/maintenance/auditOrder",
    method: "post",
    data: data,
  });
}

export function transferOrder(data) {
  return request({
    url: "/maintenance/transferOrder",
    method: "post",
    data: data,
  });
}

export function pushMsg(data) {
  return request({
    url: "/maintenance/pushMsg",
    method: "post",
    data: data,
  });
}

export function stopOrder(data) {
  return request({
    url: "/maintenance/stopOrder",
    method: "post",
    data: data,
  });
}

export function updateOrderHandleTag(data) {
  return request({
    url: "/maintenance/updateOrderHandleTag",
    method: "post",
    data: data,
  });
}

//详情-流转记录
export function orderRecord(data) {
  return request({
    url: "/maintenance/orderRecord",
    method: "post",
    data: data,
  });
}

export function createErrorOrder(data) {
  return request({
    url: "/maintenance/createErrorOrder",
    method: "post",
    data: data,
  });
}
export function fistLevelList(data) {
  return request({
    url: "/orderType/fistLevelList",
    method: "get",
    params: data,
  });
}
//根据父级工单类型获取子级（单选）
export function childrenList(data) {
  return request({
    url: "/orderType/childrenList",
    method: "get",
    params: data,
  });
}
//根据父级工单类型获取子级（多选）
export function childrenMultiList(data) {
  return request({
    url: "/orderType/getChildrenList",
    method: "get",
    params: data,
  });
}
//修改备注
export function submitRemark(data) {
  return request({
    url: "/maintenance/remarkOrder",
    method: "post",
    data: data,
  });
}

export function getDeptList(data) {
  return request({
    url: "/dept/list",
    method: "get",
    params: data,
  });
}
export function getAllDeptList(data) {
  return request({
    url: "/dept/all/list",
    method: "get",
    params: data,
  });
}
//故障详情接口
export function faultDetail(data) {
  return request({
    url: "/maintenance/getDeviceAlarmDetail",
    method: "get",
    params: data,
  });
}
//提交半径
export function submitRadius(data) {
  return request({
    url: "/maintenance/editRadius",
    method: "post",
    data: data,
  });
}
//接单
export function receiveOrder(data) {
  return request({
    url: "/maintenance/receive",
    method: "get",
    params: data,
  });
}
//取消
export function submitCancel(data) {
  return request({
    url: "/maintenance/cancel",
    method: "post",
    data: data,
  });
}
//获取权限范围的一级工单类型
export function queryBindOrderTypeList(data) {
  return request({
    url: "/user/permission/order/getBindOrderTypeOneList",
    method: "get",
    params: data,
  });
}
//获取工单编号
export function getNewOrgNo(data) {
  return request({
    url: "/maintenance/orderNo",
    method: "get",
    params: data,
  });
}
//工单报告
export function getReport(data) {
  return request({
    url: "/maintenance/getReport",
    method: "get",
    params: data,
  });
}
//租户下拉选项
export function queryPlatformMerchant(data) {
  return request({
    url: "/maintenance/platformMerchantList",
    method: "get",
    params: data,
  });
}
//转派-人员列表
export function queryTransferUser(data) {
  return request({
    url: "/maintenance/transferUserList",
    method: "get",
    params: data,
  });
}
//新增-站点列表-左侧城市树
export function getCityTree(data) {
  return request({
    url: "/maintenance/getPermissCityTree",
    method: "get",
    params: data,
  });
}
//新增-全部站点列表
export function getStationList(data) {
  return request({
    url: "/maintenance/getPermissStationList",
    method: "get",
    params: data,
  });
}
//新增-处理人列表- 获取左侧部门树
export function getDeptTree(data) {
  return request({
    url: "/maintenance/getPermissOrgTree",
    method: "get",
    params: data,
  });
}
//新增-处理人列表
export function getHandleUserList(data) {
  return request({
    url: "/maintenance/getPermissUserList",
    method: "get",
    params: data,
  });
}
//新增-全部设备列表
export function getDeviceList(data) {
  return request({
    url: "/maintenance/getPermissDeviceList",
    method: "get",
    params: data,
  });
}
//新增-保存设备
export function saveDevice(data) {
  return request({
    url: "/maintenance/saveOrderDevice",
    method: "post",
    data: data,
  });
}
//新增-获取已勾选的设备列表（调试工单）
export function querySelectedDeviceList(data) {
  return request({
    url: "/maintenance/orderDeviceList",
    method: "post",
    data: data,
  });
}
//新增-保存设备列表（调试工单）
export function saveDeviceList(data) {
  return request({
    url: "/maintenance/saveOrderDevice",
    method: "post",
    data: data,
  });
}
//新增-删除设备（调试工单）
export function deleteDevice(data) {
  return request({
    url: "/maintenance/removeOrderDevice",
    method: "get",
    params: data,
  });
}
//新增-删除全部设备（调试工单）
export function deleteAllDevice(data) {
  return request({
    url: "/maintenance/removeAllOrderDevice",
    method: "get",
    params: data,
  });
}
//处理工单-详情
export function handleDetail(data) {
  return request({
    url: "/maintenance/handleDetail",
    method: "post",
    data: data,
  });
}
//处理工单-保存
export function handleOrder(data) {
  return request({
    url: "/maintenance/handleOrder",
    method: "post",
    data: data,
  });
}
//处理工单-获取工勘工单表单数据
export function queryHandleForm(data) {
  return request({
    url: "/maintenance/customizeCheckItems",
    method: "get",
    params: data,
  });
}

// 查询工单报告列表
export function queryOrderReportlist(data) {
  return request({
    url: "/report/queryReportList",
    method: "post",
    data: data,
  });
}

// 导出
export function exportReport(data) {
  return request({
    url: "/report/export",
    method: "post",
    data: data,
  });
}

export function getReportPDF(orderNo) {
  return request({
    url: "/report/getReport",
    method: "get",
    params: { orderNo },
  });
}
