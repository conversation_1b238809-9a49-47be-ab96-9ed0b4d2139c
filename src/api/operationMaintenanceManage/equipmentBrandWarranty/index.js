import request from "@/utils/request";
//列表查询
export default {
  queryBrandWarrantyList(data) {
    return request({
      url: "/brand/warranty/queryBrandWarrantyList",
      method: "post",
      data,
    });
  },
  //删除
  deleteBrandWarranty(data) {
    return request({
      url: "/brand/warranty/deleteBrandWarranty",
      method: "post",
      data: data,
    });
  },
  //设备品牌质保保存(新增)
  saveBrandWarranty(data) {
    return request({
      url: "/brand/warranty/saveBrandWarranty",
      method: "post",
      data,
    });
  },
  //设备品牌质保编辑
  updateBrandWarranty(data) {
    return request({
      url: "/brand/warranty/updateBrandWarranty",
      method: "post",
      data,
    });
  },
  //查询设备品牌（新增）
  queryABrandList() {
    return request({
      url: "/om/platform/brand/queryABrandList",
      method: "get",
      params: {},
    });
  },
  //根据品牌ID查询设备型号（新增）
  queryModelNameById(data) {
    return request({
      url: "/om/platform/brand/queryModelNameById",
      method: "get",
      params: data,
    });
  },
  //查询品牌名称列表（品牌质保）
  queryBrandOptions(data) {
    return request({
      url: "/brand/warranty/queryBrandNameList",
      method: "get",
      params: data,
    });
  },
  //查询设备型号列表（品牌质保）
  queryModelOptions(data) {
    return request({
      url: "/brand/warranty/queryModelNameList",
      method: "post",
      data: data,
    });
  },
};
