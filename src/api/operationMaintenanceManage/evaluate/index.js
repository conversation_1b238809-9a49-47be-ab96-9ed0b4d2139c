import request from "@/utils/request";

// 新增评价配置
export function addEvaluate(data) {
  return request({
    url: "/comment/saveCommentInfo",
    method: "post",
    data: data,
  });
}

// 评价配置列表
export function queryEvaluateList(data) {
  return request({
    url: "/comment/selectCommentInfo",
    method: "post",
    data: data,
  });
}
// 修改评价配置状态
export function changeEvaluateStatus(data) {
  return request({
    url: "/comment/updateCommentStatus",
    method: "post",
    data: data,
  });
}
// 更新评价配置
export function updateEvaluate(data) {
  return request({
    url: "/comment/updateCommentInfo",
    method: "post",
    data: data,
  });
}
// 导出评价配置
export function exportEvaluatelist(data) {
  return request({
    url: "/comment/exportComment",
    method: "post",
    data,
  });
}
// 删除评价配置
export function delelteEvaluate(data) {
  return request({
    url: "/comment/deleteCommentInfo",
    method: "post",
    data,
  });
}

// 查询运维工单的评价
export function queryOperationWorkOrderEvaluate(data) {
  return request({
    url: "/comment/queryCommentByOrderId",
    method: "get",
    params: data,
  });
}

// 保存运维工单的评价
export function saveOperationWorkOrderEvaluate(data) {
  return request({
    url: "/comment/saveOrderComment",
    method: "post",
    data: data,
  });
}
