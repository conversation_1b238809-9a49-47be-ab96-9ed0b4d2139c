import request from "@/utils/request";

// 站点详情接口
//基本信息
export function queryStationBaseInfo(data) {
  return request({
    url: "/station/basic/detail",
    method: "get",
    params: data,
  });
}
//项目信息
export function queryProjectBaseInfo(data) {
  return request({
    url: "/station/projectInfo/detail",
    method: "get",
    params: data,
  });
}
//项目tab
export function queryProjectTabList(data) {
  return request({
    url: "/station/projectInfo",
    method: "get",
    params: data,
  });
}
//订单信息
export function queryOrderBaseInfo(data) {
  return request({
    url: "/charge/order/chargeOrderStatistics",
    method: "get",
    params: data,
  });
}
//充电桩信息
export function queryPileBaseInfo(data) {
  return request({
    url: "/station/pile/detail",
    method: "get",
    params: data,
  });
}
//异常记录
export function queryFaultRecord(data) {
  return request({
    url: "/charge/order/getCmErrorReportByStation",
    method: "get",
    params: data,
  });
}
