import request from "@/utils/request";

// 查询站点列表
export function queryStationInfoByPage(data) {
  return request({
    url: "/station/queryStationInfoByPage",
    method: "post",
    data: data,
  });
}
// 导出
export function exportExcel(data) {
  return request({
    url: "/station/export",
    method: "post",
    data: data,
  });
}
// 标签日志
export function queryLogList(data) {
  return request({
    url: "/stationTag/log/list",
    method: "post",
    data: data,
  });
}
// 查询重复站点
export function queryRepeatStation(data) {
  return request({
    url: "/station/queryRepeatStation",
    method: "post",
    data: data,
  });
}

// 保存站点标签
export function saveStationTag(data) {
  return request({
    url: "/station/saveStationTag",
    method: "post",
    data: data,
  });
}

// 查询已选择的站点标签
export function getSelectedTag(data) {
  return request({
    url: "/stationTag/list",
    method: "post",
    data: data,
  });
}
// 减少站点标签
export function minusStationTag(data) {
  return request({
    url: "/stationTag/minus",
    method: "post",
    data: data,
  });
}
// 增加站点标签
export function addStationTag(data) {
  return request({
    url: "/stationTag/add",
    method: "post",
    data: data,
  });
}
// 查询标签列表
export function getTagList(data) {
  return request({
    url: "/tag/list",
    method: "post",
    data: data,
  });
}
//配置站点等级
export function submitEditRowLevel(data) {
  return request({
    url: "/stationGrade/config",
    method: "post",
    data: data,
  });
}
//查询站点运维类型详情
export function queryTypeDetail(data) {
  return request({
    url: "/station/type/details",
    method: "get",
    params: data,
  });
}
//保存运维类型
export function saveContract(data) {
  return request({
    url: "/station/type/save",
    method: "post",
    data: data,
  });
}
// 查看运维合同
export function contractFileList(data) {
  return request({
    url: "/doc/listDoc",
    method: "get",
    params: data,
  });
}
// 删除运维合同
export function deleteContractFile(data) {
  return request({
    url: "/doc/removeBatch",
    method: "get",
    params: data,
  });
}
//能投大区数据核验
//列表查询
export function queryCheckStationList(data) {
  return request({
    url: "/verify/result",
    method: "post",
    data: data,
  });
}
//更新
export function updateCheckStation(data) {
  return request({
    url: "/verify/saveVerifyRecord ",
    method: "post",
    data: data,
  });
}
//能投大区数据核验记录
//列表查询
export function queryCheckRecord(data) {
  return request({
    url: "/verify/list",
    method: "get",
    params: data,
  });
}
//导出
export function exportRecordExcel(data) {
  return request({
    url: "/verify/export",
    method: "post",
    data: data,
  });
}
//站点运维等级
//列表查询
export function queryLevelList(data) {
  return request({
    url: "/stationGrade/list",
    method: "post",
    data: data,
  });
}
// 日志
export function queryLevelLog(data) {
  return request({
    url: "/stationGrade/log/list",
    method: "get",
    params: data,
  });
}
//新增
export function submitAddLevel(data) {
  return request({
    url: "/stationGrade/save",
    method: "post",
    data: data,
  });
}
//修改
export function submitEditLevel(data) {
  return request({
    url: "/stationGrade/update",
    method: "post",
    data: data,
  });
}
