import request from "@/utils/request";
//充电订单 列表查询
export function queryOrderList(data) {
  return request({
    url: "/om/charge/order/pageList",
    method: "post",
    data: data,
  });
}
//充电订单 导出
export function exportExcel(data) {
  return request({
    url: "/om/charge/order/pageList/export",
    method: "post",
    data: data,
  });
}
// 订单详情-桩连平台日志列表
export function queryPileLog(data) {
  return request({
    url: "/om/charge/order/pileLog",
    method: "get",
    params: data,
  });
} // 订单详情-桩连平台日志 导出
export function exportLogExcel(data) {
  return request({
    url: "/om/charge/order/pileLog/export",
    method: "post",
    data: data,
  });
}
//充电订单详情-订单详情
export function queryOrderInfo(data) {
  return request({
    url: "/charge/order/detail",
    method: "get",
    params: data,
  });
}
//充电订单详情-异常记录
export function queryOrderRecord(data) {
  return request({
    url: "/charge/order/getOrderErrorList",
    method: "get",
    params: data,
  });
}
//充电订单详情-充电数据
export function queryChargingInfo(data) {
  return request({
    url: "/charge/order/getChargingData",
    method: "get",
    params: data,
  });
}
//充电订单详情-操作记录
export function queryHandleRecord(data) {
  return request({
    url: "/charge/order/getControlLog",
    method: "get",
    params: data,
  });
}
