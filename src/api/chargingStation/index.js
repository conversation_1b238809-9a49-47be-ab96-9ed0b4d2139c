import request from "@/utils/request";

// 充电桩接口
//列表查询
export function queryStationList(data) {
  return request({
    url: "/platform/pile/list",
    method: "post",
    data,
  });
}
//导出
export function exportStationExcel(data) {
  return request({
    url: "/platform/export/pile",
    method: "post",
    data: data,
  });
}
//充电平台品牌列表
export function queryBrandOpts(data) {
  return request({
    url: "/platform/brandList",
    method: "get",
    params: data,
  });
}
//充电平台设备型号列表接口
export function queryModelOpts(data) {
  return request({
    url: "/platform/modelList",
    method: "get",
    params: data,
  });
}
//充电平台字典接口
export function queryChargingDict(data) {
  return request({
    url: "/platform/dictData",
    method: "get",
    params: data,
  });
}
//充电桩详情-基本信息
export function queryPileInfo(data) {
  return request({
    url: "/platform/pile/detail",
    method: "post",
    data: data,
  });
}
//充电桩详情-异常纪录
export function queryPileRecord(data) {
  return request({
    url: "/error/push/queryErrorInfoByEquipNo",
    method: "post",
    data: data,
  });
}
//充电桩详情-异常统计
export function queryPileStatistics(data) {
  return request({
    url: "/error/push/countErrorReport",
    method: "post",
    data: data,
  });
}
//充电枪
//列表查询
export function queryGunList(data) {
  return request({
    url: "/platform/gun/list",
    method: "post",
    data: data,
  });
}
//导出
export function exportGunExcel(data) {
  return request({
    url: "/platform/export/gun",
    method: "post",
    data: data,
  });
}
//充电枪详情-基本信息
export function queryGunInfo(data) {
  return request({
    url: "/platform/gun/detail",
    method: "post",
    data: data,
  });
}
//充电枪详情-日志记录
export function queryGunRecord(data) {
  return request({
    url: "/platform/gun/runLog",
    method: "post",
    data: data,
  });
}

// 枪设备型号列表：  
export function queryGunModelList(data) {
  return request({
    url: "/platform/gun/modelList",
    method: "get",
    params: data,
  });
}