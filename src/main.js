import Vue from "vue";

import Cookies from "js-cookie";
import moment from "moment"; //导入文件

import "normalize.css/normalize.css"; // a modern alternative to CSS resets

import Element from "element-ui";
import "@/assets/styles/element-variables.less";
import "element-ui/lib/theme-chalk/index.css";
import "@/assets/styles/index.less"; // global css
import "@/assets/styles/comm.less"; // comm css
import "@/assets/styles/buse.less";
import "../theme/index.css";
import print from "./utils/print"; // 引入printJS

import App from "./App";
import store from "./store";
import router from "./router";
import permission from "./directive/permission";

import "./assets/icons"; // icon
import "./permission"; // permission control
import { getDicts, getTenantDicts, listData } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";

// Mock 系统初始化和ServiceWorker清理
import initMockSystem from "@/utils/mock-init";
import autoCleanup from "@/utils/sw-cleanup";

// 根据环境决定是否初始化Mock系统或清理ServiceWorker
if (
  process.env.NODE_ENV === "development" &&
  process.env.VUE_APP_ENABLE_MOCK === "true"
) {
  // 开发环境且启用Mock时，初始化Mock系统
  setTimeout(() => {
    initMockSystem();
  }, 1000);
} else {
  // 非开发环境或Mock未启用时，清理可能存在的ServiceWorker
  setTimeout(() => {
    autoCleanup();
  }, 500);
}

import {
  parseTime,
  resetForm,
  addDateRange,
  addDateRangeCreate,
  selectDictLabel,
  download,
  accAdd,
  mul,
  moneyFormat,
} from "@/utils/comm";
import { reportTrackEvent } from "@/utils/track/track.js";
import Pagination from "@/components/Pagination";
//自定义表格工具扩展
import RightToolbar from "@/components/RightToolbar";
import _ from "lodash";
import fileDownload from "js-file-download"; //文件下载插件
// html 转PDF
import htmlToPdf from "@/utils/htmlToPdf";

import dbClick from "@/directive/permission/dbClick.js";
import NP from "number-precision";
import { cloneDeep } from "lodash-es";

import Video from "video.js";
import "video.js/dist/video-js.css";
Vue.prototype.$video = Video;
window.$deepClone = cloneDeep;
Vue.prototype.$EventBus = new Vue();

import * as echarts from "echarts";
Vue.prototype.$echarts = echarts; //构造全局实例

// 全局方法挂载
Vue.prototype.getDicts = getDicts;
Vue.prototype.listData = listData;
Vue.prototype.getConfigKey = getConfigKey;
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.addDateRangeCreate = addDateRangeCreate;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.download = download;
Vue.prototype.accAdd = accAdd;
Vue.prototype.mul = mul;
Vue.prototype.moneyFormat = moneyFormat;
Vue.prototype._ = _;
Vue.prototype.moment = moment; //时间转化插件
Vue.prototype.fileDownload = fileDownload; //文件下载插件
Vue.prototype.NP = NP; //数字精度
Vue.prototype.reportTrackEvent = reportTrackEvent;

Vue.prototype.msgSuccess = function(msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
};

Vue.prototype.msgError = function(msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
};

Vue.prototype.msgInfo = function(msg) {
  this.$message.info(msg);
};

// 全局组件挂载
Vue.component("Pagination", Pagination);
Vue.component("RightToolbar", RightToolbar);
Vue.component("print", print);

Vue.use(print);

//buse组件
import {
  AutoFilters,
  DynamicForm,
  BuseCrud,
} from "@bangdao/buse-components-element";
Vue.use(AutoFilters);
Vue.use(DynamicForm);
Vue.use(BuseCrud);

// 表单设计器
import FcDesigner from "@form-create/designer";
Vue.use(FcDesigner);
// form-create 表单生成器
import formCreate from "@form-create/element-ui";
Vue.use(formCreate);

import SignBoard from "@/components/FormCreate/components/esign/SignBoard";
formCreate.component("SignBoard", SignBoard);
import CameraPhotoUpload from "@/components/FormCreate/components/CameraPhotoUpload.vue";
Vue.component("CameraPhotoUpload", CameraPhotoUpload);
import Locate from "@/components/FormCreate/components/Locate.vue";
Vue.component("Locate", Locate);
import FormGroup from "@/components/FormCreate/components/group/FormGroup.vue";
Vue.component("FormGroup", FormGroup);
// import FormRow from "@/components/FormCreate/components/group/FormRow.vue";
// Vue.component('FormRow', FormRow)
import ProcessTableDesigner from "@/components/FormCreate/components/ProcessTableDesigner.vue";
// 全局注册
Vue.component("ProcessTableDesigner", ProcessTableDesigner);
// 注册到 formcreate（假设已引入 formcreate）
formCreate.component("ProcessTableDesigner", ProcessTableDesigner);

import MultiFileUpload from "@/components/FormCreate/components/MultiFileUpload.vue";
// 全局注册
Vue.component("MultiFileUpload", MultiFileUpload);
// 注册到 formcreate（假设已引入 formcreate）
formCreate.component("MultiFileUpload", MultiFileUpload);

Vue.use(permission);
Vue.use(htmlToPdf);
Vue.use(dbClick);

import VXETable from "vxe-table";
import "vxe-table/lib/style.css";
import VXETablePluginExportXLSX from "vxe-table-plugin-export-xlsx";
VXETable.use(VXETablePluginExportXLSX);
Vue.use(VXETable);

import CustomCascader from "@/components/CustomCascader/index.vue";
Vue.component("CustomCascader", CustomCascader);

import FileUpload from "@/components/Upload/fileUpload4.vue";
Vue.component("FileUpload", FileUpload);

import PageSelector from "@/components/PageSelector/index.vue";
Vue.component("PageSelector", PageSelector);

import PageAutocomplete from "@/components/PageSelector/PageAutocomplete.vue";
Vue.component("PageAutocomplete", PageAutocomplete);

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
Vue.component("TreeSelect", Treeselect);

import AMapLoader from "@amap/amap-jsapi-loader";
// 高德地图
window._AMapSecurityConfig = {
  securityJsCode: "af7108bd1090fd5ab151de6632a66b10",
};
AMapLoader.load({
  key: "8797c3ba8877a99a812e21fcc4c60ec0", // 申请好的Web端开发者Key，首次调用 load 时必填
  version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
  plugins: [
    "AMap.AutoComplete",
    "AMap.PlaceSearch",
    "AMap.Scale",
    "AMap.OverView",
    "AMap.Geolocation",
    "AMap.Geocoder",
    "AMap.ToolBar",
    "AMap.MapType",
    "AMap.PolyEditor",
    "AMap.CircleEditor",
    "AMap.MarkerClusterer",
    "AMap.ControlBar",
    "AMap.Riding", //骑行
    "AMap.Polygon", //多边形
    "AMap.Polyline",
    "AMap.CitySearch",
    "AMap.Weather",
    "AMap.OverlayGroup", //覆盖物组群
    "AMap.GeometryUtil", //计算两点间实际距离
    "AMap.MouseTool", //鼠标工具类
    "AMap.DistrictSearch", //行政区域
    "AMap.MassMarks", //海量点
    "AMap.LabelMarker", //海量标注
  ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
}).then((AMap) => {
  // 挂载AMap
  Vue.prototype.$AMap = AMap;
});

import AmapVue from "@amap/amap-vue";
AmapVue.config.version = "2.0"; // 默认2.0，这里可以不修改
AmapVue.config.key = "8797c3ba8877a99a812e21fcc4c60ec0";
AmapVue.config.plugins = [
  "AMap.AutoComplete",
  "AMap.PlaceSearch",
  "AMap.Scale",
  "AMap.OverView",
  "AMap.Geolocation",
  "AMap.Geocoder",
  "AMap.ToolBar",
  "AMap.MapType",
  "AMap.PolyEditor",
  "AMap.CircleEditor",
  "AMap.MarkerClusterer",
  "AMap.ControlBar",
  "AMap.Riding", //骑行
  "AMap.DistrictSearch", //行政区划
  "AMap.Polyline",
  "AMap.CitySearch",
  "AMap.Weather",
  "AMap.OverlayGroup", //覆盖物组群
  "AMap.GeometryUtil", //计算两点间实际距离
  "AMap.MouseTool", //鼠标工具类
  "AMap.DistrictSearch", //行政区域
  "AMap.MassMarks", //海量点
  "AMap.LabelMarker", //h海量标注
  // 在此配置你需要预加载的插件，如果不配置，在使用到的时候会自动异步加载
];
Vue.use(AmapVue);
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: Cookies.get("size") || "small", // set element-ui default size
});

Vue.config.productionTip = false;

//引入VueOfficeDocx组件
import VueOfficeDocx from "@vue-office/docx";
//引入相关样式
import "@vue-office/docx/lib/index.css";
Vue.use(VueOfficeDocx);
//引入VueOfficeExcel组件
import VueOfficeExcel from "@vue-office/excel";
//引入相关样式
import "@vue-office/excel/lib/index.css";
Vue.use(VueOfficeExcel);
//引入VueOfficePdf组件
import VueOfficePdf from "@vue-office/pdf";
Vue.use(VueOfficePdf);
new Vue({
  el: "#app",
  router,
  store,
  render: (h) => h(App),
});
