<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  data() {
    return {
      //开启缓存的页面整理如下（router.js中设置noCache:false且name需和页面的name保持一致）
      jumpPages: [
        "operationWorkOrderNew", //运维工单
        "maintenanceStationList", //站点档案/运维-站点信息
        "chargingStation", //充电桩
        "chargingGun", //充电枪
        "maintenanceChargingGun", //运维-设备信息
        "chargingOrder2", //运维-订单信息
        "chargingOrder", //充电订单
        "deviceList", //设备信息
        "pileList", //充电桩信息
        "constructionTeam", //施工队
        "providerManage", //服务商管理
        "errorPush", //异常上报信息
        "maintenanceErrorPush", //运维-异常信息
        "workOrderWorkbench", //工单工作台
        "projectManage", //项目管理【叫这个name的页面有多个】
        "demandPool", //需求池
        "sortingRules", //站点清分规则
        "checkGroupManage", //检查组
        // "checkItemsManage", //检查项
        "planConfigManage", //计划配置
        "accountPermissionManage", //账号权限
        // "addOperationWorkOrderNew", //创建工单
        // "investBatchList", //项目批次
        "operationWorkOrderNewAdd", //创建运维工单
        "operationWorkOrderNewEdit", //编辑运维工单
        "demandAdd", //需求池新增
        "demandEdit", //需求池编辑
        //工单台账-工单台账管理
        //工单台账-新增
        //工单台账-加单记录
        //工单台账-表单设计
        //工单台账-流程管理
        //工单台账-账号权限
        //工单台账-分组管理
        //工单台账-公司机构
        //工单台账-数据统计
        //工单台账-紧急程度
        //工单台账-部门管理
        //工单台账-公共邮箱
        //工单台账-工时管理
        //工程项目-待办
        //工程项目-场站项目列表
        //工程项目-项目投建批次
        //工程项目-项目时效统计
        //工程项目-项目成本
        //工程项目-账号权限
        //工程项目-时效配置
        //人员档案
        //信息档案-协议档案
        //信息档案-预警管理
        //信息档案-合作商档案
      ],
    };
  },
  computed: {
    cachedViews() {
      console.log(this.$store.state.tagsView.cachedViews);
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      console.log(this.$route, "----appmain");
      return this.$route.path;
    },
  },
};
</script>

<style lang="less" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 0px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="less">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    // padding-right: 15px;
  }
}
</style>
