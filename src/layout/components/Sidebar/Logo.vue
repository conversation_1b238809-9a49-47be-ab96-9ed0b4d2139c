<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <!-- <h1 class="sidebar-title">{{ title2 }}</h1> -->
        <img :src="imgUrlmini" alt="" class="logoImg" />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img :src="imgUrl" alt="" class="logoImg" />
        <!-- <h1 class="sidebar-title">{{ title }}</h1> -->
      </router-link>
    </transition>
  </div>
</template>

<script>
import store from "@/store";
import { mapState } from "vuex";

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState("app", ["tenantId"]),
  },
  data() {
    return {
      title: "二次供水",
      title2: "二供",
      imgUrl: require("@/assets/logo/logo.png"),
      imgUrlmini: require("@/assets/logo/logo.png"),
    };
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  // height: 100px;
  height: 50px;
  line-height: 100px;
  background: white;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    display: flex;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #666666;
      font-weight: 600;
      line-height: 50px;
      font-size: 20px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
  background: #02726a !important;
  // background: linear-gradient(91deg, #095755 43%, #1B6653 98%) !important;
}

.logoImg {
  // width: 40px;
  height: 20px;
  // margin-right: 10px;
}
/deep/.sidebar-logo-link {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
