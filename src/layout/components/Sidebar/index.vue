<template>
  <div :class="{ 'has-logo': showLogo }" class="wrap-border">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
        :default-openeds="defaultOpeneds"
      >
        <sidebar-item
          v-for="route in routers"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.less";

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters(["sidebarRouters", "sidebar"]),
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // ...mapGetters(['permission_routes', 'sidebar']),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
    defaultOpeneds() {
      // 这里返回你想要默认展开的菜单项的 key 数组
      return this.routers
        .filter((r) => r.children && r.children.length)
        .map((r) => r.path);
    },
  },
  created() {},
};
</script>

<style lang="less" scoped>
.wrap-border {
  // border-right: 1px solid #f0f2f5;
}
</style>
