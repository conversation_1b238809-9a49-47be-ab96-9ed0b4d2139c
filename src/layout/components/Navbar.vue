<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <!-- <div class="top-logo">
      <logo />
    </div> -->

    <!-- 菜单搜索框 -->
    <menu-search class="menu-search" placeholder="搜索菜单..." />

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" /> -->
    <!-- <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
      v-if="!topNav"
    /> -->
    <top-nav
      id="topmenu-container"
      class="breadcrumb-container"
      v-if="topNav"
    />

    <div class="right-menu">
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar" />
          <span style="vertical-align: top">{{
            nickName && nickName.length > 5
              ? nickName.slice(0, 5) + "..."
              : nickName
          }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <!-- <el-dropdown-item id="setting">
            <div @click.stop="setting = true">
              <span>布局设置</span>
            </div>
          </el-dropdown-item> -->
          <el-dropdown-item>
            <div @click.stop="openFileDownLoadPage">
              <span>下载中心</span>
            </div>
          </el-dropdown-item>
          <el-dropdown-item>
            <Screenfull />
          </el-dropdown-item>
          <el-dropdown-item id="user">
            <div @click.stop="logout">
              <span>退出登录</span>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import TopNav from "@/components/TopNav";
import Screenfull from "@/components/Screenfull/index.vue";
import Logo from "@/layout/components/Sidebar/Logo.vue";
import MenuSearch from "@/components/MenuSearch";
import { getToken } from "@/utils/auth";

export default {
  components: {
    Breadcrumb,
    Hamburger,
    TopNav,
    Screenfull,
    Logo,
    MenuSearch,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device", "nickName"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
  },
  data() {
    return {
      badge: undefined,
      Interval: null,
    };
  },
  mounted() {
    this.token = getToken();
  },
  beforeDestroy() {
    if (this.Interval) {
      clearInterval(this.Interval);
    }
  },
  methods: {
    //获取通知总数
    unreadNumber() {
      let params = {};
      unreadNumber(params)
        .then((res) => {
          if (res.data && Number(res.data) > 0) {
            this.badge = res.data;
          } else {
            this.badge = undefined;
          }
        })
        .catch(() => {
          // 结束定时任务
          clearInterval(this.Interval);
        });
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then(() => {
          //小磨盘解绑用户上报
          _bzt.logout();

          // location.reload();
          this.$router.push(
            "/login" + `?tenantId=${this.$store.state.app.tenantId}`
          );
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
  },
};
</script>

<style lang="less" scoped>
#user {
  padding: 0px;
  margin: 0px;
  text-align: center;
}
#setting {
  padding: 0px;
  margin: 0px;
  text-align: center;
}
#user > div,
#setting > div {
  padding: 0px;
  margin: auto;
  text-align: center;
}
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #02726a;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .menu-search {
    float: left;
    height: 100%;
    // line-height: 50px;
    padding: 9px 0;
    margin-left: 10px;
    width: 200px;
    position: relative;
    z-index: 1000; /* 确保搜索框在较高层级 */
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;
    width: 240px;
    justify-content: flex-end;
    .notice {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      cursor: pointer;
      margin-right: 10px;
    }

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        color: white;
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
  // Already set at the top of the class
}

/deep/.el-badge {
  sup {
    z-index: 1;
  }
}

.top-logo {
  width: 200px;
  // display: inline-block; // Removed to fix warning
  // line-height: 46px;
  float: left;
  height: 50px;
}
</style>
