import Vue from 'vue';
import Vuex from 'vuex';
import app from './modules/app';
import user from './modules/user';
import dataDict from './modules/dataDict'
import tagsView from './modules/tagsView';
import permission from './modules/permission';
import scanCode from './modules/scanCode';
import settings from './modules/settings';
import logicFlow from './modules/logicFlow';
import getters from './getters';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    user,
    tagsView,
    permission,
    settings,
    scanCode,
    logicFlow,
    dataDict
  },
  getters
});

export default store;
