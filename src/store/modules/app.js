import Cookies from "js-cookie";

const state = {
  sidebar: {
    // opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    opened: true,
    withoutAnimation: false,
  },
  device: "desktop",
  size: Cookies.get("size") || "medium",

  tenantId: "",
  isSupportOrgNo: "N",
  districtSupport: "N",
  areaSupport: "N",
  sectSupport: "N",
  paraSupport: "N",
  prcSupport: "N",
  lateFeeSupport: "N",
  mtrSupport: "N",
  reportSupport: "N",
  startBusinessFlow: "N",
  swSupportOrg: "N",
};

const mutations = {
  SET_TENANT_ID: (state, tenantId) => {
    state.tenantId = tenantId;
  },
  TOGGLE_SIDEBAR: (state) => {
    state.sidebar.opened = !state.sidebar.opened;
    state.sidebar.withoutAnimation = false;
    if (state.sidebar.opened) {
      Cookies.set("sidebarStatus", 1);
    } else {
      Cookies.set("sidebarStatus", 0);
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set("sidebarStatus", 0);
    state.sidebar.opened = false;
    state.sidebar.withoutAnimation = withoutAnimation;
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device;
  },
  SET_SIZE: (state, size) => {
    state.size = size;
    Cookies.set("size", size);
  },
  SET_IS_SUPPORT_ORG_NO: (state, payload) => {
    state.isSupportOrgNo = payload;
  },
  SET_DISTRICT_SUPPORT: (state, payload) => {
    state.districtSupport = payload;
  },
  SET_AREA_SUPPORT: (state, payload) => {
    state.areaSupport = payload;
  },
  SET_SECT_SUPPORT: (state, payload) => {
    state.sectSupport = payload;
  },
  SET_PARA_SUPPORT: (state, payload) => {
    state.paraSupport = payload;
  },
  SET_PRC_SUPPORT: (state, payload) => {
    state.prcSupport = payload;
  },
  SET_LATE_FEE_SUPPORT: (state, payload) => {
    state.lateFeeSupport = payload;
  },
  SET_MTR_SUPPORT: (state, payload) => {
    state.mtrSupport = payload;
  },
  SET_REPORT_SUPPORT: (state, payload) => {
    state.reportSupport = payload;
  },
  SET_START_BUSINESS_FLOW: (state, payload) => {
    state.startBusinessFlow = payload;
  },
  SET_SW_SUPPORT_ORG: (state, payload) => {
    state.swSupportOrg = payload;
  },
};

const actions = {
  toggleSideBar({ commit }) {
    commit("TOGGLE_SIDEBAR");
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit("CLOSE_SIDEBAR", withoutAnimation);
  },
  toggleDevice({ commit }, device) {
    commit("TOGGLE_DEVICE", device);
  },
  setSize({ commit }, size) {
    commit("SET_SIZE", size);
  },
  setTenantId({ commit }, tenantId) {
    commit("SET_TENANT_ID", tenantId);
  },
  setIsSupportOrgNo({ commit }, payload) {
    commit("SET_IS_SUPPORT_ORG_NO", payload);
  },
  setDistrictSupport({ commit }, payload) {
    commit("SET_DISTRICT_SUPPORT", payload);
  },
  setAreaSupport({ commit }, payload) {
    commit("SET_AREA_SUPPORT", payload);
  },
  setSectSupport({ commit }, payload) {
    commit("SET_SECT_SUPPORT", payload);
  },
  setParaSupport({ commit }, payload) {
    commit("SET_PARA_SUPPORT", payload);
  },
  setPrcSupport({ commit }, payload) {
    commit("SET_PRC_SUPPORT", payload);
  },
  setLateFeeSupport({ commit }, payload) {
    commit("SET_LATE_FEE_SUPPORT", payload);
  },
  setMtrSupport({ commit }, payload) {
    commit("SET_MTR_SUPPORT", payload);
  },
  setReportSupport({ commit }, payload) {
    commit("SET_REPORT_SUPPORT", payload);
  },
  setStartBusinessFlow({ commit }, payload) {
    commit("SET_START_BUSINESS_FLOW", payload);
  },
  setSwSupportOrg({ commit }, payload) {
    commit("SET_SW_SUPPORT_ORG", payload);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
