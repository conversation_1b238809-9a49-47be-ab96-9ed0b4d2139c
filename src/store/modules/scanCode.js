export default {
    namespaced: true,
    state: {
        // 扫码的值
        scanString: ''
    },
    mutations: {
        SET_SCANSTRING: (state, scanString) => {
            console.log("===scanString==",scanString)
            state.scanString = scanString
        }
    },
    actions: {
        setScan({commit}, payload){
            commit('SET_SCANSTRING', payload)
        }
    },
    getters:{
        scanString: state => state.scanString
    }
}


