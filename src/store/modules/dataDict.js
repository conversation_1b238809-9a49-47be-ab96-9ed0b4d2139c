import { getDicts } from "@/api/system/dict/data";
import {
	listUser,
} from "@/api/common";
import { useFlowList } from "@/api/orderScheduling/workStation.js";
const state = {};

const mutations = {
	SET_DICTIONARY(state, { name, data }) {
		state[name] = data.map((item) => {
			return {
				label: item.dictLabel,
				value: item.dictValue,
			};
		});
	},
	SER_ORDERTYPE(state, data) {
		state.orderTypeOption = data.map((item) => {
			return {
				label: item.flowTypeName,
				value: item.flowType,
			};
		});
	},
	SER_USEROPTION(state, data) {
		state.userOption = data.map((item) => {
			return {
				label: item.userName,
				value: item.userId,
				nickName: item.nickName
			};
		});
	}
};

const actions = {
	async getDicts({ commit, state }, codeTypes) {
		let needRequest = false; //是否需要请求
		if (!state[codeTypes]) {
			needRequest = true;
		}
		if (needRequest) {
			const res = await getDicts(codeTypes);
			commit('SET_DICTIONARY', { name: codeTypes, data: res?.data })
		}
		return Promise.resolve(state[codeTypes]);
	},
	async getOrderType({ commit, state }) {
		if (state.orderTypeOption) {
			return Promise.resolve(state.orderTypeOption);
		}
		const res = await useFlowList();
		if (res) {
			commit("SER_ORDERTYPE", res.data);
			return Promise.resolve(state.orderTypeOption);
		}
	},
	async getListUser({ commit, state }) {
		const res = await listUser({
			pageNum: 1,
			pageSize: 9999,
		});
		if (res) {
			commit("SER_USEROPTION", res.data);
			return Promise.resolve(state.userOption);
		}
	}
};

export default {
	namespaced: true,
	state,
	mutations,
	actions,
};
