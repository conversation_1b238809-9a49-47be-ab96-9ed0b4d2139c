import { login, logout, getInfo } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";

const user = {
  state: {
    token: getToken(),
    name: "",
    nickName: "",
    avatar: "",
    companyOptions: [],
    departmentOptions: [],
    orgNoList: [],
    orgNoListLabel: [],
    orgNoListId: [],
    roles: [],
    userId: "",
    tenantId: "",
    permissions: [],
    firstLogin: "",
    orgNo: "",
    deptId: "",
    phonenumber: "",
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_COMPANY_OPTIONS: (state, options) => {
      state.companyOptions = options;
    },
    SET_DEPARTMENT_OPTIONS: (state, options) => {
      state.departmentOptions = options;
    },
    SET_ORGLIST: (state, options) => {
      state.orgNoList = options;
    },
    SET_ORGLIST_LABEL: (state, options) => {
      state.orgNoListLabel = options;
    },
    SET_ORGLIST_IDL: (state, options) => {
      state.orgNoListId = options;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_USERID: (state, userId) => {
      state.userId = userId;
    },
    SET_TENANTID: (state, tenantId) => {
      state.tenantId = tenantId;
    },
    SET_NICK_NAME: (state, nickName) => {
      state.nickName = nickName;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_DEPT: (state, dept) => {
      state.dept = dept;
    },
    SET_FIRST_LOGIN: (state, dept) => {
      state.firstLogin = dept;
    },
    SET_ORGNO: (state, orgNo) => {
      state.orgNo = orgNo;
    },
    SET_DEPT_ID: (state, deptId) => {
      state.deptId = deptId;
    },
    SET_PHONENUMBER: (state, phonenumber) => {
      state.phonenumber = phonenumber;
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const salt = userInfo.salt;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      // 判断请求的来源是内网还是外网
      const isInternal = window.location.hostname.includes(
        process.env.VUE_APP_BASE_API_PREFIX_INTERNAL
      );
      // const code = userInfo.code;
      // const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        // login(username, password, code, uuid)
        login(username, password, code, uuid, salt, isInternal)
          .then((res) => {
            commit("SET_FIRST_LOGIN", "");
            if (res.data === "firstLogin") {
              commit("SET_FIRST_LOGIN", res.data);
              resolve();
            } else {
              setToken(res.data);
              commit("SET_TOKEN", res.data);
              resolve();
            }
          })
          ["catch"]((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token)
          .then((res) => {
            const user = res.data.user;
            const avatar =
              user.avatar == ""
                ? require("@/assets/image/profile.jpg")
                : user.avatar;
            if (res.data.roles && res.data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit("SET_ROLES", res.data.roles);
              commit("SET_PERMISSIONS", res.data.permissions);
            } else {
              commit("SET_ROLES", ["ROLE_DEFAULT"]);
            }
            commit("SET_NAME", user.userName);
            commit("SET_NICK_NAME", user.nickName);
            commit("SET_AVATAR", avatar);
            commit("SET_USERID", user.userId);
            commit("SET_TENANTID", user.tenantId);
            commit("SET_DEPT", user.dept);
            commit("SET_ORGNO", user.orgNo);
            commit("SET_DEPT_ID", user.deptId);
            commit("SET_PHONENUMBER", user.phonenumber);
            resolve(res);
          })
          ["catch"]((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            removeToken();
            resolve();
          })
          ["catch"]((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit("SET_TOKEN", "");
        removeToken();
        resolve();
      });
    },
  },
};

export default user;
