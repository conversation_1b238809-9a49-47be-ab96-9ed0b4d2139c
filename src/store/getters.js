const getters = {
  sidebar: (state) => state.app.sidebar,
  size: (state) => state.app.size,
  device: (state) => state.app.device,
  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  name: (state) => state.user.name,
  nickName: (state) => state.user.nickName,
  userId: (state) => state.user.userId,
  deptId: (state) => state.user.dept.deptId,
  introduction: (state) => state.user.introduction,
  roles: (state) => state.user.roles,
  permissions: (state) => state.user.permissions,
  phonenumber: (state) => state.user.phonenumber,
  permission_routes: (state) => state.permission.routes,
  scanString: (state) => state.scanCode.scanString,
  tenantId: (state) => state.user.tenantId,
  topbarRouters: (state) => state.permission.topbarRouters,
  defaultRoutes: (state) => state.permission.defaultRoutes,
  sidebarRouters: (state) => state.permission.sidebarRouters,
  weatherInfo: (state) => state.bigScreen.weatherInfo,
  nodeType: (state) => state.logicFlow.nodeType,
};
export default getters;
