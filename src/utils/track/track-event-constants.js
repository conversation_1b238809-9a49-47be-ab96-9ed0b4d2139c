// -------------------------------登录界面---------------------------------------
// 用户登录频率
export const LOGIN_USER_LOGIN_COUNT = 'UserLoginCount'

// -------------------------------项目管理---------------------------------------
// 项目管理
// 点击项目管理新增按钮
export const PROJECT_CLICK_CREATE_PROJECT_BTN = 'ClickCreateProjectBtn'
// 点击项目新增弹窗中的创建按钮
export const PROJECT_CLICK_CREATE_PROJECT_DIALOG_CREATE_BTN = 'ClickCreateProjectDialogCreate'
// 点击项目新增弹窗中的保存按钮
export const PROJECT_CLICK_CREATE_PROJECT_DIALOG_SAVE_BTN = 'ClickCreateProjectDialogSave'
// 点击项目导出
export const PROJECT_CLICK_PROJECT_REPORT = 'ClickProjectReport'

// 施工管理
// 点击批量生成施工工单
export const CONSTRUCTION_CLICK_BATCH_CREATE_ORDER = 'ClickBatchCreateOrder'

// 路测管理
// 路测管理TAB点击
export const ROADTEST_CLICK_TAB = 'ClickRoadTestTab'

// 上线管理
// 点击上线管理Tab
export const ONLINE_CLICK_TAB = 'ClickOnlineTab'

// 全量验收
// 点击全量验收Tab
export const FUNLL_ACCEPTANCE_CLICK_TAB = 'ClickFullAcceptanceTab'

// 工单工作台
// 点击工单工作台Tab
export const WORKBENCH_CLICK_TAB = 'ClickWorkBenchTab'
// 点击工单工作台导出
export const WORKBENCH_CLICK_REPORT = 'ClickWorkBenchReport'

// -------------------------------运维工单---------------------------------------
// 创建运维工单
export const MAINTENANCE_ORDER_CREATE = 'CreateMaintenanceOrder'
// 运维工单设置分组
export const MAINTENANCE_ORDER_SET_GROUP = 'MaintenanceOrderSetGroup'
// 站点责任人配置
export const MAINTENANCE_ORDER_STATION_RELATINO_SETTING = 'StationRelationSetting'
// 点击运维工单导出
export const MAINTENANCE_ORDER_CLICK_REPORT = 'ClickMaintenanceOrderReport'
// 点击运维工单备注
export const MAINTENANCE_ORDER_CLICK_REMARK = 'ClickMaintenanceOrderRemark'
// 点击运维工单编辑
export const MAINTENANCE_ORDER_CLICK_EDIT = 'ClickMaintenanceOrderEdit'
// 运维工单转派成功
export const MAINTENANCE_ORDER_TRANSFER_SUCCESS = 'MaintenanceOrderTransSuccess'
// 运维工单处理成功
export const MAINTENANCE_ORDER_HANDLE_SUCCESS = 'MaintenanceOrderHandleSuccess'
// 点击运维工单结束
export const MAINTENANCE_ORDER_STOP_SUCCESS = 'ClickMaintenanceOrderStop'
// 点击运维工单催单
export const MAINTENANCE_ORDER_CLICK_PUSH = 'ClickMaintenanceOrderPush'
// 工单审核-驳回成功
export const MAINTENANCE_ORDER_REJECT_SUCCESS = 'MaintenanceOrderRejectSuccess'
// 工单审核-待回访成功
export const MAINTENANCE_ORDER_FLLOWUP_SUCCESS = 'MaintenanceOrderFllowUpSuccess'
// 工单审核-完结成功
export const MAINTENANCE_ORDER_FINISH_SUCCESS = 'MaintenanceOrderFinishSuccess'

// -------------------------------运维工单---------------------------------------
// 点击巡检任务新增
export const INSPECT_TASK_CLICK_CREATE = 'ClickInspectTaskCreate'
// 点击巡检任务编辑
export const INSPECT_TASK_CLICK_EDIT = 'ClickInspectTaskEdit'
// 点击巡检任务配置
export const INSPECT_TASK_CLICK_SETTING = 'ClickInspectTaskSetting'
// 点击巡检任务转派
export const INSPECT_TASK_CLICK_TRANSFER = 'ClickInspectTaskTransfer'
// 点击批量配置巡检规则
export const INSPECT_TASK_CLICK_BATCH_CONFIG_RULE = 'ClickBatchConfigInspectRule'
// 点击站点列表配置
export const INSPECT_TASK_CLICK_LIST_CONFIG = 'ClickStationListConfig'

// -------------------------------异常上报---------------------------------------
// 点击异常批量转工单
export const ERROR_CLICK_BATCH_CREATE_ORDER = 'ClickBatchCreateErrorOrder'
// 点击异常批量忽略
export const ERROR_CLICK_BATCH_IGNORE = 'ClickErrorBatchIgnore'
// 点击异常转工单
export const ERROR_CLICK_CREATE_ORDER = 'ClickCreateErrorOrder'
// 点击异常忽略
export const ERROR_CLICK_IGNORE = 'ClickErrorIgnore'
// 点击批量配置建单规则
export const ERROR_CLICK_BATCH_CREATE_ORDER_RULE = 'ClickBatchCreateOrderRule'
// 点击配置建单规则
export const ERROR_CLICK_CREATE_ORDER_RULE = 'ClickCreateOrderRule'

// -------------------------------信息档案---------------------------------------
// 点击能投大区核验
export const DATA_INFO_CLICK_STATION_DATA_CHECK = 'ClickStationDataCheck'
// 点击新增站点等级
export const DATA_INFO_CLICK_CREATE_STATION_GRADE = 'ClickCreateStationGrade'
// 点击编辑站点等级
export const DATA_INFO_CLICK_EDIT_STATION_GRADE = 'ClickEditStationGrade'
// 点击站点档案导出
export const DATA_INFO_CLICK_STATION_REPORT = 'ClickStationReport'
// 点击站点档案增标签
export const DATA_INFO_CLICK_STATION_ADD_TAG = 'ClickStationAddTag'
// 点击站点档案减标签
export const DATA_INFO_CLICK_STATION_MINUS_TAG = 'ClickStationMinusTag'
// 点击站点列表配置运维等级
export const DATA_INFO_CLICK_STATION_LEVEL_CONFIG = 'ClickStationLevelConfig'
// 点击充电桩导出
export const DATA_INFO_CLICK_CHARING_STATION_REPORT = 'ClickChargingStationReport'
// 点击充电枪导出
export const DATA_INFO_CLICK_CHARING_GUN_REPORT = 'ClickChargingGunReport'