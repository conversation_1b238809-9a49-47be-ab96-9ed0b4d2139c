[{"eventCode": "ClickRoadTestTab", "eventName": "路测管理TAB点击", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "tabname", "paramName": "TAB名称"}]}, {"eventCode": "ClickOnlineTab", "eventName": "点击上线管理Tab", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "tabname", "paramName": "TAB名称"}]}, {"eventCode": "ClickFullAcceptanceTab", "eventName": "点击全量验收Tab", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "tabname", "paramName": "TAB名称"}]}, {"eventCode": "ClickWorkBenchTab", "eventName": "点击工单工作台Tab", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "tabname", "paramName": "TAB名称"}]}, {"eventCode": "ClickCreateProjectBtn", "eventName": "点击项目管理新增按钮"}, {"eventCode": "ClickCreateProjectDialogCreate", "eventName": "点击项目新增弹窗中的创建按钮"}, {"eventCode": "ClickCreateProjectDialogSave", "eventName": "点击项目新增弹窗中的保存按钮"}, {"eventCode": "ClickProjectReport", "eventName": "点击项目导出"}, {"eventCode": "ClickBatchCreateOrder", "eventName": "点击批量生成施工工单", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "projectCode", "paramName": "项目编号"}, {"dataType": "STRING", "description": "", "identifier": "projectName", "paramName": "项目名称"}]}, {"eventCode": "ClickWorkBenchReport", "eventName": "点击工单工作台导出"}, {"eventCode": "CreateMaintenanceOrder", "eventName": "创建运维工单", "eventParams": [{"dataType": "BOOLEAN", "description": "", "identifier": "noHandleUser", "paramName": "处理人为空"}, {"dataType": "BOOLEAN", "description": "", "identifier": "hasChargingPie", "paramName": "充电桩数量>0"}, {"dataType": "STRING", "description": "", "identifier": "createDuration", "paramName": "创建时长"}]}, {"eventCode": "MaintenanceOrderSetGroup", "eventName": "运维工单设置分组", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "operateType", "paramName": "操作类型"}]}, {"eventCode": "StationRelationSetting", "eventName": "站点责任人配置", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "userId", "paramName": "用户ID"}, {"dataType": "STRING", "description": "", "identifier": "userName", "paramName": "用户名称"}]}, {"eventCode": "ClickMaintenanceOrderReport", "eventName": "点击运维工单导出"}, {"eventCode": "ClickMaintenanceOrderRemark", "eventName": "点击运维工单备注"}, {"eventCode": "ClickMaintenanceOrderEdit", "eventName": "点击运维工单编辑"}, {"eventCode": "MaintenanceOrderTransSuccess", "eventName": "运维工单转派频率", "eventParams": [{"dataType": "BOOLEAN", "description": "", "identifier": "noNotifyUsers", "paramName": "通知人为空"}, {"dataType": "BOOLEAN", "description": "", "identifier": "noNotifyWays", "paramName": "通知方式为空"}, {"dataType": "STRING", "description": "", "identifier": "source", "paramName": "来源"}]}, {"eventCode": "MaintenanceOrderHandleSuccess", "eventName": "运维工单处理频率", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "isSubmit", "paramName": "是否为提交"}, {"dataType": "BOOLEAN", "description": "", "identifier": "noHandleTags", "paramName": "处理标签为空"}, {"dataType": "STRING", "description": "", "identifier": "duration", "paramName": "处理时长"}, {"dataType": "STRING", "description": "", "identifier": "source", "paramName": "来源"}]}, {"eventCode": "ClickMaintenanceOrderStop", "eventName": "点击运维工单结束", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "duration", "paramName": "结束时长"}]}, {"eventCode": "ClickMaintenanceOrderPush", "eventName": "点击运维工单催单"}, {"eventCode": "MaintenanceOrderAuditSuccess", "eventName": "工单审核成功", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "orderStatus", "paramName": "工单状态"}, {"dataType": "STRING", "description": "", "identifier": "auditDuration", "paramName": "审核时长"}, {"dataType": "STRING", "description": "", "identifier": "finishDuration", "paramName": "完结时长"}, {"dataType": "STRING", "description": "", "identifier": "source", "paramName": "来源"}]}, {"eventCode": "MaintenanceOrderRejectSuccess", "eventName": "工单审核驳回频率", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "auditDuration", "paramName": "审核时长"}]}, {"eventCode": "MaintenanceOrderFllowUpSuccess", "eventName": "工单审核待回访频率", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "auditDuration", "paramName": "审核时长"}, {"dataType": "STRING", "description": "", "identifier": "finishDuration", "paramName": "完结时长"}, {"dataType": "STRING", "description": "", "identifier": "source", "paramName": "来源"}]}, {"eventCode": "MaintenanceOrderFinishSuccess", "eventName": "工单审核完结频率", "eventParams": [{"dataType": "STRING", "description": "", "identifier": "auditDuration", "paramName": "审核时长"}, {"dataType": "STRING", "description": "", "identifier": "finishDuration", "paramName": "完结时长"}, {"dataType": "STRING", "description": "", "identifier": "source", "paramName": "来源"}]}, {"eventCode": "ClickInspectTaskCreate", "eventName": "点击巡检任务新增"}, {"eventCode": "ClickInspectTaskEdit", "eventName": "点击巡检任务编辑"}, {"eventCode": "ClickInspectTaskSetting", "eventName": "点击巡检任务配置"}, {"eventCode": "ClickInspectTaskTransfer", "eventName": "点击巡检任务转派"}, {"eventCode": "ClickBatchConfigInspectRule", "eventName": "点击批量配置巡检规则"}, {"eventCode": "ClickStationListConfig", "eventName": "点击站点列表配置"}, {"eventCode": "ClickBatchCreateErrorOrder", "eventName": "点击异常批量转工单"}, {"eventCode": "ClickErrorBatchIgnore", "eventName": "点击异常批量忽略"}, {"eventCode": "ClickCreateErrorOrder", "eventName": "点击异常转工单"}, {"eventCode": "ClickErrorIgnore", "eventName": "点击异常忽略"}, {"eventCode": "ClickBatchCreateOrderRule", "eventName": "点击批量配置建单规则"}, {"eventCode": "ClickCreateOrderRule", "eventName": "点击配置建单规则"}, {"eventCode": "ClickStationDataCheck", "eventName": "点击能投大区核验"}, {"eventCode": "ClickCreateStationGrade", "eventName": "点击新增站点等级"}, {"eventCode": "ClickEditStationGrade", "eventName": "点击编辑站点等级"}, {"eventCode": "ClickStationReport", "eventName": "点击站点档案导出"}, {"eventCode": "ClickStationAddTag", "eventName": "点击站点档案增标签"}, {"eventCode": "ClickStationMinusTag", "eventName": "点击站点档案减标签"}, {"eventCode": "ClickStationLevelConfig", "eventName": "点击站点列表配置运维等级"}, {"eventCode": "ClickChargingStationReport", "eventName": "点击充电桩导出"}, {"eventCode": "ClickChargingGunReport", "eventName": "点击充电枪导出"}]