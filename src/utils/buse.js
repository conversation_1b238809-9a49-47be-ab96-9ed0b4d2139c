/**
 * @description: 初始化参数
 * @param {*} list DynamicForm的列配置数组
 * @return {*}
 */
export function initParams(list) {
  // console.log('❤️❤️list', list)
  return list.reduce((acc, item) => {
    if (item.element === "el-checkbox-group") {
      acc[item["field"]] = item?.defaultValue || [];
    } else {
      acc[item["field"]] = item?.defaultValue;
    }
    return acc;
  }, {});
}
/**
 * 生成vxeTable表格/dynamicForm表单配置项
 * @param {*} fieldsMap 所有的配置项，其中已下划线 '_' 开头加scope中键名组成的属性视为该scope中该配置项独有的属性
 * @param {*} scope 配置项作用域，决定配置项使用范围及顺序，结构为数组组成的对象，函数会返回和此结构一样的数据结构，如果配置项名称中有table/column关键字，则算作表格配置，否则为表单配置
 * @return {*}  <Object>{table,form,filter}
 */
export function generateConfigItems({ fieldsMap, scope }) {
  // 表单相关配置项的key集合
  const formFields = [
    "field",
    "title",
    "element",
    "defaultValue",
    "slotName",
    "rules",
    "ref",
    "style",
    "props",
    "on",
    "preview",
    "previewSlot",
    "colProps",
    "itemProps",
    "show",
    "scopedSlots",
  ];
  const columnTypes = ["seq", "checkbox", "radio", "expand", "html"];
  console.log("jk", Object.keys(scope));
  return Object.keys(scope).reduce((acc, resKey) => {
    acc[resKey] = scope[resKey]
      .map((key) => {
        let obj = {};
        if (columnTypes.includes(key)) {
          obj.type = key;
        }
        if (!Object.hasOwn(fieldsMap, key)) {
          throw Error(
            "缺少scope中声明的配置项【" + key + "】，请检查fieldsMap"
          );
        }
        // 0x01 如果是字符串，直接转换为对象
        if (typeof fieldsMap[key] === "string") {
          obj = {
            ...obj,
            field: key,
            title: fieldsMap[key],
          };
        } else {
          // 0x02 如果是对象，先过滤出对应类型的key
          let { title, ...rest } = fieldsMap[key];
          let keysMap = Object.keys(rest)
            ?.filter((k) =>
              !resKey.toLowerCase().includes("table") &&
              !resKey.toLowerCase().includes("column")
                ? formFields.includes(k)
                : !formFields.includes(k)
            )
            ?.reduce((acc, key) => {
              acc[key] = rest[key];
              return acc;
            }, {});

          let otherKeysMap = rest["_" + resKey] || {};

          obj = {
            ...obj,
            field: key,
            title: title,
            ...keysMap,
            ...otherKeysMap,
          };
        }
        return obj;
      })
      .filter(Boolean);
    return acc;
  }, {});
}
