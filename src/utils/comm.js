import moment from "moment";

/**
 * 通用js方法封装处理
 * Copyright (c) 2019 bangdao
 */
const baseURL =
  window.location.hostname.includes(process.env.VUE_APP_BASE_API_PREFIX_INTERNAL)
    ? process.env.VUE_APP_BASE_API_INTERNAL
    : process.env.VUE_APP_BASE_API_EXTERNAL;

// 获取链接字段
export function getQueryString(key) {
  const paramStr = window.location.hash;
  const regExp = new RegExp(`(^|\\?|&)${key}=([^&]*)(\\s|&|$)`);
  return regExp.test(paramStr) ? RegExp.$2 : '';
}

//  秒数转化为时分秒
export function formatSeconds(value) {
  //  秒
  let second = parseInt(value)
  //  分
  let minute = 0
  //  小时
  let hour = 0
  //  天
   let day = 0
  //  如果秒数大于60，将秒数转换成整数
  if (second > 60) {
    //  获取分钟，除以60取整数，得到整数分钟
    minute = parseInt(second / 60)
    //  获取秒数，秒数取佘，得到整数秒数
    second = parseInt(second % 60)
    //  如果分钟大于60，将分钟转换成小时
    if (minute > 60) {
      //  获取小时，获取分钟除以60，得到整数小时
      hour = parseInt(minute / 60)
      //  获取小时后取佘的分，获取分钟除以60取佘的分
      minute = parseInt(minute % 60)
      //  如果小时大于24，将小时转换成天
       if (hour > 23) {
         //  获取天数，获取小时除以24，得到整天数
         day = parseInt(hour / 24)
         //  获取天数后取余的小时，获取小时除以24取余的小时
         hour = parseInt(hour % 24)
       }
    }
  }
  // let result = '' + parseInt(second) + '秒'
  let result = ''
  if (minute > 0) {
    result = '' + parseInt(minute) + '分' + result
  }
  if (hour > 0) {
    result = '' + parseInt(hour) + '小时' + result
  }
   if (day > 0) {
     result = '' + parseInt(day) + '天' + result
   }
  // console.log('result：', result)
  if (day == 0 && hour == 0 && minute == 0) {
    result = 1 + '分'
  }
  return result
}

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0) {
    return null;
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
  return time_str;
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName] !== undefined) {
    this.$refs[refName].resetFields();
  }
}

// 添加日期范围
export function addDateRange(params, dateRange) {
  var search = params;
  search.beginTime = '';
  search.endTime = '';
  if (dateRange != null && '' != dateRange) {
    search.beginTime = this.dateRange[0];
    search.endTime = this.dateRange[1];
  }
  return search;
}

/**
 * 数字金额逢三加， 比如 123,456.78
 * @param value
 * @returns {string}
 */
export function moneyFormat(value) {
    if (!value) return '0.00'
    if (typeof(value) === 'string'){
        value = Number(value)
    }
    // 将数值截取，保留两位小数
    value = value.toFixed(2)
    // 获取整数部分
    const intPart = Math.trunc(value)
    // 整数部分处理，增加,
    const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    // 预定义小数部分
    let floatPart = '.00'
    // 将数值截取为小数部分和整数部分
    const valueArray = value.toString().split('.')
    if (valueArray.length === 2) { // 有小数部分
        floatPart = valueArray[1].toString() // 取得小数部分
        return intPartFormat + '.' + floatPart
    }
    return intPartFormat + floatPart
}

// 添加日期范围
export function addDateRangeCreate(params, dateRange) {
  var search = params;
  search.createTime = '';
  search.updateTime = '';
  if (dateRange != null && '' != dateRange) {
    // search.createTime = this.dateRange[0];
    // search.updateTime = this.dateRange[1];
    search.createTime = dateRange[0];
    search.updateTime = dateRange[1];
  }

  return search;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  var actions = [];
  Object.keys(datas).map(key => {
    if (datas[key].dictValue == '' + value) {
      actions.push(datas[key].dictLabel);
      return false;
    }
  });
  return actions.join('');
}

// 通用下载方法
export function download(fileName) {

    var divFrame = document.getElementById("downLoadFrame")
    //判断是否存在，如果存在先移除，再重新创建
    if (divFrame != null) {
        document.body.removeChild(divFrame)
    }
    var elemIF = document.createElement("iframe");
    elemIF.setAttribute("id", "downLoadFrame");

    elemIF.src =  baseURL + '/common/download?fileName=' + encodeURI(fileName) + '&delete=' + true;
    elemIF.style.display = "none";
    document.body.appendChild(elemIF);
    setTimeout(function (){elemIF.src='';},1000)
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments;
  var flag = true;
  var i = 1;
  str = str.replace(/%s/g, function() {
    var arg = args[i++];
    if (typeof arg === 'undefined') {
      flag = false;
      return '';
    }
    return arg;
  });
  return flag ? str : '';
}

export function download4sftp(directory,code,name) {

    var divFrame = document.getElementById("downLoadListFrame")
    //判断是否存在，如果存在先移除，再重新创建
    if (divFrame != null) {
        document.body.removeChild(divFrame)
    }
    var elemIF = document.createElement("iframe");
    elemIF.setAttribute("id", "downLoadListFrame");

    elemIF.src = baseURL + '/common/download4sftp?directory='+encodeURI(directory)+'&templateCode='
        +encodeURI(code)+'&templateName='+ encodeURI(name);
    elemIF.style.display = "none";
    document.body.appendChild(elemIF);
    setTimeout(function (){elemIF.src='';},1000)
}

export function accAdd(arg1, arg2) {
    if (arg1 == null || arg1 == undefined || arg1 == ''){
        arg1= 0;
    }
    if (arg2 == null || arg2 == undefined || arg2 == ''){
        arg2= 0;
    }
  var r1, r2, m, c;
  try {
      r1 = arg1.toString().split(".")[1].length;
  }
  catch (e) {
      r1 = 0;
  }
  try {
      r2 = arg2.toString().split(".")[1].length;
  }
  catch (e) {
      r2 = 0;
  }
  c = Math.abs(r1 - r2);
  m = Math.pow(10, Math.max(r1, r2));
  if (c > 0) {
      var cm = Math.pow(10, c);
      if (r1 > r2) {
          arg1 = Number(arg1.toString().replace(".", ""));
          arg2 = Number(arg2.toString().replace(".", "")) * cm;
      } else {
          arg1 = Number(arg1.toString().replace(".", "")) * cm;
          arg2 = Number(arg2.toString().replace(".", ""));
      }
  } else {
      arg1 = Number(arg1.toString().replace(".", ""));
      arg2 = Number(arg2.toString().replace(".", ""));
  }
  return (arg1 + arg2) / m;
}
//js小数 减
export function accSub(num1, num2) {
  var baseNum, baseNum1, baseNum2;
  var precision;// 精度
  try {
    baseNum1 = num1.toString().split(".")[1].length;
  } catch (e) {
    baseNum1 = 0;
  }
  try {
    baseNum2 = num2.toString().split(".")[1].length;
  } catch (e) {
    baseNum2 = 0;
  }
  baseNum = Math.pow(10, Math.max(baseNum1, baseNum2));
  precision = (baseNum1 >= baseNum2) ? baseNum1 : baseNum2;
  return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision);
};
//js小数乘
export function mul(a, b) {
  let vala = parseInt(a)
  let valb = parseInt(b)
  if (vala == NaN || valb == NaN) {
    return
  } else {
    var c = 0,
      d = a.toString(),
      e = b.toString();
    try {
        c += d.split(".")[1].length;
    } catch (f) {}
    try {
        c += e.split(".")[1].length;
    } catch (f) {}
    return Number(d.replace(".", "")) * Number(e.replace(".", "")) / Math.pow(10, c);
  }
}

export function objForEach(obj, callback) {
    // 判断回调是否是一个函数
    if (typeof callback === 'function') {
        let i = 0;
        for (const key in obj) {
            // @ts-ignore
            callback(obj[key], i, key);
            i++;
        }
        return;
    }
    // 传入的回调如果不是function，那么就抛出错误
    throw new Error(
        callback +
        ' is not a function!,You can use it like this: Object.forEach(obj,(item,index,key)=>{...}) '
    );
}

/**
 * 计算两个时间的差值并且格式化为"xx天xx小时xx分钟xx秒"
 * @param obj
 * @param callback
 */
export function timeDiffFormat(startTime, endTime) {
  if (!startTime || !endTime) {
    return "";
  }

  try {
    // 将字符串转换为moment对象
    let moment1 = moment(startTime);
    let moment2 = moment(endTime);

    // 计算两个时间之间的差值
    let duration = moment.duration(moment2.diff(moment1));

    // 获取天数、小时数、分钟数和秒数
    let days = duration.days();
    let hours = duration.hours();
    let minutes = duration.minutes();
    let seconds = duration.seconds();

    // 输出差值
    let daysStr = days > 0 ? `${days}天 ` : "";
    let hoursStr = hours > 0 ? `${hours}小时 ` : "";
    let minutesStr = minutes > 0 ? `${minutes}分钟 ` : "";
    let secondsStr = seconds > 0 ? `${seconds}秒` : "";

    return `${daysStr}${hoursStr}${minutesStr}${secondsStr}`;
  } catch (e) {
    return "";
  }
}
