import {Message} from 'element-ui'
import {getToken} from '@/utils/auth'; // 与后端的协商，websocket请求需要带上token参数

let websock = null
let messageCallback = null
let errorCallback = null
let wsUrl = 'ws://localhost:6688/'
let tryTime = 0
//心跳检测
let timeout = 5000; //多少秒执行检测
let timeoutTask = null
let lockReconnect = false; //避免重复连接
let reconnectTimeoutTask = null; //重连之后多久再次重连
let ping = "ping"


function reset() {
  // 清除定时器重新发送一个心跳信息
  if (timeoutTask) {
      clearTimeout(timeoutTask);
  }
  timeoutTask = setTimeout(() => {
      //这里发送一个心跳，后端收到后，返回一个心跳消息，
      //onmessage拿到返回的心跳就说明连接正常
      websock.send(ping);
  }, timeout);
}

// 重连
function reconnect() {
  console.log("lockReconnect", lockReconnect);
  // 防止多个方法调用，多处重连
  if (lockReconnect) {
      return;
  }
  lockReconnect = true;

  //没连接上会一直重连，设置延迟避免请求过多
  reconnectTimeoutTask = setTimeout(() => {
      // 重新连接
      initWebSocket(wsUrl);
      lockReconnect = false;
  }, 4000);
}

// 接收ws后端返回的数据
async function websocketonmessage(e) {
    messageCallback(e.data)
    reset()
}

/**
 * 发起websocket连接
 * @param {Object} agentData 需要向后台传递的参数数据
 */
function websocketSend(agentData) {
    console.log("websocket发送消息", agentData);
    // 加延迟是为了尽量让ws连接状态变为OPEN
    setTimeout(() => {
        // 添加状态判断，当为OPEN时，发送消息
        if (websock.readyState === websock.OPEN) { // websock.OPEN = 1
            // 发给后端的数据需要字符串化
            websock.send(agentData)
        }
        if (websock.readyState === websock.CLOSED) { // websock.CLOSED = 3
            console.log('websock.readyState=3')
            // Message.error('ws连接异常，请稍候重试')
            errorCallback()
        }
    }, 500)
}

// 关闭ws连接
function websocketclose(e) {
    // e.code === 1000  表示正常关闭。 无论为何目的而创建, 该链接都已成功完成任务。
    // e.code !== 1000  表示非正常关闭。
    if (e && e.code !== 1000) {
        // Message.error('ws连接异常，请稍候重试')
        errorCallback()
        // // 如果需要设置异常重连则可替换为下面的代码，自行进行测试
        // if (tryTime < 10) {
        //   setTimeout(function() {
        //    websock = null
        //    tryTime++
        //    initWebSocket()
        //    console.log(`第${tryTime}次重连`)
        //  }, 3 * 1000)
        //} else {
        //  Message.error('重连失败！请稍后重试')
        //}
    }
}

// 建立ws连接
function websocketOpen(e) {
    console.log('ws连接成功')
}

// 初始化weosocket
export function initWebSocket() {
    if (typeof (WebSocket) === 'undefined') {
        Message.error('您的浏览器不支持WebSocket，无法获取数据')
        return false
    }

    // ws请求完整地址
    const requstWsUrl = wsUrl;

    console.log("websocket地址", requstWsUrl);
    websock = new WebSocket(requstWsUrl)

    websock.onmessage = function (e) {
        websocketonmessage(e)
    }
    websock.onopen = function () {
        websocketOpen()
        // 清除重连定时器
        if (reconnectTimeoutTask) {
          clearTimeout(reconnectTimeoutTask);
        }
        // 开启检测
        reset();
    }
    websock.onerror = function () {
        Message.error('ws连接异常，请稍候重试')
        errorCallback()
        reconnect()
    }
    websock.onclose = function (e) {
        websocketclose(e)
        // reconnect()
    }
}

/**
 * 发起websocket请求函数
 * @param {string} url ws连接地址
 * @param {Object} agentData 传给后台的参数
 * @param {function} successCallback 接收到ws数据，对数据进行处理的回调函数
 * @param {function} errCallback ws连接错误的回调函数
 */
export function sendWebsocket(url, agentData, successCallback, errCallback) {
    wsUrl = url
    initWebSocket()
    messageCallback = successCallback
    errorCallback = errCallback
    websocketSend(agentData)
}

/**
 * 关闭websocket函数
 */
export function closeWebsocket() {
    if (websock) {
        websock.close() // 关闭websocket
        // websock.onclose() // 关闭websocket
    }
}

