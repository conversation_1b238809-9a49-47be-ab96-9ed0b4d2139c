import axios from "axios";
import { Notification, MessageBox, Message } from "element-ui";
import store from "@/store";
import qs from "qs";
import { getToken } from "@/utils/auth";
import {
  encryptHeader,
  decryptHeader,
  decyptData,
  encyptData,
} from "@/utils/encrypt";

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";

// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: process.env.VUE_APP_BASE_API,
  paramsSerializer: (params) => qs.stringify(params, { indices: false }),
  // 超时
  timeout: 100000,
  headers:
    location.protocol == "https:" ? { ...encryptHeader, ...decryptHeader } : {},
});

// request拦截器
service.interceptors.request.use(
  (config) => {
    if (getToken()) {
      config.headers.Authorization = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    if (
      config.responseType ||
      Object.prototype.toString.call(config.data) === "[object FormData]"
    ) {
      //导出文件流----不加密
      delete config.headers.bdgatewayencryptiontype;
      delete config.headers.bdgatewayresponseneedencryptiontype;
      // console.log("--请求路径--Url--不加密", config);
      return config;
    } else {
      if (config.headers.bdgatewayencryptiontype) {
        config.data = encyptData(config.data);
        // console.log("----Url--加密", config.url);
        return config;
      } else {
        return config;
      }
    }
  },
  (error) => {
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    const { data = {} } = res;
    let { content = "", ret_code = "", ret_msg = "" } = data;
    if (res?.config?.responseType === "blob") {
      if (res?.config.needFileName) {
        //需要获取res.header中的文件名，直接返回res做处理
        return res;
      }
      return res.data;
    }
    //跳过拦截
    if (res?.config?.skipIntercept) {
      return res.data;
    }
    // console.log("res--响应拦截器--", res);
    // console.log("res--data", data);
    if (data.ret_code) {
      //走加密
      content = decyptData(content);
      if (ret_code == 200) {
        // content = decyptData(content);
        const code = content.code;

        if (res.config.url.endsWith("Print")) {
          return res.data;
        }

        //拦截导出、打印返回data
        let type = "content-type";
        const renameFlag = res.headers["rename-flag"] == "1";
        if (res.headers[type] == "application/octet-stream" || renameFlag) {
          if (renameFlag) {
            return res;
          }
          return res.data;
        }

        if (code == 40000) {
          MessageBox.confirm(
            "登录状态已过期，您可以继续留在该页面，或者重新登录",
            "系统提示",
            {
              confirmButtonText: "重新登录",
              cancelButtonText: "取消",
              type: "warning",
            }
          ).then(() => {
            store.dispatch("FedLogOut").then(() => {
              location.reload(); // 为了重新实例化vue-router对象 避免bug
            });
          });
        } else if (code != 10000) {
          Notification.error({
            title: content.message || content.msg,
            message: "traceId:" + content.traceId,
          });
          // console.log("error-content", content);
          return Promise.reject(content.message || content.msg);
        } else {
          return content;
        }
      } else if (res.config.responseType && res.config.responseType == "blob") {
        // let data = decyptData(data);
        // console.log("daochu======res", res);
        // console.log("daochu======data", decyptData(res.data));
        return decyptData(res.data.content);
      } else {
        Notification.error({
          title: ret_msg,
          message: "traceId:" + content.traceId,
          // message:response.msg
        });
      }
    } else {
      //不走加密

      const code = res.data.code;
      if (res.config.url.endsWith("Print")) {
        return res.data;
      }

      //拦截导出、打印返回data
      let type = "content-type";
      const renameFlag = res.headers["rename-flag"] == "1";
      if (res.headers[type] == "application/octet-stream" || renameFlag) {
        if (renameFlag) {
          return res;
        }
        return res.data;
      }

      if (code == 40000) {
        MessageBox.confirm("登录状态已过期，请重新登录", "系统提示", {
          confirmButtonText: "重新登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          store.dispatch("FedLogOut").then(() => {
            location.reload(); // 为了重新实例化vue-router对象 避免bug
          });
        });
      } else if (code != 10000) {
        console.log("res", res);
        Notification.error({
          title: res.data.message || res.msg,
          message: "traceId:" + res.data.traceId,
        });
        return Promise.reject(res.data.message || res.msg);
      } else {
        return res.data;
      }
    }
  },
  (error) => {
    console.log("errrrrrr", error);
    if (location.protocol == "https:" && error.content) {
      error = decyptData(error.content);
    }
    Message({
      message: error.message,
      type: "error",
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
