/**
 * ServiceWorker 清理工具
 * 
 * 用于在非开发环境中清理可能存在的Mock系统ServiceWorker
 * 确保在测试和生产环境中不会有ServiceWorker干扰正常请求
 */

/**
 * 清理所有注册的ServiceWorker
 * @returns {Promise<boolean>} 清理是否成功
 */
async function cleanupServiceWorkers() {
  if (!('serviceWorker' in navigator)) {
    console.log('[SW-Cleanup] ServiceWorker not supported');
    return true;
  }

  try {
    // 获取所有已注册的ServiceWorker
    const registrations = await navigator.serviceWorker.getRegistrations();
    
    if (registrations.length === 0) {
      console.log('[SW-Cleanup] No ServiceWorkers found');
      return true;
    }

    console.log(`[SW-Cleanup] Found ${registrations.length} ServiceWorker(s), cleaning up...`);

    // 注销所有ServiceWorker
    const unregisterPromises = registrations.map(async (registration) => {
      try {
        const success = await registration.unregister();
        console.log(`[SW-Cleanup] Unregistered ServiceWorker: ${registration.scope}`, success);
        return success;
      } catch (error) {
        console.error(`[SW-Cleanup] Failed to unregister ServiceWorker: ${registration.scope}`, error);
        return false;
      }
    });

    const results = await Promise.all(unregisterPromises);
    const allSuccess = results.every(result => result === true);

    if (allSuccess) {
      console.log('[SW-Cleanup] All ServiceWorkers cleaned up successfully');
    } else {
      console.warn('[SW-Cleanup] Some ServiceWorkers failed to unregister');
    }

    return allSuccess;
  } catch (error) {
    console.error('[SW-Cleanup] Error during ServiceWorker cleanup:', error);
    return false;
  }
}

/**
 * 检查并清理Mock系统相关的ServiceWorker
 * @returns {Promise<boolean>} 清理是否成功
 */
async function cleanupMockServiceWorker() {
  if (!('serviceWorker' in navigator)) {
    return true;
  }

  try {
    const registrations = await navigator.serviceWorker.getRegistrations();
    
    // 查找Mock系统相关的ServiceWorker
    const mockRegistrations = registrations.filter(registration => 
      registration.scope.includes('sw-mock-manager') || 
      (registration.active && registration.active.scriptURL.includes('sw-mock-manager'))
    );

    if (mockRegistrations.length === 0) {
      console.log('[SW-Cleanup] No Mock ServiceWorkers found');
      return true;
    }

    console.log(`[SW-Cleanup] Found ${mockRegistrations.length} Mock ServiceWorker(s), cleaning up...`);

    // 注销Mock相关的ServiceWorker
    const unregisterPromises = mockRegistrations.map(async (registration) => {
      try {
        const success = await registration.unregister();
        console.log(`[SW-Cleanup] Unregistered Mock ServiceWorker: ${registration.scope}`, success);
        return success;
      } catch (error) {
        console.error(`[SW-Cleanup] Failed to unregister Mock ServiceWorker: ${registration.scope}`, error);
        return false;
      }
    });

    const results = await Promise.all(unregisterPromises);
    const allSuccess = results.every(result => result === true);

    return allSuccess;
  } catch (error) {
    console.error('[SW-Cleanup] Error during Mock ServiceWorker cleanup:', error);
    return false;
  }
}

/**
 * 根据环境自动执行清理
 */
function autoCleanup() {
  const isProduction = process.env.NODE_ENV === 'production';
  const isStaging = process.env.NODE_ENV === 'staging' || process.env.ENV === 'staging';
  const mockEnabled = process.env.VUE_APP_ENABLE_MOCK === 'true';

  // 在生产环境或测试环境中，或者Mock未启用时，清理ServiceWorker
  if (isProduction || isStaging || !mockEnabled) {
    console.log('[SW-Cleanup] Auto cleanup triggered - Environment:', process.env.NODE_ENV, 'Mock enabled:', mockEnabled);
    
    // 延迟执行清理，确保页面已完全加载
    setTimeout(() => {
      cleanupMockServiceWorker().then(success => {
        if (success) {
          console.log('[SW-Cleanup] Auto cleanup completed successfully');
        } else {
          console.warn('[SW-Cleanup] Auto cleanup completed with warnings');
        }
      });
    }, 1000);
  }
}

// 导出函数
export {
  cleanupServiceWorkers,
  cleanupMockServiceWorker,
  autoCleanup
};

// 默认导出自动清理函数
export default autoCleanup;
