import store from '@/store';

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
 export default function checkPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const permissions = store.getters && store.getters.permissions
    const permissionDatas = value
    const all_permission = "*:*:*";

    const hasPermission = permissions.some(permission => {
      return all_permission === permission || permissionDatas.includes(permission)
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkPermi="['system:user:add','system:user:edit']"`)
    return false
  }
}

export function checkRoles(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.roles
    const rolesDatas = value
    const all_permission = "";

    const hasRoles = roles.some(role => {
      return all_permission === role || rolesDatas.includes(role)
    })

    if (!hasRoles) {
      return false
    }
    return true
  } else {
    console.error(`"没有角色权限"`)
    return false
  }
}