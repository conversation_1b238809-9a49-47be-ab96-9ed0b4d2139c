/**
 * 简化的Mock系统初始化脚本
 *
 * 直接在浏览器环境中初始化Mock系统，避免模块导入问题
 */

// Mock系统配置
const MOCK_CONFIG = {
  // 只在开发环境且明确启用时才启用Mock系统
  enabled:
    process.env.VUE_APP_ENABLE_MOCK === "true" &&
    process.env.NODE_ENV === "development",
  serviceWorker: {
    scriptPath: "/sw-mock-manager.js",
    debug: process.env.NODE_ENV === "development",
  },
  runtime: {
    allowToggle: process.env.NODE_ENV === "development",
    consolePrefix: "MockSystem",
    storageKey: "mock-system-state",
  },
  apis: {
    "/st/lifePay/providerInfo/*": true,
    "/st/lifePay/bankFlow/*": true,
    "/st/lifePay/providerInfo/queryPage": true,
    "/st/lifePay/providerInfo/getDropLists": true,
    "/st/lifePay/providerInfo/add": true,
    "/st/lifePay/providerInfo/edit": true,
    "/st/lifePay/providerInfo/delete": true,
    "/st/lifePay/providerInfo/exportExcel": true,
    "/st/lifePay/providerInfo/importExcel": true,
  },
};

// Mock工具函数
const MockUtils = {
  isApiEnabled: (url) => {
    if (!MOCK_CONFIG.enabled) return false;

    // 检查精确匹配
    if (MOCK_CONFIG.apis.hasOwnProperty(url)) {
      return MOCK_CONFIG.apis[url];
    }

    // 检查通配符匹配
    for (const pattern in MOCK_CONFIG.apis) {
      if (pattern.includes("*")) {
        const regex = new RegExp(pattern.replace(/\*/g, ".*"));
        if (regex.test(url) && MOCK_CONFIG.apis[pattern]) {
          return true;
        }
      }
    }

    return false;
  },
};

// Mock系统类
class SimpleMockSystem {
  constructor() {
    this.isInitialized = false;
    this.serviceWorker = null;
    this.config = MOCK_CONFIG;
  }

  async init() {
    if (this.isInitialized) {
      console.log("[MockSystem] Already initialized");
      return true;
    }

    if (!this.config.enabled) {
      console.log("[MockSystem] Mock system is disabled");
      return false;
    }

    if (!("serviceWorker" in navigator)) {
      console.warn("[MockSystem] ServiceWorker not supported");
      return false;
    }

    try {
      // 注册ServiceWorker
      const registration = await navigator.serviceWorker.register(
        this.config.serviceWorker.scriptPath,
        { scope: "/" }
      );

      console.log("[MockSystem] ServiceWorker registered:", registration);

      // 等待ServiceWorker激活
      await this.waitForServiceWorker(registration);

      this.isInitialized = true;
      console.log("[MockSystem] Mock system initialized successfully");

      // 设置全局控制
      this.setupGlobalControls();

      return true;
    } catch (error) {
      console.error("[MockSystem] Failed to initialize:", error);
      return false;
    }
  }

  waitForServiceWorker(registration) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("ServiceWorker activation timeout"));
      }, 10000);

      if (registration.active) {
        clearTimeout(timeout);
        this.serviceWorker = registration.active;
        // 发送配置到ServiceWorker
        this.sendConfigToServiceWorker();
        resolve(registration.active);
        return;
      }

      const worker = registration.installing || registration.waiting;
      if (worker) {
        worker.addEventListener("statechange", () => {
          if (worker.state === "activated") {
            clearTimeout(timeout);
            this.serviceWorker = worker;
            // 发送配置到ServiceWorker
            this.sendConfigToServiceWorker();
            resolve(worker);
          }
        });
      } else {
        clearTimeout(timeout);
        reject(new Error("No ServiceWorker found"));
      }
    });
  }

  sendConfigToServiceWorker() {
    if (this.serviceWorker) {
      // 只发送可序列化的配置数据，不包含函数
      const serializableConfig = {
        enabled: this.config.enabled,
        apis: this.config.apis,
        serviceWorker: this.config.serviceWorker,
        runtime: this.config.runtime,
      };

      this.serviceWorker.postMessage({
        type: "INIT_MOCK_CONFIG",
        data: {
          config: serializableConfig,
        },
      });
      console.log("[MockSystem] Configuration sent to ServiceWorker");
    }
  }

  setupGlobalControls() {
    window.MockSystem = {
      enable: () => {
        this.config.enabled = true;
        console.log("[MockSystem] Mock system enabled");
      },
      disable: () => {
        this.config.enabled = false;
        console.log("[MockSystem] Mock system disabled");
      },
      clearCache: () => {
        if (this.serviceWorker) {
          this.serviceWorker.postMessage({ type: "CLEAR_CACHE" });
        }
        console.log("[MockSystem] Cache cleared");
      },
      sendConfig: () => {
        this.sendConfigToServiceWorker();
      },
      debug: () => {
        console.log("[MockSystem] Debug Info:");
        console.log("- Enabled:", this.config.enabled);
        console.log("- Initialized:", this.isInitialized);
        console.log("- ServiceWorker:", !!this.serviceWorker);
        console.log("- Config:", this.config);
        if (this.serviceWorker) {
          console.log("- ServiceWorker State:", this.serviceWorker.state);
          console.log("- ServiceWorker URL:", this.serviceWorker.scriptURL);
        }

        // 测试配置发送
        if (this.serviceWorker && this.serviceWorker.state === "activated") {
          console.log("[MockSystem] Testing config transmission...");
          this.sendConfigToServiceWorker();
        }
      },
      status: () => {
        const status = {
          enabled: this.config.enabled,
          initialized: this.isInitialized,
          serviceWorker: !!this.serviceWorker,
        };
        console.log("Mock System Status:", status);
        return status;
      },
      config: () => {
        console.log("Mock System Config:", this.config);
        return this.config;
      },
      apis: () => {
        console.log("Mocked APIs:", this.config.apis);
        return this.config.apis;
      },
      test: (url) => {
        const isMocked = MockUtils.isApiEnabled(url);
        console.log(`API ${url} is ${isMocked ? "MOCKED" : "NOT MOCKED"}`);
        return isMocked;
      },
    };

    console.log("[MockSystem] Global controls available via window.MockSystem");
  }

  getStatus() {
    return {
      enabled: this.config.enabled,
      initialized: this.isInitialized,
      serviceWorker: !!this.serviceWorker,
      config: this.config,
    };
  }
}

// 创建全局实例并自动初始化
const mockSystem = new SimpleMockSystem();

// 自动初始化
function initMockSystem() {
  // 严格检查：只在开发环境且明确启用时才初始化
  if (
    MOCK_CONFIG.enabled &&
    typeof window !== "undefined" &&
    process.env.NODE_ENV === "development" &&
    process.env.VUE_APP_ENABLE_MOCK === "true"
  ) {
    console.log("[MockInit] Starting Mock System initialization...");

    mockSystem
      .init()
      .then((success) => {
        if (success) {
          console.log("[MockInit] Mock System ready!");
        } else {
          console.warn("[MockInit] Mock System initialization failed");
        }
      })
      .catch((error) => {
        console.error("[MockInit] Mock System error:", error);
      });
  } else {
    console.log(
      "[MockInit] Mock System disabled - Environment:",
      process.env.NODE_ENV,
      "Enable Mock:",
      process.env.VUE_APP_ENABLE_MOCK
    );
  }
}

// 导出初始化函数
export default initMockSystem;
export { mockSystem, MOCK_CONFIG, MockUtils };
