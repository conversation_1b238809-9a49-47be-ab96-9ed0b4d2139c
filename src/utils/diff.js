import Diff from "jsdiff-esm";

// 转义正则表达式的关键字
const escapeRegExp = (input) => {
  const regexKeywords = /[-/\\^$*+?.()|[\]{}]/g;
  return input.replace(regexKeywords, "\\$&");
};

// 分割字符串，每n个一组
const splitString = (inputString, chunkSize) => {
  const result = [];
  for (let i = 0; i < inputString.length; i += chunkSize) {
    result.push(inputString.slice(i, i + chunkSize));
  }
  return result;
};

// 拼接多页内容，用--PAGEEND--作为分割符
const concatPage = (list) => {
  const strList = list.map((item) => item.innerHTML);
  return strList.join("--PAGEEND--");
};

// 对pdf文件每页内容按照top值重排顺序,并拼接多页内容
const resetPdfSpan = (list) => {
  const strList = list.map((page) => {
    const l = [];
    let lastTop = 0;
    Array.from(page.children).forEach((node) => {
      const obj = {
        node: node.outerHTML,
        top: node.style.top
          ? parseFloat(node.style.top.replace("%", ""))
          : lastTop,
      };
      l.push(obj);
      lastTop = obj.top;
    });
    return l
      .sort((a, b) => a.top - b.top)
      .map((r) => r.node)
      .join("");
  });
  return strList.join("--PAGEEND--");
};
// 新增：自定义高亮处理器
const applyCustomHighlights = (html, highlights) => {
  if (!highlights?.length) return html;

  const escapeRegExp = (str) => str.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const parser = new DOMParser();
  const doc = parser.parseFromString(
    `<div id="root">${html}</div>`,
    "text/html"
  );
  const root = doc.getElementById("root");

  // 初始化文本节点索引（单次遍历）
  let textNodes = (() => {
    const nodes = [];
    const treeWalker = doc.createTreeWalker(
      root,
      NodeFilter.SHOW_TEXT,
      (node) =>
        node.textContent.trim()
          ? NodeFilter.FILTER_ACCEPT
          : NodeFilter.FILTER_REJECT
    );

    let globalOffset = 0;
    while (treeWalker.nextNode()) {
      const node = treeWalker.currentNode;
      const text = node.textContent;
      nodes.push({
        node,
        start: globalOffset,
        end: globalOffset + text.length,
        text,
      });
      globalOffset += text.length;
    }
    return nodes;
  })();

  // 反向处理高亮规则防止偏移变化
  [...highlights]
    .reverse()
    .forEach(({ text: context, highlight, color = "red" }) => {
      const fullText = textNodes.map((n) => n.text).join("");
      const regex = new RegExp(escapeRegExp(highlight), "gi");
      let match;
      const matches = [];

      // 先收集所有匹配位置
      while ((match = regex.exec(fullText)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + highlight.length,
        });
      }

      // 反向处理匹配项
      matches.reverse().forEach(({ start, end }) => {
        let currentPos = start;
        let remaining = end - start;

        while (remaining > 0) {
          // 二分查找优化定位节点
          const nodeIndex = binarySearch(textNodes, currentPos);
          if (nodeIndex === -1) break;

          const node = textNodes[nodeIndex];
          const localStart = currentPos - node.start;
          const localEnd = Math.min(localStart + remaining, node.text.length);

          // 安全边界检查
          if (localStart > node.text.length || localEnd > node.text.length) {
            console.warn("Invalid highlight range:", {
              localStart,
              localEnd,
              node,
            });
            break;
          }

          try {
            // 分割文本节点
            const range = doc.createRange();
            const textNode = node.node;
            range.setStart(textNode, localStart);
            range.setEnd(textNode, localEnd);

            // 创建高亮元素
            const span = doc.createElement("span");
            span.style.backgroundColor = color;
            span.setAttribute("data-highlight", "true");
            range.surroundContents(span);

            // 更新文本节点索引
            const newNodes = [];
            if (localStart > 0) {
              const beforeNode = textNode.splitText(localStart);
              newNodes.push({
                node: textNode,
                start: node.start,
                end: node.start + localStart,
                text: textNode.textContent,
              });
              textNode = beforeNode;
            }

            const highlightNode = textNode.splitText(localEnd - localStart);
            newNodes.push({
              node: span.firstChild,
              start: node.start + localStart,
              end: node.start + localEnd,
              text: span.textContent,
            });

            if (highlightNode.length > 0) {
              newNodes.push({
                node: highlightNode,
                start: node.start + localEnd,
                end: node.end,
                text: highlightNode.textContent,
              });
            }

            // 替换旧节点并更新后续索引
            textNodes.splice(
              nodeIndex,
              1,
              ...newNodes.filter((n) => n.text.length > 0)
            );
            updateOffsets(textNodes, nodeIndex);

            remaining -= localEnd - localStart;
            currentPos += localEnd - localStart;
          } catch (error) {
            console.error("Highlight error:", error);
            break;
          }
        }
      });
    });

  return root.innerHTML;
};

// 二分查找定位节点
const binarySearch = (nodes, position) => {
  let left = 0;
  let right = nodes.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    if (position < nodes[mid].start) {
      right = mid - 1;
    } else if (position >= nodes[mid].end) {
      left = mid + 1;
    } else {
      return mid;
    }
  }
  return -1;
};

// 更新后续节点偏移量
const updateOffsets = (nodes, startIndex) => {
  let currentOffset = startIndex > 0 ? nodes[startIndex - 1].end : 0;
  for (let i = startIndex; i < nodes.length; i++) {
    nodes[i].start = currentOffset;
    currentOffset += nodes[i].text.length;
    nodes[i].end = currentOffset;
  }
};
// const applyCustomHighlights = (html, highlights) => {
//   if (!highlights?.length) return html;

//   // 创建虚拟DOM进行操作
//   const parser = new DOMParser();
//   console.log("1parser", parser);
//   const doc = parser.parseFromString(
//     `<div id="root">${html}</div>`,
//     "text/html"
//   );
//   console.log("2doc", doc);
//   const root = doc.getElementById("root");
//   console.log("3root", root);

//   // 文本节点遍历器
//   const treeWalker = doc.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
//     acceptNode: (node) =>
//       node.textContent.trim()
//         ? NodeFilter.FILTER_ACCEPT
//         : NodeFilter.FILTER_REJECT,
//   });
//   console.log("4treeWalker", treeWalker);

//   // 构建文本节点位置索引
//   let textOffset = 0;
//   const textNodes = [];
//   while (treeWalker.nextNode()) {
//     const node = treeWalker.currentNode;
//     const text = node.textContent;
//     textNodes.push({
//       node,
//       start: textOffset,
//       end: textOffset + text.length,
//       text,
//     });
//     textOffset += text.length;
//   }
//   console.log("5textNodes", textNodes);

//   // 遍历所有高亮规则
//   highlights.forEach(({ text, highlight, color = "red" }) => {
//     // 在合并文本中查找所有匹配位置
//     const fullText = textNodes.map((n) => n.text).join("");
//     const regex = new RegExp(escapeRegExp(highlight), "gi");
//     let match;

//     while ((match = regex.exec(fullText)) !== null) {
//       const start = match.index;
//       const end = start + highlight.length;

//       // 查找对应的文本节点
//       let currentPos = start;
//       let remaining = highlight.length;

//       while (remaining > 0) {
//         const nodeInfo = textNodes.find(
//           (n) => n.start <= currentPos && n.end >= currentPos
//         );
//         if (!nodeInfo) break;

//         // 计算在当前节点中的偏移量
//         const nodeStart = currentPos - nodeInfo.start;
//         const nodeLength = Math.min(remaining, nodeInfo.end - currentPos);

//         // 分割文本节点
//         const range = doc.createRange();
//         range.setStart(nodeInfo.node, nodeStart);
//         range.setEnd(nodeInfo.node, nodeStart + nodeLength);

//         // 创建高亮元素
//         const span = doc.createElement("span");
//         span.style.backgroundColor = color;
//         span.style.position = "relative";
//         span.style.zIndex = "5";
//         range.surroundContents(span);

//         // 更新位置
//         currentPos += nodeLength;
//         remaining -= nodeLength;
//       }
//     }
//   });

//   return root.innerHTML;
// };
// const applyCustomHighlights = (html, highlights) => {
//   if (!highlights?.length) return html;

//   // 分页处理
//   const pages = html.split("--PAGEEND--");

//   return pages
//     .map((pageHtml) => {
//       let processedHtml = pageHtml;

//       highlights.forEach(
//         ({ text: contextText, highlight: target, color = "#FFFF00" }) => {
//           // 创建临时元素处理HTML结构
//           const tempDiv = document.createElement("div");
//           tempDiv.innerHTML = processedHtml;

//           // 获取纯文本内容用于定位
//           const fullText = tempDiv.textContent;
//           const startIndex = fullText.indexOf(contextText);
//           console.log("startIndex", contextText, startIndex);
//           if (startIndex === -1) return;

//           // 在原始HTML中定位上下文范围
//           const [startOffset, endOffset] = findHTMLPosition(
//             processedHtml,
//             fullText,
//             startIndex,
//             contextText.length
//           );

//           // 提取上下文对应的HTML片段
//           const contextHTML = processedHtml.slice(startOffset, endOffset);
//           console.log("contextHTML", contextHTML);
//           console.log("target", target);
//           // 在上下文片段中高亮目标词
//           const highlighted = contextHTML.replace(
//             new RegExp(`(${escapeRegExp(target)})`, "gi"),
//             `<span style="background:${color};position:relative;z-index:5;">$1</span>`
//           );

//           // 替换原HTML中的上下文片段
//           processedHtml =
//             processedHtml.slice(0, startOffset) +
//             highlighted +
//             processedHtml.slice(endOffset);
//         }
//       );

//       return processedHtml;
//     })
//     .join("--PAGEEND--");
// };

// 比较新老内容，处理差异
export const diffDocument = (oldData, newData, customHighlights) => {
  console.log("到择了吗");
  const oldSelector = oldData.selector;
  const newSelector = newData.selector;
  const oldType = oldData.type;
  const newType = newData.type;

  const oldNodeList =
    typeof oldSelector === "string"
      ? Array.from(document.querySelectorAll(oldSelector))
      : oldSelector;
  const newNodeList =
    typeof newSelector === "string"
      ? Array.from(document.querySelectorAll(newSelector))
      : newSelector;

  if (oldNodeList.length === 0 || newNodeList.length === 0) {
    return 1;
  }
  console.log("oldNodeList啊啊啊啊啊啊啊啊", oldNodeList);
  console.log("newNodeList啊啊啊啊啊啊啊啊", newNodeList);
  const oldNodeStr =
    typeof oldSelector === "string"
      ? concatPage(oldNodeList)
      : resetPdfSpan(oldNodeList);
  const newNodeStr =
    typeof newSelector === "string"
      ? concatPage(newNodeList)
      : resetPdfSpan(newNodeList);

  const threshold = typeof oldSelector === typeof newSelector ? 0.1 : 0.05;
  let diffList = [];
  let similarity = 0;
  const chunkSize = 5;

  const getSimilarity = () => {
    const oStr = oldNodeStr
      .replace(/<[^>]*>/g, "")
      .replace(/\s/g, "")
      .replace(/--PAGEEND--/g, "");
    const nStr = newNodeStr
      .replace(/<[^>]*>/g, "")
      .replace(/\s/g, "")
      .replace(/--PAGEEND--/g, "");
    console.time();
    diffList = Diff.diffLines(
      splitString(oStr, chunkSize).join("\n"),
      splitString(nStr, chunkSize).join("\n")
    );
    console.timeEnd();
    console.log(`以${chunkSize}个字符为一段获取的差异`, diffList);
    const a = diffList.filter((item) => item.added);
    const d = diffList.filter((item) => item.removed);
    const n = diffList.filter((item) => !item.added && !item.removed);
    const aNum = a.reduce((num, item) => num + item.value.length, 0);
    const dNum = d.reduce((num, item) => num + item.value.length, 0);
    const nNum = n.reduce((num, item) => num + item.value.length, 0);
    similarity = 1 - (aNum + dNum) / (nNum + aNum + dNum);
    console.log("数据量", `未改变${nNum}`, `新增${aNum}`, `删除${dNum}`);
    console.log("相似度", similarity);
  };
  getSimilarity();

  let byTag = false;
  if (similarity < threshold) {
    byTag = true;
    if (similarity > 0.01) {
      console.log("相似度低于阈值,以段落为单位标记差异");
      const reg = new RegExp("</span>", "g");
      diffList = Diff.diffLines(
        typeof oldSelector === "string"
          ? oldNodeStr.replace(/\<\/p>/g, "\n").replace(/<[^>]*>/g, "")
          : oldNodeStr.replace(reg, "\n").replace(/<[^>]*>/g, ""),
        typeof newSelector === "string"
          ? newNodeStr.replace(/\<\/p>/g, "\n").replace(/<[^>]*>/g, "")
          : newNodeStr.replace(reg, "\n").replace(/<[^>]*>/g, "")
      );
    } else {
      console.log("相似度过低");
      return similarity;
    }
  } else {
    if (similarity === 1) {
      return 1;
    }
    diffList = Diff.diffChars(
      oldNodeStr.replace(/<[^>]*>/g, ""),
      newNodeStr.replace(/<[^>]*>/g, "")
    );
  }
  console.log(diffList);

  const addList = [];
  const removeList = [];
  let reg = new RegExp("", "g");
  let lastIndexOld = 0;
  let lastIndexNew = 0;
  let addNum = 0;
  let delNum = 0;

  diffList.forEach((item) => {
    if (!item.removed) {
      if (byTag) {
        item.value = item.value.replace(/\n/g, "");
      }
      const strArr = byTag ? item.value.split("") : item.value.split("");
      const strLastIndexList = [];
      strArr.forEach((s) => {
        reg = new RegExp(
          `(<[^>]*>)*([^${escapeRegExp(s)}])*${escapeRegExp(s)}`,
          "gi"
        );
        reg.lastIndex = lastIndexOld;
        reg.exec(newNodeStr);
        lastIndexOld = reg.lastIndex;
        strLastIndexList.push(reg.lastIndex);
      });
      if (item.added) {
        const len = strLastIndexList.length;
        addList.push({
          value: item.value[0],
          lastIndex: strLastIndexList[0],
        });
        let i = 1;
        while (i < len) {
          const lastIndex = strLastIndexList[i - 1];
          const nowIndex = strLastIndexList[i];
          if (nowIndex === lastIndex + 1) {
            addList[addList.length - 1].value = `${
              addList[addList.length - 1].value
            }${item.value[i]}`;
          } else {
            addList.push({
              value: item.value[i],
              lastIndex: strLastIndexList[i],
            });
          }
          i += 1;
        }
        addNum += strArr.length;
      }
    }
  });
  console.log("新增列表addList", addList);

  diffList.forEach((item) => {
    if (!item.added) {
      if (byTag) {
        item.value = item.value.replace(/\n/g, "");
      }
      const strArr = byTag ? item.value.split("") : item.value.split("");
      const strLastIndexList = [];
      strArr.forEach((s) => {
        reg = new RegExp(
          `(<[^>]*>)*([^${escapeRegExp(s)}])*${escapeRegExp(s)}`,
          "gi"
        );
        reg.lastIndex = lastIndexNew;
        reg.exec(oldNodeStr);
        lastIndexNew = reg.lastIndex;
        strLastIndexList.push(reg.lastIndex);
      });
      if (item.removed) {
        const len = strLastIndexList.length;
        removeList.push({
          value: item.value[0],
          lastIndex: strLastIndexList[0],
        });
        let i = 1;
        while (i < len) {
          const lastIndex = strLastIndexList[i - 1];
          const nowIndex = strLastIndexList[i];
          if (nowIndex === lastIndex + 1) {
            removeList[removeList.length - 1].value = `${
              removeList[removeList.length - 1].value
            }${item.value[i]}`;
          } else {
            removeList.push({
              value: item.value[i],
              lastIndex: strLastIndexList[i],
            });
          }
          i += 1;
        }
        delNum += strArr.length;
      }
    }
  });
  console.log("删除列表removeList", removeList);

  let oldHtml = oldNodeStr;
  let increment = 0;
  const regex = /[^ \t\r\n\v\f]/;
  // removeList.forEach((item) => {
  //   if (item.value !== undefined && regex.test(item.value)) {
  //     reg = new RegExp(`(${escapeRegExp(item.value)})`, "y");
  //     reg.lastIndex = item.lastIndex + increment - 1;
  //     oldHtml = oldHtml.replace(reg, (word) => {
  //       if (item.value.includes("--PAGEEND--")) {
  //         const list = word.split("--PAGEEND--");
  //         const signList = list.map((s) => {
  //           if (s !== "") {
  //             increment += 47;
  //             return `<span style="background-color:${
  //               oldType === "pdf" ? "#e33f58" : "#fac5cd"
  //             };">${s}</span>`;
  //           }
  //           return "";
  //         });
  //         return signList.join("--PAGEEND--");
  //       }
  //       increment += 47;
  //       return `<span style="background-color:${
  //         oldType === "pdf" ? "#e33f58" : "#fac5cd"
  //       };">${word}</span>`;
  //     });
  //   }
  // });

  let newHtml = newNodeStr;
  increment = 0;
  // addList.forEach((item) => {
  //   if (item.value !== undefined && regex.test(item.value)) {
  //     reg = new RegExp(`(${escapeRegExp(item.value)})`, "y");
  //     reg.lastIndex = item.lastIndex + increment - 1;
  //     newHtml = newHtml.replace(reg, (word) => {
  //       if (item.value.includes("--PAGEEND--")) {
  //         const list = word.split("--PAGEEND--");
  //         const signList = list.map((s) => {
  //           if (s !== "") {
  //             increment += 47;
  //             return `<span style="background-color:${
  //               newType === "pdf" ? "#5ee983" : "#c7f0d2"
  //             };">${s}</span>`;
  //           }
  //           return "";
  //         });
  //         return signList.join("--PAGEEND--");
  //       }
  //       increment += 47;
  //       return `<span style="background-color:${
  //         newType === "pdf" ? "#5ee983" : "#c7f0d2"
  //       };">${word}</span>`;
  //     });
  //   }
  // });
  // oldHtml = applyCustomHighlights(oldHtml, customHighlights);
  console.log("newHtml", newHtml, customHighlights);
  newHtml = applyCustomHighlights(newHtml, customHighlights);
  console.log("oldHtml", oldHtml);
  const oldHtmlList = oldHtml.split("--PAGEEND--");
  const newHtmlList = newHtml.split("--PAGEEND--");
  oldNodeList.forEach((node, i) => {
    node.innerHTML = oldHtmlList[i];
  });
  newNodeList.forEach((node, i) => {
    node.innerHTML = newHtmlList[i];
  });

  // if (typeof oldSelector === "string" && typeof newSelector === "string") {
  //   const hostElement = document.querySelector(".old-docx");
  //   if (hostElement) {
  //     const shadowRoot = hostElement.attachShadow({ mode: "open" });
  //     const divElement = document.createElement("div");
  //     divElement.innerHTML = hostElement.innerHTML;
  //     hostElement.innerHTML = "";
  //     const style = document.createElement("style");
  //     style.textContent = `.docx-wrapper {
  //       background-color: #fff !important;
  //       padding: 0 !important;
  //     }
  //     .docx-wrapper>section.docx {
  //       margin-bottom: 0px !important;
  //       box-shadow: none !important;
  //     }`;
  //     shadowRoot.appendChild(style);
  //     shadowRoot.appendChild(divElement);
  //   }
  // }

  const oldNodeStrLength = oldNodeStr.replace(/<[^>]*>/g, "").length;
  return 1 - (addNum + delNum) / (oldNodeStrLength + addNum);
};
