import axios from 'axios';
import { Message } from 'element-ui';

import { ab2str } from '@/utils/index';

// 判断请求的来源是内网还是外网
const isInternalNetwork = window.location.hostname.includes(process.env.VUE_APP_BASE_API_PREFIX_INTERNAL);

// 根据内外网类型选择 API 前缀
const apiPrefix = isInternalNetwork ? process.env.VUE_APP_BASE_API_INTERNAL : process.env.VUE_APP_BASE_API_EXTERNAL;

const service1 = axios.create({
  baseURL: apiPrefix, // url = base url + request url
  timeout: 5000 // request timeout
});

service1.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    // 流
    const resAb = response.data;
    let resStr = '';
    resStr = ab2str(resAb, resStr);
    let resObj = {};
    try {
      resObj = JSON.parse(resStr);
    } catch {
      // 存在异常
    }
    // 存在异常
    if (resObj.code) {
      Message({
        message: resObj.msg || 'Error',
        type: 'error',
        duration: 5 * 1000
      });
      return;
    }
    // 导出
    const headers = response.headers;
    if (headers['content-type'] === 'application/vnd.ms-excel; charset=UTF-8') {
      return response;
    }
  },
  error => {
    // 网络异常等其它情况
    // eslint-disable-next-line
    console.log("err" + error); // for debug
    Message({
      message: '网络异常，请重试',
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default service1;
