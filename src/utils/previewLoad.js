/* eslint-disable no-undef */
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader.js";
// import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';
import { EXRLoader } from "three/examples/jsm/loaders/EXRLoader.js";
const BASE_THREE_URL = process.env.VUE_APP_THREE_BASE;
// import model from '@/views/test/model/test.glb'
let roomModel = null; //模型
let bgTexture = null; //环境贴图
const loadRoomModel = () => {
  const dracoLoader = new DRACOLoader();
  dracoLoader.setDecoderPath(`${BASE_THREE_URL}/three-demo/draco/gltf/`);
  const loader = new GLTFLoader();
  loader.setDRACOLoader(dracoLoader);
  // loader.load(model, (res) => {
  loader.load(`${BASE_THREE_URL}/three-demo/test.glb`, res => {
    console.log(res);
    roomModel = res;
  });
};
const loadExr = () => {
  // const loader = new RGBELoader();
  const loader = new EXRLoader();
  loader.load(`${BASE_THREE_URL}/three-demo/env.exr`, texture => {
    bgTexture = texture;
  });
};
setTimeout(() => {
  loadExr();
  loadRoomModel();
}, 5000);

window._getRoomModel = () => {
  return new Promise(resolve => {
    const timer = setInterval(() => {
      if (roomModel) {
        resolve(roomModel);
        clearInterval(timer);
      }
    }, 30);
  });
};
window._getExr = () => {
  return new Promise(resolve => {
    const timer = setInterval(() => {
      if (bgTexture) {
        resolve(bgTexture);
        clearInterval(timer);
      }
    }, 30);
  });
};
