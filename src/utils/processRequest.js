import axios from "axios";
import {Notification, MessageBox, Message} from "element-ui";
import store from "@/store";
import {getToken} from "@/utils/auth";
import qs from 'qs'

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// 创建axios实例
const service = axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    //  baseURL: "http://10.102.151.13:19999/project-flow-test",
    baseURL: process.env.VUE_APP_BASE_API_PROCESS,
    // 超时
    timeout: 10000
});
// request拦截器
service.interceptors.request.use(
    config => {
        if (getToken()) {
            config.headers.Authorization = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
        }
        if(config.method==='post'){
          config.data={
            app: process.env.VUE_APP_WHALE_FLOW_KEY,
            ...config.data
          }
        }else if(config.method==='get'){
          config.params={
            app: process.env.VUE_APP_WHALE_FLOW_KEY,
            ...config.params
          }
        }
        return config;
    },
    error => {
        Promise.reject(error);
    }
);

// 响应拦截器
service.interceptors.response.use(
    res => {
        const code = res.data.code;
        if (res.config.url.endsWith("PdfPrint")) {
            return res.data;
        }

        //用于判断是否是导出excel接口
        if (res.headers['content-type'] == "application/octet-stream") {
            return res.data;
        }

        if (code === 401) {
            MessageBox.confirm(
                "登录状态已过期，您可以继续留在该页面，或者重新登录",
                "系统提示",
                {
                    confirmButtonText: "重新登录",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            ).then(() => {
                store.dispatch("LogOut").then(() => {
                    location.reload(); // 为了重新实例化vue-router对象 避免bug
                });
            }).catch(() => {
                store.dispatch("LogOut").then(() => {
                    location.reload(); // 为了重新实例化vue-router对象 避免bug
                });
            });
        } else if (code == 210) {
            Message.error(res.data.msg);
            return Promise.reject("error");
        } else if (code != 10000) {
            Notification.error({
              title: res.data.message,
              // message: res.data.traceId ? ("TRACE: " + res.data.traceId): ""
            });
            return Promise.reject("error");
        } else {
            return res.data;
        }
    },
    error => {
        Message({
            message: error.message,
            type: "error",
            duration: 5 * 1000
        });
        return Promise.reject(error);
    }
);

export default service;
