import axios from "axios";
// 文件流下载
export const fileDownLoad = function(res, name) {
  return new Promise((resolve, reject) => {
    // 假设这里是下载逻辑
    // resolve() 表示下载成功，可以根据实际情况处理错误情况
    // 这里简化为 setTimeout 模拟异步操作
    setTimeout(() => {
      if (!name) {
        var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
        var contentDisposition = decodeURI(res.headers["content-disposition"]);
        var result = patt.exec(contentDisposition);
        var fileName = result[1];
        //  fileName = fileName.replace(/\"/g, "");
      }
      const elink = document.createElement("a");
      elink.download = name || fileName;
      elink.style.display = "none";
      const blob = new Blob([res.data], { type: "application/pdf" });
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click();
      document.body.removeChild(elink);
      resolve(); // 模拟下载完成
    }, 500); // 假设下载需要1秒钟
  });
};

// post接口返回文件流处理
export function downLoadFile(url, data, fileName1, type) {
  fileName = fileName1;
  axios({
    method: "post",
    url: baseURL + url,
    data: data,
    responseType: "blob",
    headers: {},
  }).then((res) => {
    resolveBlob(res, mimeMap[type]);
  });
}

export function resolveBlob(res, mimeType) {
  const aLink = document.createElement("a");
  var blob = new Blob([res.data], { type: mimeType });
  // //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
  if (!fileName) {
    var patt = new RegExp("filename=([^;]+\\.[^\\.;]+);*");
    var contentDisposition = decodeURI(res.headers["content-disposition"]);
    var result = patt.exec(contentDisposition);
    fileName = result[1];
    fileName = fileName.replace(/\"/g, "");
  }
  aLink.href = URL.createObjectURL(blob);
  aLink.setAttribute("download", fileName); // 设置下载文件名称
  document.body.appendChild(aLink);
  aLink.click();
  document.body.appendChild(aLink);
}
export function downloadUrl(url, filename) {
  console.log(url, filename, "===下载");
  return new Promise((resolve, reject) => {
    // 假设这里是下载逻辑
    // resolve() 表示下载成功，可以根据实际情况处理错误情况
    // 这里简化为 setTimeout 模拟异步操作
    setTimeout(() => {
      const elelink = document.createElement("a");
      elelink.style.display = "none";
      elelink.target = filename?.includes("pdf") ? "_blank" : "_self";
      elelink.href = url;
      elelink.download = filename;
      document.body.appendChild(elelink);
      elelink.click();
      document.body.removeChild(elelink);
      resolve(); // 模拟下载完成
    }, 500); // 假设下载需要1秒钟
  });
}
export function getNameFromUrl(url) {
  const match = url.match(/\/([^\/]+)$/);
  if (match) {
    return match[1];
  }
}
