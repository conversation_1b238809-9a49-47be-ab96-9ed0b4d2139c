<!-- 甘特图 -->
<template>
  <div ref="gantt"></div>
</template>

<script>
import { gantt } from "dhtmlx-gantt";
export default {
  name: "gantt",
  props: {
    tasks: {
      type: Object,
      default() {
        return { data: [], links: [] };
      },
    },
    columns: {
      type: Array,
      default() {
        return []
      }
    }
  },

  methods: {
    weekScaleTemplate(date) {
      var dateToStr = gantt.date.date_to_str("%d");
      var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
      const weekNum = gantt.date.date_to_str("W%W  ");
      return weekNum(date) + dateToStr(date) + " - " + dateToStr(endDate);
    },
    weekScaleTemplate2(date) {
      var dateToStr = gantt.date.date_to_str("%d %M");
      var endDate = gantt.date.add(gantt.date.add(date, 1, "week"), -1, "day");
      return dateToStr(date) + " - " + dateToStr(endDate);
    },
    setScaleConfig(level) {
      switch (level) {
        case "day":
          gantt.config.scales = [{ unit: "day", step: 1, format: "%d %M" }];
          gantt.config.scale_height = 27;
          break;
        case "week":
          gantt.config.scales = [
            { unit: "week", step: 1, format: this.weekScaleTemplate2 },
          ];
          gantt.config.scale_height = 27;
          break;
        case "month":
          gantt.config.scales = [{ unit: "month", step: 1, format: "%F, %Y" }];
          gantt.config.scale_height = 27;
          break;
        case "year":
          gantt.config.scales = [{ unit: "year", step: 1, format: "%Y" }];
          gantt.config.scale_height = 27;
          break;
      }
    },
  },

  mounted() {
    gantt.config.drag_move = false;
    gantt.config.drag_resize = false;
    gantt.config.drag_links = false;
    gantt.config.show_progress = false;
    gantt.config.details_on_create = false;
    gantt.config.open_split_tasks = false;
    gantt.config.details_on_dblclick = false;

    gantt.config.scale_height = 80;
    gantt.config.xml_date = "%Y-%m-%d";

    gantt.config.scales = [
      { unit: "year", step: 1, format: "%Y" },
      { unit: "month", step: 1, format: "%F" },
      { unit: "day", step: 1, format: "%d" + "号" },
      // { unit: "week", step: 1, format: this.weekScaleTemplate },
    ];

    //自定义左侧列
    gantt.config.columns = this.columns

    gantt.i18n.setLocale("cn");
    gantt.init(this.$refs.gantt);
    gantt.parse(this.$props.tasks);
  },
};
</script>

<style>
@import "~dhtmlx-gantt/codebase/dhtmlxgantt.css";

/* 隐藏加号新增 */
/* .gantt_grid_head_add,
.gantt_add {
  display: none;
} */
</style>
