<template>
  <el-upload
    ref="upload"
    class="avatar-uploader"
    :accept="accept"
    :action="upload.uploadPath"
    :headers="upload.headers"
    :data="uploadParam"
    :show-file-list="showFileList"
    :on-remove="handleRemove"
    :on-success="handleFileSuccess"
    :on-change="handleChange"
    :before-upload="beforeAvatarUpload"
    :multiple="true"
    :limit="limit"
    style="display: inline-block;"
    :drag="allowDrag"
    :disabled="disabled"
  >
    <div>
      <div v-if="type == 'img'">
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <video
            v-if="
              item.storePath.toLowerCase().indexOf('.mp4') > -1 ||
                item.storePath.toLowerCase().indexOf('.3gp') > -1 ||
                item.storePath.toLowerCase().indexOf('.mov') > -1
            "
            id="myVideo"
            class="video-js vjs-default-skin vjs-big-play-centered"
            controls
            preload="auto"
            @ended="onPlayerEnded($event)"
            @play="openDialog"
            @click="openDialog"
          >
            <source :src="item.storePath" />
          </video>
          <el-image
            v-if="
              item.storePath.toLowerCase().indexOf('.jpg') > -1 ||
                item.storePath.toLowerCase().indexOf('.jpeg') > -1 ||
                item.storePath.toLowerCase().indexOf('.png') > -1
            "
            :src="item.storePath"
            alt="加载失败"
            class="avatar"
            @click.stop="clickImg(index)"
            :preview-src-list="
              fileList
                .map((x) => x.storePath)
                .filter(
                  (i) =>
                    i.toLowerCase().indexOf('.jpg') > -1 ||
                    i.toLowerCase().indexOf('.jpeg') > -1 ||
                    i.toLowerCase().indexOf('.png') > -1
                )
            "
          />
          <span
            v-show="!disabled"
            class="delete-file"
            @click.stop="handleFileRemove(index)"
          >
            ×
          </span>
        </div>

        <i
          v-if="!disabled && fileList.length < limit"
          class="el-icon-plus avatar-uploader-icon"
        />
      </div>
      <el-button
        slot="tip"
        type="primary"
        size="mini"
        icon="el-icon-plus"
        v-else
        :disabled="fileList.length == limit"
      >
        上传
      </el-button>
    </div>
    <!-- <PicPreview ref="picPreview"></PicPreview> -->
    <!--图片预览-->
    <el-dialog
      title="图片预览"
      width="30%"
      :visible.sync="showImgPreview"
      @close="showImgPreview = false"
      append-to-body
    >
      <div style="display: flex;justify-content: center;padding: 20px">
        <el-image
          :src="previewImgUrl"
          fit="contain"
          alt="加载失败"
          style="width: 50vh; height: 50vh; "
        />
      </div>
    </el-dialog>
    <el-dialog
      title="视频播放"
      width="30%"
      :visible.sync="showVideoDialog"
      @close="closePreviewDialog"
      append-to-body
    >
      <div style="display: flex;justify-content: center;padding: 20px">
        <video
          id="myVideo"
          width="350"
          height="450"
          class="video-js vjs-default-skin vjs-big-play-centered"
          controls
          preload="auto"
          autoplay
          @ended="onPlayerEnded($event)"
        >
          <source :src="videoUrl" />
        </video>
      </div>
    </el-dialog>
  </el-upload>
</template>

<script>
import { getToken } from "@/utils/auth";
// import PicPreview from "@/components/Upload/picPreview.vue";

export default {
  name: "fileUpload",
  // components: { PicPreview },
  props: {
    //上传组件类型
    type: {
      type: String,
      default: "doc", //上传类型支持文件和图片:doc|img
    },
    //限制可以上传的文件数量
    limit: {
      type: Number,
      default: 1,
    },
    //是否显示文件列表
    showFileList: {
      type: Boolean,
      default: false,
    },
    //文件最大值
    fileMaxSize: {
      type: Number,
      default: 20, //默认20M
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    //是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    //是否允许拖拽
    allowDrag: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      accept: "",
      fileList: [],
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        uploadPath: baseUrl + "/doc/upload",
      },
      uploadParam: {},
      showImgPreview: false, //显示图片预览弹框
      showVideoDialog: false,
      previewImgUrl: "", //预览图片地址
      videoUrl: "", //视频地址
      myPlayer: null,
    };
  },
  mounted() {
    //初始化文件上传类型
    this.accept = this.type == "img" ? ".jpg,.jpeg,.png,.JPG,.JPEG,.PNG" : "*";
  },
  methods: {
    setFileList(files) {
      this.fileList = files;
    },
    get(item) {
      return item.storePath.indexOf(".mp4") > -1;
    },
    beforeAvatarUpload(file) {
      console.log("beforeAvatarUpload");

      //校验文件类型
      let validFileType = this.type == "img" ? this.validImgType(file) : true;
      //校验文件大小
      let validFileSize = this.validFileSize(file);

      this.uploadParam.fileList = file;

      return validFileType && validFileSize;
    },
    //图片类型校验
    validImgType(file) {
      let testmsg = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
      const extension = testmsg === "jpg";
      const extension2 = testmsg === "jpeg";
      const extension3 = testmsg === "png";
      // const extension4 = testmsg === "gif";
      //.jpg,.jpeg,.png,.gif
      if (!extension && !extension2 && !extension3) {
        this.$message.error("只能上传 jpg,jpeg,png 图片");
      }

      return extension || extension2 || extension3;
    },
    //文件大小校验
    validFileSize(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileMaxSize;
      if (!isLt2M) {
        this.$message.error(
          `上传的${this.type == "img" ? "图片" : "文件"}大小不能超过${
            this.fileMaxSize
          }MB!`
        );
      }
      return isLt2M;
    },
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      if (response.code == 10000) {
        this.upload.importOpen = false;
        console.log("上传成功");

        console.log(this.fileList);
        console.log(response);
        if (this.fileList == undefined) {
          this.fileList = [];
        }
        this.fileList.push(...response?.data);

        this.$emit("uploadResult", true, "上传成功", this.fileList);
      } else {
        console.log("上传失败");
        this.$message.error("上传失败");
        this.$emit("uploadResult", false, response.msg, this.fileList);
      }
      this.$refs["upload"].clearFiles(); //上传成功之后清除历史记录,否则无法二次上传
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handleChange(file, fileList) {},
    clickImg(index) {
      // console.log("clickImg:" + index);
      // //点击预览图片
      // this.showImgPreview = true;
      // this.previewImgUrl = this.fileList[index].storePath;
      // this.$refs.picPreview.open(index, this.fileList);
    },
    handleFileRemove(index) {
      if (index < this.fileList.length) {
        this.fileList.splice(index, 1);
        this.$emit("uploadResult", true, "删除成功", this.fileList, index);
      }
    },
    handleLimit() {
      this.$message.error(`最多上传${this.limit}个文档`);
    },
    //关闭图片预览弹框
    closePreviewDialog() {
      this.$emit("update:visible", false);
    },
    // 监听视频播放结束事件
    onPlayerEnded() {
      console.log("视频结束啦------");
      this.videoShow = !this.videoShow;
    },
    initVideo() {
      //此处初始化的调用，我放在了获取数据之后的方法内，而不是放在钩子函数mounted
      //页面dom元素渲染完毕，执行回调里面的方法
      this.$nextTick(() => {
        this.myPlayer = this.$video(document.getElementById("myVideo"), {
          //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
          controls: true,
          //自动播放属性,muted:静音播放
          autoplay: false,
          //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
          preload: "auto",
          //设置视频播放器的显示宽度（以像素为单位）
          width: "100px",
          //设置视频播放器的显示高度（以像素为单位）
          height: "100px",
          controlBar: {
            playToggle: true,
          },
        });
      });
    },
    openDialog(item) {
      this.myPlayer.pause();
      this.showVideoDialog = true;
      this.videoUrl = item.storePath;
    },
  },
  watch: {
    fileList: {
      handler(newVal) {
        if (this.fileList.length > 0) {
          this.initVideo();
        }
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  //position: relative;
  overflow: hidden;
  width: 100px;
  font-size: 28px;
  height: 100px;
  line-height: 100px;
  margin-right: 10px;
}

.file-item {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  margin-right: 10px;
  display: inline-table;
  position: relative;
  vertical-align: bottom;
}

.avatar {
  z-index: 1;
  width: 100px;
  height: 100px;
  border-radius: 6px;
}

.delete-file {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  top: 0;
  right: 0px;
  z-index: 2;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 6px;
}

.el-dialog__header {
  background: transparent;
}
/deep/ .el-upload-dragger {
  overflow: auto;
}

/deep/ .video-js .vjs-big-play-button {
  font-size: 2em !important;
  line-height: 1.5em !important;
  height: 34px !important;
  width: 38px !important;
  display: block;
  position: absolute !important;
  top: 50% !important;
  left: 60% !important;
  padding: 0;
  margin-top: -0.81666em !important;
  margin-left: -1.5em !important;
  cursor: pointer;
  opacity: 1;
  border: 0.06666em solid #fff;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: 0.3em !important;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  vertical-align: bottom !important;
}
/deep/ .video-js .vjs-control {
  position: relative;
  text-align: center;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 3em;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
}
</style>
