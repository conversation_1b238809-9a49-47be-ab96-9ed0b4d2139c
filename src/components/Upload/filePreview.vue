//文件预览-pdf直接在页面预览，其余类型展示链接
<template>
  <div>
    <div v-if="fileList && fileList.length > 0">
      <div
        v-for="(item, index) in fileList"
        :key="index"
        style="margin-bottom: 10px;"
      >
        <iframe
          :src="item.storePath"
          width="100%"
          style="height:70vh"
          v-if="item.docName.split('.')[1] === 'pdf'"
        />
        <el-link :href="item.storePath" target="_blank" v-else>{{
          item.docName
        }}</el-link>
      </div>
    </div>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
export default {
  props: {
    fileList: { type: Array, default: () => [] },
  },
  data() {
    return {};
  },
};
</script>

<style></style>
