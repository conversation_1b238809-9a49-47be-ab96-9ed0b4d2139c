// 文件上传组件 v-model绑定
<template>
  <el-upload
    ref="upload"
    :accept="accept"
    :headers="upload.headers"
    :action="upload.uploadPath"
    :disabled="upload.isUploading"
    :data="uploadParam"
    :on-success="handleFileSuccess"
    :on-remove="handleRemove"
    :before-upload="beforeAvatarUpload"
    :file-list="showFileList"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-button>选择文件</el-button>

    <div slot="tip" class="el-upload__tip">
      <slot name="textTip">
        支持格式：{{ accept }}，单个文件不能超过{{ maxSize }}MB。
      </slot>
    </div>
  </el-upload>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    accept: {
      type: String,
      default: ".jpg, .jpeg, .png, .gif, .JPG, .JPEG, .PNG, .GIF",
    },
    maxSize: {
      type: Number,
      default: 30,
    },
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      fileList: [],
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        uploadPath: baseUrl + "/doc/upload",
      },
      uploadParam: {},
    };
  },
  computed: {
    showFileList() {
      return this.fileList?.map((item) => {
        return { ...item, name: item.docName, url: item.storePath };
      });
    },
  },
  watch: {
    value: {
      handler(val) {
        this.fileList = val;
      },
    },
    fileList: {
      handler(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    handleFileSuccess(response, file, fileList) {
      if (fileList.every((item) => item.status == "success")) {
        fileList.map((item) => {
          if (item.response) {
            if (item.response.code == 10000) {
              this.fileList.push(...item.response?.data);
            } else {
              console.log("上传失败");
              this.$message.error("上传失败");
            }
          }
        });
      }
    },
    beforeAvatarUpload(file) {
      console.log("beforeAvatarUpload", file);
      const isLt2M = file.size / 1024 / 1024 < this.maxSize;
      if (!isLt2M) {
        this.$message.error(`上传文件大小不能超过 ${this.maxSize}MB!`);
        return false;
      }
      let testmsg = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
      const isAcceptType = this.accept.toLowerCase().includes(testmsg);
      if (!isAcceptType) {
        this.$message.error("请上传支持格式的文件！");
        return false;
      }
      this.uploadParam.fileList = file;
      // this.remarkForm.fileList.push(file);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileList = this.fileList?.filter((x) => x.docId !== file.docId);
    },
  },
};
</script>

<style></style>
