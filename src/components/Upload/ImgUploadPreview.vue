<template>
  <div>
    <div>
      <div v-for="(item, index) in fileList" :key="index" class="file-item">
        <el-image
          :src="item.storePath"
          alt="加载失败"
          class="avatar"
          :preview-src-list="fileList.map((x) => x.storePath)"
        />
      </div>
    </div>

    <!--图片预览-->
    <el-dialog
      title="图片预览"
      width="30%"
      :visible.sync="showImgPreview"
      @close="showImgPreview = false"
      append-to-body
    >
      <div style="display: flex;justify-content: center;padding: 20px">
        <el-image
          :src="previewImgUrl"
          fit="contain"
          alt="加载失败"
          style="width: 50vh; height: 50vh; "
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "fileUploadPreview",

  data() {
    return {
      fileList: [],
      uploadParam: {},
      showImgPreview: false, //显示图片预览弹框
      showVideoDialog: false,
      previewImgUrl: "", //预览图片地址
    };
  },
  mounted() {},
  methods: {
    setFileList(files) {
      this.fileList = files;
    },
    clickImg(index) {
      console.log("clickImg:" + index);
      //点击预览图片
      this.showImgPreview = true;
      this.previewImgUrl = this.fileList[index].storePath;
    },
    //关闭图片预览弹框
    closePreviewDialog() {
      this.$emit("update:visible", false);
    },
    openDialog(item) {
      this.myPlayer.pause();
      this.showVideoDialog = true;
      this.videoUrl = item.storePath;
    },
  },
  watch: {
    fileList: {
      handler(newVal) {},
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  //position: relative;
  overflow: hidden;
  width: 100px;
  font-size: 28px;
  height: 100px;
  line-height: 100px;
  margin-right: 10px;
}

.file-item {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  margin-right: 10px;
  display: inline-table;
  position: relative;
  vertical-align: bottom;
}

.avatar {
  z-index: 1;
  width: 100px;
  height: 100px;
  border-radius: 6px;
}

.delete-file {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  top: 0;
  right: 0px;
  z-index: 2;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 6px;
}

.el-dialog__header {
  background: transparent;
}
/deep/ .el-upload-dragger {
  overflow: auto;
}

/deep/ .video-js .vjs-big-play-button {
  font-size: 2em !important;
  line-height: 1.5em !important;
  height: 34px !important;
  width: 38px !important;
  display: block;
  position: absolute !important;
  top: 50% !important;
  left: 60% !important;
  padding: 0;
  margin-top: -0.81666em !important;
  margin-left: -1.5em !important;
  cursor: pointer;
  opacity: 1;
  border: 0.06666em solid #fff;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: 0.3em !important;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  vertical-align: bottom !important;
}
/deep/ .video-js .vjs-control {
  position: relative;
  text-align: center;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 3em;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
}
</style>
