//文件+图片均可上传，增加粘贴图片功能
<template>
  <div :class="isVertical ? '' : 'upload-content'">
    <div>
      <div v-if="showPaste">
        <el-input
          @paste.native="handlerPaste"
          placeholder="点击粘贴图片"
          class="upload-input"
          :disabled="uploadDisabled"
        ></el-input>
      </div>

      <el-upload
        ref="upload"
        :class="showDrag ? 'avatar-uploader' : ''"
        :accept="accept"
        :action="upload.uploadPath"
        :headers="upload.headers"
        :data="uploadParam"
        :show-file-list="false"
        :on-success="handleFileSuccess"
        :on-exceed="handleExceed"
        :before-upload="beforeAvatarUpload"
        :multiple="true"
        :limit="limit"
        style="display: inline-block; margin-right: 40px;"
        :disabled="uploadDisabled"
        :drag="showDrag"
      >
        <div v-if="showDrag">
          <div style="display:flex;align-items:center;justify-content:center;">
            <div class="el-upload__text">
              将文件拖到此处，或<el-button
                type="text"
                :disabled="uploadDisabled"
                size="medium"
                >点击上传</el-button
              >
            </div>
          </div>
        </div>
        <div v-else class="mb10">
          <el-button :disabled="uploadDisabled" type="primary"
            >点击上传</el-button
          >
        </div>
        <div class="el-upload__tip" slot="tip" v-if="textTip">
          {{ textTip }}
        </div>
      </el-upload>
    </div>
    <div class="file-content" v-if="fileList && fileList.length">
      <FileIcons
        :list="fileList"
        :fileOptions="{ name: 'docName', url: 'storePath' }"
        :iconWidth="iconWidth"
        @removeItem="handleFileRemove"
        :showRemove="true"
        :isCenter="false"
      ></FileIcons>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import FileIcons from "@/components/FileIcons/index.vue";
import axios from "axios";

export default {
  name: "fileUpload",
  components: { FileIcons },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    accept: {
      type: String,
      default: ".jpg, .jpeg, .png, .gif, .JPG, .JPEG, .PNG, .GIF",
    },
    //限制可以上传的文件数量
    limit: {
      type: Number,
      default: 1,
    },
    //文件最大值
    fileMaxSize: {
      type: Number,
      default: 20, //默认20M
    },
    //是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    textTip: {
      type: String,
      default: "",
    },
    showPaste: {
      type: Boolean,
      default: true,
    },
    showDrag: {
      type: Boolean,
      default: true,
    },
    //上传按钮和已上传文件展示竖直排列
    isVertical: {
      type: Boolean,
      default: false,
    },
    iconWidth: {
      type: Number,
      default: 100,
    },
  },

  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      timeStep: 0,
      fileList: this.value,
      upload: {
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        uploadPath: baseUrl + "/doc/upload",
      },
      uploadParam: {},
    };
  },
  mounted() {},
  methods: {
    customUpload(file, type) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("fileList", file);
      //需要添加你的上传url和action保持一致
      return axios
        .post(this.upload.uploadPath, formData, {
          headers: {
            ...this.upload.headers,
            Accept: "*/*",
          },
          //监听请求的上传进度，xml身上的方法
          onUploadProgress: (progressEvent) => {
            //progressEvent身上没有percent，所以我们需要手动加一个
            progressEvent.percent = Math.round(
              (progressEvent.loaded / progressEvent.total) * 100
            );
            this.$refs.upload.handleProgress(progressEvent, file);
          },
        })
        .then((response) => {
          if (response.status == 200) {
            //调用上传成功的钩子
            this.$refs.upload.handleSuccess(response.data, file);
          }
        })
        .catch((error) => {
          throw error;
        });
    },

    handlerPaste(e) {
      if (!this.uploadDisabled) {
        // 处理用户频繁粘贴操作
        let now = new Date().getTime();
        if (now - this.timeStep < 500) {
          this.$message.warning("粘贴速度太频繁！请稍后再试");
          return;
        }
        this.timeStep = now;
        var clipboardData = e.clipboardData; // IE
        if (!clipboardData) {
          //chrome
          clipboardData = e.originalEvent.clipboardData;
        }
        var items = "";
        items = (e.clipboardData || window.clipboardData).items;
        let file = null;
        if (!items || items.length === 0) {
          this.$message.error(
            "当前浏览器不支持粘贴本地图片，请打开图片复制后再粘贴！"
          );
          return;
        }
        // 搜索剪切板items
        for (let i = 0; i < items.length; i++) {
          // 限制上传文件类型
          if (items[i].type.indexOf("image") !== -1) {
            file = items[i].getAsFile();
            break;
          }
        }
        if (file) {
          console.log(file, "file");
          // 生成预览图
          this.$refs.upload.handleStart(file);
          //自己封装的上传图片方法
          this.customUpload(file);
        }
      }
    },

    beforeAvatarUpload(file) {
      //校验文件类型
      let validFileType = this.validFileType(file);
      //校验文件大小
      let validFileSize = this.validFileSize(file);

      this.uploadParam.fileList = file;

      return validFileType && validFileSize;
    },
    //文件格式校验
    validFileType(file) {
      let testmsg = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
      const isAcceptType = this.accept.toLowerCase().includes(testmsg);
      if (!isAcceptType) {
        this.$message.error("请上传支持格式的文件！");
      }
      return isAcceptType;
    },
    //文件大小校验
    validFileSize(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileMaxSize;
      if (!isLt2M) {
        this.$message.error(`上传的文件大小不能超过${this.fileMaxSize}MB!`);
      }
      return isLt2M;
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，超出数量限制`
      );
    },
    handleFileSuccess(response, file, fileList) {
      console.log("进入上传成功", response, file, fileList);
      this.upload.open = false;
      console.log(fileList);
      if (this.fileList == undefined) {
        this.fileList = [];
      }
      if (fileList.every((item) => item.status == "success")) {
        console.log("every", fileList);
        fileList.map((item) => {
          /** 这时只需要push进带有response的数据就好 */
          if (item.response) {
            if (item.response.code == 10000) {
              this.fileList.push(...item.response?.data);
            } else {
              this.$message.error("上传失败");
            }
          }
        });
        console.log(this.fileList, "上传成功");
        this.$refs["upload"].clearFiles(); //上传成功之后清除历史记录,否则无法二次上传
      }
    },
    handleFileRemove(index) {
      if (index < this.fileList.length) {
        this.fileList.splice(index, 1);
      }
    },
  },
  watch: {
    value(newValue) {
      this.fileList = newValue;
    },
    fileList(newInternalValue) {
      this.$emit("input", newInternalValue);
    },
  },
  computed: {
    uploadDisabled() {
      return this.disabled || this.fileList?.length >= this.limit;
    },
  },
};
</script>

<style lang="less" scoped>
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  //position: relative;
  overflow: hidden;
  width: 100px;
  font-size: 28px;
  height: 100px;
  line-height: 100px;
  margin-right: 10px;
}
.file-content {
  // margin-left: 40px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
}
.file-item {
  border-radius: 6px;
  margin-right: 10px;
  display: inline-table;
  vertical-align: bottom;
  position: relative;
}

.avatar {
  position: relative;
  z-index: 1;
  width: 100px;
  height: 100px;
  border-radius: 6px;
}

.delete-file {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  top: 0px;
  right: 0px;
  z-index: 2;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 6px;
  text-align: center;
  cursor: pointer;
}

.el-dialog__header {
  background: transparent;
}
/deep/ .el-upload-dragger {
  overflow: auto;
}

/deep/ .video-js .vjs-big-play-button {
  font-size: 2em !important;
  line-height: 1.5em !important;
  height: 34px !important;
  width: 38px !important;
  display: block;
  position: absolute !important;
  top: 50% !important;
  left: 60% !important;
  padding: 0;
  margin-top: -0.81666em !important;
  margin-left: -1.5em !important;
  cursor: pointer;
  opacity: 1;
  border: 0.06666em solid #fff;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: 0.3em !important;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
  vertical-align: bottom !important;
}
/deep/ .video-js .vjs-control {
  position: relative;
  text-align: center;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 3em;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
}
/deep/ .el-upload-dragger {
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-content {
  display: flex;
}
.el-upload__tip {
  width: 360px;
}
.upload-input {
  width: 200px;
  margin-bottom: 10px;
  /deep/ .el-input__inner {
    border-style: dashed;
  }
}
</style>
