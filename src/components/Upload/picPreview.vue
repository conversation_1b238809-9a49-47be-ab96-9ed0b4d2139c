<template>
  <el-dialog
    title="图片预览"
    width="50%"
    :visible.sync="showImgPreview"
    @close="showImgPreview = false"
    append-to-body
    :before-close="handleBeforeClose"
  >
    <el-carousel
      :initial-index="initialIndex"
      :autoplay="false"
      trigger="click"
      v-if="showImgPreview"
      height="70vh"
      :arrow="fileList && fileList.length > 1 ? 'hover' : 'never'"
      @change="resetPreview"
    >
      <el-carousel-item v-for="(item, index) in fileList" :key="index">
        <el-image
          :src="item.storePath || item.url || item"
          fit="contain"
          alt="加载失败"
          @mousewheel.prevent="handleImageWheel"
          :style="imageStyle"
          v-if="getFileType(item.storePath || item.url || item) === 'img'"
        />
        <div
          v-else-if="
            getFileType(item.storePath || item.url || item) === 'video'
          "
          style="height:100%;justify-content: center;display: flex"
        >
          <video :src="item.storePath || item.url || item" controls></video>
        </div>
      </el-carousel-item>
    </el-carousel>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      showImgPreview: false, //显示图片预览弹框
      fileList: [],
      initialIndex: 0,
      zoomLevel: 1,
      imageStyle: { width: "100%", height: "100%" },
    };
  },
  methods: {
    handleBeforeClose(done) {
      this.resetPreview();
      done();
    },
    resetPreview() {
      this.imageStyle = { width: "100%", height: "100%" };
      this.zoomLevel = 1;
    },
    handleImageWheel(event) {
      event.preventDefault();
      const rect = event.target.getBoundingClientRect();
      const offsetX = event.clientX - rect.left; // 鼠标位置X
      const offsetY = event.clientY - rect.top; // 鼠标位置Y

      const delta = Math.max(
        -1,
        Math.min(1, event.deltaY || -event.wheelDelta || event.detail)
      );
      const zoomStep = 0.1; // 将缩放级别调整为相对缩放以提供更细致的控制

      const MIN_ZOOM_LEVEL = 1; // 最小缩放级别，例如10%
      const MAX_ZOOM_LEVEL = 10; // 最大缩放级别，例如1000%

      if (delta > 0) {
        if (this.zoomLevel < MAX_ZOOM_LEVEL) {
          this.zoomLevel *= 1 + zoomStep;
        }
      } else {
        if (this.zoomLevel > MIN_ZOOM_LEVEL) {
          this.zoomLevel /= 1 + zoomStep;
        }
      }

      // 计算新的缩放中心点
      const newOriginX = (offsetX / rect.width) * 100;
      const newOriginY = (offsetY / rect.height) * 100;

      // 更新图片样式
      this.imageStyle = {
        ...this.imageStyle,
        transform: `scale(${this.zoomLevel})`,
        transformOrigin: `${newOriginX}% ${newOriginY}%`,
      };
    },

    open(index, list) {
      this.showImgPreview = true;
      this.fileList = list;
      this.initialIndex = index;
    },
    //判断是图片还是视频
    getFileType(url) {
      if (/\b(?:jpg|jpeg|png)\b/i.test(url.toLowerCase())) {
        return "img";
      } else if (/\b(?:mp4|3gp|mov)\b/i.test(url.toLowerCase())) {
        return "video";
      }
    },
  },
};
</script>

<style></style>
