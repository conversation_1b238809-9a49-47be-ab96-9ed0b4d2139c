<template>
  <div>
    <el-row
      v-for="(item, index) in stations"
      :key="index"
      class="station-group-item"
    >
      <el-col :span="20">
        <el-form-item
          label="站点名称："
          :prop="'stations.' + index + '.stationName'"
          :rules="{
            required: needValidate,
            message: '请选择站点名称',
            trigger: 'change',
          }"
        >
          <el-select
            v-model="item.stationName"
            placeholder="请选择或搜索站点名称"
            filterable
            remote
            style="width: 100%"
            clearable
            @change="handleStationChange(index)"
            v-el-select-loadmore="getStationList"
            :remote-method="remoteStationMethod"
          >
            <el-option
              v-for="(item, index) in stationOptions"
              :key="index"
              :label="item.stationName"
              :value="item.stationName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="踏勘编号："
          :prop="'stations.' + index + '.businessId'"
          :rules="{
            required: needValidate,
            trigger: 'change',
            validator: (rule, value, callback) => {
              validateSurveyCode(rule, value, callback, index);
            },
          }"
        >
          <!-- <el-input
            v-model="item.businessId"
            placeholder="请输入踏勘编号"
            clearable
          /> -->
          <el-select
            v-model="item.businessId"
            placeholder="请选择或模糊搜索"
            filterable
            remote
            style="width: 100%"
            clearable
            @change="handleBusinessChange(index)"
            v-el-select-loadmore="getBusinessList"
            :remote-method="remoteBusinessMethod"
          >
            <el-option
              v-for="(item, index) in businessOptions"
              :key="index"
              :label="item.businessId"
              :value="item.businessId"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3" :offset="1">
        <el-button
          type="primary"
          circle
          icon="el-icon-minus"
          style="background:red;border:1px solid red;margin-bottom: 18px;"
          @click="removeItem(index)"
          v-if="index > 0"
        />
      </el-col>
    </el-row>
    <el-button
      type="primary"
      circle
      icon="el-icon-plus"
      @click="addItem"
      style="margin-bottom: 18px;"
    />
  </div>
</template>

<script>
import { queryStationInfo } from "@/api/demandPool/index.js";
export default {
  name: "DynamicStationGroup",
  props: {
    value: {
      type: Array,
      default: () => [
        {
          stationName: "",
          businessId: "",
        },
      ],
    },
    needValidate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      stations: this.value,
      stationOptions: [],
      currentStationPage: 1,
      stationTotal: 0,
      isStationLoading: false,
      stationText: "",
      businessOptions: [],
      currentBusinessPage: 1,
      businessTotal: 0,
      isBusinessLoading: false,
      businessText: "",
    };
  },
  watch: {
    value: {
      handler(newValue) {
        this.stations = newValue;
      },
      deep: true,
      immediate: true,
    },
    stations: {
      handler(newInternalValue) {
        console.log(this.stations, "----");
        this.$emit("input", newInternalValue);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.remoteStationMethod("");
    this.remoteBusinessMethod("");
  },
  methods: {
    validateSurveyCode(rule, value, callback, index) {
      if (this.needValidate && !value) {
        const stationName =
          this.stations[index]?.stationName?.stationName ||
          this.stations[index]?.stationName;
        callback(new Error(`${stationName}踏勘编号为空，请修改后提交`));
      } else {
        callback();
      }
    },
    addItem() {
      this.stations.push({
        stationName: "",
        businessId: "",
      });
    },
    removeItem(index) {
      this.stations.splice(index, 1);
    },
    async getStationList() {
      const page =
        this.stationTotal == 0 ? 1 : Math.ceil(this.stationTotal / 10);
      if (this.isStationLoading || this.currentStationPage > page) {
        return;
      }
      this.isStationLoading = true;
      const params = {
        pageNum: this.currentStationPage,
        pageSize: 10,
        stationName: this.stationText,
      };
      const res = await queryStationInfo(params);
      this.stationTotal = res?.total;
      const newOptions = res?.data?.map((x) => {
        return { ...x };
      });
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentStationPage++;
      }
      this.isStationLoading = false;
    },
    async getBusinessList() {
      const page =
        this.businessTotal == 0 ? 1 : Math.ceil(this.businessTotal / 10);
      if (this.isBusinessLoading || this.currentBusinessPage > page) {
        return;
      }
      this.isBusinessLoading = true;
      const params = {
        pageNum: this.currentBusinessPage,
        pageSize: 10,
        businessId: this.businessText,
      };
      const res = await queryStationInfo(params);
      this.businessTotal = res?.total;
      const newOptions = [];
      res?.data?.map((x) => {
        x.businessIdList?.map((y) => {
          newOptions.push({ businessId: y, stationName: x.stationName });
        });
      });
      if (newOptions.length > 0) {
        this.businessOptions = this.businessOptions.concat(newOptions);
        this.currentBusinessPage++;
      }
      this.isBusinessLoading = false;
    },
    async remoteStationMethod(val) {
      this.stationOptions = [];
      this.currentStationPage = 1;
      this.stationText = val;
      await this.getStationList();
    },
    async remoteBusinessMethod(val) {
      this.businessOptions = [];
      this.currentBusinessPage = 1;
      this.businessText = val;
      await this.getBusinessList();
    },
    handleStationChange(index) {
      const station = this.stationOptions.find(
        (x) => x.stationName === this.stations[index].stationName
      );
      if (station) {
        this.$set(
          this.stations[index],
          "businessId",
          station.businessIdList?.[0] || ""
        );
        // this.remoteBusinessMethod(station.businessIdList?.[0] || "");
      }
    },
    handleBusinessChange(index) {
      const business = this.businessOptions.find(
        (x) => x.businessId === this.stations[index].businessId
      );
      if (business) {
        this.$set(
          this.stations[index],
          "stationName",
          business.stationName || ""
        );
      }
    },
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 下拉框下拉的框
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        // 增加滚动监听，
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          // console.log(this.scrollHeight, this.scrollTop, this.clientHeight);
          const condition =
            this.scrollHeight - this.scrollTop - 2 <= this.clientHeight;
          // 当滚动条滚动到最底下的时候执行接口加载下一页
          if (condition) {
            // console.log("进入判断");
            binding.value();
          }
        });
      },
    },
  },
};
</script>

<style lang="less" scoped>
.station-group-item {
  border: 1px dashed #029c7c;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
