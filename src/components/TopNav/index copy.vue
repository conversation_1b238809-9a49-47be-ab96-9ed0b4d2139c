<template>
  <el-menu
    :default-active="activeMenu"
    mode="horizontal"
    @select="handleSelect"
    router
  >
    <template v-for="(item, index) in topMenus">
      <el-submenu :index="item.path" v-if="index < visibleNumber">
        <template slot="title">
          <svg-icon v-if="item.meta.icon" :icon-class="item.meta.icon" />
          {{ item.meta.title }}
        </template>
        <template v-for="(child, index2) in item.children">
          <el-menu-item
            :index="child.path"
            :key="index2"
            v-if="child >= visibleNumber"
            ><svg-icon v-if="false" :icon-class="child.meta.icon" />
            {{ child.meta.title }}</el-menu-item
          >
        </template>
      </el-submenu>
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <el-submenu index="more" v-if="topMenus.length > visibleNumber">
      <template slot="title">更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <el-menu-item
          :index="item.path"
          :key="index"
          v-if="index >= visibleNumber"
          ><svg-icon v-if="false" :icon-class="item.meta.icon" />
          {{ item.meta.title }}</el-menu-item
        >
      </template>
    </el-submenu>
  </el-menu>
</template>

<script>
import { constantRoutes } from "@/router";

export default {
  data() {
    return {
      // 顶部栏初始数
      visibleNumber: 8,
      // 是否为首次加载
      isFrist: false,
      timer: null,
      elWidth: 0
    };
  },
  computed: {
    // 顶部显示菜单
    topMenus() {
      return this.routers.map((menu) => ({
        ...menu,
        // children: undefined,
      }));
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            router.children[item].path = router.path + "/" + router.children[item].path;
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path;
      let activePath = this.routers[0].path;
      // if (path.lastIndexOf("/") > 0) {
      //   const tmpPath = path.substring(1, path.length);
      //   console.log('[ tmpPath ] >', tmpPath)
      //   activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
      //   console.log('[ activePath ] >', activePath)
      // } else if ("/index" == path || "" == path) {
      //   if (!this.isFrist) {
      //     this.isFrist = true;
      //     activePath = "index";   //首次加载 去掉默认选中顶部第一个菜单，并展示首页
      //   } else {
      //     activePath = "index";
      //   }
      // }
      this.activeRoutes(activePath);
      return activePath;
    },
    spanWidth() {
      return this.$store.state.app.sidebar.opened
    }
  },
  watch: {
    spanWidth: {
      handler() {
        this.resizeHandler()
      }
    }
  },
  mounted() {
    setTimeout(() => {
      console.log('[ this.topMenus ] >', this.topMenus)
    }, 5000);
    this.setVisibleNumber();
    window.onresize = () => {
        this.resizeHandler();
    }
  },
  beforeDestroy() {
    window.onresize = null;
  },
  methods: {
    resizeHandler() {
      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.setVisibleNumber();
      }, 500);
    },
    // 根据宽度计算设置显示栏数
    setVisibleNumber() {
      let reduceWidth = 0
      if (this.$store.state.app.sidebar.opened) {
        //左侧菜单打开
        reduceWidth = 400
      } else {
        //左侧菜单关闭
        reduceWidth = 280
      }
      const width = document.body.getBoundingClientRect().width - reduceWidth;
      // const elWidth = this.$el.getBoundingClientRect().width;

      // elWidth菜单宽度不再重新计算dom宽度-------------------------
      if (this.elWidth == 0) {
        this.elWidth = this.$el.getBoundingClientRect().width;
      }
      // -------------------------------------------------------
      const menuItemNodes = this.$el.children;
      const menuWidth = Array.from(menuItemNodes).map(
        (i) => i.getBoundingClientRect().width
      );
      this.visibleNumber = (
        parseInt(width - this.elWidth) / parseInt(menuWidth)
      ).toFixed(0);

      if (this.visibleNumber <= 1) {
        this.visibleNumber = "5"
      }
    },
    // 菜单选择事件
    handleSelect(key, keyPath) {
      if (key.indexOf("http://") !== -1 || key.indexOf("https://") !== -1) {
        // http(s):// 路径新窗口打开
        window.open(key, "_blank");
      } else {
        this.activeRoutes(key);
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
    },
  },
};
</script>

<style lang="less" scoped>
.el-menu--horizontal > .el-menu-item {
  float: left;
  height: 50px;
  line-height: 50px;
  margin: 0;
  border-bottom: 3px solid transparent;
  color: #999093;
  padding: 0 5px;
  margin: 0 10px;
}

.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: 3px solid #409eff;
  color: #303133;
}
</style>
