<template>
  <el-menu
    :default-active="activeMenu"
    class="el-menu-demo"
    mode="horizontal"
    @select="handleSelect"
    text-color="#fff"
    active-text-color="#06FFCA"
    background-color="#095755"
  >
    <!-- 一级菜单 -->
    <template v-for="(item,index) in topMenus" v-if="index < visibleNumber">
      <el-submenu
        v-if="item.children && item.children.length"
        :index="item.path"
        :key="item.path"
      >
        <template slot="title"
          >
          <svg-icon v-if="item.meta.icon" :icon-class="item.meta.icon" />
          <span>{{ item.meta.title }}</span>
          </template
        >
        <!-- 二级菜单 -->
        <template v-for="itemChild in item.children">
          <el-submenu
            v-if="itemChild.children && itemChild.children.length"
            :index="itemChild.path"
            :key="itemChild.path"
          >
            <template slot="title"
              ><span>{{ itemChild.meta.title }}</span></template
            >

            <!-- 三级菜单 -->
            <el-menu-item
              v-for="itemChild_Child in itemChild.children"
              :index="itemChild_Child.path"
              :key="itemChild_Child.path"
            >
              <span slot="title">{{ itemChild_Child.meta.title }}</span>
            </el-menu-item>
          </el-submenu>

          <el-menu-item v-else :index="itemChild.path" :key="itemChild.path"
            ><span slot="title">{{ itemChild.meta.title }}</span></el-menu-item
          >
        </template>
      </el-submenu>

      <el-menu-item v-else :index="item.path" :key="item.path"
        ><span slot="title">{{ item.meta.title }}</span></el-menu-item
      >
    </template>

    <!-- <template > -->
      <el-submenu index="more" v-if="topMenus.length > visibleNumber">
        <template slot="title">更多菜单</template>
        <template v-for="(item,index) in topMenus" v-if="index >= visibleNumber">
          <el-submenu
            v-if="item.children && item.children.length"
            :index="item.path"
            :key="item.path"
          >
            <template slot="title"
              ><span>{{ item.meta.title }}</span></template
            >

            <!-- 二级菜单 -->
            <template v-for="itemChild in item.children">
              <el-submenu
                v-if="itemChild.children && itemChild.children.length"
                :index="itemChild.path"
                :key="itemChild.path"
              >
                <template slot="title">{{ itemChild.meta.title }}</template>
                <!-- 三级菜单 -->
                <el-menu-item
                  v-for="itemChild_Child in itemChild.children"
                  :index="itemChild_Child.path"
                  :key="itemChild_Child.path"
                >
                  <span slot="title">{{ itemChild_Child.meta.title }}</span>
                </el-menu-item>
              </el-submenu>

              <el-menu-item v-else :index="itemChild.path" :key="itemChild.path"
                ><span slot="title">{{ itemChild.meta.title }}</span></el-menu-item
              >

            </template>
          </el-submenu>

          <!-- <el-menu-item v-else :index="item.path" :key="item.path"
            ><span slot="title">{{ item.meta.title }}</span></el-menu-item
          > -->
        </template>
      </el-submenu>
    <!-- </template> -->
  </el-menu>
</template>

<script>
import { constantRoutes } from "@/router";

export default {
  data() {
    return {
      // 顶部栏初始数
      visibleNumber: 8,
      // 是否为首次加载
      isFrist: false,
      timer: null,
      elWidth: 0,
    };
  },
  computed: {
    // 顶部显示菜单
    topMenus() {
      return this.routers.map((menu) => ({
        ...menu,
        // children: undefined,
      }));
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            router.children[item].path =
              router.path + "/" + router.children[item].path;
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
    // 默认激活的菜单
    activeMenu() {
      const path = this.$route.path;
      let activePath = this.routers[0].path;
      // if (path.lastIndexOf("/") > 0) {
      //   const tmpPath = path.substring(1, path.length);
      //   console.log('[ tmpPath ] >', tmpPath)
      //   activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
      //   console.log('[ activePath ] >', activePath)
      // } else if ("/index" == path || "" == path) {
      //   if (!this.isFrist) {
      //     this.isFrist = true;
      //     activePath = "index";   //首次加载 去掉默认选中顶部第一个菜单，并展示首页
      //   } else {
      //     activePath = "index";
      //   }
      // }
      this.activeRoutes(activePath);
      return activePath;
    },
    spanWidth() {
      return this.$store.state.app.sidebar.opened;
    },
  },
  watch: {
    spanWidth: {
      handler() {
        this.resizeHandler();
      },
    },
  },
  mounted() {
    this.setVisibleNumber();
    window.onresize = () => {
      this.resizeHandler();
    };
  },
  beforeDestroy() {
    window.onresize = null;
  },
  methods: {
    resizeHandler() {
      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.setVisibleNumber();
      }, 500);
    },
    // 根据宽度计算设置显示栏数
    setVisibleNumber() {
      let reduceWidth = 0;
      if (this.$store.state.app.sidebar.opened) {
        //左侧菜单打开
        reduceWidth = 400;
      } else {
        //左侧菜单关闭
        reduceWidth = 280;
      }
      const width = document.body.getBoundingClientRect().width;
      // const elWidth = this.$el.getBoundingClientRect().width;

      // elWidth菜单宽度不再重新计算dom宽度-------------------------
      if (this.elWidth == 0) {
        this.elWidth = this.$el.getBoundingClientRect().width;
      }
      // -------------------------------------------------------
      const menuItemNodes = this.$el.children;
      const menuWidth = Array.from(menuItemNodes).map(
        (i) => i.getBoundingClientRect().width
      );
      this.visibleNumber = (
        parseInt(width - this.elWidth) / parseInt(menuWidth)
      ).toFixed(0);

      if (this.visibleNumber <= 1) {
        this.visibleNumber = "5";
      }
    },
    // 菜单选择事件
    handleSelect(key, keyPath) {
      if (key.indexOf("http://") !== -1 || key.indexOf("https://") !== -1) {
        // http(s):// 路径新窗口打开
        window.open(key, "_blank");
      } else {
        console.log('[ key ] >', key)
        console.log('[ keyPath ] >', keyPath)
        // if (keyPath.length < 4) {
        //   this.$router.push({path: key})
        // } else {
        //   let newPath = keyPath[keyPath.length - 2] + "/" + keyPath[keyPath.length - 1]
        //   this.$router.push({path: newPath})
        // }

        // if (keyPath[0] == 'more' || keyPath.length > 2) {
        //   let newPath = keyPath[keyPath.length - 2] + "/" + keyPath[keyPath.length - 1]
        //   this.$router.push({path: newPath})
        // } else {
        //   this.$router.push({path: key})
        // }

        if (key.substring(0,1) == '/') {
          this.$router.push({path: key})
        } else {
          let newPath = keyPath[keyPath.length - 2] + "/" + keyPath[keyPath.length - 1]
          this.$router.push({path: newPath})
        }
      }
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
    },
  },
};
</script>

<style lang="less" scoped>
.el-menu--horizontal > .el-menu-item {
  float: left;
  height: 50px;
  line-height: 50px;
  margin: 0;
  border-bottom: 3px solid transparent;
  color: #999093;
  padding: 0 5px;
  margin: 0 10px;
}

.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: 3px solid #1B6653;
  color: #303133;
}



/deep/.el-menu,.el-menu-demo {
  background: #095755;
}
/deep/.el-submenu__title:hover {
  background: #095755;
}

/deep/.el-submenu__title *{
  vertical-align: inherit;
}
</style>
