<!-- table组件 -->
<template>
  <div class="">
    <vxe-toolbar ref="xToolbar" :custom="!!tableId">
      <template #buttons>
        <slot name="xToolbarBtn" :xToolbarData="xToolbarData"></slot>
      </template>
    </vxe-toolbar>
    <slot name="tab"></slot>

    <CountTips v-if="count > 0" :count="count" @clear="clearTips" />
    <vxe-table
      border
      ref="xTable"
      :maxHeight="maxHeight"
      :id="tableId"
      @checkbox-change="checkboxChange"
      @checkbox-all="checkboxAll"
      :custom-config="{ storage: true }"
      :seq-config="{ seqMethod }"
      resizable
      :loading="loading"
      :data="tableData"
      :tree-config="treeConfig"
    >
      <vxe-column
        v-if="checkbox"
        fixed="left"
        type="checkbox"
        width="60"
      ></vxe-column>
      <vxe-column
        v-if="seq"
        fixed="left"
        align="center"
        type="seq"
        title="序号"
        width="60"
      ></vxe-column>
      <div v-for="col in columns" :key="col.field">
        <vxe-column
          :field="col.field"
          :title="col.title"
          :align="col.align || 'center'"
          :width="col.width"
          :fixed="col.fixed || ''"
          :min-width="col.minWidth"
          :show-header-overflow="col.showOverflowTooltip"
          :sortable="col.sortable ? col.sortable : false"
          :resizable="true"
          :formatter="col.formatter"
          :tree-node="col.treeNode"
          type="html"
          v-if="col.type == 'html'"
        ></vxe-column>
        <vxe-column
          :title="col.title"
          :min-width="col.minWidth"
          :width="col.width"
          :tree-node="col.treeNode"
          v-else-if="col.type == 'slot'"
        >
          <template #default="{ row }">
            <slot :name="col.field" :row="row"></slot>
          </template>
        </vxe-column>
        <vxe-column
          :field="col.field"
          :title="col.title"
          :align="col.align || 'center'"
          :width="col.width"
          :fixed="col.fixed || ''"
          :min-width="col.minWidth"
          :show-header-overflow="col.showOverflowTooltip"
          :sortable="col.sortable ? col.sortable : false"
          :resizable="true"
          :formatter="col.formatter"
          :tree-node="col.treeNode"
          v-else
        ></vxe-column>
      </div>

      <vxe-column
        title="操作"
        min-width="100"
        align="center"
        fixed="right"
        v-if="hasCtrl"
      >
        <template #default="{ row }">
          <slot name="operation" :row="row"></slot>
        </template>
      </vxe-column>
    </vxe-table>

    <pagination
      v-show="total > 0 && pageNum && pageSize"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      :autoScroll="false"
      @pagination="handlePageChange"
    />
  </div>
</template>

<script>
import CountTips from "@/components/GridTable/CountTips/index.vue";

export default {
  components: { CountTips },
  props: {
    tableId: {
      default: "", //localStorage保存table显示隐藏列，全局唯一
    },
    loading: {
      type: Boolean,
      default: false,
    },
    seq: {
      default: false,
    },
    checkbox: {
      default: false,
    },
    batchDelete: {
      default: false, //批量删除功能
    },
    hasCtrl: {
      default: true, //是否显示操作栏
    },
    treeConfig: {
      type: Object,
      default: () => {},
    },
    columns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    currentPage: {
      default: 1,
    },
    pageSize: {
      default: 10,
    },
    total: {
      default: 0,
    },
    maxHeight: {
      default: "600px",
    },
    checkboxStatus:undefined
  },
  // watch:{
  //   checkbox:{
  //     handler(val){
  //       this.checkboxStatus = val
  //     },
  //     immediate:true
  //   }
  // },
  data() {
    return {
      // height: 548,
      xToolbarData: {
        aa: 111,
      },
      currentCheckRow: null, //当前勾选的行
      checkRowArr: [], //选中的行数组
      count: 0,
      pageNum: 1,
    };
  },
  computed: {},
  watch: {
    currentPage: {
      handler(newVal, oldVal) {
        this.pageNum = newVal;
      },
      immediate: true,
    },
  },
  methods: {
    //分页变化
    handlePageChange() {
      this.$emit("update:currentPage", this.pageNum);
      this.$emit("update:pageSize", this.pageSize);
      this.$emit("changePage");

      this.checkRowArr = [];
      this.count = 0;
    },
    //分页序号连续
    seqMethod({ row, rowIndex, column, columnIndex }) {
      return (this.pageNum - 1) * this.pageSize + (rowIndex + 1);
    },
    //勾选触发
    checkboxChange({
      checked,
      row,
      rowIndex,
      $rowIndex,
      column,
      columnIndex,
      $columnIndex,
      $event,
    }) {
      this.currentCheckRow = row;
      this.checkRowArr = this.$refs.xTable.getCheckboxRecords();
      console.log("[ this.checkRowArr ] >", this.checkRowArr);
      this.count = this.checkRowArr.length;
      this.batchDelete && this.$emit("handleSelectionChange", this.checkRowArr);
    },
    //全选框
    checkboxAll({ checked, $event }) {
      if (checked) {
        this.count = this.pageSize;
        this.checkRowArr = this.$refs.xTable.getCheckboxRecords();
      } else {
        this.count = 0;
        this.checkRowArr = [];
      }
      this.batchDelete && this.$emit("handleSelectionChange", this.checkRowArr);
    },
    clearTips() {
      this.count = 0;
      this.checkRowArr = [];
      this.$refs.xTable.clearCheckboxRow();
    },
  },
  created() {},
  mounted() {
    console.log('kkkk',this.checkbox)
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped></style>
