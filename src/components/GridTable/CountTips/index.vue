<!--  -->
<template>
  <div class="tips-wrap">
    <div>
      <i class="el-icon-warning" style="color:#409EFF;"></i>
      已选择
      <span class="count">{{ count }}</span>
      项
    </div>
    <div>
      <el-link @click="clear" type="primary">清空</el-link>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    count: {
      type: Number || String,
      default: 0
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {
    clear() {
      this.$emit("clear")
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped>
.tips-wrap {
  border: 1px solid #BAE7FF;
  background-color: #E6F7FF;
  border-radius: 3px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  margin-bottom: 10px;
  .count {
    color: #409EFF;
    font-size: 18px;
  }
}
</style>
