<!-- table组件 -->
<template>
  <div class="">
    <!-- <vxe-toolbar ref="xToolbar" :custom="!!tableId">
      <template #buttons>
        <slot name="xToolbarBtn" :xToolbarData="xToolbarData"></slot>
      </template>
    </vxe-toolbar> -->
    <slot name="tab"></slot>

    <CountTips v-if="selectNum > 0" :count="selectNum" @clear="clearTips" />
    <!-- :toolbar-config="tableToolbar" -->
    <vxe-grid
      :toolbar-config="tableToolbar"
      border
      align="center"
      :show-header-overflow="false"
      show-overflow="tooltip"
      ref="xTable"
      :maxHeight="maxHeight"
      :id="tableId"
      @checkbox-change="checkboxChange"
      @checkbox-all="checkboxAll"
      @custom="toolbarCustomEvent"
      :custom-config="{ storage: true }"
      resizable
      :loading="loading"
      :data="tableData"
      :columns="tableColumns"
      :tree-config="treeConfig"
      :show-footer="showFooter"
      :footer-method="footerMethod"
      :row-config="{ isCurrent: true, keyField: rowId }"
      :checkboxConfig="{
        checkRowKeys: selectRowsId,
        reserve: true,
        checkMethod,
      }"
      class="mytable-scrollbar"
      :radio-config="radioConfig"
      @radio-change="radioChangeEvent"
      v-bind="$attrs"
      v-on="$listeners"
    >
      >
      <template #radio_header>
        <vxe-button
          type="text"
          @click="clearRadioRowEevnt"
          :disabled="!selectRadioRow"
          >取消
        </vxe-button>
      </template>
      <template #toolbar_buttons>
        <slot name="xToolbarBtn"></slot>
      </template>
      <template #deviceTypeImg="{ row }">
        <slot name="deviceTypeImg" :row="row"></slot>
      </template>
      <template #deviceStatus="{ row }">
        <slot name="deviceStatus" :row="row"></slot>
      </template>
      <template #projectCode="{ row }">
        <slot name="projectCode" :row="row"></slot>
      </template>
      <template #pileCode="{ row }">
        <slot name="pileCode" :row="row"></slot>
      </template>
      <template #pileNum="{ row }">
        <slot name="pileNum" :row="row"></slot>
      </template>
      <template #operation="{ row }">
        <slot name="operation" :row="row"></slot>
      </template>
      <template #preview="{ row }">
        <slot name="preview" :row="row"></slot>
      </template>
      <template #stationTag="{ row }">
        <slot name="stationTag" :row="row"></slot>
      </template>
      <template #companyTag="{ row }">
        <slot name="companyTag" :row="row"></slot>
      </template>
      <template #ignoreRemark="{ row }">
        <slot name="ignoreRemark" :row="row"></slot>
      </template>
      <template #smsStatus="{ row }">
        <slot name="smsStatus" :row="row"></slot>
      </template>
      <template #firstCheckType="{ row }">
        <slot name="firstCheckType" :row="row"></slot>
      </template>
      <template #enabled="{ row }">
        <slot name="enabled" :row="row"></slot>
      </template>
      <template #checkInterval="{ row }">
        <slot name="checkInterval" :row="row"></slot>
      </template>
      <template #aheadDays="{ row }">
        <slot name="aheadDays" :row="row"></slot>
      </template>
      <template #radius="{ row }">
        <slot name="radius" :row="row"></slot>
      </template>
      <template #businessNo="{ row }">
        <slot name="businessNo" :row="row"></slot>
      </template>
      <template #omStatus="{ row }">
        <slot name="omStatus" :row="row"></slot>
      </template>
      <template #expireFlag="{ row }">
        <slot name="expireFlag" :row="row"></slot>
      </template>
      <template #expireDuration="{ row }">
        <slot name="expireDuration" :row="row"></slot>
      </template>
      <template #viewPicture="{ row,column }">
        <slot name="viewPicture" :row="row" :column="column"></slot>
      </template>
      <template #jump="{ row,column }">
        <slot name="jump" :row="row" :column="column"></slot>
      </template>
      <template #groupMember="{ row,column }">
        <slot name="groupMember" :row="row" :column="column"></slot>
      </template>
      <template #status="{ row,column }">
        <slot name="status" :row="row" :column="column"></slot>
      </template>
      <template #orderDesc="{ row,column }">
        <slot name="orderDesc" :row="row" :column="column"></slot>
      </template>
      <template #tags="{ row,column }">
        <slot name="tags" :row="row" :column="column"></slot>
      </template>
      <template #projectJump="{ row,column }">
        <slot name="projectJump" :row="row" :column="column"></slot>
      </template>
    </vxe-grid>

    <pagination
      v-show="total > 0 && pageNum && pageSize"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      :autoScroll="false"
      @pagination="handlePageChange"
      :small="isSmallPagination"
    />
  </div>
</template>

<script>
import CountTips from "@/components/GridTable/CountTips/index.vue";

export default {
  components: { CountTips },
  props: {
    tableId: {
      default: "", //localStorage保存table显示隐藏列，全局唯一
    },
    loading: {
      type: Boolean,
      default: false,
    },
    seq: {
      default: false,
    },
    checkbox: {
      default: false,
    },
    batchDelete: {
      default: false, //批量删除功能
    },
    hasCtrl: {
      default: true, //是否显示操作栏
    },
    treeConfig: {
      type: Object,
      default: () => {},
    },
    columns: {
      type: Array,
      default: () => [],
    },
    columnAutoFit: {
      type: Boolean,
      default: true, //列宽自适应
    },
    columnWidth: {
      type: String | Number,
      default: "",
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    //勾选功能时必传
    rowId: {
      type: String,
      default: "_X_ROW_KEY",
    },
    currentPage: {
      default: 1,
    },
    pageSize: {
      default: 10,
    },
    total: {
      default: 0,
    },
    maxHeight: {
      // default: "600px",
      default: "",
    },
    checkboxStatus: undefined,
    showFooter: {
      type: Boolean,
      default: false,
    },
    footerMethod: {
      default: () => {},
    },
    //多选框是否可用，默认可用
    checkMethod: {
      type: Function,
      default: () => true,
    },
    //小分页
    isSmallPagination: {
      type: Boolean,
      default: false,
    },
    showRadio: {
      type: Boolean,
      default: false,
    },
    radioConfig: {
      type: Object,
      default: () => {
        return { highlight: true };
      },
    },
  },
  // watch:{
  //   checkbox:{
  //     handler(val){
  //       this.checkboxStatus = val
  //     },
  //     immediate:true
  //   }
  // },
  data() {
    return {
      // height: 548,
      xToolbarData: {
        aa: 111,
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: "toolbar_buttons",
        },
      },
      currentCheckRow: null, //当前勾选的行
      checkedTableData: [], //选中的行数组
      selectNum: 0,
      pageNum: 1,
      selectRowsId: [],
      tableColumns: [],
      selectRadioRow: null,
    };
  },
  computed: {},
  watch: {
    currentPage: {
      handler(newVal, oldVal) {
        this.pageNum = newVal;
      },
      immediate: true,
    },
    columns: {
      handler(newVal) {
        const radio = {
          type: "radio",
          customWidth: 60,
          title: "操作",
          // slots: { header: "radio_header" },
        };
        if (this.showRadio) {
          this.tableColumns = [radio, ...newVal];
        } else {
          this.tableColumns = newVal;
        }
        console.log(this.tableColumns, "tableColumns");
      },
      immediate: true,
    },
    tableData(newVal) {
      this.$nextTick(() => {
        //列宽自适应
        if (this.columnAutoFit) {
          //计算所有列宽之和，如果所有列宽加起来小于页面宽度，不主动设置列宽
          let allColumnWidthSum = 0;
          let tempColumns = $deepClone(this.tableColumns);
          let hasBtns = false; //是否有操作按钮列

          //监听tableData的值计算每一列的最大宽度
          tempColumns.forEach((item, index) => {
            if (item.customWidth) {
              //有自定义宽度取自定义宽度
              //使用了插槽，格式化属性值时算不出来合适的宽度，需要定义宽度解决
              allColumnWidthSum += item.customWidth;
              item.width = item.customWidth;
            } else {
              //获取每一列的所有数据
              let columnValArr = newVal.map((column) => {
                return column[item.field];
              });
              //把每列的表头也加进去计算每一列的最大宽度
              columnValArr.push(item.title);

              let maxWidth = Math.round(
                this.getColumnMaxWidth(columnValArr) + 40
              ); // 每列内容最大的宽度 + 表格的内间距(依据实际情况而定)
              let fitWidth = maxWidth;
              if (item.minWidth) {
                fitWidth = maxWidth > item.minWidth ? maxWidth : item.minWidth;
              }

              //console.log("maxWidth ", maxWidth, " fitWidth ", fitWidth);
              allColumnWidthSum += fitWidth;
              item.width = fitWidth;
            }
            //console.log("for allColumnWidthSum:", allColumnWidthSum);

            if (item.title == "操作" && index === tempColumns.length - 1) {
              hasBtns = true;
            }
          });

          let tableWidth = this.$refs.xTable.$el.clientWidth;

          // console.log(
          //   "tableWidth: ",
          //   tableWidth,
          //   " allColumnWidthSum: ",
          //   allColumnWidthSum
          // );
          if (allColumnWidthSum < tableWidth) {
            //如果所有列宽小于表格宽度，均分剩余的宽度给每一列(按钮列不增加宽度，按钮列可能设置了最小宽度，再增加宽度会很宽整体比例失调)
            let addRemainWidth = Math.floor(
              (tableWidth - allColumnWidthSum) /
                (hasBtns ? tempColumns.length - 1 : tempColumns.length)
            );
            //console.log("addRemainWidth ", addRemainWidth);

            tempColumns.forEach((item, index) => {
              //可能遇到除不尽的情况，直接平分可能导致出现横向滚动条
              if (index == 0) {
                //第一列
                let firstColumnAddWidth =
                  tableWidth -
                  allColumnWidthSum -
                  addRemainWidth *
                    (hasBtns ? tempColumns.length - 2 : tempColumns.length - 1);
                item.width += firstColumnAddWidth;
                //console.log("firstColumnAddWidth ", firstColumnAddWidth);
              } else if (index == tempColumns.length - 1) {
                //最后一列
                if (item.title !== "操作") {
                  item.width += addRemainWidth;
                }
              } else {
                item.width += addRemainWidth;
              }
            });
          }
          this.tableColumns = tempColumns;
          //console.log("tempColumns: ", JSON.stringify(tempColumns));

          // //监听tableData的值计算每一列的最大宽度
          // this.tableColumns = this.tableColumns.map(item => {
          //   //已经自定义列宽，使用了插槽，格式化属性值，或者最后一列操作按钮不自动计算列宽
          //   if (item.title !== "操作") {
          //     if (item.customWidth) {
          //       //有自定义宽度取自定义宽度
          //       //使用了插槽，格式化属性值时算不出来合适的宽度，需要定义宽度解决
          //       item.width = item.customWidth;
          //     } else {
          //       //获取每一列的所有数据
          //       let columnValArr = newVal.map(column => {
          //         return column[item.field];
          //       });
          //       //把每列的表头也加进去计算每一列的最大宽度
          //       columnValArr.push(item.title);
          //
          //       item.width = Math.round(
          //         this.getColumnMaxWidth(columnValArr) + 40
          //       ); // 每列内容最大的宽度 + 表格的内间距(依据实际情况而定)
          //     }
          //   }
          //
          //   return item;
          // });
          // } else if (this.columnWidth) {
          //   let tempColumns = $deepClone(this.tableColumns);
          //   tempColumns.forEach((item) => {
          //     if (item.customWidth) {
          //       item.width = item.customWidth;
          //     } else {
          //       item.width = this.columnWidth;
          //     }
          //   });
          //   this.tableColumns = tempColumns;
        }
      });
    },
  },
  methods: {
    clearRadioRowEevnt() {
      this.selectRadioRow = null;
      this.$refs.xTable.clearRadioRow();
      this.$emit("radioChangeEvent", {});
    },
    radioChangeEvent({ row }) {
      this.selectRadioRow = { ...row };
      this.$emit("radioChangeEvent", row);
    },
    toolbarCustomEvent(params) {
      const visibleColumn = this.$refs.xTable.getColumns();
      switch (params.type) {
        case "confirm": {
          if (visibleColumn.length == 0) {
            this.$message.warning("最少显示一列");
            this.$refs.xTable.resetColumn();
          }
          break;
        }
        case "reset": {
          //console.log("点击了reset", visibleColumn.length);
          break;
        }
        case "close": {
          //console.log("点击了close", visibleColumn.length);
          break;
        }
      }
    },
    //分页变化
    handlePageChange() {
      this.$emit("update:currentPage", this.pageNum);
      this.$emit("update:pageSize", this.pageSize);
      setTimeout(() => {
        this.$emit("changePage");
      }, 800);

      // this.checkRowArr = [];
      // this.count = 0;

      //回显选中状态
      this.setSelectRow();
    },
    //分页序号连续
    seqMethod({ row, rowIndex, column, columnIndex }) {
      return (this.pageNum - 1) * this.pageSize + (rowIndex + 1);
    },
    //勾选触发
    checkboxChange({ checked, records, reserves, row }) {
      this.currentCheckRow = row;
      // this.checkRowArr = this.$refs.xTable.getCheckboxRecords();
      // console.log("[ this.checkRowArr ] >", this.checkRowArr);
      // this.count = this.checkRowArr.length;

      //勾选选中时
      if (checked) {
        //第一次选数据，还未进行翻页时
        if (reserves.length == 0) {
          this.selectRowsId = records.map((v) => v[this.rowId]);
          this.checkedTableData = records;
        } else {
          //id集合，翻页存在已选中的数据时,拼接新选中的数据
          this.selectRowsId = [
            ...reserves.map((v) => v[this.rowId]),
            ...records.map((v) => v[this.rowId]),
          ];
          //数据集合，翻页存在已选中的数据时,拼接新选中的数据
          this.checkedTableData = [...reserves, ...records];
        }
      } else {
        //取消选中时
        let idIndex = this.selectRowsId?.indexOf(row[this.rowId]);
        if (idIndex > -1) {
          //删除取消选中删除指定元素id
          this.$delete(this.selectRowsId, idIndex);
        }

        let dataIndex = null;
        if (this.checkedTableData?.length > 0) {
          for (let i = 0; i < this.checkedTableData.length; i++) {
            if (this.checkedTableData[i][this.rowId] == row[this.rowId]) {
              dataIndex = i;
              break;
            }
          }
        }

        //删除取消选中的元素整个对象
        this.$delete(this.checkedTableData, dataIndex);
      }

      this.selectNum = this.checkedTableData?.length;

      this.batchDelete &&
        this.$emit("handleSelectionChange", this.checkedTableData);
    },
    //全选框
    checkboxAll({ checked, records, reserves }) {
      // if (checked) {
      //   this.count = this.pageSize;
      //   this.checkRowArr = this.$refs.xTable.getCheckboxRecords();
      // } else {
      //   this.count = 0;
      //   this.checkRowArr = [];
      // }

      //全选中时
      if (checked) {
        //第一次选数据，还未进行翻页时
        if (reserves.length == 0) {
          this.selectRowsId = records.map((v) => v[this.rowId]);
          this.checkedTableData = records;
        } else {
          //id集合，翻页存在已选中的数据时,拼接新选中的数据
          this.selectRowsId = [
            ...reserves.map((v) => v[this.rowId]),
            ...records.map((v) => v[this.rowId]),
          ];
          //数据集合，翻页存在已选中的数据时,拼接新选中的数据
          this.checkedTableData = [...reserves, ...records];
        }
      } else {
        //取消全选时,直接将翻页数据赋值，当前页数据不用加上
        this.checkedTableData = reserves;
        this.selectRowsId = reserves.map((v) => v[this.rowId]);
      }

      this.selectNum = this.checkedTableData?.length;
      this.batchDelete &&
        this.$emit("handleSelectionChange", this.checkedTableData);
    },
    clearTips() {
      // this.count = 0;
      // this.checkRowArr = [];
      // this.$refs.xTable.clearCheckboxRow();

      this.isSelectAll = false;
      this.selectNum = 0;
      this.$refs.xTable.clearCheckboxRow();
      this.$refs.xTable.clearCheckboxReserve();
      this.checkedTableData = [];
      this.selectRowsId = [];
      this.batchDelete &&
        this.$emit("handleSelectionChange", this.checkedTableData);
    },
    setSelectRow() {
      //初始化选中行
      this.$nextTick(() => {
        let table = this.$refs.xTable;
        if (this.selectNum == this.total) {
          for (let i = 0; i < this.tableData.length; i++) {
            table.setCheckboxRow(this.tableData[i], true);
          }
        } else {
          if (this.selectRowsId?.length > 0) {
            for (let i = 0; i < this.selectRowsId.length; i++) {
              let selectRow = table.getRowById(this.selectRowsId[i]);
              if (selectRow) {
                table.setCheckboxRow(selectRow, true);
              }
            }
          }
        }
      });
    },
    //计算文字宽度
    getTextWidth(text) {
      let width = 0;
      let span = document.createElement("span");
      span.innerText = text;
      document.querySelector("body").appendChild(span);
      width = span.getBoundingClientRect().width;
      span.remove();
      return width;
    },
    //计算每一列的最大宽度
    getColumnMaxWidth(arr) {
      return arr.reduce((maxWidth, item) => {
        if (item) {
          let calcLen = this.getTextWidth(item);
          if (maxWidth < calcLen) {
            maxWidth = calcLen;
          }
        }

        return maxWidth;
      }, 0);
    },
  },
  created() {},
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
/deep/ .header-row {
  height: 90px;
}

/deep/ .vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 0px;
}

/deep/ .vxe-table--render-default .vxe-header--column {
  line-height: 18px;
}

/deep/ .vxe-table--render-default .is--disabled.vxe-cell--checkbox {
  background: #edf2fc80;
  border-radius: 2px;
}

/deep/ .vxe-body--column {
  height: 45px !important;
}
/*滚动条整体部分*/
.mytable-scrollbar ::-webkit-scrollbar {
  // width: 10px;
  height: 16px;
}
/deep/ .vxe-table--render-default .vxe-table--border-line {
  z-index: 6;
}
</style>
