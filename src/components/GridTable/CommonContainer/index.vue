<!-- table -->
<template>
  <div class="common-container">
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {},
  created() {},
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.common-container {
  height: 100%;
  .content {
    padding: 16px 24px;
    background: #fff;
    border-radius: 2px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    position: relative;
    overflow: auto;
    .vxe-grid {
      flex: 1;
      overflow: auto;
    }
  }
}
</style>
