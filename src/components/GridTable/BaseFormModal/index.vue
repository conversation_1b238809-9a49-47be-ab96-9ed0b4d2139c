<template>
  <el-dialog
    :title="modalTitle"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    :width="modalWidth"
  >
    <slot name="modalDefault" :row="formParams"> </slot>
    <DynamicForm
      ref="baseForm"
      :config="config"
      :params="formParams"
      labelPosition="right"
      :defaultColSpan="24"
      v-if="showForm"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <div slot="header">
        <slot name="formHeader"></slot>
      </div>
      <div
        :slot="item.slotName"
        v-for="(item, index) in customSlotsArr"
        :key="index"
      >
        <slot :name="item.slotName" :item="item" :params="formParams"></slot>
      </div>
    </DynamicForm>
    <div slot="footer" class="dialog-footer">
      <slot name="modalFooter" :row="formParams">
        <el-button @click="closeVisible">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading"
          >确 定</el-button
        >
      </slot>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    modalTitle: {
      type: String,
      default: "",
    },
    modalWidth: {
      type: String,
      default: "50%",
    },
    config: {
      type: Array,
      default: () => [],
    },
    showForm: {
      type: Boolean,
      default: true,
    },
    //提交按钮自动关闭弹窗
    autoClose: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      formParams: {},
    };
  },
  computed: {
    customSlotsArr() {
      return this.config?.filter(
        (item) => item.element === "slot" && item.slotName
      );
    },
  },
  methods: {
    submitForm() {
      if (this.showForm) {
        this.$refs.baseForm.validate((valid) => {
          if (!valid) return;
          this.$emit("modalConfirm", this.formParams);
          this.autoClose && this.closeVisible();
        });
      } else {
        this.$emit("modalConfirm", this.formParams);
        this.autoClose && this.closeVisible();
      }
    },
    open(row) {
      this.formParams = { ...row };
      this.visible = true;
    },
    closeVisible() {
      this.visible = false;
    },
    setFormFields(values) {
      this.formParams = { ...this.formParams, ...values };
    },
    getFormFields() {
      return this.formParams;
    },
  },
};
</script>

<style></style>
