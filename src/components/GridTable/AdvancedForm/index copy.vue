<!-- 高级查询form -->
<template>
  <div class="form-content">
    <el-card>
      <div class="queryParamsWrap">
        <el-form
          :model="queryParams"
          :rules="rules"
          ref="queryForm"
          :inline="true"
        >
          <el-row>
            <el-col :span="formCol">
              <!-- <slot name="queryForm" /> -->
              <div v-for="(item, index) in filteredConfig" :key="index">
                <el-col
                  :span="item.colNum || 5"
                  v-if="item.type === 'slot' && index < 4"
                >
                  <el-form-item label="">
                    <slot :name="item.slotName || item.key"></slot>
                  </el-form-item>
                </el-col>
                <el-col :span="item.colNum || 5" v-else-if="index < 4">
                  <!-- 具体筛选组件 -->
                  <el-form-item label="">
                    <el-cascader
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      :options="item.options"
                      clearable
                      filterable
                      size="mini"
                      :disabled="item.disabled"
                      v-if="item.type === 'cascader'"
                      :props="{
                        checkStrictly: true,
                        multiple: true,
                        ...item.props,
                      }"
                      collapse-tags
                    ></el-cascader>
                    <el-cascader
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      :options="item.options"
                      clearable
                      filterable
                      size="mini"
                      :disabled="item.disabled"
                      v-if="item.type === 'deptTree'"
                      :props="{
                        checkStrictly: true,
                        multiple: true,
                        emitPath: false,
                      }"
                      collapse-tags
                    ></el-cascader>
                    <el-input
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      clearable
                      :disabled="item.disabled"
                      size="mini"
                      v-if="item.type === 'input'"
                    />
                    <el-select
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      clearable
                      filterable
                      :disabled="item.disabled"
                      size="mini"
                      style="width: 100%"
                      v-else-if="item.type === 'select'"
                      :multiple="item.multiple"
                      :collapse-tags="item.multiple"
                    >
                      <el-option
                        v-for="dict in item.options"
                        :key="dict[item.optionValue || 'dictValue']"
                        :label="dict[item.optionLabel || 'dictLabel']"
                        :value="dict[item.optionValue || 'dictValue']"
                      />
                    </el-select>
                    <el-select
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      filterable
                      clearable
                      :disabled="item.disabled"
                      size="mini"
                      style="width: 100%"
                      v-else-if="item.type === 'selectGroup'"
                    >
                      <el-option-group
                        v-for="group in item.options"
                        :key="group[item.optionValue || 'dictValue']"
                        :label="group[item.optionLabel || 'dictLabel']"
                      >
                        <el-option
                          v-for="item in group.childrenList"
                          :key="item[item.optionValue || 'dictValue']"
                          :label="item[item.optionLabel || 'dictLabel']"
                          :value="item[item.optionValue || 'dictValue']"
                        >
                        </el-option>
                      </el-option-group>
                    </el-select>
                    <el-date-picker
                      v-model="queryParams[item.key]"
                      size="mini"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      :start-placeholder="item.startPlaceholder || '开始日期'"
                      :end-placeholder="item.endPlaceholder || '结束日期'"
                      v-else-if="item.type === 'dateRange'"
                    ></el-date-picker>
                    <el-date-picker
                      v-model="queryParams[item.key]"
                      type="date"
                      value-format="yyyy-MM-dd"
                      :placeholder="item.title"
                      size="mini"
                      v-else-if="item.type === 'date'"
                    />
                    <el-date-picker
                      v-model="queryParams[item.key]"
                      size="mini"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetimerange"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :default-time="['00:00:00', '23:59:59']"
                      v-else-if="item.type === 'datetimeRange'"
                    ></el-date-picker>
                    <el-row v-if="item.type === 'subSelect'">
                      <el-col :span="12">
                        <el-select
                          v-model="queryParams[item.parentKey]"
                          :placeholder="item.title"
                          clearable
                          filterable
                          :disabled="item.disabled"
                          size="mini"
                          style="width: 100%"
                          :multiple="item.multiple"
                          @change="
                            (val) => {
                              return handleParentChange(val);
                            }
                          "
                        >
                          <el-option
                            v-for="dict in item.parentOptions"
                            :key="dict[item.optionValue || 'dictValue']"
                            :label="dict[item.optionLabel || 'dictLabel']"
                            :value="dict[item.optionValue || 'dictValue']"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="12">
                        <el-select
                          v-model="queryParams[item.key]"
                          :placeholder="item.title"
                          filterable
                          clearable
                          :disabled="item.disabled"
                          size="mini"
                          style="width: 100%"
                        >
                          <el-option
                            v-for="dict in item.options"
                            :key="dict[item.optionValue || 'dictValue']"
                            :label="dict[item.optionLabel || 'dictLabel']"
                            :value="dict[item.optionValue || 'dictValue']"
                          />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </div>
            </el-col>
            <el-col :span="24 - formCol">
              <div class="btn-wrap">
                <slot name="moreBtn">
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    v-if="showExportButton"
                    @click.stop="handleExport"
                    >导出
                  </el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click.stop="handleQuery"
                    >查询
                  </el-button>
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click.stop="resetQuery()"
                    >重置
                  </el-button>
                  <el-link
                    v-show="showMore"
                    @click.stop="openDrawer()"
                    type="primary"
                    class="highSearch"
                    ><i class="vxe-icon-funnel"></i>高级筛选
                  </el-link>
                </slot>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <el-drawer
      title="高级筛选"
      :visible.sync="visible"
      :show-close="false"
      custom-class="drawer-wrap"
      append-to-body
      ref="drawer"
    >
      <div class="demo-drawer__content">
        <div class="queryParamsWrap drawer">
          <el-form
            :model="queryParams"
            label-width="100px"
            :rules="rules"
            :inline="true"
          >
            <el-row>
              <div v-for="(item, index) in filteredConfig" :key="index">
                <el-col :span="item.span || 12">
                  <el-form-item :label="item.title" v-if="item.type === 'slot'">
                    <template>
                      <slot :name="item.key"></slot>
                    </template>
                  </el-form-item>
                  <!-- 具体筛选组件 -->
                  <el-form-item :label="item.title" v-else>
                    <el-input
                      v-model="queryParams[item.key]"
                      :placeholder="item.placeholder"
                      clearable
                      :disabled="item.disabled"
                      size="mini"
                      v-if="item.type === 'input'"
                    />
                    <el-cascader
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      :options="item.options"
                      clearable
                      filterable
                      size="mini"
                      :disabled="item.disabled"
                      v-if="item.type === 'cascader'"
                      :props="{
                        checkStrictly: true,
                        multiple: true,
                        ...item.props,
                      }"
                      collapse-tags
                    >
                    </el-cascader>
                    <el-cascader
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      :options="item.options"
                      clearable
                      filterable
                      size="mini"
                      :disabled="item.disabled"
                      v-if="item.type === 'deptTree'"
                      :props="{
                        checkStrictly: true,
                        multiple: true,
                        emitPath: false,
                      }"
                      collapse-tags
                    ></el-cascader>
                    <el-select
                      v-model="queryParams[item.key]"
                      :placeholder="item.placeholder"
                      clearable
                      filterable
                      :disabled="item.disabled"
                      size="mini"
                      style="width: 100%"
                      v-if="item.type === 'select'"
                      :multiple="item.multiple"
                      :collapse-tags="item.multiple"
                    >
                      <el-option
                        v-for="dict in item.options"
                        :key="dict[item.optionValue || 'dictValue']"
                        :label="dict[item.optionLabel || 'dictLabel']"
                        :value="dict[item.optionValue || 'dictValue']"
                      />
                    </el-select>
                    <el-select
                      v-model="queryParams[item.key]"
                      :placeholder="item.title"
                      filterable
                      clearable
                      :disabled="item.disabled"
                      size="mini"
                      style="width: 100%"
                      v-else-if="item.type === 'selectGroup'"
                    >
                      <el-option-group
                        v-for="group in item.options"
                        :key="group[item.optionValue || 'dictValue']"
                        :label="group[item.optionLabel || 'dictLabel']"
                      >
                        <el-option
                          v-for="item in group.childrenList"
                          :key="item[item.optionValue || 'dictValue']"
                          :label="item[item.optionLabel || 'dictLabel']"
                          :value="item[item.optionValue || 'dictValue']"
                        >
                        </el-option>
                      </el-option-group>
                    </el-select>
                    <el-date-picker
                      v-model="queryParams[item.key]"
                      size="mini"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      :start-placeholder="item.startPlaceholder || '开始日期'"
                      :end-placeholder="item.endPlaceholder || '结束日期'"
                      v-if="item.type === 'dateRange'"
                    ></el-date-picker>
                    <el-date-picker
                      v-model="queryParams[item.key]"
                      size="mini"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetimerange"
                      range-separator="-"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :default-time="['00:00:00', '23:59:59']"
                      v-else-if="item.type === 'datetimeRange'"
                    ></el-date-picker>
                    <el-date-picker
                      v-model="queryParams[item.key]"
                      type="date"
                      value-format="yyyy-MM-dd"
                      :placeholder="item.placeholder"
                      size="mini"
                      v-if="item.type === 'date'"
                    />
                    <el-row v-if="item.type === 'subSelect'">
                      <el-col :span="12">
                        <el-select
                          v-model="queryParams[item.parentKey]"
                          :placeholder="item.title"
                          clearable
                          filterable
                          :disabled="item.disabled"
                          size="mini"
                          style="width: 100%"
                          :multiple="item.multiple"
                          @change="
                            (val) => {
                              return handleParentChange(val);
                            }
                          "
                        >
                          <el-option
                            v-for="dict in item.parentOptions"
                            :key="dict[item.optionValue || 'dictValue']"
                            :label="dict[item.optionLabel || 'dictLabel']"
                            :value="dict[item.optionValue || 'dictValue']"
                          />
                        </el-select>
                      </el-col>
                      <el-col :span="12">
                        <el-select
                          v-model="queryParams[item.key]"
                          :placeholder="item.title"
                          filterable
                          clearable
                          :disabled="item.disabled"
                          size="mini"
                          style="width: 100%"
                        >
                          <el-option
                            v-for="dict in item.options"
                            :key="dict[item.optionValue || 'dictValue']"
                            :label="dict[item.optionLabel || 'dictLabel']"
                            :value="dict[item.optionValue || 'dictValue']"
                          />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </div>
            </el-row>
          </el-form>
        </div>
        <div class="demo-drawer__footer">
          <el-button @click="cancelForm">取 消</el-button>
          <el-button type="primary" @click="confirm" :loading="loading"
            >{{ loading ? "查询中 ..." : "查 询" }}
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    showMore: {
      type: Boolean,
      default: false,
    },
    rules: {
      type: Object,
      default: () => {},
    },
    config: {
      type: Array,
      default: () => [],
    },
    // 筛选结果
    queryParams: {
      type: Object,
      default: () => ({}),
    },
    showExportButton: {
      type: Boolean,
      default: false,
    },
    //表单部分所占栅格数
    formCol: {
      type: Number,
      default: 19,
    },
  },
  data() {
    return {
      loading: false,
      visible: false,
      connectStatusOpts: [],
    };
  },
  computed: {
    filteredConfig() {
      // 过滤掉隐藏的配置项
      return this.config?.filter((item) => !item.hidden);
    },
  },
  watch: {},
  methods: {
    handleClose() {},
    cancelForm() {
      this.visible = false;
    },
    confirm() {
      this.handleQuery();
      this.visible = false;
    },
    handleExport() {
      if (
        this.queryParams.dateRange &&
        this.queryParams.dateRange.length > 0 &&
        this.queryParams.dateRange[0].length > 10
      ) {
        this.queryParams.dateRange[0] = this.queryParams.dateRange[0];
        this.queryParams.dateRange[1] = this.queryParams.dateRange[1];
      } else if (
        this.queryParams.dateRange &&
        this.queryParams.dateRange.length > 0
      ) {
        this.queryParams.dateRange[0] =
          this.queryParams.dateRange[0] + " 00:00:00";
        this.queryParams.dateRange[1] =
          this.queryParams.dateRange[1] + " 23:59:59";
      } else if (this.queryParams.dateRange == null) {
        delete this.queryParams.createBeginTime;
        delete this.queryParams.createEndTime;
      }
      this.$emit("handleExport", this.queryParams);
    },
    handleQuery() {
      console.log("搜索条件---:", JSON.stringify(this.queryParams));
      if (
        this.queryParams.dateRange &&
        this.queryParams.dateRange.length > 0 &&
        this.queryParams.dateRange[0].length > 10
      ) {
        this.queryParams.dateRange[0] = this.queryParams.dateRange[0];
        this.queryParams.dateRange[1] = this.queryParams.dateRange[1];
      } else if (
        this.queryParams.dateRange &&
        this.queryParams.dateRange.length > 0
      ) {
        this.queryParams.dateRange[0] =
          this.queryParams.dateRange[0] + " 00:00:00";
        this.queryParams.dateRange[1] =
          this.queryParams.dateRange[1] + " 23:59:59";
      } else if (this.queryParams.dateRange == null) {
        delete this.queryParams.createBeginTime;
        delete this.queryParams.createEndTime;
      }
      this.$emit("confirm", this.queryParams);
    },
    resetQuery() {
      this.$emit("resetQuery");
    },
    openDrawer() {
      this.visible = true;
    },
    handleParentChange(val) {
      console.log("change", val);
      this.$emit("handleParentChange", val);
    },
  },
  created() {},
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.queryParamsWrap .el-col {
  padding-left: 0;
}

.btn-wrap {
  display: flex;
  justify-content: space-evenly;
}

/deep/ .drawer-wrap {
  height: 100% !important;
  width: 40% !important;
  right: 0;

  .el-col {
    margin-bottom: 10px;
  }
}

/deep/ .el-drawer__body {
  padding: 10px;
}

.highSearch {
  white-space: nowrap;
}

/deep/ .el-drawer__header {
  margin-bottom: 0 !important;
  padding: 20px !important;
  background-color: #eae9e9;
  font-weight: bold;
}

.demo-drawer__footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/deep/ .el-form-item {
  margin-bottom: 0px !important;
}

.form-content {
  margin-bottom: 10px;
}

// .drawer {
//   .el-form-item {
//     margin-bottom: 10px !important;
//   }
// }
/deep/ .el-card {
  overflow: inherit !important;
}

/deep/ .el-form-item__content {
  width: 100% !important;
}
</style>
