<!--多选 此控件是选上级全选下级 -->
<template>
  <el-select
    v-model="mineStatus"
    class="select"
    placeholder="请选择"
    multiple
    collapse-tags
    @change="selectChange"
    size="mini"
  >
    <el-option :value="mineStatusValue" class="selectTreeItem" style="height: auto">
      <el-tree
        :data="companyOptions"
        show-checkbox
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        @check-change="handleCheckChange"
      ></el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: "commonTreeSelect",
  data() {
    return {
      mineStatus: "",
      mineStatusValue: [],
      companyOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      }
    };
  },
  model: {
    prop: "orgNoList",
    event: "changeTree"
  },
  props: {
    orgNoList: {
      type: Array,
      default: null
    }
  },
  components: {},
  watch: {},
  beforeCreate() {},
  created() {
    this.companyOptions = this.$store.state.user.companyOptions;
    if (this.companyOptions.length > 0) {
      this.$emit("changeTree", this.$store.state.user.orgNoListId);
    }
  },
  mounted() {
    if (this.companyOptions.length > 0) {
      this.mineStatusValue = this.$store.state.user.orgNoList;
      this.mineStatus = this.$store.state.user.orgNoListLabel;
      this.selectChange(this.mineStatus);
    }
  },
  methods: {
    //select框值改变时候触发的事件
    selectChange(e) {
      let arrNew = [];
      let arrId = [];
      let dataLength = this.mineStatusValue.length;
      let eleng = e.length;
      for (let i = 0; i < dataLength; i++) {
        for (let j = 0; j < eleng; j++) {
          if (e[j] === this.mineStatusValue[i].label) {
            arrNew.push(this.mineStatusValue[i]);
            arrId.push(this.mineStatusValue[i].id);
          }
        }
      }
      this.$refs.tree.setCheckedNodes(arrNew); //设置勾选的值
      if (this.companyOptions.length > 0) {
        this.$emit("changeTree", arrId);
      }
    },
    handleCheckChange() {
      let res = this.$refs.tree.getCheckedNodes(false, false); //这里两个true，1. 是否只是叶子节点 2. 是否包含半选节点（就是使得选择的时候不包含父节点）
      let arrLabel = [];
      let arrId = [];
      let arr = [];
      res.forEach(item => {
        arrLabel.push(item.label);
        arrId.push(item.id);
        arr.push(item);
      });
      this.mineStatusValue = arr;
      this.mineStatus = arrLabel;
      this.$emit("changeTree", arrId);
      this.$emit("input", arrId);
    },
    // 重置
    reset() {
      if (this.companyOptions.length > 0) {
        this.mineStatusValue = this.$store.state.user.orgNoList;
        this.mineStatus = this.$store.state.user.orgNoListLabel;
        this.selectChange(this.mineStatus);
        this.$emit("changeTree", this.$store.state.user.orgNoListId);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.selectTreeItem {
  padding: 0;
  padding-right: 0 !important;
}
/deep/.el-tree-node__content{
  padding-right: 26px;
}
</style>
