<!-- vue-tree-select插件组件封装 -->
<template>
  <div class="">
    <treeselect @close="changeValue"
                :autoDeselectDescendants="true"
                v-model="queryParams.orgNoList"
                :options="orgTreeList"
                :disabled="disabledFlag"
                :multiple="multiple"
                :flat="flat"
                :clearable="false"
                :limit="1"
                :default-expand-level="1" placeholder="请选择营业所" :limitText="(count) => `更多+ ${count}`"/>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { permsTreeSelect } from "@/api/components/index.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {Treeselect},
  props: {
    disabledFlag: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    flat: {
      type: Boolean,
      default: false
    },
    orgNoList: {
      type: Array,
      default: null
    }
  },
  model: {
    prop: "orgNoList",
    event: "changeValue"
  },
  data() {
    //这里存放数据
    return {
      queryParams: {
        orgNoList: this.multiple ? [] : '',
      },
      isOpenPrivilege: 0,
      orgTreeList: []
    };
  },
  computed: {},
  watch: {
    "queryParams.orgNoList.length": {
      handler(newVal) {
        this.changeValue()
      },
      immediate:true,
      deep: true
    }
  },
  //方法集合
  methods: {
    async getTreeselect() {
      let params = {
        isOpenPrivilege: this.isOpenPrivilege,
      };
      await permsTreeSelect(params).then((response) => {
        this.orgTreeList = response.data;
      });
    },
    reset() {
      if (this.multiple) {
        this.queryParams.orgNoList = this.$store.state.user.orgNoListId
      } else {
        this.queryParams.orgNoList = this.$store.state.user.orgNoListId[0]
      }
      this.changeValue()
    },
    //选择后触发父页面事件
    changeValue() {
      this.$emit("changeValue", this.queryParams.orgNoList)
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  async created() {
    //默认选中所有
    this.reset()
    await this.getTreeselect()
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
/deep/ .el-form-item--small .el-form-item__content, .el-form-item--small .el-form-item__label {
  line-height: 14px;
}

/deep/ .vue-treeselect__control {
  height: 32px;
}

/deep/.el-form-item--mini /deep/.el-form-item__content, /deep/.el-form-item--mini /deep/.el-form-item__label{
  line-height: 24px;
}

/deep/ .vue-treeselect__value-container {
  vertical-align: text-top;
}

//组件高度
/deep/.vue-treeselect--has-value .vue-treeselect__multi-value {
  overflow-y: scroll;
  height: 23px;
}
</style>