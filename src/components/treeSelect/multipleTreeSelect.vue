<!--单选 -->
<template>
  <el-select v-model="mineStatus" class="select"       ref="selectTreeItem"
             placeholder="请选择" @focus="focusSelectChange" @blur="blurSelectChange" :disabled="isDisabled">
    <el-option
      :value="mineStatusValue"
      class="selectTreeItem"
      style="height: auto"
    >
      <el-tree
        :data="companyOptions"
        node-key="id"
        :check-strictly="true"
        ref="tree"
        highlight-current
        :props="defaultProps"
        @node-click="handleCheckChange"
      ></el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: "commonTreeSelect",
  data() {
    return {
      mineStatus: "",
      mineStatusValue: [],
      companyOptions: [],
      defaultProps: {
        children: "children",
        label: "label"
      }
    };
  },
  model: {
    prop: "org",
    event: "changeTree"
  },
  props: {
    org: {
      type: String,
      default: null
    },
    isDisabled: {
      type: Boolean,
      default: false,
    }
  },
  components: {},
  watch: {
    org(value) {
      if (typeof value == "undefined" || value == null || value == "") {
        if (this.companyOptions.length > 0) {
          this.$emit("changeTree", this.companyOptions[0].id);
        }
      } else {
        this.mineStatus = this.$store.state.user.orgNoList.find(i=>i.id == value).label;
        this.$emit("changeTree", this.$store.state.user.orgNoList.find(i=>i.id == value).id);
      }
    }
  },
  beforeCreate() {},
  created() {
    this.companyOptions = this.$store.state.user.companyOptions;
    if (this.companyOptions.length > 0 && !this.org) {
      this.$emit("changeTree", this.companyOptions[0].id);
    } else if (this.org){
      this.$emit("changeTree", this.org);
    }
  },
  mounted() {
    if (this.companyOptions.length > 0 && !this.org) {
      this.mineStatusValue = this.$store.state.user.orgNoList;
      this.mineStatus = this.companyOptions[0].label;
    } else if (this.org){
      this.mineStatusValue = this.$store.state.user.orgNoList;
      this.mineStatus = this.mineStatusValue.find(i=>i.id == this.org).label;
    }
  },
  methods: {
    blurSelectChange() {
      setTimeout(() => {
        this.$emit("blurSelectChange", this.mineStatusValue.id);
      }, 500);
    },
    focusSelectChange() {
      setTimeout(() => {
        this.$emit("focusSelectChange", this.mineStatusValue.id);
      }, 500);
    },
    handleCheckChange(data) {
      this.mineStatusValue = data;
      this.mineStatus = data.label;
      this.$emit("changeTree", data.id);
      this.$emit("input", data.id);
      this.$emit("handleCheckChange", true);
      if (data.$treeNodeId !=1){
        this.$refs["selectTreeItem"].blur();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.selectTreeItem {
  padding: 0;
  padding-right: 0 !important;
}
/deep/.el-tree-node__content {
  padding-right: 26px;
}
</style>
