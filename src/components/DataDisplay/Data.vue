<template>
  <div class="item">
    <!-- 编辑 -->
    <template v-if="config.edit">
      <component
        :is="config.element"
        v-bind="config.props"
        v-model="value"
        v-on="eventHandle()"
        :class="
          errorIndex === index && errorField === config.value ? 'errorCell' : ''
        "
      >
        <template v-for="o in config.options || []">
          <component
            v-if="optionsMap[config.element] === 'el-radio'"
            :is="optionsMap[config.element]"
            :label="o.value"
            :key="o.label"
            >{{ o.label }}</component
          >
          <component
            v-else
            :is="optionsMap[config.element]"
            v-bind="o"
            :key="o.value"
            >{{ o.label }}</component
          >
        </template>
      </component>
    </template>
    <template v-else-if="config.cellEdit">
      <component
        :is="config.element"
        v-bind="config.props"
        v-model="value"
        v-on="eventHandle()"
        :class="
          errorIndex === index && errorField === config.value ? 'errorCell' : ''
        "
        class="item__input"
        @blur="handleBlur"
        ref="selectRef"
        v-if="rowEditable"
      >
        <template
          slot="append"
          v-if="config.element === 'el-input' && config.append"
          >{{ config.append }}</template
        >
        <template v-for="o in config.options || []">
          <component
            v-if="optionsMap[config.element] === 'el-radio'"
            :is="optionsMap[config.element]"
            :label="o.value"
            :key="o.label"
            >{{ o.label }}</component
          >
          <el-option v-else v-bind="o" :key="o.value">{{ o.label }}</el-option>
        </template>
      </component>

      <div class="item__txt" v-else>
        <template v-if="config.element === 'file-upload'">
          <el-image
            class="imgWrap"
            :src="item"
            :preview-src-list="imglist(newData[config.value])"
            v-for="item in imglist(newData[config.value])"
            :key="item"
            fit="cover"
          >
          </el-image>
        </template>
        <template v-else>{{ format(config) }}</template>
      </div>
    </template>
    <!-- 展示 -->
    <template v-else>
      <template v-if="config.element === 'el-image'">
        <el-image
          class="imgWrap"
          :src="item"
          :preview-src-list="imglist(newData[config.value])"
          v-for="item in imglist(newData[config.value])"
          :key="item"
          fit="cover"
        >
        </el-image>
      </template>
      <template v-else-if="config.element === 'fileIcons'">
        <FileIcons :list="newData[config.value]" :iconWidth="40"></FileIcons>
      </template>
      <div v-else-if="config.html" v-html="format(config)" />
      <component
        v-else
        :is="config.element || 'span'"
        v-bind="config.props"
        v-on="eventHandle()"
        style="white-space: pre-wrap;"
        >{{ format(config) }}</component
      ></template
    >
  </div>
</template>

<script>
import { optionsMap } from "./constant";
import FileUpload from "@/components/Upload/fileUpload4.vue";

import FileIcons from "@/components/FileIcons/index.vue";
export default {
  components: { FileIcons, FileUpload },
  props: {
    config: {
      // 配置
      type: Object,
      default: () => {
        return {};
      },
    },
    data: {
      // table组件单行数据(展示场景)
      type: [Object, Array],
      default: () => [],
    },
    index: {
      // table组件单行index
      type: [Number, String],
      default: 0,
    },
    form: {
      // table组件单行数据（编辑场景）
      type: Object,
      default: () => {
        return {};
      },
    },
    errorField: {
      // 编辑场景，校验失败field
      type: String,
      default: null,
    },
    errorIndex: {
      // 编辑场景，校验失败index
      type: [String, Number],
      default: null,
    },
    dicts: {
      type: Object,
      default: () => ({}),
    },
    isRowEdit: {
      type: Array,
      default: () => [],
    },
    editRowId: {
      type: String,
      default: "acceptanceId",
    },
  },
  data() {
    return {
      optionsMap,
      value: null,
      newData: {},
      fileArr: [],
    };
  },
  watch: {
    value(val) {
      this.$emit("change", { value: val, field: this.config.value });
      // console.log("value-change", val, this.index, this.config.value);
    },
    data: {
      immediate: true,
      handler(val) {
        this.newData = val;
        if (this.config.edit || this.config.cellEdit) {
          this.value = val[this.config.value];
        }
      },
    },
    cellEnter(val) {
      if (val === true) {
        this.$nextTick(() => {
          this.$refs.selectRef?.focus?.();
        });
      }
    },
  },
  computed: {
    imglist() {
      return (data) => {
        if (Array.isArray(data)) {
          return data.map((x) => {
            if (typeof x === "string") {
              return x;
            } else {
              return x.storePath;
            }
          });
        } else if (data?.indexOf(",") > -1) {
          return data.split(",");
        }
        return data ? [data] : [];
      };
    },
    rowEditable() {
      return this.isRowEdit.includes(this.data?.[this.editRowId]);
    },
  },
  methods: {
    handleBlur() {
      // this.$emit("editBlur");
    },
    // 格式化数据
    format({ value, format }) {
      if (typeof format === "function") {
        // @params 当前值，索引，所有字典，单行数据
        return format(
          this.newData[value],
          this.index,
          this.dicts,
          this.newData
        );
      }
      return this.newData[value];
    },
    wrapHandler(originalHandler) {
      let _t = this;
      return function(e) {
        originalHandler.call(_t, e, {
          config: _t.config,
          formValue: _t.form,
          index: _t.index,
          data: _t.newData,
          value: _t.newData[_t.config.value],
        });
      };
    },
    eventHandle() {
      let newEvent = {};
      Object.keys(this.config.on || {}).forEach((i) => {
        Object.assign(newEvent, { [i]: this.wrapHandler(this.config.on[i]) });
      });

      return newEvent;
    },
  },
};
</script>

<style scoped lang="less">
.imgWrap {
  width: 60px;
  height: 60px;
  margin: 0 5px;
}
.errorCell {
  border: 1px solid #f56c6c;
}
.item {
  .item__input {
    // display: none;
    // width: 100px;
  }
  .item__txt {
    box-sizing: border-box;
    line-height: 24px;
    padding: 0 9px;
  }
}
</style>
