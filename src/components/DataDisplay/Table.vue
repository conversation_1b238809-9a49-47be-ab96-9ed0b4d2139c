<template>
  <div>
    <el-table
      :data="data"
      border
      :span-method="spanMethod || spanMethod2"
      header-cell-class-name="table-header"
    >
      <template v-for="i in config">
        <!-- 分组表头 -->
        <el-table-column
          v-if="i.children"
          :key="i.value + 'group'"
          :label="i.label"
          align="center"
          :show-overflow-tooltip="false"
          v-bind="i.props"
          :width="i.width"
          :minWidth="i.minWidth"
          class-name="group-column"
        >
          <template slot="header">
            <div class="more-header-cell-group">
              <div class="more-header-cell-group-name">{{ i.label }}</div>
              <div class="more-header-cell-group-ctx">
                <div
                  v-for="c in i.children"
                  :key="c.value"
                  v-bind="c.props"
                  class="more-header-cell"
                >
                  {{ c.label }}
                </div>
              </div>
            </div>
          </template>

          <template slot-scope="scope">
            <div class="more-date-group">
              <div
                v-for="(c, k) in i.children"
                :key="c.value"
                class="more-date-cell"
              >
                <Data
                  :data="scope.row"
                  :config="c"
                  :index="`${scope.$index}_${k}`"
                  :form="form"
                  @change="(v) => valChange(v, `${scope.$index}_${k}`)"
                  :dicts="dicts"
                />
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 单个表头 -->
        <el-table-column
          v-else
          :key="i.value"
          :label="i.label"
          :prop="i.value"
          :align="i.align || 'center'"
          header-align="center"
          :show-overflow-tooltip="false"
          v-bind="i.props"
          :width="i.width"
          :minWidth="i.minWidth"
          :fixed="i.fixed"
        >
          <template slot-scope="scope">
            <slot
              name="operation"
              :row="scope.row"
              v-if="i.type === 'operation'"
            ></slot>

            <Data
              :data="scope.row"
              :config="i"
              :index="scope.$index"
              :form="form"
              @change="(v) => valChange(v, scope.$index, scope.row)"
              @editBlur="handleCellLeave"
              :errorField="errorField"
              :errorIndex="errorIndex"
              :dicts="dicts"
              :isRowEdit="isRowEdit"
              :editRowId="editRowId"
              v-else
            />
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import Data from "./Data.vue";

export default {
  components: { Data },
  props: {
    config: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    spanMethod: {
      type: Function,
    },
    validate: {
      type: Function,
    },
    dicts: {
      type: Object,
      default: () => ({}),
    },
    isRowEdit: {
      type: Array,
      default: () => [],
    },
    editRowId: {
      type: String,
      default: "acceptanceId",
    },
  },
  data() {
    return {
      form: {},
      errorField: null,
      errorIndex: null,
      pageNum: 1,
      pageSize: 10,
      beforeUpdateData: "",
      cellEnter: false,
      editId: 0,
      newData: [],
    };
  },
  computed: {
    //可编辑的表格列字段名
    editableFields() {
      return this.config?.filter((x) => x.cellEdit)?.map((i) => i.value);
    },
  },
  watch: {
    data(val) {
      this.newData = val;
    },
  },
  methods: {
    /** 鼠标移入cell */
    handleCellEnter(row, column, cell) {
      if (this.editableFields.includes(column.property)) {
        setTimeout(() => {
          this.editId = row[this.editRowId];
          console.log("点击时间");
          this.cellEnter = true;
          cell.querySelector(".item__input").style.display = "block";
          cell.querySelector(".item__txt").style.display = "none";
          this.cell = cell;
          //记录编辑前的数据，保存时判断是否改变
          this.beforeUpdateData = row[column.property];
        }, 100);
      }
    },
    /** 鼠标移出cell */
    handleCellLeave(flag = true) {
      console.log("失焦事件");
      this.cellEnter = false;
      this.cell.querySelector(".item__input").style.display = "none";
      this.cell.querySelector(".item__txt").style.display = "block";
      this.$nextTick(() => {
        flag &&
          this.$emit(
            "edited",
            this.data?.find((x) => x[this.editRowId] === this.editId)
          );
      });
    },
    //分页变化
    handlePageChange() {
      this.$emit("update:currentPage", this.pageNum);
      this.$emit("update:pageSize", this.pageSize);
      setTimeout(() => {
        this.$emit("changePage");
      }, 800);
    },
    // @returns [boolean, formValue] | Promise<rejected>
    //新增传参：isNeed 是否需要校验
    async getFormValue(isNeed) {
      if (typeof this.validate === "function" && isNeed) {
        const [valid, error] = await this.validate(this.data, this.form);
        if (valid) {
          this.errorField = null;
          this.errorIndex = null;
          return [valid, this.form];
        } else {
          const { message, index, field } = error;
          this.errorField = field;
          this.errorIndex = index;
          return Promise.reject(message);
        }
      }
      return [true, this.form];
    },
    resetFormValue() {
      this.form = {};
    },
    valChange({ value, field }, index, row) {
      this.$nextTick(() => {
        let fieldValue = this.form[field] || [];
        fieldValue[index] = value;
        Object.assign(this.form, { [field]: fieldValue });
        field === "buildReason" && this.$emit("change", this.form);
        //表格数据直接修改
        this.newData[index][field] = value;
        this.$emit("edited", { ...row, [field]: value });
      });
    },
    // 设置第一列相同数据合并单元格
    spanMethod2({ rowIndex, columnIndex }) {
      const repeat = {};
      const recordRow = {};
      const firstColumn = this.config[0].value;
      for (let k = 0; k < this.data.length; k++) {
        const i = this.data[k];
        const rowspan = this.data.filter(
          (c) => c[firstColumn] === i[firstColumn]
        );
        if (rowspan.length > 1) {
          const repeatValue = rowspan[0][firstColumn];
          if (!repeat[repeatValue]) {
            repeat[repeatValue] = 1;
            Object.assign(recordRow, { [k]: [rowspan.length, 1] });
          } else {
            Object.assign(recordRow, { [k]: [0, 0] });
            continue;
          }
        }
      }
      if (columnIndex === 0) {
        if (recordRow[rowIndex]) {
          return recordRow[rowIndex];
        }
      }
    },
  },
};
</script>
<style>
.table-header,
.more-header-cell {
  background: rgb(2, 114, 106) !important;
  color: #fff !important;
}
</style>
<style lang="less" scoped>
.more-header-cell-group {
  .more-header-cell-group-ctx {
    display: flex;
    align-items: center;
    border-top: 1px solid #f1f1f1;
  }
  .more-header-cell-group-name {
    margin: 8px 0;
  }
  .more-header-cell {
    background: #fff;
    color: #666;
    flex: 1;
    border-right: 1px solid #f1f1f1;
    padding: 8px 0;
    &:last-child {
      border: none;
    }
  }
}
.more-date-group {
  display: flex;
  align-items: center;
  .more-date-cell {
    flex: 1;
    border-right: 2px solid #f1f1f1;
    padding: 8px 0;
    &:last-child {
      border: none;
    }
  }
}
/deep/ .group-column {
  padding: 0 0 !important;
  & > div {
    height: 100% !important;
    width: 100% !important;
    padding: 0 !important;
  }
}
</style>
