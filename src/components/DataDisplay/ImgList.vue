<template>
  <el-row :gutter="gutter">
    <el-col
      :span="span"
      v-for="i in imglist"
      :key="i"
      :style="{
        marginBottom: `${gutter}px`,
        width: '100px',
        marginRight: '5px',
      }"
    >
      <el-image
        class="imgWrap"
        :src="i"
        fit="cover"
        :preview-src-list="imglist"
      />
    </el-col>
    <el-empty v-if="!imglist.length" description="未上传图片！" size="small" />
  </el-row>
</template>

<script>
export default {
  props: {
    config: {
      type: Array,
      default: () => [],
    },
    data: {
      type: [Array, String],
      default: () => {
        return [];
      },
    },
    gutter: {
      type: Number,
      default: 24,
    },
    span: {
      type: Number,
      default: 3,
    },
  },
  watch: {
    data: {
      immediate: true,
      handler(val) {
        this.imglist = val ? (Array.isArray(val) ? val : val.split(",")) : [];
      },
    },
  },
  data() {
    return {
      imglist: [],
    };
  },
  methods: {},
};
</script>
<style scoped lang="less">
.imgWrap {
  width: 100px;
  height: 100px;
  margin: 0 5px;
  display: block;
}
</style>
