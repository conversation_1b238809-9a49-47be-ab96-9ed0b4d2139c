<template>
  <div>
    <div v-for="(item, index) in config" :key="index" class="common-box mb20">
      <div class="mb20">{{ item.title }}</div>
      <ImgList :data="data[item.value]"></ImgList>
    </div>
  </div>
</template>

<script>
import ImgList from "./ImgList.vue";
export default {
  components: { ImgList },
  props: {
    config: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => {
        return [];
      },
    },
    gutter: {
      type: Number,
      default: 24,
    },
    span: {
      type: Number,
      default: 4,
    },
  },
  watch: {
    // data: {
    //   immediate: true,
    //   handler(val) {
    //     this.imglist = val ? (Array.isArray(val) ? val : val.split(",")) : [];
    //   },
    // },
  },
  data() {
    return {
      imglist: [],
    };
  },
  methods: {},
};
</script>
<style scoped lang="less">
.imgWrap {
  width: 100px;
  height: 100px;
  margin: 0 5px;
  display: block;
}
.common-box {
  border: 1px dashed #029c7c;
  padding: 20px;
  border-radius: 4px;
}
</style>
