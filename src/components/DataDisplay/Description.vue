<!-- el-description -->
<template>
  <el-descriptions :column="column" border :labelStyle="labelStyle">
    <el-descriptions-item
      :label="i.label"
      v-for="i in config"
      :key="i.value"
      v-bind="i.props"
    >
      <Data :config="i" :data="newData" :dicts="dicts"></Data>
    </el-descriptions-item>
  </el-descriptions>
</template>

<script>
import Data from "./Data.vue";
export default {
  components: { Data },
  props: {
    column: {
      type: Number,
      default: 2,
    },
    data: {
      type: [Object, Array],
      default: () => {
        return {};
      },
    },
    config: {
      type: Array,
      default: () => {
        return [];
      },
    },
    labelStyle: {
      type: Object,
      default: () => ({
        width: `250px`,
      }),
    },
    dicts: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      newData: {},
    };
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {
    data: {
      immediate: true,
      handler(v) {
        if (Array.isArray(v)) {
          this.newData = v[0];
        } else {
          this.newData = v;
        }
      },
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
}
</style>
