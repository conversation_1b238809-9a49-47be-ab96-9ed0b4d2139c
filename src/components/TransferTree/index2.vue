<template>
  <div class="el-transfer flex-space-between-center padding-0-20">
    <div class="el-transfer-panel">
      <p class="el-transfer-panel__header">
        <el-checkbox
          v-model="leftObj.checkAll"
          :indeterminate="leftObj.isIndeterminate"
          @change="(val) => checkboxChange(val, 'left')"
          >{{ titles[0] }}</el-checkbox
        >
        <span class="count-float-right"
          >{{ leftChooseCount }}/{{ leftAllCount }}</span
        >
      </p>
      <div class="el-transfer-panel__body">
        <!-- <div v-if="!leftObj.treeData.length" class="el-transfer-panel__empty">
          无数据
        </div> -->
        <el-input
          prefix-icon="el-icon-search"
          v-model="leftSearchText"
          clearable
          placeholder="请输入搜索内容"
        ></el-input>
        <el-tree
          v-loading="leftLoading"
          ref="leftTree"
          :data="leftData"
          :props="defaultProps"
          show-checkbox
          node-key="id"
          @check="
            (data, checked) => {
              handleCheck(data, checked, 'left');
            }
          "
          :filter-node-method="filterNode"
          :default-expand-all="defaultExpandAll"
        />
      </div>
    </div>
    <div class="el-transfer__buttons">
      <el-button
        type="primary"
        icon="el-icon-arrow-left"
        @click="towardsLeft"
      />
      <el-button
        type="primary"
        icon="el-icon-arrow-right"
        @click="towardsRight"
      />
    </div>
    <div class="el-transfer-panel">
      <p class="el-transfer-panel__header">
        <el-checkbox
          v-model="rightObj.checkAll"
          :indeterminate="rightObj.isIndeterminate"
          @change="(val) => checkboxChange(val, 'right')"
          >{{ titles[1] }}</el-checkbox
        >
        <span class="count-float-right"
          >{{ rightChooseCount }}/{{ rightAllCount }}</span
        >
      </p>
      <div class="el-transfer-panel__body">
        <!-- <div v-if="!rightObj.treeData.length" class="el-transfer-panel__empty">
          无数据
        </div> -->
        <el-input
          prefix-icon="el-icon-search"
          v-model="rightSearchText"
          clearable
          placeholder="请输入搜索内容"
        ></el-input>
        <el-tree
          v-loading="rightLoading"
          ref="rightTree"
          :data="rightData"
          :props="defaultProps"
          show-checkbox
          node-key="id"
          @check="
            (data, checked) => {
              handleCheck(data, checked, 'right');
            }
          "
          :filter-node-method="filterNode"
          :default-expand-all="defaultExpandAll"
        />
      </div>
    </div>
  </div>
</template>

<script>
const mockTreeData = [
  {
    id: "1",
    label: "无锡",
    children: [
      {
        id: "1-1",
        label: "1-1",
        pid: "1",
      },
      {
        id: "1-2",
        label: "1-2",
        pid: "1",
      },
      {
        id: "1-3",
        label: "1-3",
        pid: "1",
      },
    ],
  },
  {
    id: "2",
    label: "北京",
    children: [
      {
        id: "2-1",
        label: "2-1",
        pid: "2",
      },
      {
        id: "2-2",
        label: "2-2",
        pid: "2",
      },
      {
        id: "2-3",
        label: "2-3",
        pid: "2",
      },
    ],
  },
  {
    id: "3",
    label: "上海",
    children: [
      {
        id: "3-1",
        label: "3-1",
        pid: "3",
      },
      {
        id: "3-2",
        label: "3-2",
        pid: "3",
      },
      {
        id: "3-3",
        label: "3-3",
        pid: "3",
      },
    ],
  },
];

export default {
  props: {
    //树全部数据
    cascadeData: {
      type: Array,
      default: () => mockTreeData,
    },
    titles: {
      type: Array,
      default: () => ["列表1", "列表2"],
    },
    //树是否默认展开
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    //右边数据
    value: { type: Array, default: () => [] },
    rightLoading: {
      type: Boolean,
      default: false,
    },
    leftLoading: {
      type: Boolean,
      default: false,
    },
  },
  layout: "login",
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
      },
      leftData: [],
      rightData: [],
      leftObj: {
        checkAll: false,
        isIndeterminate: false,
      },
      rightObj: { checkAll: false, isIndeterminate: false },
      leftSearchText: "",
      rightSearchText: "",
      leftCheckedNodes: [],
      rightCheckedNodes: [],
      leftChooseCount: 0,
      rightChooseCount: 0,
      rightFlattenData: [],
    };
  },
  mounted() {
    // this.leftData = this.cascadeData;
    // console.log("树组件加载", this.leftData);
    // this.rightData = this.value;
  },

  computed: {
    //左边所有节点数组
    leftFlattenData() {
      return this.flattenArray(this.leftData);
    },
    //右边所有节点数组
    // rightFlattenData() {
    //   return this.flattenArray(this.rightData);
    // },
    //左边所有子节点数量
    leftAllCount() {
      // console.log(
      //   this.getAllCount(this.leftData, this.leftFlattenData),
      //   "左边所有数量"
      // );
      return this.getAllCount(this.leftData, this.leftFlattenData);
    },
    //左边选择的子节点数量
    // leftChooseCount() {
    //   const checkedNodes = this.$refs.leftTree?.getCheckedNodes(false, true); // 包含半选
    //   console.log("checkedNodes", checkedNodes);
    //   return this.getAllCount(this.leftData, this.leftCheckedNodes);
    // },
    //右边所有子节点数量
    rightAllCount() {
      // console.log("右边所有子节点数量计算", this.rightFlattenData);
      return this.getAllCount(this.rightData, this.rightFlattenData);
    },
    //右边选择的子节点数量
    // rightChooseCount() {
    //   const checkedNodes = this.$refs.rightTree?.getCheckedNodes(false, true); // 包含半选
    //   return this.getAllCount(this.rightData, checkedNodes);
    // },
    // rightData() {
    //   console.log("----", this.value);
    //   return this.value;
    // },
  },
  watch: {
    cascadeData: {
      handler(val) {
        this.leftData = val;
        console.log("树组件加载", val);
      },
      immediate: true,
    },
    leftCheckedNodes(val) {
      // console.log("leftCheckedNodes变化", val);
      this.leftChooseCount = this.getAllCount(this.leftData, val);
    },
    rightCheckedNodes(val) {
      this.rightChooseCount = this.getAllCount(this.rightData, val);
    },
    leftSearchText(val) {
      this.$refs.leftTree.filter(val);
    },
    rightSearchText(val) {
      this.$refs.rightTree.filter(val);
    },
    value: {
      handler(val) {
        this.rightData = val;
      },
      immediate: true,
      deep: true,
    },
    rightData: {
      handler(val) {
        // console.log(val);
        this.rightFlattenData = this.flattenArray(val);
      },
      deep: true,
    },
  },
  methods: {
    //父组件调用：打开弹窗时计算左侧树默认值 搜索框清空
    getDefaultLeftData(array) {
      this.leftSearchText = "";
      this.rightSearchText = "";
      this.leftChooseCount = 0;
      this.rightChooseCount = 0;
      this.leftObj = { checkAll: false, isIndeterminate: false };
      this.rightObj = { checkAll: false, isIndeterminate: false };
      if (array && array.length > 0) {
        this.leftData = JSON.parse(JSON.stringify(array));
        return;
      }
      // console.log("getDefaultLeftData", this.cascadeData, this.rightData);
      // 深拷贝cascadeData和rightData
      let arr = JSON.parse(JSON.stringify(this.cascadeData));
      let check = JSON.parse(JSON.stringify(this.rightData));
      // 遍历cascadeData和rightData，对数据进行比对和处理
      arr?.forEach((item1) => {
        const correspondingItem = check?.find((item2) => item1.id === item2.id);
        if (correspondingItem) {
          item1.children = item1.children?.map((child1) => {
            const correspondingChild = correspondingItem.children.find(
              (child2) => child1.id === child2.id
            );
            if (correspondingChild) {
              child1.children = child1.children?.filter((subChild1) => {
                return !correspondingChild.children?.some(
                  (subChild2) => subChild1.id === subChild2.id
                );
              });
            }
            return child1;
          });
          if (item1.hasOwnProperty("children")) {
            item1.children = item1.children?.filter(
              (child) =>
                !child.hasOwnProperty("children") || child.children?.length > 0
            ); // 过滤子节点为空的情况
          }
        }
        return item1;
      });

      // 过滤父节点下子节点为空的情况
      // arr = arr.map((x) => {
      //   if (x.children?.length > 0) {
      //     return x;
      //   }
      // });
      let arr1 = [];
      arr.map((x) => {
        if (x.children?.length > 0 || !x.hasOwnProperty("children")) {
          arr1.push(x);
        }
      });
      // console.log("arr", arr, arr1);
      this.leftData = JSON.parse(JSON.stringify(arr1));

      // console.log("leftData", this.cascadeData, this.leftData);
    },
    filterNode(value, data) {
      // console.log(value, data, "---filter");
      if (!value) return true;
      //如果是子节点且查不到子节点本身
      // if (
      //   !data.label.includes(value) &&
      //   data.hasOwnProperty("pid") &&
      //   (data.pid != "-0" || data.pid != "0")
      // ) {
      //   //如果父节点包含value，返回所有子节点
      //   return data.parentName?.includes(value);
      // }
      // return data.label.includes(value);
      return data.label.indexOf(value) !== -1;
    },
    // filterNode(value, node, data) {
    //   if (!value) return true;

    //   // 检查当前节点及其子节点是否包含搜索值
    //   function checkInclude(currentData) {
    //     // 如果当前节点符合搜索条件，返回true
    //     if (currentData.label.includes(value)) {
    //       return true;
    //     }
    //     // 如果当前节点有子节点，递归检查子节点
    //     if (currentData.children && currentData.children.length > 0) {
    //       return currentData.children?.some((child) => checkInclude(child));
    //     }
    //     return false;
    //   }

    //   // 检查当前节点的父路径是否应该被包括
    //   const pathIncluded = data.pathNodes?.some((pathNode) =>
    //     pathNode.label.includes(value)
    //   );

    //   // 如果当前节点或其父路径节点包含搜索值，或者其子节点包含搜索值，则返回true
    //   return checkInclude(node) || pathIncluded;
    // },
    getAllCount(data, flatten) {
      // console.log("getAllCount", flatten);
      const arr = flatten?.filter((item) => !item.hasOwnProperty("children"));
      // console.log("arr======", arr);
      return arr.length;
    },
    handleCheck(data, checked, direction) {
      // console.log("check", checked);
      const checkedLength = checked.checkedNodes.length;
      if (direction === "left") {
        this.leftCheckedNodes = checked.checkedNodes;
        this.leftObj.checkAll = this.leftFlattenData.length === checkedLength;
        this.leftObj.isIndeterminate =
          checkedLength > 0 && checkedLength < this.leftFlattenData.length;
      } else {
        this.rightCheckedNodes = checked.checkedNodes;
        this.rightObj.checkAll = this.rightFlattenData.length === checkedLength;
        this.rightObj.isIndeterminate =
          checkedLength > 0 && checkedLength < this.rightFlattenData.length;
      }
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    checkboxChange(checkAll, dataName) {
      this[dataName + "Obj"].isIndeterminate = false;
      if (!checkAll) {
        this[dataName + "ChooseCount"] = 0;
        this.$refs[dataName + "Tree"].setCheckedKeys([]);
      } else {
        this[dataName + "ChooseCount"] = this[dataName + "AllCount"];
        this.$refs[dataName + "Tree"].setCheckedNodes(
          this[dataName + "Data"]?.filter((item) => item.disabled !== true)
        );
      }
    },
    // 点击向右穿梭
    towardsRight() {
      // (leafOnly, includeHalfChecked) 接收两个 boolean 类型的参数，
      // 1. 是否只是叶子节点，默认值为 false 2. 是否包含半选节点，默认值为 false
      const checkedNodes = this.$refs.leftTree.getCheckedNodes(false, true); // 包含半选
      const checkedKeys = this.$refs.leftTree.getCheckedKeys(false);
      const copyNodes = JSON.parse(JSON.stringify(checkedNodes));
      // console.log("checkedNodes", checkedNodes);
      copyNodes.forEach((x) => {
        if (x.hasOwnProperty("children")) {
          x.children = [];
        }
        // console.log("rightFlattenData", this.rightFlattenData, x);
        if (!this.rightFlattenData.some((i) => i.id === x.id)) {
          // console.log("进入判断", x);
          if (x.pid == "-0" || x.pid == "0") {
            this.$refs.rightTree.append(x);
          } else {
            this.$refs.rightTree.append(x, x.pid);
          }
          // console.log(this.$refs.rightTree);
        }
      });

      checkedKeys.forEach((x) => {
        this.$refs.leftTree.remove(x);
      });
      this.afterToward();
      this.leftCheckedNodes = [];
      // const a = this.flattenArray(this.rightData);
      // console.log(a, this.getAllCount({}, a), "---");
    },
    // 点击向左穿梭
    towardsLeft() {
      const checkedNodes = this.$refs.rightTree.getCheckedNodes(false, true); // 包含半选
      const checkedKeys = this.$refs.rightTree.getCheckedKeys(false);
      const copyNodes = JSON.parse(JSON.stringify(checkedNodes));
      copyNodes.forEach((x) => {
        if (x.hasOwnProperty("children")) {
          x.children = [];
        }
        // console.log("leftFlattenData", this.leftFlattenData, x);
        if (!this.leftFlattenData.some((i) => i.id === x.id)) {
          // console.log("进入判断", x);
          if (x.pid == "-0" || x.pid == "0") {
            this.$refs.leftTree.append(x);
          } else {
            this.$refs.leftTree.append(x, x.pid);
          }
        }
      });

      checkedKeys.forEach((x) => {
        this.$refs.rightTree.remove(x);
      });

      this.afterToward();
      this.rightCheckedNodes = [];
    },

    // 数据穿梭后
    afterToward() {
      this.$refs.leftTree.setCheckedKeys([]);
      this.$refs.rightTree.setCheckedKeys([]);
      this.leftObj.checkAll = false;
      this.leftObj.isIndeterminate = false;
      this.rightObj.checkAll = false;
      this.rightObj.isIndeterminate = false;
      this.leftChooseCount = 0;
      this.rightChooseCount = 0;
      this.$emit("input", this.rightData);
    },
  },
};
</script>
<style lang="less" scoped>
.el-transfer {
  display: flex;
  width: 100%;
  align-items: center;
  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}
.el-transfer-panel__header {
  display: flex;
  justify-content: space-between;
}
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
  font-size: 15px;
}
.el-tree {
  height: 100%;
  overflow: auto;
  margin-top: 10px;
}
.el-transfer-panel__body {
  height: 280px;
  display: flex;
  flex-direction: column;
  padding: 10px;
}
.count-float-right {
  color: #909399;
  font-size: 12px;
  margin-right: 10px;
}
</style>
