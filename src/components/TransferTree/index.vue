<template>
  <div class="el-transfer flex-space-between-center padding-0-20">
    <div class="el-transfer-panel">
      <p class="el-transfer-panel__header">
        <el-checkbox
          v-model="leftObj.checkAll"
          :indeterminate="leftObj.isIndeterminate"
          @change="(val) => checkboxChange(val, 'left')"
          >{{ titles[0] }}</el-checkbox
        >
        <span class="count-float-right"
          >{{ leftChooseCount }}/{{ leftAllCount }}</span
        >
      </p>
      <div class="el-transfer-panel__body">
        <!-- <div v-if="!leftObj.treeData.length" class="el-transfer-panel__empty">
          无数据
        </div> -->
        <el-input
          prefix-icon="el-icon-search"
          v-model="leftSearchText"
          clearable
          placeholder="请输入搜索内容"
        ></el-input>
        <el-tree
          v-loading="leftLoading"
          ref="leftTree"
          :data="leftData"
          :props="defaultProps"
          show-checkbox
          node-key="id"
          check-on-click-node
          @check="
            (data, checked) => {
              handleCheck(data, checked, 'left');
            }
          "
          :filter-node-method="filterNode"
          :default-expand-all="defaultExpandAll"
          :default-checked-keys="leftCheckedKey"
        />
      </div>
    </div>
    <div class="el-transfer__buttons">
      <el-button
        type="primary"
        icon="el-icon-arrow-left"
        @click="towardsLeft"
      />
      <el-button
        type="primary"
        icon="el-icon-arrow-right"
        @click="towardsRight"
      />
    </div>
    <div class="el-transfer-panel">
      <p class="el-transfer-panel__header">
        <el-checkbox
          v-model="rightObj.checkAll"
          :indeterminate="rightObj.isIndeterminate"
          @change="(val) => checkboxChange(val, 'right')"
          >{{ titles[1] }}</el-checkbox
        >
        <span class="count-float-right"
          >{{ rightChooseCount }}/{{ rightAllCount }}</span
        >
      </p>
      <div class="el-transfer-panel__body">
        <!-- <div v-if="!rightObj.treeData.length" class="el-transfer-panel__empty">
          无数据
        </div> -->
        <el-input
          prefix-icon="el-icon-search"
          v-model="rightSearchText"
          clearable
          placeholder="请输入搜索内容"
        ></el-input>
        <el-tree
          v-loading="rightLoading"
          ref="rightTree"
          :data="rightData"
          :props="defaultProps"
          show-checkbox
          node-key="id"
          check-on-click-node
          @check="
            (data, checked) => {
              handleCheck(data, checked, 'right');
            }
          "
          :filter-node-method="filterNode"
          :default-expand-all="defaultExpandAll"
        />
      </div>
    </div>
  </div>
</template>

<script>
const mockTreeData = [
  {
    id: "1",
    label: "无锡",
    children: [
      {
        id: "1-1",
        label: "1-1",
        pid: "1",
      },
      {
        id: "1-2",
        label: "1-2",
        pid: "1",
      },
      {
        id: "1-3",
        label: "1-3",
        pid: "1",
      },
    ],
  },
  {
    id: "2",
    label: "北京",
    children: [
      {
        id: "2-1",
        label: "2-1",
        pid: "2",
      },
      {
        id: "2-2",
        label: "2-2",
        pid: "2",
      },
      {
        id: "2-3",
        label: "2-3",
        pid: "2",
      },
    ],
  },
  {
    id: "3",
    label: "上海",
    children: [
      {
        id: "3-1",
        label: "3-1",
        pid: "3",
      },
      {
        id: "3-2",
        label: "3-2",
        pid: "3",
      },
      {
        id: "3-3",
        label: "3-3",
        pid: "3",
      },
    ],
  },
];

export default {
  props: {
    leftCheckedKey: {
      type: Array,
      default: () => [],
    },
    //树全部数据
    cascadeData: {
      type: Array,
      default: () => mockTreeData,
    },
    titles: {
      type: Array,
      default: () => ["列表1", "列表2"],
    },
    //树是否默认展开
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    //右边数据
    value: { type: Array, default: () => [] },

    rightLoading: {
      type: Boolean,
      default: false,
    },
    leftLoading: {
      type: Boolean,
      default: false,
    },
  }, // 班级的树形结构数据
  layout: "login",
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
      },
      leftData: [],
      rightData: [],
      leftObj: {
        checkAll: false,
        isIndeterminate: false,
      },
      rightObj: { checkAll: false, isIndeterminate: false },
      leftSearchText: "",
      rightSearchText: "",
      leftCheckedNodes: [],
      rightCheckedNodes: [],
      leftChooseCount: 0,
      rightChooseCount: 0,
    };
  },
  mounted() {
    console.log("树组件加载");
    // this.leftData = this.cascadeData;
    // this.rightData = this.value;
  },
  computed: {
    //左边所有节点数组
    leftFlattenData() {
      return this.flattenArray(this.leftData);
    },
    //右边所有节点数组
    rightFlattenData() {
      return this.flattenArray(this.rightData);
    },
    //左边所有子节点数量
    leftAllCount() {
      return this.getAllCount(this.leftData, this.leftFlattenData);
    },
    //左边选择的子节点数量
    // leftChooseCount() {
    //   const checkedNodes = this.$refs.leftTree?.getCheckedNodes(false, true); // 包含半选
    //   console.log("checkedNodes", checkedNodes);
    //   return this.getAllCount(this.leftData, this.leftCheckedNodes);
    // },
    //右边所有子节点数量
    rightAllCount() {
      return this.getAllCount(this.rightData, this.rightFlattenData);
    },
    //右边选择的子节点数量
    // rightChooseCount() {
    //   const checkedNodes = this.$refs.rightTree?.getCheckedNodes(false, true); // 包含半选
    //   return this.getAllCount(this.rightData, checkedNodes);
    // },
    // rightData() {
    //   console.log("----", this.value);
    //   return this.value;
    // },
  },
  watch: {
    leftCheckedNodes(val) {
      this.leftChooseCount = this.getAllCount(this.leftData, val);
    },
    rightCheckedNodes(val) {
      this.rightChooseCount = this.getAllCount(this.rightData, val);
    },
    leftSearchText(val) {
      this.$refs.leftTree.filter(val);
    },
    rightSearchText(val) {
      this.$refs.rightTree.filter(val);
    },
    value: {
      handler(val) {
        this.rightData = val;
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    //父组件调用：打开弹窗时计算左侧树默认值 搜索框清空
    getDefaultLeftData(array) {
      this.leftSearchText = "";
      this.rightSearchText = "";
      this.leftChooseCount = 0;
      this.rightChooseCount = 0;
      this.leftObj = { checkAll: false, isIndeterminate: false };
      this.rightObj = { checkAll: false, isIndeterminate: false };
      if (array && array.length > 0) {
        this.leftData = JSON.parse(JSON.stringify(array));
        return;
      }
      let arr = JSON.parse(JSON.stringify(this.cascadeData));
      let check = JSON.parse(JSON.stringify(this.rightData));
      arr.forEach((item1) => {
        const correspondingItem = check?.find((item2) => item1.id == item2.id);
        if (correspondingItem) {
          item1.children = item1.children.filter((child1) => {
            return !correspondingItem.children.some(
              (child2) => child1.id == child2.id
            );
          });
        }
      });
      let arr1 = [];
      arr.map((x) => {
        if (x.children?.length >= 0) {
          arr1.push(x);
        }
      });
      this.leftData = JSON.parse(JSON.stringify(arr1));
      console.log(this.leftData, "======");
    },
    filterNode(value, data) {
      if (!value) return true;
      //如果是子节点且查不到子节点本身
      if (!data.label.includes(value) && data.hasOwnProperty("pid")) {
        //如果父节点包含value，返回所有子节点
        return (data?.plabel || data.label).includes(value);
      }
      return data.label.includes(value);
    },
    getAllCount(data, flatten) {
      //选中的数量为子节点，去除父节点个数
      const parentIds = data?.map((x) => x.id);
      const arr = [];
      flatten?.map((x) => {
        if (!parentIds.includes(x.id)) {
          arr.push(x);
        }
      });
      return arr.length;
    },
    handleCheck(data, checked, direction) {
      console.log(checked, "handleCheck");
      const checkedLength = checked.checkedNodes.length;
      if (direction === "left") {
        this.leftCheckedNodes = checked.checkedNodes;
        this.leftObj.checkAll = this.leftFlattenData.length === checkedLength;
        this.leftObj.isIndeterminate =
          checkedLength > 0 && checkedLength < this.leftFlattenData.length;
      } else {
        this.rightCheckedNodes = checked.checkedNodes;
        this.rightObj.checkAll = this.rightFlattenData.length === checkedLength;
        this.rightObj.isIndeterminate =
          checkedLength > 0 && checkedLength < this.rightFlattenData.length;
      }
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    checkboxChange(checkAll, dataName) {
      this[dataName + "Obj"].isIndeterminate = false;
      if (!checkAll) {
        this[dataName + "ChooseCount"] = 0;
        this.$refs[dataName + "Tree"].setCheckedKeys([]);
      } else {
        this[dataName + "ChooseCount"] = this[dataName + "AllCount"];
        this.$refs[dataName + "Tree"].setCheckedNodes(this[dataName + "Data"]);
      }
    },
    // 点击向右穿梭
    towardsRight() {
      // (leafOnly, includeHalfChecked) 接收两个 boolean 类型的参数，
      // 1. 是否只是叶子节点，默认值为 false 2. 是否包含半选节点，默认值为 false
      const checkedNodes = this.$refs.leftTree.getCheckedNodes(false, true); // 包含半选
      const checkedKeys = this.$refs.leftTree.getCheckedKeys(false);
      const copyNodes = JSON.parse(JSON.stringify(checkedNodes));
      copyNodes.forEach((x) => {
        x.children = [];
        if (!this.rightFlattenData.some((i) => i.id === x.id)) {
          this.$refs.rightTree.append(x, x.pid);
        }
      });

      checkedKeys.forEach((x) => {
        this.$refs.leftTree.remove(x);
      });
      this.afterToward();
      this.leftCheckedNodes = [];
    },
    // 点击向左穿梭
    towardsLeft() {
      const checkedNodes = this.$refs.rightTree.getCheckedNodes(false, true); // 包含半选
      const checkedKeys = this.$refs.rightTree.getCheckedKeys(false);
      const copyNodes = JSON.parse(JSON.stringify(checkedNodes));
      copyNodes.forEach((x) => {
        x.children = [];
        if (!this.leftFlattenData.some((i) => i.id === x.id)) {
          this.$refs.leftTree.append(x, x.pid);
        }
      });

      checkedKeys.forEach((x) => {
        this.$refs.rightTree.remove(x);
      });

      this.afterToward();
      this.rightCheckedNodes = [];
    },

    // 数据穿梭后
    afterToward() {
      this.$refs.leftTree.setCheckedKeys([]);
      this.$refs.rightTree.setCheckedKeys([]);
      this.leftObj.checkAll = false;
      this.leftObj.isIndeterminate = false;
      this.rightObj.checkAll = false;
      this.rightObj.isIndeterminate = false;
      this.leftChooseCount = 0;
      this.rightChooseCount = 0;
      this.$emit("input", this.rightData);
    },
    // 设置右侧数据
    setRightData() {
      this.$refs.leftTree.setCheckedKeys(this.leftCheckedKey);
      this.towardsRight();
    },
  },
};
</script>
<style lang="less" scoped>
.el-transfer {
  display: flex;
  width: 100%;
  align-items: center;
  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}
.el-transfer-panel__header {
  display: flex;
  justify-content: space-between;
}
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
  font-size: 15px;
}
.el-tree {
  height: 100%;
  overflow: auto;
  margin-top: 10px;
}
.el-transfer-panel__body {
  height: 280px;
  display: flex;
  flex-direction: column;
  padding: 10px;
}
.count-float-right {
  color: #909399;
  font-size: 12px;
  margin-right: 10px;
}
</style>
