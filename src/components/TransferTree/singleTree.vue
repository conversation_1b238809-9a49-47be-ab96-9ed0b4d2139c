<template>
  <div class="el-transfer-panel" v-loading="loading">
    <p class="el-transfer-panel__header">
      <el-checkbox
        v-model="leftObj.checkAll"
        :indeterminate="leftObj.isIndeterminate"
        @change="(val) => checkboxChange(val)"
        >{{ title }}</el-checkbox
      >
      <span class="count-float-right"
        >{{ leftChooseCount }}/{{ leftAllCount }}</span
      >
    </p>
    <div class="el-transfer-panel__body">
      <!-- <div v-if="!leftObj.treeData.length" class="el-transfer-panel__empty">
          无数据
        </div> -->
      <el-input
        prefix-icon="el-icon-search"
        v-model="leftSearchText"
        clearable
        placeholder="请输入搜索内容"
      ></el-input>
      <el-tree
        ref="leftTree"
        :data="list"
        show-checkbox
        @check="
          (data, checked) => {
            handleCheck(data, checked);
          }
        "
        :filter-node-method="filterNode"
        :default-expand-all="defaultExpandAll"
        :props="defaultProps"
        v-bind="$attrs"
        v-on="$listeners"
        check-on-click-node
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
    },
    title: {
      type: String,
      default: "列表",
    },
    list: {
      type: Array,
      default: () => [],
    },
    //树是否默认展开
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: "children",
          label: "label",
          id: "id",
        };
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      leftObj: {
        checkAll: false,
        isIndeterminate: false,
      },
      leftChooseCount: 0,
      leftSearchText: "",
      checkedKeys: [],
    };
  },
  computed: {
    //左边所有节点数组
    leftFlattenData() {
      return this.flattenArray(this.list);
    },
    leftAllCount() {
      return this.getAllCount(this.leftFlattenData);
    },
  },
  watch: {
    leftSearchText(val) {
      this.$refs.leftTree.filter(val);
    },
    value: {
      handler(val) {
        this.checkedKeys = val;
        this.leftChooseCount = this.checkedKeys.length;
        this.$refs.leftTree?.setCheckedKeys(val);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getAllCount(flatten) {
      const arr = flatten?.filter(
        (item) =>
          !item.hasOwnProperty("children") || item.children?.length === 0
      );
      return arr.length;
    },
    checkboxChange(checkAll) {
      this.leftObj.isIndeterminate = false;
      if (!checkAll) {
        this.leftChooseCount = 0;
        this.$refs.leftTree.setCheckedKeys([]);
        this.checkedKeys = [];
      } else {
        this.leftChooseCount = this.leftAllCount;
        this.$refs.leftTree.setCheckedNodes(
          this.list?.filter((item) => item.disabled !== true)
        );
        this.checkedKeys = this.$refs.leftTree.getCheckedKeys(true);
      }
      this.$emit("input", this.checkedKeys);
    },
    handleCheck(data, checked) {
      const checkedLength = checked.checkedNodes.length;
      this.checkedKeys = this.$refs.leftTree.getCheckedKeys(true);
      this.leftChooseCount = this.checkedKeys.length;
      this.leftObj.checkAll = this.leftFlattenData.length === checkedLength;
      this.leftObj.isIndeterminate =
        checkedLength > 0 && checkedLength < this.leftFlattenData.length;
      this.$emit("input", this.checkedKeys);
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label || "label"].indexOf(value) !== -1;
    },
  },
};
</script>

<style lang="less" scoped>
.el-transfer-panel {
  width: 100%;
}
.el-transfer-panel__header {
  display: flex;
  justify-content: space-between;
}
.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
  font-size: 15px;
}
.el-tree {
  height: 100%;
  overflow: auto;
  margin-top: 10px;
}
.el-transfer-panel__body {
  height: 70vh;
  display: flex;
  flex-direction: column;
  padding: 10px;
}
.count-float-right {
  color: #909399;
  font-size: 12px;
  margin-right: 10px;
}
</style>
