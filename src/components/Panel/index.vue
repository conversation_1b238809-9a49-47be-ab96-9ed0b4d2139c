<!-- @format -->

<template>
  <div class="panel-wrap">
    <div :class="['panel', classType]">
      <div class="panel-header" v-if="$slots.left || $slots.right">
        <div class="panel-header-left">
          <slot name="left" />
        </div>
        <div class="panel-header-right">
          <slot name="right" />
        </div>
      </div>
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    classType: {
      type: String
    }
  }
}
</script>

<style lang="less" scoped>
.panel-wrap {
  box-sizing: border-box;
  margin-bottom: 16px;
}
.panel-header {
  margin-bottom: 10px;
  min-height: 28px;
  display: flex;
  .panel-header-left {
    flex: 1;
  }
}
.panel {
  height: 100%;
  width: 100%;
  background: #fff;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.06);
  border-radius: 2px;
  overflow-x: hidden;
  &.table-wrap {
    padding: 10px 16px 16px;
  }
}
</style>
