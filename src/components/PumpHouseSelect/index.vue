<!-- 泵房select -->
<template>
  <div class="">
    <el-select
      v-model="pumpHouseName"
      filterable
      remote
      reserve-keyword
      placeholder="请输入关键词"
      :remote-method="remoteMethod"
      :loading="loading"
      :disabled="disabled"
    >
      <el-option
        v-for="item in options"
        :key="item.pumpHouseId"
        :label="item.pumpHouseName"
        :value="item.pumpHouseId"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { pumpHouseByPage } from "@/api/satetyMonitor/listMode.js";

export default {
  components: {},
  props: {
    value: {
      type: String,
      default: () => {
        return ''
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      options: [],
      pumpHouseName: "",
      list: [],
      loading: false,
      states: [],
    };
  },
  computed: {},
  watch: {
    pumpHouseName(newVal) {
      if (newVal) {
        this.$emit("change", newVal)
      }
    },
    value(newVal) {
      this.pumpHouseName = newVal
    }
  },
  methods: {
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        let params = {
          pumpHouseName: query,
          pageNum: 1,
          pageSize: 20
        }
        pumpHouseByPage(params).then(res => {
          this.loading = false
          this.options = res.data
        })
        .catch(() => {
          this.loading = false
        })
      } else {
        this.options = [];
      }
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped></style>
