<template>
  <div>
    <el-descriptions
      class="descriptions"
      :column="column"
      border
      v-if="showList && showList.length > 0"
    >
      <el-descriptions-item
        v-for="(item, index) in showList"
        :key="index"
        :label="item.title"
        :label-style="labelStyle || commonStyle"
        :content-style="contentStyle || commonStyle"
      >
        <slot
          :name="item.slotName"
          v-if="item.slotName"
          :itemVal="item.value"
          :row="item"
        ></slot>
        <el-button type="text" @click="item.method" v-else-if="item.method">{{
          item.value
        }}</el-button>
        <!-- <el-tooltip :content="item.value" placement="top-start" v-else> -->
        <span v-else
          >{{ item.value
          }}<i
            v-if="item.copy"
            class="el-icon-document-copy pointer-icon"
            @click="copyToClipboard(item.value)"
          ></i
        ></span>
        <!-- </el-tooltip> -->
      </el-descriptions-item>
    </el-descriptions>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
import { copyToClipboard } from "@/utils/index.js";

export default {
  props: {
    column: {
      type: Number,
      default: 2,
    },
    // [{title:'描述项',value:'描述值',method:()=>{},slotName:''}]
    list: {
      type: Array,
      default: () => [],
    },
    // label样式 默认为commonStyle（label和content宽度平均）
    labelStyle: {
      type: String,
      default: "",
    },
    contentStyle: {
      type: String,
      default: "",
    },
  },
  computed: {
    showList() {
      return this.list?.filter((x) => !x.hidden);
    },
    commonStyle() {
      const customWidth = 100 / (this.column * 2) + "%";
      return `width:${customWidth}`;
    },
  },
  methods: { copyToClipboard },
};
</script>

<style lang="less" scoped>
.descriptions {
  // margin: 0 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    // max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
    // width: 25%;
  }
  .el-descriptions-item__label {
    // width: 25%;
  }
}

/deep/ .el-statistic .head {
  margin-bottom: 12px;
}
</style>
