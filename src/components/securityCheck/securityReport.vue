<!-- 用户安检报告 -->
<template>
  <div>
    <div class="app-container" id="pdfDom">
      <div>
        <p class="title">
          <span>用户安检报告</span>
        </p>
        <div class="span-box">
          <span class="span1">姓名</span>
          <span class="span2" style="overflow-x: auto;white-space: nowrap;scrollbar-width: thin;">{{ userInfo.consName }}</span>
          <span class="span3">联系电话</span>
          <span class="span4">{{ userInfo.mobile }}</span>
          <span class="span5">用户地址</span>
          <span class="span6" style="border-right: 1px solid #ccc;">{{ userInfo.addr }}</span>
        </div>
        <div class="span-box">
          <span class="span1">所属小区</span>
          <span class="span8"  style="border-right: 1px solid #ccc;">{{ userInfo.areaName }}</span>
          <!--        <span class="span3">联系电话</span>-->
          <!--        <span class="span4">133333333333</span>-->
          <!--        <span class="span5">用户地址</span>-->
          <!--        <span class="span6">23栋2209</span>-->
        </div>
        <p class="title">
          <span>户内燃气设施安全检查内容及标准</span>
        </p>
        <div class="flex-title">
          <div class="title1">安检项目</div>
          <div class="title2">安检内容</div>
          <div class="title3">安检结果</div>
          <div class="title4">整改建议</div>
          <div class="title6">备注</div>
          <div class="title5">照片</div>
        </div>

        <div class="flex-content">
          <div
            class="content-box"
            v-for="(list, key, index1) in itemList"
            :key="index1"
          >
            <div class="content-span1-new maxHeight">{{ list.projectName }}</div>
            <div class="content-span-center-new">
              <span v-for="(item, index) in list.detailList" :key="index" style="display:flex;">
                <span class="content-span2-new maxHeight ">{{ item.sdName }}</span>
                <span class="content-span3-new maxHeight">{{ item.srName }}</span>
                <span class="content-span3-new maxHeight">{{ item.rectifyRemark }}</span>
                <span class="content-span3-new maxHeight">{{ item.remark }}</span>
              </span>
            </div>
            <div class="content-span-right-new" :style="{'height': (list.detailList.length * 6).toString() + 'rem'}">
              <span v-for="(url,index2) in list.imgPath" :key="index2" class="imgstyle">
                <el-image
                  v-if="url != 'null'"
                  :src="imgBasUrl + url"
                  :preview-src-list="[imgBasUrl + url]">
                </el-image>
              </span>
            </div>
          </div>
        </div>

        <p class="title" style="border-top: 1px solid #ccc">
          <span>安检项目结果</span>
        </p>

        <div class="span-box">
          <span class="span1" style="overflow-x: auto;white-space: nowrap;scrollbar-width: thin;">安检结果</span>
          <span class="span2">{{ results.result }}</span>
          <span class="span3">安检员</span>
          <span class="span4">{{ results.checkerName }}</span>
          <span class="span5">安检时间</span>
          <span class="span6" style="border-right: 1px solid #ccc;">{{ this.moment(results.date).format("YYYY-MM-DD")}}</span>
        </div>
        <div class="span-box" style="border-bottom: 1px solid #ccc;">
          <span class="span10">备注</span>
          <span class="span8" style="border-right: 1px solid #ccc;">{{ results.remark }}</span>
        </div>

        <p class="title" style="border-top:none;">
          <span>信息采集</span>
        </p>

        <div v-for="(item, index) in infolist" :key="index">
          <div class="span-box">
            <span class="span1">名称</span>
            <span class="span2">{{nameFormat(item.name)}}</span>
            <span class="span3">
              <span class="type-left">型号</span>
              <span class="type-right">{{item.model}}</span>
            </span>
            <span class="span3">
              <span class="spanhalf1">产品现状</span>
              <span class="spanhalf2">{{productStatusFormat(item.productStatus)}}</span>
            </span>
            <span class="span5">
              <span class="spanhalf1">购买/出厂日期</span>
              <span class="spanhalf2">{{item.factoryDate}}</span>
            </span>
            <span class="span5">
              <span class="spanhalf1">使用能源</span>
              <span class="spanhalf2">{{useEnergyFormat(item.useEnergy)}}</span>
            </span>
          </div>
          <div class="span-box">
            <span class="span1">厨房器具品牌</span>
            <span class="span2">{{productBrandFormat(item.productBrand)}}</span>
            <span class="span3">
              <span class="type-left">排气方式</span>
              <span class="type-right">{{exhaustTypeFormat(item.exhaustType)}}</span>
            </span>
            <span class="span3">
              <span class="spanhalf1">安装位置</span>
              <span class="spanhalf2">{{installPlaceFormat(item.installPlace)}}</span>
            </span>
            <span class="span5">
              <span class="spanhalf1">安装放置方式</span>
              <span class="spanhalf2">{{installPlaceTypeFormat(item.installPlaceType)}}</span>
            </span>
            <span class="span5">
              <span class="spanhalf1">产品预计更换时间</span>
              <span class="spanhalf2">{{item.productReplaceDate}}</span>
            </span>
          </div>
          <div class="span-box borderbottom borderright">
            <span class="span10">备注</span>
            <span class="span8">{{item.remark}}</span>
          </div>
        </div>

        <p class="title" style="border-top: 1px solid #ccc">
          <span>安检照片</span>
        </p>

        <div class="photo-box">
          <img v-for=" (item, index) in pictures" :key="index" :id="'imgs_'+index" :src="item" class="check-photo"/>
        </div>

      </div>
    </div>
    <button type="button" class="btn btn-primary" style="margin-top:10px;" @click="initReq(); getPdf()">
      导出PDF
    </button>
  </div>
</template>
<script>
import {queryTaskUserInfoReport} from "@/api/securityCheck/ecPremise.js";
import moment from "moment";
export default {
  //import引入的组件需要注入到对象中才能使用
  props: ["spCode", "consNo"],
  components: {},
  data() {
    return {
      imgBasUrl: process.env.VUE_APP_OSS_URL,
      any:'use-credentials',
      moment:moment,
      htmlTitle: "安检报告",
      //安检项目
      itemList: [],
      //用户基本信息
      userInfo: {},
      //安检结果
      results: {},
      queryTaskUserInfoReport: queryTaskUserInfoReport,
      resultOptions: [],
      pictures: [],
      //信息采集
      infolist: [],
      nameOptions: [],
      prodectStatusOptions: [],
      useEnergyOptions: [],
      productBrandOptions: [],
      exhaustTypeOptions: [],
      installPlaceTypeOptions: [],
      installPlaceOptions: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getDicts('sc_check_result').then((response) => {
        this.resultOptions = response.data
    })
    this.getDicts('sc_info_collect_name').then((response) => {
        this.nameOptions = response.data
    })
    this.getDicts('SC_INFO_COLLECT_PRODUCT_STATUS').then((response) => {
        this.prodectStatusOptions = response.data
    })
    this.getDicts('sc_info_collect_use_energy').then((response) => {
        this.useEnergyOptions = response.data
    })
    this.getDicts('sc_info_collect_product_brand').then((response) => {
        this.productBrandOptions = response.data
    })
    this.getDicts('sc_info_collect_exhaust_type').then((response) => {
        this.exhaustTypeOptions = response.data
    })
    this.getDicts('sc_info_collect_install_place_type').then((response) => {
        this.installPlaceTypeOptions = response.data
    })
    this.getDicts('sc_info_collect_install_place').then((response) => {
        this.installPlaceOptions = response.data
    })
    this.initPage();
  },
  methods: {
    initReq(){
      //强制跳到页面顶部,解决导出pdf时页面不全的问题
      window.pageYOffset = 0;
      document.documentElement.scrollTop = 0
      document.body.scrollTop = 0;
    },
    initPage() {
      let query = {
        spCode: this.spCode,
        consNo: this.consNo,
      };
      if(query.spCode != null && query.spCode != undefined){
        this.queryTaskUserInfoReport(query).then((res) => {
          if(res.data.userInfo){
            this.userInfo = res.data.userInfo;
          }
          if(res.data.results){
            this.results = res.data.results;
            /**安检结果 */
            this.results.result = this.selectDictLabel(this.resultOptions, this.results.result)
          }
          if(res.data.securityItems){
            this.itemList = res.data.securityItems;
          }
          /**信息采集 */
          if(res.data.collectList){
            this.infolist = res.data.collectList
          }
          /**安检照片 */
          if(res.data.photoList){
            var array = new Array();
            if(res.data.photoList){
               res.data.photoList.forEach((val,index)=>{
                  array[index] = this.imgBasUrl + val
              })
            }
            if(array.length > 16){
                this.pictures = array.slice(0,16);
            }else{
                this.pictures = array;
            }
          }
        });
      }
    },
    //信息采集 -- 名称
    nameFormat(val){
      return this.selectDictLabel(this.nameOptions, val)
    },
    //信息采集 -- 产品现状
    productStatusFormat(val){
      return this.selectDictLabel(this.prodectStatusOptions, val)
    },
    //信息采集 -- 厨房器具品牌
    productBrandFormat(val){
      return this.selectDictLabel(this.productBrandOptions, val)
    },
    //信息采集 -- 排气方式
    exhaustTypeFormat(val){
      return this.selectDictLabel(this.exhaustTypeOptions, val)
    },
    //信息采集 -- 使用能源
    useEnergyFormat(val){
      return this.selectDictLabel(this.useEnergyOptions, val)
    },
    //信息采集 -- 安装放置方式
    installPlaceTypeFormat(val){
      return this.selectDictLabel(this.installPlaceTypeOptions, val)
    },
    //信息采集 -- 安装位置
    installPlaceFormat(val){
      return this.selectDictLabel(this.installPlaceOptions, val)
    },

  },
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
.app-container {
  font-size: 12px;
}
p {
  margin: 0;
  padding: 0;
}
.title {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  text-align: center;
  height: 2rem;
  line-height: 2rem;
}
.span-box {
  // width: 100%;
  // height: 2rem;
  // line-height: 2rem;
  display: flex;
  border-top: 1px solid #ccc;
}
.span1 {
  display: inline-block;
  width: 10%;
  border-right: 1px solid #ccc;
  border-left: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span2 {
  display: inline-block;
  width: 6%;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.img-check {
  width: 100%;
  height: 1rem;
}
.span3 {
  display: inline-block;
  width: 29%;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span4 {
  display: inline-block;
  width: 15%;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span5 {
  display: inline-block;
  width: 20%;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span6 {
  display: inline-block;
  width: 30%;
  // border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span7 {
  display: inline-block;
  width: 55%;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span8 {
  display: inline-block;
  width: 90%;
  // border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span9 {
  display: inline-block;
  width: 15%;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.span10{
  display: inline-block;
  width: 9%;
  border-right: 1px solid #ccc;
  border-left: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.type-left{
  width: 18%;
  display: inline-block;
  border-right: 1px solid #ccc;
  // height: 2rem;
  line-height: 2rem;
}
.type-right{
  width: 80%;
  display: inline-block;
  // height: 2rem;
  line-height: 2rem;
}
.spanhalf1 {
  width: 40%;
  display: inline-block;
  border-right: 1px solid #ccc;
  // height: 2rem;
  line-height: 2rem;
}
.spanhalf2 {
  width: 56%;
  display: inline-block;
  // height: 2rem;
  line-height: 2rem;
}

.borderbottom {
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.borderright {
  border-right: 1px solid #ccc;
}

.content-box {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.content-span1 {
  display: inline-block;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  width: 20%;
  height: 2rem;
  line-height: 2rem;
}
.content-span1-new{
  display: flex;
  align-items: center;
  width: 16.65%;
  height: 2rem;
  line-height: 2rem;
}
.content-span2 {
  display: inline-block;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  width: 30%;
  height: 2rem;
  line-height: 2rem;
}
.content-span2-new {
  display: inline-block;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  width: 50%;
  height: 2rem;
  line-height: 2rem;
}
.content-span3 {
  display: inline-block;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  width: 20%;
  height: 2rem;
  line-height: 2rem;
}
.content-span3-new {
  display: inline-block;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  width: 50%;
  height: 2rem;
  line-height: 2rem;
}
.content-span4 {
  display: inline-block;
  border-right: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  width: 50%;
  height: 2rem;
  line-height: 2rem;
}

.content-span-right {
  display: inline-block;
  // border: 1px solid #ccc;
  width: 80%;
}
.content-span-center-new {
  display: inline-block;
  width: 66.6%;
}
.content-span-right-new {
  display: inline-block;
  width: 16.65%;
  overflow-y: auto;
}
.imgstyle {
  width: 20%;
  padding: 2px;
  display: inline-block;
  img{
    width: 100%;
    height: 100%;
  }
}
.flex-title {
  display: flex;
}
.title1 {
  border-left: 1px solid #ccc;
}
.title1,.title2,.title3,.title4,.title5,.title6 {
  width: 16.65%;
  border-top: 1px solid #ccc;
  border-right: 1px solid #ccc;
  height: 2rem;
  line-height: 2rem;
}
.flex-content {
  display: flex;
  flex-wrap: wrap;
  border-right: 1px solid #ccc;
}
// .content-box {
//   display: flex;
//   align-items: center;
//   border: 1px solid #ccc;
// }
.flex-box-imgs {
  display: inline-block;
  margin-left: 10px;
}
.photo-box{
  border: 1px solid #ccc;
}
.check-photo {
  width: 11%;
  height: 6rem;
  display: inline-block;
  margin-left: 12px;
}
.maxHeight {
  line-height: inherit !important;
  height: 6rem;
}
::-webkit-scrollbar {
width: 4px; /*对垂直流动条有效*/
height: 4px; /*对水平流动条有效*/
}
</style>
