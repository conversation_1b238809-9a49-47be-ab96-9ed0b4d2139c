<template>
  <div class="app-container" style="width: 100%">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <!-- <el-tab-pane label="用户管理" name="userManager">
        <div class="tableTitle">基本信息</div>
        <div class="queryParamsWrap">
          <el-form :inline="true" label-width="100px" :model="recentUserInfo">
            <el-row>
              <el-col :span="6">
                <el-form-item label="用户编号:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.consNo"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用户名称:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.consName"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="片区:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.districtName"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="所属小区:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.commName"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="手机号码:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.mobile"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用气性质:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.consType"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="证件号码:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.certificateNo"
                    :title="recentUserInfo.certificateNo"
                    class="input-over"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用户状态:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.status"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="客户类型:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.checkCustType"
                    :title="recentUserInfo.checkCustType"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.userRemark"
                    :title="recentUserInfo.userRemark"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用户地址:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.addr"
                    :title="recentUserInfo.addr"
                    class="input-over"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-tab-pane> -->
      <el-tab-pane label="安检信息" name="checkInfo">
        <div class="tableTitle">最近安检情况</div>
        <div class="queryParamsWrap">
          <el-form :inline="true" label-width="100px">
            <el-row>
              <!-- <el-col :span="6">
                <el-form-item label="安检员:" label-width="60px">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.checkersName"
                    :title="recentUserInfo.checkersName"
                    type="text"
                  ></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="安检日期:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.checkDate"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="安检客户类型:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.checkCustType"
                    :title="recentUserInfo.checkCustType"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="安检结果:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.result"
                    :title="recentUserInfo.result"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否存在隐患:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.dangerIsExit"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="隐患等级:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.dangerLevel"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="安检报告:">
                  <el-button
                    type="text"
                    @click="showReport(recentUserInfo.spCode)"
                    >报告.pdf</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注:">
                  <el-input
                    :disabled="true"
                    v-model="recentUserInfo.checkRemark"
                    :title="recentUserInfo.checkRemark"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="tableTitle">最近执行照片</div>
        <div>
          <el-row>
            <el-col :span="24">
              <div v-for="item in pictures" :key="item" class="flex-box-imgs">
                <el-image
                  style="width: 100px; height: 100px"
                  :src="item"
                  :preview-src-list="pictures"
                  :fit="item"
                >
                </el-image>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="tableTitle" style="margin-top: 20px">安检历史</div>
        <div>
          <el-row>
            <el-table :data="checkDataList" style="width: 100%" align="center">
              <el-table-column
                prop="spId"
                label="序号"
                width="100"
                align="center"
              >
                <template slot-scope="scope">
                  {{
                    (checkParams.pageNum - 1) * checkParams.pageSize +
                    scope.$index +
                    1
                  }}
                </template>
              </el-table-column>
              <el-table-column
                prop="checkDate"
                label="最近安检日期"
                align="center"
                :formatter="timeFormat"
              ></el-table-column>
              <el-table-column
                prop="checkersName"
                label="安检组"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="result"
                label="安检结果"
                align="center"
                :formatter="checkResultFormat"
              ></el-table-column>
              <el-table-column
                prop="dangerIsExit"
                label="是否存在隐患"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="custType"
                label="安检客户类型"
                align="center"
              ></el-table-column>
              <el-table-column label="报告" align="center" prop="thisRead">
                <template slot-scope="scope">
                  <el-button type="text" @click="showReport(scope.row.spCode)"
                    >报告.pdf</el-button
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="remark"
                label="备注"
                align="center"
              ></el-table-column>
            </el-table>
          </el-row>
          <el-row v-show="checkDataList.length > 0" type="flex" justify="end">
            <el-pagination
              @current-change="checkHandleCurrentChange"
              layout="total, prev, pager, next, jumper"
              :total="this.checkPageParam.total"
            >
            </el-pagination>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      :visible.sync="dialogPageVisible"
      v-if="dialogPageVisible"
      destroy-on-close
      custom-class="mydialog"
      append-to-body
      width="90%"
    >
      <securityReport
        :isComponent="true"
        :spCode="this.spCode"
        :cons-no="userInfo.consNo"
      ></securityReport>
    </el-dialog>
  </div>
</template>

<script>
import {
  // tableList,
  checkHistoryList,
  recentPhoto,
  findUserInfoAndCheckInfo,
} from "@/api/securityCheck/ecPremise.js";
import moment, { months, now } from "moment";
import securityReport from "./securityReport";
export default {
  props: ["userInfo"],
  components: {
    securityReport,
  },
  data() {
    return {
      activeName: "checkInfo",
      // dataList: [],
      checkDataList: [],
      tableParams: {
        pageNum: 1,
        pageSize: 10,
        consNo: this.userInfo.consNo,
      },
      tablePageParam: {
        total: 0,
      },
      checkParams: {
        pageNum: 1,
        pageSize: 10,
        consNo: this.userInfo.consNo,
      },
      checkPageParam: {
        total: 0,
      },
      //安检结果
      checkGroupOptions: [],
      dialogPageVisible: false,
      spCode: "",
      // inletPathOptions: [],
      pictures: [],
      statusOptions: [],
      consTypeOptions: [],
      dangerOptions: [],
      pqCalOptions: [],
      // isPqCal: true,
      recentUserInfo: {},
    };
  },
  methods: {
    //tab点击事件
    handleClick(tab, event) {

    },
    //获取照片
    getRecentPhoto(consNo, srdCode) {
      let parms = {
        consNo: consNo,
        srdCode: srdCode,
      };
      recentPhoto(parms).then((res) => {
        var array = new Array();
        if (res.data) {
          res.data.forEach((val, index) => {
            array[index] = process.env.VUE_APP_OSS_URL + val;
          });
        }
        this.pictures = array;
        // if(array.length > 8){
        //     this.pictures = array.slice(0,8);
        // }else{
        //     this.pictures = array;
        // }
      });
    },
    //报告
    showReport(spCode) {
      this.dialogPageVisible = true;
      this.spCode = spCode;
    },
    //表具计量信息查询
    // selectTableList() {
    //   this.dataList = [];
    //   tableList(this.tableParams).then((response) => {
    //     this.dataList = response.rows;
    //     if (this.dataList && this.dataList.length > 0) {
    //       this.tablePageParam.total = response.total;
    //     }
    //   });
    // },
    //表具计量信息页面
    // tableHandleCurrentChange(val) {
    //   this.tableParams.pageNum = val;
    //   this.selectTableList();
    // },
    //安检历史信息
    selectCheckHistoryList() {
      checkHistoryList(this.checkParams).then((response) => {
        this.checkDataList = response.data;
        if (this.checkDataList && this.checkDataList.length > 0) {
          this.checkPageParam.total = response.total;
        }
      });
    },
    //安检历史页面
    checkHandleCurrentChange(val) {
      this.checkParams.pageNum = val;
      this.selectCheckHistoryList();
    },
    /**安检结果 */
    checkResultFormat(row) {
      return this.selectDictLabel(this.checkGroupOptions, row.result);
    },
    /**时间转换 */
    timeFormat(row) {
      return moment(row.checkDate).format("YYYY-MM-DD");
    },
    /**进气方向 */
    // inletPathFormat(row) {
    //   return this.selectDictLabel(this.inletPathOptions, row.inletPath);
    // },
    getList() {
      findUserInfoAndCheckInfo(this.userInfo.consNo).then((res) => {
        this.recentUserInfo = {};
        if(res.data){
          this.recentUserInfo = res.data;
          /**用户状态 */
          this.recentUserInfo.status = this.selectDictLabel(
            this.statusOptions,
            this.recentUserInfo.status
          );
          /**用气性质 */
          this.recentUserInfo.consType = this.selectDictLabel(
            this.consTypeOptions,
            this.recentUserInfo.consType
          );
          /**是否存在隐患 */
          this.recentUserInfo.dangerIsExit =
            this.recentUserInfo.dangerIsExit == 1 ? "是" : this.recentUserInfo.dangerIsExit == 0 ? "否" : "";
          /**隐患等级 */
          this.recentUserInfo.dangerLevel = this.selectDictLabel(
            this.dangerOptions,
            this.recentUserInfo.dangerLevel
          );
          /**安检结果 */
          this.recentUserInfo.result = this.selectDictLabel(
            this.checkGroupOptions,
            this.recentUserInfo.result
          );
          if(res.data.consNo && res.data.srdCode){
            this.getRecentPhoto(res.data.consNo, res.data.srdCode);
          }
          // let pqCqlName = this.selectDictLabel(
          //   this.pqCalOptions,
          //   this.recentUserInfo.pqCal
          // );
          // if (pqCqlName.indexOf("人口") != -1) {
          //   this.isPqCal = false;
          // }
        }
      });
    },
  },
  created() {
    this.getDicts("SC_CHECK_RESULT").then((response) => {
      this.checkGroupOptions = response.data;
    });
    // this.getDicts("EBS_inletPath").then((response) => {
    //   this.inletPathOptions = response.data;
    // });
    this.getDicts("EBS_PremiseStatus").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("ebs_consType").then((response) => {
      this.consTypeOptions = response.data;
    });
    this.getDicts("SC_DANGER_LEVEL").then((response) => {
      this.dangerOptions = response.data;
    });
    this.getDicts("EBS_MeteringType").then((response) => {
      this.pqCalOptions = response.data;
    });
    this.getList();
    // this.selectTableList();
    this.selectCheckHistoryList();
  },
};
</script>
<style lang='less' scoped>
.tableTitle {
  font-weight: 700;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 20px;
  color: rgba(0, 0, 0, 0.85);
}
.flex-box-imgs {
  display: inline-block;
  margin-left: 10px;
}

/deep/.el-input.is-disabled .el-input__inner {
  border: none !important;
  background-color: white;
}
.el-col-6 {
  height: 50px;
}
/deep/.input-over .el-input__inner {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>