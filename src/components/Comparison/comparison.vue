<template>
  <div class="comparison">
    <!-- :style="{ cursor: msgLoading ? 'progress' : 'default' }" -->
    <!-- <div class="similarity">
      <div class="text">
        文档相似度对比：<span>{{ similarity }}</span>
      </div>
    </div> -->
    <div class="general-card">
      <div class="comparison-list">
        <el-card>
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>源文件</span>
          </div>
          <div v-if="oldSrc" class="old-box">
            <iframe v-if="oldType === 'pdf'" id="oldpdf" :src="oldSrc"></iframe>
            <div v-else class="docx-box" :style="oldStyle">
              <vue-office-docx
                :src="oldSrc"
                class="old-docx"
                @rendered="renderedOld"
              />
            </div>
          </div>
          <el-empty v-else></el-empty>
        </el-card>
        <el-card>
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>目标文件</span>
          </div>
          <div v-if="newSrc" class="new-box">
            <iframe v-if="newType === 'pdf'" id="newpdf" :src="newSrc"></iframe>
            <div v-else class="docx-box" :style="newStyle">
              <vue-office-docx
                :src="newSrc"
                class="new-docx"
                @rendered="renderedNew"
              />
            </div>
          </div>
          <el-empty v-else></el-empty>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
import { diffDocument } from "@/utils/diff";
import { downLoadUrl2Blob } from "@/api/common.js";

export default {
  components: {
    VueOfficeDocx,
  },
  props: {
    oldUrl: { type: String, default: "" },
    newUrl: { type: String, default: "" },
    templateWordList: { type: Array, default: () => [] },
    realWordList: { type: Array, default: () => [] },
  },
  data() {
    return {
      timer1: null,
      timer2: null,
      msgLoading: true,
      similarity: "100%",
      // oldType: "",
      // newType: "",
      oldSrc: "",
      newSrc: "",
      oldSelector: ".old-docx .docx-wrapper > .docx > article",
      newSelector: ".new-docx .docx-wrapper > .docx > article",
      nextHeightOld: 0,
      nextHeightnew: 0,
      oldFlag: false,
      newFlag: false,
      readyList: [false, false],
      oldStyle: { transform: "scale(1)" },
      newStyle: { transform: "scale(1)" },
    };
  },
  computed: {
    oldType() {
      return this.oldUrl.substr(-3) === "pdf" ? "pdf" : "docx";
    },
    newType() {
      return this.newUrl.substr(-3) === "pdf" ? "pdf" : "docx";
    },
  },
  mounted() {
    // this.initDocumentTypes();
    // this.setupPdfHandlers();
  },
  beforeDestroy() {
    clearInterval(this.timer1);
    clearInterval(this.timer2);
  },
  watch: {
    newUrl: {
      async handler(val) {
        if (val) {
          this.newSrc = await this.handleDownload(val);
        }
      },
    },
    oldUrl: {
      async handler(val) {
        if (val) {
          this.oldSrc = await this.handleDownload(val);
          console.log(this.oldSrc, "oldSrc");
        }
      },
    },
    // srcObj: {
    //   deep: true,
    //   immediate: true,
    //   handler(val) {
    //     console.log("srcObjchange", val);
    //     if (val.newSrc && val.oldSrc) {
    //       this.newSrc = val.newSrc;
    //       this.oldSrc = val.oldSrc;
    //       // this.initDocumentTypes();

    //       this.setupPdfHandlers();
    //       console.log("到这了", this.oldSrc, this.newSrc);
    //     }
    //   },
    // },
    // realWordList: {
    //   deep: true,
    //   handler(newVal) {
    //     console.log("realWordList", newVal);
    //     if (newVal.length) {
    //       this.compareFun();
    //     }
    //     // if (newVal[0] && newVal[1]) {
    //     //   this.compareFun();

    //     // if (this.oldType === "docx" || this.newType === "docx") {
    //     //   // this.getDocxStyle();
    //     // }
    //     // }
    //   },
    // },
  },
  methods: {
    async handleDownload(val) {
      const response = await downLoadUrl2Blob({ fileUrl: val });

      if (response) {
        console.log("response", response);
        const res = await response.data?.arrayBuffer();
        return res;
      }
    },
    initDocumentTypes() {
      this.oldType = this.oldSrc.substr(-3) === "pdf" ? "pdf" : "docx";
      this.newType = this.newSrc.substr(-3) === "pdf" ? "pdf" : "docx";

      // const origin = window.location.origin;
      // this.oldSrc =
      //   this.oldType === "docx"
      //     ? `/charging-maintenance-ui/static/储能设备能源管理服务协议-一般纳税人.docx`
      //     : `${origin}/pdfjs/web/viewer.html?file=${encodeURIComponent(
      //         `${origin}${o}`
      //       )}`;

      // this.newSrc =
      //   this.newType === "docx"
      //     ? `/charging-maintenance-ui/static/储能设备能源管理服务协议-一般纳税人2.docx`
      //     : `${origin}/pdfjs/web/viewer.html?file=${encodeURIComponent(
      //         `${origin}${n}`
      //       )}`;
    },
    getIframeHeight(id) {
      const iframe = document.querySelector(id);
      const iframeDoc = iframe.contentWindow.document;
      const viewer = iframeDoc.querySelector("#viewer");
      return setInterval(() => {
        const height = viewer.offsetHeight;
        if (height > 0) {
          if (id === "#oldpdf") {
            if (height === this.nextHeightOld) {
              if (!this.oldFlag) {
                this.oldSelector = Array.from(
                  iframeDoc.querySelectorAll("#viewer>.page>.textLayer")
                );
                this.$set(this.readyList, 0, true);
              }
              this.oldFlag = true;
            } else {
              iframe.style.height = `${height + 10}px`;
            }
          }
          if (id === "#newpdf") {
            if (height === this.nextHeightnew) {
              if (!this.newFlag) {
                this.newSelector = Array.from(
                  iframeDoc.querySelectorAll("#viewer>.page>.textLayer")
                );
                this.$set(this.readyList, 1, true);
              }
              this.newFlag = true;
            } else {
              iframe.style.height = `${height + 10}px`;
            }
          }
        }
        if (id === "#oldpdf") {
          this.nextHeightOld = height;
        } else {
          this.nextHeightnew = height;
        }
      }, 1000);
    },
    addStyle(id) {
      const iframe = document.querySelector(id);
      const iframeWindow = iframe.contentWindow;
      iframeWindow.onload = () => {
        if (id === "#oldpdf") {
          this.timer1 = this.getIframeHeight("#oldpdf");
        } else {
          this.timer2 = this.getIframeHeight("#newpdf");
        }

        const iframeDocument = iframeWindow.document;
        const iframeHead = iframeDocument.head;
        const styleElement = iframeDocument.createElement("style");
        styleElement.type = "text/css";
        styleElement.textContent = `
          #viewerContainer { inset: 0; }
          body { background-color: #fff; }
          .toolbar { display: none; }
          #sidebarContainer{ display: none; }
        `;
        iframeHead.appendChild(styleElement);
      };
    },
    setupPdfHandlers() {
      if (this.oldType === "pdf") {
        this.$nextTick(() => this.addStyle("#oldpdf"));
      }
      if (this.newType === "pdf") {
        this.$nextTick(() => this.addStyle("#newpdf"));
      }
    },
    compareFun() {
      setTimeout(() => {
        const num = diffDocument(
          { selector: this.oldSelector, type: this.oldType },
          { selector: this.newSelector, type: this.newType },
          this.realWordList
        );
        console.log(num, "222222222");
        this.similarity = `${(num * 100).toFixed(2)}%`;
        this.msgLoading = false;
      }, 300);
    },
    getDocxStyle() {
      const w = 813;
      const list = document.querySelector(".comparison .comparison-list");
      const oldBox = document.querySelector(
        ".comparison .comparison-list .old-box"
      );
      const oldWidth = oldBox.offsetWidth;
      const oldHegiht = oldBox.offsetHeight;
      const oldScale = parseFloat((oldWidth / w).toFixed(2));
      this.oldStyle.transform = `scale(${oldScale})`;

      const newBox = document.querySelector(
        ".comparison .comparison-list .new-box"
      );
      const newWidth = newBox.offsetWidth;
      const newHegiht = newBox.offsetHeight;
      const newScale = parseFloat((newWidth / w).toFixed(2));
      this.newStyle.transform = `scale(${newScale})`;

      list.style.height = `${Math.max(
        newHegiht * newScale,
        oldHegiht * oldScale
      )}px`;
    },
    renderedOld() {
      this.$set(this.readyList, 0, true);
    },
    renderedNew() {
      this.$set(this.readyList, 1, true);
      this.compareFun();
    },
  },
};
</script>

<style scoped>
.comparison {
  /* position: fixed;
  top: 0;
  left: 200px;
  right: 0;
  bottom: 0; */
  z-index: 999;
  overflow: auto;
  background-color: #fff;
  color: #000;
}
.comparison /deep/ .docx-wrapper {
  background-color: #fff;
  padding: 0;
}
.comparison /deep/ .docx-wrapper > section.docx {
  width: 100% !important;
  margin-bottom: 0px;
  box-shadow: none;
}
.similarity {
  padding: 15px;
  line-height: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--color-neutral-3);
  /* position: fixed;
  top: 0;
  left: 200px; */
  width: 100%;
  z-index: 9;
  background-color: #fff;
  border-bottom: 1px solid #999;
}
.similarity .text {
  line-height: 20px;
  display: flex;
  align-items: center;
}
.similarity .text span {
  font-size: 16px;
}
.general-card {
  margin-top: 20px;
  height: calc(100vh - 20px);
  overflow: auto;
}
.comparison-list {
  display: flex;
}
.comparison-list > div {
  width: 50%;
  position: relative;
  overflow-x: auto;
}
.comparison-list .docx-box {
  /* width: 500px; */
  position: relative;
  transform-origin: 0 0;
  overflow: auto;
}
iframe {
  width: 100%;
  height: 30000px;
  border: none;
}
</style>
