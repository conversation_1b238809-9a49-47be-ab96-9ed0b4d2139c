<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleBatchCancel"
  >
    <el-form :model="batchForm" ref="batchForm" label-width="140px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="下载模版:" prop="file">
            <slot name="templateBtn">
              <el-link type="primary" @click="downExcel">点击下载</el-link>
            </slot>
          </el-form-item></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传文件:" prop="file">
            <el-upload
              ref="upload"
              :limit="1"
              accept=".xlsx, .xls"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :on-change="handleChangeFile"
              :auto-upload="false"
              :data="extraData"
            >
              <el-button>选择文件</el-button>
              <div slot="tip" class="el-upload__tip">
                <slot name="tip">
                  上传格式支持xlxs、xls文件，{{ maxSizeText }}以内。
                </slot>
              </div>
            </el-upload>
          </el-form-item></el-col
        >
      </el-row>
      <slot name="extraForm" :params="extraData"></slot>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        @click="handleBatchCancel"
        size="small"
        :loading="submitLoading"
        >取 消
      </el-button>
      <el-button
        type="primary"
        @click="handleBatchSubmit"
        size="small"
        :loading="submitLoading"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  props: {
    title: {
      type: String,
      default: "批量导入",
    },
    //上传时附带的额外参数
    extraData: {
      type: Object,
      default: () => {},
    },
    //上传接口
    uploadApi: {
      type: String,
      default: "",
      required: true,
    },
    //表格模版路径 eg:'/charging-maintenance-ui/static/公司机构导入模板.xlsx'
    templateUrl: {
      type: String,
      default: "",
    },
    maxSize: {
      type: Number,
      default: 2,
    },
    maxSizeText: {
      type: String,
      default: "2G",
    },
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      batchForm: {
        file: [],
      },
    };
  },
  computed: {
    upload() {
      let baseUrl = process.env.VUE_APP_BASE_API;
      return {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + this.uploadApi,
        updateAsCode: "",
      };
    },
  },
  methods: {
    open() {
      this.visible = true;
    },
    handleFileSuccess(response) {
      this.submitLoading = false;
      console.log("response===", response);
      if (!response.success) {
        this.$confirm(response.message, "导入失败！", {
          confirmButtonText: "重新上传",
          cancelButtonText: "取消",
          type: "error",
          center: true,
          dangerouslyUseHTMLString: true,
        })
          .then(() => {
            this.batchForm.file = [];
            this.$refs.upload.clearFiles();
          })
          .catch(() => {
            this.handleBatchCancel();
            //如果是部分成功部分失败的情况，也要刷新列表
            if (response.code == "60001") {
              this.$emit("uploadSuccess");
            }
          });
      } else {
        this.handleBatchCancel();
        this.$alert("导入成功", "导入结果", {
          type: "success",
          confirmButtonText: "我知道了",
          callback: () => {
            this.$emit("uploadSuccess");
          },
        });
      }
    },
    handleFileError(response) {
      this.submitLoading = false;
      this.$confirm(response.message, "导入失败！", {
        confirmButtonText: "重新上传",
        cancelButtonText: "取消",
        type: "error",
        center: true,
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          this.batchForm.file = [];
          this.$refs.upload.clearFiles();
        })
        .catch(() => {
          this.handleBatchCancel();
          //如果是部分成功部分失败的情况，也要刷新列表
          if (response.code == "60001") {
            this.$emit("uploadSuccess");
          }
        });
    },
    handleChangeFile(file, fileList) {
      console.log(file, fileList);
      this.batchForm.file = fileList || [];
    },
    downExcel() {
      window.location.href = this.templateUrl;
    },
    //批量配置-提交
    handleBatchSubmit() {
      console.log(this.upload.url, this.uploadApi, this.batchForm.file, "提交");
      if (this.batchForm.file?.length === 0) {
        this.$message.error("请上传文件！");
        return;
      }
      if (this.batchForm.file[0].size / 1024 / 1024 / 1024 > this.maxSize) {
        this.$message.error(`上传的文件大小不能超过${this.maxSizeText}!`);
        return;
      }
      this.submitLoading = true;
      this.$refs.upload.submit();
    },

    handleBatchCancel() {
      this.visible = false;
      this.$refs.batchForm.resetFields();
      this.batchForm.file = [];
      this.$refs.upload.clearFiles();
    },
  },
};
</script>

<style></style>
