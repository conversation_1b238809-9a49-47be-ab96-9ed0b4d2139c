<!-- 泵房tree列表组件--大屏 -->
<template>
  <div class="PumpHouse-wrap">
    <el-card :class="['box-card', isMapView ? 'map-view' : '', show ? '' : 'nonebody' ]">
      <div slot="header" style="display: flex;justify-content: space-between;color:#00fff6;">
        <span style="font-weight: bold;display: flex;align-items: center;">
          <img :src="titleIcon" alt="" style="width:20px;">
          {{title}}
        </span>
        <i v-if="showToggle" style="float: right; padding: 3px 0;cursor: pointer;" :class="[show ? 'el-icon-caret-bottom' : 'el-icon-caret-top']"  @click="$emit('update:show', !show)"></i>
      </div>
      <!-- <transition name="el-zoom-in-top sidebarLogoFade"> -->
          <div v-show="show">
            <div style="display:flex;">
              <el-input :placeholder="placeholder" v-model="filterText"> </el-input>
              <el-button @click="handleReload" type="primary" style="margin-left:5px;">重置</el-button>
            </div>
              <el-tree
                :data="data"
                :props="defaultProps"
                :node-key="nodeKey"
                @node-click="handleNodeClick"
                default-expand-all
                highlight-current
                :filter-node-method="filterNode"
                ref="tree"
                :expand-on-click-node="false"
              >
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span v-if="data.pumpHouseId">
                    <i class="el-icon-s-home"></i>
                  </span>
                  <span>{{ node.label }}</span>
                </span>
              </el-tree>
          </div>
      <!-- </transition> -->
    </el-card>
  </div>
</template>

<script>
export default {
  inject: ['reload'],
  components: {},
  props: {
    reSet: {
      type: Boolean,
      default: false
    },
    isMapView: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: ""
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: "children",
          label: "label",
        }
      }
    },
    nodeKey: {
      default: "id"
    },
    pumpHouseId: {
      default: ""
    },
    show: {
      type: Boolean,
      default: true
    },
    showToggle: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      filterText: "",
      // show: true,
      isOpen: true,
      titleIcon: require('@/assets/icons/treeicon1.png'),
    };
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    pumpHouseId: {
      handler(newVal) {
        this.getTreeKey(newVal, this.data)
      },
      immediate: true
    },
    "data.length": {
      handler(newVal) {
        if (newVal > 0) {
          this.getTreeKey(this.pumpHouseId, this.data)
        }
      },
      immediate: true
    }
  },
  methods: {
    handleReload() {
      this.$emit("resetTree")
      if(this.reload) {
        this.reload()
      }
    },
    getTreeKey(pumpHouseId, dataArr) {
      if(pumpHouseId && dataArr && dataArr.length > 0) {
        dataArr.forEach(element => {
          if (element.pumpHouseId == pumpHouseId) {
            this.setCurrentKey(element.treeKey)
          } else {
            this.getTreeKey(pumpHouseId, element.pumpHouseInfo)
          }
        });
      }
    },
    handleNodeClick(data) {
      this.$emit("nodeClick", data)
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1;
    },
    resetTree() {
      this.filterText = ""
      this.$emit("resetTree")
      this.$refs.tree.setCurrentKey(null)
    },
    changeArrow() {
      this.isOpen = !this.isOpen
    },
    //设置当前选中状态 treeKey
    setCurrentKey(treeKey) {
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(treeKey)
      })
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped>
.map-view {
  background-color: rgba(255, 0, 0, 0) !important;
  color: white;
  border: 0px !important;

  /deep/.el-card__header {
    padding: 10px;
    border: none;
    background-image: url("~@/assets/icons/lefticon2.png");
    background-size: 100%;
    font-size: 14px !important;
  }
  /deep/.el-card__body {
    padding: 10px;
    font-size: 12px !important;
    background-image: url("~@/assets/icons/info_wrap_bg_long.png");
    background-size: 100% 100%;
  }
  /deep/.el-input__inner {
    color: #b7c3dc !important;
    border: 1px solid #49ADFF;
    background-color: rgba(255, 0, 0, 0) !important;
  }
  /deep/.el-tree {
    max-height: calc(100vh - 280px);
    overflow-y: auto;
    background-color: rgba(255, 0, 0, 0) !important;
    // color: #49ADFF;
    color: white;
    font-size: 13px;
    .el-tree-node__content:hover, .el-upload-list__item:hover {
      background-color: #353f49 !important;
    }
    .el-tree-node__content {
      // background: linear-gradient(90deg, #d8d8d800, #7ec2ff6e, #d8d8d800);
      background-image: url("~@/assets/icons/treeitem.png");
      background-size: 100%;
      margin-top: 4px;
    }
  }
  /deep/.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #00FFF6 !important;
    color: #0F2943;
    border-radius: 5px;
  }
}

.nonebody {
  /deep/.el-card__body {
    padding: 0px;
  }
}

.box-card {
  // max-height: calc(100vh - 150px);
  height: auto;
  overflow-y: auto;
  width: 100%;
}

.sidebarLogoFade-enter-active {
  transition: opacity 1s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
  transition: opacity 0.3s;
}

.PumpHouse-wrap {
  position: relative;
  color: white;
  // border: 2px solid #25476e;
  // border-radius: 8px;
  .title {
    position: absolute;
    font-size: 20px;
    font-weight: 800;
    top: -15px;
    left: -20px;
    padding-left: 50px;
    padding-right: 30px;
    background-image: url("~@/assets/icons/t291.png");
    background-repeat: no-repeat;
    background-size: 100% 130%;
  }
}

.PumpHouse-close {
  width: 0;
  border: none;
}

.arrow {
  position: absolute;
  right: -18px;
}
.arrowOpen {
  top: calc((100vh / 2) + -70px);
  width: 17px;
  height: 50px;
  display: flex;
  align-items: center;
  background-color: #49adff82;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.arrowClose {
  top: calc((100vh / 2) + -70px);
  width: 17px;
  height: 50px;
  display: flex;
  align-items: center;
  background-color: #49adff82;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;

}
/deep/.el-tree-node:focus>.el-tree-node__content {
  background-color: initial;
}
</style>
