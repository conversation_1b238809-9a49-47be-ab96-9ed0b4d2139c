<!-- 泵房tree列表组件 -->
<template>
  <el-card :class="['box-card', isMapView ? 'map-view' : '' ]">
    <div slot="header">
      <span style="font-weight: bold;">{{title}}</span>
      <el-button v-show="reSet" style="float: right; padding: 3px 0" type="text" @click="show = !show">{{ show ? '收起' : '展开' }}</el-button>
      <el-button v-show="reSet" style="float: right; padding: 3px 5px 3px 0" type="text" @click="resetTree">重置</el-button>
    </div>
    <transition name="el-zoom-in-top sidebarLogoFade">
        <div v-show="show">
          <el-input :placeholder="placeholder" v-model="filterText"> </el-input>
            <el-tree
              :data="data"
              :props="defaultProps"
              :node-key="nodeKey"
              default-expand-all
              highlight-current
              empty-text="暂无数据"
              check-on-click-node
              :filter-node-method="filterNode"
              ref="tree"
              :expand-on-click-node="false"
              show-checkbox
              @check="handleCheck"
            >
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span v-if="data.pumpHouseId">
                  <i class="el-icon-s-home"></i>
                </span>
                <span>{{ node.label }}</span>
              </span>
            </el-tree>
        </div>
    </transition>
  </el-card>
</template>

<script>
export default {
  components: {},
  props: {
    reSet: {
      type: Boolean,
      default: false
    },
    isMapView: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ""
    },
    placeholder: {
      type: String,
      default: "输入关键字进行过滤"
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: "children",
          label: "label",
        }
      }
    },
    nodeKey: {
      default: "id"
    },
    pumpHouseId: {
      default: ""
    },
  },
  data() {
    return {
      filterText: "",
      show: true
    };
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    pumpHouseId: {
      handler(newVal) {
        this.getTreeKey(newVal, this.data)
      },
      immediate: true
    },
    "data.length": {
      handler(newVal) {
        if (newVal > 0) {
          this.getTreeKey(this.pumpHouseId, this.data)
        }
      },
      immediate: true
    }
  },
  methods: {
    getTreeKey(pumpHouseId, dataArr) {
      if(pumpHouseId && dataArr && dataArr.length > 0) {
        dataArr.forEach(element => {
          if (element.pumpHouseId == pumpHouseId) {
            this.setCurrentKey(element.treeKey)
          } else {
            this.getTreeKey(pumpHouseId, element.pumpHouseInfo)
          }
        });
      }
    },
    handleNodeClick(data) {
      this.$emit("nodeClick", data)
    },
    handleCheck() {
      let getCheckedKeys = this.$refs.tree.getCheckedKeys()
      let arr = []
      if (getCheckedKeys.length > 0) {
        getCheckedKeys.forEach(el => {
          if (el) {
            arr.push(el)
          }
        });
      }
      this.$emit("checkChange", arr)
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1;
    },
    resetTree() {
      this.filterText = ""
      this.$emit("resetTree")
      this.$refs.tree.setCurrentKey(null)
    },
    //设置当前选中状态 treeKey
    setCurrentKey(treeKey) {
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(treeKey)
      })
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped>
.map-view {
  background-color: rgba(255, 0, 0, 0) !important;
  color: white;
  /deep/.el-input__inner {
    background-color: rgba(255, 0, 0, 0) !important;
  }
  /deep/.el-tree {
    background-color: rgba(255, 0, 0, 0) !important;
    color: white;
    .el-tree-node__content:hover, .el-upload-list__item:hover {
      background-color: #409EFF !important;
    }
  }
  /deep/.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #409EFF !important;
  }
}

.box-card {
  // max-height: calc(100vh - 150px);
  overflow-y: auto;
}

.sidebarLogoFade-enter-active {
  transition: opacity 1s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
  transition: opacity 0.3s;
}
/deep/.el-tree {
  font-size: 14px;
}
</style>
