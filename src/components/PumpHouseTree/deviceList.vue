<!-- 数据项列表多选 -->
<template>
  <el-card :class="['box-card']">
    <div slot="header">
      <span style="font-weight: bold;">{{ title }}</span>
    </div>
    <el-input :placeholder="placeholder" v-model="filterText"> </el-input>
    <el-tree
      :data="deviceList"
      :props="defaultProps"
      :node-key="nodeKey"
      default-expand-all
      highlight-current
      empty-text="暂无数据"
      check-on-click-node
      :filter-node-method="filterNode"
      ref="tree"
      :expand-on-click-node="false"
      show-checkbox
      @check="handleCheck"
    >
      <span class="custom-tree-node" slot-scope="{ node, data }">
        <span>{{ node.label }}</span>
      </span>
    </el-tree>
  </el-card>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
    deviceList: {
      type: Array,
      default: () => [],
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: "children",
          label: "label",
        };
      },
    },
    nodeKey: {
      default: "id",
    },
    placeholder: {
      type: String,
      default: "请输入关键字",
    },
  },
  data() {
    return {
      checkList: [],
      filterText: "",
    };
  },
  computed: {},
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    handleCheck() {
      let getCheckedKeys = this.$refs.tree.getCheckedKeys();
      let arr = [];
      if (getCheckedKeys.length > 0) {
        getCheckedKeys.forEach((el) => {
          if (el) {
            arr.push(el);
          }
        });
      }
      console.log('[ arr ] >', arr)
      this.$emit("changeDevice", arr);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1;
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped>
.check-wrap {
  display: flex;
  flex-direction: column;
}
/deep/.el-tree-node__expand-icon.is-leaf {
  display: none;
}

/deep/.el-card__body {
  height: 100%;
}
/deep/.el-tree {
  height: 90%;
  overflow-y: auto;
  font-size: 14px;
}
</style>
