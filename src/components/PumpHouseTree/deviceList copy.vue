<!-- 设别列表多选 -->
<template>
  <el-card :class="['box-card']">
    <div slot="header">
      <span style="font-weight: bold;">{{ title }}</span>
    </div>
    <transition name="el-zoom-in-top sidebarLogoFade">
      <el-checkbox-group v-model="checkList" @change="change" class="check-wrap">
        <el-checkbox
          :label="item.value"
          v-for="(item, index) in deviceList"
          :key="index"
          >{{ item.label }}</el-checkbox
        >
      </el-checkbox-group>
    </transition>
  </el-card>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
    deviceList: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      checkList: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    change(val) {
      this.$emit("changeDevice", this.checkList)
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped>
.check-wrap {
  display: flex;
  flex-direction: column;
}
</style>
