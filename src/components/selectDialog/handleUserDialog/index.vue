<template>
  <el-dialog
    title="选择处理人"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="70%"
  >
    <el-form
      :model="baseForm"
      label-width="110px"
      :rules="baseRules"
      ref="baseForm"
    >
      <el-form-item label="处理对象" prop="userType">
        <el-radio-group v-model="baseForm.userType" @change="handleChange">
          <el-radio label="person">处理人</el-radio>
          <el-radio label="group">处理组</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <HandlePerson
      v-model="selectedUser"
      v-if="visible && baseForm.userType === 'person'"
    ></HandlePerson>
    <HandleGroup
      v-if="visible && baseForm.userType === 'group'"
      v-model="selectedUser"
    ></HandleGroup>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="handleConfirm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import HandlePerson from "./components/handlePerson.vue";
import HandleGroup from "./components/handleGroup.vue";
export default {
  components: {
    HandlePerson,
    HandleGroup,
  },

  data() {
    return {
      visible: false,
      baseForm: {
        userType: "person",
      },
      selectedUser: {},
      baseRules: {
        userType: [{ required: true, message: "请选择处理对象" }],
      },
    };
  },
  created() {},

  methods: {
    getSelectUser(row) {
      this.selectedUser = { ...row };
    },
    open(row) {
      if (row) {
        const { userType = "person", selectedUser = {} } = row;
        this.baseForm.userType = userType;
        this.selectedUser = JSON.parse(JSON.stringify(selectedUser));
      }
      console.log(this.baseForm, this.selectedUser, "===");
      this.visible = true;
    },
    closeDialog() {
      this.$refs.baseForm.resetFields();
      this.visible = false;
    },
    handleConfirm() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) {
          return false;
        }
        if (!this.selectedUser.userId && !this.selectedUser.groupId) {
          this.$message.warning("请选择处理对象");
          return false;
        }
        this.$emit("confirm", {
          ...this.baseForm,
          selectedUser: this.selectedUser,
        });
        this.closeDialog();
      });
    },
    handleChange() {
      this.selectedUser = {};
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-card__body {
  padding: 12px;
  padding-bottom: 8px;
}
/deep/.dialog-table .vxe-body--column {
  height: 32px !important;
}
/deep/ .vxe-grid--toolbar-wrapper {
  display: none;
}
/deep/ .el-dialog {
  margin-top: 2vh !important;
}
/deep/ .el-dialog__body {
  padding: 10px 20px;
}
/deep/ .pagination-container {
  margin-top: 0;
}
/deep/ .el-form-item--small.el-form-item {
  margin-bottom: 4px;
}
</style>
