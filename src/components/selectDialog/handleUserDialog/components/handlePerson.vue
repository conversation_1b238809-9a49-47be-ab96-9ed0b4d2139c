<template>
  <div>
    <el-row :gutter="10">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
            style="height:550px;overflow:auto"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <AdvancedForm
          :config="config"
          :queryParams="searchForm"
          ref="AdvancedForm"
          showMore
          @confirm="handleQuery"
          @resetQuery="resetQuery"
          v-if="config.length"
        >
        </AdvancedForm>
        <el-card>
          <GridTable
            ref="gridTable"
            :columns="columns"
            :tableData="tableData"
            @radioChangeEvent="radioChangeEvent"
            :showRadio="true"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="tableTotal"
            @changePage="queryData"
            :loading="loading"
            :tableId="tableId"
            row-id="userId"
            class="dialog-table"
            maxHeight="450"
          >
          </GridTable>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import {
  getDeptTree,
  getHandleUserList,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  props: {
    value: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      selectedObj: {},
      loading: false,
      tableId: "handlePersonList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        phonenumber: undefined,
        deptId: undefined,
      },
      deptOptions: [],
      defaultProps: {
        children: "children",
        label: "deptName",
        id: "deptId",
      },
      tableData: [],
      columns: [
        {
          field: "nickName",
          title: "用户姓名",
        },
        {
          field: "userName",
          title: "登录账号",
        },
        {
          field: "phonenumber",
          title: "手机号",
        },
      ],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "nickName",
          title: "用户姓名",
          type: "input",
          placeholder: "请输入用户姓名",
        },
        {
          key: "phonenumber",
          title: "手机号",
          type: "input",
          placeholder: "请输入手机号",
        },
      ];
    },
  },
  watch: {
    value: {
      handler(val) {
        this.selectedObj = val ? JSON.parse(JSON.stringify(val)) : {};
        console.log(JSON.parse(JSON.stringify(val)), "value");
      },
      immediate: true,
      deep: true,
    },
    // selectedObj: {
    //   handler(val) {
    //     this.$emit("change", val);
    //   },
    //   // deep: true,
    // },
  },
  created() {
    this.getTreeselect();
    this.queryData();
  },
  methods: {
    /** 查询部门下拉树结构 */
    getTreeselect() {
      getDeptTree({}).then((response) => {
        this.deptOptions = response.data;
      });
    },
    radioChangeEvent(row) {
      this.selectedObj = { ...row };
      this.$emit("input", this.selectedObj);
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.searchForm.orgNo = data.deptId;
      this.queryData();
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取列表
    queryData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      // this.selectedObj = {};
      // this.finallySearch = args;
      getHandleUserList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
          this.$nextTick(() => {
            const table = this.$refs.gridTable?.$refs.xTable;
            table?.clearRadioRow();
            let selectRow = table.getRowById(this.selectedObj?.userId);
            if (selectRow) {
              table.setRadioRow(selectRow);
            }
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style></style>
