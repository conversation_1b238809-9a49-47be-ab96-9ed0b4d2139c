<template>
  <div>
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        @radioChangeEvent="radioChangeEvent"
        :showRadio="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="queryData"
        :loading="loading"
        :tableId="tableId"
        row-id="groupId"
        class="dialog-table"
        maxHeight="450"
      >
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import { queryGroupList } from "@/api/errorPush/index.js";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  props: {
    value: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      selectedObj: {},
      loading: false,
      tableId: "handleGroupList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        phonenumber: undefined,
        deptId: undefined,
      },
      deptOptions: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      tableData: [],
      columns: [
        {
          field: "groupName",
          title: "处理组名称",
          customWidth: 200,
        },
        {
          field: "groupType",
          title: "处理组类别",
          customWidth: 200,
          formatter: ({ cellValue }) => {
            switch (cellValue) {
              case 1:
                return "手动组";
              case 0:
                return "自动组";
              default:
                return cellValue;
            }
          },
        },
      ],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "groupName",
          title: "处理组名称",
          type: "input",
          placeholder: "请输入处理组名称",
        },
      ];
    },
  },
  watch: {
    // selectedObj: {
    //   handler(val) {
    //     this.$emit("change", val);
    //   },
    //   deep: true,
    // },
    value: {
      handler(val) {
        this.selectedObj = val ? JSON.parse(JSON.stringify(val)) : {};
        // console.log(JSON.parse(JSON.stringify(val)), "value");
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.queryData();
  },
  methods: {
    radioChangeEvent(row) {
      this.selectedObj = { ...row };
      this.$emit("input", this.selectedObj);
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取列表
    queryData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        status: 0,
      };
      // this.selectedObj = {};
      // this.finallySearch = args;
      queryGroupList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
          this.$nextTick(() => {
            const table = this.$refs.gridTable?.$refs.xTable;
            table?.clearRadioRow();
            let selectRow = table.getRowById(this.selectedObj?.groupId);
            if (selectRow) {
              table.setRadioRow(selectRow);
            }
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style></style>
