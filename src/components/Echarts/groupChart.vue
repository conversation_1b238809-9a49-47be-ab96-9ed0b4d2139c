<!-- 能耗趋势分析 -->
<template>
  <div class="line-emissions">
    <div
      ref="chart"
      class="carbon-echart"
      :style="`${chartStyle};height:${height};width:${width}`"
    ></div>
  </div>
</template>
<!-- <GroupChart
          :axisData="offsetBarObj.xAxis"
          :serieData="offsetBarObj.series"
          lineType="bar"
          v-if="offsetBarObj.xAxis && offsetBarObj.xAxis.length > 0"
          unit="单位：万元"
          height="400px"
          :isSmooth="true"
        ></GroupChart>
 offsetBarObj: {
        xAxis: [
          { name: "香蕉", type: "水果" },
          { name: "苹果", type: "水果" },
          { name: "梨", type: "水果" },
          { name: "圆珠笔", type: "笔" },
          { name: "钢笔", type: "笔" },
        ],
        series: [
          {
            name: "工单耗时",
            data: [100, 120, 124, 130],
            showYAxis: true,
            yAxisIndex: 0,
          },
          { name: "工时成本", data: [130, 140, 100, 120] },
          {
            name: "幅度",
            data: [130, 140, 100, 120],
            showYAxis: true,
            yAxisIndex: 1,
            type: "line",
          },
        ],
      }, -->
<script>
import * as echarts from "echarts";
export default {
  props: {
    //x轴数据
    axisData: {
      type: Array,
      default: () => [],
      require: true,
    },
    height: {
      type: String,
      default: "350px",
    },
    width: {
      type: String,
      default: "100%",
    },
    //y轴数据
    serieData: {
      type: Array,
      default: () => [],
      require: true,
    },
    //左上角单位
    unit: {
      type: String,
      default: "",
    },
    //是否显示tooltip
    showTooltip: {
      type: Boolean,
      default: true,
    },
    //折线图样式 line--折线图 bar--柱状图
    lineType: {
      type: String,
      default: "line",
    },
    //是否是堆叠柱状图
    isStack: {
      type: Boolean,
      default: false,
    },
    //折线是否为曲线，若是曲线展示区域阴影
    isSmooth: {
      type: Boolean,
      default: false,
    },
    //自定义样式
    chartStyle: {
      type: String,
      default: null,
    },
    xRotate: {
      type: Number,
      default: 0,
    },
    //x轴滚动条默认展示范围
    showZoomNum: {
      type: Number,
      default: 10,
    },
    symbolSize: {
      // 点大小
      type: Number,
      default: 6,
    },
    symbol: {
      // 点类型
      type: String,
      default: "circle",
    },
    lineColor: {
      type: Array,
      default: () => [
        "#5470C6",
        "#91CC75",
        "#FAC858",
        "#EE6666",
        "#73C0DE",
        "#3BA272",
        "#FC8452",
        "#9A60B4",
        "#EA7CCC",
      ],
    },
    toolTipValUnit: {
      type: String,
      default: "",
    },
    //是否为横向条形图
    isHorizontalBar: {
      type: Boolean,
      default: false,
    },
    //是否展示图例
    showLegend: {
      type: Boolean,
      default: true,
    },
    //图表上是否显示数据项对应的数值
    showLabel: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      textColor: "rgba(0,0,0,0.9)",

      // activeColor: 'rgba(57,114,237,1)',
    };
  },
  computed: {
    showZoom() {
      return this.serieData[0]?.data.length > 10 ? true : false;
    },
  },
  watch: {
    axisData: {
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.initChart();
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
    serieData: {
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
    // lineType: {
    //   handler(val) {
    //     if (val) {
    //       this.$nextTick(() => {
    //         this.setOption();
    //       });
    //     }
    //   },
    //   // immediate: true, // 立即执行
    // },
    // unit: {
    //   handler(val) {
    //     if (val) {
    //       this.$nextTick(() => {
    //         this.setOption();
    //       });
    //     }
    //   },
    //   // immediate: true, // 立即执行
    // },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.initObj.resize);
  },
  methods: {
    calMax(arr) {
      var max = Math.max.apply(null, arr); // 获取最大值方法
      var maxint = Math.ceil(max / 5); // 向上以5的倍数取整
      var maxval = maxint * 5 + 5; // 最终设置的最大值
      if (max < 1 && max > 0) {
        return max;
      }
      return maxval; // 输出最大值
    },
    // 获取最小值方法
    calMin(arr) {
      var min = Math.min.apply(null, arr); // 获取最小值方法
      var minint = Math.floor(min / 1); // 向下以1的倍数取整
      var minval = minint * 1 - 5; // 最终设置的最小值
      return minval; // 输出最小值
    },
    setOption() {
      const that = this;
      /**
       * @description: 计算第N层X轴重复字符串终止索引 n>1
       * @param { string[] } list 第n层x轴全部数据
       * @return {*}
       */
      const countXAxisEndIndex = (list) => {
        const temp = {};
        if (list.length) {
          let newVal = list[0].type;
          let newIndex = 0;
          temp[list[0].type] = 0;
          for (let i = 1; i < list.length; i++) {
            if (newVal !== list[i].type) {
              temp[newVal] = newIndex;
              newVal = list[i].type;
            }
            newIndex++;
          }
        }
        temp[list[list.length - 1].type] = list.length - 1;
        return temp;
      };

      /**
       * @description: 计算第N层X轴重复字符串起始索引 n>1
       * @param { string[] } list 第n层x轴全部数据
       * @return {*}
       */
      const countXAxisStartIndex = (list) => {
        const temp = {};
        if (list.length) {
          let newVal = list[0].type;
          temp[list[0].type] = 0;
          for (let i = 1; i < list.length; i++) {
            if (newVal !== list[i].type) {
              newVal = list[i].type;
              temp[list[i].type] = i;
            }
          }
        }
        return temp;
      };
      // const xAxisData = [
      //   { type: "水果", name: "香蕉" },
      //   { type: "水果", name: "苹果" },
      //   { type: "水果", name: "梨" },
      //   { type: "笔", name: "圆珠笔" },
      //   { type: "笔", name: "钢笔" },
      // ];
      const tempStartObj = countXAxisStartIndex(that.axisData);
      const tempEndObj = countXAxisEndIndex(that.axisData);
      console.log("tempStartObj", tempStartObj, tempEndObj);
      const calculateMiddlePositions = (list) => {
        const middlePositions = {};
        if (list.length) {
          let currentType = list[0].type;
          let startIndex = 0;

          for (let i = 1; i <= list.length; i++) {
            if (i === list.length || list[i]?.type !== currentType) {
              // 计算当前分组的中间位置
              const middleIndex = Math.floor((startIndex + i - 1) / 2);
              middlePositions[currentType] = middleIndex;

              if (i < list.length) {
                currentType = list[i].type;
                startIndex = i;
              }
            }
          }
        }
        return middlePositions;
      };

      // 计算每组的中间位置
      const middlePositions = calculateMiddlePositions(that.axisData);

      const options = {
        color: ["#165DFF", "#21CCFF"],
        dataZoom: that.isHorizontalBar
          ? null
          : [
              {
                show: that.showZoom,
                type: "slider",
                realtime: true,
                startValue: 0,
                endValue: this.showZoomNum - 1,
                xAxisIndex: [0, 1],
                bottom: "10",
                left: "30",
                height: 10,
                handleIcon:
                  "path://M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
                handleSize: 16,
                borderColor: "rgba(0,0,0,0)",
                textStyle: {
                  color: "#86909C",
                },
              },
            ],
        tooltip: {
          show: that.showTooltip,
          trigger: "axis",
          backgroundColor: "rgba(229,237,250,0.5)",
          borderWidth: "0",
          textStyle: {
            color: that.textColor,
          },
          formatter: function(info) {
            // 获取当前点击的索引
            const currentIndex = info[0].dataIndex;

            // 从axisData中获取对应的大分类名称
            const categoryType = that.axisData[currentIndex]?.type;
            const itemName = info[0].name;

            // 构建tooltip内容
            let str = `<div style="text-align: left; color:#1D2129;">
              <div>${categoryType}-${itemName}</div>
            </div>`;

            info.forEach((item) => {
              str += `<div style="
                background-color: rgba(255, 255, 255, 0.8);
                height: 32px;
                display: flex;
                justify-content: space-between;
                padding: 8px;
                border-radius: 4px;
                margin: 4px 0;"
              >
                <span style="color: #4e5969; font-size: 12px">${item.marker}${item.seriesName}</span>
                <span style="color: #1d2129; font-size: 13px;margin-left:10px">${item.value}${that.toolTipValUnit}</span>
              </div>`;
            });

            return str;
          },
          axisPointer: {
            type: "line",
            shadowStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(7,185,185,0)",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,181,120,0.12)",
                  },
                ],
                global: false,
              },
            },
          },
        },
        legend: {
          //   icon: 'rect',
          show: that.showLegend,
          //   itemWidth: 16,
          //   itemHeight: 8,
          //   itemGap: 10,
          orient: "horizontal",
          //   bottom: 10,
          x: "center", //可设定图例在左、右、居中
          y: "top",
          // selectedMode: false,
          data: (function() {
            //动态数据
            var serie = [];
            that.serieData?.map((item, index) => {
              let data = {
                name: item.name,
                itemWidth: 16,
                itemHeight: 8,
              };
              serie.push(data); // 添加对象
            });
            return serie; //返回组装好的数据
          })(),
        },
        //grid: {
        //   left: 50,
        //   top: 60,
        //   right: 10,
        //   bottom: 30,
        //   containLabel: true,
        // },
        grid: {
          // bottom: "2%",
          left: "2%",
          right: "5%",
          containLabel: true,
        },
        title: {
          // title为标题部分，有一级标题text，二级标题subtext。这里我们使用二级标题，再修改一下这个二级标题的位置即可出现我们想要的效果了，当然样式也可以通过title.subtextStyle去配置
          //   subtext: 'tCO₂',
          left: 0, // 距离左边位置
          top: -15, // 距离上面位置
          subtextStyle: {
            // 设置二级标题的样式
            // color: "#baf",
          },
        },
        xAxis: [
          {
            type: that.isHorizontalBar ? "value" : "category",
            axisLabel: {
              color: that.textColor,
              rotate: that.xRotate,
              fontSize: 14,
              fontWeight: 500,
              // formatter: function(value) {
              //   if (value?.length > 15) {
              //     return value.substring(0, 15) + "...";
              //   }
              //   return value;
              // },
            },
            axisLine: {
              lineStyle: {
                color: that.textColor,
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: that.isHorizontalBar
              ? null
              : that.axisData?.map((x) => x.name),
          },
          {
            // 第二个x轴坐标
            type: "category",
            position: "bottom",
            offset: 40,
            axisLine: { show: false, onZeroAxisIndex: 0 },
            axisTick: {
              length: 60,
              // inside: true,
              interval: (index, value) =>
                index === tempStartObj[value] || index === 0,
            },
            axisLabel: {
              align: "center",
              color: that.textColor,
              fontSize: 16,
              fontWeight: 500,
              interval: 0,
              formatter: (value, index) => {
                // 获取当前数据缩放的起始和结束位置
                const dataZoom = that.initObj.getOption().dataZoom[0];
                const startValue = dataZoom?.startValue || 0;

                // 计算当前视口内的实际数据索引
                const realIndex = index + startValue;

                // 检查是否是当前分组的中间位置
                if (realIndex === middlePositions[value]) {
                  const rate = that.axisData?.[realIndex]?.groupRate;
                  return [value, "{red|" + rate + "}"].join("\n");
                }
                return "";
              },
              rich: {
                red: {
                  color: "#FF0000",
                  fontSize: 18,
                  fontWeight: 500,
                  padding: [20, 0, 0, 0],
                },
              },
            },
            data: that.axisData?.map((item) => item.type),
          },
        ],

        yAxis: (function() {
          var arr = [];
          that.serieData.map((item, index) => {
            if (item.showYAxis) {
              let data = {
                type: "value",
                name: item.unit,
                // nameLocation: "center",
                // nameRotate: 0,
                nameGap: 40,
                nameTextStyle: {
                  // 字体样式
                  padding: [0, -20, -20, 0],
                  color: that.textColor,
                  fontSize: 14, // 字体大小
                },
                // min: item.showMin ? that.calMin(item.data) : 0,
                // max: that.calMax(item.data),
                // interval: (that.calMax(item.data) - 0) / 6,
                // splitNumber: 6, //设置坐标轴的分割段数
                axisLine: {
                  show: false,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: that.lineColor,
                  },
                },
                axisLabel: {
                  show: true,
                  fontSize: 14,
                  fontWeight: 500,
                  color: that.textColor,
                },
                axisTick: { show: false },
              };
              arr.push(data);
            }
          });
          return arr;
        })(),
        series: (function() {
          //动态数据
          var serie = [];
          that.serieData.map((item, index) => {
            let data = {
              name: item.name,
              type: item.type || that.lineType,
              stack: that.isStack ? "Search Engine" : null,
              smooth: that.isSmooth,
              yAxisIndex: item.yAxisIndex || 0,
              // symbol: 'none',
              barWidth: item.barWidth || "20px",
              showSymbol: true,
              symbol: "circle", //将小圆点改成实心 不写symbol默认空心
              symbolSize: 7, //小圆点的大小
              label: {
                show: item.showLabel,
              },
              // lineStyle: {
              //   color: '#86DF6C',
              // },
              itemStyle: {
                normal: {
                  color: that.lineColor[index % that.lineColor.length], //改变折线点的颜色
                  borderColor: "#fff", // 拐点边框颜色
                  // borderWidth: 2, // 拐点边框大小
                  lineStyle: {
                    color: that.lineColor[index % that.lineColor.length], //改变折线颜色
                    width: 3,
                  },
                },
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: that.lineColor[index % that.lineColor.length],
                  },
                  {
                    offset: 1,
                    color: "rgba(255,255,255,0)",
                  },
                ]),
                opacity: that.isSmooth ? "0.5" : 0,
              },
              data: item.data,
            };
            serie.push(data); // 添加对象
          });
          return serie; //返回组装好的数据
        })(),
      };
      that.initObj.setOption(options, true);
    },
    initChart() {
      this.initObj = echarts.init(this.$refs.chart);
      window.addEventListener("resize", this.initObj.resize);
    },
  },
};
</script>

<style lang="less" scoped>
.line-emissions {
  .carbon-echart {
    // width: 100%;
    display: flex;
    text-align: center;
  }
}
</style>
