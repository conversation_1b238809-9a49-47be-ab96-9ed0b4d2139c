<!-- 多y轴 仅适合每条数据都有一个y轴的图表 -->
<template>
  <div class="line-emissions">
    <div
      ref="chart"
      class="carbon-echart"
      :style="`${chartStyle};height:${height};width:${width}`"
    ></div>
  </div>
</template>
<script>
// time: electricQuantityVOList?.map((item) => item.time),
//           tendencyArr: [
//             {
//               name: "电池SOC（%）",
//               data: electricQuantityVOList?.map((item) => item.soc),
//               showYAxis: true,
//               yAxisIndex: 0,
//               unit: "电池SOC（%）",
//             },
//             {
//               name: "充电量（度）",
//               data: electricQuantityVOList?.map((item) => item.edPq),
//               showYAxis: true,
//               yAxisIndex: 1,
//               unit: "充电量（度）",
//             },
//           ],
import * as echarts from "echarts";
export default {
  props: {
    //x轴数据
    axisData: {
      type: Array,
      default: () => [],
      require: true,
    },
    height: {
      type: String,
      default: "350px",
    },
    width: {
      type: String,
      default: "100%",
    },
    //y轴数据
    serieData: {
      type: Array,
      default: () => [],
      require: true,
    },
    //左上角单位
    unit: {
      type: String,
      default: "",
    },
    //是否显示tooltip
    showTooltip: {
      type: Boolean,
      default: true,
    },
    //折线图样式 line--折线图 bar--柱状图
    lineType: {
      type: String,
      default: "line",
    },
    //是否是堆叠柱状图
    isStack: {
      type: Boolean,
      default: false,
    },
    //折线是否为曲线，若是曲线展示区域阴影
    isSmooth: {
      type: Boolean,
      default: false,
    },
    //自定义样式
    chartStyle: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      textColor: "rgba(0,0,0,0.9)",
      lineColor: [
        "#5470C6",
        "#91CC75",
        "#FAC858",
        "#EE6666",
        "#73C0DE",
        "#3BA272",
        "#FC8452",
        "#9A60B4",
        "#EA7CCC",
      ],
      // activeColor: 'rgba(57,114,237,1)',
    };
  },
  computed: {
    showZoom() {
      return this.serieData[0]?.data.length > 12 ? true : false;
    },
  },
  watch: {
    axisData: {
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
    serieData: {
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
    lineType: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
    unit: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
      immediate: true, // 立即执行
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.initObj.resize);
  },
  methods: {
    calMax(arr) {
      var max = Math.max.apply(null, arr); // 获取最大值方法
      var maxint = Math.ceil(max / 5); // 向上以5的倍数取整
      var maxval = maxint * 5 + 5; // 最终设置的最大值
      if (max < 1 && max > 0) {
        return max;
      }
      return maxval; // 输出最大值
    },
    // 获取最小值方法
    calMin(arr) {
      var min = Math.min.apply(null, arr); // 获取最小值方法
      var minint = Math.floor(min / 1); // 向下以1的倍数取整
      var minval = minint * 1 - 5; // 最终设置的最小值
      return minval; // 输出最小值
    },
    setOption() {
      const that = this;
      that.initObj.setOption(
        {
          color: ["#165DFF", "#21CCFF"],
          dataZoom: [
            {
              show: that.showZoom,
              type: "slider",
              realtime: true,
              startValue: 0,
              endValue: 10,
              xAxisIndex: [0],
              bottom: "10",
              left: "30",
              height: 10,
              handleIcon:
                "path://M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
              handleSize: 16,
              borderColor: "rgba(0,0,0,0)",
              textStyle: {
                color: "#86909C",
              },
            },
          ],
          tooltip: {
            show: that.showTooltip,
            // 触发类型：坐标轴触发
            trigger: "axis",
            backgroundColor: "rgba(229,237,250,0.5)", // 通过设置rgba调节背景颜色与透明度
            borderWidth: "0",
            textStyle: {
              color: that.textColor,
            },
            formatter: function(info) {
              let str = `<div style="text-align: left; color:#1D2129;" >${info[0].name}</div>`;
              info.forEach((item, index) => {
                str += `<div style="
				background-color: rgba(255, 255, 255, 0.8);
				height: 32px;
				display: flex;
				justify-content: space-between;
				padding: 8px;
				border-radius: 4px;
				margin: 4px 0;"
      >
      <span style="color: #4e5969; font-size: 12px">${item.marker}${item.seriesName}</span>
      <span style="color: #1d2129; font-size: 13px;margin-left:10px">${item.value}</span></div>`;
              });
              return str;
            },
            axisPointer: {
              //坐标轴指示器，坐标轴触发有效，
              type: "line", //默认为line，line直线，cross十字准星，shadow阴影
              shadowStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(7,185,185,0)", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "rgba(0,181,120,0.12)", // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
              },
            },
          },
          legend: {
            //   icon: 'rect',
            // show: false,
            //   itemWidth: 16,
            //   itemHeight: 8,
            //   itemGap: 10,
            orient: "horizontal",
            //   bottom: 10,
            x: "right", //可设定图例在左、右、居中
            y: "top",
            // selectedMode: false,
            data: (function() {
              //动态数据
              var serie = [];
              that.serieData.map((item, index) => {
                let data = {
                  name: item.name,
                  itemWidth: 16,
                  itemHeight: 8,
                };
                serie.push(data); // 添加对象
              });
              return serie; //返回组装好的数据
            })(),
          },
          grid: {
            bottom: "2%",
            left: "2%",
            right: "5%",
            containLabel: true,
          },
          title: {
            // title为标题部分，有一级标题text，二级标题subtext。这里我们使用二级标题，再修改一下这个二级标题的位置即可出现我们想要的效果了，当然样式也可以通过title.subtextStyle去配置
            //   subtext: 'tCO₂',
            left: 0, // 距离左边位置
            top: -15, // 距离上面位置
            subtextStyle: {
              // 设置二级标题的样式
              // color: "#baf",
            },
          },
          xAxis: [
            {
              type: "category",
              axisLabel: {
                color: that.textColor,
              },
              axisLine: {
                lineStyle: {
                  color: that.textColor,
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              data: that.axisData,
            },
          ],

          yAxis: (function() {
            var arr = [];
            that.serieData.map((item, index) => {
              if (item.showYAxis) {
                let data = {
                  type: "value",
                  name: item.unit,
                  // nameLocation: "center",
                  // nameRotate: 0,
                  nameGap: 40,
                  nameTextStyle: {
                    // 字体样式
                    padding: [0, -20, -20, 0],
                    color: that.textColor,
                    fontSize: 14, // 字体大小
                  },
                  // min: 0,
                  // max: that.calMax(item.data),
                  // interval: (that.calMax(item.data) - 0) / 6,
                  // splitNumber: 6, //设置坐标轴的分割段数
                  axisLine: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: that.lineColor,
                    },
                  },
                  axisLabel: {
                    show: true,
                    fontSize: 14,
                    fontWeight: 500,
                    color: that.textColor,
                  },
                  axisTick: { show: false },
                };
                arr.push(data);
              }
            });
            return arr;
          })(),
          // [
          //   {
          //     type: "value",
          //     name: that.unit,
          //     // nameLocation: "center",
          //     // nameRotate: 0,
          //     nameGap: 40,
          //     nameTextStyle: {
          //       // 字体样式
          //       padding: [0, -60, 0, 0],
          //       color: that.textColor,
          //       fontSize: 14, // 字体大小
          //     },
          //     axisLine: {
          //       show: false,
          //     },
          //     splitLine: {
          //       show: false,
          //       lineStyle: {
          //         color: that.lineColor,
          //       },
          //     },
          //     axisLabel: {
          //       show: true,
          //       color: that.textColor,
          //     },
          //     axisTick: { show: false },
          //   },
          // ],
          series: (function() {
            //动态数据
            var serie = [];
            that.serieData.map((item, index) => {
              let data = {
                name: item.name,
                type: item.type || that.lineType,
                stack: that.isStack ? "Search Engine" : null,
                smooth: that.isSmooth,
                yAxisIndex: item.yAxisIndex || 0,
                // symbol: 'none',
                barWidth: item.barWidth || "20px",
                showSymbol: true,
                symbol: "circle", //将小圆点改成实心 不写symbol默认空心
                symbolSize: 7, //小圆点的大小
                // lineStyle: {
                //   color: '#86DF6C',
                // },
                itemStyle: {
                  normal: {
                    color: that.lineColor[index % that.lineColor.length], //改变折线点的颜色
                    borderColor: "#fff", // 拐点边框颜色
                    // borderWidth: 2, // 拐点边框大小
                    lineStyle: {
                      color: that.lineColor[index % that.lineColor.length], //改变折线颜色
                      width: 3,
                    },
                  },
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: that.lineColor[index % that.lineColor.length],
                    },
                    {
                      offset: 1,
                      color: "rgba(255,255,255,0)",
                    },
                  ]),
                  opacity: that.isSmooth ? "0.5" : 0,
                },
                data: item.data,
              };
              serie.push(data); // 添加对象
            });
            return serie; //返回组装好的数据
          })(),
        },
        true
      );
    },
    initChart() {
      this.initObj = echarts.init(this.$refs.chart);
      window.addEventListener("resize", this.initObj.resize);
    },
  },
};
</script>

<style lang="less" scoped>
.line-emissions {
  .carbon-echart {
    // width: 100%;
    display: flex;
    text-align: center;
  }
}
</style>
