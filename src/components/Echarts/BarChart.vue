<template>
  <div
    :class="className"
    ref="barchartbox"
    :style="{
      width: '100%',
      height: height,
    }"
  ></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  props: {
    className: {
      type: String,
      default: "chart",
    },
    yAxisData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    seriesData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    itemStyleColor: {
      type: String,
      default: "auto",
    },
    height: {
      type: String,
      default: "350px",
    },
    xAxisName: {
      type: String,
      default: "",
    },
    yAxisName: {
      type: String,
      default: "",
    },
    tooltip: {
      type: Object,
      default: null,
    },
    xAxisMax: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    seriesData: {
      immediate: true,
      handler(val) {
        if (val && val.length) {
          this.$nextTick(() => {
            this.setOption();
          });
        }
      },
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      var chartDom = this.$refs.barchartbox;
      this.myChart = echarts.init(chartDom);
    },
    setOption() {
      let _t = this;
      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {},
        dataZoom: [
          {
            show: true,
            width: 10,
            orient: "vertical",
            startValue: _t.seriesData.length - 10,
            endValue: _t.seriesData.length - 1,
            // zoomLock: true,
          },
          {
            //没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
            type: "inside",
            yAxisIndex: 0,
            zoomOnMouseWheel: false, //滚轮是否触发缩放
            moveOnMouseMove: true, //鼠标滚轮触发滚动
            moveOnMouseWheel: true,
          },
        ],
        grid: {
          bottom: "2%",
          left: "2%",
          right: "5%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          name: _t.xAxisName,
          nameRotate: 270,
          nameTextStyle: {
            padding: [0, 0, 0, -100],
          },
          max: _t.xAxisMax,
        },
        yAxis: {
          type: "category",
          data: _t.yAxisData,
          name: _t.yAxisName,
        },
        series: [
          {
            name: "订单启动成功率",
            type: "bar",
            data: _t.seriesData,
            itemStyle: {
              color: _t.itemStyleColor,
            },
            tooltip: _t.tooltip,
            barCategoryGap: "50%",
          },
        ],
      };
      this.myChart.setOption(option);
    },
  },
};
</script>
