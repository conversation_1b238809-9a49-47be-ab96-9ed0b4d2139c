<!-- @format -->

<template>
  <div :class="wrapClasses" :style="wrapStyles">
    <div ref="list" :class="listClasses">
      <div
        v-for="field in fields"
        ref="items"
        :key="field.prop"
        :style="fieldStyles(field)"
        class="x-field-info-list-item"
      >
        <div :style="fieldInnerStyles" class="inner">
          <div :style="labelStyle" style="white-space: nowrap" class="label">
            {{ !field.label.endsWith(':') && !field.label.endsWith('：') ? field.label + '：' : field.label }}
          </div>
          <div :class="['content', field.prop === 'img' && 'cur', '']">
            <Render v-if="field.render" :render="field.render" :params="{ data: data || {}, field }" />
            <template v-else>
              <!-- {{ data[field.prop] }}--{{ field.prop }}--{{ field }} -->
              <!-- {{ field.prop in data ? data[field.prop] : '' }} -->
              {{ (data && data[field.prop]) || '-' }}
            </template>
          </div>
        </div>
      </div>
    </div>
    <div v-if="triggerVisible" class="trigger" @click.stop="toggle">
      <a-icon class="trigger-icon" type="caret-up" />
    </div>
  </div>
</template>

<script>
import Render from '../Render/render'

export default {
  name: 'FieldInfoList',
  components: {
    Render
  },
  props: {
    // 列数
    column: {
      type: Number,
      default: 2
    },
    // 初始可见行数
    initRow: {
      type: Number
    },
    // 字段配置
    fields: {
      default: []
    },
    // 数据
    data: {
      type: Object
    },
    // 标签宽度
    labelWidth: {
      type: Number
    },
    // 间隔
    gap: {
      type: Number,
      default: 80
    }
  },
  data() {
    return {
      initHeight: 0,
      fold: true,
      triggerVisible: false,
      $refs: {
        items: [],
        list: []
      }
    }
  },
  computed: {
    wrapClasses() {
      return [
        'x-field-info-list-wrap',
        {
          'x-field-info-list-wrap--fold': this.fold,
          'x-field-info-list-wrap--with-trigger': this.triggerVisible
        }
      ];
    },
    overflow() {
      const row = Math.ceil(this.fields.length / this.column);

      return this.initRow && this.initRow < row;
    },
    wrapStyles() {
      let styles = {};
      // 指定折叠并且实际行数大于初始行数时才限制容器高度
      if (this.fold && this.overflow) {
        styles.height = `${this.initHeight}px`;
      }
      return styles;
    },
    labelStyle() {
      return {
        width: typeof this.labelWidth === 'undefined' ? '100%' : `${this.labelWidth}px`
      };
    },
    listClasses() {
      return [
        'x-field-info-list',
        {
          'x-field-info-list--inline': typeof this.labelWidth !== 'undefined'
        }
      ];
    },
    fieldInnerStyles() {
      return {
        marginRight: `${this.gap}px`
      };
    },
  },
  mounted() {
    if (this.overflow) {
      const itemHeight = this.$refs.items[0].clientHeight;
      this.initHeight = itemHeight * this.initRow;
      this.updateContainer();
    }
  },
  methods: {
    toggle() {
      this.fold = !this.fold;
    },
    fieldStyles(field) {
      const unitPercentage = 100 / this.column;
      const percentage = field.colspan ? unitPercentage * field.colspan : unitPercentage;
      return {
        width: `${percentage}%`
      };
    },
    updateContainer() {
      this.triggerVisible = this.$refs.list && this.$refs.list.clientHeight > this.initHeight;
    }
  }
}
</script>

<style lang="less" scoped>

.x-field-info-list-wrap {
  position: relative;
  // overflow: hidden;

  .trigger {
    position: absolute;
    left: 0;
    bottom: 0;
    background: #f2f2f2;
    width: 100%;
    height: 11px;
    text-align: center;
    cursor: pointer;
    line-height: 11px;

    &:hover {
      background: #e9e9e9;
    }

    &:hover .trigger-icon {
      color: @--color-primary;
    }

    .trigger-icon {
      position: absolute;
      left: 50%;
      top: 0;
      margin-left: -4px;
      line-height: 11px;
      color: #999;
      transition: transform 0.2s ease-in-out;
    }
  }
}

.x-field-info-list-wrap--with-trigger {
  padding-bottom: 10px;
}

.x-field-info-list-wrap--fold {
  &.x-field-info-list-wrap--with-trigger {
    padding-bottom: 9px;
  }

  .trigger .iconfont,
  .trigger .iconfont-lego {
    transform: rotate(180deg);
  }
}

.x-field-info-list {
  display: flex;
  flex-wrap: wrap;

  &-item {
    min-height: 24px;
    line-height: 24px;
    font-size: 14px;

    .inner {
      padding: 18px 0 8px 0;
      border-bottom: 1px solid #f2f2f2;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
    }

    .label {
      color: @--text-color-gray;
      height: 24px;
    }

    .content {
      color: #292f38;
    }
  }

  &--inline {
    .x-field-info-list-item {
      .inner {
        flex-direction: row;
        border-bottom: none;
        padding: 6px 0;
      }
    }
  }
}
.content {
  width: 300px;
}
.cur {
  // height: 250px;
  width: 250px;
}
</style>
