<template>
  <div class="menu-search-container">
    <treeselect
      v-model="selectedMenu"
      :options="menuOptions"
      :load-options="loadOptions"
      :default-expand-level="0"
      :show-count="true"
      :placeholder="placeholder"
      :clear-value-text="'清除'"
      :default-options="defaultOptions"
      :search-nested="true"
      :flat="true"
      :limit="10"
      :clear-on-select="true"
      :append-to-body="true"
      :disable-branch-nodes="true"
      :branch-nodes-selectable="false"
      :disable-branch-nodes-toggle="false"
      :allow-branch-selection="false"
      @input="handleMenuSelect"
      @select="handleNodeClick"
      noResultsText="无匹配结果"
    />
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "MenuSearch",
  components: { Treeselect },
  props: {
    placeholder: {
      type: String,
      default: "搜索菜单...",
    },
  },
  data() {
    return {
      selectedMenu: null,
      menuOptions: [],
      defaultOptions: null,
      isLoading: false,
    };
  },
  computed: {
    allMenus() {
      // 获取所有菜单数据
      return this.$store.state.permission.topbarRouters || [];
    },
  },
  created() {
    this.initMenuOptions();
  },
  methods: {
    // 初始化菜单选项
    initMenuOptions() {
      this.menuOptions = this.formatMenuOptions(this.allMenus, "");
      // 递归处理所有节点，删除空的 children 属性
      this.processTreeNodes(this.menuOptions);
    },

    // 递归处理树节点，删除空的 children 属性
    processTreeNodes(nodes) {
      if (!nodes || !Array.isArray(nodes)) return;

      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];

        // 如果节点有 children 属性
        if (node.children) {
          // 如果 children 为空数组，删除它
          if (node.children.length === 0) {
            delete node.children;
            node.isLeaf = true;
          } else {
            // 递归处理子节点
            this.processTreeNodes(node.children);
          }
        }
      }
    },

    // 格式化菜单选项为treeselect需要的格式
    formatMenuOptions(menus, parentPath) {
      if (!menus || !menus.length) return [];

      return menus
        .map((menu) => {
          // 跳过没有meta或title的菜单项
          if (!menu.meta || !menu.meta.title) return null;

          // 构建完整路径
          let fullPath = menu.path;

          // 如果是以斜杠开头的路径，则使用原始路径
          if (menu.path.startsWith("/")) {
            fullPath = menu.path;
          }
          // 如果不是以斜杠开头的路径，且有父路径，则拼接父路径
          else if (parentPath) {
            // 确保父路径不以斜杠结尾，子路径不以斜杠开头
            const cleanParentPath = parentPath.endsWith("/")
              ? parentPath.slice(0, -1)
              : parentPath;
            const cleanChildPath = menu.path.startsWith("/")
              ? menu.path.slice(1)
              : menu.path;
            fullPath = cleanParentPath + "/" + cleanChildPath;
          }

          // 创建节点对象
          const node = {
            id: menu.path, // 保留原始ID用于查找
            label: menu.meta.title,
            path: fullPath, // 使用完整路径
            fullPath: fullPath, // 额外存储完整路径
            isLeaf: !(menu.children && menu.children.length > 0),
            // 不存储原始菜单数据，避免循环引用
            meta: menu.meta ? { ...menu.meta } : null, // 只复制必要的元数据
          };

          // 处理子菜单
          if (menu.children && menu.children.length) {
            // 递归处理子菜单
            const children = this.formatMenuOptions(
              menu.children,
              fullPath
            ).filter(Boolean);

            // 只有当子菜单不为空时，才添加 children 属性
            if (children.length > 0) {
              node.children = children;
            } else {
              // 如果子菜单为空，则标记为叶子节点
              node.isLeaf = true;
            }
          }

          // 不再需要调试日志

          return node;
        })
        .filter(Boolean);
    },

    // 异步加载选项（如果需要）
    loadOptions({ action, callback }) {
      if (action === "LOAD_CHILDREN_OPTIONS") {
        // 这里可以实现异步加载，但由于我们已经有了所有菜单数据，所以不需要
        callback();
      }
    },

    // 处理节点点击事件
    handleNodeClick(node, instanceId) {
      if (node) {
        // 检查是否为叶子节点（没有子节点的节点）
        const isLeafNode = !node.children || node.children.length === 0;

        // 只有叶子节点才进行跳转
        if (isLeafNode) {
          // 阻止默认的展开/折叠行为
          if (instanceId && instanceId.$el) {
            const event = instanceId.$el.click;
            if (event) {
              event.stopPropagation();
            }
          }

          // 使用完整路径进行跳转
          const routePath = node.fullPath || node.path;
          // 使用 catch 处理导航错误
          this.$router.push({ path: routePath }).catch((err) => {
            if (err && err.name !== "NavigationDuplicated") {
              console.error("导航错误:", err);
            }
          });

          // 清空选择
          this.selectedMenu = null;

          // // 关闭下拉菜单
          // this.$nextTick(() => {
          //   const dropdown = document.querySelector(
          //     ".vue-treeselect__menu-container"
          //   );
          //   if (dropdown) {
          //     dropdown.style.display = "none";
          //   }
          // });
        }
        // 对于非叶子节点，保持默认的展开/折叠行为
      }
    },

    // 处理菜单选择
    handleMenuSelect(value) {
      if (!value) return;

      // 在树形选择器的选项中查找选中的节点
      const findNode = (nodes, id) => {
        for (const node of nodes) {
          if (node.id === id) {
            return node;
          }
          if (node.children && node.children.length) {
            const found = findNode(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      // 在菜单选项中查找选中的节点
      const selectedNode = findNode(this.menuOptions, value);

      if (selectedNode) {
        // 检查是否为叶子节点（没有子节点的节点）
        const isLeafNode =
          !selectedNode.children || selectedNode.children.length === 0;

        // 只有叶子节点才进行跳转
        if (isLeafNode) {
          // 使用完整路径进行跳转
          const routePath = selectedNode.fullPath || selectedNode.path;
          // 使用 catch 处理导航错误
          this.$router.push({ path: routePath }).catch((err) => {
            if (err && err.name !== "NavigationDuplicated") {
              console.error("导航错误:", err);
            }
          });
        }

        // 清空选择
        this.selectedMenu = null;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.menu-search-container {
  display: inline-block;
  width: 200px;
  margin-left: 10px;
  position: relative;

  /deep/ .vue-treeselect__control {
    height: 32px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /deep/ .vue-treeselect__placeholder {
    color: #fff;
    opacity: 0.8;
  }

  /deep/ .vue-treeselect__single-value {
    color: #fff;
  }

  /deep/ .vue-treeselect__input {
    color: #fff;
  }

  /deep/ .vue-treeselect__control-arrow {
    color: #fff;
  }

  /deep/ .vue-treeselect--focused .vue-treeselect__control {
    border-color: #fff;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
  }

  /deep/ .vue-treeselect__menu {
    z-index: 3000 !important; /* 确保下拉菜单在其他元素之上 */
    max-height: 400px !important; /* 设置最大高度 */
  }

  /deep/ .vue-treeselect__list {
    padding: 5px 0;
  }

  /deep/ .vue-treeselect__option {
    padding: 5px 15px;
    cursor: pointer;
    display: flex !important; /* 使用flex布局 */
    align-items: center; /* 垂直居中对齐 */
    position: relative;
    z-index: 1;
    white-space: nowrap; /* 防止文本换行 */
  }

  /deep/ .vue-treeselect__option--highlight {
    background-color: rgba(6, 255, 202, 0.1);
  }

  /deep/ .vue-treeselect__option-arrow-container {
    margin-right: 5px; /* 添加右侧间距 */
    flex-shrink: 0; /* 防止箭头缩小 */
    display: inline-block; /* 确保箭头容器是内联块级元素 */
    vertical-align: middle; /* 垂直居中 */
  }

  /* 隐藏叶子节点的箭头容器 */
  /deep/
    .vue-treeselect__option:not(.vue-treeselect__option--has-children)
    .vue-treeselect__option-arrow-container {
    display: none !important;
  }

  /deep/ .vue-treeselect__label-container {
    cursor: pointer;
    flex-grow: 1; /* 让标签容器占据剩余空间 */
    display: inline-block; /* 确保标签容器是内联块级元素 */
    vertical-align: middle; /* 垂直居中 */
  }

  /* 确保父节点可点击展开/折叠 */
  /deep/ .vue-treeselect__option--has-children {
    cursor: pointer;
  }

  /* 确保点击整个节点区域都能触发展开/折叠 */
  /deep/
    .vue-treeselect__option--has-children
    .vue-treeselect__label-container {
    width: 100%; /* 扩大点击区域 */
  }

  /* 确保箭头和标签在同一行 */
  /deep/ .vue-treeselect__option-arrow {
    display: inline-block;
    vertical-align: middle;
  }

  /* 为叶子节点添加特殊样式 */
  /deep/
    .vue-treeselect__option:not(.vue-treeselect__option--has-children)
    .vue-treeselect__label {
    font-weight: normal;
    color: #333;
    padding-left: 20px; /* 添加左侧内边距，与有箭头的节点对齐 */
  }

  /* 为子级叶子节点添加额外缩进 */
  /deep/
    .vue-treeselect__indent-level-1
    .vue-treeselect__option:not(.vue-treeselect__option--has-children)
    .vue-treeselect__label {
    padding-left: 25px; /* 二级菜单缩进 */
  }

  /deep/
    .vue-treeselect__indent-level-2
    .vue-treeselect__option:not(.vue-treeselect__option--has-children)
    .vue-treeselect__label {
    padding-left: 30px; /* 三级菜单缩进 */
  }

  /* 为分支节点添加特殊样式 */
  /deep/
    .vue-treeselect__option--has-children
    > .vue-treeselect__label-container
    .vue-treeselect__label {
    font-weight: bold;
    color: #000;
  }

  /deep/ .vue-treeselect__option-arrow-container,
  /deep/ .vue-treeselect__label-container {
    position: relative;
    z-index: 2;
  }
}
</style>
