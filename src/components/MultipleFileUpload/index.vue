<template>
  <div>
    <el-table :data="tableList">
      <el-table-column type="index" width="50" label="序号" />
      <el-table-column prop="docName" label="文件名称" />
      <el-table-column label="文件类型">
        <template slot-scope="scope">
          {{ getFileType(scope) }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="文件大小" />
      <el-table-column prop="uploadTime" label="上传时间" />
      <el-table-column prop="name" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDownload(scope.row)"
            >下载</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleDel(scope.row, scope.$index)"
            v-if="!isPreview"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="upload-btn-wrap" v-if="!isPreview">
      <div class="upload-label">附件资料：</div>
      <el-upload
        class="upload-demo"
        ref="upload"
        :action="upload.url"
        :headers="upload.headers"
        :data="uploadParam"
        :on-preview="() => {}"
        :on-remove="() => {}"
        :on-success="handleSuccess"
        :before-upload="beforeAvatarUpload"
        :accept="accept"
        multiple
        :auto-upload="false"
        :on-exceed="handleExceed"
        :limit="limit"
        :file-list="fileList2"
        :show-file-list="true"
      >
        <el-button
          slot="trigger"
          size="small"
          type="primary"
          :disabled="tableList && tableList.length >= limit"
          >选取文件</el-button
        >
        <el-button
          style="margin-left: 10px;"
          size="small"
          type="success"
          @click="submitUpload"
          :disabled="tableList && tableList.length >= limit"
          >开始上传</el-button
        >

        <div slot="tip" class="el-upload__tip">
          <slot name="customTip">
            支持格式：{{ accept }} ，单个文件不能超过{{ maxSize }}MB
          </slot>
        </div>
      </el-upload>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: "ChargingMaintenanceUiIndex",
  props: {
    isPreview: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: ".png, .jpg, .rar, .zip, .doc, .docx, .pdf",
    },
    maxSize: {
      type: Number,
      default: 30,
    },
    //限制可以上传的文件数量
    limit: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      fileList2: [],
      uploadParam: {},
      fileList: [],
      tableList: [],
      docType: [],
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/doc/upload",
        // url: "http://charging-maintenance-server-alitest.leo.bangdao-tech.com/charging-test" + "/doc/upload",
      },
    };
  },
  computed: {},
  created() {
    //文件类型字典
    this.getDicts("cm_doc_type").then((response) => {
      this.docType = response?.data;
    });
  },

  methods: {
    getFileType(scope) {
      return this.docType?.find((el) => el.dictValue == scope.row.docType)
        ?.dictLabel;
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，超出数量限制`
      );
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    beforeAvatarUpload(file) {
      console.log("beforeAvatarUpload", file);
      const isLt2M = file.size / 1024 / 1024 < this.maxSize;
      if (!isLt2M) {
        this.$message.error(`上传文件大小超出限制!`);
        return false;
      }
      let testmsg = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
      const isAcceptType = this.accept.toLowerCase().includes(testmsg);
      if (!isAcceptType) {
        this.$message.error("请上传支持格式的文件！");
        return false;
      }
      this.uploadParam.fileList = file;
      this.fileList2.push(file);
    },
    handleDownload(row) {
      const a = document.createElement("a"); // 创建一个HTML 元素
      let url = row.storePath;
      a.setAttribute("download", row.docName); //download属性
      a.setAttribute("href", url); // href链接
      a.setAttribute("target", "_blank");
      a.click(); // 自执行点击事件
    },
    handleDel(row, index) {
      this.tableList.splice(index, 1);
      this.$emit("uploadSuccess", this.tableList);
    },
    handleSuccess(response, file, fileList) {
      // if (res.code == 10000) {
      //   this.$refs.upload.clearFiles();
      //   this.tableList.push(...res.data);
      //   this.$emit("uploadSuccess", this.tableList);
      // }
      if (this.tableList == undefined) {
        this.tableList = [];
      }
      if (fileList.every((item) => item.status == "success")) {
        fileList.map((item) => {
          if (item.response) {
            if (item.response.code == 10000) {
              this.tableList.push(...item.response?.data);
            } else {
              this.$message.error("上传失败");
            }
          }
        });
        this.$emit("uploadSuccess", this.tableList);
        this.$refs.upload.clearFiles();
        this.fileList2 = [];
      }
    },
    setAttachments(values) {
      console.log(values, "---");
      this.tableList = [...values];
      this.fileList2 = [];
    },
  },
};
</script>

<style lang="less" scoped>
.upload-btn-wrap {
  display: flex;
  margin-top: 20px;
  .upload-label {
    margin-top: 10px;
  }
}
</style>
