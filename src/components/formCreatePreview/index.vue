<template>
  <div>
    <DynamicForm
      ref="dyForm"
      :config="dynamicConfig"
      :params="dynamicParams"
      :defaultColSpan="24"
      labelPosition="right"
      labelWidth="150px"
      :preview="true"
    >
      <template #filePreview="{item,params}">
        <!-- <div v-for="(i, j) in item.fileList" :key="j">
          <el-link @click="handlePreview(j, item.fileList)">{{
            i.name
          }}</el-link>
        </div>
        <PreviewFiles
          :initial-index="previewIndex"
          v-if="showViewer"
          :on-close="
            () => {
              showViewer = false;
            }
          "
          :url-list="fileList"
        /> -->
        <FileIcons
          :list="item.fileList"
          :iconWidth="40"
          :isCenter="false"
        ></FileIcons>
      </template>
      <template #editorPreview="{item,params}">
        <div v-html="item.value"></div>
      </template>
      <template #processTableDesigner="{item,params}">
        <el-table
          :data="item.value"
          border
          style="width: 100%"
          show-summary
          :summary-method="summaryMethod"
          max-height="500px"
        >
          <el-table-column prop="processName" label="工序名称" align="center" />
          <el-table-column
            prop="standardTime"
            label="标准工时(h)"
            align="center"
          />
          <el-table-column
            prop="quantity"
            label="完成数量(个)"
            align="center"
          />
          <el-table-column
            prop="processTime"
            label="工序工时(h)"
            align="center"
          />
        </el-table>
      </template>
      <template #alert="{item,params}">
        <el-alert v-bind="item.props"></el-alert>
      </template>
    </DynamicForm>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
// import PreviewFiles from "@/components/PreviewFiles/index.vue";
import FileIcons from "@/components/FileIcons/index.vue";

export default {
  components: {
    // PreviewFiles,
    FileIcons,
  },
  props: {
    formData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showViewer: false,
      previewIndex: 0,
      dynamicParams: {},
      fileList: [],
    };
  },
  computed: {
    dynamicConfig() {
      const a = [
        {
          type: "input",
          field: "owk64l7s07cv",
          title: "输入框",
          info: "",
          _fc_drag_tag: "input",
          hidden: false,
          display: true,
          props: {},
          on: {},
          options: [],
          children: [],
          effect: {},
          value: "最好我儿高中",
        },
        {
          type: "inputNumber",
          field: "xrz64l7s0h2o",
          title: "计数器",
          info: "",
          _fc_drag_tag: "inputNumber",
          hidden: false,
          display: true,
          props: {},
          on: {},
          options: [],
          children: [],
          effect: {},
          value: 1,
        },
        {
          type: "radio",
          field: "ygc64l7s0rhr",
          title: "单选框",
          info: "",
          effect: { fetch: "" },
          options: [
            { value: "1", label: "选项1" },
            { value: "2", label: "选项2" },
          ],
          _fc_drag_tag: "radio",
          hidden: false,
          display: true,
          props: {},
          on: {},
          children: [],
          value: "2",
        },
        {
          type: "checkbox",
          field: "dt31p9w5t5mks",
          title: "多选框",
          info: "",
          effect: { fetch: "" },
          options: [
            { value: "1", label: "选项1" },
            { value: "2", label: "选项2" },
          ],
          _fc_drag_tag: "checkbox",
          hidden: false,
          display: true,
          props: {},
          on: {},
          children: [],
          value: ["1", "2"],
        },
        {
          type: "select",
          field: "ifj1p9w5t8wbr",
          title: "选择器",
          info: "",
          effect: { fetch: "" },
          options: [
            { value: "1", label: "选项1" },
            { value: "2", label: "选项2" },
          ],
          _fc_drag_tag: "select",
          hidden: false,
          display: true,
          props: {},
          on: {},
          children: [],
          value: "2",
        },
        {
          type: "timePicker",
          field: "f7q1p9w5txp8p",
          title: "时间选择器",
          info: "",
          _fc_drag_tag: "timePicker",
          hidden: false,
          display: true,
          props: {},
          on: {},
          options: [],
          children: [],
          effect: {},
          value: "15:12:28",
        },
        {
          type: "datePicker",
          field: "a5of1p9w5u10do",
          title: "日期选择器",
          info: "",
          _fc_drag_tag: "datePicker",
          hidden: false,
          display: true,
          props: {},
          on: {},
          options: [],
          children: [],
          effect: {},
          value: "2024-10-23",
        },
        {
          type: "slider",
          field: "o1x1p9w5upehb",
          title: "滑块",
          info: "",
          _fc_drag_tag: "slider",
          hidden: false,
          display: true,
          value: 8,
          props: {},
          on: {},
          options: [],
          children: [],
          effect: {},
        },
        {
          type: "rate",
          field: "ewu1p9w5vbl9g",
          title: "评分",
          info: "",
          _fc_drag_tag: "rate",
          hidden: false,
          display: true,
          value: 4,
          props: {},
          on: {},
          options: [],
          children: [],
          effect: {},
        },
        {
          type: "cascader",
          field: "a0b41p9w5vhlgv",
          title: "级联选择器",
          info: "",
          effect: { fetch: "" },
          props: {
            options: [
              {
                value: "zhinan",
                label: "指南",
                children: [
                  {
                    value: "shejiyuanze",
                    label: "设计原则",
                    children: [
                      { value: "yizhi", label: "一致" },
                      { value: "fankui", label: "反馈" },
                      { value: "xiaolv", label: "效率" },
                      { value: "kekong", label: "可控" },
                    ],
                  },
                  {
                    value: "daohang",
                    label: "导航",
                    children: [
                      { value: "cexiangdaohang", label: "侧向导航" },
                      { value: "dingbudaohang", label: "顶部导航" },
                    ],
                  },
                ],
              },
              {
                value: "zujian",
                label: "组件",
                children: [
                  {
                    value: "basic",
                    label: "Basic",
                    children: [
                      { value: "layout", label: "Layout 布局" },
                      { value: "color", label: "Color 色彩" },
                      { value: "typography", label: "Typography 字体" },
                      { value: "icon", label: "Icon 图标" },
                      { value: "button", label: "Button 按钮" },
                    ],
                  },
                  {
                    value: "form",
                    label: "Form",
                    children: [
                      { value: "radio", label: "Radio 单选框" },
                      { value: "checkbox", label: "Checkbox 多选框" },
                      { value: "input", label: "Input 输入框" },
                      {
                        value: "input-number",
                        label: "InputNumber 计数器",
                      },
                      { value: "select", label: "Select 选择器" },
                      { value: "cascader", label: "Cascader 级联选择器" },
                      { value: "switch", label: "Switch 开关" },
                      { value: "slider", label: "Slider 滑块" },
                      {
                        value: "time-picker",
                        label: "TimePicker 时间选择器",
                      },
                      {
                        value: "date-picker",
                        label: "DatePicker 日期选择器",
                      },
                      {
                        value: "datetime-picker",
                        label: "DateTimePicker 日期时间选择器",
                      },
                      { value: "upload", label: "Upload 上传" },
                      { value: "rate", label: "Rate 评分" },
                      { value: "form", label: "Form 表单" },
                    ],
                  },
                ],
              },
            ],
          },
          _fc_drag_tag: "cascader",
          hidden: false,
          display: true,
          on: {},
          options: [],
          children: [],
          value: ["zhinan", "shejiyuanze", "yizhi"],
        },
        {
          type: "upload",
          field: "d56h0qxmwassc",
          title: "上传",
          info: "",
          props: {
            action:
              "https://test-napi.bangdao-tech.com/charging-maintenance-server/flow-docking/api/v1/files/upload",
            name: "file",
            uploadType: "image",
            headers: {
              Authorization:
                "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEwMDAwLTQ4YjZhMTE0LWIzNzMtNDMwMi1iZGQzLWE0YjljODAyY2I0YiJ9.lNJ6uZ3B4mDkmdqbz3RZ_gRfjR4oVIUi5bubJFGhftq0m6w_zWUnbjCV1tMpuRCpSTCMHcB773gnYpNa6-WeTg",
            },
            listType: "image",
          },
          _fc_drag_tag: "upload",
          hidden: false,
          display: true,
          on: {},
          options: [],
          children: [],
          effect: {},
          value: [
            "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/charging-maintenance-test/10000/workOrder/2024-10-11/1728630739174_嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
          ],
          fileList: [
            {
              status: "success",
              name: "嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
              size: 190614,
              percentage: 100,
              uid: 1728630738815,
              raw: { uid: 1728630738815 },
              response: {
                code: "10000",
                data:
                  "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/charging-maintenance-test/10000/workOrder/2024-10-11/1728630739174_嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
                message: "SUCCESS",
                success: true,
                traceId: "0AE9514317286307391731116",
              },
              url:
                "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/charging-maintenance-test/10000/workOrder/2024-10-11/1728630739174_嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
            },
          ],
          previewSrcList: [
            {
              status: "success",
              name: "嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
              size: 190614,
              percentage: 100,
              uid: 1728630738815,
              raw: { uid: 1728630738815 },
              response: {
                code: "10000",
                data:
                  "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/charging-maintenance-test/10000/workOrder/2024-10-11/1728630739174_嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
                message: "SUCCESS",
                success: true,
                traceId: "0AE9514317286307391731116",
              },
              url:
                "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/charging-maintenance-test/10000/workOrder/2024-10-11/1728630739174_嫦娥姐姐这么美·头像_1_要恋爱了吗_来自小红书网页版.jpg",
            },
          ],
        },
      ];
      let arr = this.formData?.map((x) => {
        let defaultVal = x.value;
        //处理选择器：有options值的（select\radio\checkbox）
        if (x.options?.length > 0) {
          //多选
          if (Array.isArray(x.value)) {
            defaultVal = x.options
              .filter((i) => x.value.includes(i.value))
              ?.map((i) => i.label)
              ?.join("、");
          } else {
            // 单选
            defaultVal = x.options.find((i) => i.value === x.value)?.label;
          }
        }
        if (x.type === "upload") {
          x["previewSlot"] = "filePreview";
          if (!x["fileList"]) {
            x["fileList"] = x.value?.map((i) => {
              // 获取最后一个 '/' 后的部分
              let lastPart = i.substring(i.lastIndexOf("/") + 1);

              // 获取第一个 '_' 后的部分
              let name = lastPart
                .split("_")
                .slice(1)
                .join("_");
              return { url: i, name: name };
            });
          }
        }
        if (x.type === "MultiFileUpload") {
          x["previewSlot"] = "filePreview";
          if (!x["fileList"]) {
            x["fileList"] = x.value?.map((i) => {
              return { url: i.storePath, name: i.docName };
            });
          }
        }
        if (x.type === "cascader") {
          defaultVal = this.findLabelByValue(x.props.options, x.value);
        }
        if (x.type === "switch") {
          defaultVal = x.value ? "开启" : "关闭";
        }
        if (x.type === "fc-editor") {
          x["previewSlot"] = "editorPreview";
        }
        if (x.type === "ProcessTableDesigner") {
          x["previewSlot"] = "processTableDesigner";
          x.value = x.value?.filter((i) => i.selected);
        }
        if (x.type === "el-alert") {
          x["previewSlot"] = "alert";
          x["formSlot"] = true;
        }
        return { ...x, defaultValue: defaultVal, title: x.title + "：" };
      });
      console.log("arrrrrrrrr", arr);
      this.dynamicParams = initParams(arr);
      return arr;
    },
  },
  created() {},
  methods: {
    summaryMethod({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计：";
          return;
        }
        if (column.property === "quantity") {
          // 使用parseFloat和toFixed避免JS浮点数精度问题
          const total = data.reduce((sum, item) => {
            // 将当前值转换为浮点数，如果无效则使用0
            const itemValue = parseFloat(item.quantity || 0);
            // 计算新的总和并保留两位小数
            return parseFloat((sum + itemValue).toFixed(2));
          }, 0);
          sums[index] = `工序数量：${total}个`;
        } else if (column.property === "processTime") {
          // 使用parseFloat和toFixed避免JS浮点数精度问题
          const total = data.reduce((sum, item) => {
            // 将当前值转换为浮点数，如果无效则使用0
            const itemValue = parseFloat(item.processTime || 0);
            // 计算新的总和并保留两位小数
            return parseFloat((sum + itemValue).toFixed(2));
          }, 0);
          sums[index] = `工序总工时：${total}h`;
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
    //附件预览
    handlePreview(index, list) {
      this.fileList = list;
      this.showViewer = true;
      this.previewIndex = index;
    },
    findLabelByValue(options, valueArray) {
      for (const option of options) {
        if (option.value === valueArray?.[0]) {
          if (valueArray.length === 1) {
            return option.label;
          }
          if (option.children) {
            const childLabel = this.findLabelByValue(
              option.children,
              valueArray.slice(1)
            );
            if (childLabel) {
              return `${option.label} - ${childLabel}`;
            }
          }
        }
      }
      return null; // 如果没有找到，返回 null
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-table__header th {
  background-color: #f8f8f9;
  color: #606266;
  font-weight: 700;
}
</style>
