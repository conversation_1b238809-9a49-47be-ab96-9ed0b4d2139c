<!-- 多组织树 -->
<template>
  <div class="">
    <treeselect v-model="deptIds" :multiple="true" :limit="1" :options="deptOptions" :flat="true" placeholder="请选择组织" @input="change"/>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {permsTree as treeselect } from "@/api/system/dept";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: { Treeselect },
  data() {
    //这里存放数据
    return {
      deptIds: [this.$store.getters.deptId],  //选中的tree
      deptOptions:[] //tree列表
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    /** 查询部门下拉树结构 */
    async getTreeselect() {
      await treeselect('crm:customer:list').then((response) => {
        this.deptOptions = response.data;
      });
    },
    change(){
      this.$emit("getOrg",this.deptIds)
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  async created() {
    await this.getTreeselect();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
/deep/ .el-form-item--small .el-form-item__content, .el-form-item--small .el-form-item__label {
  line-height: 12px;
}
//@import url(); 引入公共css类
</style>
