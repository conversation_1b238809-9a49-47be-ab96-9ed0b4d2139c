<template>
  <div class="coord-picker">
    <div class="map-container" :style="{height: height}">
      <amap
        cache-key="coord-picker-map"
        mmap-style="amap://styles/whitesmoke"
        async
        :center.sync="center"
        :zoom.sync="zoom"
        is-hotspot
        @click="onMapClick"
      >
        <amap-satellite-layer :visible="satellite" />

        <amap-marker v-if="position" :position.sync="position" draggable />

        <el-card
          class="result-panel"
        >
          <el-input v-model="query" style="margin-bottom: 5px;"></el-input>
          <el-button @click="search(true)" :disabled="!query" type="primary" size="mini">
            搜索
          </el-button>
          <el-button @click="reset" style="margin-right: 6px;" size="mini">清 除</el-button>
          <p class="text"
            >搜索 {{ searchedValue }} 共 {{ searching ? "..." : total }} 条结果</p
          >

          <div
            v-if="mode === 'result'"
            v-loading="searching"
            class="results-wrap"
          >
            <div @click="focus(item)" v-for="(item, index) in results" :key="index">
              {{ item.name }}
              <div>地址：{{ item.address }}</div>
            </div>
          </div>

        </el-card>
        <div class="btn-wrap">
          <el-input disabled v-if="position" v-model="positionText" style="width: 200px;" />
          <el-button @click="confirm" type="primary">确 定</el-button>
        </div>
      </amap>
    </div>
  </div>
</template>

<script>
import { loadAmap, loadPlugins } from "@amap/amap-vue";
export default {
  props: {
    height: {
      default: "600px"
    }
  },
  data() {
    return {
      mode: "search",
      center: null,
      zoom: 10,
      query: "",
      searchedValue:"",
      searching: false,
      tips: [],
      results: [],
      position: null,
      pageIndex: 1,
      pageSize: 1000,
      total: 0,
      satellite: false,
      resultName: "",
    };
  },
  computed: {
    wrapper() {
      return this.$refs.wrapper;
    },
    positionText() {
      if (!this.position) return "";
      return `${this.position[0]}, ${this.position[1]}`;
    },
  },
  created() {
    this.$ready = new Promise(async (resolve) => {
      const AMap = await loadAmap();
      await loadPlugins(["AMap.PlaceSearch", "AMap.AutoComplete"]);

      this.ps = new AMap.PlaceSearch({
        pageSize: this.pageSize,
        citylimit: true
      });
      this.ac = new AMap.AutoComplete();

      resolve();
    });
  },
  methods: {
    confirm() {
      this.mode = "search"
      let result = {
        position: this.position,
        name: this.resultName
      }
      this.$emit("confirm", result)
    },
    onMapClick(e) {
      if (e.lnglat) {
        this.position = [e.lnglat.lng, e.lnglat.lat];
        this.query = ""
        this.resultName = ""
        this.results = []
      } else {
        this.position = null;
      }
    },
    async search(clear = false) {
      this.mode = "result";
      await this._ready;

      if (clear) {
        this.results = [];
        this.total = 0;
        this.pageIndex = 1;
        this.ps.setPageIndex(1);
      }

      this.searching = true;
      const { query } = this;
      this.searchedValue = this._.cloneDeep(query);
      this.ps.search(query, (status, result) => {
        this.searching = false;
        if (query !== this.query) return;

        if (status === "complete" && result.poiList) {
          console.log("[ results ] >", result.poiList.pois);
          this.results = result.poiList.pois;
          this.total = result.poiList.count;
        } else {
          this.results = [];
          this.total = 0;
        }
      });
    },
    async autoComplete(kw) {
      console.log("[ kw ] >", kw);
      if (!kw) {
        this.tips = [];
      } else {
        this.ac.search(kw, (status, result) => {
          if (kw !== this.query) return;
          if (status === "complete" && result.tips) {
            this.tips = Array.from(new Set(result.tips.map((tip) => tip.name)));
          } else {
            this.tips = [];
          }
        });
      }
    },
    focus(poi) {
      console.log('[ poi ] >', poi)
      const pos = [poi.location.lng, poi.location.lat];
      this.position = [...pos];
      this.center = [...pos];
      this.resultName = poi.name

      this.confirm()
    },
    reset() {
      this.ps.setPageIndex(1);
      this.results = [];
      this.tips = [];
      this.total = 0;
      this.query = "";
      this.searchedValue = "";
      this.mode = "search";
    },
  },
  watch: {
    pageIndex(value) {
      this.$ready.then(() => {
        this.ps.setPageIndex(value);
        this.search(false);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.map-container {
  width: 100%;
}

.result-panel {
  position: absolute;
  left: 10px;
  top: 10px;
  width: 320px;
  display: flex;
  flex-direction: column;

  .search-bar {
    display: flex;
    align-items: center;
    .text {
      text-overflow: ellipsis;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
    }
  }

  .result-list.ant-list-loading {
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.info {
  position: absolute;
  right: 10px;
  top: 10px;
  padding-left: 24px;
}

.results-wrap {
  max-height: 200px;
  overflow-y: auto;
  & > div {
    cursor: pointer;
    background-color: #d2e7f690;
    padding: 5px;
    margin-bottom: 3px;
    border-radius: 5px;
  }
}
.btn-wrap {
  position: absolute;
  top: 10px;
  right: 10px;
}
</style>
