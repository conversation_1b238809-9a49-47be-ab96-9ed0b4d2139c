<!--地图属性控制器-->
<template>
  <div
    :class="[isBlueTheme ? 'blue-theme' : 'light-theme','map-prop-controls']"
  >
    <div class="map-prop-controls-item">
      <span>3D模式：</span>
      <el-switch
        style="display: block"
        v-model="mapConfig.is3D"
        active-color="#69b7ff"
        inactive-color="#ccc"
        @change="changeMap3D"
      >
      </el-switch>
    </div>

    <div class="map-prop-controls-item" style="margin: 10px 0">
      <span>蓝色主题：</span>
      <el-switch
        style="display: block"
        v-model="isBlueTheme"
        active-color="#69b7ff"
        inactive-color="#ccc"
        @change="changeMapTheme"
      >
      </el-switch>
    </div>

    <div class="map-prop-controls-item">
      <span>显示文字：</span>
      <el-switch
        style="display: block"
        v-model="mapConfig.showLabel"
        active-color="#69b7ff"
        inactive-color="#ccc"
        @change="changeMapLabel"
      >
      </el-switch>
    </div>
  </div>
</template>

<script>
export default {
  name: "MapControls",
  data() {
    return {
      mapConfig: {
        is3D: false, //是否开启3D效果
        theme: "darkblue", //light|blue主题切换
        showLabel: false //是否显示文字
      },
      isBlueTheme: true //是否为蓝色主题
    };
  },
  methods: {
    //3D模式切换
    changeMap3D(is3D) {
      this.mapConfig.is3D = is3D;
      this.$emit("updateMapConfig", this.mapConfig);
    },
    //主题切换
    changeMapTheme(isBlueTheme) {
      this.mapConfig.theme = isBlueTheme ? "darkblue" : "normal";
      this.$emit("updateMapConfig", this.mapConfig);
    },
    //地图文字标记切换
    changeMapLabel(showLabel) {
      this.mapConfig.showLabel = showLabel;
      this.$emit("updateMapConfig", this.mapConfig);
    }
  }
};
</script>

<style lang="less" scoped>
//地图属性控制
.map-prop-controls {
  position: absolute;
  bottom: 12px;
  //left: 0;
  right: 12px;
  margin: auto;
  z-index: 2;
  width: 140px;
  height: 100px;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  //align-items: center;
  color: #fff;
  border-radius: 5px;
  padding: 10px;

  .map-prop-controls-item {
    display: flex;
    align-items: center;

    span {
      width: 80px;
      height: 20px;
      line-height: 20px;
    }
  }
}

.light-theme {
  color: #fff;
  //background-color: rgba(0, 0, 0, 0.3);
  background-image: url("~@/assets/icons/info_wrap_bg_long.png");
}

.blue-theme {
  color: #7ec2ff;
  //background-color: rgba(67, 165, 255, 0.3);
  background-image: url("~@/assets/icons/info_wrap_bg_long.png");
}
</style>
