<!-- 卡片内容 -->
<template>
  <el-card class="card-container">
    <div slot="header">
      <span>{{title}}</span>
      <el-button @click="handleClick" v-if="buttonName" style="float: right; padding: 3px 0" :type="buttonType">{{buttonName}}</el-button>
    </div>
    <slot/>
  </el-card>
</template>

<script>
export default {
  components: {},
  props: {
    title: {
      type: String,
      default: ""
    },
    buttonName: {
      type: String,
      default: ""
    },
    buttonType: {
      type: String,
      default: "text"
    }
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {
    handleClick() {
      this.$emit("handleClick")
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped></style>
