<template>
  <el-cascader
    :props="{ ...props, expandTrigger: 'hover' }"
    v-model="tempValue"
    popperClass="location"
    @change="handleChange"
    v-bind="$attrs"
    v-on="$listeners"
    ref="cascader"
  >
    <template slot-scope="{ node, data }">
      <slot :node="node" :data="data"></slot>
    </template>
  </el-cascader>
</template>
<script>
export default {
  name: "customCascader",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    props: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tempValue: this.value,
    };
  },
  created() {},
  watch: {
    value(newValue) {
      this.tempValue = newValue;
    },
    tempValue(newVal) {
      this.$emit("input", newVal);
    },
  },
  methods: {
    handleChange(val) {
      //   this.$emit("change", val);
      this.tempValue = val;
      if (!this.props.multiple) {
        this.$refs.cascader.dropDownVisible = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.location {
  .el-checkbox,
  .el-radio {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }

  .el-cascader-node {
    padding-left: 34px;
  }

  .el-checkbox__input,
  .el-radio__input {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translate(0, -50%);
  }
}
</style>
