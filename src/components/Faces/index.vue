<template>
  <div>
    <ul class="faces">
      <li v-for="(item, index) in faceList" :key="index">
        <img
            :src="faceMap.get(item)"
            :alt="item"
            :title="item"
            @click="handleInsertFace(item)"
        />
      </li>
    </ul>
    <div class="clear"></div>
  </div>
</template>

<script>
import FaceUtils from "@/utils/faceUtils";

export default{
  data(){
    return {
      faceList: FaceUtils.alt,
      faceMap: FaceUtils.faces()
    }
  },
  methods: {
    handleInsertFace(item){
      this.$emit("insertFace", item);
    }
  }
}
</script>

<style scoped lang="less">
.faces {
  list-style: none;
  background-color: #ffffff;
  border: 1px solid #f0f5ff;
  display: block;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  padding-left: 0px;
  & > li {
    width: 30px !important;
    height: 30px !important;
    padding: 4px;
    float: left;
    cursor: pointer;
    & > img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
