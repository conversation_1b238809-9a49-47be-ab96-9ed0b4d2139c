<template>
  <div class="box-content">
    <div class="box-left" style="margin-right: 10px;">
      <!-- <h4>{{ leftTitle }}</h4> -->
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <slot name="leftTitle">
          <span>{{ leftTitle }}</span>
        </slot>
      </div>
      <AdvancedForm
        :config="config"
        :queryParams="searchLeftForm"
        ref="AdvancedForm"
        v-if="config.length"
        :defaultCol="12"
        @confirm="handleLeftQuery"
        @resetQuery="resetLeftQuery"
        labelWidth="80px"
      >
      </AdvancedForm>
      <el-card>
        <GridTable
          ref="leftGridTable"
          :columns="leftColumns"
          :tableData="tableLeftData"
          :currentPage.sync="searchLeftForm.pageNum"
          :pageSize.sync="searchLeftForm.pageSize"
          :total.sync="leftPropsObj.total"
          @changePage="handleLeftQuery(false)"
          :loading="leftPropsObj.loading"
          :tableId="leftPropsObj.tableId"
          :batchDelete="true"
          :checkbox="true"
          @handleSelectionChange="leftTableSelect"
          isSmallPagination
          :columnAutoFit="false"
          :rowId="leftPropsObj.rowId"
        >
          <template slot="xToolbarBtn" slot-scope="{}">
            <el-button type="primary" size="mini" @click="handleConfig"
              >配置
            </el-button>
            <el-button type="primary" size="mini" @click="handleLeftAllConfig"
              >全部一键配置
            </el-button>
          </template>
        </GridTable>
      </el-card>
    </div>
    <div class="box-left">
      <!-- <h4>{{ rightTitle }}</h4> -->
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <slot name="rightTitle">
          <span>{{ rightTitle }}</span>
        </slot>
      </div>
      <AdvancedForm
        :config="config"
        :queryParams="searchRightForm"
        ref="AdvancedForm"
        v-if="config.length"
        :defaultCol="12"
        @confirm="handleRightQuery"
        @resetQuery="resetRightQuery"
        labelWidth="80px"
      >
      </AdvancedForm>
      <el-card>
        <GridTable
          ref="rightGridTable"
          :columns="rightColumns"
          :tableData="tableRightData"
          :currentPage.sync="searchRightForm.pageNum"
          :pageSize.sync="searchRightForm.pageSize"
          :total.sync="rightPropsObj.total"
          @changePage="handleRightQuery(false)"
          :loading="rightPropsObj.loading"
          :tableId="rightPropsObj.tableId"
          :batchDelete="true"
          :checkbox="true"
          @handleSelectionChange="rightTableSelect"
          isSmallPagination
          :columnAutoFit="false"
          :rowId="rightPropsObj.rowId"
          v-bind="{ ...rightPropsObj }"
          v-on="{ ...rightEventsObj }"
        >
          <template slot="xToolbarBtn" slot-scope="{}">
            <el-button type="primary" size="mini" @click="handleClear">
              移除
            </el-button>
            <el-button type="primary" size="mini" @click="handleRightAllConfig">
              全部一键移除
            </el-button>
          </template>
        </GridTable>
      </el-card>
    </div>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import * as api from "@/api/station/relationConfig.js";
import GridTable from "@/components/GridTable/index.vue";
export default {
  name: "transferTable",
  components: { AdvancedForm, GridTable },
  props: {
    leftTitle: {
      type: String,
      default: "未配置",
    },
    rightTitle: {
      type: String,
      default: "已配置",
    },
    config: {
      type: Array,
      default: () => [],
    },
    leftParams: {
      type: Object,
      default: () => {
        return { pageNum: 1, pageSize: 10 };
      },
    },
    rightParams: {
      type: Object,
      default: () => {
        return { pageNum: 1, pageSize: 10 };
      },
    },
    leftColumns: {
      type: Array,
      default: () => [],
    },
    rightColumns: {
      type: Array,
      default: () => [],
    },
    tableLeftData: {
      type: Array,
      default: () => [],
    },
    tableRightData: {
      type: Array,
      default: () => [],
    },
    leftPropsObj: {
      type: Object,
      default: () => {
        return {
          loading: false,
          total: 0,
          rowId: "id", //表格行唯一id！用于勾选
          tableId: "leftTableList",
        };
      },
    },
    rightPropsObj: {
      type: Object,
      default: () => {
        return {
          loading: false,
          total: 0,
          rowId: "id",
          tableId: "rightTableList",
        };
      },
    },
    rightEventsObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      leftSelectedData: [],
      rightSelectedData: [],
      searchLeftForm: {},
      searchRightForm: {},
    };
  },
  watch: {
    leftParams: {
      handler(val) {
        this.searchLeftForm = val;
      },
      immediate: true,
    },
    rightParams: {
      handler(val) {
        this.searchRightForm = val;
      },
      immediate: true,
    },
  },
  methods: {
    clearTips(direction = "left") {
      this.$refs[direction + "GridTable"].clearTips();
    },
    //右侧-清空全部站点
    handleRightAllConfig() {
      this.$confirm("是否确认全部一键移除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$emit("handleAllClear");
      });
    },
    //左侧-配置全部
    handleLeftAllConfig() {
      this.$confirm("是否确认全部一键配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$emit("handleAllConfig");
      });
    },
    leftTableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.leftSelectedData = tableData;
    },
    rightTableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.rightSelectedData = tableData;
    },
    resetLeftQuery() {
      this.searchLeftForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.$emit("handleLeftQuery", this.searchLeftForm);
      this.$refs.leftGridTable.clearTips();
    },
    handleLeftQuery(isQuery = true) {
      //点击查询按钮（isQuery=true）：页码置为1
      //点击分页（isQuery=false）
      isQuery && (this.searchLeftForm.pageNum = 1);
      this.$emit("handleLeftQuery", this.searchLeftForm);
    },
    resetRightQuery() {
      this.searchRightForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.$emit("handleRightQuery", this.searchRightForm);
      this.$refs.rightGridTable.clearTips();
    },
    handleRightQuery(isQuery = true) {
      isQuery && (this.searchRightForm.pageNum = 1);
      this.$emit("handleRightQuery", this.searchRightForm);
    },
    //左侧-配置站点
    handleConfig() {
      if (this.leftSelectedData?.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }
      this.$confirm("是否确认配置选中数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$emit("handleConfig", this.leftSelectedData);
      });

      //   let params = {
      //     userName: this.userName,
      //     userId: this.userId,
      //   };
      //   params[
      //     this.activeTab === "charge" ? "chargeList" : "serviceList"
      //   ] = this.leftStationIds;
      //   api.setStationConfig(params).then((res) => {
      //     if (res?.code === "10000") {
      //       this.$message.success("配置成功");
      //       this.getLeftList();
      //       this.getRightList();
      //       this.$refs.leftGridTable.clearTips();
      //     }
      //   });
    },
    //右侧-移除站点
    handleClear() {
      if (this.rightSelectedData?.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }
      this.$confirm("是否确认移除选中数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$emit("handleClear", this.rightSelectedData);
      });

      //   let params = {
      //     userId: this.userId,
      //   };
      //   params[
      //     this.activeTab === "charge" ? "chargeList" : "serviceList"
      //   ] = this.rightStationIds;
      //   api.clearConfig(params).then((res) => {
      //     if (res?.code === "10000") {
      //       this.$message.success("配置成功");
      //       this.getLeftList();
      //       this.getRightList();
      //       this.$refs.rightGridTable.clearTips();
      //     }
      //   });
    },
  },
};
</script>

<style lang="less" scoped>
.box-content {
  display: flex;
  .box-left {
    flex: 1;
  }
}
/deep/ .el-card__body {
  padding: 12px;
}
.card-title-wrap {
  margin-bottom: 20px;
}
</style>
