import FcDesigner from "@form-create/designer";

const label = "获取定位";
const name = "Locate";

function makeRequiredRule() {
  return {
    type: "Required",
    field: "formCreate$required",
    title: "是否必填"
  };
}

export const locate = {
  //拖拽组件的图标
  icon: "icon-upload",
  //拖拽组件的名称
  label,
  //拖拽组件的 key
  name,
  //拖拽组件的生成规则
  rule() {
    //如果在 props 方法中需要修改 rule 的属性,需要提前在 rule 上定义对应的属性
    return {
      //生成组件的名称
      type: name,
      //field 自定不能重复,所以这里每次都会生成一个新的
      field: "locateValue",
      title: label,
      props: {},
      effect: {
        //设置自定义属性
        fetch: "",
        required: "请点击获取定位" //必填校验
      },
      //校验规则
      validate: [{ type: "string", required: true, message: "请点击获取定位" }]
    };
  },
  //拖拽组件配置项(props)的生成规则
  props() {
    return [
      //生成组件的`options`配置规则
      makeRequiredRule(),
    ];
  }
};
