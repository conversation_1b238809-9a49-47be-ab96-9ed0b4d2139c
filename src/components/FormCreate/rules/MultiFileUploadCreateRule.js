import MultiFileUpload from "@/components/FormCreate/components/MultiFileUpload.vue";

const label = "多功能上传";
const name = "MultiFileUpload";

export const multiFileUpload = {
  icon: "icon-upload",
  label,
  name,
  rule() {
    return {
      type: name,
      field: "multiFileUpload",
      title: label,
      component: MultiFileUpload,
      props: {},
    };
  },
  props() {
    return [
      {
        type: "input",
        field: "accept",
        title: "支持的文件类型",
        value: ".jpg, .jpeg, .png, .xls, .xlsx, .pdf, .doc, .docx",
      },
      {
        type: "inputNumber",
        field: "limit",
        title: "最大上传数量",
        value: 99,
      },
      {
        type: "inputNumber",
        field: "fileMaxSize",
        title: "文件大小限制(MB)",
        value: 50,
      },
      {
        type: "input",
        field: "textTip",
        title: "提示文字",
        value:
          "支持批量上传，上传格式支持jpg、jpeg、png、xls、xlsx、pdf、doc、docx文件，单个文件50M以内。",
      },
      {
        type: "switch",
        field: "showPaste",
        title: "显示粘贴功能",
        value: true,
      },
      {
        type: "switch",
        field: "showDrag",
        title: "显示拖拽功能",
        value: true,
      },
      {
        type: "switch",
        field: "isVertical",
        title: "垂直布局",
        value: false,
      },
      {
        type: "inputNumber",
        field: "iconWidth",
        title: "图标宽度",
        value: 100,
      },
    ];
  },
};
