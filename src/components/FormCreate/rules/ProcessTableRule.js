import ProcessTableDesigner from "@/components/FormCreate/components/ProcessTableDesigner.vue";
// import {makeOptionsRule, makeRequiredRule} from '../../utils/index';

const label = "工序配置表";
const name = "ProcessTableDesigner";

function makeRequiredRule() {
  return {
    type: "Required",
    field: "formCreate$required",
    title: "是否必填",
  };
}

export const processTableDesigner = {
  //拖拽组件的图标
  icon: "icon-checkbox",
  //拖拽组件的名称
  label,
  //拖拽组件的 key
  name,
  //拖拽组件的生成规则
  rule() {
    //如果在 props 方法中需要修改 rule 的属性,需要提前在 rule 上定义对应的属性
    return {
      //生成组件的名称
      type: name,
      field: "gongxu", //表单组件的字段名称，即v-model绑定的值
      title: label,
      component: ProcessTableDesigner, //挂载自定义组件
      //   effect: {
      //     //设置自定义属性
      //     fetch: "",
      //     required: "请点击签名", //必填校验
      //   },
      //   //校验规则
      //   validate: [{ type: "string", required: true, message: "请点击签名" }],
      props: {},
      // options: []
    };
  },
  props() {
    return [
      // makeRequiredRule(),
      // makeOptionsRule("options"),
      //   { type: "switch", field: "disabled", title: "是否禁用" },
      //   { type: "input", field: "action", title: "上传的地址(必填)" },
      //   {
      //     type: "Struct",
      //     field: "headers",
      //     title: "设置上传的请求头部",
      //     props: { defaultValue: {} },
      //   },
    ];
  },
};
