import FcDesigner from "@form-create/designer";

const label = "水印相机";
const name = "CameraPhotoUpload";

export const cameraPhotoUpload = {
  //拖拽组件的图标
  icon: "icon-upload",
  //拖拽组件的名称
  label,
  //拖拽组件的 key
  name,
  //拖拽组件的生成规则
  rule() {
    //如果在 props 方法中需要修改 rule 的属性,需要提前在 rule 上定义对应的属性
    return {
      //生成组件的名称
      type: name,
      //field 自定不能重复,所以这里每次都会生成一个新的
      field: "value",
      title: label,
      props: {
        action: "",
        onSuccess(res, file) {
          file.url = res.data.url;
        }
      }
    };
  },
  //拖拽组件配置项(props)的生成规则
  props() {
    return [
      //生成组件的`options`配置规则
      FcDesigner.makeOptionsRule("options"),
      {
        type: "Required",
        field: "formCreate$required",
        title: "是否必填"
      },
      {
        type: "select",
        field: "uploadType",
        title: "上传类型",
        value: "image",
        options: [
          { label: "图片", value: "image" },
          {
            label: "文件",
            value: "file"
          }
        ]
      },
      { type: "input", field: "action", title: "上传的地址(必填)" },
      {
        type: "Struct",
        field: "headers",
        title: "设置上传的请求头部",
        props: { defaultValue: {} }
      },
      { type: "switch", field: "multiple", title: "是否支持多选文件" },
      {
        type: "Struct",
        field: "data",
        title: "上传时附带的额外参数",
        props: { defaultValue: {} }
      },
      { type: "input", field: "name", title: "上传的文件字段名" },
      {
        type: "switch",
        field: "withCredentials",
        title: "支持发送 cookie 凭证信息"
      },
      {
        type: "input",
        field: "accept",
        title: "接受上传的文件类型（thumbnail-mode 模式下此参数无效）"
      },
      {
        type: "switch",
        field: "autoUpload",
        title: "是否在选取文件后立即进行上传",
        value: true
      },
      {
        type: "switch",
        field: "disabled",
        title: "是否禁用"
      },
      {
        type: "inputNumber",
        field: "limit",
        title: "最大允许上传个数",
        value: 1
      }
    ];
  }
};
