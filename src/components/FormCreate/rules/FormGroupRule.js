const label = "多组表单";
const name = "FormGroup";

export const formGroup = {
  icon: "icon-row",
  label,
  name,
  mask: false,
  rule() {
    return {
      type: "FormGroup",
      props: {},
      children: [
        //{ type: "hidden", field: "groupSize", value: 1 },
        {
          // type: "el-button",
          // name: "btn",
          // props: {
          //   type: "text",
          //   field: "btn"
          // },
          // children: ["+添加一组表单11"],
          // on: {
          //   click({ $f }) {
          //     //更新组件规则，否则数据修改不会更新UI
          //     console.log($f);
          //
          //     // let groupSize = $f.getRule("groupSize").value;
          //     // $f.updateRule("groupSize", {
          //     //   value: groupSize + 1
          //     // });
          //     //
          //     // const rule = $f.rule;
          //     // if (rule) {
          //     //   rule.addRow = true;
          //     //
          //     //   $f.updateRule(rule);
          //     // }
          //   }
          // },
          slot:'append'
        }
      ]
    };
  },
  // children: "col", //子组件名称
  drag: true, //	是否显示拖拽按钮
  dragBtn: true, //是否可以拖入组件
  props() {
    return [
      { type: "inputNumber", field: "gutter", title: "栅格间隔" },
      {
        type: "switch",
        field: "type",
        title: "flex布局模式",
        props: { activeValue: "flex", inactiveValue: "default" }
      },
      {
        type: "select",
        field: "justify",
        title: "flex 布局下的水平排列方式",
        options: [
          { label: "start", value: "start" },
          { label: "end", value: "end" },
          {
            label: "center",
            value: "center"
          },
          { label: "space-around", value: "space-around" },
          { label: "space-between", value: "space-between" }
        ]
      },
      {
        type: "select",
        field: "align",
        title: "flex 布局下的垂直排列方式",
        options: [
          { label: "top", value: "top" },
          { label: "middle", value: "middle" },
          {
            label: "bottom",
            value: "bottom"
          }
        ]
      }
    ];
  }
};
