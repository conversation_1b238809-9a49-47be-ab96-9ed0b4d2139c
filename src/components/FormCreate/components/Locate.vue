<!--获取当前位置信息-->
<template>
  <div style="display: flex;">
    <el-input style="flex:1" v-model="locationDesc" :disabled="disabled" />
  </div>
</template>

<script>
export default {
  name: "Locate",
  props: {
    formCreateInject: {
      type: Object
    },
  },
  data() {
    return {
      locationDesc: "",
      disabled: false //是否禁用
    };
  },
  watch: {
    locationDesc(newVal) {
      //更新自定义组件值
      this.$emit("input", newVal);
    }
  },
  mounted() {
    const rule = this.$props.formCreateInject.rule;
    this.disabled = rule.props?.disabled ?? false;
    this.locationDesc = rule?.value ?? "";
  },
  methods: {}
};
</script>

<style scoped></style>
