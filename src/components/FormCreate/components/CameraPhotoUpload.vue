<!--水印相机-拍照上传-->
<template>
  <el-upload
    ref="upload"
    class="avatar-uploader"
    :accept="accept"
    :action="upload.uploadPath"
    :headers="upload.headers"
    :data="uploadParam"
    :show-file-list="showFileList"
    :on-remove="handleRemove"
    :on-success="handleFileSuccess"
    :on-change="handleChange"
    :before-upload="beforeAvatarUpload"
    :multiple="true"
    :limit="limit"
    :disabled="disabled"
    style="display: inline-block"
  >
    <div>
      <div v-if="type == 'img'" style="display: flex;flex-wrap: wrap;">
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <el-image
            :src="item.storePath"
            alt="加载失败"
            class="avatar"
            @click.stop="clickImg(index)"
          />
          <span
            v-show="!disabled"
            class="delete-file"
            @click.stop="handleFileRemove(index)"
          >
            ×
          </span>
        </div>

        <i
          v-if="!disabled && fileList.length < limit"
          class="el-icon-picture-outline file-item camera-btn-wrap"
          :style="{ cursor: disabled ? 'not-allowed' : 'pointer' }"
        />
      </div>
      <el-button
        slot="tip"
        size="mini"
        icon="el-icon-picture-outline"
        v-else
        :disabled="fileList.length == limit"
        class="camera-btn-wrap"
      >
        上传
      </el-button>
    </div>

    <!--图片预览-->
    <el-dialog
      title=""
      width="50%"
      :visible.sync="showImgPreview"
      @close="closePreviewDialog"
      append-to-body
      :before-close="resetPreview"
    >
      <div style="display: flex; justify-content: center; padding: 20px">
        <el-image
          :src="previewImgUrl"
          fit="contain"
          alt="加载失败"
          ref="previewImage"
          @mousewheel.prevent="handleImageWheel"
          :style="imageStyle"
        />
      </div>
    </el-dialog>
    <PicPreview ref="picPreview"></PicPreview>
  </el-upload>
</template>

<script>
import { getToken } from "@/utils/auth";
import PicPreview from "@/components/Upload/picPreview.vue";
export default {
  name: "CameraPhotoUpload",
  components: { PicPreview },
  props: {
    // field: {
    //   type: String
    // },
    formCreateInject: {
      type: Object,
    },
    value: {
      type: Array,
    },
  },

  data() {
    let baseUrl = window.location.hostname.includes(
      process.env.VUE_APP_BASE_API_PREFIX_INTERNAL
    )
      ? process.env.VUE_APP_BASE_API_SECOND_WATER_INTERNAL
      : process.env.VUE_APP_BASE_API_SECOND_WATER_EXTERNAL;
    return {
      type: "img", //上传组件类型
      limit: 1, //限制可以上传的文件数量
      showFileList: false, //是否显示文件列表
      fileMaxSize: 20, //文件最大值
      disabled: false, //是否禁用

      filePrefix: "",
      accept: "", //接受的文件类型
      fileList: [],
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        uploadPath: baseUrl + "/doc/upload",
      },
      uploadParam: {},
      showImgPreview: false, //显示图片预览弹框
      previewImgUrl: "", //预览图片地址
      zoomLevel: 100,
      imageStyle: { width: "100vh", height: "100vh" },
    };
  },
  watch: {
    "formCreateInject.rule.props": {
      handler() {
        //当表单设计器的自定义设置规则修改时，同步更新FormCreateDesiger中的自定义组件
        this.setProps();
      },
      deep: true,
    },
    fileList: {
      handler() {
        if (!this.disabled) {
          //更新自定义组件值
          let files = this.fileList
            .map((file) => file.storePath)
            .filter((url) => url !== undefined);

          this.$emit("update:value", files);
        }
      },
      deep: true,
    },
    value() {
      //只读模式下显示图片列表
      if (this.disabled) {
        this.setPreviewFileList();
      }
    },
  },
  mounted() {
    //属性配置
    this.setProps();

    //初始化文件上传类型
    this.filePrefix = window.location.hostname.includes(
      process.env.VUE_APP_BASE_API_PREFIX_INTERNAL
    )
      ? process.env.VUE_APP_BASE_FILE_PREFIX_INTERNAL
      : process.env.VUE_APP_BASE_FILE_PREFIX_EXTERNAL;
    this.accept = this.type == "img" ? ".jpg,.jpeg,.png,.gif" : "*";

    //初始化文件列表
    this.setPreviewFileList();
  },
  methods: {
    beforeAvatarUpload(file) {
      console.log("beforeAvatarUpload");

      //校验文件类型
      let validFileType = this.type == "img" ? this.validImgType(file) : true;
      //校验文件大小
      let validFileSize = this.validFileSize(file);

      this.uploadParam.fileList = file;

      return validFileType && validFileSize;
    },
    //图片类型校验
    validImgType(file) {
      let testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "jpg";
      const extension2 = testmsg === "jpeg";
      const extension3 = testmsg === "png";
      const extension4 = testmsg === "gif";
      //.jpg,.jpeg,.png,.gif
      if (!extension && !extension2 && !extension3 && !extension4) {
        this.$message.error("只能上传 jpg,jpeg,png 图片");
      }

      return extension || extension2 || extension3 || extension4;
    },
    //文件大小校验
    validFileSize(file) {
      const isLt2M = file.size / 1024 / 1024 < this.fileMaxSize;
      if (!isLt2M) {
        this.$message.error(
          `上传的${this.type == "img" ? "图片" : "文件"}大小不能超过${
            this.fileMaxSize
          }MB!`
        );
      }
      return isLt2M;
    },
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      if (response.code == 10000) {
        this.upload.importOpen = false;
        console.log("上传成功");

        let uploadImgObjs = response?.data;

        if (uploadImgObjs?.length > 0) {
          uploadImgObjs.map((item) => {
            //内外网地址拼接
            let baseUrl = "";
            let imgUrl = item.storePath;
            if (imgUrl && !imgUrl.startsWith("http")) {
              //相对地址的设置前缀
              baseUrl = window.location.hostname.includes(
                process.env.VUE_APP_BASE_API_PREFIX_INTERNAL
              )
                ? process.env.VUE_APP_BASE_FILE_PREFIX_INTERNAL
                : process.env.VUE_APP_BASE_FILE_PREFIX_EXTERNAL;
            }
            item.storePath = baseUrl + imgUrl;
          });
        }

        this.fileList.push(...uploadImgObjs);

        this.$emit("uploadResult", true, "上传成功", this.fileList);
      } else {
        console.log("上传失败");
        this.$message.error("上传失败");
        this.$emit("uploadResult", false, response.msg, this.fileList);
      }
      this.$refs["upload"].clearFiles(); //上传成功之后清除历史记录,否则无法二次上传
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handleChange(file, fileList) {},
    setFileList(fileList) {
      this.fileList = fileList;
    },
    clickImg(index) {
      console.log("clickImg:" + index);
      //点击预览图片
      // this.showImgPreview = true;
      // this.previewImgUrl = this.fileList[index].storePath;
      this.$refs.picPreview.open(index, this.fileList);
    },
    handleFileRemove(index) {
      if (index < this.fileList.length) {
        this.fileList.splice(index, 1);
        this.$emit("uploadResult", true, "删除成功", this.fileList, index);
      }
    },
    handleLimit() {
      this.$message.error(`最多上传${this.limit}个文档`);
    },
    //关闭图片预览弹框
    closePreviewDialog() {
      this.$emit("update:visible", false);
    },
    //设置组件自定义属性
    setProps() {
      const rule = this.$props.formCreateInject.rule;
      this.type = rule.props?.type ?? "img";
      this.limit = rule.props?.limit ?? 1;
      this.showFileList = rule.props?.showFileList ?? false;
      this.fileMaxSize = rule.props?.fileMaxSize ?? 20;
      this.disabled = rule.props?.disabled ?? false;
    },
    //设置图片预览地址
    setPreviewFileList() {
      if (this.value) {
        this.fileList = this.value.map((url) => {
          //内外网地址拼接
          let baseUrl = "";
          if (url && !url.startsWith("http")) {
            baseUrl = this.filePrefix;
          }

          return {
            storePath: baseUrl + url,
          };
        });
      }
    },
    resetPreview(done) {
      this.zoomLevel = 100;
      done();
    },
    handleImageWheel(event) {
      event.preventDefault();
      const delta = Math.max(
        -1,
        Math.min(1, event.deltaY || -event.wheelDelta || event.detail)
      );
      const zoomStep = 10;
      if (delta > 0) {
        // 放大图片
        this.zoomLevel += zoomStep;
      } else {
        // 缩小图片
        this.zoomLevel -= zoomStep;
      }
      // 根据缩放级别调整图片大小
      this.imageStyle = {
        ...this.imageStyle,
        transform: `scale(${this.zoomLevel / 100})`,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.file-item {
  width: 58px;
  height: 58px;
  border-radius: 4px;
  margin-right: 15px;
  display: inline-table;
  position: relative;
}

.avatar {
  position: relative;
  z-index: 1;
  width: 58px;
  height: 58px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.delete-file {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  color: white;
  top: 0;
  right: 0;
  z-index: 2;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 4px;
}

/deep/ .el-dialog__header {
  background: transparent;
}

//上传按钮样式
.camera-btn-wrap {
  width: 58px;
  height: 58px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid #c0ccda;
  border-radius: 4px;
  color: #999999;

  //position: relative;
  overflow: hidden;
  font-size: 18px;
  margin-right: 15px;
}

//上传按钮图标样式
/deep/ .el-icon-camera-solid:before {
  width: 28px;
  height: 28px;
  line-height: 28px;
}
</style>
