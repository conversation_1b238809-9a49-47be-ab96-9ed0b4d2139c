<template>
  <div>
    <!-- 设计模式：用户配置工序和标准工时 -->
    <template v-if="isDesignMode">
      <el-table :data="designData" border class="design-table">
        <el-table-column label="工序名称" align="center">
          <template slot-scope="{ row }">
            <el-input
              v-model="row.processName"
              placeholder="请输入工序名称"
              @input="handleDesignChange"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="标准工时（h）" align="center">
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.standardTime"
              placeholder="请输入标准工时"
              @input="handleDesignChange"
              :min="0"
              :max="1000"
              :precision="2"
            />
          </template>
        </el-table-column> -->
        <el-table-column label="工作日标准工时（分钟）" align="center">
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.weekdaysTime"
              placeholder="请输入数字"
              @input="handleDesignChange"
              :min="0"
              :precision="3"
              :controls="false"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="工作日非工作时间标准工时（分钟）"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.weekdaysNoWorkingTime"
              placeholder="请输入数字"
              @input="handleDesignChange"
              :min="0"
              :precision="3"
              :controls="false"
            />
          </template>
        </el-table-column>
        <el-table-column label="休息日-1.1倍标准工时（分钟）" align="center">
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.weekendsTime"
              placeholder="请输入数字"
              @input="handleDesignChange"
              :min="0"
              :precision="3"
              :controls="false"
            />
          </template>
        </el-table-column>
        <el-table-column label="休息日-2倍标准工时（分钟）" align="center">
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.weekendsTwoTime"
              placeholder="请输入数字"
              @input="handleDesignChange"
              :min="0"
              :precision="3"
              :controls="false"
            />
          </template>
        </el-table-column>
        <el-table-column label="节假日-3倍标准工时（分钟）" align="center">
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.holidayTime"
              placeholder="请输入数字"
              @input="handleDesignChange"
              :min="0"
              :precision="3"
              :controls="false"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column label="完成数量（个）" align="center">
        </el-table-column> -->
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="{ $index }">
            <!-- <el-button @click="addRow" icon="el-icon-plus" circle></el-button> -->
            <el-button
              @click="removeRow($index)"
              icon="el-icon-minus"
              circle
              v-if="designData.length > 1"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px">
        <el-button @click="addRow" type="primary">添加工序</el-button>
      </div>
    </template>

    <!-- 使用模式：用户填写数量 -->
    <template v-else>
      <div style="margin-bottom: 10px">
        <el-button type="primary" icon="el-icon-plus" @click="addUserRow"
          >新增</el-button
        >
      </div>
      <el-table :data="tableData" border ref="table" max-height="500px">
        <el-table-column label="请选择工序" align="center">
          <template slot-scope="{ row }">
            <el-select
              v-model="row.processName"
              placeholder="请选择工序项"
              style="width: 100%"
              @change="handleProcessChange(row)"
              clearable
              filterable
            >
              <el-option
                v-for="item in getAvailableProcessOptions(row)"
                :key="item.processName"
                :label="item.processName"
                :value="item.processName"
                :disabled="isProcessSelected(item.processName, row)"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="请输入完成数量（个）" align="center">
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.quantity"
              placeholder="请输入0~1000数字"
              :min="0"
              :max="1000"
              :precision="0"
              @change="handleUserChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="{ $index }">
            <el-button
              @click="removeUserRow($index)"
              icon="el-icon-minus"
              type="danger"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script>
export default {
  name: "ProcessTableDesigner",
  props: {
    // 设计模式开关
    isDesignMode: {
      type: Boolean,
      default: true,
    },
    // 初始数据（设计时配置的工序列表）
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      designData: this.value || [], // 设计时的数据
      userData: [], // 存储所有工序数据
      tableData: [], // 表格显示的数据
      processOptions: [], // 可选的工序选项
      isInitializing: true,
    };
  },
  watch: {
    // 监听设计数据变化，同步到父组件
    value(newVal) {
      this.designData = newVal;
    },
    userData: {
      handler(newVal) {
        // console.log(newVal, "newVal");
        this.$emit("input", newVal);
      },
      deep: true,
    },
  },
  created() {
    if (!this.isDesignMode) {
      // 保存所有工序选项
      this.processOptions = this.value?.map((item) => ({ ...item })) || [];

      // 初始化userData为所有工序数据
      this.userData =
        this.value?.map((item) => ({
          ...item,
          // 确保数据格式正确
          processName: item.processName || "",
          quantity: item.quantity || null,
          weekdaysTime: item.weekdaysTime || "",
          weekdaysNoWorkingTime: item.weekdaysNoWorkingTime || "",
          weekendsTime: item.weekendsTime || "",
          weekendsTwoTime: item.weekendsTwoTime || "",
          holidayTime: item.holidayTime || "",
          selected: !!item.selected,
        })) || [];

      // 如果没有数据，初始化为空数组
      if (this.userData.length === 0) {
        this.userData = [];
      }

      // 初始化表格数据
      this.updateTableData();
    }
  },
  mounted() {
    if (!this.isDesignMode) {
      this.$nextTick(() => {
        this.isInitializing = false; // 初始化完成
      });
    }
  },
  methods: {
    // 添加行（设计模式）
    addRow() {
      this.designData.push({
        processName: "",
        weekdaysTime: "",
        weekdaysNoWorkingTime: "",
        weekendsTime: "",
        weekendsTwoTime: "",
        holidayTime: "",
      });
      this.handleDesignChange();
    },
    // 删除行（设计模式）
    removeRow(index) {
      this.designData.splice(index, 1);
      this.handleDesignChange();
    },
    // 设计数据变化时触发更新
    handleDesignChange() {
      this.$emit("input", this.designData);
    },
    // 用户填写数量时触发更新
    handleUserChange() {
      // 更新userData
      this.updateUserData();
    },

    // 处理工序选择变化
    handleProcessChange(row) {
      // 如果选择了工序
      if (row.processName) {
        // 从工序选项中找到对应的工序，复制其标准工时等数据
        const selectedProcess = this.processOptions.find(
          (item) => item.processName === row.processName
        );
        if (selectedProcess) {
          row.weekdaysTime = selectedProcess.weekdaysTime || "";
          row.weekdaysNoWorkingTime =
            selectedProcess.weekdaysNoWorkingTime || "";
          row.weekendsTime = selectedProcess.weekendsTime || "";
          row.weekendsTwoTime = selectedProcess.weekendsTwoTime || "";
          row.holidayTime = selectedProcess.holidayTime || "";
        }
      }

      // 更新userData
      this.updateUserData();
    },

    // 更新表格数据
    updateTableData() {
      // 检查是否有已选择的数据
      const selectedData = this.userData.filter((item) => item.selected);

      if (selectedData && selectedData.length > 0) {
        // 如果有已选择的数据，使用它们初始化表格数据
        this.tableData = selectedData.map((item) => ({ ...item }));
      } else {
        // 如果没有已选择的数据，初始化为空数组
        this.tableData = [];
      }
    },

    // 更新userData
    updateUserData() {
      // 首先重置所有工序项的selected状态
      this.userData.forEach((item) => {
        item.selected = false;
        item.quantity = null;
      });

      // 然后根据表格数据更新userData
      this.tableData.forEach((row) => {
        if (row.processName) {
          // 找到对应的工序项
          const item = this.userData.find(
            (item) => item.processName === row.processName
          );
          if (item) {
            // 更新数据
            item.selected = true;
            item.quantity = row.quantity;
          } else {
            // 如果找不到对应的工序项，说明是新添加的
            this.userData.push({
              ...row,
              selected: true,
            });
          }
        }
      });

      // 发送更新后的userData
      this.$emit("input", this.userData);
    },

    // 添加用户模式下的行
    addUserRow() {
      // 添加一行新的空数据到表格
      this.tableData.push({
        processName: "",
        quantity: null,
        weekdaysTime: "",
        weekdaysNoWorkingTime: "",
        weekendsTime: "",
        weekendsTwoTime: "",
        holidayTime: "",
      });
    },

    // 删除用户模式下的行
    removeUserRow(index) {
      // 直接从表格数据中删除该行
      this.tableData.splice(index, 1);

      // 更新userData
      this.updateUserData();
    },

    // 获取可用的工序选项
    getAvailableProcessOptions() {
      // 返回所有工序选项
      return this.processOptions;
    },

    // 判断工序是否已被选择
    isProcessSelected(processName, currentRow) {
      if (!processName) return false;

      // 检查是否在其他行中已经选择了该工序
      return this.tableData.some(
        (row) => row !== currentRow && row.processName === processName
      );
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .el-table__header th {
  background-color: #f8f8f9;
  color: #606266;
  font-weight: 700;
}
.design-table /deep/ .el-input-number--mini {
  width: 82px;
}
</style>
