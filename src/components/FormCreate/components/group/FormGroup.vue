<template>
  <div v-if="!disabled">
    <el-button type="text" style="margin-left: 8px;" @click="addForm">
      +添加一组表单
    </el-button>
    <el-button
      type="text"
      style="margin-left: 8px;color: red;"
      @click="removeForm"
    >
      -删除一组表单
    </el-button>
  </div>
</template>

<script>
export default {
  name: "FormGroup",
  props: {
    formCreateInject: {
      type: Object
    }
  },
  components: {},
  data() {
    return {
      fcRowRule: undefined, //栅格组件的内容
      disabled: false //是否禁用
    };
  },
  watch: {
    //formCreateInject为组件生成时会给自定义组件注入的参数
    formCreateInject: {
      handler() {
        //当表单设计器的自定义设置规则修改时，同步更新FormCreateDesiger中的自定义组件

        //所有一级节点，都是DragTool
        let ruleLength = this.formCreateInject.api.rule.length;
        let fcChildren =
          ruleLength == 1
            ? this.formCreateInject.api.rule[0].children
            : this.formCreateInject.api.rule;

        if (fcChildren?.length > 0) {
          if (ruleLength == 1) {
            //表单设计器中显示的数据结构
            fcChildren.map(child => {
              //第一级节点的子节点
              let firstChildChildren = child.children;
              if (firstChildChildren?.length > 0) {
                //如果是栅格布局，记录下该节点的内容
                if (firstChildChildren[0]?.config?.config?.name == "row") {
                  this.fcRowRule = child;
                  return false;
                }
              }
            });
          } else {
            //预览时显示的数据结构
            fcChildren.map(child => {
              //如果是栅格布局，记录下该节点的内容
              if (child?.type == "FcRow") {
                this.fcRowRule = JSON.parse(JSON.stringify(child));
                return false;
              }
            });
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    const rule = this.$props.formCreateInject.rule;
    this.disabled = rule.props?.disabled ?? false;
  },
  methods: {
    //复制多组表单时，修改field值，加上下标，以保证不重复
    getAppendRule(appendRule, appendIndex) {
      if (appendRule?.children?.length > 0) {
        appendRule.children = appendRule.children.map(item => {
          if (item?.children?.length > 0) {
            item.children = item.children.map(child => {
              if (child?.field) {
                //当存在field字段时修改field字段
                return { ...child, field: child.field + appendIndex };
              } else {
                //间距(div)和分割线(el-divider)不含field
                return child;
              }
            });
          }

          return item;
        });
      }
      return appendRule;
    },
    //添加一组表单
    addForm() {
      let ruleLength = this.formCreateInject.api.rule.length;
      let targetChildren =
        ruleLength == 1
          ? this.formCreateInject.api.rule[0].children
          : this.formCreateInject.api.rule;
      let targetIndex = targetChildren?.length;
      //必须做深拷贝，不然Ui不会渲染
      let appendRule = this.fcRowRule;

      //清空Value值
      if (appendRule?.children?.length > 0) {
        appendRule.children.map((child) => {
          if (child?.children?.length > 0) {
            child.children.map((item) => {
              if (item?.value) {
                delete item.value;
              }

              //处理表单规则
              this.handleFormRule(item);

              return item;
            });
          }
          return child;
        });
      }
      //以下两种方法都可以实现追加rule
      // this.$set(targetChildren, targetIndex, appendRule);
      targetChildren.push(this.getAppendRule(appendRule, targetIndex));
    },
    //删除一组表单
    removeForm() {
      let ruleLength = this.formCreateInject.api.rule.length;
      let targetChildren =
        ruleLength == 1
          ? this.formCreateInject.api.rule[0].children
          : this.formCreateInject.api.rule;
      let childLength = targetChildren?.length;

      //这里组件个数需要排除当前组件
      if (this.getFormGroupLength() == 1) {
        this.$message({
          message: "至少填写一组表单",
          type: "warning"
        });
        return;
      }

      targetChildren.splice(childLength - 1, 1);
    },
    //计算栅格布局的个数
    getFormGroupLength() {
      let ruleLength = this.formCreateInject.api.rule.length;

      //console.log(JSON.stringify(this.formCreateInject.api.rule));
      let targetChildren =
        ruleLength == 1
          ? this.formCreateInject.api.rule[0].children
          : this.formCreateInject.api.rule;

      if (targetChildren) {
        if (ruleLength == 1) {
          //表单设计器中的数据格式解析计算
          let fcRowSize = 0;
          targetChildren.map(child => {
            //第一级节点的子节点
            let firstChildChildren = child.children;
            if (firstChildChildren?.length > 0) {
              //如果是栅格布局，记录下该节点的内容
              if (firstChildChildren[0]?.config?.config?.name == "row") {
                fcRowSize++;
                return false;
              }
            }
          });
          return fcRowSize;
        } else {
          //预览模式和正常使用时的格式解析和计算
          let fcRowArrays = targetChildren.filter(
            item => item?.type == "FcRow"
          );
          return fcRowArrays?.length;
        }
      }
    },
    handleFormRule(item) {
      if (item.type == "upload") {
        console.log("item", item);
        //添加请求头
        item.props.headers = {
          Authorization:
            "Bearer " + this.$store.state.user.token
        };
        if (
          item.props?.uploadType == "image"
        ) {
          item.props.listType = "image";
        } else if (item.props?.uploadType == "file") {
          item.props.listType = "text";
          this.showfileName = true;
        } else {
          item.props.uploadType = "image";
          item.props.listType = "image";
        }
        this.$set(item.props, "onSuccess", function(
          res,
          file,
          fileList
        ) {
          file.url = res.data;
          this.$set(item, "fileList", fileList);
        });
        this.$set(item.props, "onRemove", function(file, fileList) {
          this.$set(item, "fileList", fileList);
        });
      }
    },
  }
};
</script>

<style scoped></style>
