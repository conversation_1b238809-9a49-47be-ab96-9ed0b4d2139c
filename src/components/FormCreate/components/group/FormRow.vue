<template>
  <div></div>
<!--  <draggable-->
<!--    v-model="children"-->
<!--    :group="{ name: 'default', pull: true, put: true }"-->
<!--    animation="300"-->
<!--  >-->
<!--  </draggable>-->
</template>

<script>
// import draggable from "vuedraggable";
export default {
  name: "FormRow",
  components: {
    // draggable
  },
  data() {
    return {
      children: []
    };
  }
};
</script>

<style scoped></style>
