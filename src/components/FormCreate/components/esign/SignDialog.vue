<template>
  <div class="sign-dialog" v-if="show">
    <div class="sign-dialog-content">
      <!--签名画布-->
      <div class="sign-canvas-wrap" ref="canvasRef">
        <canvas ref="canvasMapRef" id="canvas-map" width="100" height="100" />
      </div>

      <div class="btn-wrap">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button class="clear-btn" @click="clear">清除签字</el-button>
        <el-button class="confirm-btn" @click="confirm">确认签字</el-button>
      </div>
    </div>
  </div>
</template>

<script>
//签名插件：https://github.com/szimek/signature_pad
import SignaturePad from "signature_pad";
import { rotateBase64Img } from "@/utils/sign";
import { uploadBase64 } from "@/api/uploadImg.js";

export default {
  name: "SignDialog",
  props: {
    show: {
      type: Boolean
    }
  },
  data() {
    return {
      canvasNode: null, //签名画布
      imgUrl: "",
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        //弹框显示时，初始化画布,
        this.$nextTick(() => {
          //等待 DOM 更新后,初始化画布,(因为弹框一开始不显示所以组件为underfined，init方法执行不下去导致画布初始化失败)
          this.initCanvas();
        });
      } else {
        //弹框关闭释放画布资源
        this.clear();
      }
    }
  },
  methods: {
    initCanvas() {
      const _canvasBox = this.$refs.canvasRef;
      const _canvas = this.$refs.canvasMapRef;
      if (!_canvasBox || !_canvas) {
        return false;
      }

      _canvas.width = _canvasBox.clientWidth;
      _canvas.height = _canvasBox.clientHeight;

      //清空画布
      this.clear();
      this.canvasNode = new SignaturePad(_canvas, {
        minWidth: 2,
        maxWidth: 2,
        penColor: "rgb(0, 0, 0)"
      });
    },
    /**
     * 清除签名
     */
    clear() {
      if (this.canvasNode) {
        this.canvasNode.clear();
        // this.canvasNode = null;
      }
    },
    /**
     * 确认签名
     */
    confirm() {
      const canvasNode = this.canvasNode;
      // 重新初始化画布
      if (!canvasNode) {
        this.initCanvas();
      }

      // 是否签字
      if (!canvasNode || canvasNode.isEmpty()) {
        this.$message("您还没有签名");
        return false;
      }

      // 图像旋转二次处理
      const _boxWidth = window.innerWidth;
      const _boxHeight = window.innerHeight;
      let _signImg = canvasNode.toDataURL("image/png", 0.6);

      if (_boxWidth < _boxHeight) {
        rotateBase64Img(_signImg, -90, imgUrlRes => {
          //更新签名图片
          _signImg = imgUrlRes
        });
      }
      _signImg=_signImg.substring(22,_signImg.length);
      let params = {
        picBase64: _signImg
      }
      uploadBase64(params).then(res => {
        console.log('[ res ] >', res)
        this.setSignImg(res.data)
        //关闭弹框
        this.$emit("close");
      })
      .catch(err => {
        this.$toast(err)
      })
    },
    /**
     * 设置签名图片
     * @param value
     */
    setSignImg(value) {
      //更新签名图片，实现数据双向绑定
      this.$emit("input", value);
    }
  },
  mounted() {
    //通过window.resize或window.orientation判断横屏竖屏状态，设置对应宽高，解决移动端屏幕旋转，canvas触控点失灵，签名位置错乱问题
    window.addEventListener("resize", this.initCanvas, false);
  }
};
</script>

<style lang="less" scoped>
.sign-dialog {
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.sign-dialog-content {
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80%;
  height: 80%;
  position: relative;
}
.sign-close {
  position: absolute;
  right: 20px;
  top: 10px;
}

.clear-btn {
  font-size: 14px;
  color: white;
  border-radius: 6px;
  background: #ff6262;
  padding: 0 20px;
  line-height: 42px;
}

.confirm-btn {
  font-size: 14px;
  color: white;
  border-radius: 6px;
  background: #28d094;
  margin-left: 30px;
  padding: 0 20px;
  line-height: 42px;
}

.sign-canvas-wrap {
  border-radius: 15px;
  border: 2px dashed #cccccc;
  flex: 1;
  margin: 30px 44px 20px 44px;
  width: 80%;
  height: 60%;
}

.sign-canvas {
  border-radius: 15px;
  width: 100%;
  height: 100%;
}

.btn-wrap {
  display: flex;
  margin-bottom: 30px;
  height: 42px;
}
</style>
