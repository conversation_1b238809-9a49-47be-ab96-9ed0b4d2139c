<!--电子签名-->
<template>
  <div class="page-wrap">
    <!--电子签名按钮-->
    <div class="sign-btn-wrap" @click="openSignDialog" v-if="!signImg">
      <img
        src="../../../../assets/icons/icon_esign.png"
        height="28"
        width="28"
      />
    </div>

    <!--电子签名图片-->
    <div @click="openSignDialog" class="sign-preview-img" v-if="signImg">
      <img
        :src="signImg"
        width="100%"
        height="100%"
        style="object-fit: contain;"
      />
    </div>

    <!--签名弹框-->
    <sign-dialog
      :show="showSignDialog"
      @close="showSignDialog = false"
      v-model="signImg"
    />
  </div>
</template>

<script>
import SignDialog from "./SignDialog.vue";
export default {
  name: "SignBoard",
  components: { SignDialog },
  data() {
    return {
      showSignDialog: false, //是否显示签名弹框
      signImg: "",
      disabled: false,
    };
  },
  props: {
    formCreateInject: {
      type: Object
    }
  },
  mounted() {
    const rule = this.$props.formCreateInject.rule;
    this.disabled = rule.props?.disabled || false;
    const imgUrl = rule.value;
    if (imgUrl) {
      this.signImg = imgUrl;
    }
  },
  watch: {
    signImg(newValue) {
      //自定义组件v-model绑定的value值，实现数据双向绑定
      this.$emit("input", newValue);
    }
  },
  methods: {
    openSignDialog() {
      if (!this.disabled) {
        this.showSignDialog = true;
      }
    }
  }
};
</script>

<style scoped>
.page-wrap {
  display: flex;
  flex-direction: row;
}
.sign-btn-wrap {
  width: 58px;
  height: 58px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 2px 2px 5px rgb(0 0 0 / 10%);
  border: 1px solid #c0ccda;
  border-radius: 4px;
}

.sign-preview-img {
  width: 116px;
  height: 58px;
  background: rgba(0, 0, 0, 0.02);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 2px 2px 5px rgb(0 0 0 / 10%);
  border: 1px solid #c0ccda;
  border-radius: 4px;
}
</style>
