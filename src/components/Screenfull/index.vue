<template>
  <div @click.stop="click" >
    <svg-icon :style="{'color': fontColor }" :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'"/>
    <span v-if="showFont">
      全屏
    </span>
  </div>
</template>

<script>
import screenfull from 'screenfull';

export default {
  name: 'Screenfull',
  props: {
    showFont: {
      type: Boolean,
      default: true
    },
    fontColor: {
      type: String,
      default: ""
    },
  },
  data() {
    return {
      isFullscreen: false
    };
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    this.destroy();
  },
  methods: {
    click() {
      if (!screenfull.enabled) {
        this.$message({
          message: 'you browser can not work',
          type: 'warning'
        });
        return false;
      }
      const element = document.getElementsByClassName('app-container')[0];//指定全屏区域元素
      screenfull.toggle(element);
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen;
      this.$emit("changeFullscreen", screenfull.isFullscreen)
    },
    init() {
      if (screenfull.enabled) {
        screenfull.on('change', this.change);
      }
    },
    destroy() {
      if (screenfull.enabled) {
        screenfull.off('change', this.change);
      }
    }
  }
};
</script>

<style scoped>
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
