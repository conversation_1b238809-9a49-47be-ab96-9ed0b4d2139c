<template>
  <div>
    <el-timeline :hide-timestamp="true" v-if="list && list.length > 0">
      <el-timeline-item
        placement="top"
        v-for="(item, index) in list"
        :key="index"
        class="timeline"
        :color="index === 0 ? 'rgb(84, 162, 136)' : ''"
        :icon="index === 0 ? 'el-icon-more' : ''"
        size="large"
      >
        <el-card>
          <div
            style="margin-bottom: 16px;display:flex;justify-content:space-between"
          >
            <div>
              <span class="timeline-title">{{
                item[operateTypeTitle] || "--"
              }}</span>
            </div>
            <div>
              <span v-if="item[operatorNameTitle]"
                >操作人：{{ item[operatorNameTitle] }}</span
              >
            </div>
            <div>
              <span>{{ item[createTimeTitle] }}</span>
            </div>
          </div>
          <el-row class="overflowWrap"> {{ item[operateDetailTitle] }}</el-row>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    //操作类型
    operateTypeTitle: {
      type: String,
      default: "operateType",
    },
    //操作人
    operatorNameTitle: {
      type: String,
      default: "operatorName",
    },
    //操作时间
    createTimeTitle: {
      type: String,
      default: "createTime",
    },
    //说明备注
    operateDetailTitle: {
      type: String,
      default: "operateDetail",
    },
  },
};
</script>

<style scoped lang="less">
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
