//节点流程时间线
<template>
  <div>
    <el-timeline :hide-timestamp="true" v-if="list && list.length > 0">
      <el-timeline-item
        placement="top"
        v-for="(item, index) in list"
        :key="index"
        class="timeline"
        :color="index === 0 ? 'rgb(84, 162, 136)' : ''"
        :icon="index === 0 ? 'el-icon-more' : ''"
        size="large"
      >
        <el-card :header="item.nodeName" class="node-card">
          <div class="node-content">
            <div class="node-item">
              <div class="node-item-title">节点处理人</div>
              <div>{{ item.assignee }}</div>
            </div>
            <div class="node-item" v-if="item.handleAging">
              <div class="node-item-title">应处理时间</div>
              <div>{{ item.planEndTime }}</div>
            </div>
            <div class="node-item">
              <div class="node-item-title">实际处理时间</div>
              <div>{{ item.endTime }}</div>
            </div>
            <div class="node-item">
              <div class="node-item-title">被驳回次数</div>
              <div>{{ item.backTimes }}次</div>
            </div>
            <div class="node-item">
              <div class="node-item-title">该节点耗时</div>
              <div>{{ item.durationInMillis }}</div>
            </div>
            <div class="node-item" v-if="item.handleAging">
              <div class="node-item-title">该节点标准时效</div>
              <div>{{ item.handleAging }}h</div>
            </div>
            <div class="node-item">
              <div class="node-item-title">加单工时</div>
              <div>{{ item.addOrderTime }}h</div>
            </div>
          </div>
          <div
            :class="[
              'exceed',
              item.timeOutFlag == 'Y'
                ? item.preOutFlag == 'Y'
                  ? 'exceed-danger'
                  : 'exceed-warning'
                : '',
            ]"
          >
            {{ item.timeDesc }}
          </div>
          <!-- <div class="exceed exceed-danger">已超时 80天23小时59分钟</div>
          <div class="exceed">未超时</div> -->
        </el-card>
      </el-timeline-item>
    </el-timeline>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    //操作类型
    operateTypeTitle: {
      type: String,
      default: "operateType",
    },
    //操作人
    operatorNameTitle: {
      type: String,
      default: "operatorName",
    },
    //操作时间
    createTimeTitle: {
      type: String,
      default: "createTime",
    },
    //说明备注
    operateDetailTitle: {
      type: String,
      default: "operateDetail",
    },
  },
};
</script>

<style scoped lang="less">
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
.node-card /deep/ .el-card__body {
  display: flex;
  justify-content: space-between;
  .exceed {
    width: 300px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid rgb(122, 199, 86);
    background: rgb(238, 248, 232);
    color: rgb(122, 199, 86);
  }
  .exceed-warning {
    border-color: rgb(226, 152, 54);
    color: rgb(226, 152, 54);
    background: rgb(253, 245, 233);
  }
  .exceed-danger {
    border-color: rgb(242, 97, 97);
    background: rgb(242, 97, 97);
    color: #fff;
  }
  .node-content {
    width: 400px;
    .node-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      margin: 10px;
      &-title {
        color: rgb(174, 174, 174);
      }
    }
  }
}
</style>
