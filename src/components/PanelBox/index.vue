<!-- @format -->
<template>
  <panel class="panel-box">
    <div class="box-top">
      <div class="box-top-left">
        <div class="box-title-box">
          <slot name="before-title"></slot>
          <div class="title-split"></div>
          <div class="title">{{ title }}</div>
          <div class="desc">{{ desc }}</div>
        </div>
        <slot name="top-left"></slot>
      </div>
      <div class="box-top-right">
        <slot name="top-right"></slot>
      </div>
    </div>
    <div class="box-content"><slot></slot></div>
  </panel>
</template>

<script>
import Panel from '../Panel/index.vue';

export default {
  name: 'PanelBox',
  components: {
    Panel
  },
  props: {
    title: {
      type: String
    },
    desc: {
      type: String
    }
  }
};
</script>

<style scoped lang="less">
.panel-box {
  padding: 0;
  /deep/ .panel {
    box-shadow: none;
    border-radius: 0 !important;
  }
  .box-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f6f9fc;
    height: 32px;
    line-height: 32px;
    padding: 0 10px;
    .box-top-left {
      .box-title-box {
        display: flex;
        align-items: center;
        .title-split {
          width: 2px;
          height: 12px;
          background: @--color-primary;
          margin-right: 7px;
        }
        .title {
          font-size: 12px;
          color: #292f38;
          line-height: 24px;
        }
        .desc {
          font-size: 14px;
          color: rgba(153, 153, 153, 1);
          line-height: 24px;
          margin-left: 9px;
        }
      }
    }
  }
  .box-content {
    width: 100%;
    padding: 10px 20px 30px;
  }
}
</style>
