//根据URL在线预览pdf/docx/xlsx格式文件
<template>
  <div>
    <div v-if="getFileType(fileUrl) == 'docx'" v-loading="loading">
      <vue-office-docx
        :src="url"
        :style="customStyle"
        @rendered="rendered"
        @error="errorHandler"
      />
    </div>
    <div v-else-if="getFileType(fileUrl) == 'xlsx'" v-loading="loading">
      <vue-office-excel
        :src="url"
        style="height: 75vh;overflow: auto;width: 1000px"
        @rendered="rendered"
        @error="errorHandler"
      />
    </div>
    <div v-else-if="getFileType(fileUrl) == 'pdf'">
      <iframe :src="fileUrl" style="height:75vh;width: 1000px;" />
      <!-- <vue-office-pdf
        :src="url"
        style="height: 75vh;overflow: auto;width: 1000px"
        @rendered="rendered"
        @error="errorHandler"
      /> -->
    </div>
    <div class="fx-unsupport-file-preview" v-else>
      <svg-icon iconClass="icon-file" className="icon-svg" />
      <div class="preview-file-tip">
        当前文件类型不支持在线预览
      </div>
      <div class="preview-file-tip">
        <a @click="handleExport" class="download-link">点击下载到本地查看</a>
      </div>
    </div>
  </div>
</template>

<script>
import { downLoadUrl2Blob, getNameFromUrl } from "@/api/common.js";
import { fileDownLoad } from "@/utils/downLoad.js";

export default {
  props: {
    fileUrl: { type: String, default: "" },
    fileName: { type: String, default: "" },
    customStyle: {
      type: String,
      default: "height: 75vh;overflow: auto;width: 1000px",
    },
  },
  data() {
    return { url: "", loading: false, isFailed: false };
  },
  watch: {
    fileUrl: {
      handler(newVal) {
        if (newVal) {
          this.isFailed = false;
          this.handleDownload(newVal);
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleExport() {
      this.loading = true;
      downLoadUrl2Blob({ fileUrl: this.fileUrl })
        .then(async (res) => {
          if (res) {
            await fileDownLoad(
              res,
              this.fileName || getNameFromUrl(this.fileUrl)
            );
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleDownload(val) {
      this.loading = true;
      downLoadUrl2Blob({ fileUrl: val })
        .then(async (res) => {
          this.loading = false;
          if (res) {
            res.data?.arrayBuffer().then((res) => {
              this.url = res;
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 获取文件类型
    getFileType(fileUrl) {
      if (fileUrl !== "" && fileUrl != null && fileUrl !== undefined) {
        const fileType = fileUrl
          .split(".")
          .pop()
          .toLowerCase(); // 文件的扩展名（格式）
        console.log("type:", fileType);
        return fileType;
      } else {
        return "docx";
      }
    },
    // 渲染结果
    rendered() {
      console.log("渲染完成");
    },

    errorHandler(err) {
      this.isFailed = true;
      console.log(err, "err");
      this.$notify.error("加载失败，请检查文件是否加密！");
    },
  },
};
</script>

<style lang="less" scoped>
.fx-unsupport-file-preview {
  text-align: center;
  .preview-file-tip {
    color: #fff;
    margin-top: 20px;
    .download-link {
      text-decoration: underline;
      &:hover {
        color: #35b096;
      }
    }
  }
}
.icon-svg {
  width: 60px;
  height: 60px;
}
/deep/ .docx {
  width: 100% !important;
}
</style>
