//文件预览器（参考el-image-viewer和简道云样式封装）
<template>
  <transition name="viewer-fade">
    <div
      tabindex="-1"
      ref="el-image-viewer__wrapper"
      class="el-image-viewer__wrapper"
      :style="{ 'z-index': viewerZIndex }"
    >
      <div class="el-image-viewer__mask" @click.self="handleMaskClick"></div>
      <!-- CLOSE -->
      <!-- <span class="el-image-viewer__btn el-image-viewer__close" @click="hide">
        <i class="el-icon-close"></i>
      </span> -->
      <div class="preview-header">
        <div class="file-counter">{{ index + 1 + "/" + totalNum }}</div>
        <div class="file-name">{{ docName }}</div>
        <el-button
          class="download-btn"
          icon="el-icon-download"
          type="text"
          @click="handleDownload(urlList[index])"
          :loading="downloadLoading"
          >下载</el-button
        >
        <div class="close-btn" @click="hide">
          <i class="el-icon-close"></i>
        </div>
      </div>
      <!-- ARROW -->
      <template v-if="!isSingle">
        <span
          class="el-image-viewer__btn el-image-viewer__prev"
          :class="{ 'is-disabled': !infinite && isFirst }"
          @click="prev"
        >
          <i class="el-icon-arrow-left" />
        </span>
        <span
          class="el-image-viewer__btn el-image-viewer__next"
          :class="{ 'is-disabled': !infinite && isLast }"
          @click="next"
        >
          <i class="el-icon-arrow-right" />
        </span>
      </template>
      <div
        v-for="(x, i) in urlList"
        :key="x[options.url]"
        v-if="i === index"
        class="el-image-viewer__wrapper"
      >
        <!-- ACTIONS -->
        <div
          class="el-image-viewer__btn el-image-viewer__actions"
          v-if="showImg(x[options.url])"
        >
          <div class="el-image-viewer__actions__inner">
            <i class="el-icon-zoom-out" @click="handleActions('zoomOut')"></i>
            <i class="el-icon-zoom-in" @click="handleActions('zoomIn')"></i>
            <i class="el-image-viewer__actions__divider"></i>
            <i :class="mode.icon" @click="toggleMode"></i>
            <i class="el-image-viewer__actions__divider"></i>
            <i
              class="el-icon-refresh-left"
              @click="handleActions('anticlocelise')"
            ></i>
            <i
              class="el-icon-refresh-right"
              @click="handleActions('clocelise')"
            ></i>
          </div>
        </div>
        <!-- CANVAS -->
        <div class="el-image-viewer__canvas">
          <img
            ref="img"
            class="el-image-viewer__img"
            :src="currentImg"
            :style="imgStyle"
            @load="handleImgLoad"
            @error="handleImgError"
            @mousedown="handleMouseDown"
            v-if="showImg(x[options.url])"
          />
          <video
            style="height: 80vh;"
            v-else-if="showVideo(x[options.url])"
            :src="x[options.url]"
            controls
          ></video>
          <OfficeFiles
            v-else
            :fileUrl="x[options.url]"
            :fileName="x[options.name]"
          ></OfficeFiles>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { on, off } from "element-ui/src/utils/dom";
import { rafThrottle, isFirefox } from "element-ui/src/utils/util";
import { PopupManager } from "element-ui/src/utils/popup";
import OfficeFiles from "./vueOfficeFiles.vue";
const Mode = {
  CONTAIN: {
    name: "contain",
    icon: "el-icon-full-screen",
  },
  ORIGINAL: {
    name: "original",
    icon: "el-icon-c-scale-to-original",
  },
};

const mousewheelEventName = isFirefox() ? "DOMMouseScroll" : "mousewheel";
import { downloadUrl, fileDownLoad } from "@/utils/downLoad.js";
import { downLoadUrl2Blob } from "@/api/common.js";
// let prevOverflow = "";
export default {
  name: "previewFiles",
  components: { OfficeFiles },
  props: {
    urlList: {
      type: Array,
      default: () => [],
    },
    zIndex: {
      type: Number,
      default: 2999,
    },
    onSwitch: {
      type: Function,
      default: () => {},
    },
    onClose: {
      type: Function,
      default: () => {},
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
    appendToBody: {
      type: Boolean,
      default: true,
    },
    maskClosable: {
      type: Boolean,
      default: true,
    },
    fileOptions: {
      type: Object,
      default: () => {
        return {};
      },
    },
    initPrevOverflow: {
      type: String,
      default: document.body.style.overflow,
    },
  },

  data() {
    return {
      index: this.initialIndex,
      isShow: false,
      infinite: true,
      loading: false,
      mode: Mode.CONTAIN,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
      downloadLoading: false,
    };
  },
  computed: {
    docName() {
      return this.urlList[this.index]?.[this.options.name];
    },
    options() {
      return {
        url: "url",
        name: "name",
        ...this.fileOptions,
      };
    },
    totalNum() {
      return this.urlList?.length;
    },
    isSingle() {
      return this.urlList.length <= 1;
    },
    isFirst() {
      return this.index === 0;
    },
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    currentImg() {
      return this.urlList[this.index]?.[this.options.url];
    },
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? "transform .3s" : "",
        "margin-left": `${offsetX}px`,
        "margin-top": `${offsetY}px`,
      };
      if (this.mode === Mode.CONTAIN) {
        style.maxWidth = style.maxHeight = "100%";
      }
      return style;
    },
    viewerZIndex() {
      const nextZIndex = PopupManager.nextZIndex();
      return this.zIndex > nextZIndex ? this.zIndex : nextZIndex;
    },
  },
  watch: {
    index: {
      handler: function(val) {
        this.reset();
        this.onSwitch(val);
      },
    },
    currentImg(val) {
      this.$nextTick(() => {
        const $img = this.$refs.img?.[0];
        if (!$img?.complete) {
          this.loading = true;
        }
      });
    },
  },
  methods: {
    handleDownload(item) {
      this.downloadLoading = true;
      downLoadUrl2Blob({ fileUrl: item[this.options.url] })
        .then(async (res) => {
          if (res) {
            await fileDownLoad(res, item[this.options.name]);
            this.downloadLoading = false;
          }
        })
        .catch(() => {
          this.downloadLoading = false;
        });
      // console.log("item:", item);
      // downloadUrl(item[this.options.url], item[this.options.name]);
    },
    // 获取文件类型
    getFileType(fileUrl) {
      if (fileUrl !== "" && fileUrl != null && fileUrl !== undefined) {
        const fileType = fileUrl
          .split(".")
          .pop()
          .toLowerCase(); // 文件的扩展名（格式）
        console.log("type:", fileType);
        return fileType;
      } else {
        return "docx";
      }
    },
    showImg(str) {
      const imgDict = "jpg|png|jpeg";
      const imgRegex = new RegExp(imgDict, "i");
      return str?.match(imgRegex);
    },
    showVideo(str) {
      const videoDict = "mp4|3gp|mov";
      const regex = new RegExp(videoDict, "i");
      return str?.match(regex);
    },
    hide() {
      this.deviceSupportUninstall();
      this.onClose();
      document.body.style.overflow = this.initPrevOverflow;
      console.log(document.body.style.overflow, "======");
    },
    deviceSupportInstall() {
      this._keyDownHandler = (e) => {
        e.stopPropagation();
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions("zoomIn");
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions("zoomOut");
            break;
        }
      };
      this._mouseWheelHandler = rafThrottle((e) => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        if (delta > 0) {
          this.handleActions("zoomIn", {
            zoomRate: 0.015,
            enableTransition: false,
          });
        } else {
          this.handleActions("zoomOut", {
            zoomRate: 0.015,
            enableTransition: false,
          });
        }
      });
      on(document, "keydown", this._keyDownHandler);
      on(document, mousewheelEventName, this._mouseWheelHandler);
    },
    deviceSupportUninstall() {
      off(document, "keydown", this._keyDownHandler);
      off(document, mousewheelEventName, this._mouseWheelHandler);
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },
    handleImgLoad(e) {
      this.loading = false;
    },
    handleImgError(e) {
      this.loading = false;
      e.target.alt = "加载失败";
    },
    handleMouseDown(e) {
      if (this.loading || e.button !== 0) return;

      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      on(document, "mousemove", this._dragHandler);
      on(document, "mouseup", (ev) => {
        off(document, "mousemove", this._dragHandler);
      });

      e.preventDefault();
    },
    handleMaskClick() {
      if (this.maskClosable) {
        this.hide();
      }
    },
    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      };
    },
    toggleMode() {
      if (this.loading) return;

      const modeNames = Object.keys(Mode);
      const modeValues = Object.values(Mode);
      const index = modeValues.indexOf(this.mode);
      const nextIndex = (index + 1) % modeNames.length;
      this.mode = Mode[modeNames[nextIndex]];
      this.reset();
    },
    prev() {
      if (this.isFirst && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
    },
    next() {
      if (this.isLast && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case "zoomOut":
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case "zoomIn":
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case "clocelise":
          transform.deg += rotateDeg;
          break;
        case "anticlocelise":
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    },
  },
  mounted() {
    // console.log("mounted");
    // don't show viewer when preview is false
    // prevent body scroll
    // prevOverflow = document.body.style.overflow;
    document.body.style.overflow = "hidden";
    // console.log("prevOverflow:", prevOverflow);
    this.deviceSupportInstall();
    if (this.appendToBody) {
      document.body.appendChild(this.$el);
    }
    // add tabindex then wrapper can be focusable via Javascript
    // focus wrapper so arrow key can't cause inner scroll behavior underneath
    this.$refs["el-image-viewer__wrapper"].focus();
  },
  destroyed() {
    // if appendToBody is true, remove DOM node after destroy
    if (this.appendToBody && this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  },
};
</script>
<style lang="less" scoped>
.preview-header {
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  height: 50px;
  line-height: 50px;
  position: relative;
  z-index: 3000;
  .file-counter {
    color: #b5b8be;
    display: inline-block;
    padding-left: 20px;
  }
  .file-name {
    bottom: 0;
    display: inline-block;
    left: 60px;
    overflow: hidden;
    position: absolute;
    right: 200px;
    text-overflow: ellipsis;
    top: 0;
    white-space: nowrap;
  }
  .download-btn {
    color: inherit;
    display: block;
    font-size: 15px;
    height: 50px;
    outline: none;
    padding: 0 20px;
    position: absolute;
    right: 50px;
    text-decoration: none;
    top: 0;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
  }
  .close-btn {
    cursor: pointer;
    font-size: 20px;
    height: 50px;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    width: 50px;
  }
}
</style>
