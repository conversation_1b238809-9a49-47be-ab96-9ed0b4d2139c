<!-- 步骤条 -->
<template>
  <div class="step-wrap">
    <div v-for="(item, index) in List" :key="index">
      <div class="step-label">{{ item.label }}</div>
      <img v-if="!showFormatter(index + 1) && (index !== (List.length -1))" :src="imgUrl1" alt="" />
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      imgUrl1: require("@/assets/icons/step1.png"),
      imgUrl2: require("@/assets/icons/step2.png"),
      imgUrl3: require("@/assets/icons/step3.png"),
      List: [
        {
          id: "1",
          label: "节点1",
        },
        {
          id: "2",
          label: "节点2",
        },
        {
          id: "3",
          label: "节点3",
        },
        {
          id: "4",
          label: "节点4",
        },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
    showFormatter(val) {
      let result = val / 3;
      if (result !== 0 && Number.isInteger(result)) {
        // num是整数
        console.log("[ 整数 ] >", result);
        return true;
      } else {
        return false;
      }
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped>
.step-wrap {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding-left: 1vw;
  > div {
    width: 33.3%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .step-label {
    border: 1px solid white;
    width: 70%;
    display: flex;
    justify-content: center;
    background-color: rgba(170, 170, 170, 1);
  }
  img {
    width: 30%;
  }
}
</style>
