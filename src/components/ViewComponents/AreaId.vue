<!-- 区域、小区select组件 -->
<template>
  <div class="">
    <el-col :span="span">
      <el-form-item label="所属区域" prop="districtId">
        <el-select
          v-model="queryParams.districtId"
          placeholder="请选择所属区域"
          @change="commIdQuery"
          clearable
          size="mini"
        >
          <el-option
            v-for="item in regionlist"
            :key="item.districtId"
            :label="item.districtName"
            :value="item.districtId"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="span">
      <el-form-item label="所属小区" prop="areaId">
        <el-select
          v-model="queryParams.areaId"
          placeholder="请选择所属小区"
          clearable
          size="mini"
        >
          <el-option
            v-for="dict in villagelist"
            :key="dict.areaId"
            :label="dict.areaName"
            :value="dict.areaId"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { queryNormalCode } from "@/api/crm/dealservice/servicedeal"; //区域
import { list as commIdsList } from "@/api/crm/customer/village"; //小区

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    span: {
      type: Number,
      default: 6,
    },
  },
  data() {
    //这里存放数据
    return {
      queryParams: {},
      //所属区域
      regionlist: [],
      //所属小区
      villagelist: [],
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    //获取小区
    commIdQuery(item) {
      commIdsList({ districtId: item }).then(response => {
        this.villagelist = response.data;
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    //区域
    queryNormalCode().then(res => {
      this.regionlist = res.data.regionList;
    });
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>