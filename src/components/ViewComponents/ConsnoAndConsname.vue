<!-- 模糊搜索 用户编号和用户名称 -->
<template>
  <div class="">
    <el-form-item label="用户" prop="value" style="margin-bottom: -5px;">
      <el-select
        v-model="queryParams.consNoorName"
        filterable
        remote
        clearable
        reserve-keyword
        placeholder="请输入关键词"
        :remote-method="querySearchAsync"
        :loading="loading"
        size="mini"
        class="selection"
      >
        <el-option
          v-for="item in options"
          :key="item.consNo"
          :value="item.consNo"
          :label="item.consName + ' / ' + item.consNo"
        >
          <span v-html="item.dynamicLabel"></span>
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  queryConsNoorConsName,
  queryOrgNoListByUserId
} from "@/api/crm/cdmquery/consInfo";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    orgNoList: {
      type: Array,
      default: null
    },
    meterType: {
      type: String,
      default: null
    },
    status: {
      type: String,
      default: null
    }
  },
  data() {
    //这里存放数据
    return {
      isRequired: false,
      queryParams: {
        consNoorName: ""
      },
      options: [],
      loading: false,
      rules: {
        amtCode: [
          { required: true, message: "请选择费用类型", trigger: "change" }
        ]
      }
    };
  },
  computed: {},
  watch: {
    "queryParams.consNoorName": {
      handler(newVal) {
        if (newVal) {
          this.$emit("changeConsNo", this.queryParams.consNoorName)
          this.$emit("input", this.queryParams.consNoorName)
        }
      }
    }
  },
  //方法集合
  methods: {
    computeLabel(consItem, key) {
      let finalHtml = "";
      finalHtml += this.calcSingleAttr(consItem.consName, key);
      finalHtml += this.genNormalStr(" / ");
      finalHtml += this.calcSingleAttr(consItem.consNo, key);
      finalHtml += this.genNormalStr(" / ");
      finalHtml += this.calcSingleAttr(consItem.addr, key);
      finalHtml += this.genNormalStr(" / ");
      finalHtml += this.calcSingleAttr(consItem.oldConsNo, key);
      return finalHtml;
    },
    calcSingleAttr(attr, keyword) {
      if (!attr) {
        return this.genNormalStr("--");
      }
      let postArr = this.searchSubStr(attr, keyword);

      if (postArr.length <= 0) {
        return this.genNormalStr(attr);
      }
      const len = keyword.length;

      let itemHtml = "";
      let lastMatchEnd = 0;
      // eslint-disable-next-line no-constant-condition
      while (true) {
        if (lastMatchEnd >= attr.length) {
          break;
        }
        if (postArr.includes(lastMatchEnd)) {
          itemHtml += this.genKeyStr(attr.substr(lastMatchEnd, len));
          lastMatchEnd += len;
        } else {
          itemHtml += this.genNormalStr(attr.substr(lastMatchEnd, 1));
          lastMatchEnd++;
        }
      }
      return itemHtml;
    },

    genNormalStr(str) {
      return "<span>" + str + "</span>";
    },
    genKeyStr(str) {
      return (
        "<span style='color: #1a70ff; font-weight: bold'>" + str + "</span>"
      );
    },

    searchSubStr(str, subStr) {
      let positions = [];
      let pos = str.indexOf(subStr);
      while (pos > -1) {
        positions.push(pos);
        pos = str.indexOf(subStr, pos + 1);
      }
      return positions;
    },
    querySearchAsync(val) {
      if (val != "") {
        this.loading = true;
        let params = {
          consNo: val,
          pageNum: 1,
          pageSize: 99,
          orgNoList: this.orgNoList || [this.$store.state.user.orgNo],
          meterType: this.meterType,
          status: this.status
        };
        queryConsNoorConsName(params)
          .then(res => {
            if (res.total > 0) {
              let arr = res.data;
              for (let i = 0; i < arr.length; i++) {
                arr[i].dynamicLabel = this.computeLabel(arr[i], val);
              }
              this.options = arr;
              this.loading = false;
            } else {
              this.options = [];
              this.loading = false;
            }
          })
          .catch(() => {
            this.loading = false;
          });
      } else {
        this.options = [];
      }
    },
    handleSelect(val) {
      this.queryParams.consNoorName = val.value;
    },
    reset() {
      this.queryParams.consNoorName = "";
    },
    changeRequired() {
      if (this.isRequired == true) {
        this.isRequired = false;
      } else if (this.isRequired == false) {
        this.isRequired = true;
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {}
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
.selection {
  height: 32px;
  line-height: 32px;
}
</style>
