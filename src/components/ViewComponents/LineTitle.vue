<!-- line-title组件 -->
<template>
  <div class="">
    <div class="flex-box">
      <div class="line-left"></div>
      <span class="font" :style="{'font-size':fontSize + 'px'}">{{ title }}</span>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: {
      type: String,
      default: "标题"
    },
    fontSize: {
      type: String,
      default: "16"
    }
  },
  data() {
    //这里存放数据
    return {};
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.font {
  font-family: "MicrosoftYaHei-Bold", "微软雅黑 Bold", "微软雅黑", sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 16px;
  text-align: left;
}
.flex-box {
  display: flex;
  align-items: center;
  height: 30px;
  margin-bottom: 20px;
  .line-left {
    background-color: #1098f7;
    width: 5px;
    height: 15px;
  }
  .font{
    padding-left: 10px;
  }
}
</style>