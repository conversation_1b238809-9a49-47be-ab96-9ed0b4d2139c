<!-- vue-treeselect 多选有问题 -->
<template>
  <div class="">
    <el-col :span="6">
      <el-form-item label="公司" :prop="propName">
        <vueTreeselect
          v-model="queryParams.value"
          :options="deptOptions"
          :multiple="multiple"
          :flat="flat"
          noOptionsText="暂无数据"
          placeholder="请选择"
          :limitText="(count) => `更多+ ${count}`"
        />
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import vueTreeselect from "@riophae/vue-treeselect";
// import the styles
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { permsTreeSelect } from "@/api/components/index.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: { vueTreeselect },
  props: {
    multiple: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    flat: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    limit: {
      type: String,
      default: "1",
    },
    isOpenPrivilege: {
      type: String,
      default: "0"
    },
    //form表单校验规则名称
    propName: {
      type: String,
      default: "",
    },
  },
  data() {
    //这里存放数据
    return {
      deptOptions: [],
      queryParams: {
        value: this.$store.getters.deptId || "",
      },
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    getTreeselect() {
      let params = {
        isOpenPrivilege: this.isOpenPrivilege
      };
      permsTreeSelect(params).then((response) => {
        this.deptOptions = response.data;
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getTreeselect();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>