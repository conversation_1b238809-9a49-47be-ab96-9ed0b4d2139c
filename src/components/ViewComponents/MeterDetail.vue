<template>
  <div class="app-container">
    <div class="queryParamsWrap">
      <el-form :model="form" :inline="true">
        <el-row>
          <el-col :span="8">
            <el-form-item label="用户编号：">
              <el-input v-model="form.consNo" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户名称：">
              <el-input v-model="form.consName" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号码：">
              <el-input v-model="form.mobile" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地址：">
              <el-input v-model="form.addr" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表具编号：">
              <el-input v-model="form.madeNo" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上次抄表时间：">
              <el-input v-model="form.lastMrDate" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="本次读数：">
              <el-input v-model="form.thisRead" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上次读数：">
              <el-input v-model="form.lastMrNum" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="抄见用量：">
              <el-input v-model="form.thisReadPq" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="调整用量：">
              <el-input v-model="form.calculateQuantity" size="mini" disabled />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="子表分摊用量：">
              <el-input size="mini" disabled />
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="结算用量：">
              <el-input v-model="form.adjPq" size="mini" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上次结算用量：">
              <el-input v-model="form.lastMrPq" size="mini" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="抄表照片："> 
            <el-col :span="24">
              <div v-for="item in pictures" :key="item" class="flex-box-imgs">
                <el-image
                  style="width: 100px; height: 100px"
                  :src="item"
                  :preview-src-list="pictures"
                  :fit="item"
                >
                </el-image>
              </div>
            </el-col>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="抄表位置"> 
              <amap v-if="position.length > 0" :position="position" :locationName="locationName"></amap>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import { queryDataDetail } from "@/api/meterRead/copiedDataQuery.js";
import moment from "moment";
import amap from "@/components/Amap/amap.vue";

export default {
  props: ["queryData", "isArc"],
  components: {amap},
  data() {
    return {
      form: {},
      queryApi: queryDataDetail,
      mrId: this.queryData,
      pictures: [],
      position: [],
      locationName: ""
    };
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getList();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  methods: {
    getList() {
      queryDataDetail(this.mrId).then((res) => {
        this.form = {};
        if(res.data){
          this.form = res.data;
          if(res.data.longitude){
            this.locationName = res.data.location
            this.position[0] = res.data.longitude
            this.position[1] = res.data.latitude
          }
          if(this.form.imgList && this.form.imgList.length > 0){
            this.pictures = this.form.imgList;
          }
        }
      });
    },
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
.flex-box-imgs {
  display: inline-block;
  // margin-left: 17px;
}

</style>
