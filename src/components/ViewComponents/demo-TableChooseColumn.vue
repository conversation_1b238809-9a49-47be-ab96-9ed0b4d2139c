<!-- table 列控制显示隐藏 demo 非组件 -->
<template>
  <div class="">
    <el-row type="flex" justify="end" style="margin-bottom:10px">
      <el-popover placement="right" width="400" trigger="click">
        <el-checkbox-group v-model="colOptions">
          <el-checkbox
            v-for="item in colSelect"
            :label="item"
            :key="item"
          ></el-checkbox>
        </el-checkbox-group>
        <el-button slot="reference" type="primary" icon="el-icon-s-tools"></el-button>
      </el-popover>
    </el-row>
    <el-table :data="tableData" border ref="tableDataRef">
      <el-table-column
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      <el-table-column
        type="index"
        width="55"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column
        v-if="item.istrue"
        v-for="(item, index) in colData"
        :key="index"
        :prop="item.prop"
        :label="item.title"
        :show-overflow-tooltip="true"
        align="center"
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      tableData: [
        {
          name: "李白",
          age: "12",
          address: "无锡市滨湖区",
        },
        {
          name: "安其拉",
          age: "24",
          address: "无锡市新吴区",
        },
      ],
      colData: [
        { title: "名称", istrue: true, prop: "name" },
        { title: "性别", istrue: true, prop: "sex" },
        { title: "年龄", istrue: true, prop: "age" },
        { title: "时间", istrue: true, prop: "time" },
        { title: "事件", istrue: true, prop: "event" },
        { title: "地点", istrue: true, prop: "address" },
      ],
      colOptions: [],
      colSelect: [],
    };
  },
  computed: {},
  watch: {
    colOptions(valArr) {
      var arr = this.colSelect.filter((i) => valArr.indexOf(i) < 0); // 未选中
      this.colData.filter((i) => {
        if (arr.indexOf(i.title) != -1) {
          i.istrue = false;
        } else {
          i.istrue = true;
        }
      });
      this.$nextTick(() => {
        this.$refs.tableDataRef.doLayout(); //table重新加载
      });
    },
  },
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    var _this = this;
    for (let i = 0; i < _this.colData.length; i++) {
      _this.colSelect.push(_this.colData[i].title);
      if (_this.colData[i].title == "名称") {
        //初始化不想展示的列可以放在这个条件里
        continue;
      }
      _this.colOptions.push(_this.colData[i].title);
    }
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>