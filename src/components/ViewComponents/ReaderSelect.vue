<!-- 抄表员select组件 -->
<!--
    /**
     * 调用示例：
     <ReaderNo
              :span="6" //设置自适应宽度
              ref="ReaderNo"
              multiple  //是否多选
              clearable // 可清空选择  默认开启
              size  //设置尺寸  默认mini

      />
-->
<template>
  <div class="">
    <el-col :span="span">
      <el-form-item :label="lable" prop="userid">
        <el-select
          filterable
          v-model="queryParams.userid"
          placeholder="请选择抄表员"
          :clearable="clearable"
          :size="size"
          :multiple="multiple"
        >
          <el-option
            v-for="item in sectUserOptions"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
    </el-col>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getMeterReaderList } from "@/api/components/index.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    lable:{
      type: String,
      default: "抄表员"
    },
    orgNoList: {
      type: Array
      // default: function() {
      //   return []
      // }
      //接口要求传入字符串数组 如：["1","2"]
    },
    span: {
      type: Number,
      default: 6
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: "mini"
    }
  },
  data() {
    //这里存放数据
    return {
      queryParams: {
        userid: this.multiple ? [] : "", //抄表员id
        mrSectId: "", //抄表册id
        mrSectIdList: []
      },
      sectUserOptions: [], //抄表员list
      operatorList: [] //抄表册list
    };
  },
  computed: {},
  watch: {
    orgNoList: {
      handler() {
        this.sectUserList();
      }
    }
  },
  //方法集合
  methods: {
    //抄表员list
    sectUserList() {
      let params = {
        orgNoList: this.orgNoList ? this.orgNoList : (this.$store.state.user.orgNo ? [this.$store.state.user.orgNo.toString()] : undefined) //公司数组
        //父组件传入公司id数组，不传默认当前账号所属公司id数组
      };
      getMeterReaderList(params).then(res => {
        this.sectUserOptions = res.data;
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.sectUserList()
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {}
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
</style>
