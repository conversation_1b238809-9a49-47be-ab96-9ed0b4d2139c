<!-- queryform查询样例 -->
<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="19">
        <div class="queryParamsWrap">
          <el-form
            :model="queryParams"
            ref="queryForm"
            :inline="true"
            label-width="80px"
          >
            <el-row>
              <el-col :span="6">
                <ConsnoAndConsname ref="ConsnoAndConsname" />
              </el-col>
              <el-col :span="6">
                <el-form-item label="日期" prop="name">
                  <el-date-picker
                    v-model="queryParams.daterange"
                    type="daterange"
                    size="mini"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="vue树" prop="tree">
                  <vueTreeselect
                    v-model="queryParams.tree"
                    :options="deptOptions"
                    :multiple="true"
                    :flat="true"
                    :limit="1"
                    :clearable="false"
                    noOptionsText="暂无数据"
                    placeholder="请选择"
                    :limitText="(count) => `更多+ ${count}`"
                  />
                </el-form-item>
              </el-col>
              <ReaderNo :span="6" ref="ReaderNo" />
              <!-- <el-col :span="6">
                <el-form-item label="公司" prop="deptName">
                  <TreeSelect ref="TreeSelect" :clearable="true" multiple checkStrictly />
                </el-form-item>
              </el-col> -->
              <AreaId ref="AreaId" />
              <el-col :span="6">
                <el-form-item label="名称" prop="name">
                  <el-input
                    v-model="queryParams.name"
                    placeholder="请输入名称"
                    clearable
                    size="mini"
                  />
                </el-form-item>
              </el-col>
              <el-col v-show="showAll" :span="6">
                <el-form-item label="名称" prop="name">
                  <el-input
                    v-model="queryParams.name"
                    placeholder="请输入名称"
                    clearable
                    size="mini"
                  />
                </el-form-item>
              </el-col>
              <el-col v-show="showAll" :span="6">
                <el-form-item label="名称" prop="name">
                  <el-input
                    v-model="queryParams.name"
                    placeholder="请输入名称"
                    clearable
                    size="mini"
                  />
                </el-form-item>
              </el-col>
              <el-col v-show="showAll" :span="6">
                <el-form-item label="名称" prop="name">
                  <el-input
                    v-model="queryParams.name"
                    placeholder="请输入名称"
                    clearable
                    size="mini"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-col>
      <el-col :span="5">
        <div class="queryParamsWrap">
          <el-form :inline="true" label-width="80px">
            <el-row type="flex" justify="end" style="flex-wrap: wrap">
              <el-col :span="8">
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >查询</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button icon="el-icon-refresh" size="mini">重置</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button icon="el-icon-upload" size="mini">导出</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item class="toggle-wrap">
                  <el-button
                    type="text"
                    size="mini"
                    v-if="!showAll"
                    @click="showAll = true"
                    icon="el-icon-arrow-down"
                    >展开</el-button
                  >
                  <el-button
                    type="text"
                    size="medium"
                    v-if="showAll"
                    @click="showAll = false"
                    icon="el-icon-arrow-up"
                    >收起</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!-- <el-row type="flex" justify="end">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
              >查询</el-button
            >
            <el-button icon="el-icon-refresh" size="mini">重置</el-button>
            <el-button icon="el-icon-upload" size="mini">导出</el-button>
            <el-button type="text" size="medium" v-if="!showAll" @click="showAll=true" icon="el-icon-arrow-down">展开</el-button>
            <el-button type="text" size="medium" v-if="showAll" @click="showAll=false" icon="el-icon-arrow-up">收起</el-button>
        </el-row> -->
      </el-col>
    </el-row>

    <el-row type="flex" justify="end" style="margin-bottom: 10px">
      <el-button type="primary" icon="el-icon-plus" size="mini">新增</el-button>
    </el-row>
    <el-row>
      <el-table
        v-loading="loading"
        ref="multipleTable"
        :data="tableData"
        border
      >
        <el-table-column label="序号" type="index" align="center" width="50">
          <template slot-scope="scope">
            {{
              (queryParams.pageNum - 1) * queryParams.pageSize +
              scope.$index +
              1
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="consNo"
          align="center"
          min-width="100"
          label="用户编号"
        >
          <template slot-scope="scope">
            <el-link type="primary" @click.stop="jumpPage(scope.row.consNo)">{{
              scope.row.consNo
            }}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户名称"
          align="center"
          min-width="100"
          prop="consName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="用户地址"
          align="center"
          min-width="100"
          prop="addr"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="计量方式"
          align="center"
          min-width="100"
          prop="pqCal"
          :formatter="EBS_PQCalcModeOptionsFormat"
        />
        <el-table-column
          label="账单年月"
          align="center"
          min-width="100"
          prop="rcvblYm"
        />
        <el-table-column
          label="操作"
          align="center"
          min-width="100"
          fixed="right"
          width="120"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              style="margin-right: 10px"
              @click.stop="editItem(scope.row)"
              >修改</el-link
            >
            <el-link
              type="primary"
              style="margin-right: 10px"
              @click.stop="deleteItem(scope.row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="pageTotal > 0"
        :total="pageTotal"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :autoScroll="false"
        @pagination="getList"
      />
    </el-row>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import ConsnoAndConsname from "@/components/ViewComponents/ConsnoAndConsname.vue";
import ReaderNo from "@/components/ViewComponents/ReaderNo.vue";
import AreaId from "@/components/ViewComponents/AreaId.vue";
import TreeSelect from "@/components/ViewComponents/TreeSelect.vue";
import { treeselect } from "@/api/system/dept";

import vueTreeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { permsTreeSelect } from "@/api/components/index.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {
    ConsnoAndConsname,
    ReaderNo,
    TreeSelect,
    AreaId,
    vueTreeselect,
  },
  data() {
    //这里存放数据
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      pageTotal: 0,
      showAll: false,
      isOpenPrivilege: "0",
      deptOptions: [],
      options: [
        {
          value: 1,
          label: "aaa",
        },
      ],
      loading: false,
      tableData: [
        {
          consNo: "2016-05-02",
          consName: "王小虎",
          addr: "上海市普陀区金沙江路 1518 弄",
        },
        {
          consNo: "2016-05-04",
          consName: "王小虎",
          addr: "上海市普陀区金沙江路 1517 弄",
        },
        {
          consNo: "2016-05-01",
          consName: "王小虎",
          addr: "上海市普陀区金沙江路 1519 弄",
        },
        {
          consNo: "2016-05-03",
          consName: "王小虎",
          addr: "上海市普陀区金沙江路 1516 弄",
        },
      ],
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    handleQuery() {
      console.log("公司组织选中节点id", this.$refs.TreeSelect.selectedData);
      // this.getList()
    },
    getList() {
      let params = {
        ...this.queryParams,
        readerNo: this.$refs.ReaderNo.queryParams.readerNo, //获取子组件内的值
      };
      api(params).then((res) => {});
    },
    getTreeselect() {
      let params = {
        isOpenPrivilege: this.isOpenPrivilege, //等于0时候开启权限，其他时候拿下级部门
      };
      permsTreeSelect(params).then((response) => {
        this.deptOptions = response.data;
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getTreeselect();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.toggle-wrap {
  /deep/.el-form-item__content{
    display: flex;
    justify-content: center;
  }
}
</style>