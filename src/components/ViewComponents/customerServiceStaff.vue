<!-- 客服人员---模糊搜索 -->
<template>
  <div class="">
    <el-select
      v-model="name"
      filterable
      remote
      reserve-keyword
      placeholder="请输入关键词"
      :remote-method="getlistByPage"
      :loading="loading"
      size="mini"
    >
      <el-option
        v-for="item in options"
        :key="item.userId"
        :label="item.nickName"
        :value="item.userId"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { listByPage } from "@/api/customerService/statisticsTable.js";

export default {
  components: {},
  props: {
    value: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      name: "",
      userOpts: [],
      loading: false,
      options: [],
    };
  },
  computed: {},
  watch: {
    name(newVal) {
      if (newVal) {
        this.$emit("change", newVal)
      }
    },
    value(newVal) {
      this.name = newVal
    }
  },
  methods: {
    //获取客服列表
    getlistByPage(query) {
      if (query !== '') {
          this.loading = true;
          let params = {
            nickName: query,
          }
          listByPage(params).then(res => {
            this.userOpts = res.data
          })
          setTimeout(() => {
              this.loading = false;
              this.options = this.userOpts.filter(item => {
                  return item.nickName.toLowerCase()
                      .indexOf(query.toLowerCase()) > -1;
              });
          }, 1000);
      } else {
          this.options = [];
      }
    },
  },
  created() {},
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped></style>
