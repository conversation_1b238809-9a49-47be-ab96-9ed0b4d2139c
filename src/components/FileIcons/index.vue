// 文件预览缩略图
<template>
  <div
    :style="
      `display: flex;flex-wrap: wrap; justify-content: ${
        isCenter ? 'center' : 'flex-start'
      }`
    "
  >
    <div
      v-for="(item, index) in formattedList"
      :key="index"
      style="margin-right:5px"
    >
      <el-tooltip
        class="item"
        effect="dark"
        :content="item[options.name]"
        placement="top"
      >
        <div style="position: relative;">
          <div
            class="delete-file"
            @click.stop="handleFileRemove(index)"
            v-if="showRemove"
          >
            ×
          </div>
          <svg-icon
            :iconClass="'icon-' + getFileType(item.docName || item.name)"
            :style="`width: ${iconWidth}px; height: ${iconWidth}px;`"
            className="common-icon"
            v-if="showFile(item[options.name] || item[options.url])"
            @click="clickHandler(item, index)"
          />
          <el-image
            v-else
            class="common-icon"
            :src="item[options.url]"
            @click="clickHandler(item, index)"
            :style="`width: ${iconWidth}px; height: ${iconWidth}px;`"
          ></el-image>
        </div>
      </el-tooltip>
    </div>
    <PreviewFiles
      :initial-index="previewIndex"
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="formattedList"
      :fileOptions="fileOptions"
    />
  </div>
</template>

<script>
import { downloadUrl } from "@/utils/downLoad.js";
import PreviewFiles from "@/components/PreviewFiles/index.vue";

export default {
  components: {
    PreviewFiles,
  },
  props: {
    list: {
      type: [Array, String],
      default: () => [],
    },
    fileOptions: {
      type: Object,
      default: () => {
        return {};
      },
    },
    //每个图标宽度
    iconWidth: {
      type: Number,
      default: 30,
    },
    //是否展示删除图标
    showRemove: {
      type: Boolean,
      default: false,
    },
    //是否居中排列
    isCenter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      fileDict: "doc|docx|mp3|mp4|pdf|ppt|pptx|rar|txt|xls|xlsx|zip",
      imgDict: "jpg|png|jpeg",
      initialIndex: Number,
      showViewer: false,
      src: String,
      previewIndex: 0,
    };
  },
  computed: {
    options() {
      return {
        url: "url",
        name: "name",
        ...this.fileOptions,
      };
    },
    formattedList() {
      if (typeof this.list === "string") {
        return this.list?.length > 0
          ? this.list?.split(",")?.map((y) => {
              return {
                [this.options.url]: y,
                [this.options.name]: y.split("/")?.pop(),
              };
            })
          : [];
      }
      // 判断 list 的类型并格式化
      return this.list
        ?.map((item) => {
          if (typeof item === "string" && item.length > 0) {
            // 如果是 URL 字符串，提取文件名作为 name，URL 本身作为 url
            const name = item.split("/").pop(); // 获取 URL 中最后一个斜杠后的部分
            return { [this.options.name]: name, [this.options.url]: item };
          } else if (
            item &&
            typeof item === "object" &&
            item[this.options.url]
          ) {
            // 如果已经是对象数组，则直接返回
            return item;
          }
          return null; // 过滤掉无效项
        })
        .filter(Boolean); // 过滤掉 null 或无效项
    },
  },
  mounted() {
    // console.log(this.formattedList, "===----");
  },
  methods: {
    handleFileRemove(index) {
      this.$emit("removeItem", index);
    },
    clickHandler(item, index) {
      this.showViewer = true;
      this.previewIndex = index;
    },
    closeViewer() {
      this.showViewer = false;
    },
    // handleDownload(url, fileName) {
    //   // downloadUrl(url, fileName);
    //   this.$refs.previewFiles.open(url);
    // },
    //获取文件类型
    getFileType(str) {
      // 提取后缀名并转小写
      const ext = str?.match(/\.([^.]+)$/)?.[1]?.toLowerCase() || "";
      // 将预设后缀列表转为数组
      const validExts = this.fileDict.split("|");
      // 严格匹配预设列表中的后缀
      return validExts.includes(ext) ? ext : "file";
    },
    //排除图片格式，判断文件格式
    showFile(str) {
      const imgRegex = new RegExp(this.imgDict, "i");
      return !str?.match(imgRegex);
    },
  },
};
</script>

<style lang="less" scoped>
.common-icon {
  cursor: pointer;
  position: relative;
}
.delete-file {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  top: 0px;
  right: 0px;
  z-index: 2;
  position: absolute;
  background: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 6px;
  text-align: center;
  cursor: pointer;
}
</style>
