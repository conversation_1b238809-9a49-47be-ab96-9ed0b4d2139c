<template>
  <el-select
    v-model="selectedValue"
    v-el-select-loadmore="loadMore"
    :remote-method="remoteSearch"
    filterable
    remote
    clearable
    v-bind="$attrs"
    v-on="{ ...$listeners, change: handleChange }"
  >
    <el-option
      v-for="(item, index) in options"
      :key="index"
      :label="item[optionValue]"
      :value="item[optionLabel]"
    />
  </el-select>
</template>

<script>
export default {
  props: {
    value: [String, Number, Array],
    fetchMethod: {
      // 必须返回包含data数组和total的Promise
      type: Function,
      required: true,
    },
    optionValue: {
      type: String,
      default: "value",
    },
    optionLabel: {
      type: String,
      default: "label",
    },
    pageSize: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      selectedValue: null,
      options: [],
      currentPage: 1,
      total: 0,
      searchText: "",
      loading: false,
      hasMore: true,
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.selectedValue = newVal;
      },
    },
  },
  mounted() {
    console.log("选择器");
    this.loadData();
  },
  methods: {
    async loadData() {
      if (this.loading || !this.hasMore) return;

      this.loading = true;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          searchText: this.searchText,
        };

        const res = await this.fetchMethod(params);
        this.options = this.options.concat(res.data);
        this.total = res.total;
        this.hasMore = this.options.length < this.total;
        this.currentPage++;
      } finally {
        this.loading = false;
      }
    },
    remoteSearch(query) {
      this.searchText = query;
      this.resetPagination();
      this.loadData();
    },
    loadMore() {
      if (!this.loading) {
        this.loadData();
      }
    },
    resetPagination() {
      this.currentPage = 1;
      this.options = [];
      this.hasMore = true;
    },
    handleChange(value) {
      this.$emit("input", value);
      this.$emit("change", { value, options: this.options });
    },
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 下拉框下拉的框
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        // 增加滚动监听，
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          // console.log(this.scrollHeight, this.scrollTop, this.clientHeight);
          const condition =
            this.scrollHeight - this.scrollTop - 2 <= this.clientHeight;
          // 当滚动条滚动到最底下的时候执行接口加载下一页
          if (condition) {
            // console.log("进入判断");
            binding.value();
          }
        });
      },
    },
  },
};
</script>

<style></style>
