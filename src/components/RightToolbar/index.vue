<!-- <AUTHOR>   huangmx 20200807优化-->
<template>
  <div class="top-right-btn">
    <el-col :span="3">
      <el-tooltip class="item" effect="dark" :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top">
        <el-button size="mini" circle icon="el-icon-search" @click.stop="toggleSearch()" />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="刷新" placement="top">
        <el-button size="mini" circle icon="el-icon-refresh" @click.stop="refresh()" />
      </el-tooltip>
    </el-col>
  </div>
</template>
<script>
export default {
  name: 'RightToolbar',
  data() {
    return {};
  },
  props: {
    showSearch: {
      type: Boolean,
      default: true
    }
  },

  methods: {
    //搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch);
    },
    //刷新
    refresh() {
      this.$emit('queryTable');
    }
  }
};
</script>
