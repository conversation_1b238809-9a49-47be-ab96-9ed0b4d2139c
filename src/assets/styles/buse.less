.btn-wrap .el-button--small {
  padding: 9px 9px;
}
.bd3001-auto-filters-container .bd3001-fold {
  font-size: 12px !important;
  margin-left: 10px !important;
}
.bd3001-auto-filters-container .bd3001-fold .bd3001-fold-icon {
  padding-left: 4px !important;
  font-size: 12px !important;
}
.bd3001-auto-filters-container .btn-wrapper-center .wrapper-adapter {
  padding: 0 !important;
}
.bd3001-auto-filters-container {
  margin-bottom: 10px;
}
.bd3001-page-wrapper-container .table-title {
  margin: 0;
}

.header-row {
  height: 90px !important;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis) {
  padding: 0px !important;
}

.vxe-table--render-default .vxe-header--column {
  line-height: 18px !important;
}

.vxe-table--render-default .is--disabled.vxe-cell--checkbox {
  background: #edf2fc80 !important;
  border-radius: 2px !important;
}

.vxe-body--column {
  height: 45px !important;
}
/*滚动条整体部分*/
.vxe-grid ::-webkit-scrollbar,
.vxe-table ::-webkit-scrollbar {
  // width: 10px;
  height: 16px !important;
}
.vxe-table--body-wrapper.body--wrapper {
  min-height: unset !important;
}
.vxe-table--render-default .vxe-table--border-line {
  z-index: 6 !important;
}
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active {
  background-color: #029c7c !important;
}
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):focus,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):focus,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):focus,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled).is--active,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):focus {
  color: #029c7c !important;
}
.vxe-pager .vxe-pager--jump-next:not(.is--disabled):hover,
.vxe-pager .vxe-pager--jump-prev:not(.is--disabled):hover,
.vxe-pager .vxe-pager--next-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--num-btn:not(.is--disabled):hover,
.vxe-pager .vxe-pager--prev-btn:not(.is--disabled):hover,
.vxe-select-option.is--selected {
  color: #029c7c !important;
}
.vxe-pager.is--background .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--background .vxe-pager--num-btn:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--jump-next:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--jump-prev:not(.is--disabled).is--active,
.vxe-pager.is--perfect .vxe-pager--num-btn:not(.is--disabled).is--active {
  color: #fff !important;
}
.vxe-pager .vxe-pager--jump .vxe-pager--goto:focus,
.vxe-pager.is--border .vxe-pager--num-btn.is--active,
.vxe-select.is--active:not(.is--filter) > .vxe-input .vxe-input--inner {
  border-color: #029c7c !important;
}
.vxe-pager--num-btn,
.vxe-pager {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif !important;
}
.vxe-pager--num-btn {
  font-weight: 700;
}
.el-dialog__footer > div {
  display: flex;
  justify-content: center;
  .el-button + .el-button {
    margin-left: 30px;
  }
  .el-button--small {
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 4px;
  }
}
.location {
  .el-checkbox,
  .el-radio {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }

  .el-cascader-node {
    padding-left: 34px;
  }

  .el-checkbox__input,
  .el-radio__input {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translate(0, -50%);
  }
}
