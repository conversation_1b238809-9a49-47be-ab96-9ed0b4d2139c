/**
 * 通用css样式布局处理
 * Copyright (c) 2019 Bangdao
 */

/** 基础通用 **/
.pt5 {
  padding-top: 5px;
}
.pr5 {
  padding-right: 5px;
}
.pb5 {
  padding-bottom: 5px;
}
.mt5 {
  margin-top: 5px;
}
.mr5 {
  margin-right: 5px;
}
.mr4 {
  margin-right: 4px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb8 {
  margin-bottom: 8px;
}
.ml5 {
  margin-left: 5px;
}
.mt10 {
  margin-top: 10px;
}
.ml10 {
  margin-left: 10px;
}
.mr10 {
  margin-right: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.ml0 {
  margin-left: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mr20 {
  margin-right: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.m20 {
  margin-left: 20px;
}
.fs18 {
  font-size: 18px;
}

.overflowWrap {
  overflow-wrap: break-word;
  word-wrap: break-word;
}
.el-table .el-table__header-wrapper th {
  word-break: break-word;
  background-color: #f8f8f9;
  color: #515a6e;
  height: 40px;
  font-size: 13px;
}

/** 表单布局 **/
.form-header {
  font-size: 15px;
  color: #6379bb;
  border-bottom: 1px solid #ddd;
  margin: 8px 10px 25px 10px;
  padding-bottom: 5px;
}
/** 表单必填项*的样式增大 */
.el-form-item__label:before {
  font-size: large;
  width: 5px;
}

/** 表格布局 **/
.pagination-container {
  position: relative;
  height: 35px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
}

.pagination-container .el-pagination {
  right: 0;
  position: absolute;
}

.el-table .fixed-width .el-button--mini {
  color: #409eff;
  padding-left: 0;
  padding-right: 0;
  width: inherit;
}

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.list-group-striped > .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  padding-left: 0px;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}

.pull-right {
  float: right !important;
}
.el-card__header {
  padding: 14px 15px 7px;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

.el-tooltip__popper {
  max-width: 400px;
  line-height: 180%;
}
/* text color */
.text-navy {
  color: #1ab394;
}

.text-primary {
  color: inherit;
}

.text-success {
  color: #1c84c6;
}

.text-info {
  color: #23c6c8;
}

.text-warning {
  color: #f8ac59;
}

.text-danger {
  color: #ed5565;
}

.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  width: 180px;
  height: 180px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

.queryParamsWrap {
  .el-form-item {
    margin-bottom: 20px;
    .el-form-item__label {
      // font-weight: normal;
      display: flex;
      align-items: center;
      line-height: inherit;
      font-size: 13px;
      flex-direction: row-reverse;
    }
  }
  .el-col {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.right-btn-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-right: 5px;
    margin-left: 0px;
  }
  :last-child {
    margin-right: 0px;
  }
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 7px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 7px;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: #9b9898;
}
::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #f8f8f9;
  border-radius: 10px;
}

.mydialog {
  // .el-form-item{
  // 	// margin-bottom: 10px;
  // 	label{
  // 		// font-weight: normal;
  // 	}
  // }
  .el-form-item__content {
    & > div {
      width: 100%;
    }
  }
}

.el-dialog__body {
  .app-container {
    padding: 0;
  }
}

//vuetreeselect 样式修改
.queryParamsWrap {
  .vue-treeselect__menu-container {
    //浮层内部样式 写在全局中 浮层被加入到了body里
    font-size: 14px;
    color: #606266;
    font-weight: 100;
  }
}
//vue-treeselect样式
.vue-treeselect__control {
  height: 28px !important;
}
.el-form-item--small .el-form-item__content {
  .vue-treeselect {
    line-height: 13px !important;
  }
}
.el-form-item--mini .el-form-item__content {
  .vue-treeselect {
    line-height: 13px !important;
  }
}
.vue-treeselect__placeholder {
  line-height: 28px !important;
}
.vue-treeselect {
  height: 24px !important;
}
//单选
.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  line-height: 28px !important;
}
//解决发布后高度增加导致样式错开问题
.vueTreeselect {
  margin-bottom: 10px !important;
}

//顶部菜单--更多菜单样式
.el-menu--horizontal > .el-submenu .el-submenu__title {
  height: 50px !important;
  line-height: 50px !important;
}

//页面card模块样式
.card-container {
  margin: 20px;
  padding: 0;
}

//页面内部模块卡片样式
.module-item {
  border-radius: 4px;
  border: 1px solid #ebeef5;
  margin-bottom: 15px;

  //页面内部模块卡片标题部分样式
  .title {
    background-color: rgba(249, 249, 249, 1);
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.required-label {
  width: 135px;
}

.required-label:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

//  vxetable内的自定义文字大小
.table-font {
  font-size: 14px;
}

.big-screen-select-group {
  background-color: #065485 !important;
  .el-select-dropdown__item {
    color: white;
    font-size: 12px;
  }
  .el-select-dropdown__item.hover {
    background-color: #00337e !important;
  }
  .el-select-dropdown__item.selected {
    background-color: #00337e !important;
  }
  .el-select-dropdown__item.selected.hover {
    background-color: #00337e !important;
  }

  .el-select-group__title {
    padding-left: 10px;
    font-size: 14px;
  }
  .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
  .el-select-group__wrap:not(:last-of-type)::after {
    display: none;
  }
}

// 卡片title样式
.card-title-wrap {
  display: flex;
  font-weight: 800;
  align-items: center;
  .card-title-line {
    width: 5px;
    height: 18px;
    background-color: #029c7c;
    margin-right: 10px;
  }
}

.title-line-wrap {
  margin: 10px -10px;
  display: flex;
  justify-content: space-between;

  .left-wrap {
    display: flex;
    align-items: center;
    font-weight: 800;

    .line {
      width: 5px;
      height: 20px;
      background-color: #3f9eff;
      display: inline-block;
      margin-right: 5px;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
  .el-button + .el-button {
    margin-left: 30px;
  }
  .el-button--small {
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 4px;
  }
}
.el-drawer__body {
  position: relative;
  padding: 20px;
  .drawer-body {
    max-height: calc(100vh - 180px);
    overflow-y: auto;
  }
  .drawer-footer {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translate(-50%, 0);
    padding: 20px;
    padding-top: 10px;
    display: flex;
    justify-content: center;
    .el-button + .el-button {
      margin-left: 40px;
    }
    .el-button--medium {
      font-size: 16px;
      border-radius: 4px;
      padding: 14px 26px;
    }
  }
}
.el-message-box__message p {
  max-height: 60vh;
  overflow-y: auto;
}
body .vxe-table--tooltip-wrapper {
  z-index: 30000 !important;
}
//及联选择器选择时选项过长省略
.cascader-node {
  max-width: 200px;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden;
  text-overflow: ellipsis;
}
//点击复制的图标
.pointer-icon {
  cursor: pointer;
  margin-left: 5px;
  color: #bbbbbb;
  &:hover {
    color: #029c7c;
  }
}
.page-center {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24px;
  background: #eff6f4;
  font-size: 14px;
  .title {
    font-size: 18px;
  }
  .count {
    font-size: 20px;
    color: red;
  }
  .unit {
    color: red;
  }
}
