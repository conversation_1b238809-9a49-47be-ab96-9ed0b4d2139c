// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.el-input-group__append {
  background: #fff;
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}
.el-date-editor .el-range-separator {
  width: 20px;
}
.el-dialog__body {
  padding: 10px;
}
.el-dialog__header {
  background-color: #eae9e9;
  height: 60px;
}

.el-collapse-item__header {
  font-weight: 700;
  position: relative;
  padding-left: 30px;
  .el-collapse-item__arrow {
    position: absolute;
    left: 0;
    font-size: 18px;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-notification__title {
  word-break: break-all;
}
.el-notification {
  z-index: 3001 !important;
}
// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-input__inner {
  // border-radius: 0;
}

.el-button {
  // border-radius: 0;
}

.queryParamsWrap .el-form--inline .el-form-item {
  width: 100%;
  display: flex;
  .el-form-item__content {
    font-size: 12px;
    flex: 1;
    // margin-right: 10%;
    & > div {
      width: 100%;
      display: block;
      // display: flex;
    }
  }
}

.el-table th.el-table__cell {
  background-color: #f8f8f9;
}
.el-table thead {
  color: #515a6e;
}

//弹框关闭X 凸显
.el-dialog {
  .el-dialog__headerbtn {
    .el-icon-close {
      font-weight: 800;
      font-size: 25px;
    }
  }
  .el-dialog__body {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    min-height: 40vh;
  }
}

// 日期选择当日、当月、当年取消高亮
.el-date-table td.today span,
.el-month-table td.today .cell,
.el-year-table td.today .cell {
  color: #606266;
  font-weight: normal;
}
.el-date-table td.selected span {
  color: #fff !important;
}
.el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    // max-width: 200px;
    min-width: 120px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
  }
}
.el-button--text {
  user-select: unset !important;
}
.el-card.is-always-shadow {
  margin-bottom: 10px;
}
.el-drawer__header {
  margin-bottom: 16px !important;
}
.el-tooltip__popper {
  max-width: 1000px !important;
}
.el-link.el-link--default {
  color: #029c7c !important;
}
.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover,
.el-cascader-node.is-selectable.in-active-path:hover {
  background-color: rgba(2, 156, 124, 0.9) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
}
.el-range-editor--small .el-range-input {
  font-size: 11px !important;
}
