<template>
  <div class="content-wrap">
    <div class="sub_title">执行人信息</div>
    <el-form :model="form" :rules="rules" ref="form1" :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="acceptanceUser" label="全量验收负责人">
            <el-select
              v-model="form.acceptanceUser"
              filterable
              size="mini"
              placeholder="请选择全量验收负责人"
            >
              <el-option
                v-for="item in userOption"
                :key="item.dictValue"
                :label="item.nickName"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="sub_title">创建工单</div>
    <el-form :model="form2" ref="form2" :inline="true">
      <el-row v-for="(item, index) in form2.typeList" :key="index">
        <el-col :span="12">
          <el-form-item
            label="工单类型"
            :prop="'typeList.' + index + '.flowType'"
            :rules="{
              required: true,
              message: '工单类型不能为空',
              trigger: 'blur',
            }"
          >
            <el-select v-model="item.flowType" size="mini" placeholder="请选择">
              <el-option
                v-for="item in useFlowList"
                :key="item.flowType"
                :label="item.flowTypeName"
                :value="item.flowType"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button
            size="mini"
            v-if="form2.typeList.length > 1"
            @click="delItem(index)"
            >删除</el-button
          >
          <el-button type="primary" size="mini" @click="addItem"
            >添加</el-button
          >
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { listByBusinessType } from "@/api/roadTestAcceptance/otherIndex.js";
export default {
  data() {
    return {
      useFlowList: [], //工单类型
      rules: {
        acceptanceUser: [
          {
            required: true,
            message: "请选择全量验收负责人",
            trigger: "change",
          },
        ],
      },
      form: {
        acceptanceUser: undefined,
      },
      form2: {
        typeList: [
          {
            flowType: "",
          },
        ],
      },
    };
  },
  computed: {
    ...mapState("dataDict", ["userOption"]),
  },
  created() {
    this.getUseFlowList();
  },
  methods: {
    //获取工单类型列表
    async getUseFlowList() {
      const { code, data } = await listByBusinessType({
        businessType: "acceptance",
        app: "SF-CM",
      });
      if (code != 10000) return;
      this.useFlowList = data;
    },
    delItem(index) {
      this.form2.typeList.splice(index, 1);
    },
    addItem() {
      this.form2.typeList.push({
        flowType: "",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.content-wrap {
  margin-top: -20px;
}
/* 标题样式 */
.sub_title {
  margin-left: 10px;
  display: list-item;
  line-height: 40px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 20px;
}
.sub_title::marker {
  content: "| ";
  color: #029c7c;
  font-size: 18px;
  font-weight: bold;
}
</style>
