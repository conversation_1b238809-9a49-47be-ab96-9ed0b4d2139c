<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane
        label="待全量验收"
        name="1"
        v-if="checkPermission(['fullAcceptance:tab:todo'])"
      >
        <waitFullAcceptance v-if="activeName == '1'"></waitFullAcceptance>
      </el-tab-pane>
      <el-tab-pane
        label="全量验收中"
        name="2"
        v-if="checkPermission(['fullAcceptance:tab:doing'])"
      >
        <fullAcceptanceIng v-if="activeName == '2'"></fullAcceptanceIng>
      </el-tab-pane>
      <el-tab-pane
        label="验收完成"
        name="3"
        v-if="checkPermission(['fullAcceptance:tab:done'])"
      >
        <success-acceptance v-if="activeName == '3'"></success-acceptance>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import fullAcceptanceIng from "./fullAcceptanceIng.vue";
import waitFullAcceptance from "./waitFullAcceptance.vue";
import checkPermission from "@/utils/permission.js";
import successAcceptance from "./successAcceptance.vue";
import { FUNLL_ACCEPTANCE_CLICK_TAB } from "@/utils/track/track-event-constants";

export default {
  components: {
    waitFullAcceptance,
    fullAcceptanceIng,
    successAcceptance,
  },
  data() {
    return {
      activeName: "1",
    };
  },
  created() {
    // 人员
    this.$store.dispatch("dataDict/getListUser");
    //获取工单状态字典
    this.$store.dispatch("dataDict/getDicts", "cm_flow_status");
    //业务类型字典
    this.$store.dispatch("dataDict/getDicts", "flow_business_type");
  },
  methods: {
    checkPermission,
    handleTabClick() {
      let tabname="";
      switch (this.activeName){
        case "1":
          tabname="待全量验收";
          break;
        case "2":
          tabname="全量验收中";
          break;
        case "3":
          tabname="验收完成";
          break;
      }

      this.reportTrackEvent(FUNLL_ACCEPTANCE_CLICK_TAB, {
        tabname
      });
    }
  },
};
</script>

<style></style>
