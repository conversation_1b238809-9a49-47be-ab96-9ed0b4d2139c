<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="searchForm.total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="goPage(row)" type="text" size="large">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="startAcceptance(row)" type="text" size="large">
            {{ row.finishCount }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            :disabled="row.status === '11'"
            type="text"
            size="large"
            @click="startAcceptance(row)"
            v-has-permi="['fullAcceptance:wait:start']"
          >
            开始全量验收
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <div v-if="startAcceptanceVisible">
      <el-dialog
        title="开始全量验收"
        :visible.sync="startAcceptanceVisible"
        :close-on-click-modal="false"
        @close="startAcceptanceVisible = false"
        append-to-body
        width="60%"
      >
        <StartAcceptance ref="startAcceptance"></StartAcceptance>
        <div slot="footer" class="dialog-footer">
          <el-button @click.stop="startAcceptanceVisible = false"
            >取 消</el-button
          >
          <el-button type="primary" @click="submitForm">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryProjectInfoByPagee,
  acceptanceAdd,
} from "@/api/fullAcceptance/index.js";
import { regionData } from "element-china-area-data";
import StartAcceptance from "./components/startAcceptance.vue";
export default {
  components: {
    AdvancedForm,
    GridTable,
    StartAcceptance,
  },
  data() {
    return {
      loading: false,
      startAcceptanceVisible: false,
      tableData: [],
      rowData: null,
      searchForm: {
        deviceNo: "",
        realDeviceName: "",
        deviceTypeNo: null,
        stationName: "",
        statusList: ["7"],
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    tableId() {
      return "onlineManageTable";
    },
    columns() {
      return [
        {
          title: "项目编码",
          field: "projectCode",
        },
        {
          field: "projectName",
          title: "项目名称",
        },
        {
          field: "remark",
          title: "项目备注",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
    config() {
      return [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请输入项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请输入项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
  },
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    async acceptanceAdd(data) {
      const res = await acceptanceAdd(data);
      if (res.code === "10000") {
        this.$message.success("操作成功！");
        this.loadData();
        this.startAcceptanceVisible = false;
      }
    },
    startAcceptance(row) {
      this.rowData = $deepClone(row);
      this.startAcceptanceVisible = true;
    },
    //获取项目列表
    async loadData() {
      this.loading = true;
      const res = await queryProjectInfoByPagee(this.searchForm);
      this.loading = false;
      if (res?.code === "10000") {
        this.tableData = res.data;
        this.searchForm.total = res.total;
      }
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      if (Array.isArray(this.searchForm.region)) {
        this.searchForm["provinceList"] = [];
        this.searchForm["cityList"] = [];
        this.searchForm["countyList"] = [];
        this.searchForm.region.forEach((item) => {
          if (item[0]) {
            this.searchForm["provinceList"].push(item[0]);
          }
          if (item[1]) {
            this.searchForm["cityList"].push(item[1]);
          }
          if (item[2]) {
            this.searchForm["countyList"].push(item[2]);
          }
        });
      }
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    submitForm() {
      this.$refs.startAcceptance.$refs.form1.validate((valid1) => {
        this.$refs.startAcceptance.$refs.form2.validate((valid2) => {
          if (valid1 && valid2) {
            const { form, form2, useFlowList } = this.$refs.startAcceptance;
            // let arr = form2.typeList.map((item) => {
            //   return item.flowType;
            // });
            // if (arr.length != Array.from(new Set(arr)).length) {
            //   this.$message.error("存在相同的工单类型，请重新选择！");
            //   return;
            // }
            const {
              stationId,
              stationName,
              projectName,
              projectId,
            } = this.rowData;
            let data = {
              stationId,
              stationName,
              projectName,
              projectId,
              ...form,
              acceptances: form2.typeList.map((item) => {
                return {
                  ...useFlowList.find((obj) => {
                    return obj.flowType == item.flowType;
                  }),
                };
              }),
            };
            this.acceptanceAdd(data);
          }
        });
      });
    },
    //分页切换
    changePage() {
      this.loadData();
    },
  },
};
</script>

<style></style>
