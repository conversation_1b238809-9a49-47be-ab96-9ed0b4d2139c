<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="searchForm.total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceStatus" slot-scope="{ row, $index }">
          <el-button @click="showFlowInfo(row)" type="text" size="large">
            {{ row.flowCount }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <!--          <el-button type="text" size="large" @click="addAcceptance(row)">-->
          <!--            添加全量验收工单-->
          <!--          </el-button>-->
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { queryAcceptanceProjectInfoByPage } from "@/api/fullAcceptance/index.js";
import { regionData } from "element-china-area-data";
import { listUser } from "@/api/common";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      loading: false,
      addAcceptanceVisible: false,
      tableData: [],
      rowData: null,
      userOption: [],
      searchForm: {
        deviceNo: "",
        realDeviceName: "",
        deviceTypeNo: null,
        stationName: "",
        statusList: ["9"],
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    tableId() {
      return "successAcceptance";
    },
    columns() {
      return [
        {
          title: "项目编码",
          field: "projectCode",
        },
        {
          field: "projectName",
          title: "项目名称",
        },
        {
          field: "remark",
          title: "项目备注",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
        },
        {
          field: "acceptanceUser",
          title: "验收负责人",
          formatter: this.userFormat,
        },
        {
          field: "flowCount",
          title: "工单数量",
          slots: { default: "deviceStatus" },
        },
        {
          field: "commitAcceptUser",
          title: "验收确认人",
          formatter: this.userFormat,
        },
      ];
    },
    config() {
      return [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请输入项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请输入项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
  },
  async created() {
    await this.getListUser();
  },
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    userFormat({ cellValue }) {
      for (let i = 0; i < this.userOption.length; i++) {
        if (this.userOption[i].userId == cellValue) {
          return this.userOption[i].nickName;
        }
      }
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    //获取项目列表
    async loadData() {
      this.loading = true;
      const res = await queryAcceptanceProjectInfoByPage(this.searchForm);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.searchForm.total = res.total;
      }
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      if (Array.isArray(this.searchForm.region)) {
        this.searchForm["provinceList"] = [];
        this.searchForm["cityList"] = [];
        this.searchForm["countyList"] = [];
        this.searchForm.region.forEach((item) => {
          if (item[0]) {
            this.searchForm["provinceList"].push(item[0]);
          }
          if (item[1]) {
            this.searchForm["cityList"].push(item[1]);
          }
          if (item[2]) {
            this.searchForm["countyList"].push(item[2]);
          }
        });
      }
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    addAcceptance(row) {
      this.rowData = row;
      this.addAcceptanceVisible = true;
    },
    //分页切换
    changePage() {
      this.loadData();
    },
    showFlowInfo(row) {
      this.$router.push({
        path: "/workOrderWorkBench/workOrderWorkbench",
        query: { projectId: row.projectId },
      });
    },
  },
};
</script>

<style></style>
