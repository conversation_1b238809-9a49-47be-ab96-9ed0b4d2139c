<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="searchForm.total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceStatus" slot-scope="{ row, $index }">
          <el-button @click="showFlowInfo(row)" type="text" size="large">
            {{ row.flowCount }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            :disabled="row.status === '11'"
            type="text"
            size="large"
            @click="addAcceptance(row)"
            v-has-permi="['fullAcceptance:accepting:add']"
          >
            添加全量验收工单
          </el-button>
          <el-button
            :disabled="row.status === '11'"
            type="text"
            size="large"
            @click="commitAccept(row)"
            v-if="row.flowCount == row.flowFinishCount"
            v-has-permi="['fullAcceptance:accepting:verify']"
          >
            确认验收
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <div v-if="addAcceptanceVisible">
      <el-dialog
        title="添加全量验收工单"
        :visible.sync="addAcceptanceVisible"
        :close-on-click-modal="false"
        @close="addAcceptanceVisible = false"
        append-to-body
        width="40%"
      >
        <AddAcceptance ref="addAcceptance"></AddAcceptance>
        <div slot="footer" class="dialog-footer">
          <el-button @click.stop="addAcceptanceVisible = false"
            >取 消
          </el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryAcceptanceProjectInfoByPage,
  acceptanceAdd,
  commitAccept,
} from "@/api/fullAcceptance/index.js";
import AddAcceptance from "./components/addAcceptance.vue";
import { listUser } from "@/api/common.js";
import { regionData } from "element-china-area-data";

export default {
  components: {
    AdvancedForm,
    GridTable,
    AddAcceptance,
  },
  data() {
    return {
      loading: false,
      addAcceptanceVisible: false,
      tableData: [],
      rowData: null,
      userOption: [],
      searchForm: {
        projectCode: "",
        projectName: "",
        stationNo: null,
        stationName: "",
        statusList: ["8"],
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    tableId() {
      return "onlineManageTable";
    },
    columns() {
      return [
        {
          title: "项目编码",
          field: "projectCode",
        },
        {
          field: "projectName",
          title: "项目名称",
        },
        {
          field: "remark",
          title: "项目备注",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
        },
        {
          field: "acceptanceUser",
          title: "验收负责人",
          formatter: this.userFormat,
        },
        {
          field: "flowCount",
          title: "工单数量",
          slots: { default: "deviceStatus" },
        },
        {
          field: "flowFinishCount",
          title: "完成数量",
        },
        {
          field: "notFinishCount",
          title: "未完成数量",
        },
        {
          title: "操作",
          minWidth: 160,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
    config() {
      return [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请输入项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请输入项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
  },
  async created() {
    await this.getListUser();
  },
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    userFormat({ cellValue }) {
      for (let i = 0; i < this.userOption.length; i++) {
        if (this.userOption[i].userId == cellValue) {
          return this.userOption[i].nickName;
        }
      }
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    //获取项目列表
    async loadData() {
      this.loading = true;
      const res = await queryAcceptanceProjectInfoByPage(this.searchForm);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.searchForm.total = res.total;
      }
    },
    async acceptanceAdd(data) {
      const res = await acceptanceAdd(data);
      if (res.code === "10000") {
        this.$message.success("操作成功！");
        this.loadData();
        this.addAcceptanceVisible = false;
      }
    },
    commitAccept(row) {
      this.$confirm("确定验收该项目吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          projectId: row.projectId,
        };
        commitAccept(data).then((res) => {
          if (res?.success) {
            this.$message.success(res.message);
            //更新列表
            this.loadData();
          } else {
            this.$message.error("验收失败");
          }
        });
      });
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      if (Array.isArray(this.searchForm.region)) {
        this.searchForm["provinceList"] = [];
        this.searchForm["cityList"] = [];
        this.searchForm["countyList"] = [];
        this.searchForm.region.forEach((item) => {
          if (item[0]) {
            this.searchForm["provinceList"].push(item[0]);
          }
          if (item[1]) {
            this.searchForm["cityList"].push(item[1]);
          }
          if (item[2]) {
            this.searchForm["countyList"].push(item[2]);
          }
        });
      }
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    submitForm() {
      this.$refs.addAcceptance.$refs.form.validate((valid) => {
        if (valid) {
          const { form, useFlowList } = this.$refs.addAcceptance;
          //   let arr = form.typeList.map((item) => {
          //     return item.flowType;
          //   });
          //   if (arr.length != Array.from(new Set(arr)).length) {
          //     this.$message.error("存在相同的工单类型，请重新选择！");
          //     return;
          //   }
          const {
            stationId,
            stationName,
            projectName,
            projectId,
            acceptanceUser,
          } = this.rowData;
          let data = {
            stationId,
            stationName,
            projectName,
            projectId,
            acceptanceUser,
            acceptances: form.typeList.map((item) => {
              return {
                ...useFlowList.find((obj) => {
                  return obj.flowType == item.flowType;
                }),
              };
            }),
          };
          this.acceptanceAdd(data);
        }
      });
    },
    addAcceptance(row) {
      this.rowData = row;
      this.addAcceptanceVisible = true;
    },
    //分页切换
    changePage() {
      this.loadData();
    },
    showFlowInfo(row) {
      this.$router.push({
        path: "/workOrderWorkBench/workOrderWorkbench",
        query: { projectId: row.projectId },
      });
    },
  },
};
</script>

<style></style>
