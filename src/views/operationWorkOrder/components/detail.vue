<!-- 项目管理 -->
<template>
  <div class="app-container">
    <h3>工单详情</h3>
    <el-card class="step-card">
      <div
        slot="header"
        class="card-title-wrap"
        style="justify-content: space-between"
      >
        <div style="display: flex;">
          <div class="card-title-line"></div>
          <span>工单进度</span>
          <el-tag style="margin-left:10px">{{ form.orderStatusName }}</el-tag>
        </div>
        <el-button
          type="primary"
          @click="check()"
          v-if="
            roles.includes('systemOM') && ['3', '4'].includes(form.orderStatus)
          "
          v-has-permi="['operation:workOrder:check']"
        >
          审核
        </el-button>
      </div>

      <el-steps v-if="form.orderStatus === '4'" :space="200" align-center>
        <el-step
          v-for="(item, index) in steps"
          :key="index"
          :title="item.nodeName"
          :status="item.status"
        />
      </el-steps>
      <el-steps v-if="form.orderStatus === '6'" :space="200" align-center>
        <el-step
          v-for="(item, index) in endStepsCopy"
          :key="index"
          :title="item.nodeName"
          :status="item.status"
        />
      </el-steps>
      <el-steps
        v-if="form.orderStatus != '6' && form.orderStatus != '4'"
        :space="200"
        align-center
      >
        <el-step
          v-for="(item, index) in stepsCopy"
          :key="index"
          :title="item.nodeName"
          :status="item.status"
        />
      </el-steps>
    </el-card>
    <!-- <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单详情</span>
      </div> -->
    <add disabled @get="getForm" />
    <!-- </el-card> -->
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单流转记录</span>
      </div>
      <el-timeline :hide-timestamp="true" v-if="recordList.length > 0">
        <el-timeline-item
          placement="top"
          v-for="item in recordList"
          :key="item.orderRecordId"
        >
          <el-card>
            <el-row style="margin-bottom: 16px">
              <el-col :span="5">
                <span class="timeline-title">{{ item.operatorTypeName }}</span>
              </el-col>
              <el-col :span="5">
                <span>操作人：{{ item.operatorUserName }}</span>
              </el-col>
              <el-col :span="5" :offset="9">
                <span>{{ item.createTime }}</span>
              </el-col>
            </el-row>
            <el-row>
              <span
                style="display:block;margin-top: 10px"
                v-if="item.operatorTypeName === '完结工单'"
                >完结备注: {{ item.remark }}</span
              >
              <span
                style="display:block;margin-top: 10px"
                v-else-if="item.operatorTypeName === '结束工单'"
                >结束原因: {{ item.remark }}</span
              >
              <span
                style="display:block;margin-top: 10px"
                v-else-if="item.operatorTypeName === '处理标签'"
                >{{ item.remark }}</span
              >
              <span
                style="display:block;margin-top: 10px"
                v-else-if="item.operatorTypeName === '驳回工单'"
                >驳回原因: {{ item.remark }}</span
              >
              <span
                style="display:block;margin-top: 10px"
                v-else-if="item.operatorTypeName === '转派工单'"
                >{{ item.remark }}</span
              >
              <template v-else-if="item.operatorTypeName === '处理工单'">
                <span style="display:block;margin-top: 10px">{{
                  (item.remark && item.remark.split("&"))[0]
                }}</span>
                <div
                  style="display:block;margin-top: 10px"
                  v-if="showDocList(item)"
                >
                  <el-row type="flex" style="flex-wrap:wrap">
                    <div
                      v-for="(o, index) in item.remark.split('&')[1].split('|')"
                      :key="index"
                      class="file-item"
                    >
                      <el-image
                        style="width: 150px;height:150px;margin-right:10px"
                        :src="o"
                        alt="加载失败"
                        class="avatar"
                        v-if="
                          o.toLowerCase().indexOf('.jpg') > 0 ||
                            o.toLowerCase().indexOf('.jpeg') > 0 ||
                            o.toLowerCase().indexOf('.png') != -1
                        "
                        @click.stop="
                          clickImg(index, item.remark.split('&')[1].split('|'))
                        "
                      />
                      <video
                        style="width: 150px;height:150px;margin-right:10px"
                        v-if="o.toLowerCase().indexOf('.mp4') > 0"
                        :src="o"
                        controls
                        @click.stop="
                          clickImg(index, item.remark.split('&')[1].split('|'))
                        "
                      ></video>
                    </div>
                  </el-row>
                </div>
              </template>
              <span v-else style="display:block;margin-top: 10px">
                {{ item.remark }}
              </span>
            </el-row>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <el-dialog
        title=""
        width="50%"
        :visible.sync="showImgPreview"
        @close="closePreviewDialog"
        append-to-body
        :before-close="resetPreview"
      >
        <div style="display: flex; justify-content: center; padding: 20px">
          <el-image
            :src="previewImgUrl"
            fit="contain"
            alt="加载失败"
            ref="previewImage"
            @mousewheel.prevent="handleImageWheel"
            :style="imageStyle"
          />
        </div>
      </el-dialog>
      <el-dialog
        title="审核工单"
        :visible.sync="visible"
        :close-on-click-modal="false"
        @close="closeVisible"
        append-to-body
        width="50%"
      >
        <el-form
          :model="checkForm"
          ref="checkForm"
          label-width="110px"
          :rules="rules"
        >
          <el-form-item label="工单状态" prop="orderStatus">
            <el-select v-model="checkForm.orderStatus" style="width: 100%;">
              <el-option
                v-for="item in checkOrderOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="故障类别" prop="oneFaultTypes">
            <el-row>
              <el-col :span="8">
                <el-select
                  v-model="checkForm.oneFaultTypes"
                  multiple
                  @change="handleFirstFaultChange"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in firstFaultOptions"
                    :label="item.typeName"
                    :value="item.id"
                    :key="item.id"
                  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-select
                  v-model="checkForm.twoFaultTypes"
                  multiple
                  @change="handleSecondFaultChange"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in secondFaultOptions"
                    :label="item.typeName"
                    :value="item.id"
                    :key="item.id"
                  />
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-select
                  v-model="checkForm.threeFaultTypes"
                  multiple
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in thirdFaultOptions"
                    :label="item.typeName"
                    :value="item.id"
                    :key="item.id"
                  />
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="通知组" prop="groupIds">
            <el-select
              v-model="checkForm.groupIds"
              multiple
              @change="handleGroupChange"
              style="width: 100%;"
            >
              <el-option
                v-for="item in groupOptions"
                :label="item.groupName"
                :value="item.groupId"
                :key="item.groupId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="通知人" prop="userIds">
            <el-select
              v-model="checkForm.userIds"
              multiple
              @change="handleGroupMemberChange"
              style="width: 100%;"
            >
              <el-option
                v-for="item in groupMemberOptions"
                :label="item.nickName + '-' + item.userName"
                :value="item.userId"
                :key="item.userId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="通知方式" prop="messageTypes">
            <el-checkbox-group v-model="checkForm.messageTypes">
              <el-checkbox label="01">钉钉</el-checkbox>
              <el-checkbox label="02">短信</el-checkbox>
              <el-checkbox label="03">APP</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="备注" prop="reason">
            <el-input
              v-model="checkForm.reason"
              type="textarea"
              :rows="5"
              maxlength="500"
              show-word-limit
            >
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button :loading="checkLoading" @click.stop="closeVisible"
            >取消</el-button
          >
          <el-button :loading="checkLoading" type="primary" @click="submit"
            >确定</el-button
          >
        </div>
      </el-dialog>
      <PicPreview ref="picPreview"></PicPreview>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { queryGroupList, queryGroupMemberList } from "@/api/errorPush/index.js";
import { firstFaultList, childFaultList } from "@/api/faultType/index.js";
import { check, orderRecord } from "@/api/operationWorkOrder";
import add from "@/views/operationWorkOrder/components/addPreview.vue";
import PicPreview from "@/components/Upload/picPreview.vue";
export default {
  name: "operationWorkOrderDetail",
  components: {
    add,
    PicPreview,
  },
  data() {
    return {
      firstFaultOptions: [],
      secondFaultOptions: [],
      thirdFaultOptions: [],
      groupOptions: [],
      groupMemberOptions: [],
      checkOrderOptions: [
        { value: "4", label: "驳回" },
        { value: "7", label: "待回访" },
        { value: "5", label: "已完结" },
      ],
      orderNo: "",
      recordList: [],
      docList: [],
      showImgPreview: false,
      previewImgUrl: "",
      imageStyle: { width: "100vh", height: "100vh" },
      form: {
        orderStatus: "4",
        orderStatusName: "已驳回",
      },
      steps: [],
      stepsCopy: [],
      endStepsCopy: [],
      visible: false,
      checkForm: {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      },
      // rules: {
      //   reason: [
      //     { validator: this.validateReason, trigger: "blur" },
      //     { max: 500, message: "500字符以内", trigger: "blur" },
      //   ],
      // },
      checkLoading: false,
      orderStatusOptions: [],
      orderId: "",
    };
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    rules() {
      return {
        orderStatus: [
          { required: true, trigger: "blur", message: "请选择工单状态" },
        ],
        reason: [
          {
            required: this.checkForm.orderStatus == "4",
            message: "工单状态为驳回时，备注不能为空！",
            trigger: "blur",
          },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
        oneFaultTypes: [
          { required: true, message: "请选择故障类型", trigger: "blur" },
        ],
      };
    },
  },
  mounted() {
    this.getRecordList();
  },
  methods: {
    handleSecondFaultChange(val) {
      this.checkForm.threeFaultTypes = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val }).then((res) => {
          this.thirdFaultOptions = res?.data;
        });
      }
    },
    handleFirstFaultChange(val) {
      this.checkForm.twoFaultTypes = [];
      this.checkForm.threeFaultTypes = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val }).then((res) => {
          this.secondFaultOptions = res?.data;
        });
      }
    },
    handleGroupMemberChange(val) {
      console.log("通知人---", val);
    },
    handleGroupChange(val) {
      console.log("通知组", val);
      queryGroupMemberList({ groupIds: val }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
    },
    getRecordList() {
      orderRecord({ orderNo: this.orderNo }).then((res) => {
        this.recordList = res.data;
        if (this.recordList.length < 1) {
          this.$message.info("暂无流转记录");
        }
      });
    },
    clickImg(index, list) {
      // //点击预览图片
      // this.showImgPreview = true;
      // this.previewImgUrl = o;
      this.$refs.picPreview.open(index, list);
    },
    resetPreview(done) {
      this.zoomLevel = 100;
      done();
    },
    handleImageWheel(event) {
      event.preventDefault();
      const delta = Math.max(
        -1,
        Math.min(1, event.deltaY || -event.wheelDelta || event.detail)
      );
      const zoomStep = 10;
      if (delta > 0) {
        // 放大图片
        this.zoomLevel += zoomStep;
      } else {
        // 缩小图片
        this.zoomLevel -= zoomStep;
      }
      // 根据缩放级别调整图片大小
      this.imageStyle = {
        ...this.imageStyle,
        transform: `scale(${this.zoomLevel / 100})`,
      };
    },
    //关闭图片预览弹框
    closePreviewDialog() {
      this.showImgPreview = false;
    },
    check(row) {
      this.visible = true;
      queryGroupList({ pageNum: 1, pageSize: 9999, status: 0 }).then((res) => {
        this.groupOptions = res?.data;
      });
      firstFaultList().then((res) => {
        this.firstFaultOptions = res?.data;
      });
      this.secondFaultOptions = [];
      this.thirdFaultOptions = [];
      queryGroupMemberList({ groupIds: [] }).then((res) => {
        this.groupMemberOptions = res?.data;
      });

      // this.form = { ...row };
    },
    closeVisible() {
      this.visible = false;
      this.checkForm = {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      };
    },
    validateReason(rule, value, callback) {
      if (this.flag && !value) {
        callback(new Error("审核不通过原因不能为空"));
      }
      callback();
    },
    submit(flag, loading) {
      this.$refs.checkForm.validate((valid) => {
        if (valid) {
          this.checkLoading = true;
          const params = {
            ...this.checkForm,
            orderNo: this.orderNo,
            orderId: this.orderId,
          };
          check(params)
            .then((res) => {
              this.checkLoading = false;
              this.$message.success("审核工单成功");
              this.visible = false;
              //页面刷新
              this.$router.push(
                `${this.$route.path}?orderNo=${this.orderNo}&orderStatus=${res.data}&orderId=${this.orderId}`
              );
            })
            .catch((err) => {
              this.checkLoading = false;
            });
        }
      });
      // const vm = this;
      // this.flag = flag;
      // this.$refs.checkForm.validate((valid) => {
      //   if (valid) {
      //     if (vm.checkForm.orderStatus === "2") {
      //       vm.$message.error("工单正在处理中，无法审核");
      //       return;
      //     }
      //     this[loading] = true;
      //     const params = { ...vm.checkForm };
      //     params.auditFlag = vm.flag ? 0 : 1;
      //     params.orderId = this.orderId;
      //     check(params).then((res) => {
      //       vm[loading] = false;
      //       vm.$message.success("审核工单成功");
      //       vm.visible = false;
      //       //页面刷新
      //       this.$router.push(
      //         `${this.$route.path}?orderNo=${this.orderNo}&orderStatus=${res.data}&orderId=${this.orderId}`
      //       );
      //     });
      //   }
      // });
    },
    getForm(form) {
      this.form = { ...form, ...this.form };
    },
    async getOrderStatusOptions() {
      await this.getDicts("order_status").then((response) => {
        this.orderStatusOptions = response?.data;
      });
    },
    orderStatusFormat(value) {
      return this.selectDictLabel(this.orderStatusOptions, value);
    },
    showDocList(item) {
      const arr = item.remark.split("&");
      return arr && arr[1];
    },
  },
  watch: {
    $route: {
      handler(newVal) {
        console.log("newVal", newVal);
        this.orderNo = newVal.query.orderNo;
        this.orderId = newVal.query.orderId;
        this.form.orderStatus = newVal.query.orderStatus;
        Promise.all([this.getOrderStatusOptions()]).then((res) => {
          console.log(this.orderStatusOptions);
          this.form.orderStatusName = this.orderStatusFormat(
            this.form.orderStatus
          );
          this.steps = [
            {
              nodeName: "创建工单",
              status: ["1", "2", "3", "4", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "处理工单",
              status: ["2", "3", "4", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "工单完成",
              status: ["3", "4", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "工单驳回",
              status: ["4", "5"].includes(this.form.orderStatus) ? "error" : "",
            },
            {
              nodeName: "工单完结",
              status: ["5"].includes(this.form.orderStatus) ? "success" : "",
            },
          ];
          this.stepsCopy = [
            {
              nodeName: "创建工单",
              status: ["1", "2", "3", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "处理工单",
              status: ["2", "3", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "工单完成",
              status: ["3", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "工单完结",
              status: ["5"].includes(this.form.orderStatus) ? "success" : "",
            },
          ];
          this.endStepsCopy = [
            {
              nodeName: "创建工单",
              status: ["1", "2", "3", "5", "6"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "结束工单",
              status: ["2", "3", "5", "6"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "处理工单",
              status: ["2", "3", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "工单完成",
              status: ["3", "5"].includes(this.form.orderStatus)
                ? "success"
                : "",
            },
            {
              nodeName: "工单完结",
              status: ["5"].includes(this.form.orderStatus) ? "success" : "",
            },
          ];
        });
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/.el-select-group__title {
    padding-left: 10px;
  }
  /deep/.el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }
  /deep/.el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.step-card /deep/ .el-steps {
  justify-content: center;
}
/deep/ .el-card {
  margin-bottom: 10px;
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
