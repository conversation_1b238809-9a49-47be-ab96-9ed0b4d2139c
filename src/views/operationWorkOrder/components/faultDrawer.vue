<template>
  <el-drawer title="故障详情" :visible.sync="drawerVisible" size="60%">
    <!-- 故障告警 -->
    <div v-if="orderType == '01'">
      <el-descriptions class="descriptions" :column="2" border>
        <el-descriptions-item
          v-for="(item, index) in faultInfoList"
          :key="index"
          :label="item.title"
        >
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions
        class="descriptions"
        :column="2"
        border
        v-for="(obj, i) in faultDetailList"
        :key="i"
      >
        <el-descriptions-item
          v-for="(item, index) in faultDictArr"
          :key="index"
          :label="item.title"
        >
          <el-tooltip :content="obj[item.code]" placement="top-start">
            <span v-if="item.slot === 'faultLevel'">
              {{ getFaultLevel(obj[item.code]) }}
            </span>
            <span v-else>{{ obj[item.code] }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 业务告警 -->
    <div v-else-if="orderType == '02'">
      <el-descriptions
        class="descriptions"
        :column="2"
        border
        v-for="(obj, i) in businessDetailList"
        :key="i"
      >
        <el-descriptions-item
          v-for="(item, index) in businessDictArr"
          :key="index"
          :label="item.title"
        >
          <el-tooltip :content="obj[item.code]" placement="top-start">
            <span>{{ obj[item.code] }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script>
import { faultDetail } from "@/api/operationWorkOrder/index.js";
export default {
  props: {
    orderType: {
      type: String,
      default: "01",
    },
    orderNo: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      drawerVisible: false,
      faultInfoList: [],
      faultDictArr: [
        { title: "故障名称", code: "errorName" },
        { title: "推送时间", code: "pushTime" },
        { title: "故障码", code: "faultCode" },
        { title: "告警类型", code: "type" },
        { title: "故障描述", code: "alarmDesc" },
        { title: "是否恢复", code: "recover" },
        { title: "影响", code: "alarmInfluence" },
        { title: "恢复类型", code: "status" },
        { title: "处理建议", code: "alarmSuggestions" },
        { title: "故障次数", code: "triggerCount" },
        { title: "上报报文", code: "sbbw" },
        { title: "告警持续时间", code: "alarmDurationDesc" },
        { title: "告警等级", code: "faultLevel", slot: "faultLevel" },
        { title: "告警创建时间", code: "gmtCreate" },
        { title: "告警清除时间", code: "gmtClear" },
        { title: "最新告警时间", code: "gmtLastTrigger" },
        { title: "告警首次触发时间", code: "gjsccfsj" },
        { title: "告警结束时间", code: "gmtEnd" },
      ],
      businessDictArr: [
        { title: "故障名称", code: "errorName" },
        { title: "站点编码", code: "stationCode" },
        { title: "故障详情", code: "bizAlarmDesc" },
        { title: "站点名称", code: "stationName" },
        { title: "推送时间", code: "pushTime" },
        { title: "是否恢复", code: "recover" },
        { title: "详细地址", code: "stationAddr" },
        { title: "恢复类型", code: "status" },
        { title: "告警类型", code: "type" },
        { title: "", code: "" },
      ],
      faultDetailList: [],
      businessDetailList: [],
      faultLevelOptions: [],
    };
  },
  created() {
    console.log(this.orderType, "orderType");
    this.getDicts("fault_level").then((response) => {
      this.faultLevelOptions = response.data;
    });
  },
  methods: {
    getFaultLevel(val) {
      return (
        this.faultLevelOptions?.find((el) => el.dictValue == val)?.dictLabel ||
        val
      );
    },
    openDrawer() {
      this.drawerVisible = true;
      faultDetail({ orderType: this.orderType, orderNo: this.orderNo }).then(
        (res) => {
          if (res?.code == "10000") {
            if (this.orderType === "01") {
              this.faultInfoList = [
                { title: "设备编码", value: res.data?.equipNo || "" },
                { title: "设备名称", value: res.data?.deviceName || "" },
                { title: "设备类型", value: res.data?.equipType || "" },
                { title: "分组", value: res.data?.catalogName || "" },
                {
                  title: "品牌-型号",
                  value:
                    (res.data?.pileBrandName || "") +
                    "-" +
                    (res.data?.pileModelName || ""),
                },
              ];
              this.faultDetailList = res.data?.alramList;
            } else {
              this.businessDetailList = res.data?.alramList;
            }
          }
        }
      );
    },
  },
};
</script>

<style lang="less" scoped>
.descriptions {
  margin: 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    // white-space: wrap;
    white-space: pre-line;
    // text-overflow: ellipsis;
  }
}
</style>
