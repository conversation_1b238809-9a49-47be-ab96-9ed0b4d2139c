<template>
  <div>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <el-form
        :model="basicForm"
        ref="basicForm"
        label-width="110px"
        :rules="rules"
        :disabled="disabled"
      >
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            v-model="basicForm.businessType"
            @change="getBusinessTypeName"
          >
            <el-option
              v-for="item in businessTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工单类型" prop="orderTypeParentName">
          <el-row>
            <el-col :span="3.3">
              <el-select
                v-model="basicForm.orderTypeParentName"
                @change="
                  (val) => {
                    orderTypeParentChange(val, 2);
                  }
                "
              >
                <el-option
                  v-for="item in orderTypeParentOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="3.3">
              <el-select
                v-model="basicForm.orderTypeName"
                @change="
                  (val) => {
                    orderTypeParentChange(val, 3);
                  }
                "
                clearable
              >
                <el-option
                  v-for="item in orderTypeOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="3.3">
              <el-select v-model="threeOrderTypeName" clearable>
                <el-option
                  v-for="item in threeOrderTypeOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="处理人" prop="handleUser">
          <el-select
            v-model="basicForm.handleUser"
            @change="getHandleUserName"
            filterable
            clearable
          >
            <el-option
              v-for="item in userOptions"
              :label="`${item.nickName}-${item.userName}`"
              :value="item.userId"
              :key="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="站点名称" prop="stationId">
          <el-select
            v-model="basicForm.stationId"
            @change="getPileList"
            filterable
          >
            <el-option
              v-for="item in stationOptions"
              :label="item.stationName"
              :value="item.stationId"
              :key="item.stationId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择区域" prop="region">
          <el-cascader
            v-model="basicForm.region"
            placeholder="请选择区域"
            :options="areaOptions"
            clearable
            filterable
            :disabled="pageType === 'detail'"
            :props="{ checkStrictly: true }"
            collapse-tags
          ></el-cascader>
        </el-form-item>
        <el-form-item label="详细地址">
          <el-input
            v-model="basicForm.address"
            type="input"
            :disabled="pageType === 'detail'"
          />
        </el-form-item>
        <el-form-item label="问题描述" prop="orderDesc">
          <Editor v-model="basicForm.orderDesc" />
        </el-form-item>
        <el-form-item label="用户姓名" v-if="orderId && channel == '02'">
          <el-input
            v-model="basicForm.customerUserName"
            type="input"
            :disabled="pageType === 'detail'"
          />
        </el-form-item>
        <el-form-item label="用户手机号" v-if="orderId && channel == '02'">
          <el-input
            v-model="basicForm.customerUserPhone"
            type="input"
            :disabled="pageType === 'detail'"
          />
        </el-form-item>
        <el-form-item label="车型信息" v-if="orderId && channel == '02'">
          <el-row>
            <el-col :span="3.3">
              <el-input
                v-model="basicForm.customerCarBrand"
                type="input"
                placeholder="输入品牌"
              />
            </el-col>
            <el-col :span="3.3">
              <el-input
                v-model="basicForm.customerCarModel"
                type="input"
                placeholder="输入车型"
              />
            </el-col>
            <el-col :span="3.3">
              <el-date-picker
                v-model="basicForm.customerCarYear"
                type="year"
                placeholder="选择年份"
                :value-format="'yyyy'"
                :format="'yyyy'"
              >
              </el-date-picker>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="问题发生时间">
          <el-date-picker
            type="datetime"
            v-model="basicForm.occurrenceDate"
            @blur="changeOccurrenceDate"
          />
          <el-radio-group
            v-model="basicForm.occurrenceDateFlag"
            @change="changeOccurrenceDateFlag"
          >
            <el-radio label="0">不明确</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="期望处理时间">
          <el-date-picker
            type="datetime"
            v-model="basicForm.expectHandleDate"
            @blur="changeExpectHandleDate"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
          <el-radio-group
            v-model="basicForm.expectHandleType"
            @change="changeExpectHandleDateType"
          >
            <el-radio label="1">今天</el-radio>
            <el-radio label="2">明天</el-radio>
            <el-radio label="3">3天内</el-radio>
            <el-radio label="4">7天内</el-radio>
            <el-radio label="5">不要求</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="紧急程度" prop="urgencyLevel">
          <el-radio-group v-model="basicForm.urgencyLevel">
            <el-radio label="0">紧急</el-radio>
            <el-radio label="1">一般</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="问题图片">
          <file-upload
            type="img"
            ref="upload"
            :limit="9"
            :disabled="disabled"
            textTip="支持批量上传，上传格式为jpg/png文件"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>充电桩信息</span>
      </div>
      <el-form
        :model="chargingPileForm"
        ref="chargingPileForm"
        label-width="110px"
        :rules="rules"
        :disabled="disabled"
      >
        <el-row
          v-for="(item, index) in chargingPileForm.pileGunList"
          :key="index"
        >
          <el-col :span="14">
            <el-card>
              <el-form-item label="充电桩：">
                <el-select
                  v-model="item.pileCode"
                  @change="(value) => getPileInfo(value, item)"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="i in availableOptions"
                    :label="`${i.pileCode}-${i.pileName}`"
                    :value="i.pileCode"
                    :key="i.pileCode"
                    :disabled="i.disabled"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="充电枪：">
                <el-row>
                  <el-col :span="5">
                    <span
                      >共
                      <span style="color:#029c7c">{{ item.count }} </span
                      >枪</span
                    >
                  </el-col>
                  <el-col :span="5">
                    <el-form-item>
                      <el-button
                        v-if="!disabled"
                        :disabled="!item.pileCode"
                        :loading="loading"
                        @click="queryRunningStatus(item)"
                        type="primary"
                        >查询枪运行状态
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <template
                  v-if="
                    item.gunList.length > 0 && item.gunList[0].runStatusName
                  "
                >
                  <el-row v-for="(object, index) in item.gunList" :key="index">
                    <el-col :span="5">
                      <el-checkbox v-model="object.checked" />
                      <span>{{ object.gunNo }}枪</span>
                    </el-col>
                    <el-col :span="7">
                      <el-row>
                        <el-button type="info"
                          >{{ object.runStatusName }}
                        </el-button>
                      </el-row>
                      <el-row>
                        <span style="font-size:12px;color:#606266"
                          >上报时间：{{ object.updateTime }}</span
                        >
                      </el-row>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="" prop="gunDesc">
                        <el-input
                          v-model="object.gunDesc"
                          type="textarea"
                          size="mini"
                          :rows="2"
                          maxlength="500"
                          show-word-limit
                          :disabled="pageType === 'detail'"
                          placeholder="请输入具体的问题描述"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </el-form-item>
              <el-form-item label="故障描述">
                <el-input
                  v-model="item.pileDesc"
                  type="textarea"
                  size="mini"
                  :disabled="pageType === 'detail'"
                  max="500"
                  :rows="5"
                />
              </el-form-item>
            </el-card>
          </el-col>
          <el-col :span="1">
            <el-form-item>
              <el-button
                v-if="item.pileCode"
                type="primary"
                circle
                style="margin-top: 90px"
                icon="el-icon-plus"
                @click="addItem"
              />
              <el-button
                v-if="index > 0"
                type="primary"
                circle
                style="margin-top: 90px"
                icon="el-icon-minus"
                @click="removeItem(index)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div slot="footer" class="dialog-footer" v-if="!disabled">
      <el-button @click.stop="closeVisible" size="medium">取 消</el-button>
      <el-button @click="submit" type="primary" size="medium">提交</el-button>
    </div>
  </div>
</template>
<script>
import { stationList } from "@/api/workOrderWorkbench";
import { regionData } from "element-china-area-data";
import { pileList } from "@/api/business/flow/flow";
import { queryTreeList } from "@/api/workOrderType/index.js";
import {
  detail,
  pileGunList,
  saveOrder,
  fistLevelList,
  childrenList,
} from "@/api/operationWorkOrder";
import FileUpload from "@/components/Upload/fileUpload2.vue";
import moment from "moment";
import Editor from "@/components/Editor/index.vue";
import { listAllUser } from "@/api/common";
import { timeDiffFormat } from "@/utils/comm";
import {
  MAINTENANCE_ORDER_CLICK_EDIT,
  MAINTENANCE_ORDER_CREATE
} from "@/utils/track/track-event-constants";

export default {
  name: "addOperationWorkOrder",
  components: {
    FileUpload,
    Editor,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      channel: "",
      flattenData: [],
      threeOrderTypeName: "",
      basicForm: {
        occurrenceDate: "",
        expectHandleType: "",
        urgencyLevel: "",
        orderTypeName: "",
        threeOrderTypeName: "",
        businessTypeName: "",
      },
      chargingPileForm: {
        pileGunList: [
          {
            count: 0,
            gunList: [
              {
                gunDesc: "",
              },
            ],
          },
        ],
      },
      businessTypeOptions: [],
      orderTypeParentOptions: [
        { label: "故障工单", value: "故障工单" },
        { label: "抄表工单", value: "抄表工单" },
        { label: "真车充电测试派单", value: "真车充电测试派单" },
      ],

      pageType: undefined,
      areaOptions: regionData,
      rules: {
        businessType: {
          required: true,
          message: "业务类型不能为空",
          trigger: "blur",
        },
        orderTypeParentName: [
          { required: true, message: "工单类型不能为空", trigger: "blur" },
          { validator: this.validateOrderTypeParentName, trigger: "blur" },
        ],
        orderDesc: [
          { required: true, message: "问题描述不能为空", trigger: "blur" },
          // { max: 500, message: "500个字符以内", trigger: "blur" },
        ],
        stationId: {
          required: true,
          message: "站点名称不能为空",
          trigger: "blur",
        },
        region: {
          validator: this.validateRegion,
          trigger: "blur",
        },
        urgencyLevel: {
          required: true,
          message: "紧急程度不能为空",
          trigger: "blur",
        },
      },
      count: 0,
      stationOptions: [],
      userOptions: [],
      pileOptions: [],
      loading: false,
      orderTypeOptions: [],
      threeOrderTypeOptions: [],
      orderId: "",
      editOriginalPileCode: [],
      handleStartDate: null //处理开始时间
    };
  },
  computed: {
    availableOptions() {
      const codeArr = this.chargingPileForm.pileGunList?.map((x) => {
        return x.pileCode;
      });
      console.log(this.pileOptions, "pileOptions");
      const arr = this.pileOptions?.map((item) => {
        if (codeArr.includes(item.pileCode)) {
          return { ...item, disabled: true };
        } else {
          return { ...item, disabled: false };
        }
      });
      console.log(arr, "------下拉选项");
      return arr;
    },
  },
  methods: {
    getOrderTypeOptions() {
      fistLevelList().then((res) => {
        this.orderTypeParentOptions = res?.data?.map((x) => {
          return { label: x.typeName, value: x.id };
        });
      });
    },
    orderTypeParentChange(val, level) {
      console.log("---", this.basicForm);
      if (level == 2) {
        this.basicForm.orderTypeName = "";
        this.threeOrderTypeName = "";
        this.threeOrderTypeOptions = [];
        childrenList({ id: val }).then((res) => {
          this.orderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      } else {
        this.threeOrderTypeName = "";
        childrenList({ id: val }).then((res) => {
          this.threeOrderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
        console.log("222", this.basicForm);
      }
    },
    validateOrderTypeParentName(rule, value, callback) {
      if (!this.basicForm.orderTypeParentName) {
        callback(new Error("工单类型不能为空"));
      }
      callback();
    },
    validateRegion(rule, value, callback) {
      if (value && value.length < 3) {
        callback(new Error("省市区域不能为空"));
      }
      callback();
    },
    closeVisible() {
      this.$router.push({
        path: "order/operationWorkOrder/list",
      });
    },
    addItem() {
      this.chargingPileForm.pileGunList.push({
        count: 0,
        gunList: [
          {
            gunDesc: "",
          },
        ],
      });
    },
    removeItem(index) {
      this.chargingPileForm.pileGunList.splice(index, 1);
    },
    getBusinessTypeOptions() {
      this.getDicts("business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    async getStationList() {
      stationList().then((res) => {
        this.stationOptions = res?.data;
      });
    },
    async getUserList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };

      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOptions = data;
    },
    getCheckedGunMap(gunList) {
      let obj = {};
      gunList.forEach((item) => {
        obj[item.gunNo] = item;
      });
      return obj;
    },
    getHandleUserName(e) {
      this.basicForm.handleUserName = this.userOptions.find(
        (item) => item.dictValue == e
      ).dictLabel;
    },
    getPileList(stationId, flag) {
      if (!flag) {
        this.chargingPileForm = {
          pileGunList: [
            {
              count: 0,
              gunList: [
                {
                  gunDesc: "",
                },
              ],
            },
          ],
        };
      }
      const station = this.stationOptions.find(
        (item) => item.stationId === stationId
      );
      this.basicForm.region = station
        ? [station.province, station.city, station.county]
        : [this.basicForm.province, this.basicForm.city, this.basicForm.county];
      this.basicForm.address = station
        ? station.stationAddress
        : this.basicForm.address;
      this.basicForm.stationName = station
        ? station.stationName
        : this.basicForm.address?.stationName;
      pileList({ stationId: stationId }).then((response) => {
        // pileList({stationId: "369539647983980544"}).then((response) => {
        this.pileOptions = response?.data;
      });
    },
    queryRunningStatus(item) {
      this.loading = true;
      pileGunList({ pileNo: item.pileCode, pileName: item.pileName }).then(
        (response) => {
          // pileGunList({ pileNo: "***********", pileName: "12号桩"}).then((response) => {
          this.loading = false;
          item.gunList = [...response?.data];
          item.count = item.gunList.length;
        }
      );
    },
    getPileInfo(value, item) {
      item.pileCode = value;
      item.pileName = this.pileOptions.find(
        (x) => x.pileCode === value
      ).pileName;
      this.queryRunningStatus(item);
    },
    getBusinessTypeName(e) {
      this.basicForm.businessTypeName = this.businessTypeOptions.find(
        (item) => item.dictValue == e
      ).dictLabel;
    },
    submit() {
      const vm = this;
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          let flag = null;
          for (const item of vm.chargingPileForm.pileGunList) {
            for (const o of item.gunList) {
              if (o.gunDesc?.length > 500) {
                flag = true;
                break;
              }
            }
          }
          if (flag) {
            this.$message.error("充电枪问题描述长度不能超过500");
            return;
          }
          this.$confirm("是否确认提交", "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "info",
          }).then(function() {
            const {
              orderTypeParentName,
              orderTypeName,
              customerCarYear,
              ...form
            } = vm.basicForm;
            form.customerCarYear = customerCarYear
              ? moment(customerCarYear).format("YYYY")
              : "";
            if (form.occurrenceDate) {
              form.occurrenceDateFlag = "1";
            } else {
              form.occurrenceDateFlag = "0";
            }
            if (Array.isArray(form.region)) {
              form.province = form.region[0];
              form.city = form.region[1];
              form.county = form.region[2];
              form.provinceAddress = regionData.find(
                (item) => item.value === form.province
              )?.label;
              form.cityAddress = regionData.find(
                (item) => item.value === form.city
              )?.label;
              form.countyAddress = regionData.find(
                (item) => item.value === form.county
              )?.label;
              delete form.region;
            }
            const pileGunList = [];
            if (
              vm.chargingPileForm.pileGunList.length > 0 &&
              vm.chargingPileForm.pileGunList[0].hasOwnProperty("pileCode")
            ) {
              vm.chargingPileForm.pileGunList.forEach((item, index) => {
                if (
                  vm.editOriginalPileCode.length > 0 &&
                  vm.editOriginalPileCode[index].label === item.pileCode
                ) {
                  item.pileCode = vm.editOriginalPileCode[index].code;
                  item.pileName = vm.editOriginalPileCode[index].name;
                }
                // item.gunList.forEach((object) => {
                // if (object.checked) {
                // const itemCopy = { ...item };
                // delete itemCopy.gunList;
                // pileGunList.push({ ...object, ...itemCopy });
                // }
                // });
                const gunListCopy = [...item.gunList];
                item.gunList = gunListCopy?.filter((x) => x.checked);
              });
              // form.pileGunList = pileGunList;
              form.pileGunList = vm.chargingPileForm.pileGunList;
            } else {
              form.pileGunList = [];
            }

            form.docList = vm.$refs.upload.fileList;
            form.orderId = vm.orderId;

            //如果工单类型存在于现有树结构类型中，传工单类型id和对应的名称
            const orderTypeNameTemp = vm.flattenData?.find(
              (x) => x.id === orderTypeName
            )?.typeName;
            if (orderTypeNameTemp) {
              form["orderTypeId"] = orderTypeName;
              form["orderTypeName"] = orderTypeNameTemp;
            } else {
              //类型已经被删除/选择类型为空--传原本的名称/id传空
              form["orderTypeId"] = "";
              form["orderTypeName"] = orderTypeName;
            }

            const orderTypeParentNameTemp = vm.flattenData?.find(
              (x) => x.id === orderTypeParentName
            )?.typeName;
            if (orderTypeParentNameTemp) {
              form["orderTypeParentId"] = orderTypeParentName;
              form["orderTypeParentName"] = orderTypeParentNameTemp;
            } else {
              form["orderTypeParentId"] = "";
              form["orderTypeParentName"] = orderTypeParentName;
            }

            const threeOrderTypeNameTemp = vm.flattenData?.find(
              (x) => x.id === vm.threeOrderTypeName
            )?.typeName;
            if (threeOrderTypeNameTemp) {
              form["threeOrderTypeId"] = vm.threeOrderTypeName;
              form["threeOrderTypeName"] = threeOrderTypeNameTemp;
            } else {
              form["threeOrderTypeId"] = "";
              form["threeOrderTypeName"] = vm.threeOrderTypeName;
            }
            //编辑时 客服工单的业务类型 可能与其options对不上，因此设置了businessType=businessTypeName用于回显展示；
            //最后提交时 如果businessType===businessTypeName，则没有改动业务类型选项，仍然只传businessTypeName，删除businessType
            if (form.businessType === form.businessTypeName) {
              delete form.businessType;
            }
            console.log("提交", form);
            saveOrder(form).then((res) => {
              if (vm.orderId) {
                vm.$message.success("编辑工单成功");

                //事件上报
                vm.reportTrackEvent(MAINTENANCE_ORDER_CLICK_EDIT);
              } else {
                vm.$message.success("创建工单成功");

                //事件上报
                vm.reportTrackEvent(MAINTENANCE_ORDER_CREATE, {
                  noHandleUser:
                    !form?.handleUser || form?.handleUser?.length == 0,
                  hasChargingPie: form?.pileGunList?.length > 0,
                  createDuration: timeDiffFormat(vm.handleStartDate, new Date()) //时间格式为xx天xx小时xx分钟xx秒
                });
              }
              vm.$router.push({
                path: "order/operationWorkOrder/list"
              });
            });
          });
        }
      });
    },
    changeOccurrenceDateFlag() {
      this.basicForm.occurrenceDate = "";
    },
    changeOccurrenceDate() {
      this.$set(
        this.basicForm,
        "occurrenceDate",
        this.basicForm.occurrenceDate
      );
      if (this.basicForm.occurrenceDate) {
        this.$set(this.basicForm, "occurrenceDateFlag", "");
      }
    },
    changeExpectHandleDateType() {
      this.basicForm.expectHandleDate = "";
    },
    changeExpectHandleDate() {
      this.$set(
        this.basicForm,
        "expectHandleDate",
        this.basicForm.expectHandleDate
      );
      if (this.basicForm.expectHandleDate) {
        this.$set(this.basicForm, "expectHandleType", "");
      }
    },
    async getTreeData() {
      const res = await queryTreeList({});
      this.flattenData = this.flattenArray(res.data);
      console.log("-----", this.flattenData);
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
  },
  mounted() {
    this.basicForm.occurrenceDate = moment(new Date()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.basicForm.expectHandleType = "4";
    this.basicForm.urgencyLevel = "0";
  },
  watch: {
    $route: {
      handler(newVal) {
        console.log("route");
        if (newVal.query?.orderId) {
          this.orderId = newVal.query.orderId;
          detail({ orderId: this.orderId }).then(async (res) => {
            const {
              threeOrderTypeName,
              orderTypeParentName,
              orderTypeName,
              orderTypeParentId,
              orderTypeId,
              threeOrderTypeId,
              channel,
              ...rest
            } = res?.data;
            this.basicForm = { ...rest };
            this.channel = channel;
            if (this.basicForm.expectHandleType) {
              this.$set(this.basicForm, "expectHandleDate", "");
            }
            console.log("----this.basicForm-----", this.basicForm);
            if (!this.flattenData.length) {
              await this.getTreeData();
              console.log("this.flattenData", this.flattenData);
            }
            if (!this.basicForm.businessType) {
              const { businessTypeName } = this.basicForm;
              this.$set(this.basicForm, "businessType", businessTypeName);
            }
            //根据id展示内容，如果存在id，查找下一级下拉选项；如果不存在，则被删除了，展示名称
            //一级--如果现有工单类型中存在一级类型的id
            if (this.flattenData.some((x) => x.id == orderTypeParentId)) {
              childrenList({ id: orderTypeParentId }).then((res) => {
                this.orderTypeOptions = res?.data?.map((x) => {
                  return { label: x.typeName, value: x.id };
                });
                this.basicForm.orderTypeParentName = orderTypeParentId.toString();
              });
            } else {
              console.log("未找到该类型");
              //如果该类型已经被删了，则展示名称
              this.basicForm.orderTypeParentName = orderTypeParentName;
            }

            //二级--如果现有工单类型中存在类型的id
            if (this.flattenData.some((x) => x.id == orderTypeId)) {
              childrenList({ id: orderTypeId }).then((res) => {
                this.threeOrderTypeOptions = res?.data?.map((x) => {
                  return { label: x.typeName, value: x.id };
                });
                this.basicForm.orderTypeName = orderTypeId.toString();
              });
            } else {
              //如果该类型已经被删了，则展示名称
              this.basicForm.orderTypeName = orderTypeName;
            }

            //三级--如果现有工单类型中存在类型的id
            if (this.flattenData.some((x) => x.id == threeOrderTypeId)) {
              this.threeOrderTypeName = threeOrderTypeId.toString();
            } else {
              //如果该类型已经被删了，则展示名称
              this.threeOrderTypeName = threeOrderTypeName;
            }

            this.getPileList(this.basicForm.stationId, true);
            this.editOriginalPileCode = [];
            if (res.data.pileGunList.length > 0) {
              this.chargingPileForm = { ...res.data };
              this.chargingPileForm.pileGunList.forEach((item) => {
                const arr = [...item.gunList];
                this.loading = true;
                pileGunList({
                  pileNo: item.pileCode,
                  pileName: item.pileName,
                }).then((response) => {
                  // pileGunList({ pileNo: "***********", pileName: "12号桩"}).then((response) => {
                  this.loading = false;
                  item.gunList = [...response?.data];
                  item.count = item.gunList.length;
                  const gunMap = this.getCheckedGunMap(arr);
                  const list = [];
                  item.gunList.forEach((o) => {
                    if (gunMap.hasOwnProperty(o.gunNo)) {
                      // o["checked"] = true;
                      // o["gunDesc"] = gunMap[o.gunNo]["gunDesc"] || "";
                      this.$set(o, "checked", true);
                      this.$set(o, "gunDesc", gunMap[o.gunNo]["gunDesc"]);
                    }
                    list.push(o);
                  });
                  item.gunList = [...list];
                  this.editOriginalPileCode.push({
                    code: item.pileCode,
                    name: item.pileName,
                    label: `${item.pileCode}-${item.pileName}`,
                  });
                  if (
                    !this.pileOptions.some((x) => x.pileCode === item.pileCode)
                  ) {
                    item.pileCode = `${item.pileCode}-${item.pileName}`;
                  }
                });
              });
              console.log(this.chargingPileForm.pileGunList, "====遍历结果");
            }
            this.$refs.upload.fileList = res.data.createDocList;
          });
        }
      },
      immediate: true,
    },
    basicForm: {
      handler(newVal) {
        console.log("basicForm----watch", newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    console.log("created");
    await this.getStationList();
    await this.getUserList();
    this.getOrderTypeOptions();
    await this.getTreeData();
    this.getBusinessTypeOptions();

    this.handleStartDate = new Date();
  },
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

/deep/ .el-card {
  margin-bottom: 10px;
}

/deep/ .el-button--medium {
  padding: 12px 24px;
  font-size: 18px;
  border-radius: 4px;
}
</style>
