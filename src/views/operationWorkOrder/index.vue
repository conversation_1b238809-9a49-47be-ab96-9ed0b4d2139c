<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['operation:workOrder:export'])"
      @handleExport="handleExport"
    >
      <template slot="orderTypeName">
        <el-row>
          <el-col :span="8">
            <el-select
              v-model="searchForm.orderTypeParentName"
              clearable
              filterable
              style="width: 100%"
              @change="
                (val) => {
                  return orderTypeParentChange(val, 2);
                }
              "
            >
              <el-option
                v-for="item in orderTypeParentOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.orderTypeName"
              clearable
              filterable
              style="width: 100%"
              :disabled="searchForm.tag == '1' || searchForm.tag == '2'"
              @change="
                (val) => {
                  return orderTypeParentChange(val, 3);
                }
              "
            >
              <el-option
                v-for="item in orderTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.threeOrderTypeName"
              clearable
              filterable
              style="width: 100%"
              :disabled="searchForm.tag == '1' || searchForm.tag == '2'"
            >
              <el-option
                v-for="item in threeOrderTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
        </el-row>
      </template>
      <template slot="faultType">
        <el-row>
          <el-col :span="8">
            <el-select
              v-model="searchForm.oneFaultType"
              @change="handleFirstFaultStrChange"
              style="width: 100%;"
              clearable
              filterable
            >
              <el-option
                v-for="item in firstFaultOptions"
                :label="item.typeName"
                :value="item.id"
                :key="item.id"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.twoFaultType"
              @change="handleSecondFaultStrChange"
              style="width: 100%;"
              clearable
              filterable
            >
              <el-option
                v-for="item in secondFaultOptions"
                :label="item.typeName"
                :value="item.id"
                :key="item.id"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.threeFaultType"
              style="width: 100%;"
              clearable
              filterable
            >
              <el-option
                v-for="item in thirdFaultOptions"
                :label="item.typeName"
                :value="item.id"
                :key="item.id"
              />
            </el-select>
          </el-col>
        </el-row>
      </template>
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :checkbox="true"
        :batchDelete="true"
        @handleSelectionChange="tableSelect"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['operation:workOrder:create']"
            @click.stop="handleCreate"
          >
            创建工单
          </el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['operation:workOrder:type']"
            @click.stop="handleOrderType"
          >
            工单类型
          </el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['operation:workOrder:faultType']"
            @click.stop="handleFaultType"
          >
            故障类别
          </el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['operation:workOrder:setGroup']"
            @click.stop="handleProcessGroup"
          >
            设置分组
          </el-button>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['operation:workOrder:manage']"
            @click.stop="openStationManage"
          >
            站点责任人管理
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="handleRemark(row)"
            v-if="showOperateButton(row, 'remark')"
            v-has-permi="['operation:workOrder:remark']"
          >
            备注
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="openEditPage(row)"
            v-if="showOperateButton(row, 'edit')"
            v-has-permi="['operation:workOrder:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="transfer(row)"
            v-if="showOperateButton(row, 'handleOver')"
            v-has-permi="['operation:workOrder:handleOver']"
          >
            转派
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="showHandleDialog(row)"
            v-if="showOperateButton(row, 'handle')"
            v-has-permi="['operation:workOrder:handle']"
          >
            处理
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="stopOrder(row)"
            v-if="showOperateButton(row, 'finish')"
            v-has-permi="['operation:workOrder:stop']"
          >
            结束
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="orderPushMsg(row)"
            v-if="showOperateButton(row, 'remind')"
            v-has-permi="['operation:workOrder:remind']"
          >
            催单
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['operation:workOrder:detail']"
          >
            详情
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="check(row)"
            v-if="showOperateButton(row, 'check')"
            v-has-permi="['operation:workOrder:check']"
          >
            审核
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="updateTag(row)"
            v-if="showOperateButton(row, 'tag')"
            v-has-permi="['operation:workOrder:tag']"
          >
            标签
          </el-button>
        </template>
        <template slot="stationTag" slot-scope="{ row }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in row.stationNameArr"
          >
            {{ item }}
          </el-tag>
        </template>
        <template slot="orderDesc" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="openDescDetail(row.orderDesc)"
            >查看
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row, column }">
          <span :style="row.isTimeout === 'Y' ? 'color:red' : ''">{{
            row[column.property] === "Y"
              ? "是"
              : row[column.property] === "N"
              ? "否"
              : row[column.property]
          }}</span>
        </template>
        <template slot="tags" slot-scope="{ row }">
          <el-tag
            class="tags-item"
            :key="item"
            v-for="item in getFaultTypeArr(row)"
            type="info"
          >
            <el-tooltip :content="item" placement="top-start">
              <span>{{ item }}</span>
            </el-tooltip>
          </el-tag>
        </template>
      </GridTable>
    </el-card>

    <el-dialog
      title="审核工单"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeVisible"
      append-to-body
      width="50%"
    >
      <el-form :model="form" ref="form" label-width="110px" :rules="rules">
        <el-form-item label="工单状态" prop="orderStatus">
          <el-select v-model="form.orderStatus" style="width: 100%;">
            <el-option
              v-for="item in checkOrderOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="故障类别" prop="oneFaultTypes">
          <el-row>
            <el-col :span="8">
              <el-select
                v-model="form.oneFaultTypes"
                multiple
                @change="handleFirstFaultChange"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in firstFaultOptions"
                  :label="item.typeName"
                  :value="item.id"
                  :key="item.id"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="form.twoFaultTypes"
                multiple
                @change="handleSecondFaultChange"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in secondFaultOptions"
                  :label="item.typeName"
                  :value="item.id"
                  :key="item.id"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="form.threeFaultTypes"
                multiple
                style="width: 100%;"
              >
                <el-option
                  v-for="item in thirdFaultOptions"
                  :label="item.typeName"
                  :value="item.id"
                  :key="item.id"
                />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="通知组" prop="groupIds">
          <el-select
            v-model="form.groupIds"
            multiple
            @change="handleGroupChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupOptions"
              :label="item.groupName"
              :value="item.groupId"
              :key="item.groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知人" prop="userIds">
          <el-select
            v-model="form.userIds"
            multiple
            @change="handleGroupMemberChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupMemberOptions"
              :label="item.nickName + '-' + item.userName"
              :value="item.userId"
              :key="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="form.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备注" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="checkLoading" @click.stop="closeVisible"
          >取消
        </el-button>
        <el-button :loading="checkLoading" type="primary" @click="submit"
          >确定
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="转派工单"
      :visible.sync="handleOverVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="transferform"
        ref="transferform"
        label-width="110px"
        :rules="rules1"
        v-if="this.channel !== '03'"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model="transferform.deptId"
                :options="deptOptions"
                placeholder="请选择归属部门"
                @select="handleNodeClick"
                :beforeClearAll="beforeClearAll"
                :default-expand-level="1"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="人员" prop="handleUser">
              <el-select
                v-model="transferform.handleUser"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in userOption"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="transferform.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="改派原因" prop="remark">
          <el-input
            v-model="transferform.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <el-form
        :model="transferform2"
        ref="transferform2"
        label-width="110px"
        :rules="rules2"
        v-else
      >
        <el-form-item label="处理组" prop="groupIds">
          <el-select
            v-model="transferform2.groupIds"
            multiple
            @change="handleGroupIdsChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupOptions"
              :label="item.groupName"
              :value="item.groupId"
              :key="item.groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知人" prop="userIds">
          <el-select
            v-model="transferform2.userIds"
            multiple
            @change="handleGroupMemberChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupHandleMemberOptions"
              :label="item.nickName + '-' + item.userName"
              :value="item.userId"
              :key="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="transferform2.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="改派原因" prop="remark">
          <el-input
            v-model="transferform2.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit()">提交</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="结束工单"
      :visible.sync="stopVisible"
      @close="closeStopDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="stopForm" ref="stopForm" label-width="110px">
        <el-form-item label="结束原因" prop="remark">
          <el-input
            v-model="stopForm.remark"
            type="textarea"
            maxlength="500"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeStopDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleStopSubmit()"
          >提交
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="工单标签"
      :visible.sync="tagManageVisible"
      @close="close"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="tagForm" ref="tagForm" label-width="110px">
        <el-form-item
          label="处理标签"
          prop="handleTag"
          :rules="{
            required: true,
            message: '处理标签不能为空',
            trigger: 'blur',
          }"
        >
          <el-select v-model="tagForm.handleTag" style="width: 100%;">
            <el-option
              v-for="item in handleTageOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因" prop="remark">
          <el-input
            v-model="tagForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="close">取 消</el-button>
        <el-button type="primary" @click.stop="handleTagSubmit()"
          >提交
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="工单处理"
      :visible.sync="handleDialogVisible"
      @close="close"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="handleForm"
        ref="handleForm"
        label-width="110px"
        :rules="handleFormRules"
      >
        <el-form-item label="处理类型" prop="handleType">
          <el-select v-model="handleForm.handleType" style="width: 100%;">
            <el-option
              v-for="item in handleTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理方式" prop="handleMethod">
          <el-select
            v-model="handleForm.handleMethod"
            multiple
            style="width: 100%;"
          >
            <el-option
              v-for="item in handleMethodOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="故障原因" prop="faultType">
          <el-select
            v-model="handleForm.faultType"
            multiple
            style="width: 100%;"
          >
            <el-option
              v-for="item in faultTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理说明" prop="handleDesc">
          <el-input
            v-model="handleForm.handleDesc"
            type="textarea"
            :rows="5"
            maxlength="500"
            style="width: 100%;"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="处理照片" prop="remark">
          <file-upload
            type="img"
            ref="upload"
            :limit="9"
            textTip="支持批量上传，上传格式为jpg/png文件"
          />
        </el-form-item>
        <el-form-item label="处理标签" prop="handleTag">
          <el-select v-model="handleForm.handleTag" style="width: 100%;">
            <el-option
              v-for="item in handleTageOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeHandleDialog">取 消</el-button>
        <el-button :loading="saveLoading" @click.stop="save">保存</el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click.stop="handleDialogSubmit()"
          >提交
        </el-button>
      </div>
    </el-dialog>
    <el-drawer title="查看问题描述" :visible.sync="drawerVisible" size="60%">
      <div v-html="orderDesc" class="order-desc"></div>
    </el-drawer>
    <el-dialog
      title="备注"
      :visible.sync="remarkVisible"
      @close="closeRemarkDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="remarkForm"
        ref="remarkForm"
        label-width="110px"
        :rules="remarkFormRules"
      >
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="remarkForm.remark"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="请输入"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeRemarkDialog">取 消</el-button>
        <el-button
          :loading="remarkLoading"
          @click.stop="saveRemark"
          type="primary"
          >确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  check,
  childrenList,
  exportExcel,
  fistLevelList,
  handleDetail,
  handleOrder,
  pushMsg,
  queryList,
  stopOrder,
  submitRemark,
  transferOrder,
  updateOrderHandleTag,
  getAllDeptList,
} from "@/api/operationWorkOrder";
import checkPermission from "@/utils/permission.js";
import { queryTreeList } from "@/api/workOrderType/index.js";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { regionData } from "element-china-area-data";
import { queryGroupList, queryGroupMemberList } from "@/api/errorPush/index.js";
import { childFaultList, firstFaultList } from "@/api/faultType/index.js";
import { mapGetters } from "vuex";
import { listAllUser, listDept } from "@/api/common";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FileUpload from "@/components/Upload/fileUpload2.vue";
import { getToken } from "@/utils/auth";
import { queryLevelList } from "@/api/station/station";
import {
  MAINTENANCE_ORDER_CLICK_PUSH,
  MAINTENANCE_ORDER_CLICK_REMARK,
  MAINTENANCE_ORDER_CLICK_REPORT,
  MAINTENANCE_ORDER_FINISH_SUCCESS,
  MAINTENANCE_ORDER_FLLOWUP_SUCCESS,
  MAINTENANCE_ORDER_HANDLE_SUCCESS,
  MAINTENANCE_ORDER_REJECT_SUCCESS,
  MAINTENANCE_ORDER_STOP_SUCCESS,
  MAINTENANCE_ORDER_TRANSFER_SUCCESS,
} from "@/utils/track/track-event-constants";
import { timeDiffFormat } from "@/utils/comm";

export default {
  name: "operationWorkOrder",
  components: { FileUpload, AdvancedForm, GridTable, Treeselect },
  data() {
    return {
      transferform2: {
        groupIds: [],
        userIds: [],
        messageTypes: ["01", "02", "03"],
        remark: undefined,
        orderNo: undefined,
      },
      orderId: "",
      firstFaultOptions: [],
      secondFaultOptions: [],
      thirdFaultOptions: [],
      groupOptions: [],
      groupMemberOptions: [],
      checkOrderOptions: [
        { value: "4", label: "驳回" },
        { value: "7", label: "待回访" },
        { value: "5", label: "已完结" },
      ],
      remarkVisible: false,
      drawerVisible: false,
      remarkLoading: false,
      orderDesc: "",
      config: [],
      stationId: undefined,
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "orderNo",
          title: "工单编号",
          treeNode: true,
        },
        {
          field: "channel",
          title: "工单来源",
          showOverflowTooltip: true,
          formatter: this.channelFormat,
          titlePrefix: {
            message: `来源说明：
              1、手动：在能源维保通的工单管理中，手动创建的工单；
              2、客服工单：客服工单系统推送过来的工单；
              3、充电平台：告警运维中心推送过来的告警信息，在能源维保通的异常信息中手动转工单或自动生成的运维工单。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "businessType",
          title: "业务类型",
          showOverflowTooltip: true,
          formatter: this.businessTypeFormat,
        },
        {
          field: "orderTypeName",
          title: "工单类型",
          formatter: this.orderTypeFormat,
        },
        {
          field: "faultTypeName",
          title: "故障类别",
          slots: { default: "tags" },
          // formatter: this.orderTypeFormat,
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "orderDesc1",
          title: "问题描述",
          // showOverflowTooltip: true,
          // showOverflow: true,
          slots: { default: "orderDesc" },
          width: "100px",
        },
        {
          field: "stationName",
          title: "站点名称",
          showOverflowTooltip: true,
        },
        {
          field: "stationGrade",
          title: "站点等级",
        },
        {
          field: "deptName",
          title: "能投大区",
        },
        {
          field: "stationNameArr",
          title: "站点标签",
          slots: { default: "stationTag" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "address",
          title: "地址",
          showOverflowTooltip: true,
          customWidth: 200,
          formatter: this.addressFormat,
        },
        {
          field: "createByName",
          title: "创建人",
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "orderStatus",
          title: "工单状态",
          formatter: this.orderStatusFormat,
        },
        {
          field: "handleGroupName",
          title: "处理组",
        },
        {
          field: "handleUserName",
          title: "处理人",
        },
        {
          field: "handleTime",
          title: "处理时间",
        },
        {
          field: "isTimeout",
          title: "是否超时",
          slots: { default: "status" },
        },
        {
          field: "timeoutStr",
          title: "超时时长",
          slots: { default: "status" },
        },
        {
          field: "handleTag",
          title: "处理标签",
          formatter: this.handleTagFormat,
        },
        {
          field: "closeUserNickName",
          title: "关单人",
        },
        {
          field: "finishTime",
          title: "完结时间",
        },
        // {
        //   field: "remark",
        //   title: "备注",
        //   width: 100,
        // },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderTypeName: "",
        orderTypeParentName: "",
        threeOrderTypeName: "",
        oneFaultType: "",
        threeFaultType: "",
        twoFaultType: "",
      },
      rules1: {
        handleUser: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },
      rules2: {
        groupIds: [
          { required: true, message: "请选择处理组", trigger: "change" },
        ],
      },
      total: 0,
      loading: false,
      tableId: "stationList",
      detailVisible: false,
      stopVisible: false,
      rowInfo: {},
      stationTypeDict: [],
      stationTagDict: [],
      addProjectDialogVisible: false,
      pageType: "detail",
      orderNo: undefined,
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handleOverVisible: false,
      businessTypeOptions: [],
      handleTageOptions: [],
      orderStatusOptions: [],
      areaData: [],
      deptOptions: [],
      userOption: [],
      transferform: {
        deptId: undefined,
        orderNo: undefined,
        handleUser: undefined,
        messageTypes: ["01", "02", "03"],
      },
      stopForm: {},
      tagForm: {},
      channelOptions: [
        { dictLabel: "手动", dictValue: "01" },
        { dictLabel: "客服", dictValue: "02" },
        { dictLabel: "充电平台", dictValue: "03" },
      ],
      orderTypeParentOptions: [
        { dictLabel: "故障工单", dictValue: "故障工单" },
        { dictLabel: "抄表工单", dictValue: "抄表工单" },
        { dictLabel: "真车充电测试派单", dictValue: "真车充电测试派单" },
      ],
      orderTypeOptions: [
        // { dictLabel: "急停故障", dictValue: "急停故障" },
        // { dictLabel: "离线断电", dictValue: "离线断电" },
        // { dictLabel: "离线断网", dictValue: "离线断网" },
        // {
        //   dictLabel: "电桩上报故障（不含急停）",
        //   dictValue: "电桩上报故障（不含急停）",
        // },
        // {
        //   dictLabel: "离线&故障（不含急停）同时发生",
        //   dictValue: "离线&故障（不含急停）同时发生",
        // },
        // { dictLabel: "潜藏故障", dictValue: "潜藏故障" },
        // { dictLabel: "抄表工单", dictValue: "抄表工单" },
        // { dictLabel: "真实充电测试派单", dictValue: "真实充电测试派单" },
      ],
      visible: false,
      form: {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      },
      flag: true,
      checkLoading: false,
      handleTypeOptions: [],
      handleMethodOptions: [],
      faultTypeOptions: [],
      handleForm: {
        handleType: "",
        handleMethod: "",
        faultType: "",
      },
      handleFormRules: {
        handleType: {
          required: true,
          message: "处理类型不能为空",
          trigger: "blur",
        },
        handleMethod: {
          required: true,
          message: "处理方式不能为空",
          trigger: "blur",
        },
        faultType: {
          required: true,
          message: "故障原因不能为空",
          trigger: "blur",
        },
        handleDesc: { max: 500, message: "500字符以内", trigger: "blur" },
      },
      submitLoading: false,
      saveLoading: false,
      handleDialogVisible: false,
      curOrderCreateTime: "",
      curOrderHandleTime: "",
      threeOrderTypeOptions: [],
      flattenData: [],
      remarkForm: {
        remark: "",
      },
      remarkFormRules: {
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
      },
      remarkNo: "",
      handRow: {
        orderNos: [],
      },
      deptOptionList: [],
      isInit: true,
      channel: "01",
      levelOptions: [],
      groupAllOptions: [],
      groupHandleMemberOptions: [],
    };
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    rules() {
      return {
        orderStatus: [
          { required: true, trigger: "blur", message: "请选择工单状态" },
        ],
        reason: [
          {
            required: this.form.orderStatus == "4",
            message: "工单状态为驳回时，备注不能为空！",
            trigger: "blur",
          },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
        oneFaultTypes: [
          { required: true, message: "请选择故障类型", trigger: "blur" },
        ],
      };
    },
  },
  watch: {},
  async created() {
    //站点类型
    await this.getDicts("cm_station_type").then((response) => {
      this.stationTypeDict = response?.data;
    });
    await this.getDicts("cm_station_tag").then((response) => {
      this.stationTagDict = response?.data;
    });
    this.getTreeselect();
    this.getOrderTypeOptions();
    this.getTreeData();
    this.getDeptList();
  },
  mounted() {
    Promise.all([
      this.getBusinessTypeOptions(),
      this.getOrderStatusOptions(),
      this.getAreaData(regionData),
      this.getHandleTypeOptions(),
      this.getHandleTagOptions(),
      this.getHandleMethodOptions(),
      this.getFaultTypeOptions(),
      this.getList(),
      this.getListUser(),
      this.getDeptList(),
      this.getGroupList(),
      firstFaultList().then((res) => {
        this.firstFaultOptions = res?.data;
      }),
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
    this.token = getToken();
  },
  async activated() {
    //站点类型
    await this.getDicts("cm_station_type").then((response) => {
      this.stationTypeDict = response?.data;
    });
    await this.getDicts("cm_station_tag").then((response) => {
      this.stationTagDict = response?.data;
    });
    this.getTreeselect();
    this.getOrderTypeOptions();
    this.getTreeData();
    this.getDeptList();
    Promise.all([
      this.getBusinessTypeOptions(),
      this.getOrderStatusOptions(),
      this.getAreaData(regionData),
      this.getHandleTypeOptions(),
      this.getHandleTagOptions(),
      this.getHandleMethodOptions(),
      this.getFaultTypeOptions(),
      this.getList(),
      this.getListUser(),
      this.getGroupList(),
    ]).then(() => {});
    this.token = getToken();
  },
  methods: {
    checkPermission,
    getFaultTypeArr(row) {
      const { oneFaultType = "", twoFaultType = "", threeFaultType = "" } = row;
      let result = [];
      let arrA = oneFaultType.split(",").filter((item) => item !== "");
      let arrB = twoFaultType.split(",").filter((item) => item !== "");
      let arrC = threeFaultType.split(",").filter((item) => item !== "");

      result = result.concat(arrA, arrB, arrC);
      return result;
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        orderNos: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.orderNos = tableData.map((v) => v.orderNo);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleSecondFaultChange(val) {
      this.form.threeFaultTypes = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val }).then((res) => {
          this.thirdFaultOptions = res?.data;
        });
      }
    },
    handleSecondFaultStrChange(val) {
      this.searchForm.threeFaultType = "";
      if (val) {
        childFaultList({ ids: [val] }).then((res) => {
          this.thirdFaultOptions = res?.data;
        });
      }
    },
    handleFirstFaultChange(val) {
      this.form.twoFaultTypes = [];
      this.form.threeFaultTypes = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val }).then((res) => {
          this.secondFaultOptions = res?.data;
        });
      }
    },
    handleFirstFaultStrChange(val) {
      this.searchForm.twoFaultType = "";
      this.searchForm.threeFaultType = "";
      if (val) {
        childFaultList({ ids: [val] }).then((res) => {
          this.secondFaultOptions = res?.data;
        });
      }
    },
    handleGroupMemberChange(val) {
      console.log("通知人---", val);
    },
    //处理组变化
    async handleGroupIdsChange(val) {
      if (!val || val?.length === 0) {
        this.groupHandleMemberOptions = [];
      } else {
        const res = await queryGroupMemberList({ groupIds: val });
        this.groupHandleMemberOptions = res?.data;
      }
      const arr = this.groupHandleMemberOptions?.map((x) => x.userId);
      this.transferform2.userIds = this.transferform2.userIds?.filter(
        (item) => arr.indexOf(item) > -1
      );
    },
    //通知组变化
    handleGroupChange(val) {
      console.log("通知组", val);
      queryGroupMemberList({ groupIds: val }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
    },
    async saveRemark() {
      this.$refs.remarkForm.validate(async (valid) => {
        if (!valid) return false;
        this.remarkLoading = true;
        const params = {
          orderNo: this.remarkNo,
          remark: this.remarkForm.remark,
        };
        const res = await submitRemark(params);
        this.remarkLoading = false;
        if (res?.code == "10000") {
          this.$message.success("备注提交成功");
          this.remarkVisible = false;
          this.getList();

          this.reportTrackEvent(MAINTENANCE_ORDER_CLICK_REMARK);
        }
      });
    },
    handleRemark(row) {
      this.remarkVisible = true;
      this.remarkNo = row.orderNo;
    },
    openDescDetail(desc) {
      this.drawerVisible = true;
      this.orderDesc = desc;
    },
    getOrderTypeOptions() {
      fistLevelList().then((res) => {
        this.orderTypeParentOptions = res?.data?.map((x) => {
          return { label: x.typeName, value: x.id };
        });
        this.orderTypeParentOptions.push(
          ...[
            { label: "全部", value: "all" },
            { label: "其他（已删除类型）", value: "other" },
          ]
        );
      });
    },
    //工单类型按钮 跳转至工单类型页面
    handleOrderType() {
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/workOrderType",
      });
    },
    handleFaultType() {
      this.$router.push({
        path: "/faultType",
      });
    },
    orderTypeParentChange(val, level) {
      if (val === "all") {
        this.searchForm.tag = 1;
        this.searchForm.orderTypeName = undefined;
        this.searchForm.threeOrderTypeName = undefined;
        return;
      }
      if (val === "other") {
        this.searchForm.tag = 2;
        this.searchForm.orderTypeName = undefined;
        this.searchForm.threeOrderTypeName = undefined;
        return;
      }
      this.searchForm.tag = 0;
      if (level == 2) {
        this.searchForm.orderTypeName = undefined;
        this.searchForm.threeOrderTypeName = undefined;
        this.threeOrderTypeOptions = [];
        childrenList({ id: val }).then((res) => {
          this.orderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      } else {
        this.searchForm.threeOrderTypeName = undefined;
        childrenList({ id: val }).then((res) => {
          this.threeOrderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      }
    },
    //获取用户列表
    async getListUser(param) {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      if (param) {
        params.orgNo = Number(param);
      }
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
      this.userOptionCopy = data;
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    //能投大区下拉选项
    getDeptList() {
      getAllDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    closeDialog() {
      let formName = this.channel === "03" ? "transferform2" : "transferform";
      this.$refs[formName].resetFields();
      this.handleOverVisible = false;
    },
    closeStopDialog() {
      this.$refs.stopForm.resetFields();
      this.stopVisible = false;
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    beforeClearAll() {
      this.transferform.handleUser = undefined;
      this.transferform.deptId = undefined;
      this.getListUser();
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.transferform.handleUser = undefined;
      this.getListUser(data.deptId);
    },
    handleStopSubmit() {
      this.$refs.stopForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.stopForm,
            orderNo: this.orderNo,
          };

          const loading = this.$loading({
            lock: true,
            text: "结束中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          stopOrder(params)
            .then((res) => {
              if (res?.success) {
                this.closeStopDialog();
                this.handleQuery(this.searchForm);
                this.$message.success("结束成功");

                this.reportTrackEvent(MAINTENANCE_ORDER_STOP_SUCCESS, {
                  duration: timeDiffFormat(
                    new Date(this.curOrderCreateTime),
                    new Date()
                  ),
                });
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    handleTagSubmit() {
      this.$refs.tagForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.tagForm,
            orderNo: this.orderNo,
          };

          const loading = this.$loading({
            lock: true,
            text: "修改中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          updateOrderHandleTag(params)
            .then((res) => {
              if (res?.success) {
                this.handleQuery(this.searchForm);
                loading.close();
                this.$message.success("成功");
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.close();
              loading.close();
            });
        }
      });
    },
    handleSubmit() {
      let formName = this.channel === "03" ? "transferform2" : "transferform";
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {
            ...this[formName],
          };

          const loading = this.$loading({
            lock: true,
            text: "指派中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          transferOrder(params)
            .then((res) => {
              if (res?.success) {
                this.closeDialog();
                this.handleQuery(this.searchForm);
                loading.close();
                this.$message.success("指派成功");

                this.reportTrackEvent(MAINTENANCE_ORDER_TRANSFER_SUCCESS, {
                  noNotifyUsers:
                    !params?.handleUser || params?.handleUser?.length == 0,
                  noNotifyWays:
                    !params?.messageTypes || params?.messageTypes?.length == 0,
                  source: "web", //和app共用一个事件
                });
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    closeRemarkDialog() {
      this.remarkVisible = false;
      this.$refs.remarkForm.resetFields();
    },
    showDetail(row) {
      this.rowInfo = row;
      this.$router.push({
        path: "/operationWorkOrder/detail",
        query: {
          orderNo: row.orderNo,
          orderStatus: row.orderStatus,
          orderId: row.orderId,
        },
        // query: { orderNo: 'YW20240117095853601', orderStatus: row.orderStatus,  orderId: row.orderId}
      });
    },
    transfer(row) {
      this.channel = row.channel;
      this.handleOverVisible = true;
      this.groupHandleMemberOptions = [];
      let formName = this.channel === "03" ? "transferform2" : "transferform";
      this[formName].orderNo = row.orderNo;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        orderTypeName: "",
        orderTypeParentName: "",
        threeOrderTypeName: "",
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    stopOrder(row) {
      this.orderNo = row.orderNo;
      this.stopVisible = true;
      this.curOrderCreateTime = row.createTime;
    },
    orderPushMsg(row) {
      this.$confirm("确定APP和短信发送催单通知吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          orderNo: row.orderNo,
        };
        pushMsg(data).then((res) => {
          if (res?.success) {
            this.$message.success("催单成功");
            //更新列表
            this.getList();

            this.reportTrackEvent(MAINTENANCE_ORDER_CLICK_PUSH);
          } else {
            this.$message.error("催单失败");
          }
        });
      });
    },
    getTreeData() {
      queryTreeList({}).then((res) => {
        this.flattenData = this.flattenArray(res.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getList() {
      this.loading = true;
      const {
        orderTypeName,
        orderTypeParentName,
        threeOrderTypeName,
        oneFaultType,
        twoFaultType,
        threeFaultType,
        handleType,
        ...rest
      } = this.searchForm;
      let params = {
        ...rest,
      };
      params["orderTypeName"] = this.flattenData?.find(
        (x) => x.id == orderTypeName
      )?.typeName;
      params["orderTypeParentName"] = this.flattenData?.find(
        (x) => x.id == orderTypeParentName
      )?.typeName;
      params["threeOrderTypeName"] = this.flattenData?.find(
        (x) => x.id == threeOrderTypeName
      )?.typeName;
      params["oneFaultType"] = this.firstFaultOptions?.find(
        (x) => x.id == oneFaultType
      )?.typeName;
      params["twoFaultType"] = this.secondFaultOptions?.find(
        (x) => x.id == twoFaultType
      )?.typeName;
      params["threeFaultType"] = this.thirdFaultOptions?.find(
        (x) => x.id == threeFaultType
      )?.typeName;
      params["handleTypeName"] = this.handleTypeOptions?.find(
        (x) => x.dictValue == handleType
      )?.dictLabel;

      if (Array.isArray(params.createTime)) {
        params.startCreateTime = params.createTime[0] + " 00:00:00";
        params.endCreateTime = params.createTime[1] + " 23:59:59";
        delete params.createTime;
      }
      if (Array.isArray(params.handleTime)) {
        params.startHandleTime = params.handleTime[0] + " 00:00:00";
        params.endHandleTime = params.handleTime[1] + " 23:59:59";
        delete params.handleTime;
      }
      if (Array.isArray(params.finishTime)) {
        params.startFinishTime = params.finishTime[0] + " 00:00:00";
        params.endFinishTime = params.finishTime[1] + " 23:59:59";
        delete params.finishTime;
      }

      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }

      queryList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "orderNo",
          title: "工单编号",
          type: "input",
          placeholder: "请填写工单编号",
        },
        {
          key: "createByName",
          title: "创建人",
          type: "input",
          placeholder: "请填写创建人",
        },
        {
          key: "createTime",
          title: "创建时间",
          type: "dateRange",
          placeholder: "请选择创建时间",
          startPlaceholder: "创建开始时间",
          endPlaceholder: "创建结束时间",
        },
        {
          key: "orderStatus",
          title: "工单状态",
          type: "select",
          placeholder: "请选择工单状态",
          options: this.orderStatusOptions,
        },
        {
          key: "handleUserName",
          title: "处理人",
          type: "input",
          placeholder: "请输入处理人",
        },
        {
          key: "region",
          title: "省市区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "handleTime",
          title: "处理时间",
          type: "dateRange",
          placeholder: "请选择处理时间",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
        {
          key: "orderTypeName",
          parentKey: "orderTypeParentName",
          title: "工单类型",
          type: "slot",
          placeholder: "请选择工单类型",
          span: 24,
          colNum: 16,
          // options: this.orderTypeOptions,
          // parentOptions: this.orderTypeParentOptions,
        },
        {
          key: "channel",
          title: "工单来源",
          type: "select",
          placeholder: "请选择工单来源",
          options: this.channelOptions,
        },
        {
          key: "stationGrade",
          title: "站点等级",
          type: "select",
          placeholder: "请选择站点等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeName",
        },
        {
          key: "handleGroup",
          title: "处理组",
          type: "select",
          placeholder: "请选择处理组",
          options: this.groupAllOptions,
          optionLabel: "groupName",
          optionValue: "groupId",
        },
        {
          key: "isTimeout",
          title: "是否超时",
          type: "select",
          placeholder: "请选择是否超时",
          options: [
            { dictValue: "Y", dictLabel: "是" },
            { dictValue: "N", dictLabel: "否" },
          ],
        },
        {
          key: "faultType",
          title: "故障类别",
          type: "slot",
          placeholder: "请选择故障类别",
          span: 24,
          colNum: 16,
        },
        {
          key: "handleTag",
          title: "处理标签",
          type: "select",
          placeholder: "请选择处理标签",
          options: this.handleTageOptions,
        },
        {
          key: "closeUserNickName",
          title: "关单人",
          type: "input",
          placeholder: "请输入关单人",
        },
        {
          key: "finishTime",
          title: "完结时间",
          type: "dateRange",
          placeholder: "请选择完结时间",
        },
        {
          key: "handleType",
          title: "处理类型",
          type: "select",
          placeholder: "请选择处理类型",
          options: this.handleTypeOptions,
        },
      ];
    },
    //工单类型转换
    orderTypeFormat({ cellValue, row }) {
      const arr = [
        row.orderTypeParentName,
        row.orderTypeName,
        row.threeOrderTypeName,
      ];
      return arr.filter((x) => x).join(" | ");
    },
    channelFormat({ cellValue, row }) {
      return this.selectDictLabel(this.channelOptions, cellValue);
    },
    //业务类型转换
    businessTypeFormat({ cellValue, row }) {
      return this.selectDictLabel(this.businessTypeOptions, cellValue) == ""
        ? row.businessTypeName
        : this.selectDictLabel(this.businessTypeOptions, cellValue);
    },
    handleTagFormat({ cellValue }) {
      return this.selectDictLabel(this.handleTageOptions, cellValue);
    },
    orderStatusFormat({ cellValue }) {
      return this.selectDictLabel(this.orderStatusOptions, cellValue);
    },
    addressFormat({ cellValue, row }) {
      const provinceAddress = this.areaData.find(
        (item) => item.value === row.province
      )?.label;
      const cityAddress = this.areaData.find((item) => item.value === row.city)
        ?.label;
      const countyAddress = this.areaData.find(
        (item) => item.value === row.county
      )?.label;
      return provinceAddress
        ? provinceAddress +
            "-" +
            cityAddress +
            "-" +
            countyAddress +
            "-" +
            (cellValue ? cellValue : "")
        : cellValue
        ? cellValue
        : "";
    },
    updateTag(row) {
      this.orderNo = row.orderNo;
      this.tagManageVisible = true;
    },
    close() {
      this.tagManageVisible = false;
    },
    handleCreate() {
      this.$router.push({
        path: "/operationWorkOrderAdd",
      });
    },
    getBusinessTypeOptions() {
      this.getDicts("business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    getHandleTagOptions() {
      this.getDicts("handle_tag").then((response) => {
        this.handleTageOptions = response?.data;
      });
    },
    getOrderStatusOptions() {
      this.getDicts("order_status").then((response) => {
        this.orderStatusOptions = response?.data;
      });
    },
    getHandleMethodOptions() {
      this.getDicts("handle_method").then((response) => {
        this.handleMethodOptions = response?.data;
      });
    },
    getFaultTypeOptions() {
      this.getDicts("fault_type").then((response) => {
        this.faultTypeOptions = response?.data;
      });
    },
    getHandleTypeOptions() {
      this.getDicts("handle_type").then((response) => {
        this.handleTypeOptions = response?.data;
      });
    },
    getAreaData(list) {
      list.forEach((item) => {
        this.areaData.push({ label: item.label, value: item.value });
        if (item?.children) {
          this.getAreaData(item.children);
        }
      });
    },
    showOperateButton(row, type) {
      //运维角色
      const isSystemOM = this.roles.includes("systemOM");
      //工单处理人角色
      const isHandler = row.handleUser
        ? row.handleUser === this.userId
        : row.handleGroupUser?.split(",").includes(this.userId);
      //工单责任人角色
      const isCharge = row.chargeUser === this.userId;
      //站点服务人角色
      const isService = row.serviceUser?.split(",").includes(this.userId);
      const btnArr = [
        {
          type: "remark",
          title: "备注",
          condition: (row) => {
            return (
              isSystemOM && ["1", "2", "3", "4", "7"].includes(row.orderStatus)
            );
          },
        },
        {
          type: "edit",
          title: "编辑",
          condition: (row) => {
            return isSystemOM && ["1", "7"].includes(row.orderStatus);
          },
        },
        {
          type: "handleOver",
          title: "转派",
          condition: (row) => {
            return (
              (isSystemOM && ["1", "2", "7"].includes(row.orderStatus)) ||
              (isHandler && ["1", "7"].includes(row.orderStatus))
            );
          },
        },
        {
          type: "handle",
          title: "处理",
          condition: (row) => {
            return (
              (isSystemOM && ["1", "2", "7"].includes(row.orderStatus)) ||
              (isHandler && ["1", "2", "4", "7"].includes(row.orderStatus))
            );
          },
        },
        {
          type: "finish",
          title: "结束",
          condition: (row) => {
            return isSystemOM && ["1", "7"].includes(row.orderStatus);
          },
        },
        {
          type: "remind",
          title: "催单",
          condition: (row) => {
            return isSystemOM && ["1", "7"].includes(row.orderStatus);
          },
        },
        {
          type: "detail",
          title: "详情",
          condition: (row) => {
            return isSystemOM || isHandler || isService || isCharge;
          },
        },
        {
          type: "tag",
          title: "标签",
          condition: (row) => {
            return isSystemOM && ["2", "3", "4"].includes(row.orderStatus);
          },
        },
        {
          type: "check",
          title: "审核",
          condition: (row) => {
            return isSystemOM && ["3", "4"].includes(row.orderStatus);
          },
        },
      ];
      return btnArr.find((item) => item.type === type)?.condition(row);
      // if (type === "edit" || type === "finish" || type === "remind") {
      //   return row.createBy === this.userId && row.orderStatus === "1";
      // } else if (type === "handleOver") {
      //   return (
      //     (row.handleUser === this.userId ||
      //       row.chargeUser === this.userId ||
      //       row.createBy === this.userId) &&
      //     row.orderStatus === "1"
      //   );
      // } else if (type === "handle") {
      //   return (
      //     (row.createBy === this.userId && row.orderStatus === "1") ||
      //     (row.handleUser === this.userId &&
      //       (row.orderStatus === "1" ||
      //         row.orderStatus === "2" ||
      //         row.orderStatus === "4")) ||
      //     (row.chargeUser === this.userId &&
      //       (row.orderStatus === "1" ||
      //         row.orderStatus === "2" ||
      //         row.orderStatus === "4"))
      //   );
      // } else if (type === "finish") {
      //   return (
      //     ((row.createBy === this.userId || row.handleUser === this.userId) &&
      //       (row.orderStatus === "1" || row.orderStatus === "2")) ||
      //     (row.handleUser === this.userId && row.orderStatus === "4")
      //   );
      // } else if (type === "check") {
      //   return (
      //     row.createBy === this.userId &&
      //     (row.orderStatus === "3" || row.orderStatus === "4")
      //   );
      // } else if (type === "detail") {
      //   return (
      //     row.createBy === this.userId ||
      //     row.handleUser === this.userId ||
      //     row.chargeUser === this.userId ||
      //     (row.serviceUser && row.serviceUser.indexOf(this.userId) != -1)
      //   );
      // } else if (type === "tag") {
      //   return (
      //     (row.createBy === this.userId &&
      //       (row.orderStatus === "3" || row.orderStatus === "4")) ||
      //     (row.createBy === this.userId && row.orderStatus === "2")
      //   );
      // }
    },
    openEditPage(row) {
      this.$router.push({
        path: "/operationWorkOrderAdd",
        query: { orderId: row.orderId },
      });
    },
    async handleExport() {
      const {
        orderTypeName,
        orderTypeParentName,
        threeOrderTypeName,
        ...rest
      } = this.searchForm;
      let params = {
        ...rest,
        orderNos: this.handRow.orderNos,
      };
      params["orderTypeName"] = this.flattenData?.find(
        (x) => x.id == orderTypeName
      )?.typeName;
      params["orderTypeParentName"] = this.flattenData?.find(
        (x) => x.id == orderTypeParentName
      )?.typeName;
      params["threeOrderTypeName"] = this.flattenData?.find(
        (x) => x.id == threeOrderTypeName
      )?.typeName;
      if (Array.isArray(params.createTime)) {
        params.startCreateTime = params.createTime[0] + " 00:00:00";
        params.endCreateTime = params.createTime[1] + " 23:59:59";
        delete params.createTime;
      }
      if (Array.isArray(params.handleTime)) {
        params.startHandleTime = params.handleTime[0] + " 00:00:00";
        params.endHandleTime = params.handleTime[1] + " 23:59:59";
        delete params.handleTime;
      }
      if (Array.isArray(params.finishTime)) {
        params.startFinishTime = params.finishTime[0] + " 00:00:00";
        params.endFinishTime = params.finishTime[1] + " 23:59:59";
        delete params.finishTime;
      }
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      let text =
        this.handRow.orderNos?.length == 0
          ? "是否确认导出所有数据?"
          : "是否确认导出所选数据?";
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          this.reportTrackEvent(MAINTENANCE_ORDER_CLICK_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    closeVisible() {
      this.visible = false;
      this.form = {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      };
    },
    validateReason(rule, value, callback) {
      if (this.flag && !value) {
        callback(new Error("审核不通过原因不能为空"));
      }
      callback();
    },
    //提交审核
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.checkLoading = true;
          const params = {
            ...this.form,
            orderNo: this.orderNo,
            orderId: this.orderId,
          };
          check(params)
            .then((res) => {
              this.checkLoading = false;
              this.$message.success("审核工单成功");
              this.visible = false;
              this.getList();

              this.reportAuditTrack();
            })
            .catch((err) => {
              this.checkLoading = false;
            });
        }
      });
    },
    reportAuditTrack() {
      switch (this.form.orderStatus) {
        case "4":
          //驳回
          this.reportTrackEvent(MAINTENANCE_ORDER_REJECT_SUCCESS, {
            orderStatus: "驳回",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            source: "web",
          });
          break;
        case "5":
          //已完结
          this.reportTrackEvent(MAINTENANCE_ORDER_FINISH_SUCCESS, {
            orderStatus: "已完结",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            finishDuration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ), //完结时长
            source: "web",
          });
          break;
        case "7":
          //待回访
          this.reportTrackEvent(MAINTENANCE_ORDER_FLLOWUP_SUCCESS, {
            orderStatus: "待回访",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            finishDuration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ), //完结时长
            source: "web",
          });
          break;
      }
    },
    getGroupList() {
      queryGroupList({ pageNum: 1, pageSize: 9999, status: 0 }).then((res) => {
        this.groupOptions = res?.data;
      });
      queryGroupList({ pageNum: 1, pageSize: 9999 }).then((res) => {
        this.groupAllOptions = res?.data;
      });
    },
    check(row) {
      this.visible = true;
      this.curOrderCreateTime = row.createTime;
      this.curOrderHandleTime = row.handleTime;
      this.secondFaultOptions = [];
      this.thirdFaultOptions = [];
      queryGroupMemberList({ groupIds: [] }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
      this.orderNo = row.orderNo;
      this.orderId = row.orderId;
      // this.form = { ...row };
    },
    save() {
      const vm = this;
      this.$refs.handleForm.validate((valid) => {
        if (valid) {
          // if (
          //   vm.handleForm.orderStatus === "2" &&
          //   vm.handleForm.handleUser !== vm.userId
          // ) {
          //   vm.$message.error("工单正在处理中，无法保存");
          //   return;
          // }
          vm.saveLoading = true;
          const params = { ...vm.handleForm };
          params.submitFlag = 0;
          vm.handleFormSubmit(params, "saveLoading", "保存");
        }
      });
    },
    handleDialogSubmit() {
      const vm = this;
      this.$refs.handleForm.validate((valid) => {
        if (valid) {
          // if (
          //   vm.handleForm.orderStatus === "2" &&
          //   vm.handleForm.handleUser !== vm.userId
          // ) {
          //   vm.$message.error("工单正在处理中，无法处理");
          //   return;
          // }
          if (
            vm.handleForm.orderStatus === "3" ||
            vm.handleForm.orderStatus === "5"
          ) {
            vm.$message.error("工单已处理完成，请勿重复提交");
            return;
          }
          vm.submitLoading = true;
          const params = { ...vm.handleForm };
          params.submitFlag = 1;
          vm.handleFormSubmit(params, "submitLoading", "提交");
        }
      });
    },
    handleFormSubmit(params, loading, text) {
      const vm = this;
      params.handleTypeName = vm.handleTypeOptions.find(
        (item) => item.dictValue === params.handleType
      ).dictLabel;
      const arr = [];
      params.handleMethod.forEach((o) => {
        const handleMethodName = vm.handleMethodOptions.find(
          (item) => item.dictValue === o
        ).dictLabel;
        arr.push(handleMethodName);
      });

      const faultArr = [];
      params.faultType.forEach((o) => {
        const faultTypeName = vm.faultTypeOptions.find(
          (item) => item.dictValue === o
        ).dictLabel;
        faultArr.push(faultTypeName);
      });

      params.docList = vm.$refs.upload.fileList;
      params.handleMethod = params.handleMethod.join(",");
      params.handleMethodName = arr.join(",");

      params.faultType = params.faultType.join(",");
      params.faultTypeName = faultArr.join(",");
      handleOrder(params)
        .then((res) => {
          vm[loading] = false;
          vm.$message.success(text + "成功");
          vm.handleDialogVisible = false;
          vm.getList();

          this.reportTrackEvent(MAINTENANCE_ORDER_HANDLE_SUCCESS, {
            isSubmit: params.submitFlag,
            noHandleTags: !params?.handleTag || params?.handleTag?.length == 0,
            duration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ),
            source: "web", //和app共用一个事件
          });
        })
        .catch((err) => {
          vm[loading] = false;
        });
    },
    showHandleDialog(row) {
      this.handleForm = {};
      this.handleForm.orderStatus = row.orderStatus;
      this.handleForm.orderNo = row.orderNo;
      this.handleForm.handleUser = row.handleUser;
      this.handleDialogVisible = true;
      this.curOrderCreateTime = row.createTime;
      this.getHandleDetail(row.orderNo);
      // this.getHandleDetail('YW20240116035029204')
    },
    getHandleDetail(orderId) {
      const vm = this;
      handleDetail({ orderNo: orderId }).then((res) => {
        if (res.data) {
          vm.handleForm = { ...vm.handleForm, ...res.data };
          vm.handleForm.handleMethod = res.data.handleMethod.indexOf(",")
            ? res.data.handleMethod.split(",")
            : [res.data.handleMethod];

          vm.handleForm.faultType = res.data.faultType.indexOf(",")
            ? res.data.faultType.split(",")
            : [res.data.faultType];
          vm.$refs.upload.fileList = res.data.docList || [];
        } else {
          vm.handleForm = { ...vm.handleForm, ...{} };
          vm.$refs.upload.fileList = [];
        }
      });
    },
    closeHandleDialog() {
      this.handleDialogVisible = false;
    },
    openStationManage() {
      this.$router.push({
        path: "/stationRelation",
      });
    },
    handleProcessGroup() {
      this.$router.push({
        path: "/errorPush/setProcessGroup",
        query: {},
      });
    },
  },
  watch: {
    $route: {
      handler(newVal) {
        if (Object.keys(this.$route.params).length > 0) {
          this.searchForm = {
            pageNum: 1,
            pageSize: 10,
            orderTypeName: "",
            orderTypeParentName: "",
            threeOrderTypeName: "",
            oneFaultType: "",
            threeFaultType: "",
            twoFaultType: "",
            ...this.$route.params,
          };
        }
        if (newVal.query.businessNo) {
          this.searchForm.orderNo = newVal.query.businessNo;
          this.getList();
        }
      },
      immediate: true,
    },
  },
};
</script>

<style scoped lang="less">
.order-desc {
  padding: 20px;
  margin: 0 20px;
  border: 1px solid #ccc;
  overflow: auto;
  white-space: pre-line;
}
/deep/ .upload-content {
  flex-direction: column;
}
.tags-item {
  margin-right: 10px;
  margin-bottom: 5px;
  width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
