<!-- 流程设计列表 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <!-- 测试网厅回显 -->
    <!-- <CreatForm /> -->

    <el-card>
      <GridTable
        :columns="columns"
        :column-auto-fit="false"
        :tableData="tableData1"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="pageTotal1"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            style="margin: 10px 0"
            @click="handleAdd"
            >新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="mini"
            @click.stop="goToFormCreate(row)"
            style="margin-right: 10px"
            >新增版本
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click.stop="formView(row)"
            style="margin-right: 10px"
            >版本管理
          </el-button>
          <el-button type="text" size="mini" @click.stop="deleteItem(row)"
            >删除
          </el-button>
          <el-button type="text" size="mini" @click.stop="copyWorkFlow(row)"
            >复制
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <el-dialog
      :title="title"
      v-if="visibleAdd"
      :visible.sync="visibleAdd"
      @close="visibleAdd = false"
      width="500px"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="流程名称" prop="formName">-->
        <!--          <el-input v-model="form.flowName" />-->
        <!--        </el-form-item>-->
        <el-form-item label="流程编码" prop="flowKey" style="display: none">
          <el-input v-model="form.flowKey" disabled />
        </el-form-item>
        <el-form-item label="流程类型" prop="flowType">
          <el-select
            clearable
            v-model="form.flowType"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="dict in flowTypeOptions"
              :key="dict.typeLabel"
              :label="dict.typeLabel"
              :value="dict.typeCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.flowDefDesc" :rows="5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div>
          <el-checkbox v-model="isOpenChecked">添加后自动打开设计</el-checkbox>
        </div>
        <div>
          <el-button type="primary" @click="submit">确 定</el-button>
          <el-button @click.stop="visibleAdd = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="复制"
      :visible.sync="copyVisible"
      @close="copyVisible = false"
      width="500px"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="流程名称" prop="formName">-->
        <!--          <el-input v-model="form.flowName" />-->
        <!--        </el-form-item>-->
        <el-form-item label="流程编码" prop="flowKey">
          <el-input v-model="form.flowKey" disabled />
        </el-form-item>
        <el-form-item label="流程类型" prop="flowType">
          <el-select clearable v-model="form.flowType" style="width: 100%">
            <el-option
              v-for="dict in flowTypeOptions"
              :key="dict.typeLabel"
              :label="dict.typeLabel"
              :value="dict.typeCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.flowDefDesc" :rows="5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div>
          <el-button type="primary" @click="copyFlowDef">确 定</el-button>
          <el-button @click.stop="copyVisible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="版本管理"
      v-if="visible"
      :visible.sync="visible"
      width="70%"
    >
      <el-table v-loading="loading" ref="multipleTable" :data="tableData">
        <el-table-column label="版本号" align="center" prop="flowVersion" />
        <el-table-column
          label="状态"
          align="center"
          prop="formStatus"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.flowStatus == 1 ? "启用" : "未启用" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="创建人"
          align="center"
          prop="flowCreator"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column
          label="更新时间"
          align="center"
          prop="updateTime"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          prop="updateTime"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              @click.stop="viewDesign(scope.row)"
              style="margin-right: 10px"
              >查看
            </el-link>
            <el-link
              type="primary"
              @click.stop="editFlowDesign(scope.row)"
              style="margin-right: 10px"
              >设计流程
            </el-link>
            <el-link
              type="primary"
              @click.stop="copy(scope.row)"
              style="margin-right: 10px"
              >复制
            </el-link>
            <el-link
              type="primary"
              style="margin-right: 10px"
              @click.stop="deleteInner(scope.row)"
              >删除
            </el-link>
            <el-link
              type="primary"
              style="margin-right: 10px"
              @click.stop="handleInvalid(scope.row)"
              >作废
            </el-link>
            <el-link type="primary" @click.stop="changeFormStatus(scope.row)"
              >{{ scope.row.flowStatus == "1" ? "取消发布" : "发布" }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <div></div>
        <div>
          <!-- <el-button type="primary" @click="submit">确 定</el-button> -->
          <el-button @click.stop="visible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  getKey,
  add,
  copyFlowDef,
  list,
  handleDelete,
  deleteItem,
  modifyStatus,
  handleCopy,
  queryByFlowId,
} from "@/api/process/lfDesign.js";
import { list as flowCateOptions } from "@/api/orderConfig/processTypeConfig.js";
import CreatForm from "@/views/process/components/CreatForm.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { invalidAllFlow, invalidFlow } from "@/api/business/flow/flow";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: { CreatForm, GridTable, AdvancedForm },
  data() {
    //这里存放数据
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        flowName: undefined,
        flowType: undefined,
        status: undefined,
      },
      finallySearch: null,
      loading: false,
      tableData: [],
      tableData1: [],
      isOpenChecked: false,
      pageTotal: 0,
      pageTotal1: 0,
      pageNum: 1,
      pageNum1: 1,
      pageSize: 10,
      pageSize1: 10,
      title: "新增",
      visibleAdd: false,
      visible: false,
      innerVisible: false,
      copyVisible: false,
      copyRow: "",
      inspectResultType: [], //流程类型
      formPlatOptions: [
        {
          dictValue: "1",
          dictValue: "PC",
        },
        {
          dictValue: "2",
          dictValue: "移动端",
        },
        {
          dictValue: "3",
          dictValue: "全部",
        },
      ], //流程平台
      flowTypeOptions: [], //流程类型
      form: {},
      rules: {
        // flowName: [
        //   { required: true, message: "请输入流程名称", trigger: "blur" },
        // ],
        flowKey: [
          { required: true, message: "请输入流程编码", trigger: "blur" },
        ],
        flowType: [
          { required: true, message: "请输入流程类型", trigger: "change" },
        ],
      },
      form2: {
        formTitle: "",
        formPlat: "",
        formDesc: "",
        formKey: "",
        formId: "",
      },
      rules2: {
        formTitle: [
          { required: true, message: "请输入表单标题", trigger: "blur" },
        ],
        formPlat: [
          { required: true, message: "请输入表单平台", trigger: "change" },
        ],
      },
      formStatusOptions: [
        {
          dictValue: "1",
          dictLabel: "已启用",
        },
        {
          dictValue: "0",
          dictLabel: "未启用",
        },
      ],
      config: [],
      tableId: "LFListTable",
      columns: [
        // {
        //   field: "flowName",
        //   title: "流程名称",
        //   showOverflowTooltip: true,
        // },
        {
          field: "flowTypeValue",
          title: "流程类型",
          showOverflowTooltip: true,
        },
        {
          field: "flowDefDesc",
          title: "描述",
          showOverflowTooltip: true,
        },
        {
          field: "formStatusValue",
          title: "状态",
        },
        {
          field: "flowCreator",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          field: "createTimeValue",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  computed: {},
  watch: {
    visibleAdd(newVal) {
      if (newVal) {
        this.getKey();
      } else {
        this.form = {};
        this.isOpenChecked = false;
      }
    },
    copyVisible(newVal) {
      if (newVal) {
        this.getKey();
      } else {
        this.form = {};
        this.isOpenChecked = false;
      }
    },
  },
  //方法集合
  methods: {
    //初始化
    initConfig() {
      this.config = [
        // {
        //   key: "flowName",
        //   title: "流程名称",
        //   type: "input",
        //   placeholder: "请输入流程名称",
        // },
        {
          key: "flowType",
          title: "流程类型",
          type: "select",
          options: this.flowTypeOptions,
          optionLabel: "typeLabel",
          optionValue: "typeCode",
          placeholder: "请选择流程类型",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          options: this.formStatusOptions,
          placeholder: "请选择状态",
        },
      ];
    },
    formatterFlowTypeOptions(row) {
      let name = "";
      this.flowTypeOptions.map((item) => {
        if (item.typeCode == row.flowType) {
          name = item.typeLabel;
        }
      });
      return name;
    },
    //获取流程类型
    getFlowTypeOptions() {
      let params = {};
      // params.typeStatus = "1";
      return flowCateOptions(params).then((res) => {
        this.flowTypeOptions = res.data;
      });
    },
    //复制
    copy(row) {
      if (this.tableData.length >= 6) {
        this.$eleMessage.info("版本数量已达到上线，无法复制版本");
        return;
      }
      let params = {
        flowId: row.flowId,
      };
      handleCopy(params).then((res) => {
        this.tableData.unshift(res.data);
        this.visible = false;
        this.getList();
      });
    },
    //作废 流程
    handleInvalid(row) {
      let that = this;
      this.$confirm("是否确认作废此流程所有的在途工单数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function() {
          let params = {
            flowId: row.flowId,
            flowKey: row.flowKey,
          };
          invalidAllFlow(params).then((res) => {
            that.visible = false;
            that.getList();
            this.$message.success("作废成功");
          });
        })
        ["catch"](function() {});
    },
    //删除流程
    deleteInner(row) {
      let that = this;
      this.$confirm(
        '是否确认删除流程版本号为"' + row.flowVersion + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          let params = {
            flowId: row.flowId,
          };
          handleDelete(params).then((res) => {
            that.$message.success("删除成功");
            that.visible = false;
            that.getList();
          });
        })
        ["catch"](function() {});
    },
    // 修改流程
    editFlowDesign(row) {
      if (row.flowStatus == "1") {
        this.$message.error("该流程已发布, 请取消发布后进行修改");
        return;
      }
      let params = {
        flowId: row.flowId,
      };
      queryByFlowId(params).then((res) => {
        if (res.code == 10000) {
          this.$router.push({
            path: "/process/process",
            query: { obj: JSON.stringify(res.data) },
          });
        }
      });
    },
    viewDesign(row) {
      let params = {
        flowId: row.flowId,
      };
      queryByFlowId(params).then((res) => {
        if (res.code == 10000) {
          this.$router.push({
            path: "/process/process",
            query: {
              obj: JSON.stringify({ ...res.data, ...{ viewFlag: true } }),
            },
          });
        }
      });
    },
    getKey() {
      getKey().then((res) => {
        this.$set(this.form, "flowKey", res.data);
      });
    },
    //新增
    handleAdd() {
      this.visibleAdd = true;
    },
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        params.pageSize = this.queryParams.pageSize;
      }
      this.getList(params);
    },
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    copyFlowDef() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.fromFlowKey = this.copyRow.flowKey;
          copyFlowDef(this.form).then((res) => {
            this.copyVisible = false;
            this.getList();
          });
        }
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          add(this.form).then((res) => {
            if (this.isOpenChecked) {
              // let tagsView = this.$store.state.tagsView.visitedViews;
              // let view = null;
              // for (let i = 0; i < tagsView.length; i++) {
              //   if (tagsView[i].path == "/process/process") {
              //     view = tagsView[i];
              //   }
              // }
              // this.closeSelectedTag(view);

              this.$router.push({
                path: "/process/process",
                query: { obj: JSON.stringify(res.data) },
              });
            }
            this.visibleAdd = false;
            this.getList();
          });
        }
      });
    },
    closeSelectedTag(view) {
      this.$store
        .dispatch("tagsView/delView", view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        this.$router.push(latestView);
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === "Dashboard") {
          // to reload home page
          this.$router.replace({ path: "/redirect" + view.fullPath });
        } else {
          this.$router.push("/");
        }
      }
    },
    //启用-未用
    changeFormStatus(row) {
      let param = {
        flowId: row.flowId,
        flowKey: row.flowKey,
        status: row.flowStatus,
      };
      modifyStatus(param).then((res) => {
        if (row.flowStatus == "1") {
          this.$message.success("取消发布成功");
        } else {
          this.$message.success("发布成功");
        }
        this.visible = false;
        this.getList();
      });
    },
    //新增设计
    goToFormCreate(row) {
      if (row.defFlows && row.defFlows.length >= 6) {
        this.$eleMessage.info("版本数量已达到上线，无法新建版本");
        return;
      }
      this.$router.push({
        path: "/process/process",
        query: { flowKey: row.flowKey, obj: JSON.stringify(row) },
      });
    },
    //表单视图
    formView(row) {
      this.visible = true;
      this.tableData = row.defFlows;
    },

    //复制表单
    copyWorkFlow(row) {
      this.copyVisible = true;
      this.copyRow = row;
    },
    //删除
    deleteItem(row) {
      let that = this;
      this.$confirm("是否确认删除该流程?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            flowDefId: row.flowDefId,
          };
          deleteItem(params).then((res) => {
            that.$message.success("删除成功");
            that.getList();
          });
        })
        ["catch"](function() {});
    },
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.finallySearch = args;
      this.loading = true;
      list(args)
        .then((res) => {
          this.tableData1 = res.data;
          this.pageTotal1 = res.total;
          this.tableData1.forEach((element) => {
            //流程类型
            this.$set(
              element,
              "flowTypeValue",
              this.formatterFlowTypeOptions(element)
            );
            //状态
            this.$set(
              element,
              "formStatusValue",
              element.flowStatus == 1 ? "启用" : "未启用"
            );
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  async created() {
    await this.getFlowTypeOptions();
    this.initConfig();
    this.handleQuery();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
