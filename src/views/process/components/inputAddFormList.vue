<!-- 关联表单列表组件 -->
<template>
  <div class="">
    <el-link
      type="primary"
      :underline="false"
      v-model="content"
      @click="handleInput"
    >
      <i class="el-icon-circle-plus-outline" />
      <span style="margin-left:3px;" >
        添加
      </span>
    </el-link>

    <el-dialog
      :title="title"
      v-if="visible"
      :visible.sync="visible"
      @close="visible = false"
      width="850"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="loading"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        :data="tableData"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          label="表单名称"
          align="center"
          prop="formName"
          :show-overflow-tooltip="true"
        />
<!--        <el-table-column-->
<!--          label="修改人"-->
<!--          align="center"-->
<!--          prop="defLastOperator"-->
<!--          :show-overflow-tooltip="true"-->
<!--        />-->
<!--        <el-table-column-->
<!--          label="修改时间"-->
<!--          align="center"-->
<!--          :show-overflow-tooltip="true"-->
<!--        >-->
<!--          <template slot-scope="scope">-->
<!--            {{ moment(scope.row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.formStatus == "1" ? "启用" : "未启用" }}
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="formDefDesc"
          :show-overflow-tooltip="true"
        />
        <el-table-column
            label="创建人"
            align="center"
            prop="defCreator"
            :show-overflow-tooltip="true"
        />
        <el-table-column
            label="创建时间"
            align="center"
            :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
      </el-table>


      <div slot="footer" class="dialog-footer">
        <!-- <div></div>
        <div> -->
        <el-button type="primary" @click="handleOk">确 定</el-button>
        <el-button @click.stop="visible = false">取 消</el-button>
        <!-- </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { list } from "@/api/process/formDesign.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  prop: ["newValue"],
  model: {
    event: "input-change",
    prop: "newValue",
  },
  data() {
    //这里存放数据
    return {
      content: this.newValue,
      visible: false,
      loading: false,
      title: "表单列表",
      tableData: [],
      pageTotal: 0,
      multipleSelection: [],
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    handleInput() {
      this.visible = true;
      // this.$emit('input-change', this.content)
    },
    handleSelectionChange(val) {
      console.log("[ this. ] >", val);
      this.multipleSelection = val;
    },
    handleOk() {
      if (this.multipleSelection.length >= 1) {
        this.$emit("changeForm", this.multipleSelection)
        this.$emit("input-change", this.content);
        this.visible = false;
      } else {
        this.$message.warning("请选择一条表单");
      }
    },
    getList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
        formStatus: 1
      };
      this.loading = true;
      list(params)
        .then((res) => {
          this.tableData = res.data;
          this.pageTotal = res.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getList();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.el-icon-circle-plus-outline {
  font-size: 20px;
}
/deep/.el-link--inner {
  display: flex;
  align-items: center;
}
</style>