<!-- 部门列表弹框 -->
<template>
  <div class="">
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="800px"
      append-to-body
      @close="$emit('close')"
    >
      <el-table
        v-loading="loading"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        :data="tableData.slice((currentPage-1)*pageSize,currentPage*pageSize)"
        :current-page.sync="currentPage"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          label="部门ID"
          align="center"
          prop="groupId"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="部门名称"
          align="center"
          prop="orgName"
          :show-overflow-tooltip="true"
        />
      </el-table>
      <div style="text-align: right;padding-top: 1em">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[10, 15, 20]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length">
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- <div></div>
        <div> -->
        <el-button type="primary" @click="handleOk">确 定</el-button>
        <el-button @click.stop="$emit('close')">取 消</el-button>
        <!-- </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getUserAvailGroups } from "@/api/process/lfDesign.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    //这里存放数据
    return {
      title: "部门",
      loading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      multipleSelection: [],
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    handleSizeChange(val) {
      this.pagesize = val;
    },
    handleCurrentChange(val) {
      this.currentPage = val;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      this.loading = true;
      getUserAvailGroups(params)
        .then((res) => {
          this.tableData = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleOk() {
      if (this.multipleSelection.length == 1) {
        this.$emit("chooseDepartmentList", this.multipleSelection)
        this.$emit("close")
      } else if (this.multipleSelection.length > 1) {
        if (!this.multiple) {
          this.$message.warning("请选择一条数据！")
          return
        } else {
          this.$emit("chooseDepartmentList", this.multipleSelection)
          this.$emit("close")
        }
      } else {
        this.$message.warning("请至少选择一条数据！")
        return
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getList()
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>
