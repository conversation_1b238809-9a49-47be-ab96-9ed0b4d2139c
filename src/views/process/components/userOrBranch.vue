<!--  -->
<template>
  <div class="">
    <el-col :span="7">
      <el-form-item :label="label" prop="selectType">
        <el-select
          v-model="form.selectType"
          @change="changeSelectType"
          size="mini"
        >
          <!-- <el-option label="无" value="1" /> -->
          <el-option label="个人" value="2" @click.native="clickItemOption(2)" />
<!--          <el-option label="候选人" value="3" @click.native="clickItemOption(3)" />-->
<!--          <el-option label="部门" value="4" @click.native="clickItemOption(4)" />-->
           <el-option label="指定值" value="5" />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="17">
      <el-form-item label="" v-if="form.selectType != 1" label-width="1px" prop="value">
        <el-input
          size="mini"
          placeholder=""
          v-model="form.name"
          :disabled="form.selectType != '5'"
        />
      </el-form-item>
    </el-col>


  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getUserAvailUsers, getUserAvailGroups } from "@/api/process/lfDesign.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  props: {
    label: {
      type: String,
      default: ""
    },
    arr: {
      type:Array,
      default:() => []
    },
    userIdList: {
      type:Array,
      default:() => []
    },
    branchList: {
      type:Array,
      default:() => []
    },
    nodeInfo: {
      type:Object,
      default:() => {}
    }
  },
  components: {},
  data() {
    //这里存放数据
    return {
      form: {
        selectType: "",
        name: "",
        value: "",
      },
      visibleUserList: false,
      tableDataUser: [],
      tableDataBranch: [],
      isCkick: false,
    };
  },
  computed: {

  },
  watch: {
    // "form.selectType": {
    //   handler(newVal) {
    //     this.$emit("changeType", newVal)
    //   },
    //   immediate:true,
    //   deep: true
    // },
    "arr": {
      async handler(newVal) {
        if (newVal.length == 0) {
          return
        }
        let name = []
        let value = []
        console.log("this.form.selectType", this.form.selectType);
        console.log("this.nodeInfo.properties['whale-flow_selectType']", this.nodeInfo.properties['whale-flow_selectType']);
        if (this.form.selectType == 2 || this.form.selectType == 3 || ((this.nodeInfo.properties['whale-flow_selectType'] == 2 || this.nodeInfo.properties['whale-flow_selectType'] == 3) && !this.isCkick)) {
          await this.getUserList()
          for (const item of newVal) {
            for (const el of this.tableDataUser) {
              if (item.userId == el.userId) {
                name.push(el.nickName)
                value.push(el.userId)
              }
            }
          }
          console.log("selectType", this.form.selectType);
          console.log("name", name);
          console.log("value", value);
        } else if (this.form.selectType == 4 || (this.nodeInfo.properties['whale-flow_selectType'] == 4 && !this.isCkick)) {
          await this.getBranchList()
          console.log("newVal4", newVal);
          for (const item of newVal) {
            for (const el of this.tableDataBranch) {
              if (item.orgId == el.orgId) {
                name.push(el.orgName)
                value.push(el.orgId)
              }
            }
          }
          console.log("selectType", this.form.selectType);
          console.log("name", name);
          console.log("value", value);
        } else if (this.form.selectType == 5 || (this.nodeInfo.properties['whale-flow_selectType'] == 5 && !this.isCkick)) {
          console.log("newVal5", newVal);
          name = newVal[0].userId
          value = newVal[0].userId
        }
        console.log("name最终", name);
        console.log("value最终", value);
        this.form.name = name.toString()
        this.form.value = value.toString()

        console.log("newVal最终", newVal);
        console.log("this.form", this.form);
      },
      deep: true,
      immediate: true
    },
    userIdList: {
      async handler(newVal) {
        if (newVal.length > 0) {
          await this.getUserList()
          let arr = []
          for (const item of newVal) {
            for (const el of this.tableDataUser) {
              if (item == el.userId) {
                arr.push(el)
              }
            }
          }
          let name = ""
          arr.map(el => {
            name += (el.nickName + ',')
          })
          this.$set(this.form, "name", name)
        }
      },
      deep: true,
      immediate:true
    },
    branchList: {
      async handler(newVal) {
        if (newVal.length > 0) {
          await this.getBranchList()
          let arr = []
          for (const item of newVal) {
            for (const el of this.tableDataBranch) {
              if (item == el.orgId) {
                arr.push(el)
              }
            }
          }
          let name = ""
          arr.map(el => {
            name += (el.orgName + ',')
          })
          this.$set(this.form, "name", name)
        }
      },
      deep: true,
      immediate:true
    }
  },
  //方法集合
  methods: {
    //点击相同选项触发事件
    clickItemOption(val) {
      if (val == 2) {
        this.$emit("changeType", 2)
        this.multiple = false
      } else if (val == '3') {
        //用户多选
        this.$emit("changeType", 3)
        this.multiple = true
      } else if (val == '4') {
        //部门多选
        this.$emit("changeType", 4)
        this.multiple = true
      }
    },
    changeSelectType(val) {
      this.isCkick = true
      if (val == '2') {
        //用户单选
        this.$emit("changeType", 2)
        this.multiple = false
      } else if (val == '3') {
        //用户多选
        this.$emit("changeType", 3)
        this.multiple = true
      } else if (val == '4') {
        //部门多选
        this.$emit("changeType", 4)
        this.multiple = true
      } else if (val == '5') {
        //指定值
        this.$emit("changeType", 5)
        this.multiple = false
      }
    },
    async getUserList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      await getUserAvailUsers(params)
        .then((res) => {
          this.tableDataUser = res.data;
        })
    },
    async getBranchList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      await getUserAvailGroups(params)
        .then((res) => {
          this.tableDataBranch = res.data;
          console.log("获取组织列表", this.tableDataBranch);
        })
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>