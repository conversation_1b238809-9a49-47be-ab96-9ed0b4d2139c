<template>
  <div class="app-container" v-loading="loading">
    <div class="page-box">
      <div class="page-box-left">
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>快捷入口</span
            ><span class="card-title-tips">（12个以内）</span>
          </div>
          <div class="card-content">
            <router-link
              :to="'/' + item.fullPath"
              v-for="item in checkedList"
              :key="item.menuId"
            >
              <el-button>{{ item.menuName }}</el-button>
            </router-link>
          </div>
        </el-card>
      </div>
      <div class="page-box-right">
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>自定义快捷入口</span>
          </div>
          <el-tree
            ref="treeList"
            :data="treeList"
            show-checkbox
            node-key="menuId"
            default-expand-all
            check-on-click-node
            :default-checked-keys="checkedKeys"
            :props="{
              label: 'menuName',
              disabled: (data, node) => {
                return !node.isLeaf;
              },
            }"
            @check="handleCheckChange"
          >
          </el-tree>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAllMenuList,
  getCheckedList,
  saveMenuList,
} from "@/api/home/<USER>";

export default {
  data() {
    return { treeList: [], checkedKeys: [], checkedList: [], loading: false };
  },
  mounted() {
    this.getAllMenuList();
    this.getCheckedList();
    console.log(this.$store.state.permission.routes, "-----routes");
  },
  methods: {
    getAllMenuList() {
      getAllMenuList().then((res) => {
        this.treeList = res.data;
      });
    },
    getCheckedList() {
      getCheckedList().then((res) => {
        this.checkedKeys = res.data?.map((x) => x.menuId);
        this.checkedList = res.data;
      });
    },
    handleCheckChange(checkedNodes, checkedKeys) {
      console.log(checkedNodes, checkedKeys);
      let checkedArr = this.checkedKeys?.map((x) => x);
      if (!checkedArr.some((x) => x == checkedNodes.menuId)) {
        checkedArr.push(checkedNodes.menuId);
      } else {
        checkedArr.splice(checkedArr.indexOf(checkedNodes.menuId), 1);
      }
      this.loading = true;
      saveMenuList({
        menuIds: checkedArr,
      })
        .then((res) => {
          this.loading = false;
          if (res.code === "10000") {
            this.$message.success("设置成功");
            this.getCheckedList();
          }
        })
        .catch(() => {
          this.loading = false;
          this.$refs.treeList.setChecked(checkedNodes.menuId, false);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.page-box {
  display: flex;
  &-left {
    flex: 2;
    margin-right: 20px;
  }
  &-right {
    flex: 1;
    /deep/ .el-card__body {
      height: 80vh;
      overflow: auto;
    }
  }
}
.card-title-tips {
  font-size: 12px;
  font-weight: normal;
  color: #999;
  margin-left: 10px;
}
.card-content {
  display: flex;
  flex-wrap: wrap;
  .el-button--small {
    margin: 20px 10px;
    width: 120px;
    padding: 30px 0;
    font-size: 14px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
</style>
