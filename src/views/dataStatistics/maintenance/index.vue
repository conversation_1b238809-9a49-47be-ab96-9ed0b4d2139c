// 运维数据
<template>
  <div>
    <div class="box-menu">
      <el-radio-group v-model="activeName" style="margin: 20px;">
        <el-radio-button label="order">运维工单数据统计</el-radio-button>
        <!-- <el-radio-button label="task">巡检任务数据统计</el-radio-button> -->
      </el-radio-group>
    </div>
    <div class="form-content">
      <el-form :model="form" ref="form">
        <!-- <el-row style="text-align: right;">
            <el-col :span="8"> -->
        <el-form-item label="数据范围" prop="deptId" label-width="80px">
          <!-- <el-select
              v-model="form.deptId"
              placeholder="请选择数据范围"
              clearable
              size="mini"
              style="width: 100%"
              @change="queryPageData"
            >
              <el-option
                v-for="dict in dataRangeOptions"
                :key="dict.stationId"
                :label="dict.stationName"
                :value="dict.stationId"
            /></el-select> -->
          <treeselect
            v-model="form.deptId"
            :options="deptOptions"
            placeholder="请选择数据范围"
            @select="handleSelect"
            :beforeClearAll="beforeClearAll"
            :default-expand-level="1"
            :normalizer="normalizer"
          />
        </el-form-item>
        <!-- </el-col>
            <el-col :span="16"
              ><el-row> -->
        <el-form-item label="时间" prop="timeRange" label-width="50px">
          <!-- <el-col :span="8"> -->
          <el-date-picker
            v-model="form.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="截止日期"
            value-format="yyyy-MM-dd"
            size="mini"
            @change="queryPageData"
          ></el-date-picker>
          <!-- </el-col>
                  <el-col :span="12"> -->
          <el-radio-group
            v-model="form.timeRange"
            size="mini"
            @change="queryPageData"
          >
            <el-radio-button
              v-for="(x, i) in timeArr"
              :label="x.date"
              :key="i"
              >{{ x.title }}</el-radio-button
            >
          </el-radio-group>
          <!-- </el-col> -->
        </el-form-item>
        <!-- </el-row>
            </el-col>
          </el-row> -->
      </el-form>
    </div>
    <MaintenanceOrder ref="maintenanceOrder"></MaintenanceOrder>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import moment from "moment";
import { listDept } from "@/api/common";
import Treeselect from "@riophae/vue-treeselect";
import MaintenanceOrder from "./components/maintenanceOrder";
export default {
  components: { MaintenanceOrder, Treeselect },
  data() {
    return {
      activeName: "order",
      form: { deptId: undefined, timeRange: [] },
      dataRangeOptions: [],
      deptOptions: [],
    };
  },
  computed: {
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD 00:00:00"),
            moment().format("YYYY-MM-DD 23:59:59"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
  },
  created() {
    this.getTreeselect();
    this.form.timeRange = [
      moment()
        .subtract(6, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
  },
  mounted() {
    this.queryPageData();
  },
  methods: {
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    handleSelect(item) {
      this.form.deptId = item.deptId;
      this.queryPageData();
    },
    beforeClearAll() {
      this.form.deptId = undefined;
      this.queryPageData();
    },
    queryPageData() {
      let params = { ...this.form };
      if (this.form.timeRange?.length > 0) {
        params["startTime"] = moment(this.form.timeRange[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        params["endTime"] = moment(this.form.timeRange[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }
      if (this.activeName === "order") {
        this.$refs.maintenanceOrder.queryData(params);
      }
      console.log("----params----", params);
    },
  },
};
</script>

<style lang="less" scoped>
.box-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-content {
  padding: 0 20px;
  margin-bottom: 18px;
  /deep/ .el-form {
    display: flex;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 10px;
  }
  /deep/ .el-date-editor .el-range-separator {
    width: 7%;
  }
  /deep/ .el-form-item--small.el-form-item {
    margin-bottom: 0;
    // margin-right: 20px;
    // display: flex;
  }
  /deep/ .vue-treeselect__control {
    table-layout: auto;
  }
  /deep/ .el-date-editor--daterange.el-input__inner {
    width: 250px;
  }
}
</style>
