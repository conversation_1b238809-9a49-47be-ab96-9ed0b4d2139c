<template>
  <div>
    <!-- 运维工单数据概览-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading1">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>运维工单数据概览</span>
      </div>
      <div class="statistics-box">
        <div
          v-for="(item, index) in statisticsList"
          :key="index"
          class="statistics-item"
        >
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <!-- 运维工单数据概览-end -->
    <!-- 工单完成趋势-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading2">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单完成趋势</span>
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <p>每日完成：每日完结、结束、待回访的工单数量</p>
            <p>每日新增：当日新增的工单数量</p>
          </div>
          <i class="el-icon-question ml5"></i>
        </el-tooltip>
      </div>
      <LineChart
        :axisData="handleTend.time"
        :serieData="handleTend.tendencyArr"
        lineType="line"
        v-if="handleTend.time && handleTend.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 工单完成趋势-end -->
    <div style="display: flex;">
      <!-- 工单来源-start -->
      <el-card
        style="margin-bottom: 10px;flex:1;margin-right: 10px;"
        v-loading="loading3"
      >
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>工单来源</span>
        </div>
        <PieChartSolid
          :list="originPieList"
          v-if="originPieList && originPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 工单来源-end -->
      <!-- 能投大区工单占比-start -->
      <el-card style="margin-bottom: 10px;flex:1" v-loading="loading4">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>能投大区工单占比</span>
        </div>
        <PieChartSolid
          :list="deptPieList"
          :pieRadius="['47%', '70%']"
          v-if="deptPieList && deptPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 能投大区工单占比-end -->
    </div>
    <!-- 工单类型-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading5">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单类型</span>
      </div>
      <LineChart
        :axisData="orderTypeObj.time"
        :serieData="orderTypeObj.tendencyArr"
        lineType="bar"
        v-if="orderTypeObj.time && orderTypeObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 工单类型-end -->
    <!-- 故障类别-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading6">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>故障类别</span>
      </div>
      <LineChart
        :axisData="faultTypeObj.time"
        :serieData="faultTypeObj.tendencyArr"
        lineType="bar"
        v-if="faultTypeObj.time && faultTypeObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 故障类别-end -->
    <div style="display: flex;">
      <!-- 超时情况-start -->
      <el-card
        style="margin-bottom: 10px;flex:1;margin-right: 10px;"
        v-loading="loading7"
      >
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>处理超时情况</span>
        </div>
        <PieChartSolid
          :list="exceedPieList"
          v-if="exceedPieList && exceedPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 超时情况-end -->
      <!-- 紧急程度-start -->
      <el-card style="margin-bottom: 10px;flex:1" v-loading="loading8">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>紧急程度</span>
        </div>
        <PieChartSolid
          :list="urgencyPieList"
          :pieRadius="['47%', '70%']"
          v-if="urgencyPieList && urgencyPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 紧急程度-end -->
    </div>
    <!-- 站点工单数排名（前十）-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading9">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>站点工单数排名（前十）</span>
      </div>
      <LineChart
        :axisData="NumRateObj.time"
        :serieData="NumRateObj.tendencyArr"
        lineType="bar"
        isStack
        v-if="NumRateObj.time && NumRateObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 站点工单数排名（前十）-end -->
  </div>
</template>

<script>
import LineChart from "@/components/Echarts/LineChart.vue";
import PieChartSolid from "@/components/Echarts/pieChartSolid.vue";
import {
  queryOrderData,
  queryHandleTend,
  queryOrderChannel,
  queryOrgOrder,
  queryOrderType,
  queryFaultType,
  queryTimeout,
  queryUrgencyLevel,
  queryStationOrder,
} from "@/api/dataStatistics/maintenance.js";
export default {
  components: { LineChart, PieChartSolid },
  data() {
    return {
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      loading5: false,
      loading6: false,
      loading7: false,
      loading8: false,
      loading9: false,
      handleTend: { time: [], tendencyArr: [] },
      statisticsList: [
        { title: "累计工单数", unit: "个", value: 0 },
        { title: "待处理", unit: "个", value: 0 },
        { title: "处理中", unit: "个", value: 0 },
        { title: "已处理", unit: "个", value: 0 },
        { title: "已完结", unit: "个", value: 0 },
        { title: "已驳回", unit: "个", value: 0 },
        { title: "工单平均处理时长", unit: "h", value: 0 },
        { title: "工单审核通过率", unit: "%", value: 0 },
        { title: "处理超时工单", unit: "个", value: 0 },
        { title: "转单数", unit: "个", value: 0 },
        { title: "催单数", unit: "个", value: 0 },
      ],
      originPieList: [],
      deptPieList: [],
      orderTypeObj: { time: [], tendencyArr: [] },
      faultTypeObj: { time: [], tendencyArr: [] },
      exceedPieList: [],
      urgencyPieList: [],
      NumRateObj: { time: [], tendencyArr: [] },
    };
  },
  methods: {
    queryData(params) {
      this.loading1 = true;
      //运维工单数据概览
      queryOrderData(params)
        .then((res1) => {
          this.loading1 = false;
          if (res1?.code == "10000") {
            this.statisticsList = [
              {
                title: "累计工单数",
                unit: "个",
                value: res1.data?.totalCount ?? 0,
              },
              { title: "待处理", unit: "个", value: res1.data?.todoCount ?? 0 },
              {
                title: "处理中",
                unit: "个",
                value: res1.data?.doingCount ?? 0,
              },
              { title: "已处理", unit: "个", value: res1.data?.doneCount ?? 0 },
              {
                title: "已完结",
                unit: "个",
                value: res1.data?.finishCount ?? 0,
              },
              { title: "已驳回", unit: "个", value: res1.data?.backCount ?? 0 },
              {
                title: "工单平均处理时长",
                unit: "h",
                value: res1.data?.avgHandleHours ?? 0,
              },
              {
                title: "工单审核通过率",
                unit: "%",
                value: res1.data?.passingPercent ?? 0,
              },
              {
                title: "处理超时工单",
                unit: "个",
                value: res1.data?.timeoutCount ?? 0,
              },
              {
                title: "转单数",
                unit: "个",
                value: res1.data?.transferCount ?? 0,
              },
              { title: "催单数", unit: "个", value: res1.data?.urgeCount ?? 0 },
            ];
          }
        })
        .catch(() => {
          this.loading1 = false;
        });

      //工单完成趋势
      queryHandleTend(params)
        .then((res2) => {
          this.loading2 = false;
          if (res2?.code !== "10000") return;
          this.handleTend = {
            time: res2.data?.map((item) => item.time),
            tendencyArr: [
              {
                name: "每日新增",
                data: res2.data?.map((item) => item.createCount),
              },
              {
                name: "每日完成",
                data: res2.data?.map((item) => item.completeCount),
              },
            ],
          };
        })
        .catch(() => {
          this.loading2 = false;
        });
      //工单来源
      queryOrderChannel(params)
        .then((res3) => {
          this.loading3 = false;
          if (res3?.code !== "10000") return;
          this.originPieList = res3.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
          // [
          //   { value: 36, name: "客服工单" },
          //   { value: 24, name: "手动" },
          //   { value: 24, name: "充电平台" },
          // ];
        })
        .catch(() => {
          this.loading3 = false;
        });
      //能投大区工单占比
      queryOrgOrder(params)
        .then((res4) => {
          this.loading4 = false;
          if (res4?.code !== "10000") return;
          this.deptPieList = res4.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading4 = false;
        });
      //工单类型
      queryOrderType(params)
        .then((res5) => {
          this.loading5 = false;
          if (res5?.code !== "10000") return;
          this.orderTypeObj = {
            time: res5.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "工单个数",
                data: res5.data?.map((x) => x.count),
              },
            ],
          };
        })
        .catch(() => {
          this.loading5 = false;
        });
      //故障类别
      queryFaultType(params)
        .then((res6) => {
          this.loading6 = false;
          if (res6?.code !== "10000") return;
          this.faultTypeObj = {
            time: res6.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "工单个数",
                data: res6.data?.map((x) => x.count),
              },
            ],
          };
        })
        .catch(() => {
          this.loading6 = false;
        });
      //超时情况
      queryTimeout(params)
        .then((res7) => {
          this.loading7 = false;
          if (res7?.code !== "10000") return;
          this.exceedPieList = res7.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading7 = false;
        });
      //紧急程度
      queryUrgencyLevel(params)
        .then((res8) => {
          this.loading8 = false;
          if (res8?.code !== "10000") return;
          this.urgencyPieList = res8.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading8 = false;
        });
      //站点工单数排名
      queryStationOrder(params)
        .then((res9) => {
          this.loading9 = false;
          if (res9?.code !== "10000") return;
          this.NumRateObj = {
            time: res9.data?.map((x) => x.stationName),
            tendencyArr: [
              {
                name: "离线工单",
                data: res9.data?.map((x) => x.offlineCount),
              },
              {
                name: "故障工单",
                data: res9.data?.map((x) => x.faultCount),
              },
              {
                name: "其它",
                data: res9.data?.map((x) => x.otherCount),
              },
            ],
          };
        })
        .catch(() => {
          this.loading9 = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto;
  grid-row-gap: 32px;
  grid-column-gap: 32px;
  .statistics-item {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
/deep/ .el-statistic {
  margin: 20px 0;
  display: flex;
  flex-direction: column-reverse;
  .head {
    color: #469a7d;
    font-size: 14px;
    margin-bottom: 0;
  }
  .con {
    color: #469a7d;
    margin-bottom: 12px;
    .number {
      font-size: 24px;
      font-weight: 500;
    }
  }
}
</style>
