<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            type="primary"-->
          <!--            icon="el-icon-plus"-->
          <!--            @click.stop="handleCreate"-->
          <!--          >-->
          <!--            新增-->
          <!--          </el-button>-->
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['device:list:detail']"
          >
            详情
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <detail
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :row-info="rowInfo"
      @update-list="getList"
    >
    </detail>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import Detail from "@/views/device/components/detail.vue";
import { queryDeviceInfoByPage } from "@/api/device/device";

export default {
  name: "deviceList",
  components: { AdvancedForm, GridTable, Detail },
  data() {
    return {
      config: [],
      columns: [
        {
          field: "deviceCode",
          title: "设备编号",
          treeNode: true,
          width: 180,
        },
        {
          field: "deviceName",
          title: "设备名称",
          width: 180,
          showOverflowTooltip: true,
        },
        {
          field: "stationCode",
          title: "所属站点",
          width: 180,
          showOverflowTooltip: true,
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 180,
          showOverflowTooltip: true,
        },
        {
          field: "remark",
          title: "设备备注",
          width: 180,
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "deviceList",
      detailVisible: false,
      rowInfo: {},
      pileBrandDict: [],
    };
  },
  async created() {
    this.initConfig();
    this.getList();
  },
  mounted() {},
  methods: {
    showDetail(row) {
      this.rowInfo = row;
      this.detailVisible = true;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      queryDeviceInfoByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "deviceCode",
          title: "设备编号",
          type: "input",
          placeholder: "请填写设备编号",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请填写设备名称",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
      ];
    },
  },
};
</script>

<style scoped></style>
