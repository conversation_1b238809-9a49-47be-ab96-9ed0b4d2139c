<template>
  <el-dialog
    title="详情"
    :visible.sync="visible"
    @close="closeDialog"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
  >
    <el-form :model="form" ref="form" label-width="110px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备编码" prop="deviceCode">
            <el-input
              placeholder="请输入设备编码"
              v-model="form.deviceCode"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              placeholder="请输入设备名称"
              v-model="form.deviceName"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备备注" prop="remark">
            <el-input
              placeholder="请输入设备备注"
              v-model="form.remark"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: "detail",
  props: ["visible", "rowInfo"],
  data() {
    return {
      form: this.rowInfo,
      rules: {},
      pileBrandDict: []
    };
  },
  async created() {
    await this.getDicts("cm_pile_brand").then(response => {
      this.pileBrandDict = response?.data;
    });
  },
  methods: {
    closeDialog() {
      this.$emit("update:rowInfo", {});
      this.$emit("update:visible", false);
    },
  }
};
</script>

<style scoped>

</style>