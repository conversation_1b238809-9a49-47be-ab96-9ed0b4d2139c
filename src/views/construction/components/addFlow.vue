<template>
  <el-dialog
    title="添加工单"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeAddOrderVisible"
    append-to-body
    width="50%"
  >
    <div class="queryParamsWrap">
      <el-form :model="addForm" ref="addForm" :inline="true">
        <el-row v-for="(item, index) in addForm.typeList" :key="index">
          <el-col :span="9">
            <el-form-item label="业务类型">
              <el-select
                v-model="item.relaBizType"
                @change="changeRelaBizType(item, index)"
                size="mini"
                placeholder="请选择"
                :disabled="disabledFlag"
              >
                <el-option
                  v-for="item in businessTypeOption"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label="工单类型"
              :prop="'typeList.' + index + '.orderType'"
              :rules="{
                required: true,
                message: '工单类型不能为空',
                trigger: 'blur',
              }"
            >
              <el-select
                v-model="item.orderType"
                size="mini"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in item.useFlowList"
                  :key="item.flowType"
                  :label="item.flowTypeName"
                  :value="item.flowType"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button
              size="mini"
              v-if="addForm.typeList.length > 1"
              @click="delItem(index)"
              >删除</el-button
            >
            <el-button type="primary" size="mini" @click="addItem"
              >添加</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeAddOrderVisible">取 消</el-button>
      <el-button type="primary" @click.stop="submitForm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addProjectConstructionFlow } from "@/api/projectManage";
import { useFlowList as useFlowListApi } from "@/api/orderScheduling/workStation.js";
import { CONSTRUCTION_CLICK_BATCH_CREATE_ORDER } from "@/utils/track/track-event-constants";

export default {
  name: "addFlow",
  props: ["visible", "projectId", "projectName", "addAll"],
  data() {
    return {
      addForm: {
        typeList: [
          // {
          //   relaBizType: "construction",
          //   orderType: "",
          // },
        ],
      },
      businessTypeOption: [],
      constructingBusinessType: ["construction"],
      disabledFlag: false,
      useFlowList: [], //工单类型
    };
  },
  async created() {},
  async mounted() {
    this.disabledFlag = true;
    //获取业务类型字典
    await this.getDicts("flow_business_type").then((response) => {
      if (response?.data && response?.data.length > 0) {
        response.data.forEach((item) => {
          if (this.constructingBusinessType.indexOf(item.dictValue) !== -1) {
            this.businessTypeOption.push(item);
          }
        });
      }
    });
    let list = await this.getUseFlowList("construction");
    if (this.addAll) {
      list.map((item, index) => {
        this.addForm.typeList.push({
          relaBizType: "construction",
          orderType: item.flowType,
        });
        this.$set(this.addForm.typeList[index], "useFlowList", list);
      });
    } else {
      this.addForm.typeList = [
        {
          relaBizType: "construction",
          orderType: "",
        },
      ];
      this.$set(this.addForm.typeList[0], "useFlowList", list);
    }
  },
  methods: {
    //获取工单类型列表
    async getUseFlowList(businessType) {
      let params = {
        businessTypeList: businessType,
      };
      const { code, data } = await useFlowListApi(params);
      if (code !== "10000") return;
      return data;
    },
    closeAddOrderVisible() {
      this.$emit("update:visible", false);
      this.$emit("update-list");
    },
    //切换业务类型，查询工单类型
    async changeRelaBizType(item, index) {
      this.addForm.typeList[index].relaBizType = item.relaBizType;
      let list = await this.getUseFlowList(item.relaBizType);
      this.$set(this.addForm.typeList[index], "useFlowList", list);
    },
    delItem(index) {
      this.addForm.typeList.splice(index, 1);
    },
    async addItem() {
      let length = this.addForm.typeList.length;
      this.addForm.typeList.push({
        relaBizType: "construction",
        orderType: "",
      });
      let list = await this.getUseFlowList("construction");
      this.$set(this.addForm.typeList[length], "useFlowList", list);
    },
    async submitForm() {
      let businessFlowList = [];
      this.addForm.typeList.forEach((item) => {
        if (item.orderType) {
          let obj = {
            relaBizType: item.relaBizType, //业务类型
            flowType: item.orderType, //流程类型
            flowTypeName: item.useFlowList.find(
              (el) => el.flowType === item.orderType
            ).flowTypeName, //流程类型名称
            flowKey: item.useFlowList.find(
              (el) => el.flowType === item.orderType
            ).flowKey, //流程必传
            flowName: item.useFlowList.find(
              (el) => el.flowType === item.orderType
            ).flowName, //流程名称必传
          };
          businessFlowList.push(obj);
        }
      });
      let params = {
        projectId: this.projectId,
        businessFlowList: businessFlowList,
      };

      const loading = this.$loading({
        lock: true,
        text: "提交中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      await addProjectConstructionFlow(params)
        .then((res) => {
          loading.close();
          if (res?.success) {
            this.closeAddOrderVisible();
            this.$message.success(res.message);

            this.reportTrackEvent(CONSTRUCTION_CLICK_BATCH_CREATE_ORDER, {
              projectCode: this.projectId,
              projectName: this.projectName
            });
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          loading.close();
        });
    },
  },
};
</script>

<style scoped></style>
