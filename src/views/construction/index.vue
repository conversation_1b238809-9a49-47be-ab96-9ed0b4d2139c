<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}"> </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showAddFlow(row, false)"
            :disabled="row.status === '11'"
            v-has-permi="['construction:add']"
          >
            添加施工工单
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="showAddFlow(row, true)"
            :disabled="row.status === '11'"
            v-has-permi="['construction:batchAdd']"
          >
            批量生成施工单
          </el-button>
        </template>
        <template slot="projectCode" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="showProjectInfo(row)">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button
            type="text"
            size="large"
            @click="showConstructingFlowInfo(row)"
          >
            {{ row.flowCount }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <!--  查看项目详情  -->
    <ProjectInfo
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :page-type="pageType"
      :project-id="projectId"
      :title="dialogTitle"
    />
    <!--  新增施工工单  -->
    <AddFlow
      v-if="addOrderVisible"
      :visible.sync="addOrderVisible"
      :project-id="projectId"
      :project-name="projectName"
      @update-list="getList"
      :addAll="addAll"
    />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import ProjectInfo from "@/views/projectManage/addProject.vue";
import AddFlow from "@/views/construction/components/addFlow.vue";
import { queryConstructingProjectInfoByPage } from "@/api/projectManage";
import { regionData } from "element-china-area-data";

export default {
  name: "constructing",
  components: { AdvancedForm, GridTable, ProjectInfo, AddFlow },
  data() {
    return {
      config: [],
      columns: [
        {
          field: "projectCode",
          title: "项目编码",
          slots: { default: "projectCode" },
        },
        {
          field: "projectName",
          title: "项目名称",
          customWidth: 200,
        },
        {
          field: "remark",
          title: "项目备注",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 200,
        },
        {
          field: "createTime",
          title: "项目创建时间",
        },
        {
          field: "flowCount",
          title: "施工工单",
          slots: { default: "deviceTypeImg" },
        },
        {
          field: "finishCount",
          title: "已完成数量",
        },
        {
          field: "notFinishCount",
          title: "未完成数量",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        statusList: ["1", "2", "3", "4", "5", "6", "7", "8"],
        projectName: "",
        projectCode: "",
        stationCode: "",
        stationName: "",
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "constructing",
      projectId: undefined,
      projectName: "",
      detailVisible: false,
      dialogTitle: "详情",
      pageType: "detail",
      addOrderVisible: false,
      pileBrandDict: [],
      addAll: false,
    };
  },
  async created() {
    this.initConfig();
    this.getList();
  },
  mounted() {},
  methods: {
    showAddFlow(row, addAll) {
      this.projectId = row.projectId;
      this.projectName = row.projectName;
      this.addOrderVisible = true;
      this.addAll = addAll;
    },
    showProjectInfo(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.detailVisible = true;
    },
    showConstructingFlowInfo(row) {
      this.$router.push({
        path: "/workOrderWorkBench/workOrderWorkbench",
        query: { projectId: row.projectId, relaBizType: "construction" },
      });
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      queryConstructingProjectInfoByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请填写项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请填写项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请填写站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
  },
};
</script>

<style scoped></style>
