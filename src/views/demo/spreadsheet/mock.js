// 模拟服务器端的Excel数据
export const mockExcelData = [
  {
    name: "Sheet1",
    color: "",
    status: 1,
    order: 0,
    data: [
      [
        { v: "产品", ct: { fa: "General", t: "g" } },
        { v: "数量", ct: { fa: "General", t: "g" } },
        { v: "单价", ct: { fa: "General", t: "g" } },
        { v: "总价", ct: { fa: "General", t: "g" } },
        { v: "备注", ct: { fa: "General", t: "g" } },
      ],
      [
        { v: "产品A", ct: { fa: "General", t: "g" } },
        { v: 10, ct: { fa: "General", t: "n" } },
        { v: 100, ct: { fa: "General", t: "n" } },
        { v: 1000, f: "B2*C2", ct: { fa: "General", t: "n" } },
        { v: "", ct: { fa: "General", t: "g" } },
      ],
      [
        { v: "产品B", ct: { fa: "General", t: "g" } },
        { v: 5, ct: { fa: "General", t: "n" } },
        { v: 200, ct: { fa: "General", t: "n" } },
        { v: 1000, f: "B3*C3", ct: { fa: "General", t: "n" } },
        { v: "", ct: { fa: "General", t: "g" } },
      ],
      [
        { v: "产品C", ct: { fa: "General", t: "g" } },
        { v: 8, ct: { fa: "General", t: "n" } },
        { v: 150, ct: { fa: "General", t: "n" } },
        { v: 1200, f: "B4*C4", ct: { fa: "General", t: "n" } },
        { v: "", ct: { fa: "General", t: "g" } },
      ],
      [
        { v: "总计", ct: { fa: "General", t: "g" } },
        { v: 23, f: "SUM(B2:B4)", ct: { fa: "General", t: "n" } },
        { v: "", ct: { fa: "General", t: "g" } },
        { v: 3200, f: "SUM(D2:D4)", ct: { fa: "General", t: "n" } },
        { v: "", ct: { fa: "General", t: "g" } },
      ],
    ],
    config: {
      merge: {},
      rowlen: {
        0: 36,
        1: 36,
        2: 36,
        3: 36,
        4: 36,
      },
      columnlen: {
        0: 150,
        1: 100,
        2: 100,
        3: 100,
        4: 150,
      },
    },
    index: 0,
  },
];

// 模拟加载Excel数据的API
export const loadExcelData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockExcelData);
    }, 500);
  });
};

// 模拟保存Excel数据的API
export const saveExcelData = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log("保存的Excel数据:", data);
      resolve({ success: true, message: "导出成功，已下载为JSON文件" });
    }, 500);
  });
};

// jspreadsheet示例数据
export const jspreadsheetData = [
  ["产品", "数量", "单价", "总价", "备注"],
  ["产品A", "10", "100", "=B2*C2", ""],
  ["产品B", "5", "200", "=B3*C3", ""],
  ["产品C", "8", "150", "=B4*C4", ""],
  ["总计", "=SUM(B2:B4)", "", "=SUM(D2:D4)", ""],
];

// 模拟加载jspreadsheet数据的API
export const loadJspreadsheetData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(jspreadsheetData);
    }, 500);
  });
};

// 模拟保存jspreadsheet数据的API
export const saveJspreadsheetData = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log("保存的jspreadsheet数据:", data);
      resolve({ success: true, message: "保存成功" });
    }, 500);
  });
};
