<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="LuckySheet" name="luckysheet">
        <div class="tab-content">
          <div class="control-panel">
            <el-button type="primary" @click="importExcel">导入Excel</el-button>
            <el-button
              type="success"
              @click="exportExcel"
              :disabled="!luckysheetLoaded"
              >导出Excel</el-button
            >
            <input
              ref="fileInput"
              type="file"
              accept=".xlsx"
              style="display: none"
              @change="handleFileChange"
            />
          </div>
          <div id="luckysheet" class="spreadsheet-container"></div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="jspreadsheet-ce" name="jspreadsheet">
        <div class="tab-content">
          <div class="control-panel">
            <el-button type="primary" @click="loadJspreadsheetData"
              >加载数据</el-button
            >
            <el-button type="success" @click="saveJspreadsheetData"
              >保存数据</el-button
            >
          </div>
          <div id="jspreadsheet" class="spreadsheet-container"></div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="比较" name="comparison">
        <div class="comparison-container">
          <h3>在线表格功能比较</h3>
          <el-table :data="comparisonData" border style="width: 100%">
            <el-table-column
              prop="feature"
              label="功能"
              width="180"
            ></el-table-column>
            <el-table-column
              prop="luckysheet"
              label="LuckySheet"
            ></el-table-column>
            <el-table-column
              prop="jspreadsheet"
              label="jspreadsheet-ce"
            ></el-table-column>
            <el-table-column prop="spreadjs" label="SpreadJS"></el-table-column>
          </el-table>

          <h3 class="mt-20">评估结论</h3>
          <el-card class="box-card">
            <div class="conclusion">
              <p>
                <strong>LuckySheet:</strong>
                功能丰富的开源电子表格组件，支持Excel导入导出，公式计算，数据透视表等功能。适合需要完整Excel功能的场景。
              </p>
              <p>
                <strong>jspreadsheet-ce:</strong>
                轻量级的JavaScript表格组件，适合简单的数据编辑场景，不需要复杂Excel功能的情况。
              </p>
              <p>
                <strong>SpreadJS:</strong>
                商业级电子表格组件，功能最完整，性能最好，但需要付费授权。适合企业级应用。
              </p>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "SpreadsheetDemo",
  data() {
    return {
      activeTab: "luckysheet",
      luckysheetLoaded: false,
      jspreadsheetInstance: null,
      comparisonData: [
        {
          feature: "Excel导入导出",
          luckysheet: "✅ 支持",
          jspreadsheet: "⚠️ 有限支持",
          spreadjs: "✅ 完全支持",
        },
        {
          feature: "公式计算",
          luckysheet: "✅ 支持大部分公式",
          jspreadsheet: "⚠️ 基础公式支持",
          spreadjs: "✅ 支持450+公式",
        },
        {
          feature: "数据透视表",
          luckysheet: "✅ 支持",
          jspreadsheet: "❌ 不支持",
          spreadjs: "✅ 支持",
        },
        {
          feature: "图表",
          luckysheet: "✅ 支持",
          jspreadsheet: "❌ 不支持",
          spreadjs: "✅ 支持",
        },
        {
          feature: "性能(大数据)",
          luckysheet: "⚠️ 中等",
          jspreadsheet: "⚠️ 适合小数据量",
          spreadjs: "✅ 优秀",
        },
        {
          feature: "文件大小",
          luckysheet: "⚠️ 较大",
          jspreadsheet: "✅ 轻量",
          spreadjs: "⚠️ 较大",
        },
        {
          feature: "开源/商业",
          luckysheet: "✅ 开源",
          jspreadsheet: "✅ 开源",
          spreadjs: "❌ 商业",
        },
      ],
    };
  },
  mounted() {
    this.loadScripts();
  },
  methods: {
    // Helper method to convert column index to column name (A, B, C, etc.)
    getColumnName(index) {
      let columnName = "";
      while (index >= 0) {
        columnName = String.fromCharCode(65 + (index % 26)) + columnName;
        index = Math.floor(index / 26) - 1;
      }
      return columnName;
    },
    loadScripts() {
      // 根据当前激活的标签页加载相应的脚本
      if (this.activeTab === "luckysheet") {
        this.loadLuckysheetScripts();
      } else if (this.activeTab === "jspreadsheet") {
        this.loadJspreadsheetScripts();
      }
    },
    loadLuckysheetScripts() {
      // 动态加载LuckySheet脚本和样式
      const linkCss1 = document.createElement("link");
      linkCss1.rel = "stylesheet";
      linkCss1.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/css/pluginsCss.css";
      document.head.appendChild(linkCss1);

      const linkCss2 = document.createElement("link");
      linkCss2.rel = "stylesheet";
      linkCss2.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/plugins.css";
      document.head.appendChild(linkCss2);

      const linkCss3 = document.createElement("link");
      linkCss3.rel = "stylesheet";
      linkCss3.href =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/css/luckysheet.css";
      document.head.appendChild(linkCss3);

      const script1 = document.createElement("script");
      script1.src =
        "https://cdn.jsdelivr.net/npm/luckysheet/dist/plugins/js/plugin.js";
      script1.onload = () => {
        const script2 = document.createElement("script");
        script2.src =
          "https://cdn.jsdelivr.net/npm/luckysheet/dist/luckysheet.umd.js";
        script2.onload = () => {
          this.initLuckysheet();
        };
        document.head.appendChild(script2);
      };
      document.head.appendChild(script1);
    },
    loadJspreadsheetScripts() {
      // 动态加载jspreadsheet-ce脚本和样式
      const linkCss = document.createElement("link");
      linkCss.rel = "stylesheet";
      linkCss.href =
        "https://cdn.jsdelivr.net/npm/jspreadsheet-ce/dist/jspreadsheet.min.css";
      document.head.appendChild(linkCss);

      const script = document.createElement("script");
      script.src =
        "https://cdn.jsdelivr.net/npm/jspreadsheet-ce/dist/index.min.js";
      script.onload = () => {
        this.initJspreadsheet();
      };
      document.head.appendChild(script);
    },
    initLuckysheet() {
      // 初始化LuckySheet
      if (window.luckysheet) {
        try {
          const defaultData = [
            {
              name: "Sheet1",
              color: "",
              status: 1,
              order: 0,
              data: Array(50)
                .fill(0)
                .map(() => Array(26).fill("")),
              config: {},
              index: 0,
            },
          ];

          window.luckysheet.create({
            container: "luckysheet",
            lang: "zh",
            data: defaultData,
            showinfobar: false,
          });
          this.luckysheetLoaded = true;
        } catch (error) {
          console.error("Error initializing LuckySheet:", error);
          this.$message.error("初始化表格组件失败");
        }
      }
    },
    initJspreadsheet() {
      // 初始化jspreadsheet-ce
      if (window.jspreadsheet && !this.jspreadsheetInstance) {
        try {
          // Create a simple initial data structure
          const initialData = [
            ["", "", "", "", ""],
            ["", "", "", "", ""],
            ["", "", "", "", ""],
            ["", "", "", "", ""],
            ["", "", "", "", ""],
          ];

          // Initialize jspreadsheet
          this.jspreadsheetInstance = window.jspreadsheet(
            document.getElementById("jspreadsheet"),
            {
              data: initialData,
              columns: [
                { type: "text", title: "A", width: 120 },
                { type: "text", title: "B", width: 120 },
                { type: "text", title: "C", width: 120 },
                { type: "text", title: "D", width: 120 },
                { type: "text", title: "E", width: 120 },
              ],
            }
          );
        } catch (error) {
          console.error("Error initializing jspreadsheet:", error);
          this.$message.error("初始化表格组件失败");
        }
      }
    },
    importExcel() {
      // 可以选择从文件导入或从API加载示例数据
      this.$confirm("请选择导入方式", "导入Excel", {
        confirmButtonText: "从文件导入",
        cancelButtonText: "加载示例数据",
        type: "info",
      })
        .then(() => {
          // 从文件导入
          this.$refs.fileInput.click();
        })
        .catch(() => {
          // 加载示例数据
          import("./mock").then(({ loadExcelData }) => {
            loadExcelData().then((data) => {
              if (window.luckysheet) {
                window.luckysheet.destroy();
                window.luckysheet.create({
                  container: "luckysheet",
                  lang: "zh",
                  data: data,
                  showinfobar: false,
                });
                this.$message.success("示例数据加载成功");
                this.luckysheetLoaded = true;
              }
            });
          });
        });
    },
    handleFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 检查文件类型
      if (
        file.type !==
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ) {
        this.$message.error("请选择.xlsx格式的Excel文件");
        return;
      }

      // 加载LuckyExcel库
      const script = document.createElement("script");
      script.src =
        "https://cdn.jsdelivr.net/npm/luckyexcel/dist/luckyexcel.umd.js";
      script.onload = () => {
        // 读取文件
        const reader = new FileReader();
        reader.onload = (event) => {
          const data = event.target.result;
          window.LuckyExcel.transformExcelToLucky(data, (exportJson) => {
            // 加载到LuckySheet
            window.luckysheet.destroy();
            window.luckysheet.create({
              container: "luckysheet",
              lang: "zh",
              data: exportJson.sheets,
              title: file.name,
            });
            this.$message.success("Excel文件导入成功");
          });
        };
        reader.readAsArrayBuffer(file);
      };
      document.head.appendChild(script);

      // 重置文件输入
      e.target.value = "";
    },
    exportExcel() {
      // 导出Excel
      if (!window.luckysheet) {
        this.$message.error("LuckySheet未初始化");
        return;
      }

      // 直接调用导出方法
      this.doExportExcel();
    },
    doExportExcel() {
      // 获取LuckySheet数据
      const allSheets = window.luckysheet.getAllSheets();

      // 使用mock API保存数据
      import("./mock").then(({ saveExcelData }) => {
        saveExcelData(allSheets).then((response) => {
          if (response.success) {
            this.$message.success(response.message);

            // 创建一个简单的下载链接
            const jsonString = JSON.stringify(allSheets, null, 2);
            const blob = new Blob([jsonString], { type: "application/json" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = "luckysheet_export.json";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          } else {
            this.$message.error("导出失败");
          }
        });
      });
    },
    loadJspreadsheetData() {
      // 加载示例数据到jspreadsheet
      if (this.jspreadsheetInstance) {
        try {
          // 从mock API加载数据
          import("./mock").then(({ loadJspreadsheetData }) => {
            loadJspreadsheetData().then((data) => {
              try {
                // 简单方法：直接重新初始化表格
                // 先获取当前表格的DOM元素
                const container = document.getElementById("jspreadsheet");

                // 清空容器
                container.innerHTML = "";

                // 重新初始化jspreadsheet
                this.jspreadsheetInstance = window.jspreadsheet(container, {
                  data: data,
                  columns: [
                    { type: "text", title: "A", width: 120 },
                    { type: "text", title: "B", width: 120 },
                    { type: "text", title: "C", width: 120 },
                    { type: "text", title: "D", width: 120 },
                    { type: "text", title: "E", width: 120 },
                  ],
                });

                this.$message.success("示例数据加载成功");
              } catch (error) {
                console.error("Error loading data into jspreadsheet:", error);
                this.$message.error("加载数据失败");
              }
            });
          });
        } catch (error) {
          console.error("Error in loadJspreadsheetData:", error);
          this.$message.error("加载数据失败");
        }
      }
    },
    saveJspreadsheetData() {
      // 保存jspreadsheet数据
      if (this.jspreadsheetInstance) {
        const data = this.jspreadsheetInstance.getData();
        // 使用mock API保存数据
        import("./mock").then(({ saveJspreadsheetData }) => {
          saveJspreadsheetData(data).then((response) => {
            if (response.success) {
              this.$message.success(response.message);
            } else {
              this.$message.error("保存失败");
            }
          });
        });
      }
    },
  },
  watch: {
    activeTab(newVal) {
      // 当标签页切换时，加载相应的脚本
      if (newVal === "luckysheet" && !this.luckysheetLoaded) {
        this.loadLuckysheetScripts();
      } else if (newVal === "jspreadsheet" && !this.jspreadsheetInstance) {
        this.loadJspreadsheetScripts();
      }
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.tab-content {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 500px;
}
.control-panel {
  margin-bottom: 10px;
}
.spreadsheet-container {
  width: 100%;
  height: calc(100% - 50px);
  min-height: 450px;
  border: 1px solid #dcdfe6;
}
.comparison-container {
  padding: 20px 0;
}
.mt-20 {
  margin-top: 20px;
}
.conclusion {
  line-height: 1.8;
}
</style>
