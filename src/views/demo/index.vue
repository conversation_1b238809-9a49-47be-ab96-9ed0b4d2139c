<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
      <!-- 设备类型 -->
      <template slot="simpleDeviceTypeNo">
        <el-form-item label="" prop="deviceTypeName">
          <!-- <treeselect
            placeholder="设备类型"
            v-model="searchForm.deviceTypeNo"
            :options="deviceTypeList"
            :normalizer="normalizer"
            :multiple="false"
          /> -->
          <el-select v-model="searchForm.deviceTypeNo" filterable clearable size="mini" placeholder="设备类型">
            <el-option-group
              v-for="group in deviceTypeList"
              :key="group.deviceTypeNo"
              :label="group.deviceTypeName">
              <el-option
                v-for="item in group.childrenList"
                :key="item.deviceTypeNo"
                :label="item.deviceTypeName"
                :value="item.deviceTypeNo">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
      </template>
      <template slot="deviceTypeNo">
        <el-form-item label="" prop="deviceTypeName">
          <!-- <treeselect
            placeholder="请选择设备类型"
            v-model="searchForm.deviceTypeNo"
            :options="deviceTypeList"
            :normalizer="normalizer"
            :multiple="false"
          /> -->
          <el-select v-model="searchForm.deviceTypeNo" filterable clearable size="mini" placeholder="设备类型">
            <el-option-group
              v-for="group in deviceTypeList"
              :key="group.deviceTypeNo"
              :label="group.deviceTypeName">
              <el-option
                v-for="item in group.childrenList"
                :key="item.deviceTypeNo"
                :label="item.deviceTypeName"
                :value="item.deviceTypeNo">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
      </template>
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="deviceTable"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="deviceTableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleCreate"
            v-hasPermi="['deviceManage:deviceArchives:add']"
          >
            新增
          </el-button>

          <el-button
            size="mini"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-hasPermi="['deviceManage:deviceArchives:export']"
          >
            导出
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            v-hasPermi="['salary:config:edit']"
            type="text"
            size="large"
            @click="showDeviceDetail(row)"
          >
            视图
          </el-button>
          <el-button
            v-hasPermi="['deviceManage:deviceArchives:edit']"
            type="text"
            size="large"
            @click="editDevice(row)"
          >
            编辑
          </el-button>
          <el-button
            v-hasPermi="['deviceManage:deviceArchives:delete']"
            @click="deleteDevice(row)"
            type="text"
            size="large"
          >
            删除
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import { getDeviceList } from "./mock";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  name: "index",
  components: {
    Treeselect,
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        deviceNo: "",
        deviceName: "",
        realDeviceName: "",
        deviceTypeNo: null,
        deviceModelId: "",
        pumpHouseId: "",
        deviceStatus: "",
        pageNum: 1,
        pageSize: 10,
      },
      finallySearch: null,
      deviceTable: [],
      deviceTableTotal: 0,
      addDeviceArchivesDialogVisible: false, //显示增加设备档案弹框
      deviceViewDialogVisible: false, //显示设备试图弹框
      deviceNo: "", //设备编号
      deviceTypeList: [], //设备类型列表
      deviceModelList: [], //设备型号列表
      statusDict: [], //泵房状态字典
      pumpHouseList: [], //泵房列表
      isAddDevice: true, //是否为新增设备,设备新增和修改共用一个页面
      config: [],
      columns: [
        {
          field: "deviceNo",
          title: "设备编号",
        },
        {
          field: "realDeviceName",
          title: "设备名称",
        },
        {
          field: "deviceName",
          title: "设备属性",
        },
        {
          field: "deviceTypeName",
          title: "设备类型",
        },
        {
          field: "modelNo",
          title: "型号",
        },
        {
          field: "pumpHouseName",
          title: "所属泵房",
        },
        {
          field: "deviceStatusValue", //todo
          title: "设备状态",
        },
        {
          field: "installDate",
          title: "投入日期",
        },
        {
          field: "serviceLife",
          title: "使用年限",
        },
        {
          field: "replaceTimes",
          title: "更换次数",
        },
        {
          field: "remark",
          title: "备注",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "deviceArchivesTable", //tableId必须项目唯一，用于缓存展示列
    };
  },
  mounted() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getDeviceStatusDicts()
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        })
      }, 500);
    })
    //获取设备列表
    this.getDeviceList();
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "deviceNo",
          title: "设备编号",
          type: "input",
          placeholder: "请输入设备编号",
        },
        {
          key: "realDeviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请输入设备名称",
        },
        {
          key: "deviceTypeNo",
          title: "设备类型",
          type: "slot",
          slotName: "simpleDeviceTypeNo",
          placeholder: "请选择设备类型",
        },
        {
          key: "deviceModelId",
          title: "设备型号",
          type: "select",
          options: this.deviceModelList,
          optionLabel: "modelNo",
          optionValue: "deviceModelId",
          placeholder: "请选择设备型号",
        },
        {
          key: "pumpHouseId",
          title: "所属泵房",
          type: "select",
          options: this.pumpHouseList,
          optionLabel: "pumpHouseName",
          optionValue: "pumpHouseId",
          placeholder: "请选择所属泵房",
        },
        {
          key: "deviceStatus",
          title: "设备状态",
          type: "select",
          options: this.statusDict,
          placeholder: "请选择设备状态",
        },
        {
          key: "mydate",
          title: "日期",
          type: "date",
          placeholder: "请选择日期",
        },
        {
          key: "mydateRange",
          title: "起始日期",
          type: "dateRange",
          placeholder: "请选择日期",
        },
        {
          key: "mydatetimeRange",
          title: "起始日期时间",
          type: "datetimeRange",
          placeholder: "请选择日期",
        },
      ];
    },
    getDeviceStatusDicts() {
      //获取设备状态字典
      return this.getDicts("sw_device_status").then((response) => {
        this.statusDict = response?.data;
      });
    },
    submitForm() {
      this.$refs.deviceDialog.submitForm();
    },
    closeDialog() {
      console.log("closeDialog", this.$refs);
      this.$refs.deviceDialog.resetForm();
      this.addDeviceArchivesDialogVisible = false;
    },
    //table内容格式化
    statusFormat(val) {
      return this.selectDictLabel(this.statusDict, val.deviceStatus);
    },
    //获取设备类型
    getDeviceTypeList() {
      return getDeviceTypeList({}).then((res) => {
        //构建设备类型树
        this.deviceTypeList = res?.data;
      });
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deviceTypeNo,
        label: node.deviceTypeName,
        children: node.childrenList,
      };
    },
    //获取设备型号
    getDeviceModelList() {
      return getDeviceModelList({ modelStatus: "0" }).then((res) => {
        this.deviceModelList = res?.data;
      });
    },
    //获取泵房列表
    getPumpHouseList(pumpHouseName) {
      let data = { pumpHouseName };
      pumpHouseByPage(data).then((res) => {
        this.pumpHouseList = res?.data;
      });
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1
      params.pageSize = this.searchForm.pageSize
      this.getDeviceList(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getDeviceList();
    },
    //新建
    handleCreate() {
      this.addDeviceArchivesDialogVisible = true;
      this.isAddDevice = true;
    },
    //导出
    handleExport() {
      console.log("000", this.searchForm)
      this.$confirm("是否确认导出所有数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportData(this.searchForm)
          .then((res) => {
            if (res?.success) {
              this.$message.success(
                "导出任务已提交，请稍后至下载中心查看或下载"
              );
            } else {
              this.$message.error("导出失败");
            }
          })
          .catch(() => {
            this.$message.error("导出失败");
          });
      });
    },
    //获取设备列表
    getDeviceList(params) {
      const args = this._.cloneDeep(params ? params : this.searchForm);
      args.deviceTypeNo = this.searchForm.deviceTypeNo; //设备类型
      this.loading = true;
      this.finallySearch = args
      getDeviceList(args)
        .then((res) => {
          this.loading = false;
          this.deviceTable = res?.data;
          this.deviceTableTotal = res?.total;
          this.deviceTable.forEach((element) => {
            this.$set(element, "deviceStatusValue", this.statusFormat(element));
          });
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //显示设备详情
    showDeviceDetail(row) {
      this.deviceViewDialogVisible = true;
      this.deviceNo = row.deviceNo;
    },
    //设备信息修改
    editDevice(row) {
      findBindRelation({ deviceId: row.deviceId }).then(res => {
        console.info("绑定关系", res);
        if (res.data) {
          return this.msgError("请先解绑该设备绑定的设备组！！！");
        } else {
          this.addDeviceArchivesDialogVisible = true;
          this.isAddDevice = false;
          this.deviceNo = row.deviceNo;
        }
      });
    },
    //删除设备
    deleteDevice(row) {
      this.$confirm("确定删除该设备组吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let data = {
          deviceId: row.deviceId
        };
        deleteDevice(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");

            //更新列表
            this.getDeviceList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    //分页切换
    changePage() {
      if(this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum
        this.finallySearch.pageSize = this.searchForm.pageSize
      }
      this.getDeviceList(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/.el-select-group__title {
    padding-left: 10px;
  }
  /deep/.el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }
  /deep/.el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
</style>
