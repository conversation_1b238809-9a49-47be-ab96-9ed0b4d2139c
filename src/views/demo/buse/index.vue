<template>
  <BuseCrud
    ref="crud"
    title=""
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :tableProps="tableProps"
    :modalConfig="modalConfig"
    @modalCancel="modalCancelHandler"
    @modalSubmit="modalSubmit"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @loadData="loadData"
    @handleCreate="rowAdd"
    @handleReset="handleReset"
  >
    <!-- :tabRadioList="tabRadioList"
    @tabRadioChange="tabRadioChange"
    :vxeToolBar="vxeToolBar" -->
    <template slot="regionCascader">
      <RegionCascader
        v-model="params.region"
        :props="{ checkStrictly: true }"
      ></RegionCascader>
    </template>
    <template slot="graduateYearForPop" slot-scope="{ params }">
      <BuseRangePicker
        type="year"
        v-model="params.graduateYear"
        :needShowSecondPicker="() => false"
        format="YYYY"
        :disableDateFunc="disableDateFunc"
      />
    </template>
    <template slot="completeInfo">
      <h3>自我介绍</h3>
      <el-input
        type="textarea"
        v-model="personInfo"
        placeholder="输入自我介绍"
      />
    </template>
    <template slot="workEx" slot-scope="{ row, operationType }">
      <h3>工作经验</h3>
      <p>当前是{{ row.name }}的工作简历，当前的操作类型是{{ operationType }}</p>
      <!-- <JobTable :company="row.company" class="job-table" /> -->
    </template>
    <template slot="abroadApplication">
      <DynamicForm
        ref="abroadApplication"
        :config="abroadApplicationFormConfig"
        :params="abroadApplicationParams"
      ></DynamicForm>
    </template>
  </BuseCrud>
</template>

<script>
import RegionCascader from "./RegionCascader.vue";
import moment from "moment";
import api from "@/api/demo/index.js";
import { hometownMock, hobbiesMock, jobMock } from "./mock";
import { getTableData, getSexOptions } from "./api";
// import JobTable from "./job.vue";
import { initParams } from "@/utils/buse.js";

export default {
  name: "DumiDocVueIndex",
  components: {
    // JobTable,
    RegionCascader,
  },
  data() {
    return {
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
      },
      vxeToolBar: {},
      tabRadioList: [
        { value: "0", id: "0", label: "tab1", number: 1 },
        { value: "1", id: "1", label: "tab2" },
        { value: "2", id: "2", label: "tab3" },
      ],
      abroadApplicationParams: {
        name: undefined,
        income: undefined,
        code: undefined,
      },
      personInfo: undefined,
      completeInfoList: [],
      signInList: [],
      tableData: [],
      isFold: false,

      params: {
        // name: undefined,
        // sex: undefined,
        // age: undefined,
        // birthDate: undefined,
        // graduateYear: undefined,
        // entrance: undefined,
        // WorkExperience: undefined,
        // hometown: undefined,
        // hobbies: undefined,
        // job: undefined,
        // region: undefined,
      },
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      sexOptions: null,
      abroadApplicationFormConfig: [
        {
          field: "name",
          title: "姓名",
          rules: [{ required: true, message: "请填写姓名" }],
        },
        {
          field: "code",
          title: "身份证号",
          rules: [{ required: true, message: "请输入身份证号码" }],
        },
        {
          field: "income",
          title: "年收入",
          props: {
            prefix: "￥",
            suffix: "RMB",
          },
        },
      ],
    };
  },
  computed: {
    tableColumn() {
      return [
        // {
        //   type: "checkbox",
        // },
        {
          field: "id",
          title: "id",
        },
        {
          field: "name",
          title: "名字",
        },
        {
          field: "status",
          title: "状态",
          formatter: ({ cellValue }) => {
            return cellValue == "1" ? "启用" : "停用";
          },
        },
        {
          field: "createBy",
          title: "创建人",
        },
        // {
        //   field: "name9",
        //   title: "姓名",
        //   width: 80,
        //   children: this.isFold
        //     ? []
        //     : [
        //         { field: "name1", title: "刘亦菲", width: 100 },
        //         { field: "name2", title: "王星越", width: 100 },
        //         { field: "name3", title: "赵丽颖", width: 100 },
        //         { field: "name4", visible: false, width: 100 },
        //       ],
        //   slots: {
        //     header: () => {
        //       return (
        //         <div>
        //           <span>姓名</span>
        //           <i
        //             class={
        //               this.isFold ? "el-icon-caret-right" : "el-icon-caret-left"
        //             }
        //             on={{
        //               click: () => {
        //                 this.handleCollapse();
        //               },
        //             }}
        //           ></i>
        //         </div>
        //       );
        //     },
        //   },
        // },
        // {
        //   field: "sex",
        //   title: "性别",
        //   formatter: ({ cellValue }) => {
        //     return cellValue === 1 ? "男" : "女";
        //   },
        // },
        // {
        //   field: "age",
        //   title: "年龄",
        // },
        // {
        //   field: "birthDate",
        //   title: "出生日",
        //   width: 150,
        // },
        // {
        //   field: "entrance",
        //   title: "入学时间",
        //   width: 150,
        // },
        // {
        //   field: "WorkExperience",
        //   title: "工作年限",
        //   width: 250,
        //   formatter: ({ cellValue }) => {
        //     return `${cellValue[0]}至${cellValue[1]}`;
        //   },
        // },
        // {
        //   field: "hometown",
        //   title: "家乡",
        //   formatter: ({ cellValue }) => {
        //     return cellValue.label;
        //   },
        // },
        // {
        //   field: "hobbies",
        //   title: "兴趣",
        //   width: 150,
        //   formatter: ({ cellValue }) => {
        //     return this.translateHobbies(cellValue);
        //   },
        // },
        // {
        //   field: "job",
        //   title: "职业",
        //   width: 300,
        //   formatter: ({ cellValue }) => {
        //     return this.translateJob(cellValue);
        //   },
        // },
      ];
    },
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "name",
            title: "姓名",
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { value: "1", label: "启用" },
                { value: "0", label: "停用" },
              ],
            },
          },
          // {
          //   field: "birthDate",
          //   title: "出生日期",
          //   element: "el-date-picker",
          // },
          //   {
          //     field: "graduateYear",
          //     title: "毕业年份",
          //     element: "slot",
          //     slotName: "graduateYear",
          //     // rules: [{ required: true, message: "请选择年份" }],
          //   },
          // {
          //   field: "region",
          //   title: "区域(自定义控件)",
          //   element: "slot",
          //   slotName: "regionCascader",
          //   itemProps: {
          //     labelWidth: "130px",
          //   },
          //   defaultValue: [],
          //   // show: false,
          // },
          // {
          //   field: "entrance",
          //   title: "入学年月",
          //   element: "el-date-picker",
          //   props: {
          //     // placeholder: "选择日期",
          //     type: "month",
          //   },
          // },
          // {
          //   field: "WorkExperience",
          //   title: "工作年限",
          //   element: "el-date-picker",
          //   props: {
          //     // placeholder: "选择日期",
          //     type: "daterange",
          //   },
          // },
          //   {
          //     field: "hometown",
          //     title: "故乡",
          //     element: "a-tree-select",
          //     props: {
          //       labelInValue: true,
          //       treeData: hometownMock,
          //     },
          //   },
          // {
          //   field: "hobbies",
          //   title: "兴趣爱好",
          //   element: "el-select",
          //   props: {
          //     multiple: true,
          //     options: hobbiesMock,
          //     labelInValue: true,
          //   },
          // },
          // {
          //   field: "job",
          //   title: "工作类型",
          //   element: "el-cascader",
          //   props: {
          //     options: jobMock,
          //   },
          // },
          // {
          //   field: "job",
          //   title: "工作类型",
          //   element: "el-cascader",
          //   props: {
          //     options: jobMock,
          //   },
          // },
          // {
          //   field: "job",
          //   title: "工作类型",
          //   element: "el-cascader",
          //   props: {
          //     options: jobMock,
          //   },
          // },
          // {
          //   field: "job",
          //   title: "工作类型",
          //   element: "el-cascader",
          //   props: {
          //     options: jobMock,
          //   },
          // },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        modalFullScreen: true,
        submitBtn: true,
        okText: "保存草稿",
        cancelText: "取消",
        addBtn: true,
        menu: true,
        menuWidth: 300,
        menuFixed: "right",
        modalWidth: "1000px",
        formConfig: [
          {
            field: "name",
            title: "姓名",
            rules: [
              {
                required: true,
                message: "请输入姓名",
              },
            ],
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: [
                { value: "1", label: "启用" },
                { value: "0", label: "停用" },
              ],
            },
            rules: [
              {
                required: true,
                message: "请选择",
              },
            ],
            defaultValue: "1",
          },
          // {
          //   field: "age",
          //   title: "年龄",
          // },
          // {
          //   field: "birthDate",
          //   title: "出生日期",
          //   element: "el-date-picker",
          // },
          //   {
          //     field: "graduateYear",
          //     title: "毕业年份",
          //     element: "slot",
          //     slotName: "graduateYearForPop",
          //     rules: [
          //       {
          //         required: true,
          //         validator: (rule, value, callback) => {
          //           if (value?.startValue) {
          //             callback();
          //           } else {
          //             callback("请选择年份");
          //           }
          //         },
          //       },
          //     ],
          //     echoFormatter: (value) => {
          //       return { startValue: moment(value) };
          //     },
          //     previewFormatter: (value) => {
          //       return value.startValue.format("YYYY");
          //     },
          //   },
          // {
          //   field: "entrance",
          //   title: "入学年月",
          //   element: "el-date-picker",
          //   props: {
          //     // placeholder: "选择日期",
          //     type: "month",
          //   },
          // },
          //   {
          //     field: "WorkExperience",
          //     title: "工作年限",
          //     element: "a-range-picker",
          //     echoFormatter: (value) => {
          //       return [moment(value[0]), moment(value[1])];
          //     },
          //     previewFormatter: (value) => {
          //       return `${value[0]?.format("YYYY-MM-DD")}至${value[1]?.format(
          //         "YYYY-MM-DD"
          //       )}`;
          //     },
          //   },
          //   {
          //     field: "hometown",
          //     title: "家乡",
          //     element: "a-tree-select",
          //     props: {
          //       labelInValue: true,
          //       treeData: hometownMock,
          //     },
          //     previewFormatter: (value) => {
          //       return value.label;
          //     },
          //   },
          // {
          //   field: "hobbies",
          //   title: "兴趣爱好",
          //   element: "el-select",
          //   props: {
          //     multiple: true,
          //     options: hobbiesMock,
          //     labelInValue: true,
          //   },
          // },
          // {
          //   field: "job",
          //   title: "工作类型",
          //   element: "el-cascader",
          //   props: {
          //     options: jobMock,
          //   },
          // },
        ],
        customOperationTypes: [
          // {
          //   title: "签到",
          //   typeName: "signIn",
          //   clickConfirmConfig: {
          //     title: "Are you sure？",
          //     okText: "Yes",
          //     cancelText: "No",
          //   },
          //   event: (row) => {
          //     return new Promise((resolve) => {
          //       setTimeout(() => {
          //         this.$message.success("签到成功");
          //         this.signInList.push(row.id);
          //         this.loadData();
          //         resolve();
          //       }, 1000);
          //     });
          //   },
          //   condition: (row) => {
          //     return this.signInList.includes(row.id) ? false : true;
          //   },
          // },
          // {
          //   title: "完善信息",
          //   typeName: "completeInfo",
          //   slotName: "completeInfo",
          //   event: this.completeInfoHandler,
          //   condition: (row) => {
          //     return this.completeInfoList.includes(row.id) ? false : true;
          //   },
          // },
          // {
          //   title: "工作简历",
          //   typeName: "workEx",
          //   slotName: "workEx",
          //   showForm: false,
          //   event: (row) => {
          //     return this.$refs.crud.switchModalView(true, "workEx", row);
          //   },
          //   modalProps: {
          //     footer: null,
          //   },
          // },
          // {
          //   title: "出国申请",
          //   typeName: "abroadApplication",
          //   slotName: "abroadApplication",
          //   showForm: false,
          //   event: (row) => {
          //     return this.$refs.crud.switchModalView(
          //       true,
          //       "abroadApplication",
          //       row
          //     );
          //   },
          //   condition: (row) => {
          //     return row.age > 28;
          //   },
          // },
        ],
        formLayoutConfig: {
          defaultColSpan: 12,
          //定义弹窗表单的header
          header: () => {
            return <h3>基础信息</h3>;
          },
          //定义弹窗表单的footer
          footer: () => {
            return <p class="line"></p>;
          },
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    this.initData();
    this.loadData();
  },
  methods: {
    handleCollapse() {
      this.isFold = !this.isFold;
      // const fields = ["name1", "name2", "name3"];
      // const xTable4 = this.$refs.crud.getVxeTableRef();
      // fields.forEach((field) => {
      //   const column = xTable4.getColumnByField(field);
      //   console.log(column, "循环");
      //   column.visible = !this.isFold;
      // });
      // xTable4.getColumnByField("name4").visible = this.isFold;
      // xTable4.refreshColumn();
    },
    tabRadioChange(val) {
      console.log(val, "tab切换");
    },
    initData() {
      //获取性别数据
      this.getSexOptions();
    },
    async getSexOptions() {
      this.sexOptions = await getSexOptions();
    },
    translateJob(cellValue) {
      const strings = [];
      let currentFindObj = jobMock;
      const find = function(items, value) {
        const val = items.filter((item) => item.value === value)[0];
        return val;
      };
      if (!cellValue?.length) return "";
      for (let i = 0; i < cellValue.length; i++) {
        currentFindObj = find(
          currentFindObj.children || currentFindObj,
          cellValue[i]
        );
        strings.push(currentFindObj.label);
      }
      return strings.join("/");
    },
    translateHobbies(cellValue) {
      return hobbiesMock
        .filter((item) => {
          return cellValue.includes(item.value);
        })
        .map((item) => item.label)
        .join(",");
    },
    //完善个人信息
    completeInfoHandler(row) {
      return this.$refs.crud.switchModalView(true, "completeInfo", row);
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const {
        birthDate,
        graduateYear,
        entrance,
        WorkExperience,
        hometown,
        hobbies,
      } = this.params;
      const resolvedParams = {
        ...this.params,
        //多选/树形结构的数据也有可能要处理；
        hobbies: hobbies,
        hometown: hometown?.label,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        //选年的控件是自定义的，数据需要处理
        // graduateYear: graduateYear?.startValue.format("YYYY"),
        // //时间类型的数据需要处理下再请求接口;
        // birthDate: birthDate?.format("YYYY-MM-DD"),
        // entrance: entrance?.format("YYYY-MM"),
        // WorkExperience: [
        //   WorkExperience?.[0].format("YYYY-MM-DD"),
        //   WorkExperience?.[1].format("YYYY-MM-DD"),
        // ],
      };
      console.log(resolvedParams, "params");
      this.loading = true;
      const result = await api.getTableData(resolvedParams).catch(() => {
        this.loading = false;
      });
      this.loading = false;
      console.log(result, "res111");
      this.tableData = result.data;
      this.tablePage.total = result.total;
    },
    disableDateFunc(val) {
      return val.isAfter(moment());
    },
    abroadApplicationConfirmHandler() {
      return new Promise((resolve) => {
        this.$refs.abroadApplication.validate((val) => {
          if (!val) {
            //返回false，阻止弹窗关闭
            resolve(false);
          } else {
            console.log(
              "this.abroadApplicationParams",
              this.abroadApplicationParams
            );
            setTimeout(() => {
              this.$refs.abroadApplication.resetFields();
              resolve();
            }, 1000);
          }
        });
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler(value) {
      const { crudOperationType } = value;
      if (crudOperationType === "abroadApplication") {
        const res = await this.abroadApplicationConfirmHandler();
        return res;
      }
      console.log("modalConfirmHandler", value);
      return new Promise((resolve) => {
        setTimeout(() => {
          this.$message.success(`模拟保存草稿成功，${JSON.stringify(value)}`);
          resolve();
        }, 1000);
      });
    },
    //弹窗提交按钮事件
    async modalSubmit(value) {
      const { crudOperationType } = value;
      // if (crudOperationType === "abroadApplication") {
      //   const res = await this.abroadApplicationConfirmHandler();
      //   return res;
      // }
      // const res = await new Promise((resolve) => {
      //   setTimeout(() => {
      //     this.$message.success(`模拟提交成功，${JSON.stringify(value)}`);
      //     resolve();
      //   }, 1000);
      // });
      const res = await api[crudOperationType](value);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      }
      console.log(value, "提交");
    },
    modalCancelHandler() {
      this.$refs.abroadApplication?.resetFields?.();
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.job-table {
  padding-bottom: 16px;
}
</style>
