<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <!-- 筛选项插槽-区域选择器 -->
      <template slot="regionCascader">
        <RegionCascader
          v-model="params.region1"
          :props="{ checkStrictly: true }"
        ></RegionCascader>
      </template>
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['maintenance:timeConfig:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['maintenance:timeConfig:add']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="orderType" slot-scope="{ item, params }">
        <el-select
          v-model="params.orderType"
          filterable
          clearable
          style="width: 100%;"
          placeholder="请选择工单类型"
        >
          <el-option
            v-for="item in modalOrderTypeOptions"
            :label="item.typeName"
            :value="item.id"
            :key="item.id"
          />
          <el-option
            v-if="isDeletedOrderType(params.orderType)"
            :label="params.orderName"
            :key="params.orderType"
            :value="params.orderType"
          />
        </el-select>
      </template>
      <template #statusChange="{ row }"
        ><el-switch
          v-model="row.status"
          active-value="0"
          inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['maintenance:timeConfig:status'])"
        >
        </el-switch
      ></template>
      <template #custom="{ row, operationType }">
        <h3>自定义弹窗内容</h3>
        <span>弹窗type：{{ operationType }}</span>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'custom'">
          <el-button @click="handleCancelCustom">关闭</el-button>
        </div>
      </template>
      <template #modalDefault="{ row, operationType }">
        <div>defaultContent</div>
      </template>
    </BuseCrud>
    <!-- <StationDialog ref="stationDialog"></StationDialog> -->
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
// import StationDialog from "./components/stationDialog.vue";
import api from "@/api/ledger/urgencyDegree.js";
import { queryFirstOrderType } from "@/api/operationMaintenanceManage/configManage/checkGroup.js";
import { queryBindOrderTypeList } from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";
import RegionCascader from "./RegionCascader.vue";

export default {
  name: "timeConfigPage",
  components: { RegionCascader },
  mixins: [exportMixin],
  data() {
    return {
      businessTypeOptions: [],
      orderTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "urgencyId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "businessName",
          title: "业务类型",
          width: 150,
        },
        {
          field: "orderName",
          title: "工单类型",
          width: 150,
        },
        {
          field: "treatmentAging",
          title: "处理时效(h)",
          width: 150,
        },
        {
          field: "auditAging",
          title: "审核时效(h)",
          width: 150,
        },
        {
          field: "entrance",
          title: "已配置站点",
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.showStation(row),
                  }}
                >
                  {row.stationCount || 0}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "createByName",
          title: "创建人",
          width: 150,
          //   width: 250,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateByName",
          title: "修改人",
          width: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedData: [],
    };
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          //输入框
          {
            field: "stationName",
            element: "el-input",
            title: "站点名称",
          },
          //多选下拉
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "停用", value: 1 },
                { label: "启用", value: 0 },
              ],
            },
          },
          //下拉联动
          {
            field: "businessType",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.businessTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
            on: {
              change: (val) => {
                return this.handleBusinessChange(val);
              },
            },
          },
          {
            field: "orderType",
            title: "工单类型",
            element: "el-select",
            props: {
              options: this.orderTypeOptions,
              optionLabel: "typeName",
              optionValue: "id",
              filterable: true,
            },
          },
          //日期选择器
          {
            field: "birthDate",
            title: "出生日期",
            element: "el-date-picker",
          },
          //及联选择器
          {
            field: "region",
            title: "区域",
            element: "el-cascader",
            props: {
              options: regionData,
            },
          },
          //插槽写法
          {
            field: "region1",
            title: "区域(自定义控件)",
            element: "slot",
            slotName: "regionCascader",
            itemProps: {
              labelWidth: "130px", //自定义表单label宽度
            },
            defaultValue: [],
            // show: false,
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: true,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增时效规则",
        editBtn: checkPermission(["maintenance:timeConfig:edit"]),
        editTitle: "编辑时效规则",
        delBtn: checkPermission(["maintenance:timeConfig:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "businessType",
            title: "业务类型",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.businessTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择业务类型",
              },
            ],
            on: {
              change: (val) => {
                this.handleBusinessChange(val, "modal");
              },
            },
          },
          {
            field: "orderType",
            title: "工单类型",
            element: "slot",
            slotName: "orderType",
            rules: [
              {
                required: true,
                message: "请选择工单类型",
              },
            ],
          },
          {
            field: "treatmentAging",
            title: "处理时效",
            slots: {
              append: "h",
            },
            rules: [
              {
                required: true,
                message: "处理时效不允许为空！",
              },
              {
                pattern: /^(0|[1-9]\d{0,5}|999999)$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "请输入整数，允许为0",
            },
          },
          {
            field: "auditAging",
            title: "审核时效",
            slots: {
              append: "h",
            },
            rules: [
              {
                required: true,
                message: "审核时效不允许为空！",
              },
              {
                pattern: /^(0|[1-9]\d{0,5}|999999)$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "请输入整数，允许为0",
            },
          },
        ],
        //自定义操作按钮
        customOperationTypes: [
          {
            title: "自定义弹窗内容",
            typeName: "custom",
            slotName: "custom",
            showForm: false,
            event: (row) => {
              return this.rowCustom(row);
            },
            condition: (row) => {
              return true;
            },
          },
          {
            title: "配置站点",
            typeName: "stationConfig",
            event: (row) => {
              return this.handleStationConfig(row);
            },
            condition: (row) => {
              return checkPermission(["maintenance:timeConfig:stationConfig"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    Promise.all([
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    //手动清空已选择的方法
    cancelSelected() {
      this.$refs.crud.tableDeselectHandler();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handleCancelCustom() {
      this.$refs.crud.switchModalView(false);
    },
    rowCustom(row) {
      this.$refs.crud.switchModalView(true, "custom", { ...row });
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    checkPermission,
    //状态切换
    handleStatusChange(row) {
      // if (row.status == "0") {
      //   // this.$message.warning("自动派单的站点不能为空，请配置站点！");
      //   row.status = row.status == 1 ? 0 : 1;
      //   return;
      // }
      if (row.status == "1") {
        this.$confirm("是否确认停用？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then((res) => {
            this.updateStatus(row);
          })
          .catch(() => {
            row.status = row.status == "1" ? "0" : "1";
          });
      } else {
        this.updateStatus(row);
      }
    },
    updateStatus(row) {
      const text = row.status == "0" ? "启用" : "停用";
      const { updateTime, createTime, ...params } = row;
      api
        .update({ ...params, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.loadData();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },

    showStation(row) {
      // this.$refs.stationDialog.open(row);
    },
    isDeletedOrderType(id) {
      return id && !this.modalOrderTypeOptions?.some((y) => y.id == id);
    },
    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },
    handleBusinessChange(val, type = "filter") {
      if (type === "filter") {
        this.params.orderType = "";
        if (val) {
          queryFirstOrderType({ businessType: val }).then((res) => {
            this.orderTypeOptions = res.data;
          });
        } else {
          this.orderTypeOptions = [];
        }
      } else {
        this.$refs.crud.setFormFields({ orderType: "" });
        if (val) {
          queryBindOrderTypeList({ businessType: val }).then((res) => {
            this.modalOrderTypeOptions = res.data;
          });
        } else {
          this.modalOrderTypeOptions = [];
        }
      }
    },
    handleStationConfig(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/timeConfig/config",
        query: { agingId: row.agingId },
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.orderTypeOptions = [];
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const { updateTime, createTime, ...params } = formParams;
      api.update(params).then((res) => {
        if (res.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },

    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.modalOrderTypeOptions = [];
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      if (row.businessType) {
        queryBindOrderTypeList({ businessType: row.businessType }).then(
          (res) => {
            this.modalOrderTypeOptions = res.data;
          }
        );
      }
      this.$refs.crud.switchModalView(true, "UPDATE", { ...row });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
