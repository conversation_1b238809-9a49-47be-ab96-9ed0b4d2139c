import { tableMockData, sexOptions } from "./mock";
//模拟获取表格数据
export const getTableData = function (data) {
  console.log("api:getTableData", JSON.stringify(data));
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(tableMockData);
    }, 500);
  });
};

//模拟获取性别数据
export const getSexOptions = function (data) {
  console.log("api:getSexOptions", JSON.stringify(data));
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(sexOptions);
    }, 50);
  });
};
