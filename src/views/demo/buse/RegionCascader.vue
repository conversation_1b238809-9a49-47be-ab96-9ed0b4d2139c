<template>
  <div>
    <el-cascader
      v-model="internalValue"
      :options="options"
      @change="handleChange"
      clearable
      placeholder="请选择地区"
      style="width: 100%"
      v-bind="$attrs"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import { regionData } from "element-china-area-data";

export default {
  props: {
    value: Array, // 接受外部v-model传入的值
  },
  data() {
    return {
      internalValue: this.value, // 内部值用于与el-cascader组件双向绑定
      options: regionData,
      props: {},
    };
  },
  watch: {
    value(newVal) {
      this.internalValue = newVal;
    },
    internalValue(newVal) {
      this.$emit("input", newVal);
    },
  },
  methods: {
    handleChange(value) {
      this.internalValue = value;
    },
  },
};
</script>

<style scoped>
/* 你可以在这里添加针对该组件的特定样式 */
</style>
