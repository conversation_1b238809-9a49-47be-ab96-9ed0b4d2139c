//加单记录
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click.stop="jumpToOld"
          v-has-permi="['ledger:addRecord:oldRecord']"
          >历史加单记录
        </el-button>
        <!-- <el-button
          icon="el-icon-plus"
          type="primary"
          @click="JumpToEdit"
          v-has-permi="['maintenance:timeConfig:add']"
          >新增</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="handleBatchTypes"
          v-has-permi="['maintenance:timeConfig:add']"
          >批量导入</el-button
        > -->
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ledger:addRecord:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="auditResult" slot-scope="{ row }">
        <div style="display:flex;align-items:center;justify-content:center">
          <span>{{ row.auditResultName }}</span>
          <el-tooltip placement="top" v-if="row.auditResult == '2'">
            <div slot="content">{{ row.auditReason }}</div>
            <img
              :src="require('@/assets/icons/icon_question_mark.png')"
              width="15"
              height="15"
              style="margin-left:5px;"
            />
          </el-tooltip>
        </div>
      </template>
      <template #withdraw="{ row, crudOperationType }">
        <div class="confirm-text">确定要撤回到草稿状态重新编辑吗？</div>
        <el-form :model="row" ref="withdrawForm" label-width="110px">
          <el-form-item label="原因" prop="reason">
            <el-input
              v-model="row.reason"
              rows="5"
              maxlength="500"
              show-word-limit
              type="textarea"
              placeholder="请输入具体的原因描述，500个字符以内"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'check'">
          <el-button @click="submitCheck(row, false)">审核不通过</el-button>
          <el-button @click="submitCheck(row, true)" type="primary"
            >审核通过</el-button
          >
        </div>
      </template>
    </BuseCrud>
    <AddOrderDrawer ref="addOrderDrawer" @success="loadData"></AddOrderDrawer>
    <CheckOrderDrawer
      ref="checkOrderDrawer"
      @success="loadData"
    ></CheckOrderDrawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/addNewRecord.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { queryTreeList as queryOrderTreeList } from "@/api/ledger/workOrderType.js";
import { regionData } from "element-china-area-data";
import comApi from "@/api/ledger/company.js";
import { mapGetters } from "vuex";
import AddOrderDrawer from "../ledgerManage/components/addOrderDrawer.vue";
import CheckOrderDrawer from "../ledgerManage/components/checkOrderDrawer.vue";

import moment from "moment";
export default {
  name: "ledgerNewAddRecord",
  components: { AddOrderDrawer, CheckOrderDrawer },
  mixins: [exportMixin],
  data() {
    return {
      projectStatus: "",
      projectBatchId: "",
      businessTypeOptions: [],
      orderTypeOptions: [],
      activeTab: "0",
      //buse参数-s
      tabRadioList: [
        { value: "0", id: "0", label: "全部" },
        // { value: "1", id: "1", label: "进行中" },
      ],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "projectBatchId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "transfer",
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedData: [],
      auditStatusOptions: [
        { dictValue: "0", dictLabel: "待审核" },
        { dictValue: "1", dictLabel: "审核通过" },
        { dictValue: "2", dictLabel: "审核不通过" },
      ],
      supportDeptOptions: [],
      originOptions: [],
      urgencyDegreeOptions: [],
    };
  },

  watch: {},
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    Promise.all([
      this.getBusinessOptions(),
      this.getDicts("support_dept").then((response) => {
        this.supportDeptOptions = response.data;
      }),
      //工单来源
      this.getDicts("ledger_demand_source").then((response) => {
        this.originOptions = response.data;
      }),
      //紧急程度
      this.getDicts("urgency_level").then((response) => {
        this.urgencyDegreeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    jumpToOld() {
      this.$router.push({
        name: "ledgerAddRecord",
        params: {},
      });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    jumpToDetail(row) {
      this.$router.push({
        path: "/ledger/ledgerManage/detail",
        query: {
          orderId: row.orderId,
          orderNo: row.orderNo,
          orderType: 2,
        },
      });
    },
    jumpToList(row) {
      this.$router.push({
        name: "ledgerList",
        params: {
          orderNo: row.orderNo,
        },
      });
    },
    submitCheck(row, flag) {
      if (!flag && !row.auditReason) {
        this.$message.warning("审核不通过原因不能为空！");
        return;
      }
      const params = {
        ...row,
        auditResult: flag ? "1" : "2",
      };
      api.check(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.handleCancelCustom();
          this.loadData();
        }
      });
    },
    //获取业务类型
    getBusinessOptions() {
      queryTreeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
        this.businessTypeOptions = res.data;
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },

    //删除
    deleteRowHandler(row) {
      this.$confirm("确定删除该工单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          addOrderRecordId: row.addOrderId,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //审核
    handleCheck(row) {
      // this.operationType = "check";
      // this.$refs.crud.switchModalView(true, "check", {
      //   ...initParams(this.modalConfig.formConfig),
      //   ...row,
      // });
      this.$refs.checkOrderDrawer.open(row);
    },
    tabRadioChange(val) {
      this.activeTab = val;
      this.handleQuery();
    },

    handleCancelCustom() {
      this.$refs.crud.switchModalView(false);
    },

    checkPermission,

    handleExport() {
      const { businessType, orderTypeArr } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        oneBusinessTypeId: businessType?.[0],
        twoBusinessTypeId: businessType?.[1],
        threeBusinessTypeId: businessType?.[2],
        oneOrderTypeId: orderTypeArr?.[0],
        twoOrderTypeId: orderTypeArr?.[1],
        threeOrderTypeId: orderTypeArr?.[2],
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "addTime",
          title: "加单时间",
          startFieldName: "startAddTime",
          endFieldName: "endAddTime",
        },
        {
          field: "auditTime",
          title: "审核时间",
          startFieldName: "startAuditTime",
          endFieldName: "endAuditTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      const { businessType, orderTypeArr } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        oneBusinessTypeId: businessType?.[0],
        twoBusinessTypeId: businessType?.[1],
        threeBusinessTypeId: businessType?.[2],
        oneOrderTypeId: orderTypeArr?.[0],
        twoOrderTypeId: orderTypeArr?.[1],
        threeOrderTypeId: orderTypeArr?.[2],
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    getOrderTypeOptions(val) {
      this.params.orderType = undefined;
      if (val) {
        queryOrderTreeList({ supportDepts: val }).then((res) => {
          this.orderTypeOptions = res.data;
        });
      } else {
        this.orderTypeOptions = [];
      }
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:transfer/withdraw
      //   const res = await api[crudOperationType](params);
      //   if (res?.code === "10000") {
      //     this.$message.success("提交成功");
      //     this.loadData();
      //   } else {
      //     return false;
      //   }
    },
    querySearch(queryString, cb, api) {
      comApi[api]({
        name: queryString,
        companyName: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.companyName || x };
        });
        cb(result);
      });
    },
    handleEdit(row) {
      this.$refs.addOrderDrawer.open(row, "edit");
    },
  },
  computed: {
    ...mapGetters(["nickName", "roles"]),
    //系统管理员
    isAdminRole() {
      return this.roles.includes("admin");
    },
    tableColumn() {
      return [
        {
          field: "auditResultName",
          title: "审核状态",
          width: 160,
          slots: {
            default: "auditResult",
          },
        },
        {
          field: "orderNo",
          title: "工单单号",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.jumpToList(row),
                  }}
                >
                  {row.orderNo}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "nodeName",
          title: "加单工序",
          width: 100,
        },
        {
          field: "addTimeValue",
          title: "加单工时（h）",
          width: 150,
        },
        {
          field: "addTypeValue",
          title: "加单类别",
          width: 100,
        },
        {
          field: "operateUserName",
          title: "加单人",
          width: 100,
        },
        {
          field: "addTime",
          title: "加单时间",
          width: 150,
        },
        {
          field: "reason",
          title: "加单原因",
          width: 100,
        },
        {
          field: "auditUserName",
          title: "审核人",
          width: 100,
        },
        {
          field: "auditTime",
          title: "审核时间",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "orderNo",
            element: "el-input",
            title: "工单单号",
          },
          {
            field: "auditResult",
            title: "审核状态",
            element: "el-select",
            props: {
              options: this.auditStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "operateUserName",
            title: "加单人",
            element: "el-input",
          },
          {
            field: "addTime",
            title: "加单时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "addType",
            title: "加单类别",
            element: "el-select",
            props: {
              options: [
                { value: "0", label: "需审核" },
                { value: "1", label: "无需审核" },
              ],
              filterable: true,
            },
          },
          {
            field: "auditUserName",
            element: "el-input",
            title: "审核人",
          },
          {
            field: "auditTime",
            title: "审核时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        //审核
        check: [
          {
            field: "auditReason",
            element: "el-input",
            title: "不通过原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的原因描述，500个字符以内",
            },
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "编辑",
            typeName: "edit",
            event: (row) => {
              return this.handleEdit(row);
            },
            condition: (row) => {
              //待审核/审核不通过
              const condition1 = ["0", "2"].includes(row.auditResult);
              //加单人
              const condition2 = this.nickName === row.operateUserName;
              //需审核
              const condition3 = row.addType == "0";
              return (
                condition1 &&
                condition2 &&
                condition3 &&
                checkPermission(["ledger:addRecord:edit"])
              );
            },
          },
          {
            title: "审核",
            typeName: "check",
            modalTitle: "加单是否审核通过",
            event: (row) => {
              return this.handleCheck(row);
            },
            condition: (row) => {
              //待审核/审核不通过
              const condition1 = ["0", "2"].includes(row.auditResult);
              //加单审核人/系统管理员
              const condition2 =
                this.isAdminRole || this.nickName === row.auditUserName;
              //需审核
              const condition3 = row.addType == "0";
              return (
                condition1 &&
                condition2 &&
                condition3 &&
                checkPermission(["ledger:addRecord:check"])
              );
            },
          },
          {
            title: "详情",
            typeName: "detail",
            event: (row) => {
              return this.jumpToDetail(row);
            },
            condition: (row) => {
              //加单人
              const condition1 = this.nickName === row.operateUserName;
              //加单审核人
              const condition2 = this.nickName === row.auditUserName;
              return (
                (condition1 || condition2) &&
                checkPermission(["ledger:addRecord:detail"])
              );
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.confirm-text {
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}
</style>
