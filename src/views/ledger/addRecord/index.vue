//历史加单记录
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click.stop="jumpToNew"
          v-has-permi="['ledger:oldRecord:newRecord']"
          >返回加单记录
        </el-button>
        <!-- <el-button
          icon="el-icon-plus"
          type="primary"
          @click="JumpToEdit"
          v-has-permi="['maintenance:timeConfig:add']"
          >新增</el-button
        >
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="handleBatchTypes"
          v-has-permi="['maintenance:timeConfig:add']"
          >批量导入</el-button
        > -->
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ledger:oldRecord:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="auditStatus" slot-scope="{ row }">
        <div style="display:flex;align-items:center;justify-content:center">
          <span>{{
            row.auditStatus == "1"
              ? "待审核"
              : row.auditStatus == "2"
              ? "审核通过"
              : row.auditStatus == "3"
              ? "审核不通过"
              : row.auditStatus
          }}</span>
          <el-tooltip placement="top" v-if="row.auditStatus == '3'">
            <div slot="content">{{ row.auditReason }}</div>
            <img
              :src="require('@/assets/icons/icon_question_mark.png')"
              width="15"
              height="15"
              style="margin-left:5px;"
            />
          </el-tooltip>
        </div>
      </template>
      <template #withdraw="{ row, crudOperationType }">
        <div class="confirm-text">确定要撤回到草稿状态重新编辑吗？</div>
        <el-form :model="row" ref="withdrawForm" label-width="110px">
          <el-form-item label="原因" prop="reason">
            <el-input
              v-model="row.reason"
              rows="5"
              maxlength="500"
              show-word-limit
              type="textarea"
              placeholder="请输入具体的原因描述，500个字符以内"
            />
          </el-form-item>
        </el-form>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'check'">
          <el-button @click="submitCheck(row, false)">审核不通过</el-button>
          <el-button @click="submitCheck(row, true)" type="primary"
            >审核通过</el-button
          >
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/addRecord.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { queryTreeList as queryOrderTreeList } from "@/api/ledger/workOrderType.js";
import { regionData } from "element-china-area-data";
import comApi from "@/api/ledger/company.js";
import { mapGetters } from "vuex";

import moment from "moment";
export default {
  name: "ledgerAddRecord",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      projectStatus: "",
      projectBatchId: "",
      businessTypeOptions: [],
      orderTypeOptions: [],
      activeTab: "0",
      //buse参数-s
      tabRadioList: [
        { value: "0", id: "0", label: "全部" },
        // { value: "1", id: "1", label: "进行中" },
      ],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "projectBatchId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "transfer",
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedData: [],
      auditStatusOptions: [
        { dictValue: "1", dictLabel: "待审核" },
        { dictValue: "2", dictLabel: "审核通过" },
        { dictValue: "3", dictLabel: "审核不通过" },
      ],
      supportDeptOptions: [],
      originOptions: [],
      urgencyDegreeOptions: [],
    };
  },

  watch: {},
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    Promise.all([
      this.getBusinessOptions(),
      this.getDicts("support_dept").then((response) => {
        this.supportDeptOptions = response.data;
      }),
      //工单来源
      this.getDicts("ledger_demand_source").then((response) => {
        this.originOptions = response.data;
      }),
      //紧急程度
      this.getDicts("urgency_level").then((response) => {
        this.urgencyDegreeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    jumpToNew() {
      this.$router.push({
        name: "ledgerNewAddRecord",
        params: {},
      });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //跳转到新增/编辑/加单
    JumpToEdit(row, type = "add") {
      this.$router.push({
        path: `/ledger/ledgerManage/${type}`,
        query: {
          type: type,
          addOrderId: row.addOrderId,
          orderNo: row.orderNo,
        },
      });
    },
    jumpToDetail(row) {
      this.$router.push({
        path: "/ledger/ledgerManage/detail",
        query: {
          addOrderId: row.addOrderId,
          orderNo: row.orderNo,
          flowKey: row.flowKey,
          orderType: 2,
        },
      });
    },
    jumpToList(row) {
      this.$router.push({
        name: "ledgerList",
        params: {
          orderNo: row.mainOrderNo,
        },
      });
    },
    submitCheck(row, flag) {
      if (!flag && !row.auditReason) {
        this.$message.warning("审核不通过原因不能为空！");
        return;
      }
      const params = {
        ...row,
        auditStatus: flag ? "2" : "3",
      };
      api.check(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.handleCancelCustom();
          this.loadData();
        }
      });
    },
    //获取业务类型
    getBusinessOptions() {
      queryTreeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
        this.businessTypeOptions = res.data;
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },

    //删除
    deleteRowHandler(row) {
      this.$confirm("确定删除该工单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          addOrderRecordId: row.addOrderId,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //审核
    handleCheck(row) {
      this.operationType = "check";
      this.$refs.crud.switchModalView(true, "check", {
        ...initParams(this.modalConfig.formConfig),
        addOrderId: row.addOrderId,
      });
    },
    tabRadioChange(val) {
      this.activeTab = val;
      this.handleQuery();
    },

    handleCancelCustom() {
      this.$refs.crud.switchModalView(false);
    },

    checkPermission,

    handleExport() {
      const { businessType, orderTypeArr } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        oneBusinessTypeId: businessType?.[0],
        twoBusinessTypeId: businessType?.[1],
        threeBusinessTypeId: businessType?.[2],
        oneOrderTypeId: orderTypeArr?.[0],
        twoOrderTypeId: orderTypeArr?.[1],
        threeOrderTypeId: orderTypeArr?.[2],
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "addOrderTime",
          title: "加单时间",
          startFieldName: "startAddOrderTime",
          endFieldName: "endAddOrderTime",
        },
        {
          field: "auditTime",
          title: "审核时间",
          startFieldName: "auditStartTime",
          endFieldName: "auditEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      const { businessType, orderTypeArr } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        oneBusinessTypeId: businessType?.[0],
        twoBusinessTypeId: businessType?.[1],
        threeBusinessTypeId: businessType?.[2],
        oneOrderTypeId: orderTypeArr?.[0],
        twoOrderTypeId: orderTypeArr?.[1],
        threeOrderTypeId: orderTypeArr?.[2],
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    getOrderTypeOptions(val) {
      this.params.orderType = undefined;
      if (val) {
        queryOrderTreeList({ supportDepts: val }).then((res) => {
          this.orderTypeOptions = res.data;
        });
      } else {
        this.orderTypeOptions = [];
      }
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:transfer/withdraw
      //   const res = await api[crudOperationType](params);
      //   if (res?.code === "10000") {
      //     this.$message.success("提交成功");
      //     this.loadData();
      //   } else {
      //     return false;
      //   }
    },
    querySearch(queryString, cb, api) {
      comApi[api]({
        name: queryString,
        companyName: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.companyName || x };
        });
        cb(result);
      });
    },
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    tableColumn() {
      return [
        {
          field: "auditStatus",
          title: "审核状态",
          width: 100,
          slots: {
            default: "auditStatus",
          },
        },
        {
          field: "orderNo",
          title: "工单单号",
          width: 180,
        },
        {
          field: "mainOrderNo",
          title: "主工单单号",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.jumpToList(row),
                  }}
                >
                  {row.mainOrderNo}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "supportDeptName",
          title: "支持部门",
          width: 100,
        },
        {
          field: "businessTypeStr",
          title: "业务类型",
          width: 150,
        },
        {
          field: "orderTypeStr",
          title: "工单类型",
          width: 150,
        },
        // {
        //   field: "demandSourceName",
        //   title: "工单来源",
        //   width: 100,
        // },
        {
          field: "urgencyLevelName",
          title: "紧急程度",
          width: 100,
        },
        {
          field: "orderDesc",
          title: "问题描述",
          width: 100,
        },
        {
          field: "regionStr",
          title: "省市区",
          width: 150,
        },
        {
          field: "companyName",
          title: "公司名称",
          width: 100,
        },
        {
          field: "companyCategory",
          title: "公司类别",
          width: 100,
        },
        {
          field: "companyAttribute",
          title: "公司属性",
          width: 100,
        },
        {
          field: "createUserName",
          title: "加单人",
          width: 100,
        },
        {
          field: "addOrderTime",
          title: "加单时间",
          width: 150,
        },
        {
          field: "auditorName",
          title: "审核人",
          width: 100,
        },
        {
          field: "auditTime",
          title: "审核时间",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "orderNo",
            element: "el-input",
            title: "工单单号",
          },
          {
            field: "mainOrderNo",
            element: "el-input",
            title: "主工单单号",
          },
          {
            field: "addOrderUserName",
            title: "加单人",
            element: "el-input",
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              clearable: true,
              props: {
                checkStrictly: true,
                multiple: false,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.businessTypeOptions,
            },
          },
          {
            field: "supportDept",
            title: "支持部门",
            element: "el-select",
            props: {
              options: this.supportDeptOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
            on: {
              change: this.getOrderTypeOptions,
            },
          },
          {
            field: "orderTypeArr",
            title: "工单类型",
            element: "custom-cascader",
            attrs: {
              clearable: true,
              collapseTags: true,
              props: {
                checkStrictly: true,
                multiple: false,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.orderTypeOptions,
            },
          },
          {
            field: "auditStatus",
            title: "审核状态",
            element: "el-select",
            props: {
              options: this.auditStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          // {
          //   field: "demandSource",
          //   title: "工单来源",
          //   element: "el-select",
          //   props: {
          //     options: this.originOptions,
          //     optionLabel: "dictLabel", //自定义选项名
          //     optionValue: "dictValue", //自定义选项值
          //     filterable: true,
          //   },
          // },
          {
            field: "auditorName",
            element: "el-input",
            title: "审核人",
          },
          {
            field: "urgencyLevel",
            title: "紧急程度",
            element: "el-select",
            props: {
              options: this.urgencyDegreeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "region",
            title: "省市区",
            element: "custom-cascader",
            attrs: {
              clearable: true,
              collapseTags: true,
              props: {
                checkStrictly: true,
                multiple: true,
                // value: "areaCode",
                // label: "areaName",
              },
              options: regionData,
            },
          },
          {
            field: "addOrderTime",
            title: "加单时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "companyName",
            title: "公司名称",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryList");
              },
            },
          },
          {
            field: "companyCategory",
            title: "公司类别",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryCompanyType");
              },
            },
          },
          {
            field: "companyAttribute",
            title: "公司属性",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryCompanyAttrs");
              },
            },
          },
          {
            field: "auditTime",
            title: "审核时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        //审核
        check: [
          {
            field: "auditReason",
            element: "el-input",
            title: "不通过原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的原因描述，500个字符以内",
            },
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: checkPermission(["ledger:oldRecord:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [
          {
            type: "delete",
            condition: (row) => {
              //待审核/审核不通过
              const condition1 = ["1", "3"].includes(row.auditStatus);
              //加单人
              const condition2 = this.userId === row.createUser;
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:oldRecord:delete"])
              );
            },
          },
        ],
        customOperationTypes: [
          {
            title: "编辑",
            typeName: "edit",
            event: (row) => {
              return this.JumpToEdit(row, "addOrder");
            },
            condition: (row) => {
              //待审核/审核不通过
              const condition1 = ["1", "3"].includes(row.auditStatus);
              //加单人
              const condition2 = this.userId === row.createUser;
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:oldRecord:edit"])
              );
            },
          },
          {
            title: "审核",
            typeName: "check",
            modalTitle: "加单是否审核通过",
            event: (row) => {
              return this.handleCheck(row);
            },
            condition: (row) => {
              //待审核/审核不通过
              const condition1 = ["1", "3"].includes(row.auditStatus);
              //加单审核人
              const condition2 = this.userId === row.auditorId;
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:oldRecord:check"])
              );
            },
          },
          {
            title: "详情",
            typeName: "detail",
            event: (row) => {
              return this.jumpToDetail(row);
            },
            condition: (row) => {
              //加单人
              const condition1 = this.userId === row.createUser;
              //加单审核人
              const condition2 = this.userId === row.auditorId;
              return (
                (condition1 || condition2) &&
                checkPermission(["ledger:oldRecord:detail"])
              );
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.confirm-text {
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}
</style>
