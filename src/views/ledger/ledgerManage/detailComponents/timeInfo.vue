//时效统计
<template>
  <div>
    <el-card id="info" v-loading="loading">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>节点时效</span
        ><span style="color: #bbb;font-size: 12px;"
          >（总计需处理节点数{{ nodeObj.nodeCount }}个，已处理{{
            nodeObj.nodeDoneCount
          }}个节点，待处理{{ nodeObj.nodeTodoCount }}个节点）</span
        >
      </div>
      <NodeTimeline :list="nodeList"></NodeTimeline>
    </el-card>
  </div>
</template>

<script>
import api from "@/api/ledger/index.js";
import NodeTimeline from "@/components/Timeline/nodeTimeline.vue";
export default {
  components: {
    NodeTimeline,
  },
  data() {
    return {
      loading: false,
      nodeList: [
        // {
        //   name: "节点1",
        //   user: "王二狗",
        //   time: "2021-08-01",
        //   haoshi: "1小时",
        //   shixiao: "6.89",
        // },
        // {
        //   name: "节点2",
        //   user: "王二狗",
        //   time: "2021-08-01",
        //   haoshi: "1小时",
        //   shixiao: "6.89",
        // },
      ],
      nodeObj: { nodeCount: "-", nodeTodoCount: "-", nodeDoneCount: "-" },
    };
  },
  methods: {
    getDetail(row) {
      this.loading = true;
      api
        .queryProcessNode({ orderNo: row.orderNo, flowKey: row.flowKey })
        .then((res) => {
          this.loading = false;
          if (res?.code === "10000") {
            this.nodeObj = { ...this.nodeObj, ...res.data };
            this.nodeList = res.data?.processNodeTimeVos;
          }
        })
        .catch(() => {
          this.loading = false;
          this.nodeObj = {
            nodeCount: "-",
            nodeTodoCount: "-",
            nodeDoneCount: "-",
          };
          this.nodeList = [];
        });
    },
  },
};
</script>

<style></style>
