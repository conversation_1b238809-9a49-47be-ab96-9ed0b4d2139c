//处理信息
<template>
  <div>
    <el-card>
      <el-steps :space="300" align-center>
        <el-step
          v-for="(item, index) in stepsList"
          :key="index"
          :title="item.nodeName"
          :status="item.handleFlag"
          @click.native="handleClick(item, index)"
          :style="{
            cursor: item.handleFlag === 'wait' ? 'not-allowed' : 'pointer',
          }"
          :class="item.checked ? 'step-checked' : ''"
        >
          <div slot="description">
            <div>{{ item.assignee }}</div>
            <div v-if="item.handleFlag === 'success'">{{ item.endTime }}</div>
            <div
              v-else-if="item.handleFlag === 'process'"
              style="text-wrap: nowrap;"
            >
              应处理时间：{{ item.planEndTime }}
            </div>
          </div>
        </el-step>
      </el-steps>
    </el-card>
    <BaseInfo ref="base" v-if="isFirstNode" onlyShowBase></BaseInfo>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>节点处理信息</span>
      </div>
      <div v-for="(item, index) in formArr" :key="index">
        <FormCreatePreview :formData="item.formJson"></FormCreatePreview>
      </div>
      <!-- <div v-for="(item, index) in formArr" :key="index">
        <form-create
          :rule="item.formJson"
          v-model="dealFormData[index]"
          :option="item.formConfig"
        />
        <div v-if="item.formJson.showfileName">
          <div style="font-size:14px;margin:10px 0;">
            已上传的文件名称：
          </div>
          <div v-for="(j, i) in item.formJson" :key="i">
            <div v-if="j.fileList && j.fileList.length > 0">
              <div v-for="(f, fi) in j.fileList" :key="fi">
                {{ f.name }}
                <el-button type="text" size="small" @click="handleDownload(f)"
                  >下载</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div> -->
    </el-card>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>节点处理人</span>
      </div>
      <div v-for="(item, index) in handlerList" :key="index">
        <el-divider v-if="index !== 0"></el-divider>
        <DynamicForm
          ref="baseForm"
          :config="baseConfig"
          :params="item"
          :defaultColSpan="24"
          labelPosition="right"
          labelWidth="120px"
          :preview="true"
        ></DynamicForm>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from "@/api/ledger/index.js";
import formCreateMixin from "@/mixin/formCreate.js";
import FormCreatePreview from "@/components/formCreatePreview/index.vue";
import BaseInfo from "./baseInfo.vue";

export default {
  components: { FormCreatePreview, BaseInfo },
  mixins: [formCreateMixin],
  data() {
    return {
      isFirstNode: false,
      orderNo: "",
      orderId: "",
      handlerList: [
        // { ren: "笑笑", time: "2019-09-09", result: "提交", reason: "驳回" },
        // { ren: "笑笑2", time: "2019-09-09", result: "提交", reason: "驳回" },
      ],
      showfileName: false,
      stepsList: [
        // {
        //   name: "创建工单",
        //   status: "success",
        //   createByName: "小小",
        //   createTime: "2022-05-05",
        //   id: "1",
        // },
        // {
        //   name: "节点名称1",
        //   status: "process",
        //   createByName: "笑笑",
        //   createTime: "2022-05-05",
        //   shouldTime: "2022-05-05",
        //   checked: true,
        //   id: "2",
        // },
        // {
        //   name: "节点名称1",
        //   status: "wait",
        //   createByName: "笑笑",
        //   createTime: "2022-05-05",
        //   checked: false,
        //   id: "3",
        // },
      ],
      dealFormData: [],
      formArr: [],
    };
  },
  computed: {
    checkedData() {
      return this.stepsList?.find((item) => item.checked === true);
    },
    baseConfig() {
      return [
        {
          field: "handleUser",
          title: "处理人：",
        },
        {
          field: "endTime",
          title: "处理时间：",
        },
        {
          field: "handleResult",
          title: "处理结果：",
        },
        {
          field: "remark",
          title: "处理原因：",
          show: (params) => {
            return params.remark !== undefined;
          },
        },
      ];
    },
  },
  methods: {
    handleClick(item, index) {
      console.log(item, index);
      if (item.handleFlag === "wait") return;
      console.log(item);
      this.stepsList?.forEach((x, i) => {
        x.checked = false;
        if (i == index) {
          x.checked = true;
        }
      });
      this.getHandleForm();
      this.getNodeHandlerList();
      this.isFirstNode = false;

      if (index === 0) {
        this.isFirstNode = true;
        this.$nextTick(() => {
          this.$refs.base?.getDetail({
            orderId: this.orderId,
            orderNo: this.orderNo,
          });
        });
      }
    },
    getDetail(row) {
      this.orderNo = row.orderNo;
      this.orderId = row.orderId;
      api.queryHandleSteps({ orderNo: row.orderNo }).then((res) => {
        if (res?.code === "10000") {
          this.stepsList = res.data?.map((x) => {
            return { ...x, checked: false };
          });
          //取最后一个非wait的节点设为checked
          for (let i = this.stepsList.length - 1; i >= 0; i--) {
            if (this.stepsList[i].handleFlag !== "wait") {
              this.stepsList[i].checked = true;
              break; // Exit the loop after setting the first found task
            }
          }
          this.getHandleForm();
          this.getNodeHandlerList();
        }
      });
    },
    getNodeHandlerList() {
      if (this.checkedData) {
        api
          .getTaskNodeInfo({
            orderNo: this.orderNo,
            taskDefKey: this.checkedData.taskDefKey,
          })
          .then((res) => {
            if (res?.code === "10000") {
              this.handlerList = res.data;
            }
          });
      }
    },
    getHandleForm() {
      if (this.checkedData) {
        api
          .getFormJsonByNode({
            orderNo: this.orderNo,
            taskId: this.checkedData.taskId,
            handleFlag: this.checkedData.handleFlag,
          })
          .then((res) => {
            if (res?.code === "10000") {
              this.formArr = res.data?.dealForms?.map((x) => {
                return {
                  ...x,
                  formConfig: JSON.parse(x.formConfig),
                  formJson: JSON.parse(x.formJson)?.map((item) => {
                    this.handlePreviewFormRule(item);
                    return item;
                  }),
                  dealFormData: {},
                };
              });
            }
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.step-checked {
  /deep/ .el-step__icon.is-text {
    background: #029c7c;
    color: #fff;
    border-color: #029c7c;
  }
}
/deep/ .el-steps {
  justify-content: center;
}
</style>
