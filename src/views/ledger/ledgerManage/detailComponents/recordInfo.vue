//操作记录
<template>
  <div>
    <Timeline
      :list="recordList"
      operateTypeTitle="operateTypeName"
      operatorNameTitle="operatorName"
      createTimeTitle="operateTime"
      operateDetailTitle="remark"
    ></Timeline>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import api from "@/api/ledger/index.js";

export default {
  components: {
    Timeline,
  },
  data() {
    return {
      recordList: [],
    };
  },
  methods: {
    getDetail(row) {
      api
        .queryLedgerRecord({ orderNo: row.orderNo, orderType: row.orderType })
        .then((res) => {
          if (res?.code === "10000") {
            this.recordList = res.data;
          }
        });
    },
  },
};
</script>

<style></style>
