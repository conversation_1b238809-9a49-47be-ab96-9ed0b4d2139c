<!-- 处理工单 -->
<template>
  <div class="app-container">
    <h3>处理工单</h3>
    <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
    <el-divider></el-divider>
    <el-card>
      <el-steps :space="300" align-center>
        <el-step
          v-for="(item, index) in stepsList"
          :key="index"
          :title="item.nodeName"
          :status="item.handleFlag"
          @click.native="handleClick(item, index)"
          :style="{
            cursor: item.handleFlag === 'wait' ? 'not-allowed' : 'pointer',
          }"
          :class="item.checked ? 'step-checked' : ''"
        >
          <div slot="description">
            <div>{{ item.assignee }}</div>
            <div v-if="item.handleFlag === 'success'">{{ item.endTime }}</div>
            <div
              v-else-if="item.handleFlag === 'process'"
              style="text-wrap: nowrap;"
            >
              应处理时间：{{ item.planEndTime }}
            </div>
          </div>
        </el-step>
      </el-steps>
    </el-card>
    <el-divider></el-divider>
    <BaseInfo ref="base" v-if="isFirstNode" onlyShowBase></BaseInfo>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理工单</span>
      </div>
      <div v-if="handleFlag === 'process'">
        <form-create
          v-for="(item, index) in formArr"
          :key="index"
          :rule="item.formJson"
          v-model="dealFormData[index]"
          :option="item.formConfig"
        />
      </div>
      <div
        v-if="handleFlag === 'success'"
        v-for="(item, index) in formArr"
        :key="index"
      >
        <FormCreatePreview :formData="item.formJson"></FormCreatePreview>
      </div>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>通知人</span>
      </div>
      <DynamicForm
        ref="noticeForm"
        :config="noticeConfig"
        :params="noticeParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      ></DynamicForm>
    </el-card>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
    >
      <div slot="modalDefault">
        <div v-if="operationType === 'cancel'" class="cancel-tip">
          确定要作废该工单吗？
        </div>
      </div>
    </BaseFormModal>
    <div class="dialog-footer" v-if="handleFlag === 'process'">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading"
        >返回</el-button
      >
      <el-button
        @click.stop="handleReject"
        size="medium"
        :loading="btnLoading"
        v-if="showBackBtn === 'Y'"
        >驳回</el-button
      >
      <el-button
        @click.stop="handleCancel"
        size="medium"
        :loading="btnLoading"
        v-if="showInvalidBtn === 'Y'"
        >作废</el-button
      >
      <el-button
        @click="submit"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import formCreateMixin from "@/mixin/formCreate.js";
import api from "@/api/ledger/index.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import { initParams } from "@/utils/buse.js";
import FormCreatePreview from "@/components/formCreatePreview/index.vue";
import BaseInfo from "./detailComponents/baseInfo.vue";
import { listAllUser } from "@/api/common.js";

export default {
  components: { FormCreatePreview, BaseDescriptions, BaseFormModal, BaseInfo },
  mixins: [formCreateMixin],
  props: {
    flowKeyProp: {
      type: String,
      default: "",
    },
    orderIdProp: {
      type: String,
      default: "",
    },
    orderNoProp: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      userOptions: [],
      noticeParams: {},
      isFirstNode: false,
      flowKey: "",
      handleFlag: "process",
      showInvalidBtn: "N",
      showBackBtn: "N",
      rejectNodeOptions: [],
      btnLoading: false,
      operationType: "reject",
      //formCreate-s
      showfileName: false,
      dealFormData: [],
      formArr: [],
      //formCreate-e
      orderId: "",
      orderNo: "",
      stepsList: [
        // {
        //   name: "创建工单",
        //   status: "success",
        //   createByName: "小小",
        //   createTime: "2022-05-05",
        //   id: "1",
        // },
        // {
        //   name: "节点名称1",
        //   status: "process",
        //   createByName: "笑笑",
        //   createTime: "2022-05-05",
        //   shouldTime: "2022-05-05",
        //   checked: true,
        //   id: "2",
        // },
        // {
        //   name: "节点名称1",
        //   status: "wait",
        //   createByName: "笑笑",
        //   createTime: "2022-05-05",
        //   checked: false,
        //   id: "3",
        // },
      ],
      desObj: {},
    };
  },
  computed: {
    noticeConfig() {
      return [
        {
          field: "ccUserIdList",
          element: "el-select",
          title: "通知人",
          props: {
            options: this.userOptions,
            multiple: true,
            filterable: true,
          },
          rules: [{ required: false, message: "请选择通知人" }],
        },
        {
          field: "handleMessageType",
          element: "el-checkbox-group",
          title: "通知方式",
          props: {
            options: [
              { label: "钉钉", value: "00" },
              { label: "短信", value: "01" },
            ],
          },
          rules: [
            {
              required: this.noticeParams.ccUserIdList?.length > 0,
              message: "请选择通知方式",
            },
          ],
        },
      ];
    },
    checkedData() {
      return this.stepsList?.find((item) => item.checked === true);
    },
    modalConfig() {
      const form = {
        //作废
        cancel: {
          modalTitle: "作废",
          formConfig: [
            {
              field: "invalidRemark",
              element: "el-input",
              title: "作废原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入具体的原因描述，500个字符以内",
              },
            },
          ],
        },
        //驳回
        reject: {
          modalTitle: "驳回",
          formConfig: [
            {
              field: "taskDefKey",
              element: "el-select",
              title: "驳回节点",
              props: {
                options: this.rejectNodeOptions,
                optionLabel: "nodeName",
                optionValue: "taskDefKey",
              },
              rules: [{ required: true, message: "请选择" }],
            },
            {
              field: "backRemark",
              element: "el-input",
              title: "驳回原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入具体的原因描述，500个字符以内",
              },
            },
          ],
        },
      };
      return form[this.operationType];
    },
    baseList() {
      return [
        {
          title: "工单单号",
          value: this.desObj?.orderNo,
        },
        {
          title: "流程节点数",
          value: this.desObj?.totalNode,
        },
        {
          title: "工单类型",
          value: this.desObj?.orderTypeStr,
        },
        {
          title: "超时节点",
          value: this.desObj?.timeoutNodeCount,
        },
      ];
    },
  },
  created() {
    this.flowKey = this.$route.query.flowKey || this.flowKeyProp;
    this.orderId = this.$route.query.orderId || this.orderIdProp;
    this.orderNo = this.$route.query.orderNo || this.orderNoProp;
    this.listAllUser();
    this.noticeParams = initParams(this.noticeConfig);
    this.getDetail();
  },
  methods: {
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    handleClick(item, index) {
      if (item.handleFlag === "wait") return;
      this.handleFlag = item.handleFlag;

      console.log(item);
      this.stepsList?.forEach((x, i) => {
        x.checked = false;
        if (i == index) {
          x.checked = true;
        }
      });
      this.getHandleForm();
      // this.getNodeHandlerList();
      this.isFirstNode = false;

      if (index === 0) {
        this.isFirstNode = true;
        this.$nextTick(() => {
          this.$refs.base?.getDetail({
            orderId: this.orderId,
            orderNo: this.orderNo,
          });
        });
      }
    },
    async checkDealForms() {
      let flag = true;
      if (this.formArr?.length > 0) {
        flag = false;
        for (let i = 0; i < this.dealFormData.length; i++) {
          await this.dealFormData[i].validate((valid) => {
            if (valid) {
              flag = true;
            } else {
              this.$message.warning("请填写完整表单信息!");
            }
          });
        }
      }
      return flag;
    },
    async submit() {
      if (!(await this.checkDealForms())) {
        return;
      }
      this.$refs.noticeForm.validate((valid) => {
        if (!valid) return;
        this.$confirm(`是否确认提交？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async (response) => {
          const params = {
            taskId: this.checkedData.taskId,
            taskDefKey: this.checkedData.taskDefKey,
            orderNo: this.orderNo,
            preDealForms: this.formArr?.map((x) => {
              return {
                ...x,
                formJson: JSON.stringify(x.formJson),
                formConfig: JSON.stringify(x.formConfig),
              };
            }),
            ...this.noticeParams,
          };
          api.handle(params).then((res) => {
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              if (this.flowKeyProp) {
                // 详情抽屉
                this.$emit("submit");
              } else {
                this.goBack();
              }
            }
          });
        });
      });
    },
    modalConfirmHandler(row) {
      //operationType：cancel/reject
      const text = this.operationType === "cancel" ? "作废" : "驳回";
      api[this.operationType](row).then((res) => {
        if (res?.code === "10000") {
          this.$message.success(`${text}成功`);
          this.goBack();
        }
      });
    },
    handleReject() {
      this.getReJectNodeOptions();
      this.operationType = "reject";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        orderNo: this.orderNo,
        taskId: this.checkedData.taskId,
      });
    },
    getReJectNodeOptions() {
      console.log("--------", this.checkedData.taskId, this.orderNo);
      api
        .getBackNodes({
          taskId: this.checkedData.taskId,
          orderNo: this.orderNo,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.rejectNodeOptions = res.data;
          }
        });
    },
    handleCancel() {
      this.operationType = "cancel";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        taskId: this.checkedData.taskId,
        orderNo: this.orderNo,
      });
    },
    goBack() {
      this.$router.go(-1);
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    getHandleForm() {
      if (this.checkedData) {
        api
          .getFormJsonByNode({
            orderNo: this.orderNo,
            taskId: this.checkedData.taskId,
            handleFlag: this.checkedData.handleFlag,
          })
          .then((res) => {
            if (res?.code === "10000") {
              this.formArr = res.data?.dealForms?.map((x) => {
                return {
                  ...x,
                  formConfig: JSON.parse(x.formConfig),
                  formJson: JSON.parse(x.formJson)?.map((item) => {
                    this.handleFormRule(item);
                    return item;
                  }),
                  dealFormData: {},
                };
              });
            }
          });
      }
    },
    getBtnShow() {
      api
        .queryBtnShow({
          flowKey: this.flowKey,
          taskDefKey: this.checkedData.taskDefKey,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.showInvalidBtn = res.data?.showInvalidBtn;
            this.showBackBtn = res.data?.showBackBtn;
          }
        });
    },
    async getDetail() {
      api.queryHandleSteps({ orderNo: this.orderNo }).then((res) => {
        if (res?.code === "10000") {
          this.stepsList = res.data?.map((x) => {
            if (x.handleFlag === "process") {
              x.checked = true;
            } else {
              x.checked = false;
            }
            return { ...x };
          });
          this.getHandleForm();
          this.getBtnShow();
        }
      });
      api.queryDetail({ orderId: this.orderId }).then((res) => {
        if (res?.code === "10000") {
          this.desObj = res.data;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.cancel-tip {
  font-size: 18px;
  text-align: center;
  margin-bottom: 20px;
  color: rgb(16, 16, 16);
}
.step-checked {
  /deep/ .el-step__icon.is-text {
    background: #029c7c;
    color: #fff;
    border-color: #029c7c;
  }
}
/deep/ .el-steps {
  justify-content: center;
}
</style>
