//新增/编辑/加单
<template>
  <div class="app-container" v-loading="pageLoading">
    <h3>
      {{ type === "add" ? "新增工单" : type === "edit" ? "修改工单" : "加单" }}
    </h3>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="baseParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      >
        <template #urgencyLevel>
          <el-select
            v-model="baseParams.urgencyLevel"
            placeholder="请选择紧急程度"
            filterable
            style="width: 95%"
            clearable
          >
            <el-option
              v-for="(item, index) in urgencyLevelOptions"
              :key="index"
              :label="item.urgencyName"
              :value="item.urgencyId"
            />
          </el-select>
          <el-tooltip
            effect="dark"
            :content="getUrgencyExplain(baseParams.urgencyLevel)"
            placement="top"
            v-if="getUrgencyExplain(baseParams.urgencyLevel)"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template #fileUpload>
          <file-upload
            ref="upload"
            v-model="baseParams.annexList"
            :limit="20"
            accept=".jpg, .jpeg, .png, .xls, .xlsx, .pdf, .doc, .docx"
            :fileMaxSize="200"
            textTip="支持批量上传，上传格式为jpg、jpeg、png、xls、xlsx、pdf、doc、docx文件，单个文件200M以内"
          />
        </template>
        <template #stationSelect>
          <el-select
            v-model="baseParams.stationId"
            placeholder="请选择或搜索站点名称"
            filterable
            remote
            style="width: 100%"
            clearable
            @change="handleStationChange"
            v-el-select-loadmore="getStationList"
            :remote-method="remoteStationMethod"
          >
            <el-option
              v-for="(item, index) in stationOptions"
              :key="index"
              :label="item.stationName"
              :value="item.stationId"
            />
          </el-select>
        </template>
        <template #stations>
          <DynamicStationGroup
            v-model="baseParams.stations"
            :needValidate="showMultiStation"
          ></DynamicStationGroup>
        </template>
      </DynamicForm>
    </el-card>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <form-create
        v-for="(item, index) in formArr"
        :key="index"
        :rule="item.formJson"
        v-model="dealFormData[index]"
        :option="item.formConfig"
      />
    </el-card>
    <!-- <el-card id="info" v-show="type === 'addOrder'">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>审核人</span>
      </div>
      <el-form
        :model="checkForm"
        ref="checkForm"
        label-width="130px"
        :rules="checkRules"
      >
        <el-form-item label="请指定审核人：" prop="auditorId">
          <el-select v-model="checkForm.auditorId" filterable clearable>
            <el-option
              v-for="item in userOption"
              :key="item.userId"
              :label="item.userName + '-' + item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </el-card> -->
    <div class="dialog-footer">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading"
        >取 消</el-button
      >
      <el-button
        @click="submit(0)"
        size="medium"
        :loading="btnLoading"
        v-if="type !== 'addOrder' && !this.addOrderId"
        >保存</el-button
      >
      <el-button
        @click="submit(1)"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import { queryTreeList } from "@/api/ledger/businessType.js";
import FileUpload from "@/components/Upload/fileUpload4.vue";
import { initParams } from "@/utils/buse.js";
import { regionData } from "element-china-area-data";
import api from "@/api/ledger/index.js";
import { listAllUser } from "@/api/common";
import { getToken } from "@/utils/auth";
import formCreateMixin from "@/mixin/formCreate.js";
import { queryStationInfoByPage } from "@/api/operationMaintenanceManage/station/index.js";
import comApi from "@/api/ledger/company.js";
import DynamicStationGroup from "@/components/DynamicStationGroup.vue";
export default {
  name: "ledgerAddPage",
  components: { FileUpload, DynamicStationGroup },
  mixins: [formCreateMixin],
  data() {
    return {
      orderId: "",
      addOrderId: "",
      orderNo: "",
      userOption: [],
      checkForm: {
        auditorId: "",
      },
      btnLoading: false,
      pageLoading: false,
      type: "add",
      baseParams: {},
      supportDeptOptions: [],
      orderTypeOptions: [],
      businessTypeOptions: [],
      urgencyLevelOptions: [],
      demandOriginOptions: [],
      stationOptions: [],
      companyOptions: [],
      //formCreate-s
      showfileName: false,
      formJson: [],
      formConfig: {},
      dealFormData: [],
      formArr: [],
      //formCreate-e
      currentStationPage: 1,
      stationText: "",
      stationTotal: 0,
      isStationLoading: false,
    };
  },
  computed: {
    checkRules() {
      return {
        auditorId: [
          {
            required: this.type === "addOrder",
            message: "请选择指定审核人",
            trigger: "change",
          },
        ],
      };
    },
    baseConfig() {
      return [
        {
          field: "supportDept",
          title: "支持部门：",
          element: "el-select",
          props: {
            options: this.supportDeptOptions,
            placeholder: "请选择支持部门",
            filterable: true,
            clearable: true,
            optionValue: "supportDept",
            optionLabel: "supportDeptName",
          },
          rules: [
            {
              required: true,
              message: "请选择支持部门",
              trigger: "change",
            },
          ],
          on: {
            change: this.handleDeptChange,
          },
        },
        {
          field: "businessType",
          title: "业务类型：",
          element: "el-cascader",
          ref: "businessCascader",
          props: {
            popperClass: "location",
            collapseTags: true,
            props: {
              checkStrictly: true,
              multiple: false,
              value: "id",
              label: "typeName",
              children: "childrenList",
              expandTrigger: "hover",
              // emitPath: false,
            },
            options: this.businessTypeOptions,
            placeholder: "请选择业务类型",
          },
          rules: [
            {
              required: true,
              message: "请选择业务类型",
              trigger: "change",
            },
          ],
          on: {
            change: this.handleBusinessChange,
          },
          defaultValue: [],
        },
        {
          field: "orderTypeArr",
          title: "工单类型：",
          element: "el-cascader",
          ref: "orderCascader",
          props: {
            popperClass: "location",
            collapseTags: true,
            props: {
              checkStrictly: true,
              multiple: false,
              value: "id",
              label: "typeName",
              children: "childrenList",
              expandTrigger: "hover",
              // emitPath: false,
            },
            options: this.orderTypeOptions,
            placeholder: "请选择工单类型",
          },
          rules: [
            {
              required: true,
              message: "请选择工单类型",
              trigger: "change",
            },
          ],
          on: {
            change: (val) => {
              this.getFormJson(val);
              this.getOrderDesc(val);
              this.$refs.baseForm.$refs.orderCascader.dropDownVisible = false;
            },
          },
          defaultValue: [],
        },
        {
          field: "urgencyLevel",
          title: "紧急程度：",
          element: "slot",
          slotName: "urgencyLevel",
          props: {
            options: this.urgencyLevelOptions,
            placeholder: "请选择紧急程度",
            filterable: true,
            clearable: true,
            optionValue: "urgencyId",
            optionLabel: "urgencyName",
          },
          rules: [
            {
              required: true,
              message: "请选择紧急程度",
              trigger: "change",
            },
          ],
          // defaultValue: "3",
        },
        {
          field: "orderDesc",
          title: "问题描述：",
          element: "el-input",
          props: { type: "textarea" },
          attrs: {
            rows: 5,
            maxlength: 10000,
            showWordLimit: true,
            placeholder: "10000个字符以内",
          },
          rules: [
            {
              required: true,
              message: "请输入问题描述",
            },
          ],
        },
        {
          field: "demandSource",
          title: "需求来源：",
          element: "el-select",
          props: {
            options: this.demandOriginOptions,
            placeholder: "请选择需求来源",
            filterable: true,
            clearable: true,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
          },
          rules: [
            {
              required: true,
              message: "请选择需求来源",
              trigger: "change",
            },
          ],
          defaultValue: "01",
        },
        {
          field: "annexList",
          title: "附件/图片：",
          element: "slot",
          slotName: "fileUpload",
          defaultValue: [],
        },
        {
          field: "stationId",
          title: "站点名称：",
          element: "slot",
          slotName: "stationSelect",
          props: {
            options: this.stationOptions,
            placeholder: "请选择站点名称",
            filterable: true,
            clearable: true,
            optionValue: "stationId",
            optionLabel: "stationName",
          },
          on: {
            change: this.handleStationChange,
          },
          show: !this.showMultiStation,
        },
        {
          field: "region",
          title: "省市区：",
          element: "custom-cascader",
          attrs: {
            options: regionData,
            placeholder: "请选择省市区",
            filterable: true,
            clearable: true,
          },
          show: !this.showMultiStation,
        },
        {
          field: "detailAddress",
          title: "详细地址：",
          element: "el-input",
          attrs: {
            placeholder: "请输入详细地址",
          },
          show: !this.showMultiStation,
        },
        {
          field: "companyId",
          title: "公司名称：",
          element: "el-select",
          props: {
            options: this.companyOptions,
            placeholder: "请选择公司名称",
            filterable: true,
            clearable: true,
            optionValue: "companyId",
            optionLabel: "companyName",
          },
        },
        {
          field: "stations",
          title: "",
          element: "slot",
          slotName: "stations",
          defaultValue: [
            {
              stationName: "",
              businessId: "",
            },
          ],
          itemProps: {
            labelWidth: "0px",
          },
          show: this.showMultiStation,
        },
      ];
    },
    flattenOrderTypeOptions() {
      return this.flattenArray(this.orderTypeOptions);
    },
    showMultiStation() {
      const name = this.orderTypeOptions?.find(
        (x) => x.id === this.baseParams.orderTypeArr[0]
      )?.typeName;
      return name === "协议申请";
    },
  },
  created() {
    this.orderId = this.$route.query.orderId;
    this.addOrderId = this.$route.query.addOrderId;
    this.orderNo = this.$route.query.orderNo;
    this.type = this.$route.query.type;
    // this.getDicts("support_dept").then((response) => {
    //   this.supportDeptOptions = response.data;
    // });
    api.permissionDept({}).then((res) => {
      this.supportDeptOptions = res?.data;
    });
    // this.getDicts("urgency_level").then((response) => {
    //   this.urgencyLevelOptions = response?.data;
    // });
    this.getDicts("ledger_demand_source").then((response) => {
      this.demandOriginOptions = response?.data;
    });
    this.getStationList();
    // this.getBusinessOptions();
    this.getListUser();
    this.queryCompanyOptions();
    this.baseParams = initParams(this.baseConfig);
    this.getDetail();
    //新增时回显上次选择的支持部门
    if (this.type === "add") {
      const supportDept = localStorage.getItem("supportDept");
      if (
        supportDept &&
        this.supportDeptOptions?.some(
          (item) => item.supportDept === supportDept
        )
      ) {
        this.baseParams.supportDept = supportDept;
        this.getBusinessOptions(supportDept);
      }
    }
  },
  // watch: {
  //   baseParams: {
  //     handler(val) {
  //       console.log(val, "------val");
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    getOrderDesc(val) {
      if (val?.length > 0) {
        const orderDesc =
          this.orderTypeOptions?.find((x) => x.id == val[0])?.orderDesc || "";
        this.baseParams.orderDesc = orderDesc;
      }
    },
    getUrgencyOptions(params, showDefault = false) {
      api
        .getUrgencyDegree({
          pageSize: 99999,
          pageNum: 1,
          status: 0,
          ...params,
        })
        .then((res) => {
          this.urgencyLevelOptions = res.data;
          if (showDefault) {
            //选择部门和业务类型后，紧急程度如果有一般选项则默认一般，没有则为空
            const level =
              this.urgencyLevelOptions?.find((x) => x.urgencyName === "一般") ||
              {};
            this.baseParams.urgencyLevel = level.urgencyId;
          }
        });
    },
    getUrgencyExplain(level) {
      return this.urgencyLevelOptions?.find((x) => x.urgencyId === level)
        ?.urgencyDefinition;
    },
    async remoteStationMethod(val) {
      this.stationOptions = [];
      this.currentStationPage = 1;
      this.stationText = val;
      await this.getStationList();
    },
    async getStationList() {
      const page =
        this.stationTotal == 0 ? 1 : Math.ceil(this.stationTotal / 10);
      if (this.isStationLoading || this.currentStationPage > page) {
        return;
      }
      this.isStationLoading = true;
      const params = {
        pageNum: this.currentStationPage,
        pageSize: 10,
        stationName: this.stationText,
      };
      const res = await queryStationInfoByPage(params);
      this.stationTotal = res?.total;
      const newOptions = res?.data?.map((x) => {
        return { ...x };
      });
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentStationPage++;
      }
      this.isStationLoading = false;
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    queryCompanyOptions() {
      comApi.queryList({ pageNum: 1, pageSize: 99999 }).then((res) => {
        this.companyOptions = res.data;
      });
    },
    handleStationChange(val) {
      const obj = this.stationOptions.find((x) => x.stationId === val);
      this.baseParams.region = obj.provinceCityCounty;
      this.baseParams.detailAddress = obj.stationAddress;
    },
    goBack() {
      this.$router.go(-1);
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      this.userOption = data;
    },
    handleDeptChange(val) {
      this.baseParams.urgencyLevel = "";
      this.baseParams.orderTypeArr = [];
      //根据部门获取业务类型下拉选项
      this.baseParams.businessType = [];
      this.getBusinessOptions(val);
      // const len = this.baseParams.businessType?.length || 0;
      // if (len > 0) {
      //   api
      //     .queryOrderOptionsByBusiness({
      //       supportDept: val,
      //       businessTypeId: this.baseParams.businessType[len - 1],
      //     })
      //     .then((res) => {
      //       this.orderTypeOptions = res.data?.map((x) => {
      //         return { ...x, disabled: true };
      //       });
      //     });
      //   this.getUrgencyOptions({
      //     supportDept: val,
      //     businessType: this.baseParams.businessType[0],
      //   });
      // }
    },
    handleBusinessChange(val) {
      this.$refs.baseForm.$refs.businessCascader.dropDownVisible = false;
      this.baseParams.orderTypeArr = [];
      this.baseParams.urgencyLevel = "";
      const len = this.baseParams.businessType?.length || 0;
      if (len > 0) {
        api
          .queryOrderOptionsByBusiness({
            supportDept: this.baseParams.supportDept,
            businessTypeId: val[len - 1],
            permissionFlag: "0",
          })
          .then((res) => {
            this.orderTypeOptions = res.data?.map((x) => {
              return { ...x, disabled: true };
            });
          });
        this.getUrgencyOptions(
          {
            supportDept: this.baseParams.supportDept,
            businessType: val[0],
          },
          true
        );
      }
    },
    async getDetail() {
      if (!this.orderId && !this.addOrderId) return;
      this.pageLoading = true;
      const method = this.addOrderId ? "queryAddOrderBaseInfo" : "queryDetail";
      const res = await api[method]({
        orderId: this.orderId,
        addOrderId: this.addOrderId,
      }).catch(() => {
        this.pageLoading = false;
      });
      this.pageLoading = false;
      if (res?.code === "10000") {
        const {
          supportDept,
          oneBusinessTypeId,
          twoBusinessTypeId,
          threeBusinessTypeId,
          oneOrderTypeId,
          twoOrderTypeId,
          threeOrderTypeId,
          province,
          county,
          city,
          auditorId = "",
          stations = [],
        } = res.data;
        this.baseParams = { ...this.baseParams, ...res.data };
        if (!this.showMultiStation) {
          this.baseParams = {
            ...this.baseParams,
            ...stations[0],
            region: [
              stations[0]?.province,
              stations[0]?.city,
              stations[0]?.county,
            ],
          };
        }
        this.baseParams.region = [province, city, county];
        this.baseParams.businessType = [];
        this.baseParams.orderTypeArr = [];
        oneBusinessTypeId &&
          (this.baseParams.businessType[0] = oneBusinessTypeId);
        twoBusinessTypeId &&
          (this.baseParams.businessType[1] = twoBusinessTypeId);
        threeBusinessTypeId &&
          (this.baseParams.businessType[2] = threeBusinessTypeId);
        this.getBusinessOptions(supportDept);
        const len = this.baseParams.businessType?.length || 0;
        if (len > 0) {
          api
            .queryOrderOptionsByBusiness({
              supportDept: this.baseParams.supportDept,
              businessTypeId: this.baseParams.businessType[len - 1],
              permissionFlag: "0",
            })
            .then((res) => {
              this.orderTypeOptions = res.data?.map((x) => {
                return { ...x, disabled: true };
              });
            });
          this.getUrgencyOptions({
            supportDept: this.baseParams.supportDept,
            businessType: this.baseParams.businessType[0],
          });
        }
        oneOrderTypeId && (this.baseParams.orderTypeArr[0] = oneOrderTypeId);
        twoOrderTypeId && (this.baseParams.orderTypeArr[1] = twoOrderTypeId);
        threeOrderTypeId &&
          (this.baseParams.orderTypeArr[2] = threeOrderTypeId);
        this.formArr = res.data?.preDealForms?.map((x) => {
          return {
            ...x,
            formConfig: JSON.parse(x.formConfig),
            formJson: JSON.parse(x.formJson)?.map((item) => {
              this.handleFormRule(item);
              return item;
            }),
            dealFormData: {},
          };
        });
        console.log(this.baseParams, "params");
        // this.checkForm.auditorId = auditorId;
      }
    },

    getFormJson(val) {
      const len = val.length || 0;
      if (len > 0) {
        api.queryFormJson({ orderTypeId: val[len - 1] }).then((res) => {
          if (res.data) {
            this.formArr = res.data?.map((x) => {
              const { formConfig, formJson } = x;
              return {
                ...x,
                formConfig: JSON.parse(formConfig),
                formJson: JSON.parse(formJson)?.map((item) => {
                  this.handleFormRule(item);
                  return item;
                }),
                dealFormData: {},
              };
            });
            // const { formConfig, formJson } = res.data;
            // this.formConfig = JSON.parse(formConfig);
            // this.formJson = JSON.parse(formJson)?.map((item) => {
            //   this.handleFormRule(item);
            //   return item;
            // });
            // console.log(JSON.parse(formJson), this.formJson, "formJson");
            // console.log(this.formConfig, "formConfig");
          } else {
            this.formArr = [];
            // this.formConfig = {};
            // this.formJson = [];
          }
        });
      } else {
        this.formArr = [];
        // this.formConfig = {};
        // this.formJson = [];
      }
    },
    async checkDealForms() {
      let flag = true;
      if (this.formArr?.length > 0) {
        flag = false;
        for (let i = 0; i < this.dealFormData.length; i++) {
          await this.dealFormData[i].validate((valid) => {
            if (valid) {
              flag = true;
            } else {
              this.$message.warning("请填写完整表单信息!");
            }
          });
        }
      }
      return flag;
    },
    async submit(flag) {
      console.log(this.baseParams, "提交");
      // 表单提交以及审核通过需要校验填写表单的内容
      if (!(await this.checkDealForms())) {
        return;
      }
      this.$refs.baseForm.validate((valid) => {
        if (!valid) {
          return;
        }
        // this.$refs.checkForm?.validate(async (valid1) => {
        //   if (!valid1) return;
        this.$confirm(`是否确认${flag == 0 ? "保存" : "提交"}？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async (response) => {
          const { businessType, orderTypeArr, region } = this.baseParams;
          //取选中的工单类型中的数据
          const orderObj = this.flattenOrderTypeOptions?.find(
            (x) => x.id === orderTypeArr[orderTypeArr?.length - 1]
          );
          let params = {
            ...this.baseParams,
            stationName: this.stationOptions?.find(
              (x) => x.stationId === this.baseParams?.stationId
            )?.stationName,
            oneBusinessTypeId: businessType?.[0],
            twoBusinessTypeId: businessType?.[1],
            threeBusinessTypeId: businessType?.[2],
            oneOrderTypeId: orderTypeArr?.[0],
            twoOrderTypeId: orderTypeArr?.[1],
            threeOrderTypeId: orderTypeArr?.[2],
            province: region?.[0],
            city: region?.[1],
            county: region?.[2],
            companyName: this.companyOptions?.find(
              (x) => x.companyId === this.baseParams.companyId
            )?.companyName,
            // ...this.checkForm,
            // auditorName: this.userOption?.find(
            //   (x) => x.userId === this.checkForm.auditorId
            // )?.nickName,
            preDealForms: this.formArr?.map((x) => {
              return {
                ...x,
                formJson: JSON.stringify(x.formJson),
                formConfig: JSON.stringify(x.formConfig),
              };
            }),
            orderStatus: flag,
            orderId: this.orderId,
            flowKey: orderObj?.flowKey,
            handleTotalValidTime: orderObj?.handleTotalValidTime,
            orderType: this.type === "addOrder" ? 2 : 1,
            addOrderId: this.addOrderId,
          };
          //加单时设置主工单号，编辑加单时不需要
          if (this.type === "addOrder" && !this.addOrderId) {
            params.mainOrderNo = this.orderNo;
          }
          if (!this.showMultiStation) {
            params.stations = [
              {
                province: region?.[0],
                city: region?.[1],
                county: region?.[2],
                stationId: this.baseParams?.stationId,
                stationName: this.stationOptions?.find(
                  (x) => x.stationId === this.baseParams?.stationId
                )?.stationName,
                detailAddress: this.baseParams.detailAddress,
              },
            ];
          }
          console.log(this.flattenOrderTypeOptions, params);
          this.btnLoading = true;
          const res = await api.submitForm(params).catch(() => {
            this.btnLoading = false;
          });
          this.btnLoading = false;
          if (res?.code === "10000") {
            localStorage.setItem("supportDept", params.supportDept);
            this.$message.success("提交成功");
            this.$router.go(-1);
            this.$store.dispatch("tagsView/delView", this.$route);
          }
        });
      });
      // });
    },
    //获取业务类型
    getBusinessOptions(val) {
      // queryTreeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
      //   this.businessTypeOptions = res.data;
      // });
      api
        .queryBusinessByDept({ supportDept: val, permissionFlag: "0" })
        .then((res) => {
          this.businessTypeOptions = res.data;
        });
    },
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 下拉框下拉的框
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        // 增加滚动监听，
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          console.log(this.scrollHeight, this.scrollTop, this.clientHeight);
          const condition =
            this.scrollHeight - this.scrollTop - 2 <= this.clientHeight;
          // 当滚动条滚动到最底下的时候执行接口加载下一页
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
};
</script>

<style lang="less"></style>
