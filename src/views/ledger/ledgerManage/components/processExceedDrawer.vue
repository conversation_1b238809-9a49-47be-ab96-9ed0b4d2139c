//工单流程节点超时情况抽屉
<template>
  <el-drawer
    title="工单流程节点超时情况"
    :visible.sync="visible"
    :before-close="handleClose"
    size="80%"
  >
    <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
    <el-divider></el-divider>
    <div v-loading="loading">
      <div class="node-title">
        <div>节点耗时</div>
        <span
          >（总计需处理节点数{{ nodeObj.nodeCount }}个，已处理{{
            nodeObj.nodeDoneCount
          }}个节点，待处理{{ nodeObj.nodeTodoCount }}个节点）</span
        >
      </div>
      <NodeTimeline :list="nodeList"></NodeTimeline>
    </div>
  </el-drawer>
</template>

<script>
import NodeTimeline from "@/components/Timeline/nodeTimeline.vue";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";

import api from "@/api/ledger/index.js";

export default {
  components: { NodeTimeline, BaseDescriptions },
  data() {
    return {
      loading: false,
      visible: false,
      nodeList: [
        // {
        //   name: "节点1",
        //   user: "王二狗",
        //   time: "2021-08-01",
        //   haoshi: "1小时",
        //   shixiao: "6.89",
        // },
        // {
        //   name: "节点2",
        //   user: "王二狗",
        //   time: "2021-08-01",
        //   haoshi: "1小时",
        //   shixiao: "6.89",
        // },
      ],
      desObj: {},
      nodeObj: { nodeCount: "-", nodeTodoCount: "-", nodeDoneCount: "-" },
    };
  },
  computed: {
    baseList() {
      return [
        {
          title: "工单单号",
          value: this.desObj?.orderNo,
        },
        {
          title: "流程节点数",
          value: this.desObj?.totalNode,
        },
        {
          title: "工单类型",
          value: this.desObj?.orderTypeStr,
        },
        {
          title: "超时节点",
          value: this.desObj?.timeoutNodeCount,
        },
      ];
    },
  },
  methods: {
    handleClose() {
      this.visible = false;
    },
    open(row) {
      this.visible = true;
      this.desObj = { ...row };
      this.loading = true;
      api
        .queryProcessNode({ orderNo: row.orderNo, flowKey: row.flowKey })
        .then((res) => {
          this.loading = false;
          if (res?.code === "10000") {
            this.nodeObj = { ...this.nodeObj, ...res.data };
            this.nodeList = res.data?.processNodeTimeVos;
          }
        })
        .catch(() => {
          this.loading = false;
          this.nodeObj = {
            nodeCount: "-",
            nodeTodoCount: "-",
            nodeDoneCount: "-",
          };
          this.nodeList = [];
        });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-drawer__body {
  padding: 0 20px;
}
.node-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    color: #bbb;
    font-size: 12px;
  }
  div {
    font-weight: 700;
    font-size: 16px;
    color: rgb(16, 16, 16);
  }
}
</style>
