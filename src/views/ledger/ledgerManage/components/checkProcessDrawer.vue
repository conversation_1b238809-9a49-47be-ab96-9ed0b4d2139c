<!-- 审批进度抽屉 -->
<template>
  <el-drawer
    title="运营管理系统审批进度"
    :visible.sync="processVisible"
    @close="processVisible = false"
    size="70%"
  >
    <div class="tag-title" slot="title">
      运营管理系统审批进度
      <!-- <el-tag class="ml10">{{
        omApproveStatusName
      }}</el-tag> -->
    </div>
    <div class="info-title" type="flex">
      <div>申请单号：{{ applyNo }}</div>
    </div>
    <Timeline
      :list="processList"
      operateTypeTitle="itemCodeName"
      operatorNameTitle="operateEmpName"
      createTimeTitle="operateTime"
      operateDetailTitle="operateRemark"
    ></Timeline>
  </el-drawer>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import api from "@/api/ledger/index.js";
export default {
  components: { Timeline },
  data() {
    return {
      processVisible: false,
      processList: [],
      applyNo: "",
      omApproveStatusName: "",
    };
  },
  methods: {
    open(row) {
      this.processVisible = true;
      this.applyNo = row.applyNo;
      api.queryProcess({ applyNo: row.applyNo }).then((res) => {
        if (res?.code == 10000) {
          this.processList = res.data;
        }
      });
      this.omApproveStatusName = row.omApproveStatus;
    },
  },
};
</script>

<style lang="less" scoped>
.tag-title {
  // margin: 10px 40px 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.info-title {
  margin: 10px 40px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
