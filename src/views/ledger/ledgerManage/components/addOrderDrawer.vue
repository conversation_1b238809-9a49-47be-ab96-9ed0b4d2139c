<template>
  <el-drawer
    :title="type === 'add' ? '加单' : '修改加单'"
    :visible.sync="visible"
    :before-close="handleClose"
    size="70%"
  >
    <div class="drawer-body">
      <DynamicForm
        ref="dyForm"
        :config="dynamicConfig"
        :params="dynamicParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      >
        <template #workItems>
          <el-row v-for="(item, index) in dynamicParams.workItems" :key="index">
            <el-col :span="10">
              <el-form-item
                :prop="'workItems.' + index + '.workId'"
                :rules="{
                  required: true,
                  message: '加单工序项不能为空！',
                  trigger: 'change',
                }"
              >
                <el-select
                  v-model="item.workId"
                  placeholder="请选择加单工序项名称"
                  filterable
                  style="width: 100%;"
                  value-key="taskDefinitionKey"
                  @change="(val) => handleSelect(val, index)"
                >
                  <el-option
                    v-for="i in workItemOptions"
                    :key="i.taskDefinitionKey"
                    :label="i.nodeName"
                    :value="i.taskDefinitionKey"
                    :disabled="isOptionDisabled(i.taskDefinitionKey, index)"
                  />
                </el-select>
              </el-form-item>
              <div v-if="item.workId" class="work-item-info">
                该工序项前有效加单次数
                {{ item.effectiveAddTimeCount || 0 }}，加单累计时长
                {{ item.effectiveAddTime || 0 }} h
              </div>
            </el-col>
            <el-col :span="10" :offset="1">
              <el-form-item
                :prop="
                  'workItems.' +
                    index +
                    (orderAttr !== '02' ? '.addTimes' : '.validTimeMinute')
                "
                :rules="{
                  required: true,
                  trigger: 'change',
                  message: '不能为空',
                }"
              >
                <template v-if="orderAttr !== '02'">
                  <!-- 标准工单 -->

                  <span class="mr10"
                    >标准工时: {{ item.validTime || 0 }} h</span
                  >
                  <el-input-number
                    v-model="item.addTimes"
                    :min="1"
                    :max="99"
                    :precision="0"
                    :step="1"
                  >
                  </el-input-number>
                  次
                </template>
                <template v-else>
                  <!-- 非标工单 -->
                  <el-input-number
                    v-model="item.validTimeMinute"
                    :min="1"
                    :precision="3"
                    :step="1"
                  >
                  </el-input-number>
                  分钟
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button
                type="primary"
                circle
                icon="el-icon-plus"
                @click="addItem"
                v-if="index === 0"
                style="margin-bottom: 18px;"
                :disabled="workItemOptionsLen <= dynamicParams.workItems.length"
              />
              <el-button
                type="primary"
                circle
                icon="el-icon-minus"
                style="background:red;border:1px solid red;margin-bottom: 18px;"
                @click="removeItem(index)"
                v-else
              />
            </el-col>
          </el-row>
        </template>
      </DynamicForm>
    </div>
    <div class="drawer-footer">
      <el-button @click.stop="handleClose" size="medium" :loading="btnLoading"
        >取 消</el-button
      >
      <el-button
        @click="handleSubmit"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >确 定</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import { listAllUser } from "@/api/common";
import { initParams } from "@/utils/buse";
import api from "@/api/ledger/index.js";

export default {
  data() {
    return {
      type: "add",
      visible: false,
      btnLoading: false,
      dynamicParams: {},
      userOption: [],
      orderNo: "",
      orderAttr: "01", // 默认为标准工单
      workItemOptions: [],
      workItemOptionsLen: 0,
    };
  },
  computed: {
    selectedWorkItems() {
      return this.dynamicParams.workItems
        .map((item) => item.workId)
        .filter(Boolean); // 过滤掉空值
    },
    dynamicConfig() {
      return [
        {
          field: "reason",
          title: "加单原因",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入具体的原因描述，500个字符以内",
          },
          rules: [{ required: true, message: "加单原因不能为空" }],
        },
        {
          field: "auditUser",
          title: "选择审核人",
          element: "el-select",
          props: {
            placeholder: "请输入或选择人员姓名",
            filterable: true,
            options: this.userOption,
          },
        },
        {
          field: "notifyType",
          element: "el-checkbox-group",
          title: "通知方式",
          props: {
            options: [
              { label: "钉钉", value: "1" },
              { label: "短信", value: "2" },
            ],
          },
          // rules: [
          //   {
          //     required: this.dynamicParams?.auditUser,
          //     message: "请选择通知方式",
          //   },
          // ],
        },
        {
          field: "workItems",
          element: "slot",
          slotName: "workItems",
          title: "加单工序项",
          rules: [{ required: true, message: "加单工序项不能为空！" }],
          defaultValue: [
            { workId: undefined, addTimes: 1, validTimeMinute: 1 },
          ],
        },
      ];
    },
  },
  created() {
    this.getListUser();
  },
  methods: {
    // 检查选项是否已被选中（排除当前操作的选项）
    isOptionDisabled(taskDefinitionKey, currentIndex) {
      // 当前选项的 workId
      const currentWorkId = this.dynamicParams.workItems[currentIndex].workId;
      // 如果当前选项的 workId 和传入的 taskDefinitionKey 相同，不禁用
      if (taskDefinitionKey === currentWorkId) {
        return false;
      }
      // 否则检查是否在其他下拉列表中被选中
      return this.selectedWorkItems.includes(taskDefinitionKey);
    },
    handleSelect(val, index) {
      // 找到选中的工序项
      const selectedWorkItem = this.workItemOptions?.find(
        (x) => x.taskDefinitionKey === val
      );

      // 加单工序项时效为空时加个提示
      if (!selectedWorkItem?.validTime) {
        this.$message.warning("该工序项工时为空！");
      }

      // 更新工序项的相关数据
      if (selectedWorkItem && index !== undefined) {
        // 单独设置每个属性，而不是替换整个对象
        this.$set(this.dynamicParams.workItems[index], "workId", val);
        this.$set(
          this.dynamicParams.workItems[index],
          "validTime",
          selectedWorkItem.validTime || 0
        );
        this.$set(
          this.dynamicParams.workItems[index],
          "effectiveAddTimeCount",
          selectedWorkItem.effectiveAddTimeCount || 0
        );
        this.$set(
          this.dynamicParams.workItems[index],
          "effectiveAddTime",
          selectedWorkItem.effectiveAddTime || 0
        );
      }
    },
    getProcessOptions() {
      api.queryProcessOptions({ orderNo: this.orderNo }).then((res) => {
        if (res?.code === "10000") {
          this.workItemOptions = res.data;
          this.workItemOptionsLen = this.workItemOptions?.length || 0;
        }
      });
    },
    addItem() {
      // 创建新的工序项
      const newItem = {
        workId: undefined,
        addTimes: 1,
        validTimeMinute: 1,
        validTime: 0,
        effectiveAddTimeCount: 0,
        effectiveAddTime: 0,
      };

      // 获取当前工序项数组的副本
      const workItems = [...this.dynamicParams.workItems];

      // 添加新项
      workItems.push(newItem);

      // 使用$set更新整个数组
      this.$set(this.dynamicParams, "workItems", workItems);
    },
    removeItem(index) {
      // 获取当前工序项数组的副本
      const workItems = [...this.dynamicParams.workItems];

      // 移除指定索引的项
      workItems.splice(index, 1);

      // 使用$set更新整个数组
      this.$set(this.dynamicParams, "workItems", workItems);
    },
    open(row, type = "add") {
      this.visible = true;
      this.orderNo = row.orderNo;
      this.type = type;
      // 设置工单类型（标准/非标）
      this.orderAttr = row.orderAttr || "01";
      this.dynamicParams = { ...row, ...initParams(this.dynamicConfig) };

      if (type === "edit") {
        api
          .checkInfo({
            orderNo: this.orderNo,
            ledgerAddOrderId: row.ledgerAddOrderId,
          })
          .then((res) => {
            if (res?.code === "10000") {
              const { orderNodeList, notifyType, ...rest } = res.data;

              // 先设置基本属性
              Object.keys(rest).forEach((key) => {
                this.$set(this.dynamicParams, key, rest[key]);
              });

              // 设置通知类型
              this.$set(
                this.dynamicParams,
                "notifyType",
                notifyType?.split(",") || []
              );

              // 设置工序项
              const workItems =
                orderNodeList?.map((x) => {
                  return {
                    workId: x.taskDefinitionKey,
                    addTimes: x.addTimes,
                    validTimeMinute: x.validTimeMinute,
                    validTime: x.validTime || 0,
                    effectiveAddTimeCount: x.effectiveAddTimeCount || 0,
                    effectiveAddTime: x.effectiveAddTime || 0,
                  };
                }) || [];

              this.$set(this.dynamicParams, "workItems", workItems);
            }
          });
      } else {
        // 初始化一个空的工序项
        const initialWorkItem = {
          workId: undefined,
          addTimes: 1,
          validTimeMinute: 1,
          validTime: 0,
          effectiveAddTimeCount: 0,
          effectiveAddTime: 0,
        };

        this.$set(this.dynamicParams, "workItems", [initialWorkItem]);
      }
      this.getProcessOptions();
      console.log("workItems", this.dynamicParams.workItems);
    },
    handleSubmit() {
      this.$refs.dyForm.validate((valid) => {
        if (!valid) return;
        const text = this.type === "add" ? "加单" : "保存";
        this.$confirm(`是否确认${text}？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const {
            workItems,
            notifyType,
            auditResult,
            ...rest
          } = this.dynamicParams;

          // 处理工序项数据
          const arr = [];
          this.workItemOptions?.forEach((x) => {
            const obj = workItems?.find(
              (i) => i.workId === x.taskDefinitionKey
            );
            obj && arr.push({ ...x, ...obj });
          });
          const params = {
            ...rest,
            orderNodeList: arr,
            orderNo: this.orderNo,
            orderAttr: this.orderAttr, // 添加工单类型
            notifyType: notifyType?.join(","),
          };
          this.btnLoading = true;
          const method = this.type === "add" ? "addOrder" : "editAddOrder";
          api[method](params)
            .then((res) => {
              this.btnLoading = false;
              if (res?.code === "10000") {
                this.$message.success("提交成功");
                this.$emit("success");
                this.handleClose();
              }
            })
            .catch(() => {
              this.btnLoading = false;
            });
        });
      });
    },
    handleClose() {
      this.visible = false;
    }, //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
        ledgerEnableFlag: 0,
      };
      const { code, data } = await api.addUserList(params);
      if (code != 10000) return;
      this.userOption = data?.map((x) => {
        return {
          ...x,
          value: x.userId,
          label:
            x.userName +
            "-" +
            x.nickName +
            (x.phonenumber ? "-" + x.phonenumber : ""),
        };
      });
    },
  },
};
</script>

<style lang="less" scoped>
.work-item-info {
  font-size: 12px;
  color: #666;
  margin-top: -10px;
  margin-bottom: 10px;
}
</style>
