<template>
  <el-drawer
    title="模糊搜索"
    :visible.sync="visible"
    :before-close="handleClose"
    size="50%"
  >
    <div class="drawer-body">
      <el-form>
        <el-row v-for="(item, index) in searchList" :key="index">
          <el-col :span="20">
            <el-form-item
              :prop="'searchList.' + index + '.name'"
              :rules="{
                required: false,
                message: '请选择',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.name"
                placeholder="请输入"
                clearable
                style="width: 100%;"
              >
              </el-input> </el-form-item
          ></el-col>
          <el-col :span="3" :offset="1">
            <el-button
              type="primary"
              circle
              icon="el-icon-plus"
              @click="addItem"
              v-if="index === 0"
              style="margin-bottom: 18px;"
            />
            <el-button
              type="primary"
              circle
              icon="el-icon-minus"
              style="background:red;border:1px solid red;margin-bottom: 18px;"
              @click="removeItem(index)"
              v-else
            />
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button @click.stop="handleReset" size="medium" :loading="btnLoading"
        >重 置</el-button
      >
      <el-button
        @click="handleSubmit"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >查 询</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import { listAllUser } from "@/api/common";
import { initParams } from "@/utils/buse";
import api from "@/api/ledger/index.js";

export default {
  data() {
    return {
      visible: false,
      btnLoading: false,
      searchList: [{ name: undefined }],
    };
  },
  computed: {},
  created() {},
  methods: {
    addItem() {
      this.searchList.push({ name: undefined });
    },
    removeItem(index) {
      this.searchList.splice(index, 1);
    },
    open(params) {
      this.visible = true;
      this.searchList = params.obscureParamsList || [{ name: undefined }];
    },
    handleSubmit() {
      console.log(this.searchList, "search");
      this.$emit("search", this.searchList);
      this.handleClose();
    },
    handleReset() {
      this.$emit("search", [{ name: undefined }]);
      this.handleClose();
    },
    handleClose() {
      this.visible = false;
    },
  },
};
</script>

<style></style>
