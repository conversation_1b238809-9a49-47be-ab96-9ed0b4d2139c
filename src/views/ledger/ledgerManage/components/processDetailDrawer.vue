//流程处理详情抽屉
<template>
  <el-drawer
    title="工单流程节点处理详情"
    :visible.sync="visible"
    :before-close="handleClose"
    size="80%"
  >
    <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
    <el-divider></el-divider>
    <el-card>
      <el-steps :space="300" align-center>
        <el-step
          v-for="(item, index) in stepsList"
          :key="index"
          :title="item.nodeName"
          :status="item.handleFlag"
          @click.native="handleClick(item, index)"
          :style="{
            cursor: item.handleFlag === 'wait' ? 'not-allowed' : 'pointer',
          }"
          :class="item.checked ? 'step-checked' : ''"
        >
          <div slot="description">
            <div>{{ item.assignee }}</div>
            <div v-if="item.handleFlag === 'success'">{{ item.endTime }}</div>
            <div
              v-else-if="item.handleFlag === 'process'"
              style="text-wrap: nowrap;"
            >
              应处理时间：{{ item.planEndTime }}
            </div>
          </div>
        </el-step>
      </el-steps>
    </el-card>
    <BaseInfo ref="base" v-if="isFirstNode" onlyShowBase></BaseInfo>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>流程节点表单信息项</span>
      </div>
      <div v-for="(item, index) in formArr" :key="index">
        <FormCreatePreview :formData="item.formJson"></FormCreatePreview>
      </div>
      <!-- <div v-for="(item, index) in formArr" :key="index">
        <form-create
          :rule="item.formJson"
          v-model="dealFormData[index]"
          :option="item.formConfig"
        />
        <div v-if="item.formJson.showfileName">
          <div style="font-size:14px;margin:10px 0;">
            已上传的文件名称：
          </div>
          <div v-for="(j, i) in item.formJson" :key="i">
            <div v-if="j.fileList && j.fileList.length > 0">
              <div v-for="(f, fi) in j.fileList" :key="fi">
                {{ f.name }}
                <el-button type="text" size="small" @click="handleDownload(f)"
                  >下载</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div> -->
    </el-card>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import formCreateMixin from "@/mixin/formCreate.js";
import FormCreatePreview from "@/components/formCreatePreview/index.vue";

import api from "@/api/ledger/index.js";
import BaseInfo from "../detailComponents/baseInfo.vue";

export default {
  mixins: [formCreateMixin],
  components: { BaseDescriptions, FormCreatePreview, BaseInfo },
  data() {
    return {
      isFirstNode: false,
      orderNo: "",
      orderId: "",
      formArr: [],
      dealFormData: [],
      visible: false,
      stepsList: [
        // {
        //   name: "创建工单",
        //   status: "success",
        //   createByName: "小小",
        //   createTime: "2022-05-05",
        //   id: "1",
        // },
        // {
        //   name: "节点名称1",
        //   status: "process",
        //   createByName: "笑笑",
        //   createTime: "2022-05-05",
        //   shouldTime: "2022-05-05",
        //   checked: true,
        //   id: "2",
        // },
        // {
        //   name: "节点名称1",
        //   status: "wait",
        //   createByName: "笑笑",
        //   createTime: "2022-05-05",
        //   checked: false,
        //   id: "3",
        // },
      ],
      desObj: {},
    };
  },
  computed: {
    checkedData() {
      return this.stepsList?.find((item) => item.checked === true);
    },
    baseList() {
      return [
        {
          title: "工单单号",
          value: this.desObj?.orderNo,
        },
        {
          title: "工单状态",
          value: this.desObj?.orderStatusName,
        },
        {
          title: "工单类型",
          value: this.desObj?.orderTypeStr,
        },
        {
          title: "当前处理人",
          value: this.desObj?.handleUserName,
        },
      ];
    },
  },
  methods: {
    handleClose() {
      this.visible = false;
    },
    handleClick(item, index) {
      if (item.handleFlag === "wait") return;

      this.stepsList?.forEach((x, i) => {
        x.checked = false;
        if (i == index) {
          x.checked = true;
        }
      });
      this.getHandleForm();
      this.isFirstNode = false;
      if (index === 0) {
        this.isFirstNode = true;
        this.$nextTick(() => {
          this.$refs.base?.getDetail({
            orderId: this.orderId,
            orderNo: this.orderNo,
          });
        });
      }
    },
    open(row) {
      this.isFirstNode = false;
      this.visible = true;
      this.desObj = { ...row };
      this.orderNo = row.orderNo;
      this.orderId = row.orderId;
      api
        .queryHandleSteps({ orderNo: row.orderNo })
        .then((res) => {
          if (res?.code === "10000") {
            this.stepsList = res.data?.map((x) => {
              return { ...x, checked: false };
            });
            //取最后一个非wait的节点设为checked
            for (let i = this.stepsList.length - 1; i >= 0; i--) {
              if (this.stepsList[i].handleFlag !== "wait") {
                this.stepsList[i].checked = true;
                break; // Exit the loop after setting the first found task
              }
            }
            this.getHandleForm();
          }
        })
        .catch(() => {
          this.stepsList = [];
          this.formArr = [];
        });
    },
    getHandleForm() {
      if (this.checkedData) {
        api
          .getFormJsonByNode({
            orderNo: this.orderNo,
            taskId: this.checkedData.taskId,
            handleFlag: this.checkedData.handleFlag,
          })
          .then((res) => {
            if (res?.code === "10000") {
              this.formArr = res.data?.dealForms?.map((x) => {
                return {
                  ...x,
                  formConfig: JSON.parse(x.formConfig),
                  formJson: JSON.parse(x.formJson)?.map((item) => {
                    this.handlePreviewFormRule(item);
                    return item;
                  }),
                  dealFormData: {},
                };
              });
            }
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-drawer__body {
  padding: 0 20px;
}
.node-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span {
    color: #bbb;
    font-size: 12px;
  }
  div {
    font-weight: 700;
    font-size: 16px;
    color: rgb(16, 16, 16);
  }
}
.step-checked {
  /deep/ .el-step__icon.is-text {
    background: #029c7c;
    color: #fff;
    border-color: #029c7c;
  }
}
/deep/ .el-steps {
  justify-content: center;
}
</style>
