<template>
  <el-drawer
    title="审核加单"
    :visible.sync="visible"
    :before-close="handleClose"
    size="50%"
  >
    <div class="drawer-body">
      <el-card>
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>基本信息</span>
        </div>
        <DynamicForm
          ref="dyForm"
          :config="dynamicConfig"
          :params="dynamicParams"
          :defaultColSpan="24"
          labelPosition="right"
          labelWidth="150px"
          preview
        >
          <template #orderNodeList>
            <vxe-grid
              :columns="columns"
              :data="dynamicParams.orderNodeList"
              resizable
              align="center"
            ></vxe-grid>
          </template>
        </DynamicForm>
      </el-card>
      <el-card>
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>审核结果</span>
        </div>
        <DynamicForm
          ref="checkForm"
          :config="checkConfig"
          :params="checkParams"
          :defaultColSpan="24"
          labelPosition="right"
          labelWidth="150px"
        >
        </DynamicForm>
      </el-card>
    </div>
    <div class="drawer-footer">
      <el-button @click.stop="submit(2)" size="medium" :loading="btnLoading"
        >审核不通过</el-button
      >
      <el-button
        @click="submit(1)"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >审核通过</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import { listAllUser } from "@/api/common";
import { initParams } from "@/utils/buse";
import api from "@/api/ledger/index.js";
import { copyToClipboard } from "@/utils/index.js";
export default {
  data() {
    return {
      visible: false,
      btnLoading: false,
      dynamicParams: {},
      checkParams: {},
      userOption: [],
      columns: [
        { title: "节点名称", field: "nodeName" },
        { title: "加单次数", field: "addTimes" },
        { title: "节点标准工时(h)", field: "validTime" },
      ],
      orderNo: "",
      ledgerAddOrderId: "",
    };
  },
  computed: {
    checkConfig() {
      return [
        {
          field: "auditReason",
          title: "审核不通过原因：",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入具体的原因描述，500个字符以内",
          },
        },
      ];
    },
    dynamicConfig() {
      return [
        {
          field: "orderNo",
          title: "工单编码：",
          previewFormatter: (val) => {
            return (
              <div>
                <span>{val}</span>
                <i
                  class="el-icon-document-copy pointer-icon"
                  on={{ click: () => copyToClipboard(val) }}
                ></i>
              </div>
            );
          },
        },
        {
          field: "orderStatus",
          title: "工单状态：",
        },
        {
          field: "orderTypeStr",
          title: "工单类型：",
        },
        {
          field: "reason",
          title: "加单原因：",
        },
        {
          field: "orderNodeList",
          title: "加单工序：",
          element: "slot",
          previewSlot: "orderNodeList",
          defaultValue: [],
        },
        {
          field: "timeAmount",
          title: "总计所需增加工时：",
        },
        {
          field: "operateUserName",
          title: "申请人：",
        },
        {
          field: "applyTime",
          title: "申请时间：",
        },
      ];
    },
  },
  created() {
    this.getListUser();
    // this.getProcessOptions();
  },
  methods: {
    getProcessOptions() {
      api.queryProcessOptions({}).then((res) => {
        if (res?.code === "10000") {
          this.workItemOptions = res.data;
        }
      });
    },
    open(row) {
      this.visible = true;
      this.orderNo = row.orderNo;
      this.ledgerAddOrderId = row.ledgerAddOrderId;
      this.dynamicParams = { ...initParams(this.dynamicConfig), ...row };
      this.checkParams = initParams(this.checkConfig);
      this.getCheckInfo();
    },
    getCheckInfo() {
      api
        .checkInfo({
          orderNo: this.orderNo,
          ledgerAddOrderId: this.ledgerAddOrderId,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.dynamicParams = { ...this.dynamicParams, ...res.data };
          }
        });
    },
    submit(flag) {
      if (flag == 2 && !this.checkParams.auditReason) {
        this.$message.warning("审核不通过原因不能为空！");
        return;
      }
      const text = flag == 2 ? "审核不通过" : "审核通过";
      this.$confirm(`是否确认要${text}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const params = {
          ...this.dynamicParams,
          ...this.checkParams,
          auditResult: flag,
        };
        this.btnLoading = true;
        api
          .checkAddOrder(params)
          .then((res) => {
            this.btnLoading = false;
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              this.$emit("success");
              this.handleClose();
            }
          })
          .catch(() => {
            this.btnLoading = false;
          });
      });
    },
    handleClose() {
      this.visible = false;
    }, //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      this.userOption = data?.map((x) => {
        return {
          ...x,
          value: x.userId,
          label: x.userName + "-" + x.nickName + "-" + (x.phonenumber || ""),
        };
      });
    },
    copyToClipboard(val) {
      // 创建一个textarea元素
      const textarea = document.createElement("textarea");
      // 设置textarea的值为要复制的文本
      textarea.value = val;
      // 将textarea添加到文档中
      document.body.appendChild(textarea);
      // 选中textarea中的文本
      textarea.select();
      try {
        // 尝试复制选中的文本
        const successful = document.execCommand("copy");
        const msg = successful ? "复制成功" : "复制失败";
        console.log(msg);
        this.$message.success(msg);
      } catch (err) {
        console.log("不能使用这种方法复制", err);
      }
      // 将textarea从文档中移除
      document.body.removeChild(textarea);
    },
  },
};
</script>

<style></style>
