<template>
  <el-dialog
    title="批量导入工单"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleBatchCancel"
  >
    <el-form :model="batchForm" ref="batchForm" label-width="140px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="下载模版:" prop="file">
            <el-link type="primary" @click="downExcel">点击下载</el-link>
          </el-form-item></el-col
        >
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="上传文件:" prop="file">
            <el-upload
              ref="upload"
              :limit="1"
              accept=".xlsx, .xls"
              :headers="upload.headers"
              :action="upload.url"
              :disabled="upload.isUploading"
              :on-success="handleFileSuccess"
              :on-error="handleFileError"
              :on-change="handleChangeFile"
              :auto-upload="false"
              :data="extraData"
            >
              <el-button>选择文件</el-button>
              <div slot="tip" class="el-upload__tip">
                上传格式支持xlxs、xls文件，500m以内。
              </div>
            </el-upload>
          </el-form-item></el-col
        >
      </el-row>
      <slot name="extraForm" :params="extraData"></slot>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        @click="handleBatchCancel"
        size="small"
        :loading="submitLoading"
        >取 消
      </el-button>
      <el-button
        type="primary"
        @click="handleBatchSubmit"
        size="small"
        :loading="submitLoading"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  props: {
    //上传时附带的额外参数
    extraData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      visible: false,
      submitLoading: false,
      batchForm: {
        file: [],
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/ledger/order/import",
        updateAsCode: "",
      },
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    handleFileSuccess(response) {
      this.submitLoading = false;
      console.log("response===", response);
      if (!response.success) {
        this.$confirm(response.message, "导入失败！", {
          confirmButtonText: "重新上传",
          cancelButtonText: "取消",
          type: "error",
          center: true,
          dangerouslyUseHTMLString: true,
        })
          .then(() => {
            this.batchForm.file = [];
            this.$refs.upload.clearFiles();
          })
          .catch(() => {
            this.handleBatchCancel();
          });
      } else {
        this.handleBatchCancel();
        this.$alert("导入成功", "导入结果", {
          type: "success",
          confirmButtonText: "我知道了",
          callback: () => {
            this.$emit("uploadSuccess");
          },
        });
      }
    },
    handleFileError(response) {
      this.submitLoading = false;
      this.$confirm(response.message, "导入失败！", {
        confirmButtonText: "重新上传",
        cancelButtonText: "取消",
        type: "error",
        center: true,
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          this.batchForm.file = [];
          this.$refs.upload.clearFiles();
        })
        .catch(() => {
          this.handleBatchCancel();
        });
    },
    handleChangeFile(file, fileList) {
      console.log(file, fileList);
      this.batchForm.file = fileList || [];
    },
    downExcel() {
      window.location.href =
        "/charging-maintenance-ui/static/工单台账导入模板.xlsx";
    },
    //批量配置-提交
    handleBatchSubmit() {
      console.log("extraData", this.extraData);
      console.log(this.batchForm.file, "提交");
      if (this.batchForm.file?.length === 0) {
        this.$message.error("请上传文件！");
        return;
      }
      if (this.batchForm.file[0].size / 1024 / 1024 > 500) {
        this.$message.error("上传的文件大小不能超过500m!");
        return;
      }
      if (!this.extraData.orderStatus) {
        this.$message.error("请选择数据处理方式！");
        return;
      }
      if (this.extraData.orderStatus == "4" && !this.extraData.finishRemark) {
        this.$message.error("选择完结时，完结原因必填!");
        return;
      }
      this.submitLoading = true;
      this.$refs.upload.submit();
    },

    handleBatchCancel() {
      this.visible = false;
      this.$refs.batchForm.resetFields();
      this.batchForm.file = [];
      this.$refs.upload.clearFiles();
    },
  },
};
</script>

<style></style>
