<template>
  <el-form :model="form" ref="baseForm">
    <div class="operation-wrap" v-if="showAddBtn">
      <el-button type="text" size="mini" @click="handleAdd" icon="el-icon-plus">
        新增
      </el-button>
      <el-button
        type="text"
        size="mini"
        @click="handleMinus"
        icon="el-icon-minus"
        style="color:red"
      >
        删除
      </el-button>
    </div>
    <vxe-table
      :data="form.tableData"
      resizable
      align="center"
      border
      ref="xTable"
      max-height="500px"
    >
      <vxe-column v-bind="item" v-for="(item, index) in columns" :key="index">
        <template #header="{ column }" v-if="!item.type">
          <span class="required-star" v-if="isRequired(item)">*</span
          >{{ column.title }}
        </template>
        <template #default="{ row,$rowIndex }" v-if="item.isEdit">
          <el-form-item
            :prop="'tableData[' + $rowIndex + '].' + item.field"
            :rules="item.rules"
            :size="item.itemProps ? item.itemProps.size || 'mini' : 'mini'"
            v-bind="item.itemProps"
          >
            <component
              :is="item.element || 'el-input'"
              v-model="row[item.field]"
              v-bind="{ ...item.props, ...item.attrs }"
              v-on="getProxyEvents(item, row, $rowIndex)"
            >
              <template
                v-if="
                  item.element === 'el-select' &&
                    item.props &&
                    item.props.options
                "
              >
                <el-option
                  v-for="(i, j) in item.props.options"
                  :key="j"
                  :label="i[item.props.optionLabel || 'label']"
                  :value="i[item.props.optionValue || 'value']"
              /></template>
            </component>
          </el-form-item>
        </template>
      </vxe-column>
    </vxe-table>
  </el-form>
</template>

<script>
import { initParams } from "@/utils/buse.js";

export default {
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Array,
      default: () => [],
    },
    showAddBtn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: {
        tableData: this.value,
      },
    };
  },
  watch: {
    value(newValue) {
      this.form.tableData = newValue;
    },
    "form.tableData"(newVal) {
      this.$emit("input", newVal);
    },
  },
  methods: {
    handleProxyEvent(handlerName, item, row, rowIndex) {
      console.log("handleProxyEvent", handlerName, item, row, rowIndex);
      return (...args) => {
        if (typeof item.on?.[handlerName] === "function") {
          // 将原始事件参数与自定义参数合并
          item.on[handlerName](...args, item, row, rowIndex);
        }
      };
    },
    getProxyEvents(item, row, rowIndex) {
      return Object.keys(item.on || {}).reduce((res, eventName) => {
        res[eventName] = this.handleProxyEvent(eventName, item, row, rowIndex);
        return res;
      }, {});
    },
    handleMinus() {
      const selected = this.$refs.xTable.getCheckboxRecords(true);
      if (!selected?.length) {
        this.$message.warning("请勾选要删除的行");
        return;
      }
      this.form.tableData = this.form.tableData.filter(
        (x) => !selected.includes(x)
      );
    },
    handleAdd() {
      this.form.tableData.push({ ...initParams(this.columns) });
    },
    isRequired(item) {
      const flag = item.rules?.find((x) => x.required);
      return flag ? true : false;
    },
    // // 统一处理字段变化
    // handleFieldChange(column, value, row, rowIndex) {
    //   console.log("handleFieldChange");
    //   // 执行列配置中的自定义事件
    //   if (typeof column.on?.change === "function") {
    //     column.on.change(value, row, rowIndex);
    //   }
    // },
    async validate() {
      const valid = await this.$refs.baseForm.validate();
      if (!valid) {
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="less" scoped>
.operation-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
/deep/ .el-form-item {
  margin-bottom: 0 !important;
}
/deep/ .el-form-item__error {
  line-height: 4px !important;
}
/deep/ .vxe-body--row {
  height: 60px;
}
.required-star {
  color: red;
  margin-right: 3px;
  font-weight: bold;
}
</style>
