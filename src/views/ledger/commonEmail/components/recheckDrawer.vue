<!-- 该页面已废弃 -->

<template>
  <el-drawer
    title="活动复核结果"
    :visible.sync="drawerVisible"
    size="80%"
    :destroy-on-close="false"
    :before-close="handleBeforeClose"
  >
    <!-- 使用keep-alive缓存内容，避免重复渲染 -->
    <keep-alive>
      <div v-if="drawerVisible" v-loading="loading">
        <DynamicForm
          ref="baseForm"
          :config="config"
          :params="formParams"
          labelPosition="right"
          :defaultColSpan="24"
          labelWidth="150px"
        >
          <template #stationTable>
            <!-- <StationConfigTable
              :columns="stationColumns"
              v-model="formParams.stationReviewDetails"
              ref="stationTable"
              showAddBtn
            ></StationConfigTable> -->
            <LuckySheet
              v-model="formParams.stationReviewDetails"
              :containerId="'luckysheet-config-' + uniqueId"
              ref="luckySheet"
            />
          </template>
          <template #activityType>
            <div style="display: flex; align-items: center;">
              <el-select
                v-model="formParams.activityType"
                placeholder="请选择活动类型"
                style="width: 200px;"
                filterable
                clearable
              >
                <el-option
                  v-for="item in activityTypeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
              <div
                style="display: flex; align-items: center; margin-left: 10px;"
              >
                <el-input-number
                  v-model="formParams.activityCount"
                  :min="0"
                  :precision="0"
                  controls-position="right"
                  style="width: 120px;"
                ></el-input-number>
                <span style="margin-left: 5px;">次</span>
              </div>
            </div>
          </template>
        </DynamicForm>
        <BaseFormModal
          ref="formModal"
          :modalTitle="modalConfig.modalTitle"
          :config="modalConfig.formConfig"
          @modalConfirm="modalConfirmHandler"
          modalWidth="50%"
          labelWidth="120px"
        ></BaseFormModal>
        <div class="drawer-btn">
          <el-button @click.stop="handleClose" size="medium">取 消</el-button>
          <el-button @click.stop="handleReject" size="medium"
            >驳回到配置</el-button
          >
          <el-button @click="handleSubmit" type="primary" size="medium"
            >确 定</el-button
          >
        </div>
      </div>
    </keep-alive>
  </el-drawer>
</template>

<script>
import api from "@/api/ledger/commonEmail.js";
import { queryCityTree } from "@/api/common.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import { initParams } from "@/utils/buse.js";
import LuckySheet from "@/components/LuckySheet/index.vue";
import StationConfigTable from "./stationConfigTable.vue";
export default {
  components: { StationConfigTable, BaseFormModal, LuckySheet },
  data() {
    return {
      mailDataId: "",
      differenceTypeOptions: [],
      formParams: {},
      drawerVisible: false,
      operatorOptions: [],
      regionData: [],
      loading: false,
      activityTypeOptions: [],
      // 添加唯一ID，确保每个LuckySheet实例有唯一的containerId
      uniqueId: Date.now() + Math.floor(Math.random() * 1000),
      // 添加标记，用于跟踪抽屉是否已初始化
      isInitialized: false,
    };
  },
  computed: {
    stationColumns() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          title: "场站名称",
          field: "stationName",
          width: 150,
          isEdit: true,
          element: "page-autocomplete",
          rules: [{ required: true, trigger: "change", message: "不能为空" }],
          props: {
            clearable: true,
            optionValue: "stationName",
            optionLabel: "stationName",
            fetchMethod: (params) => {
              return this.remoteStationOptions(params, "stationName");
            },
          },
          attrs: {
            maxlength: 100,
          },
          on: {
            change: (value, { options }, item, row, rowIndex) => {
              console.log("change", value, options);
              const stationCode = options?.find((x) => x.stationName == value)
                ?.stationCode;
              this.$set(row, "stationCode", stationCode);
            },
          },
        },
        {
          title: "场站编码",
          field: "stationCode",
          width: 150,
          isEdit: true,
          element: "page-autocomplete",
          attrs: {
            maxlength: 100,
          },
          props: {
            clearable: true,
            optionValue: "stationCode",
            optionLabel: "stationCode",
            fetchMethod: (params) => {
              return this.remoteStationOptions(params, "stationCode");
            },
          },
        },
        {
          title: "活动日期",
          field: "activityDate",
          width: 500,
          isEdit: true,
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: "活动生效日期",
            endPlaceholder: "活动结束日期",
          },
          rules: [
            {
              required: true,
              trigger: "change",
              message: "不能为空",
            },
          ],
        },
        // {
        //   title: "活动结束日期",
        //   field: "activityEndDate",
        //   width: 250,
        //   isEdit: true,
        //   element: "el-date-picker",
        //   props: {
        //     valueFormat: "yyyy-MM-dd 23:59:59",
        //     placeholder: "请选择活动结束日期",
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       trigger: "change",
        //       message: "不能为空",
        //     },
        //   ],
        // },
        {
          title: "结算比例（%）",
          field: "settlementRatio",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
          rules: [
            {
              required: true,
              trigger: "change",
              message: "不能为空",
            },
          ],
        },
        {
          title: "分润配置（分润收入）（%）",
          field: "profitSharingIncome",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
        },
        {
          title: "购电配置（X折结算）（%）",
          field: "purchaseElectricityDiscount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
        },
        {
          title: "长协（通用分润）（%）",
          field: "longTermAgreement",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
          rules: [
            {
              required: true,
              trigger: "change",
              message: "不能为空",
            },
          ],
        },
        {
          title: "短协（活动分润）（%）",
          field: "shortTermAgreement",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
        },
        {
          title: "开始结束时间",
          field: "recordTime",
          width: 500,
          isEdit: true,
          element: "el-date-picker",
          props: {
            type: "datetimerange",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            startPlaceholder: "开始时间",
            endPlaceholder: "结束时间",
          },
        },
        // {
        //   title: "结束时间",
        //   field: "recordEndTime",
        //   width: 250,
        //   isEdit: true,
        //   element: "el-date-picker",
        //   props: {
        //     type: "datetime",
        //     valueFormat: "yyyy-MM-dd hh:mm:ss",
        //     placeholder: "请选择结束时间",
        //   },
        // },
        {
          title: "应收分润（%）",
          field: "expectedProfitSharing",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
          on: {
            change: (val, args, item, row, rowIndex) => {
              if (
                (row.expectedProfitSharing || row.expectedProfitSharing == 0) &&
                (row.actualProfitSharing || row.actualProfitSharing == 0)
              ) {
                const differenceAmount =
                  row.expectedProfitSharing - row.actualProfitSharing;
                this.$set(row, "differenceAmount", differenceAmount);
              }
            },
          },
        },
        {
          title: "实际执行分润（%）",
          field: "actualProfitSharing",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
          on: {
            change: (val, args, item, row, rowIndex) => {
              if (
                (row.expectedProfitSharing || row.expectedProfitSharing == 0) &&
                (row.actualProfitSharing || row.actualProfitSharing == 0)
              ) {
                const differenceAmount =
                  row.expectedProfitSharing - row.actualProfitSharing;
                this.$set(row, "differenceAmount", differenceAmount);
              }
            },
          },
        },
        {
          title: "差异额（%）",
          field: "differenceAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
        },
        {
          title: "差异类型",
          field: "differenceType",
          width: 150,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.differenceTypeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
            placeholder: "请选择",
          },
        },
        { title: "原因", field: "reason", width: 150, isEdit: true },
      ];
    },
    modalConfig() {
      return {
        modalTitle: "驳回到配置环节",
        formConfig: [
          {
            field: "reviewReason",
            title: "原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的描述，500个字符以内",
            },
            rules: [
              {
                required: true,
                trigger: "blur",
                message: "请输入原因",
              },
            ],
          },
        ],
      };
    },
    config() {
      return [
        // {
        //   field: "operator",
        //   title: "运营商名称",
        //   element: "el-autocomplete",
        //   props: {
        //     placeholder: "请输入或下拉选择",
        //     fetchSuggestions: (queryString, cb) => {
        //       return this.querySearch(queryString, cb, "getOperatorList");
        //     },
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       trigger: "change",
        //       message: "运营商名称不能为空",
        //     },
        //   ],
        // },
        // {
        //   field: "region",
        //   title: "所属省市",
        //   element: "custom-cascader",
        //   attrs: {
        //     collapseTags: true,
        //     props: {
        //       checkStrictly: false,
        //       multiple: false,
        //       value: "areaCode",
        //       label: "areaName",
        //     },
        //     options: this.regionData, //省市数据,
        //   },
        //   rules: [
        //     { required: true, trigger: "change", message: "所属省市不能为空" },
        //   ],
        // },
        // {
        //   field: "stationReviewDetails",
        //   title: "场站配置明细",
        //   element: "slot",
        //   slotName: "stationTable",
        //   defaultValue: [{ ...initParams(this.stationColumns) }],
        //   rules: [
        //     {
        //       required: true,
        //       trigger: "change",
        //       message: "场站配置明细不能为空",
        //     },
        //   ],
        // },
        {
          field: "stationReviewDetails",
          title: "配置结果",
          element: "slot",
          slotName: "stationTable",
          defaultValue: [],
          rules: [
            {
              required: true,
              trigger: "change",
              message: "场站配置明细不能为空",
            },
          ],
        },
        {
          field: "activityType",
          title: "活动类型",
          element: "slot",
          slotName: "activityType",
          rules: [
            { required: true, trigger: "change", message: "活动类型不能为空" },
          ],
          defaultValue: "1",
        },
        {
          field: "activityCount",
          title: "",
          show: false,
          defaultValue: "1",
        },
        {
          field: "stationShareCount",
          title: "场站分润（次）",
          element: "el-input-number",
          props: {
            max: 100,
            min: 0,
            precision: 0,
          },
          rules: [
            { required: true, trigger: "change", message: "场站分润不能为空" },
          ],
          defaultValue: 1,
        },
        {
          field: "reviewInstructions",
          title: "复核说明",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入具体的描述，500个字符以内",
          },
        },
        {
          field: "attachmentFileList",
          title: "上传",
          element: "file-upload",
          props: {
            limit: 99999,
            accept: ".jpg, .jpeg, .png",
            fileMaxSize: 50,
            textTip: "支持批量上传，上传格式为jpg、jpeg、png文件",
          },
        },
      ];
    },
  },
  created() {
    this.formParams = initParams(this.config);
    this.getCityRegionData();
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activityTypeOptions = response.data;
    });
  },
  methods: {
    async remoteStationOptions(params, key) {
      console.log("remoteStationOptions", params, key);
      const { searchText, ...rest } = params;
      const res = await api.getStationOptions({
        ...rest,
        [key]: searchText,
      });
      return {
        data: res.data,
        total: res.total,
      };
    },
    modalConfirmHandler(row) {
      api.saveRecheck(row).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("驳回成功");
          this.handleClose();
        }
      });
    },
    handleReject() {
      this.$refs.formModal.open({
        ...this.formParams,
        reviewReason: "",
        mailDataId: this.baseData.mailId,
        reviewResult: "1",
      });
    },
    async handleSubmit() {
      console.log("提交", this.formParams);
      // 注意：由于我们现在使用LuckySheet而不是stationTable，需要修改验证逻辑
      // const tableValid = await this.$refs.stationTable.validate();
      // if (!tableValid) return false;

      this.$refs.baseForm.validate((valid) => {
        if (!valid) return false;

        // 检查stationReviewDetails是否有效
        if (
          !this.formParams.stationReviewDetails ||
          (Array.isArray(this.formParams.stationReviewDetails) &&
            this.formParams.stationReviewDetails.length === 0)
        ) {
          this.$message.error("场站配置明细不能为空");
          return false;
        }

        this.$confirm("是否确认复核完成？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const { region, stationReviewDetails } = { ...this.formParams };

          // 确保stationReviewDetails是有效的数据
          let reviewDetailsToSubmit = stationReviewDetails;

          // 如果stationReviewDetails已经是字符串，不需要再次转换
          if (typeof reviewDetailsToSubmit !== "string") {
            try {
              reviewDetailsToSubmit = JSON.stringify(reviewDetailsToSubmit);
            } catch (error) {
              console.error("序列化stationReviewDetails失败:", error);
              this.$message.error("数据格式错误，无法提交");
              return;
            }
          }

          let params = {
            ...this.formParams,
            provinceCode: region?.[0] ? region?.[0] + "0000" : undefined,
            cityCode: region?.[1] ? region?.[1] + "00" : undefined,
            mailDataId: this.baseData.mailId,
            reviewResult: "0",
            // 使用处理后的数据
            stationReviewDetails: reviewDetailsToSubmit,
          };

          console.log("校验提交", params);
          api
            .saveRecheck(params)
            .then(() => {
              this.$message.success("保存成功");
              this.handleClose();
            })
            .catch((error) => {
              console.error("提交失败:", error);
              this.$message.error("提交失败，请检查数据格式");
            });
        });
      });
    },
    // 处理抽屉关闭前的操作
    handleBeforeClose(done) {
      // 确保在关闭前清理资源
      if (window.luckysheet) {
        try {
          // 不销毁LuckySheet实例，只是隐藏
          console.log("抽屉关闭，保留LuckySheet实例");
        } catch (error) {
          console.error("关闭抽屉时出错:", error);
        }
      }
      // 调用done完成关闭
      this.drawerVisible = false;
      this.$emit("close");
      done();
    },

    // 处理取消按钮点击
    handleClose() {
      this.drawerVisible = false;
      this.$emit("close");
    },
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        operator: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },

    open(row) {
      this.baseData = { ...row };

      // 如果抽屉已经初始化过，只需要更新数据
      if (this.isInitialized) {
        console.log("抽屉已初始化，仅更新数据");
        this.drawerVisible = true;
        this.getDetail();
      } else {
        // 首次打开，需要完整初始化
        console.log("首次打开抽屉，完整初始化");
        this.drawerVisible = true;
        this.formParams = initParams(this.config);
        this.getDetail();
        // 标记为已初始化
        this.isInitialized = true;
      }
    },
    getDetail() {
      this.loading = true;
      api
        .getConfigDetail({ mailDataId: this.baseData.mailId })
        .then((res) => {
          const {
            provinceCode,
            cityCode,
            stationConfigDetails,
            ...rest
          } = res.data;

          // 先解析stationConfigDetails，确保数据格式正确
          let parsedStationDetails = [];
          if (stationConfigDetails) {
            try {
              // 尝试解析JSON字符串
              parsedStationDetails = JSON.parse(stationConfigDetails);
              console.log("成功解析stationConfigDetails数据");
            } catch (error) {
              console.error("解析stationConfigDetails失败:", error);
              // 解析失败时使用空数组
              parsedStationDetails = [];
            }
          }

          // 使用setTimeout延迟设置formParams，避免立即触发更新
          setTimeout(() => {
            this.formParams = {
              ...this.formParams,
              ...rest,
              region:
                provinceCode && cityCode
                  ? [provinceCode.slice(0, -4), cityCode.slice(0, -2)]
                  : [],
              // 设置stationReviewDetails
              stationReviewDetails: parsedStationDetails,
            };
            console.log("已设置formParams.stationReviewDetails");
          }, 100);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
</style>
