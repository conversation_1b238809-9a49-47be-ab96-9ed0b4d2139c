<template>
  <el-drawer title="场站差异明细" :visible.sync="drawerVisible" size="80%">
    <StationConfigTable
      :columns="stationColumns"
      v-model="stationList"
      ref="stationTable"
    ></StationConfigTable>
    <div class="drawer-btn">
      <el-button @click.stop="drawerVisible = false" size="medium"
        >取 消</el-button
      >
      <el-button @click="handleSubmit" type="primary" size="medium"
        >确 定</el-button
      >
    </div>
  </el-drawer>
</template>

<script>
import StationConfigTable from "./stationConfigTable.vue";
import api from "@/api/ledger/commonEmail.js";

export default {
  components: { StationConfigTable },
  data() {
    return {
      drawerVisible: false,
      differenceTypeOptions: [],

      stationList: [],
    };
  },
  computed: {
    stationColumns() {
      return [
        // { type: "checkbox", width: 60, fixed: "left" },
        {
          title: "场站名称",
          field: "stationName",
          width: 150,
        },
        {
          title: "应收分润（%）",
          field: "expectedProfitSharing",
          width: 100,
        },
        {
          title: "实际执行分润（%）",
          field: "actualProfitSharing",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
          on: {
            change: (val, args, item, row, rowIndex) => {
              if (
                (row.expectedProfitSharing || row.expectedProfitSharing == 0) &&
                (row.actualProfitSharing || row.actualProfitSharing == 0)
              ) {
                const differenceAmount =
                  row.expectedProfitSharing - row.actualProfitSharing;
                this.$set(row, "differenceAmount", differenceAmount);
              }
            },
          },
        },
        {
          title: "差异额（%）",
          field: "differenceAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            controls: false,
            precision: 2,
          },
        },
        {
          title: "差异类型",
          field: "differenceType",
          width: 150,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.differenceTypeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
            placeholder: "请选择",
          },
        },
        { title: "原因", field: "reason", width: 150, isEdit: true },
        {
          title: "场站编码",
          field: "stationCode",
          width: 150,
        },
        {
          title: "活动生效日期",
          field: "activityStartDate",
          width: 120,
        },
        {
          title: "活动结束日期",
          field: "activityEndDate",
          width: 120,
        },
        {
          title: "结算比例（%）",
          field: "settlementRatio",
          width: 100,
        },
        {
          title: "分润配置（分润收入）（%）",
          field: "profitSharingIncome",
          width: 100,
        },
        {
          title: "购电配置（X折结算）（%）",
          field: "purchaseElectricityDiscount",
          width: 100,
        },
        {
          title: "长协（通用分润）（%）",
          field: "longTermAgreement",
          width: 100,
        },
        {
          title: "短协（活动分润）（%）",
          field: "shortTermAgreement",
          width: 100,
        },
        {
          title: "开始时间",
          field: "recordStartTime",
          width: 120,
        },
        {
          title: "结束时间",
          field: "recordEndTime",
          width: 120,
        },
      ];
    },
  },
  created() {
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
  },
  methods: {
    handleSubmit() {
      api
        .difference({
          reviewDetailList: this.stationList,
          mailDataId: this.mailId,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.$message.success("提交成功");
            this.handleClose();
          }
        });
    },
    open(row) {
      this.mailId = row.mailId;
      this.drawerVisible = true;
      this.getDetail();
    },
    getDetail() {
      api.getRecheckDetail({ mailDataId: this.mailId }).then((res) => {
        if (res?.code === "10000") {
          this.stationList = res?.data?.stationReviewDetails;
        }
      });
    },
    handleClose() {
      this.drawerVisible = false;
      this.$emit("close");
    },
  },
};
</script>
<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
</style>
