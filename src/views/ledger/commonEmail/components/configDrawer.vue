<!-- 该页面已废弃 -->
<template>
  <el-drawer
    title="活动配置结果"
    :visible.sync="drawerVisible"
    size="80%"
    append-to-body
    :destroy-on-close="false"
    :before-close="handleBeforeClose"
  >
    <!-- 使用keep-alive缓存内容，避免重复渲染 -->
    <keep-alive>
      <div v-if="drawerVisible" v-loading="loading">
        <!-- 添加 @submit.prevent 阻止表单默认提交行为 -->
        <DynamicForm
          ref="baseForm"
          :config="config"
          :params="formParams"
          labelPosition="right"
          :defaultColSpan="24"
          labelWidth="150px"
          @submit.prevent
        >
          <template #stationTable>
            <LuckySheet
              v-model="formParams.stationConfigDetails"
              :containerId="'luckysheet-config-' + uniqueId"
              ref="luckySheet"
            />
          </template>
          <template #mailReplyContent>
            <Editor v-model="formParams.mailReplyContent" />
          </template>
          <template #activityType>
            <div style="display: flex; align-items: center;">
              <el-select
                v-model="formParams.activityType"
                placeholder="请选择活动类型"
                style="width: 200px;"
                filterable
                clearable
              >
                <el-option
                  v-for="item in activityTypeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
              <div
                style="display: flex; align-items: center; margin-left: 10px;"
              >
                <el-input-number
                  v-model="formParams.activityCount"
                  :min="0"
                  :precision="0"
                  controls-position="right"
                  style="width: 120px;"
                ></el-input-number>
                <span style="margin-left: 5px;">次</span>
              </div>
            </div>
          </template>
        </DynamicForm>
        <div class="drawer-btn">
          <el-button @click.stop="handleClose" size="medium">取 消</el-button>
          <el-button
            @click="handleSubmit"
            type="primary"
            size="medium"
            :loading="btnLoading"
            >确 定</el-button
          >
        </div>
      </div>
    </keep-alive>
  </el-drawer>
</template>

<script>
import { queryCityTree } from "@/api/common.js";
import api from "@/api/ledger/commonEmail.js";
import { initParams } from "@/utils/buse.js";
import StationConfigTable from "./stationConfigTable.vue";
import Editor from "@/components/Editor/index.vue";
import LuckySheet from "@/components/LuckySheet/index.vue";

export default {
  components: { StationConfigTable, Editor, LuckySheet },
  data() {
    return {
      baseData: {},
      mailDataId: "",
      differenceTypeOptions: [],
      formParams: {},
      drawerVisible: false,
      operatorOptions: [],
      regionData: [],
      btnLoading: false,
      loading: false,
      activityTypeOptions: [],
      // 添加唯一ID，确保每个LuckySheet实例有唯一的containerId
      uniqueId: Date.now() + Math.floor(Math.random() * 1000),
      // 添加标记，用于跟踪抽屉是否已初始化
      isInitialized: false,
    };
  },
  computed: {
    config() {
      return [
        // {
        //   field: "operator",
        //   title: "运营商名称",
        //   element: "el-autocomplete",
        //   props: {
        //     placeholder: "请输入或下拉选择",
        //     fetchSuggestions: (queryString, cb) => {
        //       return this.querySearch(queryString, cb, "getOperatorList");
        //     },
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       trigger: "change",
        //       message: "运营商名称不能为空",
        //     },
        //   ],
        // },
        // {
        //   field: "region",
        //   title: "所属省市",
        //   element: "custom-cascader",
        //   attrs: {
        //     collapseTags: true,
        //     props: {
        //       checkStrictly: false,
        //       multiple: false,
        //       value: "areaCode",
        //       label: "areaName",
        //     },
        //     options: this.regionData, //省市数据,
        //   },
        //   rules: [
        //     { required: true, trigger: "change", message: "所属省市不能为空" },
        //   ],
        // },
        // {
        //   field: "stationConfigDetails",
        //   title: "场站配置明细",
        //   element: "slot",
        //   slotName: "stationTable",
        //   defaultValue: [{ ...initParams(this.stationColumns) }],
        //   rules: [
        //     {
        //       required: true,
        //       trigger: "change",
        //       message: "场站配置明细不能为空",
        //     },
        //   ],
        // },
        {
          field: "stationConfigDetails",
          title: "配置结果",
          element: "slot",
          slotName: "stationTable",
          defaultValue: [],
          rules: [
            {
              required: true,
              trigger: "change",
              message: "场站配置明细不能为空",
            },
          ],
        },
        {
          field: "activityType",
          title: "活动类型",
          element: "slot",
          slotName: "activityType",
          rules: [
            { required: true, trigger: "change", message: "活动类型不能为空" },
          ],
          defaultValue: "1",
        },
        {
          field: "activityCount",
          title: "",
          show: false,
          defaultValue: "1",
        },
        {
          field: "stationShareCount",
          title: "场站分润（次）",
          element: "el-input-number",
          props: {
            max: 100,
            min: 0,
            precision: 0,
          },
          rules: [
            { required: true, trigger: "change", message: "场站分润不能为空" },
          ],
          defaultValue: 1,
        },
        {
          field: "configInstructions",
          title: "配置说明",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入具体的描述，500个字符以内",
          },
        },
        {
          field: "attachmentFileList",
          title: "上传",
          element: "file-upload",
          props: {
            limit: 20,
            accept: ".jpg, .jpeg, .png",
            fileMaxSize: 50,
            textTip: "支持批量上传，上传格式为jpg、jpeg、png文件",
          },
        },
        // {
        //   field: "isReplyNew",
        //   title: "是否按新邮件发送",
        //   element: "el-radio-group",
        //   props: {
        //     options: [
        //       { value: "N", label: "否（按回复方式发送，将携带原始邮件内容）" },
        //       { value: "Y", label: "是（按新邮件发送，不带原始邮件内容）" },
        //     ],
        //   },
        //   rules: [{ required: true, trigger: "change", message: "请选择" }],
        //   defaultValue: "N",
        //   on: {
        //     change: (val) => {
        //       this.formParams.mailReplyContent =
        //         val === "Y" ? this.baseData.mailContent : "";
        //     },
        //   },
        // },
        // {
        //   field: "isReplied",
        //   title: "是否邮件回复",
        //   element: "el-radio-group",
        //   props: {
        //     options: [
        //       { value: "Y", label: "回复" },
        //       {
        //         value: "N",
        //         label: "不回复（不发送邮件，仅在维保通内记录配置结果）",
        //       },
        //     ],
        //   },
        //   rules: [{ required: true, trigger: "change", message: "请选择" }],
        //   defaultValue: "Y",
        // },
        // {
        //   field: "mailReplyContent",
        //   title: "邮件回复内容",
        //   slotName: "mailReplyContent",
        //   element: "slot",
        //   // attrs: {
        //   //   rows: 5,
        //   //   maxlength: 10000,
        //   //   showWordLimit: true,
        //   //   placeholder: "请输入邮件正文内容",
        //   // },
        //   rules: [
        //     {
        //       required: this.formParams.isReplied == "Y",
        //       trigger: "change",
        //       message: "邮件回复内容不能为空",
        //     },
        //   ],
        // },
        // {
        //   field: "emailSubject",
        //   title: "邮件主题",
        //   attrs: {
        //     maxlength: 1000,
        //   },
        //   rules: [
        //     {
        //       required: this.formParams.isReplied == "Y",
        //       trigger: "change",
        //       message: "邮件主题不能为空",
        //     },
        //   ],
        //   defaultValue: this.baseData.mailSubject,
        // },
        // {
        //   field: "recipient",
        //   title: "收件人",
        //   attrs: {
        //     placeholder: "请输入邮件地址，多个地址之间用【,】分隔",
        //   },
        //   rules: [
        //     {
        //       required: this.formParams.isReplied == "Y",
        //       message: "收件人邮箱地址不能为空",
        //     },
        //     { validator: this.validateEmails },
        //   ],
        //   defaultValue: this.baseData.sender?.match(/<([^>]+)>/)?.[1] || "",
        // },
        // {
        //   field: "ccRecipient",
        //   title: "抄送人",
        //   attrs: {
        //     placeholder: "请输入邮件地址，多个地址之间用【,】分隔",
        //   },
        //   rules: [{ validator: this.validateEmails }],
        //   defaultValue: this.baseData.ccTo,
        // },
        // {
        //   field: "sendAttachmentFileList",
        //   title: "上传",
        //   element: "file-upload",
        //   props: {
        //     limit: 99999,
        //     accept: ".jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .pdf",
        //     fileMaxSize: 50,
        //     textTip:
        //       "支持批量上传，支持word、excel、pdf、图片格式的文件，单个文件50M以内",
        //   },
        //   defaultValue: this.baseData.attachment || [],
        // },
      ];
    },
  },
  created() {
    this.formParams = initParams(this.config);
    this.getCityRegionData();
    this.getDicts("difference_type").then((response) => {
      this.differenceTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activityTypeOptions = response.data;
    });
  },

  methods: {
    async remoteStationOptions(params, key) {
      const { searchText, ...rest } = params;
      const res = await api.getStationOptions({
        ...rest,
        [key]: searchText,
      });
      return {
        data: res.data,
        total: res.total,
      };
    },
    // 邮箱校验
    validateEmails(rule, value, callback) {
      const emails = value?.split(/,\s*/) || [];
      // if (emails.length > 100) {
      //   callback(new Error("最多输入100个邮箱地址"));
      //   return;
      // }
      const invalid = emails.find(
        (e) => !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(e)
      );
      invalid ? callback(new Error(`无效邮箱地址：${invalid}`)) : callback();
    },
    handleSubmit() {
      console.log("提交", this.formParams);

      // 表单验证

      this.$refs.baseForm.validate((valid) => {
        if (!valid) return false;

        const { region, stationConfigDetails } = { ...this.formParams };
        let params = {
          ...this.formParams,
          provinceCode: region?.[0] ? region?.[0] + "0000" : undefined,
          cityCode: region?.[1] ? region?.[1] + "00" : undefined,
          mailDataId: this.baseData.mailId,
          messageId: this.baseData.messageId,
          // 直接使用LuckySheet原始数据，不进行格式转换
          stationConfigDetails: JSON.stringify(stationConfigDetails),
        };
        console.log("校验提交", params);
        this.btnLoading = true;
        api
          .saveConfig(params)
          .then(() => {
            this.$message.success("保存成功");
            this.handleClose();
          })
          .finally(() => {
            this.btnLoading = false;
          });
      });
    },
    // 处理抽屉关闭前的操作
    handleBeforeClose(done) {
      // 确保在关闭前清理资源
      if (window.luckysheet) {
        try {
          // 不销毁LuckySheet实例，只是隐藏
          console.log("抽屉关闭，保留LuckySheet实例");
        } catch (error) {
          console.error("关闭抽屉时出错:", error);
        }
      }
      // 调用done完成关闭
      this.drawerVisible = false;
      this.$emit("close");
      done();
    },

    // 处理取消按钮点击
    handleClose() {
      this.drawerVisible = false;
      this.$emit("close");
    },
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        operator: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },

    open(row) {
      console.log("打开配置抽屉", row);
      this.baseData = { ...row };

      // 如果抽屉已经初始化过，只需要更新数据
      if (this.isInitialized) {
        console.log("抽屉已初始化，仅更新数据");
        this.drawerVisible = true;
        this.getDetail();
      } else {
        // 首次打开，需要完整初始化
        console.log("首次打开抽屉，完整初始化");
        this.drawerVisible = true;
        this.formParams = initParams(this.config);
        this.getDetail();
        // 标记为已初始化
        this.isInitialized = true;
      }
    },
    getDetail() {
      this.loading = true;
      api
        .getConfigDetail({ mailDataId: this.baseData.mailId })
        .then((res) => {
          const { provinceCode, cityCode, stationConfigDetails } = res.data;
          this.formParams = {
            ...this.formParams,
            ...res.data,
            region:
              provinceCode && cityCode
                ? [provinceCode.slice(0, -4), cityCode.slice(0, -2)]
                : [],
            // 直接使用后端返回的LuckySheet原始数据
            stationConfigDetails: stationConfigDetails
              ? JSON.parse(stationConfigDetails)
              : [],
          };
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
</style>
