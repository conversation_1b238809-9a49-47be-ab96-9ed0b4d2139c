<template>
  <el-popover
    placement="bottom-end"
    width="400"
    trigger="click"
    v-model="visible"
    popper-class="filter-preference-popover"
  >
    <div class="filter-preference-container">
      <div class="filter-preference-header">
        <el-button type="primary" size="mini" @click="selectAll"
          >全部</el-button
        >
      </div>
      <div class="filter-preference-body">
        <el-checkbox-group v-model="selectedFilters">
          <div
            v-for="(item, index) in filterOptions"
            :key="index"
            class="filter-preference-item"
          >
            <el-checkbox :label="item.field">
              <div class="filter-preference-item-content">
                <span class="filter-preference-item-title">{{
                  item.title
                }}</span>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      <div class="filter-preference-footer">
        <el-button size="small" @click="handleRestore">恢复默认</el-button>
        <el-button size="small" @click="handleCancel">取消</el-button>
        <el-button type="primary" size="small" @click="handleConfirm"
          >确认</el-button
        >
      </div>
    </div>
    <el-button slot="reference" type="text">
      <i class="el-icon-s-grid"></i>
    </el-button>
  </el-popover>
</template>

<script>
export default {
  name: "FilterPreference",
  props: {
    filterOptions: {
      type: Array,
      required: true,
    },
    value: {
      type: Array,
      default: () => [],
    },
    defaultFields: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      selectedFilters: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        this.selectedFilters = [...val];
      },
      immediate: true,
    },
  },
  methods: {
    selectAll() {
      this.selectedFilters = this.filterOptions.map((item) => item.field);
    },
    handleRestore() {
      this.selectedFilters = [...this.defaultFields];
    },
    handleCancel() {
      this.visible = false;
      this.selectedFilters = [...this.value];
    },
    handleConfirm() {
      this.$emit("input", [...this.selectedFilters]);
      this.$emit("change", [...this.selectedFilters]);
      this.visible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.filter-preference-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.filter-preference-header {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.filter-preference-body {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  max-height: 400px;
}

.filter-preference-item {
  margin-bottom: 10px;
}

.filter-preference-item-content {
  display: flex;
  align-items: center;
}

.filter-preference-item-dots {
  margin-right: 5px;
  color: #909399;
}

.filter-preference-item-title {
  flex: 1;
}

.filter-preference-item-actions {
  display: flex;
  gap: 5px;
  color: #909399;
}

.filter-preference-footer {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
