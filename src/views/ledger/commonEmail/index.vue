<!-- 公共邮箱 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <OriginalData
      v-show="tabActiveTab === 'originalData'"
      ref="originalData"
      :searchParams="searchParams"
    ></OriginalData>
    <ConfigResult
      v-show="tabActiveTab === 'configResult'"
      ref="configResult"
      @jump="handleJump"
    ></ConfigResult>
    <EmailSourceConfig
      v-show="tabActiveTab === 'sourceConfig'"
      ref="sourceConfig"
    ></EmailSourceConfig>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import OriginalData from "./originalData.vue";
import ConfigResult from "./configResult.vue";
import EmailSourceConfig from "./emailSourceConfig.vue";
export default {
  name: "commonMailList",
  components: { OriginalData, ConfigResult, EmailSourceConfig },
  data() {
    return {
      tabActiveTab: "originalData",
      topTabDict: [
        {
          value: "originalData",
          label: "邮件原始数据",
          show: () => {
            return this.checkPermission(["commonEmail:originalData:list"]);
          },
        },
        {
          value: "configResult",
          label: "邮件配置结果",
          show: () => {
            return this.checkPermission(["commonEmail:configResult:list"]);
          },
        },
        {
          value: "sourceConfig",
          label: "邮箱数据源配置",
          show: () => {
            return this.checkPermission(["commonEmail:sourceConfig:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    // if (Object.keys(this.$route.params)?.length > 0) {
    //   this.params = { ...this.params, ...this.$route.params };
    // }
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        this.tabActiveTab = "originalData";
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
