<template>
  <div class="card-container">
    <!-- 上面固定部分-S -->
    <div class="page-header">
      <el-card class="page-header-left">
        <div class="page-header-left-top">
          <h3>{{ params.mailSubject }}</h3>
          <el-dropdown @command="(command) => handleCommand(command, params)">
            <el-button type="text" size="large">
              快捷操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in modalConfig.customOperationTypes"
                  :command="item.typeName"
                  :key="index"
                  v-show="item.condition(params)"
                >
                  {{ item.title }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="page-header-left-bottom">
          <div style="flex:1" class="mr10">
            <div class="text-title">接单时间</div>
            <div class="text-content">{{ params.receiveTime }}</div>
          </div>
          <div style="flex:1" class="mr10">
            <div class="text-title">接单人</div>
            <div class="text-content">{{ params.receiveOrderUserName }}</div>
          </div>
          <div style="flex:1" class="mr10">
            <div class="text-title">配置时间</div>
            <div class="text-content">
              {{ params.configTime }}
            </div>
          </div>
          <div style="flex:1">
            <div class="text-title">配置人</div>
            <div class="text-content">
              {{ params.configUserName }}
            </div>
          </div>
        </div>
      </el-card>
      <el-card class="page-header-right">
        <div class="page-header-right-bottom">
          <div style="flex:1" class="mr10">
            <div class="text-title">接单状态</div>
            <div class="text-content">
              <el-tag
                :style="{
                  color: getTagType(params.receiveOrderStatusName).fontColor,
                  borderColor: getTagType(params.receiveOrderStatusName)
                    .fontColor,
                  width: '80px',
                }"
                :type="getTagType(params.receiveOrderStatusName).type"
                :color="getTagType(params.receiveOrderStatusName).color"
                size="medium"
              >
                {{ params.receiveOrderStatusName || "--" }}
              </el-tag>
            </div>
          </div>
          <div style="flex:1" class="mr10">
            <div class="text-title">配置状态</div>
            <div class="text-content">
              <el-tag
                :style="{
                  color: getTagType(params.configStatusName).fontColor,
                  borderColor: getTagType(params.configStatusName).fontColor,
                  width: '80px',
                }"
                :type="getTagType(params.configStatusName).type"
                :color="getTagType(params.configStatusName).color"
                size="medium"
              >
                {{ params.configStatusName || "--" }}
              </el-tag>
            </div>
          </div>
          <div style="flex:1">
            <div class="text-title">邮件是否已回复</div>
            <div class="text-content">
              <el-tag
                :style="{
                  color: getTagType(params.isReplied).fontColor,
                  borderColor: getTagType(params.isReplied).fontColor,
                  width: '80px',
                }"
                :type="getTagType(params.isReplied).type"
                :color="getTagType(params.isReplied).color"
                size="medium"
              >
                {{
                  params.isReplied === "Y"
                    ? "是"
                    : params.isReplied === "N"
                    ? "否"
                    : "--"
                }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <!-- 上面固定部分-E -->
    <!-- tab部分-S -->
    <el-card class="mt20">
      <el-tabs v-model="activeName" @tab-click="loadData">
        <el-tab-pane label="邮件内容" name="base">
          <DynamicForm
            ref="baseForm"
            :config="config"
            :params="params"
            preview
            :defaultColSpan="24"
            labelPosition="right"
            labelWidth="120px"
          >
            <template #copy="{item,params}">
              <span>{{ params[item.field] }} </span>
              <i
                class="el-icon-document-copy pointer-icon"
                @click="copyToClipboard(params[item.field])"
              ></i>
            </template>
            <template #htmlText="{item,params}">
              <div
                v-html="params[item.field]"
                style="border:1px solid #ccc;padding: 10px;"
              ></div>
            </template>
            <template #fileList="{item,params}">
              <FileIcons
                :list="params[item.field]"
                :isCenter="false"
                :fileOptions="{ url: 'storePath', name: 'docName' }"
              ></FileIcons>
            </template>
          </DynamicForm>
        </el-tab-pane>
        <el-tab-pane label="配置结果" name="config">
          <ResultDetail
            ref="configResultDetail"
            :mailDataId="mailId"
            resultType="config"
          ></ResultDetail>
        </el-tab-pane>
        <el-tab-pane label="复核结果" name="recheck">
          <ResultDetail
            ref="recheckResultDetail"
            :mailDataId="mailId"
            resultType="recheck"
          ></ResultDetail>
        </el-tab-pane>
        <el-tab-pane label="操作记录" name="record">
          <Timeline
            :list="recordList"
            operateTypeTitle="operatorTypeName"
            operatorNameTitle="operatorUserName"
            createTimeTitle="operatorTime"
            operateDetailTitle="remark"
          ></Timeline>
        </el-tab-pane>
        <el-tab-pane label="邮件发送记录" name="emailRecord">
          <div v-if="emailRecordList && emailRecordList.length > 0">
            <div v-for="(item, index) in emailRecordList" :key="index">
              <DynamicForm
                :config="emailRecordConfig"
                :params="item"
                preview
                :defaultColSpan="24"
                labelPosition="right"
                labelWidth="150px"
              >
                <template #htmlContent="{item,params}">
                  <div
                    v-html="params[item.field]"
                    style="border:1px solid #ccc;padding: 10px;"
                  ></div>
                </template>
                <template #fileList="{item,params}">
                  <FileIcons
                    :list="params[item.field]"
                    :isCenter="false"
                    :fileOptions="{ url: 'storePath', name: 'docName' }"
                  ></FileIcons>
                </template>
              </DynamicForm>
              <div
                class="email-record-divider"
                v-if="index < emailRecordList.length - 1"
              ></div>
            </div>
          </div>
          <el-empty v-else description="暂无邮件发送记录"></el-empty>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <!-- tab部分-E -->
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.formConfig.modalTitle"
      :config="modalConfig.formConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
      :auto-close="false"
    >
    </BaseFormModal>
    <ConfigDrawer ref="configDrawer" @close="loadData"></ConfigDrawer>
    <RecheckDrawer ref="recheckDrawer" @close="loadData"></RecheckDrawer>
    <DifferenceDrawer
      ref="differenceDrawer"
      @close="loadData"
    ></DifferenceDrawer>
    <ReplyEmailDrawer
      ref="replyEmailDrawer"
      @close="loadData"
    ></ReplyEmailDrawer>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import checkPermission from "@/utils/permission.js";
import FileIcons from "@/components/FileIcons/index.vue";
import api from "@/api/ledger/commonEmail.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import ResultDetail from "./components/resultDetail.vue";
import ConfigDrawer from "./components/configDrawer.vue";
import RecheckDrawer from "./components/recheckDrawer.vue";
import DifferenceDrawer from "./components/differenceDrawer.vue";
import ReplyEmailDrawer from "./components/replyEmailDrawer.vue";
import { copyToClipboard } from "@/utils/index.js";
import { initParams } from "@/utils/buse";
export default {
  components: {
    FileIcons,
    ResultDetail,
    Timeline,
    BaseFormModal,
    ConfigDrawer,
    RecheckDrawer,
    DifferenceDrawer,
    ReplyEmailDrawer,
  },
  data() {
    return {
      activeName: "base",
      params: {},
      recordList: [],
      emailRecordList: [],
      operationType: "accept",
      mailId: "",
      messageId: "",
      activityTypeOptions: [],
      cancelTypeOptions: [],
    };
  },
  computed: {
    modalConfig() {
      const form = {
        //接单
        accept: {
          modalTitle: "接单",
          formConfig: [
            {
              field: "receiveOrderType",
              element: "el-radio-group",
              title: "选择接单操作",
              props: {
                options: [
                  { value: "1", label: "接单" },
                  { value: "2", label: "作废" },
                ],
              },
              defaultValue: "1",
              rules: [{ required: true, message: "请选择接单操作" }],
            },
            {
              field: "cancelType",
              element: "el-select",
              title: "作废类别",
              props: {
                options: this.cancelTypeOptions,
                filterable: true,
                optionLabel: "dictLabel",
                optionValue: "dictValue",
              },
              rules: [
                {
                  required: this.$refs.crud?.getFormFields()?.acceptType == "2",
                  message: "请选择",
                },
              ],
            },
            {
              field: "cancelReason",
              element: "el-input",
              title: "作废原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "非必填，500个字符以内",
              },
            },
          ],
        },
        //作废
        cancel: {
          modalTitle: "作废",
          formConfig: [
            {
              field: "cancelType",
              element: "el-select",
              title: "作废类别",
              props: {
                options: this.cancelTypeOptions,
                filterable: true,
                optionLabel: "dictLabel",
                optionValue: "dictValue",
              },
              rules: [
                {
                  required: true,
                  message: "请选择",
                },
              ],
            },
            {
              field: "cancelReason",
              element: "el-input",
              title: "作废原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "非必填，500个字符以内",
              },
            },
          ],
        },
        //活动类别
        activityType: {
          modalTitle: "活动类别",
          formConfig: [
            {
              field: "activityType",
              element: "el-select",
              title: "选择活动类别",
              props: {
                options: this.activityTypeOptions,
                filterable: true,
                optionLabel: "dictLabel",
                optionValue: "dictValue",
              },
              rules: [
                {
                  required: true,
                  message: "请选择",
                },
              ],
            },
          ],
        },
        //备注
        remark: {
          modalTitle: "备注",
          formConfig: [
            {
              field: "remark",
              element: "el-input",
              title: "备注信息",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "请输入具体的描述，500个字符以内",
              },
              rules: [{ required: true, message: "备注不能为空！" }],
            },
          ],
        },
      };
      return {
        formConfig: form[this.operationType] || [],
        customOperationTypes: [
          {
            title: "接单",
            typeName: "accept",
            isOutside: true,
            event: (row) => {
              return this.handleAccept(row);
            },
            condition: (row) => {
              //未接单/已作废
              const condition1 = ["1", "3"].includes(row.receiveOrderStatus);
              return (
                condition1 &&
                checkPermission(["commonEmail:originalData:accept"])
              );
            },
          },
          {
            title: "配置",
            typeName: "config",
            slotName: "config",
            showForm: false,
            isOutside: true,
            event: (row) => {
              return this.handleConfig(row);
            },
            condition: (row) => {
              //已接单
              const condition1 = row.receiveOrderStatus == "2";
              //未配置/复核驳回
              const condition2 = ["1", "4"].includes(row.configStatus);
              return (
                condition1 &&
                condition2 &&
                checkPermission(["commonEmail:originalData:config"])
              );
            },
          },
          {
            title: "复核",
            typeName: "recheck",
            isOutside: true,
            event: (row) => {
              return this.handleRecheck(row);
            },
            condition: (row) => {
              //已接单
              const condition1 = row.receiveOrderStatus == "2";
              //已配置
              const condition2 = row.configStatus == "2";
              return (
                condition1 &&
                condition2 &&
                checkPermission(["commonEmail:originalData:recheck"])
              );
            },
          },
          {
            title: "作废",
            typeName: "cancel",
            event: (row) => {
              return this.handleCancel(row);
            },
            condition: (row) => {
              //已接单
              const condition1 = row.receiveOrderStatus == "2";
              return (
                condition1 &&
                checkPermission(["commonEmail:originalData:cancel"])
              );
            },
          },
          {
            title: "备注",
            typeName: "remark",
            // showForm: false,
            event: (row) => {
              return this.handleRemark(row);
            },
            condition: (row) => {
              return checkPermission(["commonEmail:originalData:remark"]);
            },
          },
          {
            title: "差异",
            typeName: "difference",
            event: (row) => {
              return this.handleDifference(row);
            },
            condition: (row) => {
              //已接单
              const condition1 = row.receiveOrderStatus == "2";
              //已复核
              const condition2 = row.configStatus == "3";
              return (
                condition1 &&
                condition2 &&
                checkPermission(["commonEmail:originalData:difference"])
              );
            },
          },
          // {
          //   title: "活动类别",
          //   typeName: "activityType",
          //   event: (row) => {
          //     return this.handleActivityType(row);
          //   },
          //   condition: (row) => {
          //     //已接单
          //     const condition1 = row.receiveOrderStatus == "2";
          //     //未配置/已配置/复核驳回
          //     const condition2 = ["1", "2", "4"].includes(row.configStatus);
          //     return (
          //       condition1 &&
          //       condition2 &&
          //       checkPermission(["commonEmail:originalData:activityType"])
          //     );
          //   },
          // },
          {
            title: "发邮件",
            typeName: "replyEmail",
            isOutside: true,
            event: (row) => {
              return this.handleReplyEmail(row);
            },
            condition: (row) => {
              return checkPermission(["commonEmail:originalData:replyEmail"]);
            },
          },
        ],
      };
    },
    config() {
      return [
        {
          field: "mailSubject",
          title: "邮件标题：",
        },
        {
          field: "sender",
          title: "发件人：",
          previewSlot: "copy",
        },
        {
          field: "ccTo",
          title: "抄送人：",
          previewSlot: "copy",
        },
        {
          field: "sentTime",
          title: "发件时间：",
        },
        {
          field: "mailContent",
          title: "邮件正文：",
          previewSlot: "htmlText",
        },
        {
          field: "attachment",
          title: "邮件附件：",
          previewSlot: "fileList",
          defaultValue: [],
        },
        // {
        //   field: "name",
        //   title: "发件人签名：",
        // },
      ];
    },
    emailRecordConfig() {
      return [
        {
          field: "mailSubject",
          title: "邮件主题：",
        },
        {
          field: "recipient",
          title: "收件人：",
        },
        {
          field: "ccTo",
          title: "抄送人：",
        },
        {
          field: "mailReplyContent",
          title: "邮件回复内容：",
          previewSlot: "htmlContent",
        },
        {
          field: "senderAddr",
          title: "发件人：",
        },
        {
          field: "isReplyNew",
          title: "是否按新邮件发送：",
        },
        {
          field: "isCreateOrder",
          title: "是否创建工单台账：",
        },
        {
          field: "businessTypeName",
          title: "业务类型：",
        },
        {
          field: "attachments",
          title: "发送附件：",
          previewSlot: "fileList",
          defaultValue: [],
        },
        {
          field: "sendTime",
          title: "邮件发送时间：",
        },
        {
          field: "sendStatus",
          title: "邮件发送状态：",
        },
        {
          field: "createByName",
          title: "发送人：",
        },
      ];
    },
  },
  created() {
    this.getDicts("cancel_type").then((response) => {
      this.cancelTypeOptions = response.data;
    });
    this.getDicts("activity_type").then((response) => {
      this.activityTypeOptions = response.data;
    });
    this.mailId = this.$route.query.mailId;
    this.messageId = this.$route.query.messageId;
    this.loadData();
  },
  methods: {
    copyToClipboard,
    checkPermission,
    handleCommand(command, row) {
      this.modalConfig.customOperationTypes
        ?.find((x) => x.typeName == command)
        ?.event(row);
    },
    getTagType(status) {
      const arr = [
        { type: "success", status: "已接单", fontColor: "#67c23a" },
        { type: "danger", status: "未接单", fontColor: "#f56c6c" },
        { type: "info", status: "已作废", fontColor: "#909399" },
        { type: "warning", status: "未配置", fontColor: "#e6a23c" },
        {
          type: "",
          status: "已配置",
          color: "rgba(2, 156, 124, 0.07)",
          fontColor: "#449a7f",
        },
        {
          type: "",
          status: "已复核",
          color: "rgba(2, 99, 156, 0.09)",
          fontColor: "#2a6198",
        },
        { type: "danger", status: "复核驳回", fontColor: "#f56c6c" },
        {
          type: "",
          status: "Y",
          color: "rgba(2, 156, 124, 0.07)",
          fontColor: "#449a7f",
        },
        { type: "info", status: "N", fontColor: "#909399" },
      ];
      return arr.find((x) => x.status == status) || {};
    },
    getRecord() {
      api.queryRecordList({ businessId: this.mailId }).then((res) => {
        this.recordList = res.data;
      });
    },
    getMailDetail() {
      api.queryMailDetail({ mailId: this.mailId }).then((res) => {
        this.params = { ...initParams(this.config), ...res.data };
      });
    },
    getEmailSendRecords() {
      api.getEmailSendRecords({ messageId: this.messageId }).then((res) => {
        this.emailRecordList = res.data || [];
      });
    },
    loadData() {
      const arr = [
        {
          activeName: "base",
          event: () => {
            this.getMailDetail();
          },
        },
        {
          activeName: "config",
          event: () => {
            this.$refs.configResultDetail?.loadData();
            this.getMailDetail();
          },
        },
        {
          activeName: "recheck",
          event: () => {
            this.$refs.recheckResultDetail?.loadData();
            this.getMailDetail();
          },
        },
        {
          activeName: "record",
          event: () => {
            this.getRecord();
          },
        },
        {
          activeName: "emailRecord",
          event: () => {
            this.getEmailSendRecords();
          },
        },
      ];
      arr.find((x) => x.activeName == this.activeName)?.event();
    },

    //快捷操作部分
    async modalConfirmHandler(row) {
      let params = { ...row };
      // crudOperationType:accept/remark/cancel/activityType

      if (this.operationType === "accept" || this.operationType === "cancel") {
        this.$confirm(`是否确定提交？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            const res = await api[this.operationType](params);
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              this.loadData();
              this.$refs.formModal.closeVisible();
            } else {
              return false;
            }
          })
          .catch(() => {
            return false;
          });
      } else {
        const res = await api[this.operationType](params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          this.$refs.formModal.closeVisible();
        } else {
          return false;
        }
      }
    },
    // 配置
    handleConfig(row) {
      this.$refs.configDrawer.open(row);
    },
    //复核
    handleRecheck(row) {
      this.$refs.recheckDrawer.open(row);
    },
    //差异
    handleDifference(row) {
      this.$refs.differenceDrawer.open(row);
    },
    //接单
    handleAccept(row) {
      this.operationType = "accept";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig.formConfig),
        mailId: row.mailId,
      });
    },
    //作废
    handleCancel(row) {
      this.operationType = "cancel";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig.formConfig),
        mailId: row.mailId,
      });
    },
    //活动类别
    handleActivityType(row) {
      this.operationType = "activityType";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig.formConfig),
        mailId: row.mailId,
      });
    },
    handleRemark(row) {
      this.operationType = "remark";
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig.formConfig),
        mailId: row.mailId,
      });
    },
    //发邮件
    handleReplyEmail(row) {
      this.$refs.replyEmailDrawer.open(row);
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  &-left {
    flex: 1.5;
    margin-right: 20px;
    &-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-bottom {
      margin-top: 24px;
      display: flex;
      .text-title {
        font-size: 14px;
        color: rgba(154, 180, 174, 1);
      }
      .text-content {
        margin-top: 18px;
        font-size: 16px;
        color: rgba(16, 16, 16, 1);
      }
    }
  }
  &-right {
    flex: 1;
    &-bottom {
      margin-top: 24px;
      display: flex;
      text-align: center;
      .text-title {
        font-size: 14px;
        color: rgba(154, 180, 174, 1);
      }
      .text-content {
        margin-top: 30px;
      }
    }
  }
}

.email-record-divider {
  border-bottom: 1px dashed #029c7c;
  margin: 20px 0;
}
</style>
