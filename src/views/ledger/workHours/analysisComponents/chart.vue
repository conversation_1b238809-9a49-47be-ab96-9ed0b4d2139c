<template>
  <div>
    <el-form :model="form" ref="form">
      <el-form-item label="时间" prop="timeRange" label-width="50px">
        <el-date-picker
          v-model="form.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          value-format="yyyy-MM-dd"
          size="mini"
          @change="loadData"
          :clearable="false"
        ></el-date-picker>

        <el-radio-group v-model="form.timeRange" size="mini" @change="loadData">
          <el-radio-button v-for="(x, i) in timeArr" :label="x.date" :key="i">{{
            x.title
          }}</el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-card style="margin-bottom: 10px;" v-loading="loading1">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据概览</span>
      </div>
      <div>
        <DynamicForm
          ref="baseForm"
          :config="statisticsConfig"
          :params="statisticsForm"
          labelPosition="right"
          :defaultColSpan="12"
        ></DynamicForm>
      </div>
      <div class="statistics-box">
        <div
          v-for="(item, index) in statisticsList"
          :key="index"
          class="statistics-item"
        >
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
              <el-tooltip
                effect="dark"
                :content="item.tooltip"
                placement="top"
                v-if="item.tooltip"
              >
                <i class="el-icon-question"></i>
              </el-tooltip>
              <span class="rate">
                环比<span
                  :style="item.rate > 0 ? 'color: red' : 'color: #469a7d'"
                  ><i
                    :class="item.rate > 0 ? 'el-icon-top' : 'el-icon-bottom'"
                  ></i
                  >{{ Math.abs(item.rate) }}%</span
                ></span
              >
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <el-card style="margin-bottom: 10px;" v-loading="loading2">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>各部门人力成本对比</span>
        <el-tooltip effect="dark" :content="1" placement="top">
          <div slot="content">
            <p>
              差额幅度=（该业务类型的填报工时－该业务的订单制工时/该业务的订单制工时）*100%，单位%，小数点后保留2位数，四舍五入
            </p>
            <p>
              部门的差额幅度=该部门所有业务类型的填报工时－该部门所有业务类型的订单制工时/该部门所有业务类型的订单制工时）*100%，单位%，小数点后保留2位数，四舍五入
            </p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <GroupChart
        :axisData="deptChartObj.xAxis"
        :serieData="deptChartObj.series"
        lineType="bar"
        v-if="deptChartObj.xAxis && deptChartObj.xAxis.length > 0"
        height="400px"
        :isSmooth="true"
        width="calc(100vw - 280px)"
      ></GroupChart>
      <el-empty v-else></el-empty>
    </el-card>
    <el-card style="margin-bottom: 10px;" v-loading="loading3">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>各业务人力成本对比</span>
        <el-tooltip effect="dark" :content="1" placement="top">
          <div slot="content">
            <p>
              差额幅度=（该部门类型的填报工时－该部门的订单制工时/该部门的订单制工时）*100%，单位%，小数点后保留2位数，四舍五入
            </p>
            <p>
              业务类型的差额幅度=该业务类型所有填报工时－该业务类型所有订单制工时/该业务类型所有订单制工时）*100%，单位%，小数点后保留2位数，四舍五入
            </p>
          </div>
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <GroupChart
        :axisData="businessChartObj.xAxis"
        :serieData="businessChartObj.series"
        lineType="bar"
        v-if="businessChartObj.xAxis && businessChartObj.xAxis.length > 0"
        height="400px"
        :isSmooth="true"
        width="calc(100vw - 280px)"
      ></GroupChart>
      <el-empty v-else></el-empty>
    </el-card>
    <el-card style="margin-bottom: 10px;" v-loading="loading4">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>各部门人员统计</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="personConfig"
        :params="personForm"
        labelPosition="right"
        :defaultColSpan="8"
      ></DynamicForm>
      <TwoYaxis
        :axisData="personChartObj.xAxis"
        :serieData="personChartObj.series"
        v-if="personChartObj.xAxis && personChartObj.xAxis.length > 0"
        height="300px"
        unit=""
        lineType="bar"
        width="calc(100vw - 280px)"
      ></TwoYaxis>
      <el-empty v-else></el-empty>
    </el-card>
  </div>
</template>

<script>
import moment from "moment";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { listDept } from "@/api/common.js";
import api from "@/api/ledger/workHours/analysis.js";
import GroupChart from "@/components/Echarts/groupChart.vue";
import TwoYaxis from "@/components/Echarts/twoYaxis.vue";
import { listAllUser } from "@/api/common.js";

export default {
  components: { GroupChart, TwoYaxis },
  data() {
    return {
      form: {
        timeRange: [],
      },
      statisticsForm: { deptId: [], businessType: [] },
      personForm: { deptId: [], businessType: [], userId: [] },
      deptOptions: [],
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      statisticObj: {},
      businessTypeOptions: [],
      personChartObj: {
        xAxis: [],
        series: [],
      },
      businessChartObj: {
        xAxis: [],
        series: [],
      },
      deptChartObj: {
        xAxis: [
          //   { name: "香蕉", type: "水果" },
          //   { name: "苹果", type: "水果" },
          //   { name: "梨", type: "水果" },
          //   { name: "圆珠笔", type: "笔" },
          //   { name: "钢笔", type: "笔" },
        ],
        series: [
          //   {
          //     name: "工单耗时",
          //     data: [100, 120, 124, 130],
          //     showYAxis: true,
          //     yAxisIndex: 0,
          //   },
          //   { name: "工时成本", data: [130, 140, 100, 120] },
          //   {
          //     name: "幅度",
          //     data: [130, 140, 100, 120],
          //     showYAxis: true,
          //     yAxisIndex: 1,
          //     type: "line",
          //   },
        ],
      },
      userOptions: [],
    };
  },
  computed: {
    personConfig() {
      return [
        {
          field: "userId",
          title: "姓名",
          element: "el-select",
          props: {
            options: this.userOptions,
            filterable: true,
            // collapseTags: true,
            multiple: true,
          },
          on: {
            change: (val) => {
              this.getPersonData();
            },
          },
        },
        {
          field: "deptId",
          title: "部门名称",
          element: "el-select",
          props: {
            options: this.deptOptions,
            filterable: true,
            optionLabel: "deptName",
            optionValue: "deptId",
            defaultExpandLevel: 1,
            multiple: true,
          },
          on: {
            input: (val) => {
              this.getPersonData();
            },
          },
        },
        {
          field: "businessType",
          title: "业务类型",
          element: "el-select",
          props: {
            options: this.businessTypeOptions,
            optionValue: "id",
            optionLabel: "typeName",
            filterable: true,
            multiple: true,
          },
          on: {
            change: (val) => {
              this.getPersonData();
            },
          },
        },
      ];
    },
    statisticsConfig() {
      return [
        {
          field: "deptId",
          title: "部门名称",
          element: "el-select",
          props: {
            options: this.deptOptions,
            optionLabel: "deptName",
            optionValue: "deptId",
            filterable: true,
            defaultExpandLevel: 1,
            multiple: true,
          },
          on: {
            input: (val) => {
              this.getStatisticsData();
            },
          },
        },
        {
          field: "businessType",
          title: "业务类型",
          element: "el-select",
          props: {
            options: this.businessTypeOptions,
            filterable: true,
            optionValue: "id",
            optionLabel: "typeName",
            multiple: true,
          },
          on: {
            change: (val) => {
              this.getStatisticsData();
            },
          },
        },
      ];
    },
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
    statisticsList() {
      return [
        {
          title: "填报工时",
          unit: "h",
          value: Number(this.statisticObj?.fillWorkHour ?? 0),
          tooltip:
            "筛选时间、部门、业务类型范围内的所有填报工时总和，环比=筛选条件范围内的填报工时/筛选条件范围内上个周期的填报工时",
          rate: Number(this.statisticObj?.fillWorkHourRatio ?? 0),
        },
        {
          title: "订单制工时",
          unit: "h",
          value: Number(this.statisticObj?.orderWorkHour ?? 0),
          tooltip:
            "筛选时间、部门、业务类型范围内的所有订单制工时总和，环比=筛选条件范围内的订单制工时/筛选条件范围内上个周期的订单制工时",
          rate: Number(this.statisticObj?.orderWorkHourRatio ?? 0),
        },
        {
          title: "人力冗余时间",
          unit: "h",
          value: Number(this.statisticObj?.redundancyTime ?? 0),
          tooltip:
            "筛选时间、部门、业务类型范围内的所有人力冗余时间总和，环比=筛选条件范围内的人力冗余时间/筛选条件范围内上个周期的人力冗余时间",
          rate: Number(this.statisticObj?.redundancyTimeRatio ?? 0),
        },
        {
          title: "冗余人力",
          unit: "",
          value: Number(this.statisticObj?.redundancyHuman ?? 0),
          tooltip:
            "筛选时间、部门、业务类型范围内的所有冗余人力总和，环比=筛选条件范围内的冗余人力/筛选条件范围内上个周期的冗余人力",
          rate: Number(this.statisticObj?.redundancyHumanRatio ?? 0),
        },
      ];
    },
  },
  created() {
    this.form.timeRange = [
      moment()
        .subtract(29, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
    this.getTreeselect();
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    this.listAllUser();
  },
  methods: {
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },

    async getPersonData() {
      const { timeRange } = this.form;
      const params = {
        startDate: timeRange[0],
        endDate: timeRange[1],
        ...this.personForm,
      };
      this.loading4 = true;
      const res = await api.queryPersonData(params).finally((res) => {
        this.loading4 = false;
      });
      this.personChartObj = {
        xAxis: res.data?.map((x) => x.name),
        series: [
          {
            name: "填报工时(h)",
            data: res.data?.map((x) => x.fillWorkHour),
            showYAxis: true,
            yAxisIndex: 0,
            unit: "工时(h)",
          },
          {
            name: "订单制工时(h)",
            data: res.data?.map((x) => x.orderWorkHour),
          },
          {
            name: "差额幅度(%)",
            data: res.data?.map((x) => x.differenceRate),
            showYAxis: true,
            yAxisIndex: 1,
            type: "line",
            unit: "差额幅度(%)",
          },
          {
            name: "人力冗余时间(h)",
            data: res.data?.map((x) => x.redundancyTime),
          },
        ],
      };
    },
    async getStatisticsData() {
      const { timeRange } = this.form;
      const params = {
        startDate: timeRange[0],
        endDate: timeRange[1],
        ...this.statisticsForm,
      };
      const res = await api.queryStatisticsData(params).finally(() => {
        this.loading1 = false;
      });
      this.statisticObj = res.data;
    },
    async getBusinessChartData() {
      const { timeRange } = this.form;
      const params = {
        startDate: timeRange[0],
        endDate: timeRange[1],
      };
      this.loading3 = true;
      const res = await api.queryBusinessChartData(params).finally(() => {
        this.loading3 = false;
      });

      this.businessChartObj = {
        xAxis: this.transformedData(res.data),
        series: [
          {
            name: "填报工时(h)",
            data: this.transformedData(res.data)?.map((x) => x.fillWorkHour),
            showYAxis: true,
            yAxisIndex: 0,
            unit: "工时(h)",
          },
          {
            name: "订单制工时(h)",
            data: this.transformedData(res.data)?.map((x) => x.orderWorkHour),
          },
          {
            name: "差额幅度(%)",
            data: this.transformedData(res.data)?.map((x) => x.rate),
            showYAxis: true,
            yAxisIndex: 1,
            type: "line",
            showLabel: true,
            unit: "差额幅度(%)",
          },
        ],
      };
      console.log("businessChartObj", this.businessChartObj);
    },
    async getDeptChartData() {
      const { timeRange } = this.form;
      const params = {
        startDate: timeRange[0],
        endDate: timeRange[1],
      };
      this.loading2 = true;
      const res = await api.queryDeptChartData(params).finally(() => {
        this.loading2 = false;
      });

      this.deptChartObj = {
        xAxis: this.transformedData(res.data),
        series: [
          {
            name: "填报工时(h)",
            data: this.transformedData(res.data)?.map((x) => x.fillWorkHour),
            showYAxis: true,
            yAxisIndex: 0,
            unit: "工时(h)",
          },
          {
            name: "订单制工时(h)",
            data: this.transformedData(res.data)?.map((x) => x.orderWorkHour),
          },
          {
            name: "差额幅度(%)",
            data: this.transformedData(res.data)?.map((x) => x.rate),
            showYAxis: true,
            yAxisIndex: 1,
            type: "line",
            showLabel: true,
            unit: "差额幅度(%)",
            showMin: true,
          },
        ],
      };
      console.log("deptChartObj", this.deptChartObj);
    },
    loadData() {
      this.getStatisticsData();
      this.getDeptChartData();
      this.getBusinessChartData();
      this.getPersonData();
    },
    transformedData(data) {
      return data.reduce((acc, curr) => {
        const items = curr.dataList.map((item) => ({
          ...item,
          name: item.name,
          type: curr.name,
          groupRate: curr.differ + "%",
        }));
        return [...acc, ...items];
      }, []);
    },
    // //判断是否为正数
    // isPositive(val) {
    //   return /^[+]?\d*\.?\d+%?$/.test(val.toString());
    // },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.flattenArray(response.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto;
  grid-row-gap: 32px;
  grid-column-gap: 32px;
  .statistics-item {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    .rate {
      color: #606266;
      font-size: 12px;
    }
  }
}
/deep/ .el-statistic {
  margin: 20px 0;
  display: flex;
  flex-direction: column-reverse;
  .head {
    color: #469a7d;
    font-size: 16px;
    margin-bottom: 0;
  }
  .con {
    color: #469a7d;
    margin-bottom: 12px;
    .number {
      font-size: 24px;
      font-weight: 500;
    }
  }
}
/deep/ .el-card {
  overflow: visible;
}
</style>
