//数据看板
<template>
  <div>
    <div class="box-menu">
      <el-radio-group
        v-model="activeName"
        style="margin: 20px;"
        size="medium"
        @click="handleJump({ tab: activeName })"
      >
        <el-radio-button
          v-for="(item, index) in realList"
          :key="index"
          :label="item.label"
          >{{ item.title }}</el-radio-button
        >
        <!-- <el-radio-button
          label="detail"
          v-show="checkPermission(['demandPool:dashboard:detail'])"
          >需求明细</el-radio-button
        > -->
      </el-radio-group>
    </div>
    <Overview
      ref="overview"
      v-show="activeName == 'overview'"
      v-has-permi="['ledger:dashboard:overview']"
    ></Overview>
    <DeptTable
      ref="dept"
      v-show="activeName == 'dept'"
      v-has-permi="['ledger:dashboard:dept']"
      @jump="handleJump"
    ></DeptTable>
    <PersonTable
      ref="person"
      v-show="activeName == 'person'"
      v-has-permi="['ledger:dashboard:person']"
      @jump="handleJump"
    ></PersonTable>
    <DetailTable
      ref="detail"
      v-show="activeName == 'detail'"
      v-has-permi="['ledger:dashboard:detail']"
    ></DetailTable>
    <ProcessItemTable
      ref="processItem"
      v-show="activeName == 'processItem'"
      v-has-permi="['ledger:dashboard:processItem']"
      @jump="handleJump"
    ></ProcessItemTable>
  </div>
</template>

<script>
import Overview from "./components/overview";
import DetailTable from "./components/detailTable";
import DeptTable from "./components/deptTable.vue";
import PersonTable from "./components/personTable.vue";
import ProcessItemTable from "./components/processItemTable.vue";
import checkPermission from "@/utils/permission.js";
export default {
  name: "ledgerDashboard",
  components: {
    Overview,
    DetailTable,
    DeptTable,
    PersonTable,
    ProcessItemTable,
  },
  data() {
    return {
      activeName: "overview",
      searchParams: {},
      groupRadioList: [
        {
          label: "overview",
          title: "数据看板",
          show: () => {
            return this.checkPermission(["ledger:dashboard:overview"]);
          },
        },
        {
          label: "dept",
          title: "部门汇总",
          show: () => {
            return this.checkPermission(["ledger:dashboard:dept"]);
          },
        },
        {
          label: "person",
          title: "人员汇总",
          show: () => {
            return this.checkPermission(["ledger:dashboard:person"]);
          },
        },
        {
          label: "detail",
          title: "数据明细",
          show: () => {
            return this.checkPermission(["ledger:dashboard:detail"]);
          },
        },
        {
          label: "processItem",
          title: "自定义工序项统计",
          show: () => {
            return this.checkPermission(["ledger:dashboard:processItem"]);
          },
        },
      ],
    };
  },
  // watch: {
  //   activeName: {
  //     handler(val) {
  //       console.log("activeName", val);
  //       this.$refs[val]?.loadData(this.searchParams[val]);
  //     },
  //   },
  // },
  created() {
    this.realList = this.groupRadioList.filter((x) => !!x.show());
    this.activeName = this.realList[0]?.label || "";
    this.$refs[this.activeName]?.loadData();
  },
  activated() {
    console.log("activated");
    if (this.$route.params.tab) {
      this.handleJump({
        tab: this.$route.params.tab,
        params: this.$route.params,
      });
    }
  },
  mounted() {},
  methods: {
    checkPermission,
    handleJump({ tab = "detail", params }) {
      this.searchParams[tab] = params;
      this.$nextTick(() => {
        this.activeName = tab;
        this.$refs[this.activeName]?.loadData(
          this.searchParams[this.activeName]
        );
      });
    },
  },
};
</script>

<style lang="less" scoped>
.box-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-content {
  padding: 0 20px;
  margin-bottom: 18px;
  /deep/ .el-form {
    display: flex;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 10px;
  }
  /deep/ .el-date-editor .el-range-separator {
    width: 7%;
  }
  /deep/ .el-form-item--small.el-form-item {
    margin-bottom: 0;
    // margin-right: 20px;
    // display: flex;
  }
  /deep/ .vue-treeselect__control {
    table-layout: auto;
  }
  /deep/ .el-date-editor--daterange.el-input__inner {
    width: 250px;
  }
}
</style>
