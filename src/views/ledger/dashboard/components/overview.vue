<template>
  <div>
    <div class="form-content">
      <!-- 筛选项-start -->
      <el-form
        :model="form"
        ref="form"
        label-width="100px"
        style="padding:0 12px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="支持部门">
              <el-select
                v-model="form.supportDept"
                placeholder="支持部门"
                clearable
                multiple
                collapseTags
                style="width: 100%;"
              >
                <el-option
                  v-for="item in supportDeptOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务类型">
              <el-cascader
                ref="businessCascader"
                popperClass="location"
                v-model="form.businessType"
                placeholder="业务类型"
                clearable
                collapseTags
                :props="{
                  checkStrictly: true,
                  multiple: true,
                  value: 'id',
                  label: 'typeName',
                  children: 'childrenList',
                  expandTrigger: 'hover',
                }"
                :options="businessTypeOptions"
                @change="handleBusinessChange"
                style="width: 100%;"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工单类型">
              <el-cascader
                ref="orderCascader"
                popperClass="location"
                v-model="form.orderTypeArr"
                placeholder="工单类型"
                clearable
                collapseTags
                :props="{
                  checkStrictly: true,
                  multiple: true,
                  value: 'id',
                  label: 'typeName',
                  children: 'childrenList',
                  expandTrigger: 'hover',
                }"
                :options="orderTypeOptions"
                @change="handleOrderChange"
                style="width: 100%;"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="22">
            <el-form-item label="时间" prop="timeRange">
              <el-date-picker
                v-model="form.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
                value-format="yyyy-MM-dd"
                @change="loadData"
              ></el-date-picker>
              <el-radio-group v-model="form.timeRange" @change="loadData">
                <el-radio-button
                  v-for="(x, i) in timeArr"
                  :label="x.date"
                  :key="i"
                  >{{ x.title }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button
              type="primary"
              @click="loadData"
              icon="el-icon-search"
              style="float: right;margin-right:4px"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <!-- 筛选项-end -->
    </div>
    <!-- 数据概览-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading1">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据概览</span>
      </div>
      <div class="statistics-box">
        <div
          v-for="(item, index) in statisticsList"
          :key="index"
          class="statistics-item"
        >
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
              <el-tooltip effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <!-- 数据概览-end -->
    <!-- 工单完成趋势-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading2">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单完成趋势</span>
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <p>
              每日完成率：（当日创建的工单/当日处理完成的工单）*100%
            </p>
            <p>每日完成：当日处理完成的工单</p>
            <p>每日新增：当日新增的工单数量</p>
          </div>
          <i class="el-icon-question ml5"></i>
        </el-tooltip>
      </div>
      <LineChart
        :axisData="handleTend.time"
        :serieData="handleTend.tendencyArr"
        lineType="line"
        v-if="handleTend.time && handleTend.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 工单完成趋势-end -->
    <!-- 业务类型-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading3">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>业务类型TOP 10</span>
      </div>
      <LineChart
        :axisData="businessTypeObj.time"
        :serieData="businessTypeObj.tendencyArr"
        lineType="bar"
        v-if="businessTypeObj.time && businessTypeObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 业务类型-end -->
    <!-- 工单类型-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading4">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单类型TOP 10</span>
      </div>
      <LineChart
        :axisData="orderTypeObj.yAxis"
        :serieData="orderTypeObj.series"
        lineType="bar"
        :isHorizontalBar="true"
        v-if="orderTypeObj.yAxis && orderTypeObj.yAxis.length > 0"
        :lineColor="['#dabf71']"
        height="500px"
        :showLegend="false"
        showLabel
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 工单类型-end -->
    <div style="display: flex;">
      <!-- 支持部门工单占比-start -->
      <el-card
        style="margin-bottom: 10px;flex:1;margin-right: 10px;"
        v-loading="loading5"
      >
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>支持部门工单占比</span>
        </div>
        <PieChartSolid
          :list="deptPieList"
          v-if="deptPieList && deptPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 支持部门工单占比-end -->
      <!-- 工单来源占比-start -->
      <el-card style="margin-bottom: 10px;flex:1;" v-loading="loading6">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>工单来源占比</span>
        </div>
        <PieChartSolid
          :list="originPieList"
          :pieRadius="['47%', '70%']"
          v-if="originPieList && originPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 工单来源占比-end -->
    </div>
    <div style="display: flex;">
      <!-- 超时工单占比-start -->
      <el-card
        style="margin-bottom: 10px;flex:1;margin-right: 10px;"
        v-loading="loading7"
      >
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>超时工单占比</span>
        </div>
        <PieChartSolid
          :list="exceedPieList"
          v-if="exceedPieList && exceedPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 超时工单占比-end -->
      <!-- 紧急程度-start -->
      <el-card style="margin-bottom: 10px;flex:1;" v-loading="loading8">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>紧急程度</span>
        </div>
        <PieChartSolid
          :list="urgencyPieList"
          :pieRadius="['47%', '70%']"
          v-if="urgencyPieList && urgencyPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 紧急程度-end -->
    </div>
  </div>
</template>

<script>
import LineChart from "@/components/Echarts/LineChart.vue";
import moment from "moment";
import PieChartSolid from "@/components/Echarts/pieChartSolid.vue";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import listApi from "@/api/ledger/index.js";
import api from "@/api/ledger/dashboard.js";
export default {
  components: { LineChart, PieChartSolid },
  data() {
    return {
      statisticObj: {},
      form: {
        supportDept: "",
        orderTypeArr: [],
        businessType: [],
        timeRange: [],
      },
      businessTypeOptions: [],
      supportDeptOptions: [],
      orderTypeOptions: [],
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      loading5: false,
      loading6: false,
      loading7: false,
      loading8: false,
      handleTend: { time: [], tendencyArr: [] },
      businessTypeObj: { time: [], tendencyArr: [] },
      orderTypeObj: {},
      deptPieList: [],
      originPieList: [],
      exceedPieList: [],
      urgencyPieList: [],
    };
  },
  computed: {
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD 00:00:00"),
            moment().format("YYYY-MM-DD 23:59:59"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
    statisticsList() {
      return [
        {
          title: "累计工单",
          unit: "个",
          value: this.statisticObj?.totalCount ?? 0,
          tooltip: "所有状态的工单总数",
        },
        {
          title: "处理中",
          unit: "个",
          value: this.statisticObj?.handlingCount ?? 0,
          tooltip: "处理中状态的工单总数",
        },
        {
          title: "已完成",
          unit: "个",
          value: this.statisticObj?.finishedCount ?? 0,
          tooltip: "已完成状态的工单总数",
        },
        {
          title: "已完结",
          unit: "个",
          value: this.statisticObj?.endedCount ?? 0,
          tooltip: "已完结状态的工单总数",
        },
        {
          title: "已作废",
          unit: "个",
          value: this.statisticObj?.cancelledCount ?? 0,
          tooltip: "已作废状态的工单总数",
        },
        {
          title: "草稿",
          unit: "个",
          value: this.statisticObj?.draftCount ?? 0,
          tooltip: "草稿状态的工单总数",
        },
        {
          title: "平均单日新增工单",
          unit: "个",
          value: this.statisticObj?.avgDailyNewCount ?? 0,
          tooltip:
            "时间筛选范围内，工单总数/天数，取整数，四舍五入，单位【个】",
        },
        {
          title: "加单工时",
          unit: "h",
          value: this.statisticObj?.addOrderTime ?? 0,
          tooltip: "时间筛选范围内，加单的累计工时，单位【h】",
        },
        {
          title: "累计工单工时",
          unit: "h",
          value: this.statisticObj?.totalOrderTime ?? 0,
          tooltip:
            "时间筛选范围内，所有工单的工单总工时+加单总工时之和，单位【h】",
        },
        {
          title: "完成率",
          unit: "%",
          value: this.statisticObj?.finishPercent ?? 0,
          tooltip:
            "（已完成+已完结+已作废）状态的工单数/（已完成+已完结+已作废+处理中）状态的工单数*100%，小数点后保留一位，四舍五入，单位为【%】",
        },
        {
          title: "及时率",
          unit: "%",
          value: this.statisticObj?.onTimePercent ?? 0,
          tooltip:
            "及时率=未超时工单数/工单总数*100%，未超时工单指的是工单列表的工单是否超时标记为【否】的工单；及时率小数点后保留一位，四舍五入，单位为【%】",
        },
        {
          title: "工单价格",
          unit: "元",
          value: this.statisticObj?.orderPrice ?? 0,
          tooltip: "所有工单的工单价格总和",
        },
      ];
    },
  },
  async created() {
    this.getDicts("support_dept").then((response) => {
      this.supportDeptOptions = response.data;
    });
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    queryDeptOrderTree({}).then((res) => {
      this.orderTypeOptions = res.data?.map((x) => {
        return { ...x, disabled: true };
      });
    });
    this.form.timeRange = [
      moment().format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
    this.loadData();
  },
  methods: {
    handleOrderChange() {
      this.$refs.orderCascader.dropDownVisible = false;
    },
    handleBusinessChange(val) {
      this.$refs.businessCascader.dropDownVisible = false;
      // this.form.orderTypeArr = [];
      // const len = this.form.businessType?.length || 0;
      // if (len > 0) {
      //   listApi
      //     .queryOrderOptionsByBusiness({
      //       supportDept: this.form.supportDept,
      //       businessTypeId: val[len - 1],
      //     })
      //     .then((res) => {
      //       this.orderTypeOptions = res.data;
      //     });
      // }
    },

    //获取业务类型
    // getBusinessOptions(val) {
    //   listApi.queryBusinessByDept({ supportDept: val }).then((res) => {
    //     this.businessTypeOptions = res.data;
    //   });
    // },
    // handleDeptChange(val) {
    //   this.form.orderTypeArr = [];
    //   //根据部门获取业务类型下拉选项
    //   this.form.businessType = [];
    //   this.getBusinessOptions(val);
    // },

    // 处理多选级联选择器数据
    processMultiCascaderData(cascaderData) {
      const result = {
        level1: [],
        level2: [],
        level3: [],
        level4: [],
      };

      // 如果数据为空，直接返回空结果
      if (!cascaderData) {
        return result;
      }

      // 处理旧数据格式（单选模式下可能是字符串或数字）
      if (!Array.isArray(cascaderData)) {
        // 如果是字符串或数字，将其视为第一级的选择
        result.level1.push(cascaderData);
        return result;
      }

      // 处理单个数组（非嵌套数组）的情况
      if (
        Array.isArray(cascaderData) &&
        cascaderData.length > 0 &&
        !Array.isArray(cascaderData[0])
      ) {
        // 如果是单个数组（如 [1, 2, 3]），将其视为一个完整的路径
        if (cascaderData.length >= 1) {
          result.level1.push(cascaderData[0]);
        }
        if (cascaderData.length >= 2) {
          result.level2.push(cascaderData[1]);
        }
        if (cascaderData.length >= 3) {
          result.level3.push(cascaderData[2]);
        }
        if (cascaderData.length >= 4) {
          result.level4.push(cascaderData[3]);
        }
        return result;
      }

      // 处理多选数据格式（嵌套数组）
      cascaderData.forEach((path) => {
        if (Array.isArray(path)) {
          // 根据路径长度确定层级
          if (path.length >= 1) {
            result.level1.push(path[0]);
          }
          if (path.length >= 2) {
            result.level2.push(path[1]);
          }
          if (path.length >= 3) {
            result.level3.push(path[2]);
          }
          if (path.length >= 4) {
            result.level4.push(path[3]);
          }
        }
      });

      return result;
    },

    loadData() {
      const { businessType, orderTypeArr, supportDept,...rest } = this.form;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      let params = {
        ...this.rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
      };
      if (this.form.timeRange?.length > 0) {
        params["startTime"] = moment(this.form.timeRange[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        params["endTime"] = moment(this.form.timeRange[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }
      this.loading1 = true;
      //数据概览
      api
        .queryOrderData(params)
        .then((res1) => {
          this.loading1 = false;
          if (res1?.code == "10000") {
            this.statisticObj = res1.data;
          }
        })
        .catch(() => {
          this.loading1 = false;
        });

      //需求单完成趋势
      api
        .queryHandleTend(params)
        .then((res2) => {
          this.loading2 = false;
          if (res2?.code !== "10000") return;
          this.handleTend = {
            time: res2.data?.map((item) => item.time),
            tendencyArr: [
              {
                name: "每日新增(个)",
                data: res2.data?.map((item) => item.createCount),
              },
              {
                name: "每日完成(个)",
                data: res2.data?.map((item) => item.completeCount),
              },
              {
                name: "每日完成率(%)",
                data: res2.data?.map((item) => item.completePercent),
              },
            ],
          };
        })
        .catch(() => {
          this.loading2 = false;
        });
      //业务类型
      api
        .queryBusinessTypeRank(params)
        .then((res3) => {
          this.loading3 = false;
          if (res3?.code !== "10000") return;
          this.businessTypeObj = {
            time: res3.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "工单数",
                data: res3.data?.map((x) => x.count),
              },
            ],
          };
        })
        .catch(() => {
          this.loading3 = false;
        });
      //工单类型
      api
        .queryOrderTypeRank(params)
        .then((res4) => {
          const { code, data } = res4;
          this.loading4 = false;
          if (code !== "10000") return;
          this.orderTypeObj = {
            yAxis: data?.map((x) => x.name)?.reverse(),
            series: [
              {
                name: "工单数",
                data: data?.map((x) => x.count)?.reverse(),
              },
            ],
          };
        })
        .catch(() => {
          this.loading4 = false;
        });
      //支持部门工单占比
      api
        .queryDeptPieList(params)
        .then((res5) => {
          this.loading5 = false;
          if (res5?.code !== "10000") return;
          this.deptPieList = res5.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading5 = false;
        });
      //工单来源占比
      api
        .queryOriginPieList(params)
        .then((res6) => {
          this.loading6 = false;
          if (res6?.code !== "10000") return;
          this.originPieList = res6.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading6 = false;
        });
      //超时工单占比
      api
        .queryExceedPieList(params)
        .then((res7) => {
          this.loading7 = false;
          if (res7?.code !== "10000") return;
          this.exceedPieList = res7.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading7 = false;
        });
      //紧急程度
      api
        .queryUrgencyPieList(params)
        .then((res8) => {
          this.loading8 = false;
          if (res8?.code !== "10000") return;
          this.urgencyPieList = res8.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading8 = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.form-content {
  padding: 0 20px;
  margin-bottom: 18px;
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto;
  grid-row-gap: 32px;
  grid-column-gap: 32px;
  .statistics-item {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
/deep/ .el-statistic {
  margin: 20px 0;
  display: flex;
  flex-direction: column-reverse;
  .head {
    color: #469a7d;
    font-size: 14px;
    margin-bottom: 0;
  }
  .con {
    color: #469a7d;
    margin-bottom: 12px;
    .number {
      font-size: 24px;
      font-weight: 500;
    }
  }
}
</style>
