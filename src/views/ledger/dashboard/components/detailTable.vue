// 数据明细
<template>
  <div style="padding: 0 20px;">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
      tabType="card"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ledger:dashboard:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="orderProcess" slot-scope="{ row }">
        <el-progress :percentage="row.progress"></el-progress>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { initParams } from "@/utils/buse.js";
import { queryTreeList } from "@/api/ledger/businessType.js";

import { listAllUser, queryCityTree } from "@/api/common.js";

import api from "@/api/ledger/dashboard.js";
import comApi from "@/api/ledger/company.js";
import listApi from "@/api/ledger/index.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import checkPermission from "@/utils/permission.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";

export default {
  components: {},
  mixins: [exportMixin],
  props: {
    // searchParams: {
    //   type: Object,
    //   default: () => {},
    // },
  },
  data() {
    return {
      token: "",
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "projectBatchId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
        cellStyle: this.cellStyle,
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      businessTypeOptions: [],
      originOptions: [],
      supportDeptOptions: [],
      urgencyDegreeOptions: [],
      statusOptions: [],
      orderTypeOptions: [],
      processColumns: [],
      userOptions: [],
      orderEndTypeOptions: [],
      orderPropOptions: [],
      addCheckStatusOptions: [],
    };
  },
  created() {
    this.token = getToken();
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    this.listAllUser();
    this.getCityRegionData();
    queryDeptOrderTree({}).then((res) => {
      this.orderTypeOptions = res.data?.map((x) => {
        return { ...x, disabled: true };
      });
    });
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
  },
  mounted() {
    Promise.all([
      this.getDicts("support_dept").then((response) => {
        this.supportDeptOptions = response.data;
      }),
      //工单来源
      this.getDicts("ledger_demand_source").then((response) => {
        this.originOptions = response.data;
      }),
      //工单状态
      this.getDicts("ledger_order_status").then((response) => {
        this.statusOptions = response.data;
      }),
      // this.getUrgencyOptions(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  computed: {
    modalConfig() {
      return { menu: false, addBtn: false };
    },
    tableColumn() {
      return [
        {
          field: "orderNo",
          title: "工单单号",
          width: 180,
        },
        {
          field: "supportDeptName",
          title: "支持部门",
          width: 100,
        },
        {
          field: "businessTypeStr",
          title: "业务类型",
          width: 150,
        },
        {
          field: "orderTypeStr",
          title: "工单类型",
          width: 150,
        },
        {
          field: "demandSourceName",
          title: "工单来源",
          width: 100,
        },
        {
          field: "activeType",
          title: "活动类型",
          width: 100,
        },
        {
          field: "urgencyLevelName",
          title: "紧急程度",
          width: 100,
        },
        {
          field: "orderDesc",
          title: "问题描述",
          width: 100,
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 100,
        },
        {
          field: "regionStr",
          title: "省市区",
          width: 150,
        },
        {
          field: "companyName",
          title: "公司名称",
          width: 100,
        },
        {
          field: "companyCategory",
          title: "公司类别",
          width: 100,
        },
        {
          field: "companyAttribute",
          title: "公司属性",
          width: 100,
        },
        {
          field: "createUserName",
          title: "提交人",
          width: 100,
        },
        {
          field: "createTime",
          title: "提交时间",
          width: 150,
        },
        {
          field: "orderStatusName",
          title: "工单状态",
          width: 100,
        },
        {
          field: "finishWay",
          title: "工单完成方式",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue == 1
              ? "正常结束"
              : cellValue == 4
              ? "作废"
              : cellValue == 5
              ? "手动完结"
              : "";
          },
        },
        {
          field: "handleUserName",
          title: "当前处理人",
          width: 100,
        },
        {
          field: "finishTime",
          title: "处理时间",
          width: 100,
        },
        {
          field: "progress",
          title: "工单进度",
          slots: { default: "orderProcess" },
          width: 150,
        },
        {
          field: "totalNode",
          title: "流程总节点",
          width: 100,
        },
        {
          field: "nodeIsTimeout",
          title: "流程节点是否超时",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{
                    color:
                      row.nodeIsTimeout === "Y"
                        ? "red"
                        : row.nodeIsTimeout === "N"
                        ? "green"
                        : "",
                  }}
                >
                  {row.nodeIsTimeout === "Y"
                    ? "是"
                    : row.nodeIsTimeout === "N"
                    ? "否"
                    : row.nodeIsTimeout}
                </div>,
              ];
            },
          },
        },
        {
          field: "duration",
          title: "工单耗时",
          width: 150,
        },
        {
          field: "orderIsTimeout",
          title: "工单是否超时",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{
                    color:
                      row.orderIsTimeout === "Y"
                        ? "red"
                        : row.orderIsTimeout === "N"
                        ? "green"
                        : "",
                  }}
                >
                  {row.orderIsTimeout === "Y"
                    ? "是"
                    : row.orderIsTimeout === "N"
                    ? "否"
                    : ""}
                </div>,
              ];
            },
          },
        },
        {
          field: "timeOutDuration",
          title: "超时时长",
          width: 150,
        },
        {
          field: "totalOrderTime",
          title: "工单总工时（h）",
          width: 150,
        },
        {
          field: "totalAddTime",
          title: "加单总工时（h）",
          width: 150,
        },
        {
          field: "auditResult",
          title: "加单审核状态",
          width: 150,
          formatter: ({ cellValue }) => {
            if (cellValue == 0) {
              return "待审核";
            } else if (cellValue == 1) {
              return "审核通过";
            } else if (cellValue == 2) {
              return "审核不通过";
            }
          },
        },
        {
          field: "addReason",
          title: "加单原因",
          width: 100,
        },
        {
          field: "remark",
          title: "备注",
          width: 100,
        },
        {
          field: "orderAttributes",
          title: "工单属性",
          width: 100,
          formatter: ({ cellValue }) => {
            if (cellValue == 1) {
              return "标准需求";
            } else if (cellValue == 2) {
              return "非标准需求";
            }
          },
        },
        {
          field: "orderPrice",
          title: "工单价格",
          width: 100,
          titlePrefix: {
            message: `工单价格=该工单所属部门设置的时薪报价×（工单总工时+加单工时）`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "finishRemark",
          title: "完成方式说明",
          width: 100,
        },
        {
          field: "process",
          title: "流程处理详情",
          children: this.processColumns,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "orderNo",
            element: "el-input",
            title: "工单单号",
          },
          // {
          //   field: "mainOrderNo",
          //   element: "el-input",
          //   title: "主工单单号",
          // },
          {
            field: "activeType",
            element: "el-input",
            title: "活动类型",
          },
          {
            field: "orderIsTimeout",
            title: "工单是否超时",
            element: "el-select",
            props: {
              options: [
                { value: "Y", label: "是" },
                { value: "N", label: "否" },
              ],
            },
          },
          {
            field: "supportDept",
            title: "所属部门",
            element: "el-select",
            props: {
              options: this.supportDeptOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
              multiple: true,
              collapseTags: true,
            },
            on: {
              // change: this.getOrderTypeOptions,
            },
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "el-cascader",
            ref: "businessCascader",
            props: {
              popperClass: "location",
              collapseTags: true,
              props: {
                expandTrigger: "hover",
                checkStrictly: true,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.businessTypeOptions,
            },
            on: {
              change: () => {
                // this.params.orderTypeArr = undefined;
                this.$refs.crud.$refs.autoFilters.$refs.businessCascader.dropDownVisible = false;
              },
            },
          },
          {
            field: "orderTypeArr",
            title: "工单类型",
            element: "el-cascader",
            ref: "orderCascader",
            props: {
              popperClass: "location",
              collapseTags: true,
              props: {
                expandTrigger: "hover",
                checkStrictly: true,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              options: this.orderTypeOptions,
            },
            on: {
              change: () => {
                this.$refs.crud.$refs.autoFilters.$refs.orderCascader.dropDownVisible = false;
              },
            },
          },
          {
            field: "orderStatus",
            title: "工单状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "createUserName",
            element: "el-input",
            title: "提交人",
          },
          {
            field: "nodeIsTimeout",
            title: "流程节点是否超时",
            element: "el-select",
            props: {
              options: [
                { value: "Y", label: "是" },
                { value: "N", label: "否" },
              ],
            },
          },
          {
            field: "handleUserName",
            element: "el-input",
            title: "处理人",
          },
          {
            field: "demandSource",
            title: "工单来源",
            element: "el-select",
            props: {
              options: this.originOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "submitTime",
            title: "提交时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "companyName",
            title: "公司名称",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryList");
              },
            },
          },
          {
            field: "urgencyLevel",
            title: "紧急程度",
            element: "el-select",
            props: {
              options: this.urgencyDegreeOptions,
              optionLabel: "urgencyName", //自定义选项名
              optionValue: "urgencyId", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "region",
            title: "省市区",
            element: "custom-cascader",
            attrs: {
              clearable: true,
              collapseTags: true,
              props: {
                checkStrictly: true,
                multiple: true,
                // value: "areaCode",
                // label: "areaName",
              },
              options: regionData,
            },
          },
          {
            field: "finishTime",
            title: "完成时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "companyCategory",
            title: "公司类别",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryCompanyType");
              },
            },
          },
          {
            field: "companyAttribute",
            title: "公司属性",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryCompanyAttrs");
              },
            },
          },
          {
            field: "auditResult",
            title: "加单审核状态",
            element: "el-select",
            props: {
              options: [
                { value: "0", label: "待审核" },
                { value: "1", label: "审核通过" },
                { value: "2", label: "审核不通过" },
              ],
            },
          },
          {
            field: "addTime",
            title: "加单时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "allHandleUsers",
            title: "节点处理人包含",
            element: "el-select",
            props: {
              options: this.userOptions,
              filterable: true,
              multiple: true,
            },
            defaultValue: [],
          },
          {
            field: "orderAttributes",
            title: "工单属性",
            element: "el-select",
            props: {
              options: [
                { value: "1", label: "标准需求" },
                { value: "2", label: "非标准需求" },
              ],
            },
          },
          {
            field: "finishWay",
            title: "工单完成方式",
            element: "el-select",
            props: {
              options: [
                { value: "1", label: "正常结束" },
                { value: "4", label: "作废" },
                { value: "5", label: "手动完结" },
              ],
            },
          },
          {
            field: "handleTime",
            title: "节点处理时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },
  },
  watch: {
    // searchParams: {
    //   handler(val) {
    //     this.params = { ...initParams(this.filterOptions.config), ...val };
    //     console.log("searchParams", this.params);
    //     this.loadData();
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    // "params.supportDept": {
    //   handler(val) {
    //     if (val) {
    //       this.getBusinessOptions(val);
    //     } else {
    //       this.orderTypeOptions = [];
    //       this.businessTypeOptions = [];
    //       this.urgencyDegreeOptions = [];
    //     }
    //   },
    // },
    // "params.businessType": {
    //   handler(val) {
    //     const len = val?.length || 0;
    //     if (len > 0) {
    //       listApi
    //         .queryOrderOptionsByBusiness({
    //           supportDept: this.params.supportDept,
    //           businessTypeId: val[len - 1],
    //         })
    //         .then((res) => {
    //           this.orderTypeOptions = res.data;
    //         });
    //       this.getUrgencyOptions({
    //         supportDept: this.params.supportDept,
    //         businessType: val[0],
    //       });
    //     } else {
    //       this.orderTypeOptions = [];
    //       this.urgencyDegreeOptions = [];
    //     }
    //   },
    // },
  },
  methods: {
    // 处理多选级联选择器数据
    processMultiCascaderData(cascaderData) {
      const result = {
        level1: [],
        level2: [],
        level3: [],
        level4: [],
      };

      // 如果数据为空，直接返回空结果
      if (!cascaderData) {
        return result;
      }

      // 处理旧数据格式（单选模式下可能是字符串或数字）
      if (!Array.isArray(cascaderData)) {
        // 如果是字符串或数字，将其视为第一级的选择
        result.level1.push(cascaderData);
        return result;
      }

      // 处理单个数组（非嵌套数组）的情况
      if (
        Array.isArray(cascaderData) &&
        cascaderData.length > 0 &&
        !Array.isArray(cascaderData[0])
      ) {
        // 如果是单个数组（如 [1, 2, 3]），将其视为一个完整的路径
        if (cascaderData.length >= 1) {
          result.level1.push(cascaderData[0]);
        }
        if (cascaderData.length >= 2) {
          result.level2.push(cascaderData[1]);
        }
        if (cascaderData.length >= 3) {
          result.level3.push(cascaderData[2]);
        }
        if (cascaderData.length >= 4) {
          result.level4.push(cascaderData[3]);
        }
        return result;
      }

      // 处理多选数据格式（嵌套数组）
      cascaderData.forEach((path) => {
        if (Array.isArray(path)) {
          // 根据路径长度确定层级
          if (path.length >= 1) {
            result.level1.push(path[0]);
          }
          if (path.length >= 2) {
            result.level2.push(path[1]);
          }
          if (path.length >= 3) {
            result.level3.push(path[2]);
          }
          if (path.length >= 4) {
            result.level4.push(path[3]);
          }
        }
      });

      return result;
    },

    cellStyle({ column }) {
      if (column?.field) {
        const field = column.field;
        // 分割字段名以获取下划线后的数字
        const parts = field.split("_");
        if (parts.length > 1) {
          const numStr = parts[parts.length - 1]; // 取最后一段
          const num = parseInt(numStr, 10);
          if (!isNaN(num)) {
            // 动态生成HSL颜色，确保不同数字颜色不同
            const hue = (num * 30) % 360; // 调整乘数改变颜色间隔
            return {
              backgroundColor: `hsl(${hue}, 70%, 85%)`, // 柔和色调
            };
          }
        }
      }
      return {}; // 默认无样式
    },

    getUrgencyOptions(params) {
      api
        .getUrgencyDegree({
          pageSize: 99999,
          pageNum: 1,
          status: 0,
          ...params,
        })
        .then((res) => {
          this.urgencyDegreeOptions = res.data;
        });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    // getOrderTypeOptions(val) {
    //   this.params.orderTypeArr = undefined;
    //   this.params.businessType = undefined;
    //   // if (val) {
    //   //   this.getBusinessOptions(val);
    //   // } else {
    //   //   this.orderTypeOptions = [];
    //   // }
    // },
    querySearch(queryString, cb, api) {
      comApi[api]({
        name: queryString,
        companyName: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.companyName || x };
        });
        cb(result);
      });
    },
    initProcessColumn(maxCount) {
      this.processColumns = [];
      //列表固定3个节点信息
      for (let i = 1; i <= maxCount; i++) {
        this.processColumns.push({
          field: "node_" + i,
          title: "节点" + i,
          children: [
            { field: "nodeName_" + i, title: "节点名称", width: 100 },
            { field: "handleUserName_" + i, title: "处理人", width: 100 },
            {
              field: "handleStatus_" + i,
              title: "处理状态",
              width: 100,
              formatter: ({ cellValue }) => {
                return cellValue === "Y"
                  ? "已处理"
                  : cellValue === "N"
                  ? "处理中"
                  : cellValue;
              },
            },
            { field: "handlePlanTime_" + i, title: "应处理时间", width: 200 },
            { field: "handleRealTime_" + i, title: "实际处理时间", width: 200 },
            { field: "ygRealTime_" + i, title: "实际计算时间", width: 200 },
            { field: "handleDuration_" + i, title: "节点耗时", width: 150 },
            {
              field: "timeOutDuration_" + i,
              title: "节点超时时长",
              width: 150,
            },
            { field: "validTime_" + i, title: "节点工序工时(h)", width: 150 },
            { field: "addOrderTime_" + i, title: "加单工时(h)", width: 100 },
            { field: "dealForms_" + i, title: "节点处理信息", width: 150 },
            // { field: "backTimes_" + i, title: "被驳回次数", width: 100 },
          ],
        });
      }
      console.log(this.processColumns, "processColumns");
    },
    checkPermission,
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        // this.regionData = this.cleanTree(res.data);
        this.regionData = res.data;
      });
    },
    async loadData(searchParams) {
      if (searchParams) {
        this.params = {
          ...initParams(this.filterOptions.config),
          ...searchParams,
        };
      }
      const { businessType, orderTypeArr, supportDept, ...rest } = this.params;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      console.log(params, "params");
      this.loading = true;
      const res = await api.getTableData(params);
      // const res = {
      //   total: 20,
      //   data: [
      //     {
      //       orderNo: "111111",
      //       processList: [
      //         { nodeName: "aaa", assignee: "处理人小a" },
      //         { nodeName: "bbb", assignee: "处理人小b" },
      //         { nodeName: "ccc", assignee: "处理人小c" },
      //         { nodeName: "ddd", assignee: "处理人小d" },
      //       ],
      //     },
      //     {
      //       orderNo: "222222",
      //       processList: [
      //         { nodeName: "ccc", assignee: "处理人小c" },
      //         { nodeName: "ddd", assignee: "处理人小d" },
      //       ],
      //     },
      //   ],
      // };
      //需要的结构：
      // [
      //   {
      //     name: "工单号",
      //     nodeName_1: "aaa",
      //     assignee_1: "处理人小a",
      //     nodeName_2: "bbb",
      //     assignee_2: "处理人小b",
      //   },
      // ];
      this.loading = false;
      const maxCount = res.data?.reduce((max, item) => {
        return Math.max(max, item.processList?.length || 0);
      }, 0);
      console.log(maxCount);
      this.initProcessColumn(maxCount);
      this.tableData = res.data?.map((x) => {
        x.processList?.forEach((process, index) => {
          for (const key in process) {
            if (process.hasOwnProperty(key)) {
              x[`${key}_${index + 1}`] = process[key];
            }
          }
        });
        return { ...x };
      });
      this.tablePage.total = res.total;
    },
    handleExport() {
      const { businessType, orderTypeArr, supportDept, ...rest } = this.params;

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "submitTime",
          title: "提交时间",
          startFieldName: "createStartTime",
          endFieldName: "createEndTime",
        },
        {
          field: "finishTime",
          title: "完成时间",
          startFieldName: "finishStartTime",
          endFieldName: "finishEndTime",
        },
        {
          field: "addTime",
          title: "加单时间",
          startFieldName: "addStartTime",
          endFieldName: "addEndTime",
        },
        {
          field: "handleTime",
          title: "节点处理时间",
          startFieldName: "handleStartTime",
          endFieldName: "handleEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
