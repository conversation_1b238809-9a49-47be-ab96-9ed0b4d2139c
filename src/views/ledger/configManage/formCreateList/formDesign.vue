<!-- 表单设计器 -->
<template>
  <div>
    <el-row style="padding: 20px 20px 0 20px;">
      <el-form :model="form" ref="form" :rules="rules" label-width="100px">
        <el-col :span="8">
          <el-form-item label="表单名称" prop="formTitle">
            <el-input
              v-model="form.formTitle"
              placeholder="请输入具体的表单名称，长度1000个字符以内"
              maxlength="1000"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="表单类别" prop="formType">
            <el-radio-group v-model="form.formType">
              <el-radio label="01">创建表单</el-radio>
              <el-radio label="02">处理表单</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="表单属性" prop="formAttr">
            <el-autocomplete
              v-model="form.formAttr"
              clearable
              style="width: 100%"
              :fetch-suggestions="querySearch"
            >
            </el-autocomplete>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-row type="flex" justify="end" style="padding: 0 20px 20px 20px;">
      <el-button @click="formSave" type="primary"> 保存</el-button>
      <el-button @click="handleBack"> 取消</el-button>
    </el-row>
    <fc-designer ref="designer" height="780px" :mask="false"></fc-designer>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  formSave,
  getFormData,
  getAppUploadPath,
  queryPropOptions,
  formAdd,
  formUpdate,
} from "@/api/ledger/formDesign.js";
//自定义电子签名组件生成规则
import { signboard } from "@/components/FormCreate/rules/signBoardCreateRule";
import { cameraPhotoUpload } from "@/components/FormCreate/rules/CameraPhotoUploadCreateRule";
import { processTableDesigner } from "@/components/FormCreate/rules/ProcessTableRule.js";
import { locate } from "@/components/FormCreate/rules/LocateRule";
import { formGroup } from "@/components/FormCreate/rules/FormGroupRule";
import { formRow } from "@/components/FormCreate/rules/FormRowRule";
import { multiFileUpload } from "@/components/FormCreate/rules/MultiFileUploadCreateRule";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      rules: {
        formType: [
          { required: true, message: "请选择表单类别", trigger: "change" },
        ],
        formTitle: [
          { required: true, message: "请输入表单名称", trigger: "blur" },
        ],
      },
      form: {
        formTitle: "",
        formType: "01",
        formAttr: "",
      },
      dialogTitle: "", // 对话框标题
      dialogState: false, // 对话框状态
      dialogMenu: false, // 对话框菜单状态

      foptions: {},
      frule: [],

      // codemirror配置
      codemirrorOptions: {
        mode: "application/json",
        theme: "default",
        gutters: ["CodeMirror-lint-markers"],
        tabSize: 2,
        lint: true,
        line: true,
        lineNumbers: true,
        matchBrackets: true,
        lineWrapping: true,
        styleActiveLine: true,
        readOnly: false,
      },
      // codemirror内容
      codemirrorContent: null,
      actionUrl: "", //图片上传地址
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    querySearch(queryString, cb) {
      queryPropOptions({
        itemAttributeName: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    //获取表单数据
    getFormData() {
      let params = {
        formKey: this.$route.query.formKey,
      };
      getFormData(params).then((res) => {
        this.form = {
          formTitle: res.data.formTitle,
          formType: res.data.formType,
          formAttr: res.data.formAttr,
        };
        this.foptions = JSON.parse(res.data.formConfig);
        this.frule = JSON.parse(res.data.formJson);
        //回显数据
        this.$refs.designer.setRule(this.frule);
        this.$refs.designer.setOption(this.foptions);
      });
    },
    delVisitedView() {
      this.$store.dispatch("tagsView/delVisitedView", this.$route);
    },
    handleBack() {
      this.delVisitedView();
      this.$router.back();
    },

    //保存表单
    formSave() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return;
        this.handleDownloadRule();
        this.handleDownloadOption();
        // 校验工序项表格组件必填
        let rules = this.$refs.designer.getRule();
        rules.map((x) => {
          if (x.type === "ProcessTableDesigner") {
            const isValid = x.value?.every((item) => {
              return (
                !!item.processName &&
                item.weekdaysTime !== undefined &&
                item.weekdaysTime !== "" &&
                item.weekdaysTime !== null
              );
            });
            if (!isValid) {
              this.$message.error(
                "工序配置表的工序名称、工作日标准工时不能为空！"
              );
              throw new Error("工序配置表的工序名称、工作日标准工时不能为空！");
            }
          }
        });

        let params = {
          ...this.form,
          formJson: this.handleDownloadRule(),
          formConfig: this.handleDownloadOption(),
        };
        if (this.$route.query.isNew) {
          formAdd(params).then((res) => {
            if (res?.code === "10000") {
              this.$message.success("保存成功！");
              this.delVisitedView();
              this.$router.back();
            }
          });
        } else {
          params.formKey = this.$route.query.formKey;
          formUpdate(params).then((res) => {
            if (res?.code === "10000") {
              this.$message.success("保存成功！");
              this.delVisitedView();
              this.$router.back();
            }
          });
        }
      });
    },

    //获取表单图片上传地址
    async getAppUploadPath() {
      let { data = "" } = await getAppUploadPath();
      this.actionUrl = data;
    },
    // 导出表单JSON
    handleDownloadRule() {
      this.dialogTitle = "表单规则";

      this.codemirrorOptions.readOnly = true;
      let rules = this.$refs.designer.getRule();
      rules.forEach((item) => this.handleFormRule(item));
      console.log("rules", rules);
      this.codemirrorContent = JSON.stringify(rules, null, 2);
      return this.codemirrorContent;
    },
    handleFormRule(item) {
      //官方内置组件的field为自动生成的，首字符可能为数字
      //field如果以数字开头会影响排他网关的判断，如果field以数字开头时手动添加一个字符a
      if (item.field) {
        let fieldStartWithNumber = /^[0-9]/.test(item.field);
        if (fieldStartWithNumber) {
          item.field = "a" + item.field;
        }
      }

      if (item.type == "upload" || item.type == "CameraPhotoUpload") {
        //CameraPhotoUpload水印相机
        this.$set(item.props, "name", "file");
        if (
          item.props?.uploadType == "file" ||
          item.props?.uploadType == "image"
        ) {
        } else {
          this.$set(item.props, "uploadType", "image");
        }
        // let env = process.env.NOOD_ENV == "production" ? "" : "saas-water"

        this.$set(item.props, "action", process.env.VUE_APP_BASE_UPLOAD_URL);

        this.$set(item.props, "onSuccess", function(res, file, fileList) {
          file.url = res.data;
          console.log("file", file);
        });
      }
      //自定义电子签名组件，保存组件规则时去掉component属性，否则根据规则动态绘制菜单时找不到对应路径的组件会报错
      //动态绘制时通过formcreate注入组件的方式提前注入自定义组件
      if (
        item.type === "SignBoard" ||
        item.type == "CameraPhotoUpload" ||
        item.type == "Locate" ||
        item.type == "FormGroup" ||
        item.type == "ProcessTableDesigner" ||
        item.type == "MultiFileUpload"
      ) {
        Reflect.deleteProperty(item, "component");
      }
      // 递归设置表单规则
      if (item.children && item.children.length > 0) {
        item.children.map((object) => this.handleFormRule(object));
      }
    },
    // 导出表单配置
    handleDownloadOption() {
      this.dialogTitle = "表单配置";

      this.codemirrorOptions.readOnly = true;
      let option = this.$refs.designer.getOption();
      option.submitBtn = false; //隐藏提交按钮
      this.codemirrorContent = JSON.stringify(option, null, 2);
      return this.codemirrorContent;
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.$nextTick(() => {
      //插入组件规则
      this.$refs.designer.addComponent(signboard);
      this.$refs.designer.addComponent(cameraPhotoUpload);
      this.$refs.designer.addComponent(locate);
      this.$refs.designer.addComponent(formGroup);
      this.$refs.designer.addComponent(processTableDesigner);
      this.$refs.designer.addComponent(multiFileUpload);
      // this.$refs.designer.addComponent(formRow);

      //插入自定义菜单
      this.$refs.designer.addMenu({
        title: "自定义组件",
        name: "custom",
        list: [
          {
            icon: signboard.icon,
            name: signboard.name,
            label: signboard.label,
          },
          {
            icon: cameraPhotoUpload.icon,
            name: cameraPhotoUpload.name,
            label: cameraPhotoUpload.label,
          },
          {
            icon: locate.icon,
            name: locate.name,
            label: locate.label,
          },
          {
            icon: formGroup.icon,
            name: formGroup.name,
            label: formGroup.label,
          },
          {
            icon: processTableDesigner.icon,
            name: processTableDesigner.name,
            label: processTableDesigner.label,
          },
          {
            icon: multiFileUpload.icon,
            name: multiFileUpload.name,
            label: multiFileUpload.label,
          },
          // {
          //   icon: formRow.icon,
          //   name: formRow.name,
          //   label: formRow.label
          // }
        ],
      });
    });
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  async mounted() {
    if (this.$route.query.isNew != "true") {
      this.getFormData();
    }
    await this.getAppUploadPath();
  },
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ aside {
  padding: 0;
  background: none;
}
</style>
