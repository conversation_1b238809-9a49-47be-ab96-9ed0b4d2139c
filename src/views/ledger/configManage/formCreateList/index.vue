//表单设计
<!-- 表单设计列表 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    ></AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="pageTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            style="margin: 10px 0"
            @click="goToFormCreate"
            v-has-permi="['ledger:formCreate:add']"
            >新增
          </el-button>
        </template>
        <!-- 状态 -->
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :value="row.formStatus"
            active-value="1"
            inactive-value="2"
            @change="(val) => handleStatusChange(val, row)"
            :disabled="!checkPermission(['ledger:formCreate:status'])"
          >
          </el-switch>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click.stop="editFormDesign(row)"
            style="margin-right: 10px"
            v-has-permi="['ledger:formCreate:edit']"
            >编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            :disabled="row.mrStatusCode === '01'"
            @click.stop="deleteItem(row)"
            v-has-permi="['ledger:formCreate:delete']"
            >删除
          </el-button>
          <el-button
            type="text"
            size="large"
            :disabled="row.formType === '1'"
            @click.stop="handleCopy(row)"
            style="margin-right: 10px"
            v-has-permi="['ledger:formCreate:copy']"
            >复制
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  deleteForm,
  list,
  queryPropOptions,
  changeStatus,
  copyForm,
} from "@/api/ledger/formDesign.js";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
export default {
  name: "ledgerForm",
  //import引入的组件需要注入到对象中才能使用
  components: { GridTable, AdvancedForm },
  data() {
    //这里存放数据
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      finallySearch: null,
      formStatusOptions: [
        {
          dictValue: "1",
          dictLabel: "启用",
        },
        {
          dictValue: "2",
          dictLabel: "未启用",
        },
      ],
      loading: false,
      tableData: [],
      pageTotal: 0,
      title: "新增",
      visible: false,
      config: [],
      tableId: "formCreateTable",
      columns: [
        {
          field: "formTitle",
          title: "表单名称",
          showOverflowTooltip: true,
        },
        {
          field: "formType",
          title: "表单类别",
          showOverflowTooltip: true,
          formatter: ({ cellValue }) => {
            return cellValue == "01"
              ? "创建表单"
              : cellValue == "02"
              ? "处理表单"
              : cellValue;
          },
        },
        {
          field: "formAttr",
          title: "表单属性",
          showOverflowTooltip: true,
        },
        {
          field: "formCreator",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          field: "createTime",
          title: "创建时间",
          showOverflowTooltip: true,
          width: 180,
        },
        {
          field: "formLastOperator",
          title: "修改人",
          showOverflowTooltip: true,
        },
        {
          field: "updateTime",
          title: "修改时间",
          showOverflowTooltip: true,
          width: 180,
        },
        {
          field: "formStatus",
          title: "状态",
          showOverflowTooltip: true,
          slots: { default: "status" },
        },
        // {
        //   field: "formDefDesc",
        //   title: "备注",
        //   showOverflowTooltip: true,
        // },
        {
          title: "操作",
          minWidth: 300,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      disabled: false,
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    checkPermission,
    async handleStatusChange(val, { formKey }) {
      this.loading = true;
      const res = await changeStatus({
        formKey,
        formStatus: val,
      });
      this.loading = false;
      if (res.code === "10000") {
        this.getList();
      }
    },
    querySearch(queryString, cb) {
      queryPropOptions({
        itemAttributeName: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    //初始化
    initConfig() {
      this.config = [
        {
          key: "formTitle",
          title: "表单名称",
          type: "input",
          placeholder: "请输入表单名称",
        },
        {
          key: "formType",
          title: "表单类别",
          type: "select",
          placeholder: "请选择表单类别",
          options: [
            { dictLabel: "创建表单", dictValue: "01" },
            { dictLabel: "处理表单", dictValue: "02" },
          ],
        },
        {
          key: "formAttr",
          title: "表单属性",
          type: "autocomplete",
          placeholder: "请输入表单属性",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb);
            },
          },
        },
        {
          key: "formStatus",
          title: "状态",
          type: "select",
          placeholder: "请选择表单状态",
          options: this.formStatusOptions,
        },
      ];
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        params.pageSize = this.queryParams.pageSize;
      }
      this.getList(params);
    },
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },

    handleCopy(row) {
      this.$confirm("是否确认复制此表单？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          formKey: row.formKey,
        };
        copyForm(data).then((res) => {
          if (res?.success) {
            this.$message.success("复制成功");
            //更新列表
            this.getList();
          }
        });
      });
    },

    //设计表单
    goToFormCreate() {
      this.$router.push({
        path: "/ledger/configManage/formCreateList/formDesign",
        query: { isNew: true },
      });
    },

    //外层删除
    deleteItem(row) {
      let that = this;
      this.$confirm(
        '是否确认删除表单名称为"' + row.formTitle + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then((res) => {
        let params = {
          formKey: row.formKey,
        };
        deleteForm(params).then((res) => {
          this.$message.success("删除成功");
          that.getList();
        });
      });
    },

    //修改表单
    editFormDesign(row) {
      this.$router.push({
        path: "/ledger/configManage/formCreateList/formDesign",
        query: { formKey: row.formKey, formId: row.formId },
      });
    },
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      this.finallySearch = args;
      list(args)
        .then((res) => {
          this.tableData = res.data;
          this.pageTotal = res.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  activated() {
    this.initConfig();
    this.getList();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
