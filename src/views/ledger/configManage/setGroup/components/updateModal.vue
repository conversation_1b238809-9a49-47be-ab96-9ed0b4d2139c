<template>
  <el-dialog
    :title="title"
    :visible.sync="addVisible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="70%"
  >
    <div class="queryParamsWrap">
      <el-form
        :model="editForm"
        :inline="true"
        ref="editForm"
        :rules="editRules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="设置组名称:" prop="groupName">
              <el-input
                v-model="editForm.groupName"
                placeholder="请输入名称"
                size="mini"
                style="width:80%"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <TransferTree
        :cascadeData="cascadeData"
        v-model="checkedData"
        ref="transferTree"
        :titles="['未配置', '已配置']"
      ></TransferTree>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="handleSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TransferTree from "@/components/TransferTree/index2.vue";

// import { MAINTENANCE_ORDER_SET_GROUP } from "@/utils/track/track-event-constants";
import {
  queryTreeList,
  queryGroupUser,
  submitSave,
} from "@/api/ledger/setGroup.js";
export default {
  components: { TransferTree },
  data() {
    return {
      title: "新增组",
      updateType: "add",
      checkedData: [],
      addVisible: false,
      editForm: { groupName: "" },
      editRules: {
        groupName: [
          { required: true, message: "请输入名称", trigger: "change" },
        ],
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      cascadeData: [],
      groupId: "",
    };
  },
  async created() {
    await this.getTreeList();
  },
  methods: {
    //获取所有组织-用户树形结构
    async getTreeList() {
      const res = await queryTreeList({});
      if (res) {
        this.cascadeData = res.data;
        this.traverseArr(this.cascadeData);
      }
    },
    traverseArr(arr) {
      arr.forEach((obj) => {
        // 添加id和label
        if (obj.userId) {
          obj.id = obj.userId;
          obj.label = obj.nickName;
          obj.pid = "-" + obj.deptId;
        } else if (obj.deptId) {
          obj.id = "-" + obj.deptId;
          obj.label = obj.deptName;
          obj.pid = "-" + obj.parentId;
          obj["children"] = obj.childrenList;
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children);
        }
      });
    },
    async openDialog(type, row) {
      this.addVisible = true;
      this.updateType = type;
      if (type === "add") {
        this.title = "新增组";
        this.checkedData = [];
        this.editForm = {
          groupName: "",
        };
      } else {
        this.title = "修改组";
        this.groupId = row.groupId;
        //获取已绑定的用户
        const res = await queryGroupUser({ groupId: row.groupId });
        if (res) {
          this.checkedData = res.data;
          this.traverseArr(this.checkedData);
          this.editForm.groupName = row.groupName;
        }
      }
      this.$nextTick(() => {
        this.$refs.transferTree.getDefaultLeftData();
      });
    },
    closeDialog() {
      this.addVisible = false;
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    //提交
    async handleSubmit() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        let arr = this.flattenArray(this.checkedData);
        console.log(this.checkedData, arr, "---arr");
        let userIds = [];
        arr.map((x) => {
          if (x.hasOwnProperty("userId")) {
            userIds.push(x.userId);
          }
        });
        if (userIds.length == 0) {
          this.$message.warning("组内人员不能为空！");
          return false;
        }
        let params = {
          groupName: this.editForm.groupName,
          userIds: userIds,
        };
        if (this.updateType === "edit") {
          params = { ...params, groupId: this.groupId };
        }
        const res = await submitSave(params);
        if (res?.code == 10000) {
          this.$message.success("保存成功");
          this.closeDialog();
          this.$emit("submit");

          // this.reportTrackEvent(MAINTENANCE_ORDER_SET_GROUP, {
          //   operateType: "修改",
          // });
        }
      });
      //   let validCount = 0;
      //   this.addForm.map((item, index) => {
      //     console.log(this.$refs);
      //     this.$refs["addForm_" + index][0].validate(async (valid) => {
      //       if (valid) {
      //         validCount++;
      //       }
      //     });
      //   });
      //   if (validCount == this.addForm.length) {
      //     console.log(this.addForm);
      //     const form = this.addForm.map((item) => {
      //       return {
      //         ...item,
      //         startDate: item.dateRange ? item.dateRange[0] : "",
      //         endDate: item.dateRange ? item.dateRange[1] : "",
      //       };
      //     });
      //     const res = await submitAddStation(form);
      //     if (res?.code == 10000) {
      //       this.$message.success("新增成功");
      //       this.closeAddDialog();
      //       this.queryData();
      //     }
      //   }
    },
  },
};
</script>

<style></style>
