<!-- 流程监听列表 -->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" label-width="100px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="服务名称" prop="flowName">
            <el-input
              placeholder="请输入抄表员编号"
              v-model="queryParams.flowName"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="监听类型" prop="flowType">
            <el-select clearable v-model="queryParams.flowType">
              <el-option
                v-for="dict in inspectResultType"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="flowStatus">
            <el-select clearable v-model="queryParams.flowStatus">
              <el-option
                v-for="dict in formStatusOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="padding-left: 15px; padding-bottom: 15px">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click.stop="handleQuery"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click.stop="resetQuery"
            >重置</el-button
          >
        </el-col>
      </el-row>
      <el-row>
        <el-button
          type="primary"
          size="mini"
          style="margin: 20px"
          @click="handleAdd"
          >新增</el-button
        >
      </el-row>
    </el-form>

    <el-table v-loading="loading" ref="multipleTable" :data="tableData1">
      <el-table-column
        label="接口服务名称"
        align="center"
        prop="consNo"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="服务类型"
        align="center"
        prop="consNo"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :disabled="scope.row.mrStatusCode === '01'"
            @click.stop="toggleItem(scope.row)"
            >启用</el-link
          >
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        align="center"
        prop="consNo"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="consNo"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ moment(scope.row.actualDate).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <el-link
            type="primary"
            @click.stop="editItem(scope.row)"
            style="margin-right: 10px"
            >编辑</el-link
          >
          <el-link
            type="primary"
            @click.stop="disableItem(scope.row)"
            style="margin-right: 10px"
            >禁用</el-link
          >
          <el-link
            type="primary"
            @click.stop="deleteItem(scope.row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      queryParams: {},
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    handleQuery() {},
    resetQuery() {},
    handleAdd() {},
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>