<!-- 流程设计列表 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <!-- 测试网厅回显 -->
    <!-- <CreatForm /> -->

    <el-card>
      <GridTable
        :columns="columns"
        :column-auto-fit="false"
        :tableData="tableData"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="pageTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            style="margin: 10px 0"
            @click="handleAdd"
            v-has-permi="['ledger:process:add']"
            >新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="mini"
            @click.stop="goToFormCreate(row)"
            style="margin-right: 10px"
            v-has-permi="['ledger:process:edit']"
            >编辑
          </el-button>
          <!-- <el-button
            type="text"
            size="mini"
            @click.stop="formView(row)"
            style="margin-right: 10px"
            >版本管理
          </el-button> -->
          <el-button
            type="text"
            size="mini"
            @click.stop="deleteItem(row)"
            v-has-permi="['ledger:process:delete']"
            >删除
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click.stop="copyWorkFlow(row)"
            v-has-permi="['ledger:process:copy']"
            >复制
          </el-button>
        </template>
        <!-- 状态 -->
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :value="row.flowStatus"
            active-value="1"
            inactive-value="0"
            @change="(val) => handleStatusChange(val, row)"
            :disabled="!checkPermission(['ledger:process:status'])"
          >
          </el-switch>
        </template>
      </GridTable>
    </el-card>

    <el-dialog
      :title="title"
      v-if="visibleAdd"
      :visible.sync="visibleAdd"
      @close="visibleAdd = false"
      width="500px"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="流程名称" prop="formName">-->
        <!--          <el-input v-model="form.flowName" />-->
        <!--        </el-form-item>-->
        <el-form-item label="流程编码" prop="flowKey" style="display: none">
          <el-input v-model="form.flowKey" disabled />
        </el-form-item>
        <el-form-item label="流程类型" prop="flowType">
          <el-select
            clearable
            v-model="form.flowType"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="dict in flowTypeOptions"
              :key="dict.typeLabel"
              :label="dict.typeLabel"
              :value="dict.typeCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.flowDefDesc" :rows="5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div>
          <el-checkbox v-model="isOpenChecked">添加后自动打开设计</el-checkbox>
        </div>
        <div>
          <el-button type="primary" @click="submit">确 定</el-button>
          <el-button @click.stop="visibleAdd = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="复制"
      :visible.sync="copyVisible"
      @close="copyVisible = false"
      width="500px"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="流程名称" prop="formName">-->
        <!--          <el-input v-model="form.flowName" />-->
        <!--        </el-form-item>-->
        <el-form-item label="流程编码" prop="flowKey">
          <el-input v-model="form.flowKey" disabled />
        </el-form-item>
        <el-form-item label="流程类型" prop="flowType">
          <el-select clearable v-model="form.flowType" style="width: 100%">
            <el-option
              v-for="dict in flowTypeOptions"
              :key="dict.typeLabel"
              :label="dict.typeLabel"
              :value="dict.typeCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.flowDefDesc" :rows="5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div>
          <el-button type="primary" @click="copyFlowDef">确 定</el-button>
          <el-button @click.stop="copyVisible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  getKey,
  add,
  copyFlowDef,
  list,
  handleDelete,
  deleteItem,
  modifyStatus,
  handleCopy,
  queryByFlowId,
  changeStatus,
  copyFlow,
  createFlowKey,
} from "@/api/ledger/process.js";
import checkPermission from "@/utils/permission.js";
import { list as flowCateOptions } from "@/api/orderConfig/processTypeConfig.js";
import CreatForm from "@/views/process/components/CreatForm.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { invalidAllFlow, invalidFlow } from "@/api/business/flow/flow";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: { CreatForm, GridTable, AdvancedForm },
  name: "ledgerProcessList",
  data() {
    //这里存放数据
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        flowName: undefined,
        flowStatus: undefined,
      },
      finallySearch: null,
      loading: false,
      tableData: [],
      isOpenChecked: false,
      pageTotal: 0,
      pageNum: 1,
      pageSize: 10,
      title: "新增",
      visibleAdd: false,
      visible: false,
      innerVisible: false,
      copyVisible: false,
      copyRow: "",
      inspectResultType: [], //流程类型
      formPlatOptions: [
        {
          dictValue: "1",
          dictValue: "PC",
        },
        {
          dictValue: "2",
          dictValue: "移动端",
        },
        {
          dictValue: "3",
          dictValue: "全部",
        },
      ], //流程平台
      flowTypeOptions: [], //流程类型
      form: {},
      rules: {
        // flowName: [
        //   { required: true, message: "请输入流程名称", trigger: "blur" },
        // ],
        flowKey: [
          { required: true, message: "请输入流程编码", trigger: "blur" },
        ],
        flowType: [
          { required: true, message: "请输入流程类型", trigger: "change" },
        ],
      },
      form2: {
        formTitle: "",
        formPlat: "",
        formDesc: "",
        formKey: "",
        formId: "",
      },
      rules2: {
        formTitle: [
          { required: true, message: "请输入表单标题", trigger: "blur" },
        ],
        formPlat: [
          { required: true, message: "请输入表单平台", trigger: "change" },
        ],
      },
      formStatusOptions: [
        {
          dictValue: "1",
          dictLabel: "已启用",
        },
        {
          dictValue: "0",
          dictLabel: "未启用",
        },
      ],
      config: [],
      tableId: "ledgerLFListTable",
      columns: [
        {
          field: "flowName",
          title: "流程名称",
          showOverflowTooltip: true,
        },
        {
          field: "flowRemark",
          title: "备注",
          showOverflowTooltip: true,
        },
        {
          field: "flowStatus",
          title: "状态",
          slots: { default: "status" },
        },
        {
          field: "flowCreator",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  computed: {},
  watch: {
    visibleAdd(newVal) {
      if (newVal) {
        this.getKey();
      } else {
        this.form = {};
        this.isOpenChecked = false;
      }
    },
    copyVisible(newVal) {
      if (newVal) {
        this.getKey();
      } else {
        this.form = {};
        this.isOpenChecked = false;
      }
    },
  },
  //方法集合
  methods: {
    checkPermission,
    async handleStatusChange(val, { flowKey }) {
      this.loading = true;
      const res = await changeStatus({
        flowKey,
        flowStatus: val,
      }).catch(() => {
        this.loading = false;
      });
      this.loading = false;
      if (res.code === "10000") {
        this.getList();
      }
    },
    //初始化
    initConfig() {
      this.config = [
        {
          key: "flowName",
          title: "流程名称",
          type: "input",
          placeholder: "请输入流程名称",
        },
        // {
        //   key: "flowType",
        //   title: "流程类型",
        //   type: "select",
        //   options: this.flowTypeOptions,
        //   optionLabel: "typeLabel",
        //   optionValue: "typeCode",
        //   placeholder: "请选择流程类型",
        // },
        {
          key: "flowStatus",
          title: "状态",
          type: "select",
          options: this.formStatusOptions,
          placeholder: "请选择状态",
        },
      ];
    },
    formatterFlowTypeOptions(row) {
      let name = "";
      this.flowTypeOptions.map((item) => {
        if (item.typeCode == row.flowType) {
          name = item.typeLabel;
        }
      });
      return name;
    },
    //获取流程类型
    getFlowTypeOptions() {
      let params = {};
      // params.typeStatus = "1";
      return flowCateOptions(params).then((res) => {
        this.flowTypeOptions = res.data;
      });
    },
    //复制
    copy(row) {
      if (this.tableData.length >= 6) {
        this.$eleMessage.info("版本数量已达到上线，无法复制版本");
        return;
      }
      let params = {
        flowId: row.flowId,
      };
      handleCopy(params).then((res) => {
        this.tableData.unshift(res.data);
        this.visible = false;
        this.getList();
      });
    },
    //作废 流程
    handleInvalid(row) {
      let that = this;
      this.$confirm("是否确认作废此流程所有的在途工单数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function() {
          let params = {
            flowId: row.flowId,
            flowKey: row.flowKey,
          };
          invalidAllFlow(params).then((res) => {
            that.visible = false;
            that.getList();
            this.$message.success("作废成功");
          });
        })
        ["catch"](function() {});
    },
    //删除流程
    deleteInner(row) {
      let that = this;
      this.$confirm(
        '是否确认删除流程版本号为"' + row.flowVersion + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          let params = {
            flowId: row.flowId,
          };
          handleDelete(params).then((res) => {
            that.$message.success("删除成功");
            that.visible = false;
            that.getList();
          });
        })
        ["catch"](function() {});
    },
    // 修改流程
    editFlowDesign(row) {
      if (row.flowStatus == "1") {
        this.$message.error("该流程已发布, 请取消发布后进行修改");
        return;
      }
      let params = {
        flowId: row.flowId,
      };
      queryByFlowId(params).then((res) => {
        if (res.code == 10000) {
          this.$router.push({
            path: "/ledger/configManage/process/design",
            query: { obj: JSON.stringify(res.data) },
          });
        }
      });
    },
    viewDesign(row) {
      let params = {
        flowId: row.flowId,
      };
      queryByFlowId(params).then((res) => {
        if (res.code == 10000) {
          this.$router.push({
            path: "/ledger/configManage/process/design",
            query: {
              obj: JSON.stringify({ ...res.data, ...{ viewFlag: true } }),
            },
          });
        }
      });
    },
    getKey() {
      getKey().then((res) => {
        this.$set(this.form, "flowKey", res.data);
      });
    },
    //新增
    handleAdd() {
      createFlowKey().then((res) => {
        if (res?.code === "10000") {
          this.$router.push({
            path: "/ledger/configManage/process/design",
            query: { isNew: true, flowKey: res.data },
          });
        }
      });
      // this.visibleAdd = true;
    },
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        params.pageSize = this.queryParams.pageSize;
      }
      this.getList(params);
    },
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    copyFlowDef() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.fromFlowKey = this.copyRow.flowKey;
          copyFlowDef(this.form).then((res) => {
            this.copyVisible = false;
            this.getList();
          });
        }
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          add(this.form).then((res) => {
            if (this.isOpenChecked) {
              // let tagsView = this.$store.state.tagsView.visitedViews;
              // let view = null;
              // for (let i = 0; i < tagsView.length; i++) {
              //   if (tagsView[i].path == "/process/process") {
              //     view = tagsView[i];
              //   }
              // }
              // this.closeSelectedTag(view);

              this.$router.push({
                path: "/ledger/configManage/process/design",
                query: { obj: JSON.stringify(res.data) },
              });
            }
            this.visibleAdd = false;
            this.getList();
          });
        }
      });
    },
    closeSelectedTag(view) {
      this.$store
        .dispatch("tagsView/delView", view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        this.$router.push(latestView);
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === "Dashboard") {
          // to reload home page
          this.$router.replace({ path: "/redirect" + view.fullPath });
        } else {
          this.$router.push("/");
        }
      }
    },

    //编辑设计
    goToFormCreate(row) {
      // if (row.defFlows && row.defFlows.length >= 6) {
      //   this.$eleMessage.info("版本数量已达到上线，无法新建版本");
      //   return;
      // }
      this.$router.push({
        path: "/ledger/configManage/process/design",
        query: { flowKey: row.flowKey },
      });
    },
    //表单视图
    formView(row) {
      this.visible = true;
      this.tableData = row.defFlows;
    },

    //复制表单
    copyWorkFlow(row) {
      this.$confirm("是否确认复制此流程？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          flowKey: row.flowKey,
        };
        copyFlow(data).then((res) => {
          if (res?.success) {
            this.$message.success("复制成功");
            //更新列表
            this.getList();
          }
        });
      });
    },
    //删除
    deleteItem(row) {
      let that = this;
      this.$confirm("是否确认删除该流程?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            flowKey: row.flowKey,
          };
          deleteItem(params).then((res) => {
            that.$message.success("删除成功");
            that.getList();
          });
        })
        ["catch"](function() {});
    },
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.finallySearch = args;
      this.loading = true;
      list(args)
        .then((res) => {
          this.tableData = res.data;
          this.pageTotal = res.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  async activated() {
    await this.getFlowTypeOptions();
    this.initConfig();
    this.handleQuery();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  // activated() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
