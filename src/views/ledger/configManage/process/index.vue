<!-- 流程图 -->
<template>
  <div class="">
    <el-drawer
      :title="isDrawerView ? '查看配置' : '配置'"
      size="50%"
      v-if="drawer"
      :visible.sync="drawer"
      :direction="direction"
      :before-close="handleClose"
      :wrapperClosable="isDrawerView"
      :append-to-body="true"
    >
      <el-tabs
        v-model="activeName"
        @tab-click="changeTab"
        v-if="nodeType == 'bpmn:sequenceFlow'"
      >
        <el-tab-pane label="连线配置" name="sixth">
          <div>
            <el-radio-group v-model="form6.radio">
              <el-radio label="1">选择判断条件</el-radio>
              <el-radio label="2">自定义判断条件</el-radio>
            </el-radio-group>
          </div>
          <div v-if="form6.radio == '1'" class="">
            <div>
              <label class="el-form-item__label">目标节点</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.targetNode"
                  @change="changeTargetNode"
                  size="mini"
                  class="el-form-item__content"
                >
                  <el-option
                    v-for="(item, index) in rejectIdOptions"
                    :label="item.text.value"
                    :value="item.id"
                    :key="index"
                  />
                </el-select>
              </div>
            </div>
            <div>
              <label class="el-form-item__label">目标表单</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.targetForm"
                  @change="changeTargetForm"
                  size="mini"
                  class="el-form-item__content"
                >
                  <el-option
                    v-for="(item, index) in targetFormOptions"
                    :label="item.formName"
                    :value="item.formKey"
                    :key="index"
                  />
                </el-select>
              </div>
            </div>
            <div>
              <label class="el-form-item__label">判断对象</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.judgeObject"
                  size="mini"
                  class="el-form-item__content"
                >
                  <el-option
                    v-for="(item, index) in judgeObjectOptions"
                    :label="item.label"
                    :value="item.value"
                    :key="index"
                  />
                </el-select>
              </div>
            </div>
            <div>
              <label class="el-form-item__label">判断条件设置</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.condition"
                  size="mini"
                  class="el-form-item__content"
                  style="width: 80px;"
                >
                  <el-option
                    v-for="(item, index) in conditionOptions"
                    :label="item.label"
                    :value="item.value"
                    :key="index"
                  />
                </el-select>
                <el-input
                  v-model="form6.conditionValue"
                  size="mini"
                  style="width:150px"
                />
              </div>
            </div>
          </div>
          <div v-else-if="form6.radio == '2'" class="">
            <label class="el-form-item__label">条件规则</label>
            <div class="el-form-item__content">
              <el-input v-model="form6.value" size="mini" style="width:150px" />
            </div>
          </div>
          <el-row type="flex" justify="end">
            <div>
              <el-button type="primary" @click="onSubmit6">保存</el-button>
              <el-button @click="drawer = false">取消</el-button>
            </div>
          </el-row>
        </el-tab-pane></el-tabs
      >
      <el-form
        :model="basicForm"
        ref="basicForm"
        :rules="basicRules"
        label-width="130px"
        :disabled="isDrawerView"
        v-else
      >
        <el-form-item label="处理表单" prop="formKeys">
          <inputAddFormList
            @changeForm="changeFormDeal"
            v-model="basicForm.formKeys"
            :disabled="isDrawerView"
          />
          <el-tag
            v-for="tag in dealTags"
            :key="tag.formKey"
            :closable="!isDrawerView"
            @close="handleCloseDealTag(tag)"
            style="margin-right: 3px"
          >
            {{ tag.formTitle }}
          </el-tag>
        </el-form-item>
        <el-form-item label="处理人" prop="name" v-if="!isFirstProcessNode">
          <el-row>
            <el-col :span="8">
              <el-select
                v-model="basicForm.selectType"
                @change="changeSelectType"
                style="width: 100%;"
                clearable
              >
                <el-option
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                  v-for="item in selectTypeDict"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="16">
              <el-select
                v-model="basicForm.name"
                style="width: 100%;"
                clearable
                :multiple="basicForm.selectType !== '2'"
                @change="userArrChange"
                :loading="selectLoading"
                :key="basicForm.selectType"
                filterable
              >
                <el-option
                  :key="item.dictValue"
                  :value="item.dictValue"
                  :label="item.dictLabel"
                  v-for="item in handleOptions"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          label="通知方式"
          prop="messageTypes"
          v-if="!isFirstProcessNode"
        >
          <el-checkbox-group v-model="basicForm.messageTypes">
            <!-- <el-checkbox label="1">app消息通知</el-checkbox> -->
            <el-checkbox label="2">钉钉</el-checkbox>
            <el-checkbox label="3">短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="允许驳回"
          prop="allowedBack"
          v-if="!isFirstProcessNode"
        >
          <el-select v-model="basicForm.allowedBack">
            <el-option label="是" value="Y" />
            <el-option label="否" value="N" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="允许作废"
          prop="allowedInvalidate"
          v-if="!isFirstProcessNode"
        >
          <el-select v-model="basicForm.allowedInvalidate">
            <el-option label="是" value="Y" />
            <el-option label="否" value="N" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="工序工时"
          prop="handleAging"
          v-if="!isFirstProcessNode"
        >
          <el-input
            v-model="basicForm.handleAging"
            placeholder="请输入工序工时"
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="即将超时提醒时效"
          prop="overRemindAging"
          v-if="!isFirstProcessNode"
        >
          <el-input
            v-model="basicForm.overRemindAging"
            placeholder="请输入即将超时提醒时效"
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="处理人限制"
          prop="handlerUserSettings"
          v-if="!isFirstProcessNode"
        >
          <el-radio-group v-model="basicForm.handlerUserSettings">
            <el-radio label="N">不限制</el-radio>
            <el-radio label="Y">上一个节点处理人不能处理该节点</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="钉钉工单需求发起人通知"
          prop="dingDingNoticeFlag"
          v-if="!isFirstProcessNode"
        >
          <el-radio-group v-model="basicForm.dingDingNoticeFlag">
            <el-radio label="01">不通知</el-radio>
            <el-radio label="02">钉钉通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="节点完成时抄送通知人"
          prop="ccUserNames"
          v-if="!isFirstProcessNode"
        >
          <el-select
            v-model="basicForm.ccUserNames"
            style="width: 100%;"
            clearable
            :multiple="true"
            filterable
          >
            <el-option
              :key="item.dictValue"
              :value="item.dictValue"
              :label="item.dictLabel"
              v-for="item in userOptions"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="抄送方式" prop="ccType" v-if="!isFirstProcessNode">
          <el-checkbox-group v-model="basicForm.ccType">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-row type="flex" justify="end" v-if="!isDrawerView">
          <div>
            <el-button type="primary" @click="handleSubmitConfig"
              >保存</el-button
            ><el-button @click="drawer = false">取消</el-button>
          </div>
        </el-row>
      </el-form>

      <!-- <el-tabs v-model="activeName" @tab-click="changeTab">
        <el-tab-pane
          label="基本属性"
          name="first"
          v-if="nodeType != 'bpmn:sequenceFlow'"
        >
          <el-form
            :model="form1"
            ref="form1"
            :rules="rules1"
            label-width="80px"
          >
            <p>节点基本信息</p>
            <el-row>
              <el-col :span="8" style="margin-bottom: -2px">
                <el-form-item
                  v-if="nodeType != 'bpmn:sequenceFlow'"
                  label="节点名称"
                  prop="nodeName"
                >
                  <el-input
                    size="mini"
                    placeholder="请输入节点名称"
                    v-model="form1.nodeName"
                    @blur="changeNodeName"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="流程名称" prop="flowName">
                  <el-input
                    size="mini"
                    placeholder=""
                    v-model="form1.flowName"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="流程类型" prop="flowType">
                  <el-select
                    v-model="form1.flowType"
                    clearable
                    size="mini"
                    disabled
                  >
                    <el-option
                      v-for="dict in flowTypeOptions"
                      :key="dict.typeLabel"
                      :label="dict.typeLabel"
                      :value="dict.typeCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="节点类型" prop="bpmnType">
                  <el-select
                    v-model="form1.bpmnType"
                    clearable
                    size="mini"
                    disabled
                  >
                    <el-option
                      v-for="dict in bpmnTypeList"
                      :key="dict.type"
                      :label="dict.label"
                      :value="dict.type"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <p>自动审批配置</p>
            <el-form :model="ruleForm" ref="ruleForm" label-width="80px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="自动审批通过" prop="taskApproveTime">
                    <el-input-number
                      placeholder="自定义"
                      :precision="1"
                      v-model="ruleForm.taskApproveTime"
                    />
                    小时
                  </el-form-item>
                </el-col>
              </el-row>
              <p>消息通知配置</p>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="通知方式" prop="ways">
                    <el-checkbox-group v-model="ways">
                      <el-checkbox label="1">app消息通知</el-checkbox>
                      <el-checkbox label="2">钉钉通知</el-checkbox>
                      <el-checkbox label="3">短信</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <el-row
              type="flex"
              justify="end"
              v-if="nodeType != 'bpmn:sequenceFlow'"
            >
              <el-button v-if="!obj.viewFlag" type="primary" @click="onSubmit1"
                >保存</el-button
              >
              <el-button @click="drawer = false">取消</el-button>
            </el-row>
          </el-form>
        </el-tab-pane>

        <el-tab-pane
          label="处理设置"
          name="second"
          v-if="nodeType == 'bpmn:userTask'"
        >
          <el-form
            :model="form2"
            ref="form2"
            :rules="rules2"
            label-width="80px"
          >
            <el-row>
              <el-col>
                <el-form-item label="处理表单" prop="nodeName">
                  <inputAddFormList
                    @changeForm="changeFormDeal"
                    v-model="form2.formKeys"
                  />

                  <el-tag
                    v-model="form2.dealTags"
                    v-for="tag in dealTags"
                    :key="tag.formName"
                    closable
                    @close="handleCloseDealTag(tag)"
                    style="margin-right: 3px"
                  >
                    {{ tag.formName }}
                  </el-tag>
                </el-form-item>
              </el-col>
              <userOrBranch
                ref="userOrBranch"
                :nodeInfo="nodeInfo"
                :arr="userOrBranchArr"
                :userIdList="userIdList"
                :branchList="branchList"
                @changeType="changeType"
                label="处理人"
                :activeName="activeName"
              />
            </el-row>
            <el-row type="flex" justify="end">
              <div>
                <el-button
                  v-if="!obj.viewFlag"
                  type="primary"
                  @click="onSubmit2"
                  >保存</el-button
                >
                <el-button @click="drawer = false">取消</el-button>
              </div>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          label="审批设置"
          name="third"
          v-if="nodeType == 'bpmn:userTask'"
        >
          <el-form
            :model="form3"
            ref="form3"
            :rules="rules3"
            label-width="80px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="允许驳回" prop="nodeName">
                  <el-select v-model="form3.isReject" size="mini">
                    <el-option label="是" value="Y" />
                    <el-option label="否" value="N" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form3.isReject == 'Y'">
                <el-form-item label="驳回至" prop="nodeName">
                  <el-select v-model="form3.rejectId" size="mini">
                    <el-option
                      v-for="(dict, index) in rejectIdOptions"
                      :key="index"
                      :label="dict.text.value"
                      :value="dict.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="允许作废" prop="nodeName">
                  <el-select v-model="form3.isInvalid" size="mini">
                    <el-option label="是" value="Y" />
                    <el-option label="否" value="N" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="end">
              <div>
                <el-button
                  v-if="!obj.viewFlag"
                  type="primary"
                  @click="onSubmit3"
                  >保存</el-button
                >
                <el-button @click="drawer = false">取消</el-button>
              </div>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          label="业务配置"
          name="fourth"
          v-if="nodeType == 'bpmn:userTask'"
        >
          <el-form
            :model="form4"
            ref="form4"
            :rules="rules4"
            label-width="80px"
          >
            <el-row>
              <el-col>
                <el-form-item label="关联业务" prop="nodeName">
                  <inputAddBusinessList
                    @changeBusiness="changeBusiness"
                    v-model="form4.compensationIds"
                  />

                  <el-tag
                    v-model="form4.tags"
                    v-for="tag in tags4"
                    :key="tag.compensationName"
                    closable
                    @close="handleCloseTag4(tag)"
                    style="margin-right: 3px"
                  >
                    {{ tag.compensationName }}
                  </el-tag>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex" justify="end">
              <el-button v-if="!obj.viewFlag" type="primary" @click="onSubmit4"
                >保存</el-button
              >
              <el-button @click="drawer = false">取消</el-button>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          label="会签配置"
          name="fifth"
          v-if="nodeType == 'bpmn:multiInstanceLoopCharacteristics'"
        >
          <div class="sign-wrap">
            <label class="el-form-item__label">会签处理人</label>
            <el-select
              v-model="form5.signUserIds"
              multiple
              size="mini"
              class="el-form-item__content"
            >
              <el-option
                v-for="(item, index) in signUserOption"
                :label="item.nickName"
                :value="item.userId"
                :key="index"
              />
            </el-select>
          </div>
          <div class="sign-wrap">
            <label class="el-form-item__label">会签处理部门</label>
            <el-select
              v-model="form5.signDepIds"
              multiple
              size="mini"
              class="el-form-item__content"
            >
              <el-option
                v-for="(item, index) in signDepartmentOption"
                :label="item.orgName"
                :value="item.orgId"
                :key="index"
              />
            </el-select>
          </div>
          <div class="tab-row-wrap">
            <label class="el-form-item__label">当会签处理人通过大于</label>
            <div class="el-form-item__content">
              <el-input-number
                v-model="form5.value"
                :controls="false"
                size="mini"
                style="width:80px"
              />
            </div>
            <label class="el-form-item__label"> %时，节点可以通过</label>
          </div>
          <el-row type="flex" justify="end">
            <div>
              <el-button type="primary" @click="onSubmit5">保存</el-button>
              <el-button @click="drawer = false">取消</el-button>
            </div>
          </el-row>
        </el-tab-pane>
        <el-tab-pane
          label="连线配置"
          v-if="nodeType == 'bpmn:sequenceFlow'"
          name="sixth"
        >
          <div>
            <el-radio-group v-model="form6.radio">
              <el-radio label="1">选择判断条件</el-radio>
              <el-radio label="2">自定义判断条件</el-radio>
            </el-radio-group>
          </div>
          <div v-if="form6.radio == '1'" class="">
            <div>
              <label class="el-form-item__label">目标节点</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.targetNode"
                  @change="changeTargetNode"
                  size="mini"
                  class="el-form-item__content"
                >
                  <el-option
                    v-for="(item, index) in rejectIdOptions"
                    :label="item.text.value"
                    :value="item.id"
                    :key="index"
                  />
                </el-select>
              </div>
            </div>
            <div>
              <label class="el-form-item__label">目标表单</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.targetForm"
                  @change="changeTargetForm"
                  size="mini"
                  class="el-form-item__content"
                >
                  <el-option
                    v-for="(item, index) in targetFormOptions"
                    :label="item.formName"
                    :value="item.formKey"
                    :key="index"
                  />
                </el-select>
              </div>
            </div>
            <div>
              <label class="el-form-item__label">判断对象</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.judgeObject"
                  size="mini"
                  class="el-form-item__content"
                >
                  <el-option
                    v-for="(item, index) in judgeObjectOptions"
                    :label="item.label"
                    :value="item.value"
                    :key="index"
                  />
                </el-select>
              </div>
            </div>
            <div>
              <label class="el-form-item__label">判断条件设置</label>
              <div class="el-form-item__content">
                <el-select
                  v-model="form6.condition"
                  size="mini"
                  class="el-form-item__content"
                  style="width: 80px;"
                >
                  <el-option
                    v-for="(item, index) in conditionOptions"
                    :label="item.label"
                    :value="item.value"
                    :key="index"
                  />
                </el-select>
                <el-input
                  v-model="form6.conditionValue"
                  size="mini"
                  style="width:150px"
                />
              </div>
            </div>
          </div>
          <div v-else-if="form6.radio == '2'" class="">
            <label class="el-form-item__label">条件规则</label>
            <div class="el-form-item__content">
              <el-input v-model="form6.value" size="mini" style="width:150px" />
            </div>
          </div>
          <el-row type="flex" justify="end">
            <div>
              <el-button type="primary" @click="onSubmit6">保存</el-button>
              <el-button @click="drawer = false">取消</el-button>
            </div>
          </el-row>
        </el-tab-pane>
      </el-tabs> -->
    </el-drawer>

    <UserList
      ref="UserList"
      v-if="visibleUserList"
      :visible="visibleUserList"
      :multiple="multiple"
      @close="visibleUserList = false"
      @chooseUserList="chooseUserList"
    />
    <DepartmentList
      ref="DepartmentList"
      v-if="visibleDepartmentList"
      :visible="visibleDepartmentList"
      :multiple="multiple"
      @close="visibleDepartmentList = false"
      @chooseDepartmentList="chooseDepartmentList"
    />

    <div style="width: 100%;height:200px;">
      <LF
        ref="LF"
        :nodeInfo="nodeInfo"
        :arr="newUserOrBranchArr"
        :chooseType="chooseType"
        :isSilentMode="false"
        :visible="true"
        :flowKey="flowKey"
        @open="open"
        :obj="obj"
        class="LF-height"
        :isDrawer="!!drawerFlowKey"
        @closeDrawer="closeDrawer"
      />
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import CreatForm from "./components/CreatForm.vue";
import LF from "./components/LF.vue";
import inputAddFormList from "./components/inputAddFormList.vue";
import inputAddBusinessList from "@/views/process/components/inputAddBusinessList.vue";
import UserList from "@/views/process/components/UserList.vue";
import DepartmentList from "@/views/process/components/DepartmentList.vue";
import userOrBranch from "@/views/process/components/userOrBranch.vue";
import { list as flowCateOptions } from "@/api/orderConfig/processTypeConfig.js";
import * as api from "@/api/ledger/process.js";

import { mapState } from "vuex";
import { saveApproveConfig, getFlowConfigs } from "@/api/business/flow/flow";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {
    CreatForm,
    LF,
    inputAddFormList,
    inputAddBusinessList,
    UserList,
    userOrBranch,
    DepartmentList,
  },
  props: {
    drawerFlowKey: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isFirstProcessNode: false,
      selectLoading: false,
      newUserOrBranchArr: [],
      baseForm: { flowName: "", flowRemark: "" },
      baseRules: {
        flowName: [
          { required: true, message: "流程名称不能为空！", trigger: "change" },
        ],
      },
      handleOptions: [],
      selectTypeDict: [
        {
          label: "指定人",
          value: "2",
        },
        {
          label: "指定组",
          value: "3",
        },
        {
          label: "指定角色",
          value: "5",
        },
      ],
      basicForm: {
        formKeys: [],
        selectType: "",
        name: undefined,
        messageTypes: [],
        allowedBack: "",
        allowedInvalidate: "",
        handleAging: "",
        overRemindAging: "",
        handlerUserSettings: "N",
        dingDingNoticeFlag: "01",
        ccUserNames: [],
        ccType: [],
      },

      projectPeriodList: [],
      bpmnTypeList: [
        {
          type: "bpmn:startEvent",
          text: "开始",
          label: "开始节点",
        },
        {
          type: "bpmn:userTask",
          text: "流程节点",
          label: "流程节点",
        },
        {
          type: "bpmn:multiInstanceLoopCharacteristics",
          text: "会签节点",
          label: "会签节点",
        },
        // {
        //   type: 'bpmn:serviceTask',
        //   text: "系统任务",
        //   label: "系统任务",
        // },
        {
          type: "bpmn:exclusiveGateway",
          text: "是否",
          label: "条件判断",
        },
        {
          type: "bpmn:endEvent",
          text: "结束",
          label: "结束节点",
        },
      ],
      flowTypeOptions: [], //流程类型
      drawer: false,
      direction: "rtl",

      nodeInfo: null, //节点信息
      activeName: "first",
      form1: {
        ways: [],
        reminderTimeValue: "3",
      },
      rules1: {
        // nodeName: [
        //   { required: true, message: "请输入流程名称", trigger: "blur" }
        // ],
        // configStatus: [
        //   { required: true, message: "请确认启用状态", trigger: "blur" }
        // ],
        // taskExceptTime: [
        //   { required: true, message: "请输入计划时间", trigger: "blur" }
        // ],
        // reminderTimeValue: [
        //   { required: true, message: "请选择提醒时间", trigger: "blur" }
        // ],
        // ways: [
        //   { required: true, message: "请选择通知方式", trigger: "blur" }
        // ]
      },
      form2: {},
      ruleForm: {
        taskApproveTime: undefined,
      },
      ways: [],
      rules2: {},
      checkTags: [], //查询表单
      dealTags: [],
      userIdList: [], //人
      branchList: [], //部门

      form3: {},
      rules3: {},

      form4: {},
      rules4: {},
      tags4: [],

      form5: {
        value: "",
        signUserIds: [],
        signDepIds: [],
      },
      rules5: {},
      signUserOption: [],
      signDepartmentOption: [],

      form6: {
        radio: "1",
        value: "", //自定义条件规则
        targetNode: undefined, //目标节点
        targetForm: undefined, //目标表单
        judgeObject: undefined, //判断对象
        condition: undefined, //判断条件
        conditionValue: "", //判断条件值
      },

      chooseType: undefined,
      userOrBranchArr: [],
      visibleUserList: false,
      visibleDepartmentList: false,
      multiple: false,
      formStatusOptions: [
        {
          dictLabel: "是",
          dictValue: 1,
        },
        {
          dictLabel: "否",
          dictValue: 0,
        },
      ],
      remindTimeOptions: [
        {
          dictLabel: "剩余50%",
          dictValue: "1",
        },
        {
          dictLabel: "剩余30%",
          dictValue: "2",
        },
        {
          dictLabel: "剩余10%",
          dictValue: "3",
        },
      ],
      rejectIdOptions: [], //驳回至节点list、目标节点
      flowId: undefined,
      targetFormOptions: [], //目标表单
      judgeObjectOptions: [], //判断对象
      conditionOptions: [
        {
          label: ">",
          // value: "&lt;"
          value: "大于",
        },
        {
          label: "<",
          // value: "&gt;"
          value: "小于",
        },
        {
          label: ">=",
          // value: "&lt;="
          value: "大于等于",
        },
        {
          label: "<=",
          // value: "&gt;="
          value: "小于等于",
        },
        {
          label: "=",
          // value: "="
          value: "等于",
        },
      ], //判断条件
      flowKey: "",
      obj: {},
      isDrawerView: false,
      userOptions: [],
    };
  },
  computed: {
    // obj() {
    //   if (this.$route.query.obj) {
    //     let obj = JSON.parse(this.$route.query.obj);
    //     this.$set(this.form1, "flowType", obj.flowType);
    //     this.$set(this.form1, "flowName", obj.flowName);
    //     console.log(obj);
    //     return JSON.parse(this.$route.query.obj);
    //   }
    // },
    ...mapState({
      nodeType: (state) => state.logicFlow.nodeType,
    }),
    basicRules() {
      return {
        handleAging: [
          {
            pattern: /^(0|[1-9]\d{0,5}|999999)(\.\d{1,2})?$/,
            message: "请输入正确的数字",
          },
        ],
        overRemindAging: [
          {
            pattern: /^(0|[1-9]\d{0,5}|999999)(\.\d{1,2})?$/,
            message: "请输入正确的数字",
          },
        ],
        name: [
          {
            required: !this.isFirstProcessNode,
            message: "请选择处理人",
            trigger: "change",
          },
        ],
        ccType: [
          {
            required:
              !this.isFirstProcessNode &&
              this.basicForm?.ccUserNames?.length > 0,
            message: "请选择抄送方式",
            trigger: "change",
          },
        ],
      };
    },
  },
  watch: {
    drawer(newVal) {
      if (!newVal) {
        this.activeName = "first";
      }
    },
    activeName(newVal) {
      if (newVal == "first") {
        this.getTaskBizParams();
      }

      if (newVal == "second") {
        this.getAllBindForms();
      }
      if (newVal == "second" || newVal == "third") {
        if (newVal == "second") {
          setTimeout(() => {
            if (this.nodeInfo.properties["whale-flow_selectType"]) {
              console.log("里面second", this.nodeInfo.properties);
              this.$refs.userOrBranch.form.selectType = this.nodeInfo.properties[
                "whale-flow_selectType"
              ];
            }
          }, 500);
        }
        if (this.nodeInfo.properties["whale-flow_selectType"] == "2") {
          this.userOrBranchArr = [
            {
              userId: this.nodeInfo.properties.flowable_assignee,
            },
          ];
        } else if (this.nodeInfo.properties["whale-flow_selectType"] == "3") {
          if (this.nodeInfo.properties.flowable_candidateUsers) {
            let arr = [];
            if (
              this.nodeInfo.properties.flowable_candidateUsers.indexOf(",") > 0
            ) {
              arr = this.nodeInfo.properties.flowable_candidateUsers.split(",");
            } else {
              arr = [this.nodeInfo.properties.flowable_candidateUsers];
            }
            console.log("[ arr 3 ] >", arr);
            this.userIdList = arr;
          }
        } else if (this.nodeInfo.properties["whale-flow_selectType"] == "4") {
          if (this.nodeInfo.properties.flowable_candidateGroups) {
            let arr = this.nodeInfo.properties.flowable_candidateGroups.split(
              ","
            );
            this.branchList = arr;
          }
        } else if (this.nodeInfo.properties["whale-flow_selectType"] == "5") {
          this.userOrBranchArr = [
            {
              userId: this.nodeInfo.properties.flowable_assignee || "",
            },
          ];
        }
      }
    },
  },
  //方法集合
  methods: {
    closeDrawer() {
      this.$emit("close");
    },
    //配置详情-处理人回显
    handleUserFormDetail() {
      setTimeout(() => {
        if (this.nodeInfo.properties["whale-flow:selectType"]) {
          this.$set(
            this.basicForm,
            "selectType",
            this.nodeInfo.properties["whale-flow:selectType"]
          );

          // this.basicForm.selectType = this.nodeInfo.properties[
          //   "whale-flow_selectType"
          // ];
          this.changeSelectType(this.basicForm.selectType);
          const dict = [
            {
              value: "2",
              title: "指定人",
              name: "flowable:assignee",
              fn: (val) => {
                return val;
              },
            },
            {
              value: "3",
              title: "指定组",
              name: "flowable:candidateGroups",
              fn: (val) => {
                return val?.split(",");
              },
            },
            {
              value: "5",
              title: "指定角色",
              name: "flowable:candidateGroups",
              fn: (val) => {
                return val?.split(",");
              },
            },
          ];
          const obj = dict.find((x) => x.value == this.basicForm.selectType);
          this.$set(
            this.basicForm,
            "name",
            obj.fn(this.nodeInfo.properties[obj.name])
          );
          this.userArrChange(this.basicForm.name);
          // this.basicForm.name = obj.fn(this.nodeInfo.properties[obj.name]);
        }
      }, 500);
    },
    //处理人选择回调--获取已选中数据赋值给newUserOrBranchArr，用于给流程图xml中绑定属性
    userArrChange(val) {
      if (this.basicForm.selectType == "2") {
        this.newUserOrBranchArr = [{ userId: val }];
      } else {
        this.newUserOrBranchArr = val.map((x) => {
          return { userId: x };
        });
      }
    },
    // 根据当前节点获取前一个节点id
    getPreviousNodeIds(currentNodeId) {
      // 获取所有以此节点为终点的边。
      const edges = this.$refs.LF.lf.getNodeIncomingEdge(currentNodeId) || [];

      // 提取这些边的源节点ID（即前驱节点ID）
      const previousNodeIds = edges[0]?.sourceNodeId || null;

      return previousNodeIds;
    },
    //保存配置
    handleSubmitConfig() {
      console.log("nodeInfo", this.nodeInfo);
      this.$refs.basicForm.validate((valid) => {
        if (!valid) return;
        let params = {
          ...this.basicForm,
          formKeys: this.dealTags?.map((x) => x.formKey),
          taskDefinitionKey: this.nodeInfo.id,
          flowKey: this.flowKey,
          flowId: this.obj.flowId || this.flowId,
          taskDefinitionName: this.nodeInfo.text.value,
          ccUserNames: this.basicForm.ccUserNames?.join(","),
          ccType: this.basicForm.ccType?.join(","),
        };
        const beforeNodeDefKey = this.getPreviousNodeIds(this.nodeInfo.id);
        if (this.isFirstProcessNode) {
          params.messageTypes = [];
          params.allowedBack = "";
          params.allowedInvalidate = "";
          params.handleAging = "";
          params.overRemindAging = "";
          params.handlerUserSettings = "N";
          params.dingDingNoticeFlag = "01";
        } else if (params.handlerUserSettings == "Y") {
          if (!beforeNodeDefKey || beforeNodeDefKey.length == 0) {
            this.$message.error("该节点暂无上一个节点，请先设置上一个节点！");
            return;
          } else {
            params.beforeNodeDefKey = beforeNodeDefKey;
          }
        }

        api.saveNodeConfig(params).then((res) => {
          this.flowId = this.obj?.flowId;
          this.$message.success("配置保存成功！");
          this.$refs.LF.setProperties();
          this.drawer = false;
        });
      });
    },
    //指定处理下拉选项改变
    async changeSelectType(val) {
      this.chooseType = val;
      // this.basicForm.name = [];
      this.$set(this.basicForm, "name", null);
      const apiArr = [
        {
          value: "2",
          title: "指定人",
          params: {},
          api: "getUserList",
          dictValue: "userId",
          dictLabel: "nickName",
        },
        {
          value: "3",
          title: "指定组",
          params: { pageNum: 1, pageSize: 99999, status: 0 },
          api: "getGroupList",
          dictValue: "groupId",
          dictLabel: "groupName",
        },
        {
          value: "5",
          title: "指定角色",
          params: { pageNum: 1, pageSize: 99999, status: 0 },
          api: "getRoleList",
          dictValue: "roleId",
          dictLabel: "roleName",
        },
      ];
      if (val) {
        this.selectLoading = true;
        const obj = apiArr.find((item) => item.value == val);
        const res = await api[obj.api](obj.params);
        this.selectLoading = false;
        this.handleOptions = res.data?.map((x) => {
          return {
            ...x,
            dictLabel: x[obj.dictLabel],
            dictValue: x[obj.dictValue],
          };
        });
      } else {
        this.handleOptions = [];
      }
    },
    getTaskBizParams() {
      const params = {
        flowId: this.obj?.flowId || this.flowId,
        flowKey: this.flowKey,
        taskDefinitionKey: this.nodeInfo.id,
      };
      api.queryNodeConfig(params).then((res) => {
        if (res?.code === "10000") {
          const { ccUserNames = "", ccType = "", ...rest } = res.data;
          this.basicForm = {
            ...this.basicForm,
            ...rest,
            ccUserNames: ccUserNames ? ccUserNames.split(",") : [],
            ccType: ccType ? ccType.split(",") : [],
          };
        }
      });
      api.queryBindForm(params).then((res) => {
        this.dealTags = res.data?.map((x) => {
          return { ...x, formTitle: x.formName };
        });
      });
      // let params = {
      //   taskDefinitionKey: this.nodeInfo.id,
      //   flowKey: this.flowKey,
      //   flowId: this.obj?.flowId || this.flowId,
      // };
      // api.getTaskBizParams(params).then((res) => {
      //   this.$set(this.form1, "projectPeriod", res.data.projectPeriod);
      // });

      // getFlowConfigs(params).then((response) => {
      //   if (response.data != undefined) {
      //     this.ruleForm = response.data;
      //     this.ways = response.data.ways == undefined ? [] : response.data.ways;
      //   }
      // });
    },

    //获取用户列表
    async getUserList() {
      await api.getUserList({}).then((res) => {
        this.userOptions = res.data?.map((x) => {
          return {
            ...x,
            dictValue: x.userName,
            dictLabel: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    //获取部门列表
    async getBranchList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      await api.getUserAvailGroups(params).then((res) => {
        this.signDepartmentOption = res.data;
      });
    },
    //获取流程类型
    getFlowTypeOptions() {
      let params = {};
      params.typeStatus = "1";
      flowCateOptions(params).then((res) => {
        this.flowTypeOptions = res.data;
      });
    },
    //更新节点文本信息
    changeNodeName() {
      this.$set(this.obj, "nodeText", this.form1.nodeName);
      this.$refs.LF.updateText();
      this.$message.success("节点名称设置成功");
    },
    changeType(val) {
      this.chooseType = val;
      if (val == 1) {
        if (this.activeName == "second") {
          this.$refs.userOrBranch.form.name = "";
          this.$refs.userOrBranch.form.value = "";
        }
      } else if (val == 2) {
        this.visibleUserList = true;
        this.multiple = false;
      } else if (val == 3) {
        this.visibleUserList = true;
        this.multiple = true;
      } else if (val == 4) {
        this.visibleDepartmentList = true;
        this.multiple = true;
      } else if (val == 5) {
        this.userOrBranchArr = [
          {
            userId:
              this.activeName == "second"
                ? this.$refs.userOrBranch.form.name
                : this.$refs.userOrBranch2.form.name,
          },
        ];
        this.multiple = false;
      }
    },
    getProjectPeriodList() {
      return this.getDicts("pm_project_period").then((response) => {
        this.projectPeriodList = response?.data;
      });
    },
    async open(val, isView = false, isFirst = false) {
      this.isFirstProcessNode = isFirst;
      this.isDrawerView = isView;
      this.$set(this.basicForm, "selectType", "");
      this.$set(this.basicForm, "name", undefined);
      await this.getDetail();
      this.drawer = true;
      console.log("[ 节点信息 ] >", val);
      if (val.properties.condition) {
        if (val.properties.condition == ">") {
          // val.properties.condition = "&lt;"
          val.properties.condition = "大于";
        } else if (val.properties.condition == "<") {
          // val.properties.condition = "&gt;"
          val.properties.condition = "小于";
        } else if (val.properties.condition == ">=") {
          // val.properties.condition = "&lt;="
          val.properties.condition = "大于等于";
        } else if (val.properties.condition == "<=") {
          // val.properties.condition = "&gt;="
          val.properties.condition = "小于等于";
        } else if (val.properties.condition == "=") {
          val.properties.condition = "等于";
        }
      }
      this.nodeInfo = val;
      this.handleUserFormDetail();
      // 查询业务参数(项目阶段...)
      this.getTaskBizParams();
      //判断是否非连线
      if (!val.id.includes("Flow_")) {
        this.$set(this.form1, "bpmnType", val.type);
        this.$set(this.form1, "nodeName", val.text.value);
        let nodes = this.$refs.LF.lf.getNodeModelById(this.nodeInfo.id)
          .graphModel.nodes;
        console.log("11111", nodes);
        this.rejectIdOptions = [];
        nodes.map((el) => {
          this.rejectIdOptions.push(el);
        });
        //获取节点信息--回显
        setTimeout(() => {
          this.$refs.LF.getProperties();
        }, 500);

        //会签信息回显
        if (val.id.includes("Mult_")) {
          if (Object.hasOwn(val.properties, val.id + "Users")) {
            if (val.properties[val.id + "Users"].length > 0) {
              this.form5.signUserIds = val.properties[val.id + "Users"].split(
                ","
              );
            }
          } else {
            this.form5.signUserIds = [];
          }
          if (Object.hasOwn(val.properties, val.id + "Deps")) {
            if (val.properties[val.id + "Deps"].length > 0) {
              this.form5.signDepIds = val.properties[val.id + "Deps"].split(
                ","
              );
            }
          } else {
            this.form5.signDepIds = [];
          }
          if (Object.hasOwn(val.properties, val.id + "Rule")) {
            this.form5.value = val.properties[val.id + "Rule"];
          } else {
            this.form5.value = "";
          }
        }
      } else {
        this.activeName = "sixth";
        console.log("是连线判断");
        let nodes = this.$refs.LF.lf.getEdgeModelById(this.nodeInfo.id)
          .graphModel.nodes;
        // console.log("11111", nodes)
        this.rejectIdOptions = [];
        nodes.map((el) => {
          if (el.type == "bpmn:userTask") {
            this.rejectIdOptions.push(el);
          }
        });
        //清空规则
        this.form6 = {
          radio: "1",
          value: "", //自定义条件规则
          targetNode: undefined, //目标节点
          targetForm: undefined, //目标表单
          judgeObject: undefined, //判断对象
          condition: undefined, //判断条件
          conditionValue: "", //判断条件值
        };
        //回显规则
        if (this.nodeInfo.properties.radio) {
          this.$nextTick(() => {
            this.form6 = this.nodeInfo.properties;
            this.getTargetForm(this.form6.targetNode);
            this.getJudgeObject(this.form6.targetForm);
          });
        }
      }
    },
    handleClose(done) {
      this.drawer = false;
      // this.$confirm("确认关闭？")
      //   .then((_) => {
      //     done();
      //   })
      //   .catch((_) => {});
    },
    //切换tab
    changeTab() {
      if (this.activeName == "second") {
        // this.getAllBindForms(1); //处理
        // this.getAllBindForms(2); //查询
      } else if (this.activeName == "third" || this.activeName == "fourth") {
        //回显
        this.getBizConfigs();
      }
    },

    // getReminderConfig(val) {
    //   let params = {
    //     taskDefinitionKey: this.nodeInfo.id,
    //     flowKey: this.obj.flowKey,
    //     flowId: this.obj.flowId || this.flowId,
    //   };
    //   getReminderConfig(params).then((res) => {
    //     if (res.data.configStatus !== undefined) {
    //       this.$set(this.form1, "configStatus", res.data.configStatus);
    //       this.$set(this.form1, "taskExceptTime", res.data.taskExceptTime);
    //       this.$set(this.form1, "reminderTimeValue", res.data.reminderTimeValue);
    //       this.$set(this.form1, "ways", res.data.ways ? res.data.ways : []);
    //     } else {
    //       // 默认值
    //       this.$set(this.form1, "configStatus", 0);
    //       this.$set(this.form1, "reminderTimeValue", "3");
    //       this.$set(this.form1, "taskExceptTime", undefined);
    //       this.$set(this.form1, "ways", ["1"]);
    //     }
    //   });
    // },

    //获取绑定表单
    getAllBindForms(val) {
      let params = {
        taskDefinitionKey: this.nodeInfo.id,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
      };
      api.getAllBindForms(params).then((res) => {
        //清空之前存储的表单内容
        this.checkTags.length = 0;
        this.dealTags.length = 0;
        this.dealTags = res.data.handleForms;
        this.checkTags = res.data.queryForms;
      });
    },
    //获取绑定表单--判断
    getTargetForm(id) {
      let params = {
        taskDefinitionKey: id,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
      };
      api.getAllBindForms(params).then((res) => {
        this.$nextTick(() => {
          this.targetFormOptions = res.data.handleForms;
        });
      });
    },
    //改变目标节点
    changeTargetNode(id) {
      this.$set(this.form6, "targetForm", undefined);
      this.$set(this.form6, "judgeObject", undefined);
      this.getTargetForm(id);
    },
    //改变目标表单
    changeTargetForm(key) {
      this.$set(this.form6, "judgeObject", undefined);
      this.getJudgeObject(key);
    },
    //获取判断对象列表
    getJudgeObject(key) {
      let params = {
        formKey: key,
      };
      api.queryFormVariables(params).then((res) => {
        this.$nextTick(() => {
          this.judgeObjectOptions = res.data;
        });
      });
    },
    //获取业务回显
    getBizConfigs() {
      let params = {
        taskDefinitionKey: this.nodeInfo.id,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
      };
      api.getBizConfigs(params).then((res) => {
        this.tags4 = res.data.compensations;
        let obj = {
          isReject: res.data.isReject,
          isInvalid: res.data.isInvalid,
          isReCall: res.data.isReCall,
          isApprove: res.data.isApprove == undefined ? "N" : res.data.isApprove,
          rejectId: res.data.rejectId,
        };
        this.form3 = Object.assign({}, this.form3, obj);

        console.log("form3: ", this.form3);
      });
    },
    //查询表单
    changeFormCheck(val) {
      this.checkTags = [];
      val.forEach((element) => {
        this.checkTags.push({
          formName: element.formName,
          formDefId: element.formDefId,
        });
      });
    },
    //处理表单
    changeFormDeal(val) {
      this.dealTags = [];
      val.forEach((element) => {
        this.dealTags.push({
          formTitle: element.formTitle,
          formKey: element.formKey,
        });
      });
    },
    //关联业务
    changeBusiness(val) {
      this.tags4 = [];
      val.forEach((element) => {
        this.tags4.push({
          compensationName: element.compensationName,
          compensationId: element.compensationId,
        });
      });
    },
    handleCloseCheckTag(tag) {
      this.checkTags.splice(this.checkTags.indexOf(tag), 1);
    },
    handleCloseDealTag(tag) {
      this.dealTags.splice(this.dealTags.indexOf(tag), 1);
    },
    handleCloseTag4(tag) {
      this.tags4.splice(this.tags4.indexOf(tag), 1);
    },
    //选择用户
    chooseUserList(val) {
      this.userOrBranchArr = val;
    },
    chooseDepartmentList(val) {
      this.userOrBranchArr = val;
    },
    // 第一个TAB页保存
    onSubmit1() {
      let that = this;

      let bizParams = {
        projectPeriod: this.form1.projectPeriod,
      };

      let params = {
        ...this.form1,
        taskDefinitionKey: this.nodeInfo.id,
        bpmnType: this.nodeInfo.type,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
        bizParams: bizParams,
      };

      this.$refs["form1"].validate((valid) => {
        if (valid) {
          api.basicConfigBind(params).then((res) => {
            setTimeout(() => {
              let ruleParams = {
                ...that.ruleForm,
                flowId: res.data,
                taskDefinitionKey: params.taskDefinitionKey,
                ways: this.ways,
              };
              saveApproveConfig(ruleParams);

              that.$set(that.obj, "flowId", res.data);
              that.flowId = res.data;
              that.$message.success("审批设置保存成功！");
              that.$refs.LF.setProperties();
            }, 500);
          });
        }
      });
    },
    //第二个tab保存
    onSubmit2() {
      let that = this;
      let queryFormDefIds = [];
      let handleFormDefIds = [];
      this.checkTags.forEach((element) => {
        queryFormDefIds.push(element.formDefId);
      });
      this.dealTags.forEach((element) => {
        handleFormDefIds.push(element.formDefId);
      });
      // if (queryFormDefIds.length == 0 && handleFormDefIds.length == 0) {
      //   this.$message.warning("查询表单、处理表单必选一项!")
      //   return
      // }
      let params = {
        bindType: "1",
        queryFormDefIds: queryFormDefIds,
        handleFormDefIds: handleFormDefIds,
        taskDefinitionKey: this.nodeInfo.id,
        bpmnType: this.nodeInfo.type,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
      };
      api.handleBindTab(params).then((res) => {
        this.$nextTick(() => {
          this.chooseType = this.$refs.userOrBranch.form.selectType;
        });
        if (this.$refs.userOrBranch.form.selectType == 5) {
          this.userOrBranchArr = [
            {
              userId: this.$refs.userOrBranch.form.name,
            },
          ];
        }

        setTimeout(() => {
          that.$set(that.obj, "flowId", res.data);
          that.flowId = res.data;
          that.$message.success("处理设置保存成功！");
          that.$refs.LF.setProperties();
        }, 500);
      });
    },
    onSubmit3() {
      let that = this;
      // let tags = [];
      // this.tags.forEach((element) => {
      //   tags.push(element.formDefId);
      // });
      let params = {
        bindType: "2",
        // formDefIds: tags,
        ...this.form3,
        taskDefinitionKey: this.nodeInfo.id,
        bpmnType: this.nodeInfo.type,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
      };
      api.approveConfigBind(params).then((res) => {
        // this.$nextTick(() => {
        //   this.chooseType = this.$refs.userOrBranch2.form.selectType;
        // });
        // if (this.$refs.userOrBranch2.form.selectType == 5) {
        //   this.userOrBranchArr = [
        //     {
        //       userId: this.$refs.userOrBranch2.form.name
        //     }
        //   ]
        // }
        setTimeout(() => {
          that.$set(that.obj, "flowId", res.data);
          that.flowId = res.data;
          that.$message.success("审批设置保存成功！");
          that.$refs.LF.setProperties();
        }, 500);
      });
    },
    onSubmit4() {
      let that = this;
      let tags = [];
      this.tags4.forEach((element) => {
        tags.push(element.compensationId);
      });
      let params = {
        // ...this.form4,
        compensationIds: tags,
        taskDefinitionKey: this.nodeInfo.id,
        bpmnType: this.nodeInfo.type,
        flowKey: this.obj.flowKey,
        flowId: this.obj.flowId || this.flowId,
      };
      api.bizConfigBind(params).then((res) => {
        this.$set(this.obj, "flowId", res.data);
        that.flowId = res.data;
        this.$message.success("业务配置保存成功！");
        that.$refs.LF.setProperties();
      });
    },
    //会签tab保存
    onSubmit5() {
      if (
        this.form5.signUserIds.length == 0 &&
        this.form5.signDepIds.length == 0
      ) {
        this.$message.warning("请选择会签处理人或处理部门");
      } else {
        if (this.form5.signUserIds.length > 0) {
          let key = this.nodeInfo.id + "Users";
          let obj = {};
          this.$set(obj, key, this.form5.signUserIds.toString());
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
        } else {
          let key = this.nodeInfo.id + "Users";
          let obj = {};
          this.$set(obj, key, "");
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
        }
        if (this.form5.signDepIds.length > 0) {
          let key = this.nodeInfo.id + "Deps";
          let obj = {};
          this.$set(obj, key, this.form5.signDepIds.toString());
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
        } else {
          let key = this.nodeInfo.id + "Deps";
          let obj = {};
          this.$set(obj, key, "");
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
        }
        if (this.form5.value) {
          let key = this.nodeInfo.id + "Rule";
          let obj = {};
          this.$set(obj, key, this.form5.value);
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
        } else {
          let key = this.nodeInfo.id + "Rule";
          let obj = {};
          this.$set(obj, key, "");
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
        }
        // this.$message.success("保存成功")

        let that = this;
        let params = {
          bindType: "2",
          isSignApprove: "Y",
          taskDefinitionKey: this.nodeInfo.id,
          bpmnType: this.nodeInfo.type,
          flowKey: this.obj.flowKey,
          flowId: this.obj.flowId || this.flowId,
        };
        api.approveConfigBind(params).then((res) => {
          setTimeout(() => {
            that.$set(that.obj, "flowId", res.data);
            that.flowId = res.data;
            that.$message.success("保存成功！");
            that.$refs.LF.setProperties();
          }, 500);
        });
      }
    },
    //连线tab保存
    onSubmit6() {
      if (this.form6.radio == "1") {
        if (this.form6.condition) {
          //选择配置
          let obj = this.form6;
          let rule =
            "${" +
            this.form6.judgeObject +
            this.form6.condition +
            this.form6.conditionValue +
            "}";
          obj.rule = rule;
          console.log("连线--obj", obj);
          // return
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
          this.$message.success("保存成功");
        } else {
          this.$message.warning("请填写完整条件规则");
        }
      } else if (this.form6.radio == "2") {
        if (this.form6.value) {
          //选择配置
          let obj = this.form6;
          obj.rule = this.form6.value;
          this.$refs.LF.lf.setProperties(this.nodeInfo.id, obj);
          this.$message.success("保存成功");
        } else {
          this.$message.warning("请填写条件规则");
        }
      }
      // this.$message.warning("请填写条件规则")
    },
    async getDetail() {
      const res = await api.queryDetail({ flowKey: this.flowKey });
      if (res.code === "10000") {
        this.obj = res.data;
        console.log("[ res.data ] >", res.data);
      }
    },
  },
  mounted() {
    if (this.$route.query.flowKey || this.drawerFlowKey) {
      this.flowKey = this.$route.query.flowKey || this.drawerFlowKey;
      this.getDetail();
    }
    // 获取流程类型
    // this.getFlowTypeOptions();
    // 获取用户列表
    this.getUserList();
    // 获取部门列表
    // this.getBranchList();

    // this.getProjectPeriodList();
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
// /deep/.lf-canvas-overlay {
//   min-height: calc(100vh - 120px);
// }

/deep/ .el-drawer__body {
  padding: 0 20px;
}

/deep/ .el-input-group__prepend {
  width: 100px;
}

/deep/ .el-input-group__append {
  padding: 0 10px;
}

.tab-row-wrap {
  display: flex;
}
</style>
