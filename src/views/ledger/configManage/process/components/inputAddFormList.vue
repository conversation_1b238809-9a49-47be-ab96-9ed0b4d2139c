<!-- 关联表单列表组件 -->
<template>
  <div class="">
    <el-link
      type="primary"
      :underline="false"
      v-model="content"
      @click="handleInput"
      :disabled="disabled"
    >
      <i class="el-icon-circle-plus-outline" />
      <span style="margin-left:3px;">
        添加
      </span>
    </el-link>

    <el-dialog
      :title="title"
      v-if="visible"
      :visible.sync="visible"
      @close="handleClose"
      width="60%"
      append-to-body
      :close-on-click-modal="false"
    >
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :pagerProps="pagerProps"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :tableProps="tableProps"
        :modalConfig="modalConfig"
        @loadData="getList"
        @handleReset="handleReset"
        selectStyleType="infoTip"
        @handleBatchSelect="handleSelectionChange"
        :showSelectNum="showSelectNum"
      ></BuseCrud>
      <!-- <el-table
        v-loading="loading"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        :data="tableData"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          label="表单名称"
          align="center"
          prop="formName"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.formStatus == "1" ? "启用" : "未启用" }}
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="formDefDesc"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建人"
          align="center"
          prop="defCreator"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建时间"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
      </el-table> -->

      <div slot="footer" class="dialog-footer">
        <!-- <div></div>
        <div> -->
        <el-button type="primary" @click="handleOk">确 定</el-button>
        <el-button @click.stop="handleClose">取 消</el-button>
        <!-- </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { list, queryPropOptions } from "@/api/ledger/formDesign.js";
import { initParams } from "@/utils/buse.js";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: ["newValue", "disabled"],
  model: {
    event: "input-change",
    prop: "newValue",
  },
  data() {
    //这里存放数据
    return {
      content: this.newValue,
      visible: false,
      title: "选择表单",
      pageTotal: 0,
      multipleSelection: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "formKey",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
          trigger: "row",
        },
      },
      tableData: [],
      tableColumn: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "formTitle",
          title: "表单名称",
          width: 150,
        },
        {
          field: "formType",
          title: "表单类别",
          width: 150,
          formatter: ({ cellValue }) => {
            return cellValue == "01"
              ? "创建表单"
              : cellValue == "02"
              ? "处理表单"
              : cellValue;
          },
        },
        {
          field: "formAttr",
          title: "表单属性",
          width: 150,
        },
        {
          field: "formCreator",
          title: "创建人",
          width: 150,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
    };
  },
  computed: {
    showSelectNum() {
      return this.multipleSelection?.length > 0;
    },
    modalConfig() {
      return {
        addBtn: false,
        menu: false,
      };
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "80px",
        //筛选控件配置
        config: [
          //输入框
          {
            field: "formTitle",
            element: "el-input",
            title: "表单名称",
          },
          {
            field: "formType",
            title: "表单类别",
            element: "el-select",
            props: {
              options: [
                { label: "创建表单", value: "01" },
                { label: "处理表单", value: "02" },
              ],
            },
          },
          {
            field: "formAttr",
            title: "表单属性",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb);
              },
            },
          },
        ],
        params: this.params,
      };
    },
  },
  watch: {},
  //方法集合
  methods: {
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.getList();
    },
    handleClose() {
      this.visible = false;
      this.multipleSelection = [];
    },
    querySearch(queryString, cb) {
      queryPropOptions({
        itemAttributeName: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    handleInput() {
      this.visible = true;
      // this.$emit('input-change', this.content)
    },
    handleSelectionChange(val) {
      console.log("[ this. ] >", val);
      this.multipleSelection = val;
    },

    handleOk() {
      if (this.multipleSelection.length >= 1) {
        this.$emit("changeForm", this.multipleSelection);
        this.$emit("input-change", this.content);
        this.visible = false;
        this.handleClose();
      } else {
        this.$message.warning("请选择一条表单");
      }
    },
    getList() {
      // let params = {
      //   pageNum: 1,
      //   pageSize: 9999,
      //   formStatus: 1,
      // };
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        formStatus: 1,
      };
      this.loading = true;
      list(params)
        .then((res) => {
          this.tableData = res.data;
          this.tablePage.total = res.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.params = initParams(this.filterOptions.config);
    this.getList();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
.el-icon-circle-plus-outline {
  font-size: 20px;
}
/deep/.el-link--inner {
  display: flex;
  align-items: center;
}
</style>
