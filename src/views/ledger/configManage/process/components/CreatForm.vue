<!-- 表单生成 -->
<template>
  <div class="">
    <el-button @click="getTestApi()">移动端接口-空表单</el-button>
    <el-button @click="getTaskConfigs()">移动端接口-按钮类型</el-button>
    <el-button @click="startWithForm()">移动端接口-提交</el-button>

    <form-create :rule="rule" v-model="fApi" :option="options" />
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  taskFormsFirstDeal,
  taskConfigs,
  startWithForm
} from "@/api/process/lfDesign.js";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      fApi: {},
      options: {},
      rule: [],
      formTitle: "",
      bindType: "",
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    taskFormsFirstDeal() {
      let params = {
        flowId: 92,
        flowKey: "process_220914_4d4dae3f",
      }
      taskFormsFirstDeal(params).then(res => {
        console.log('[ 测试移动端res ] >', res)
        this.options = JSON.parse(res.data.formConfig)
        this.rule = JSON.parse(res.data.formJson)
        this.formTitle = res.data.formTitle
        this.bindType = res.data.bindType
        console.log('[ options ] >', JSON.parse(options))
        console.log('[ rule ] >', JSON.parse(rule))
        console.log('[ formTitle ] >', formTitle)
      })
    },
    // 按钮类型
    getTaskConfigs() {
      let params = {
        taskId: "Activity_2a6kqji",
        flowId: 89
      }
      taskConfigs(params).then(res => {
        console.log('[ 按钮类型 ] >', res.data.config)
      })
    },
    //提交
    startWithForm() {
      let params = {
        preDealForms: [
          {
            formId: 19,
            formTitle: this.formTitle,
            bindType: this.bindType,
            formJson: JSON.stringify(this.fApi.rule),
            formConfig: JSON.stringify(this.options),
          }
        ],
        flowKey: "process_220914_4d4dae3f",
        businessKey: undefined,
      }
      startWithForm(params).then(res => {
        console.log('[ res.data ] >', res.data)
      })
    },
    getFormModel() {
      this.rule = [
        {
          type: "input",
          field: "rqt5w0qimv89",
          title: "姓名",
          info: "",
          _fc_drag_tag: "input",
          hidden: false,
          display: true,
        },
        {
          type: "checkbox",
          field: "mvr5w0qing22",
          title: "性别",
          info: "",
          effect: {
            fetch: "",
          },
          options: [
            {
              value: "1",
              label: "男",
            },
            {
              value: "2",
              label: "女",
            },
          ],
          _fc_drag_tag: "checkbox",
          hidden: false,
          display: true,
        },
      ];
      this.options = {
        form: {
          labelPosition: "right",
          size: "mini",
          labelWidth: "125px",
          hideRequiredAsterisk: false,
          showMessage: true,
          inlineMessage: false,
        },
        submitBtn: false,
        resetBtn: false,
      };
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // setTimeout(() => {
    //   this.getFormModel()
    // }, 1000);
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>