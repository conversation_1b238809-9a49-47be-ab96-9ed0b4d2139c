<template>
  <div>
    <el-row style="padding: 20px 20px 0 20px;" v-if="!isSilentMode && visible">
      <el-form
        :model="obj"
        ref="baseForm"
        :rules="baseRules"
        label-width="100px"
      >
        <el-col :span="8">
          <el-form-item label="流程名称" prop="flowName">
            <el-input
              v-model="obj.flowName"
              placeholder="请输入流程名称"
              maxlength="1000"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="flowRemark">
            <el-input
              v-model="obj.flowRemark"
              clearable
              type="textarea"
              :maxlength="500"
              placeholder="长度限制在500个字符以内"
              show-word-limit
              :rows="2"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="display: flex;justify-content: flex-end;">
          <el-button @click="handleSave" type="primary">保存</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-col>
      </el-form>
    </el-row>

    <!-- <el-row
      v-if="!isSilentMode && visible"
      type="flex"
      justify="end"
      style="padding:10px;"
    >
      <el-button
        @click="
          saveXML();
          delVisitedView();
        "
        type="primary"
        style="margin-left: 16px"
      >
        保存
      </el-button>
    </el-row> -->
    <el-divider v-if="!isSilentMode"></el-divider>
    <div
      :class="{ container: !isSilentMode, 'mini-container': isSilentMode }"
      ref="container"
    ></div>
    <!-- 节点信息展示弹框 -->
    <div v-if="showNodeInfo" :style="popupStyle">
      <div>审批人：{{ currentNodeInfo.name }}</div>
      <div>开始时间：{{ currentNodeInfo.startTime }}</div>
      <div>结束时间：{{ currentNodeInfo.endTime }}</div>
    </div>
  </div>
</template>

<script>
import * as api from "@/api/ledger/process.js";

import LogicFlow from "@logicflow/core";
import "@logicflow/core/dist/style/index.css";
import {
  Menu,
  MiniMap,
  Control,
  DndPanel,
  SelectionSelect,
  BpmnElement,
  BpmnAdapter,
  BpmnXmlAdapter,
  lfJson2Xml,
  lfXml2Json,
} from "@logicflow/extension";
import "@logicflow/extension/lib/style/index.css";
// LogicFlow.use(Menu);
// LogicFlow.use(Control);
// LogicFlow.use(DndPanel);
// LogicFlow.use(SelectionSelect);
LogicFlow.use(MiniMap);
// LogicFlow.use(BpmnElement);
// LogicFlow.use(BpmnXmlAdapter);

//自定义节点
import multiInstanceLoopCharacteristics from "./registerNode/multiInstanceLoopCharacteristics";
import parallelGateway from "./registerNode/parallelGateway";
import exclusiveGateway from "./registerNode/exclusiveGateway";
import userTask from "./registerNode/userTask";

export default {
  props: {
    isDrawer: {
      type: Boolean,
      default: false,
    },
    obj: {
      type: Object,
      default: () => {
        return { flowName: "", flowRemark: "" };
      },
    },
    nodeInfo: {
      type: Object,
      default: () => {},
    },
    arr: {
      type: Array,
      default: () => [],
    },
    chooseType: {
      type: Number | String,
      default: "",
    },
    isSilentMode: {
      type: Boolean, //是否开启静默模式，默认不开启
      default: false,
    },
    visible: {
      type: Boolean, //是否展示，默认展示
      default: true,
    },
    flowKey: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      baseForm: { flowName: "", flowRemark: "" },
      baseRules: {
        flowName: [
          { required: true, message: "流程名称不能为空！", trigger: "change" },
        ],
      },
      lf: null,
      data: {
        nodes: [],
        edges: [],
      },
      xml: null,
      closeTab: true,
      nodeProperties: {},

      showNodeInfo: false,
      currentNodeInfo: {}, // 当前节点信息
      popupStyle: {
        position: "absolute",
        backgroundColor: "#fff",
        padding: "8px",
        boxShadow: "0 2px 5px rgba(0, 0, 0, 0.5)",
        zIndex: 999,
        borderRadius: "10px",
        fontSize: "12px",
      },
      processId: "",
    };
  },
  watch: {
    obj: {
      handler(val) {
        const { flowData } = val;
        if (flowData) {
          console.log(flowData, "----flowData");
          this.getlfXml2Json(val);
          this.lf.render(this.data);
        }
      },
    },
  },
  methods: {
    //判断是否是开始节点后的第一个流程节点
    calculateIsFirst(node) {
      const json = this.lf.getGraphData();
      console.log(json, "=====json");
      const processNode = json["bpmn:definitions"]["bpmn:process"];
      const line = processNode["bpmn:startEvent"]?.["bpmn:outgoing"] || "none";
      let firstId;
      if (Array.isArray(processNode["bpmn:userTask"])) {
        //第一种情况：有多个流程节点，则bpmn:userTask是个数组
        firstId =
          processNode["bpmn:userTask"]?.find(
            (x) => x?.["bpmn:incoming"] === line
          )?.["-id"] || null;
      } else {
        //第二种情况：只有一个流程节点，则bpmn:userTask是个对象
        firstId =
          processNode["bpmn:userTask"]["bpmn:incoming"] === line
            ? processNode["bpmn:userTask"]["-id"]
            : null;
      }

      return node.id === firstId;
    },
    async handleSave() {
      const res = await this.saveXML(true);
      if (res) {
        if (this.isDrawer) {
          this.$emit("closeDrawer");
        } else {
          this.handleBack();
        }
      }
    },
    handleBack() {
      if (this.closeTab) {
        this.$router.back();
      }
      this.delVisitedView();
    },
    delVisitedView() {
      this.$store.dispatch("tagsView/delVisitedView", this.$route);
    },
    //设置节点属性
    setProperties() {
      console.log("[ this.arr--setProperties ] >", this.arr);
      this.lf.setProperties(this.nodeInfo.id, {}); //首先清除原来的属性
      let assignee = [];
      this.lf.deleteProperty(this.nodeInfo.id, "flowable:assignee");
      this.lf.deleteProperty(this.nodeInfo.id, "flowable:candidateGroups");
      this.lf.deleteProperty(this.nodeInfo.id, "flowable:candidateRoles");
      if (this.chooseType == "2") {
        //指定人
        this.arr.forEach((element) => {
          assignee.push(element.userId);
        });
        console.log("指定人", assignee);
        this.lf.setProperties(this.nodeInfo.id, {
          "flowable:assignee": assignee.toString(),
          "whale-flow:selectType": "2",
          "whale-flow:tab": this.nodeInfo.properties["whale-flow:tab"],
        });
      } else if (this.chooseType == "3") {
        //指定组
        this.arr.forEach((element) => {
          assignee.push(element.userId);
        });
        this.lf.setProperties(this.nodeInfo.id, {
          "flowable:candidateGroups": assignee.toString(),
          "whale-flow:selectType": "3",
          "whale-flow:tab": this.nodeInfo.properties["whale-flow:tab"],
        });
      } else if (this.chooseType == "5") {
        //指定角色
        this.arr.forEach((element) => {
          assignee.push(element.userId);
        });
        this.lf.setProperties(this.nodeInfo.id, {
          "flowable:candidateGroups": assignee.toString(),
          "whale-flow:selectType": "5",
          "whale-flow:tab": this.nodeInfo.properties["whale-flow:tab"],
        });
      }
      // if (this.chooseType == "1") {
      //   this.lf.setProperties(this.nodeInfo.id, {
      //     flowable_assignee: "",
      //     "whale-flow_selectType": "1",
      //     "whale-flow_tab": this.nodeInfo.properties["whale-flow_tab"],
      //   });
      // } else if (this.chooseType == "2") {
      //   //用户单选
      //   this.arr.forEach((element) => {
      //     assignee.push(element.userId);
      //   });
      //   this.lf.setProperties(this.nodeInfo.id, {
      //     flowable_assignee: assignee.toString(),
      //     "whale-flow_selectType": "2",
      //     "whale-flow_tab": this.nodeInfo.properties["whale-flow_tab"],
      //   });
      // } else if (this.chooseType == "3") {
      //   //用户多选
      //   this.arr.forEach((element) => {
      //     assignee.push(element.userId);
      //   });
      //   this.lf.setProperties(this.nodeInfo.id, {
      //     flowable_candidateUsers: assignee.toString(),
      //     "whale-flow_selectType": "3",
      //     "whale-flow_tab": this.nodeInfo.properties["whale-flow_tab"],
      //   });
      // } else if (this.chooseType == "4") {
      //   //部门多选
      //   this.arr.forEach((element) => {
      //     assignee.push(element.groupId);
      //   });
      //   this.lf.setProperties(this.nodeInfo.id, {
      //     flowable_candidateGroups: assignee.toString(),
      //     "whale-flow_selectType": "4",
      //     "whale-flow_tab": this.nodeInfo.properties["whale-flow_tab"],
      //   });
      // } else if (this.chooseType == "5") {
      //   //指定值
      //   console.log("[ 设置指定值5 ] >", this.arr);
      //   this.arr.forEach((element) => {
      //     assignee.push(element.userId);
      //   });
      //   this.lf.setProperties(this.nodeInfo.id, {
      //     flowable_assignee: assignee.toString(),
      //     "whale-flow_selectType": "5",
      //     "whale-flow_tab": this.nodeInfo.properties["whale-flow_tab"],
      //   });
      // }
      this.closeTab = false;
      this.saveXML();
    },
    // 获取节点属性
    getProperties() {
      this.nodeProperties = this.lf
        .getNodeModelById(this.nodeInfo.id)
        .getProperties();
    },
    //更新节点名称
    updateText() {
      this.lf.getNodeModelById(this.nodeInfo.id).updateText(this.obj.nodeText);
    },
    saveXML(msg = false) {
      return new Promise((resolve, reject) => {
        this.$refs.baseForm.validate(async (valid) => {
          if (!valid) {
            resolve(false);
            return;
          }
          // json数据转成xml
          let jsonData = this.lf.getGraphData();
          console.log("jsonData", jsonData);
          //是否有开始结束节点
          const hasEndNode = jsonData["bpmn:definitions"][
            "bpmn:process"
          ]?.hasOwnProperty("bpmn:endEvent");
          const hasStartNode = jsonData["bpmn:definitions"][
            "bpmn:process"
          ]?.hasOwnProperty("bpmn:startEvent");
          //保存时必须有开始结束节点
          if (msg && (!hasEndNode || !hasStartNode)) {
            this.$message.error("请添加开始节点和结束节点");
            resolve(false);
            return;
          }
          //为了处理回显偏移问题
          jsonData["bpmn:definitions"]["bpmndi:BPMNDiagram"][
            "bpmndi:BPMNPlane"
          ]["bpmndi:BPMNShape"].forEach((el) => {
            if (el["-bpmnElement"].includes("Mult_")) {
              el["dc:Bounds"]["-x"] += 40;
              el["dc:Bounds"]["-y"] += 40;
            }
            if (el["-bpmnElement"].includes("Para_")) {
              el["dc:Bounds"]["-x"] += 40;
              el["dc:Bounds"]["-y"] += 40;
            }
          });
          // jsonData["bpmn:definitions"]["bpmn:process"]["-id"] = this.processId;
          this.xml = lfJson2Xml(jsonData);
          // this.xml = this.lf.getGraphData();
          console.log("this.xml", this.xml);
          // return
          let params = {
            flowName: this.obj?.flowName,
            flowRemark: this.obj?.flowRemark,
            flowData: this.xml,
            flowKey: this.flowKey,
          };
          console.log(this.obj, "-====obj");
          const method = this.obj.flowId ? "updateFlow" : "addFlow";
          const res = await api[method](params).finally(() => {
            resolve(true);
            this.closeTab = true;
          });
          if (res?.code === "10000" && msg) {
            this.$message.success("流程设计保存成功！");
          }
        });
        resolve(true);
      });
    },
    //导出xml文件
    download(filename, text) {
      var element = document.createElement("a");
      element.setAttribute(
        "href",
        "data:text/plain;charset=utf-8," + encodeURIComponent(text)
      );
      element.setAttribute("download", filename);

      element.style.display = "none";
      document.body.appendChild(element);

      element.click();

      document.body.removeChild(element);
    },
    //初始化LogicFlow
    init() {
      let that = this;
      this.lf = new LogicFlow({
        container: this.$refs.container,
        grid: this.isSilentMode ? { visible: false } : true,
        stopMoveGraph: false,
        // background: {
        //   backgroundColor: "#70a1ff"
        // },
        autoExpand: true,
        style: {
          circle: {
            fill: "#FFFFFF",
            stroke: "blue",
            strokeWidth: 2,
          },
          rect: {
            fill: "#FFFFFF",
            stroke: "blue",
            strokeWidth: 2,
          },
          diamond: {
            fill: "#FFFFFF",
            stroke: "blue",
            strokeWidth: 2,
          },
          circle: {
            fill: "#FFFFFF",
            stroke: "blue",
            strokeWidth: 2,
          },
        },
        plugins: [
          Menu,
          // MiniMap,
          // Control,
          DndPanel,
          // SelectionSelect,
          BpmnElement,
          BpmnAdapter,
          // BpmnXmlAdapter,
        ],

        isSilentMode: this.isSilentMode, //静默模式
      });
      this.lf.setTheme({
        nodeText: {
          color: "#000000",
          overflowMode: "autoWrap",
          lineHeight: 1.2,
          fontSize: 12,
        },
      });
      if (!this.isSilentMode) {
        // 为菜单追加选项（必须在 lf.render() 之前设置）
        this.lf.extension.menu.addMenuConfig({
          // 节点右键菜单
          nodeMenu: [
            {
              text: "配置",
              async callback(node) {
                //只有会签、用户节点可以打开右侧抽屉配置
                if (
                  node.type == "bpmn:userTask" ||
                  node.type == "bpmn:multiInstanceLoopCharacteristics"
                ) {
                  const res = await that.saveXML();
                  console.log(res, "=====res");
                  if (res) {
                    that.$store.dispatch("logicFlow/changeNodeType", node.type);
                    if (node.properties["whale-flow_tab"]) {
                    } else {
                      that.$set(node.properties, "whale-flow_tab", "deal");
                      that.lf.setProperties(node.id, {
                        "whale-flow_tab": "deal",
                      });
                    }

                    setTimeout(() => {
                      const isFirstProcess = that.calculateIsFirst(node);

                      that.$emit("open", node, false, isFirstProcess);
                    }, 500);
                  }
                } else {
                  that.$message.warning("仅限流程和会签节点可以进行配置");
                }
              },
            },
            {
              text: "查看配置",
              async callback(node) {
                //只有会签、用户节点可以打开右侧抽屉配置
                if (
                  node.type == "bpmn:userTask" ||
                  node.type == "bpmn:multiInstanceLoopCharacteristics"
                ) {
                  const isFirstProcess = that.calculateIsFirst(node);
                  that.$emit("open", node, true, isFirstProcess);
                } else {
                  that.$message.warning("仅限流程和会签节点可以进行配置");
                }
              },
            },
          ],
          // 边右键菜单
          edgeMenu: [
            {
              text: "配置",
              callback(edge) {
                console.log("边框edge", edge);
                //判断只有排他网关后面的边才能设置
                if (edge.sourceNodeId.includes("Excl_")) {
                  that.$store.dispatch("logicFlow/changeNodeType", edge.type);
                  that.$emit("open", edge);
                } else {
                  that.$message.warning(
                    "仅限条件判断节点后的连线可以进行表达式配置！"
                  );
                }
              },
            },
          ],
          // 画布右键菜单
          graphMenu: [
            // {
            //   text: "分享",
            //   callback() {
            //     alert("分享成功！");
            //   },
            // },
          ],
        });

        //拖拽面板---左侧菜单
        this.lf.extension.dndPanel.setPatternItems([
          // {
          //   label: "选区",
          //   icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAAOVJREFUOBGtVMENwzAIjKP++2026ETdpv10iy7WFbqFyyW6GBywLCv5gI+Dw2Bluj1znuSjhb99Gkn6QILDY2imo60p8nsnc9bEo3+QJ+AKHfMdZHnl78wyTnyHZD53Zzx73MRSgYvnqgCUHj6gwdck7Zsp1VOrz0Uz8NbKunzAW+Gu4fYW28bUYutYlzSa7B84Fh7d1kjLwhcSdYAYrdkMQVpsBr5XgDGuXwQfQr0y9zwLda+DUYXLaGKdd2ZTtvbolaO87pdo24hP7ov16N0zArH1ur3iwJpXxm+v7oAJNR4JEP8DoAuSFEkYH7cAAAAASUVORK5CYII=",
          //   callback: () => {
          //     this.lf.extension.selectionSelect.openSelectionSelect();
          //     this.lf.once("selection:selected", () => {
          //       this.lf.extension.selectionSelect.closeSelectionSelect();
          //     });
          //   },
          // },
          {
            type: "bpmn:startEvent",
            text: "开始",
            label: "开始节点",
            icon:
              "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAAnBJREFUOBGdVL1rU1EcPfdGBddmaZLiEhdx1MHZQXApraCzQ7GKLgoRBxMfcRELuihWKcXFRcEWF8HBf0DdDCKYRZpnl7p0svLe9Zzbd29eQhTbC8nv+9zf130AT63jvooOGS8Vf9Nt5zxba7sXQwODfkWpkbjTQfCGUd9gIp3uuPP8bZ946g56dYQvnBg+b1HB8VIQmMFrazKcKSvFW2dQTxJnJdQ77urmXWOMBCmXM2Rke4S7UAW+/8ywwFoewmBps2tu7mbTdp8VMOkIRAkKfrVawalJTtIliclFbaOBqa0M2xImHeVIfd/nKAfVq/LGnPss5Kh00VEdSzfwnBXPUpmykNss4lUI9C1ga+8PNrBD5YeqRY2Zz8PhjooIbfJXjowvQJBqkmEkVnktWhwu2SM7SMx7Cj0N9IC0oQXRo8xwAGzQms+xrB/nNSUWVveI48ayrFGyC2+E2C+aWrZHXvOuz+CiV6iycWe1Rd1Q6+QUG07nb5SbPrL4426d+9E1axKjY3AoRrlEeSQo2Eu0T6BWAAr6COhTcWjRaYfKG5csnvytvUr/WY4rrPMB53Uo7jZRjXaG6/CFfNMaXEu75nG47X+oepU7PKJvvzGDY1YLSKHJrK7vFUwXKkaxwhCW3u+sDFMVrIju54RYYbFKpALZAo7sB6wcKyyrd+aBMryMT2gPyD6GsQoRFkGHr14TthZni9ck0z+Pnmee460mHXbRAypKNy3nuMdrWgVKj8YVV8E7PSzp1BZ9SJnJAsXdryw/h5ctboUVi4AFiCd+lQaYMw5z3LGTBKjLQOeUF35k89f58Vv/tGh+l+PE/wG0rgfIUbZK5AAAAABJRU5ErkJggg==",
          },
          {
            type: "bpmn:userTask",
            text: "流程节点",
            label: "流程节点",
            icon:
              "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAEFVwZaAAAABGdBTUEAALGPC/xhBQAAAqlJREFUOBF9VM9rE0EUfrMJNUKLihGbpLGtaCOIR8VjQMGDePCgCCIiCNqzCAp2MyYUCXhUtF5E0D+g1t48qAd7CCLqQUQKEWkStcEfVGlLdp/fm3aW2QQdyLzf33zz5m2IsAZ9XhDpyaaIZkTS4ASzK41TFao88GuJ3hsr2pAbipHxuSYyKRugagICGANkfFnNh3HeE2N0b3nN2cgnpcictw5veJIzxmDamSlxxQZicq/mflxhbaH8BLRbuRwNtZp0JAhoplVRUdzmCe/vO27wFuuA3S5qXruGdboy5/PRGFsbFGKo/haRtQHIrM83bVeTrOgNhZReWaYGnE4aUQgTJNvijJFF4jQ8BxJE5xfKatZWmZcTQ+BVgh7s8SgPlCkcec4mGTmieTP4xd7PcpIEg1TX6gdeLW8rTVMVLVvb7ctXoH0Cydl2QOPJBG21STE5OsnbweVYzAnD3A7PVILuY0yiiyDwSm2g441r6rMSgp6iK42yqroI2QoXeJVeA+YeZSa47gZdXaZWQKTrG93rukk/l2Al6Kzh5AZEl7dDQy+JjgFahQjRopSxPbrbvK7GRe9ePWBo1wcU7sYrFZtavXALwGw/7Dnc50urrHJuTPSoO2IMV3gUQGNg87IbSOIY9BpiT9HV7FCZ94nPXb3MSnwHn/FFFE1vG6DTby+r31KAkUktB3Qf6ikUPWxW1BkXSPQeMHHiW0+HAd2GelJsZz1OJegCxqzl+CLVHa/IibuHeJ1HAKzhuDR+ymNaRFM+4jU6UWKXorRmbyqkq/D76FffevwdCp+jN3UAN/C9JRVTDuOxC/oh+EdMnqIOrlYteKSfadVRGLJFJPSB/ti/6K8f0CNymg/iH2gO/f0DwE0yjAFO6l8JaR5j0VPwPwfaYHqOqrCI319WzwhwzNW/aQAAAABJRU5ErkJggg==",
          },
          // {
          //   type: 'bpmn:multiInstanceLoopCharacteristics',
          //   text: "会签节点",
          //   label: "会签节点",
          //   icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABKBJREFUWEftl3uIVHUUxz/fO7pLIWSRlmSWED20pBe4M6uiFD7Q0hDNIgUfESa66c7dEQrUIHLnjmlrSlhG5B+VmVqGkRWu6NxRxBBBwQwyqSCLyCJt1bkn7rSzjLt3Hugf/tP9a2bO63POPef8fiOu8qOrHJ//AWqqQGOrPWwODwD3Y9xuDscd2JdNauuVvsKqAImMfYDxZJlAByWWZJPad7kgFQESnn0DhcwhYIVi/Jg3vhOMFTwEjAXOKWBGNqVPLweiLEC81abIYRvw7XX1DPt8kTq6B2j0zDNIGrQHvZnhnGcmYqZgWKgr2BGIz3JJbSgHVxag0bP9BsMDGLPfVXupg3jaxkuMBsYV+qLKY3Col5i1N6lj3VUjARpa7R7H4ViYQdbV492CT5PYXC1ohPxwXS/GtC/WH6WySICEZ4uA14H1vqsFRYPGVnvcHD4Jv5tY7cDWwOF75wJ/OmJYXjwHzKxQ7jVZV4urA6RtKmKLwRs5VwuLBomMfYXxiImnc0m9HxUo7tlOwYQomeDvIEa/3BKdK8ojKzDyFRuQr+NnxEk/qcGhcmPa5pl4C9jnuxpZLsv4a3aH8hwE+kZCOEzMNmtnRYBQmEhbDtFQzDbu2WrBC0CT76qtUg8kMrYNY0qZKrRkXXlVAUZmbEhg7DG4EWMV4ibgGQVMrjbzjWl71cTSSICA+dmU3qwKECqEEHnjaKHpoF0wWmJqtRWc8GxTCFsGYGw2pS9rAohnbJyM+cDkLmcBK/yUlld8BWk7grivh454x09qbtUp6Cx/ymBWp/JpwQH7b8PdZgFP5FLaHgWR8CzcEdOiZB15+h5aqjMVAQplh80YQxHbMdK+q1xhEjI2woy94WfBQqeeD/cu0q8F2RobZBdZhjEnIvjZACb2FqcvGutyrsZEvoLS4OGiySW1pLuzhGfPAl27vdAbYhDGLUB9l75xFnHMjI+J8UUvo6MrMdjkuypU95I9kMhYG8bC0GkpZdFpg2fDHdGE8VSlHkAs8pNaW9QZtcpuvXCRAfX9OXL+N94DrvVdTboEYJRngy/CYeAv39XAHpm32nJiNGP0AcJjep8MX3l2UUe/IOBBAu7GIVzj14f2vitNaLP6M//QjhiIMddv0a7GlTYou1SnLgFIhAEclkWNWVHWCdXccYK1hzboQlQVGlvtLnNoQlzjJzU74dkWYGpR14zpuRZ91KMHisevdTAw95J+KiqUBs+f5+YDL+qXiuUvESYytgajqesnscBPan2PKRiyzOr69iG8cJzwXd1ZVOjc6weAGzBm+y16t+bgnqWAlV2ZimXZpF7ubl9owrD7w/FQwJ7SJZNIWxtiIcbbfovC7q/piadtjsTGrrJ3O1V7VCDK68g265fv4HQoi4mhUbeZKLv4KpukgB0lsi2+q8jF1GMMSx02eDbagd2Io35S99aS+gjPwitceDnt36nvW4zHckv0ezn7snfCRMaex1hnsDHnal4tAHHPdocHVkHXOCljfDal45VsywIUtmKe6bEYm2sufxHAOC94NNuiwtq+LIBqhlHyzpH9wRxO5Zr1dS0+qv4zqsXJlej8CxP31DCxswdnAAAAAElFTkSuQmCC",
          // },
          // {
          //   type: "bpmn:parallelGateway",
          //   text: "并行节点",
          //   label: "并行节点",
          //   icon:
          //     "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAzBJREFUWEetl2+kFWkcx7/f01oilojYFxGR3cS9M5eI1ItliUtv9pxy+7PKrV2lKN0z524qu505dxWltSml0qp72xdLiWVfbJZldc7MJV1SRBFxaS2xLJ1vZpp7zZnOzDwz27w5zDy/5/v5/Xl+z+8QJZ9BV6MERgNzARd8hxfKbMUyRoMndIwVHI3bqovj/jiPFd2vMEBcXEAoyOi3DEQhgB7PhSNeg98FAFZT34D4NkxHwUgYAyTC3vAcuvFwW64cAM2iEEYAcXEKY50Gv++Xa7upwyImikDkAiQ8P+Q5PJVVaJargwBOmkJkAvSIEwe8Os+YVLnV0n4Ip00gUgF6wg7s6zj8wUR8bo3taq+As3kQfQESnn/t1XmuiPjcWqulryD8mAXxDkDinO/O6nBRN/zbc/hzGmC05nwaRA9Aotp3dRq8lFNwCr57DjNryW5qp4iL/SDmDROef+k7vJIXdsuVEUCwz6CrHQQuJyFCgB5xYqtf50954mEHLAAQ6rQ0QuFaHIJ2S9skXI16+paOwxsm4mUAAhvb1WYB10M9YjstV78DWC+i6td5M03ccrUHgMlpmPEcrspyYrClLyhMAbg7D1AhVrXrnPm/AALaHwgj9xp8lLbXUEufdoUHIUAsBQ/VRdUf532TFNiu1gsIonfXc7jBxCaqt9WshN6vDFPQU4TEYwrVjsPpvA3LANiuBkRMQVgxd233O4ZPKkCt7bCdBVEUYMjVUBeYBLA8PjOkNaKnqqDmj/GvNIiBpj6pEDMEbnccDmcW3YTWsBuKL0sOLOmtWHhOoNpp8M+MNmtJeDbd4GzaGruptQKmQHzcb1rKu4xedInqdJ1/5NVEv+8DLa2rvD1uS9NGNZPreBYVVL0xBhVv/FgT2oBuKL4ka040G0iElxRqnXH+ZkJgn9BnIiZBLM4bUouMZP8IqPkOf80sOFef8221f5QnHrX/fJ9il9UrEDWvzjv9rKyWNkKh+CITcWOAnmYF/Bumo8FbcQi7qeEw7MBCU/FCAAmI/6J0/BK+d7UpCvuHRcQLAyQgXgeRCN5Fni8oKl4KIAExn4Uy4qUBorC/l7/nbwB3xc1SUZWGlAAAAABJRU5ErkJggg==",
          // },
          // {
          //   type: 'bpmn:serviceTask',
          //   text: "系统任务",
          //   label: "系统任务",
          //   icon: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAaBJREFUWEftlr9OwlAUxr8DCYmDiS/gpJsLibQzPoQuvoKymEjrgg4KmLgor+CiDyFzwbg44uQLmDhoGPjILbS5ECX3ttZGQycCp+f73fPn4wpyfiRnfSwB4gq4LT4D2PqlljwGnlSUlg7AUJw4s4IoYPKe6UOcqtDAk1B7BoBAt+fJjmmuJHFOiw8CVP8HQKXJqqpC35euaTV+rALOOctSxJPeTxOIVADOFdc5xEaRKLOIBoi1qegHiNoIGEgJL70jef0OJhWA2+QNBAcLT0p0Al8OswFo8w7E7jT5YATU1OcCcA1gM/xecB/UZS8TAJXUbTH2i8CXcKfdNlU7ZvY7cwAKmr26nCghp80LIXyTgUw3A1oLlGkJcRuap2BfmUvmLch9CNUaFojVkeBdhjiON4LosITL6LfM1nB+sKKBjHw9cyOaF8jVik1O+1VMqi1IKqq/txAgDLS9kNhSCRq6X+gXkj6Abdt8ieI1u058K44teULwGXiykgQmDcCbLhh4Ev01W3EYAUSDY5PZ9H75NwBsTm4ba1QB26Q28UuAMSpvMTC6ESdOAAAAAElFTkSuQmCC",
          // },
          {
            type: "bpmn:exclusiveGateway",
            text: "是否",
            label: "条件判断",
            icon:
              "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAVCAYAAAHeEJUAAAAABGdBTUEAALGPC/xhBQAAAvVJREFUOBGNVEFrE0EU/mY3bQoiFlOkaUJrQUQoWMGePLX24EH0IIoHKQiCV0G8iE1covgLiqA/QTzVm1JPogc9tIJYFaQtlhQxqYjSpunu+L7JvmUTU3AgmTfvffPNN++9WSA1DO182f6xwILzD5btfAoQmwL5KJEwiQyVbSVZ0IgRyV6PTpIJ81E5ZvqfHQR0HUOBHW4L5Et2kQ6Zf7iAOhTFAA8s0pEP7AXO1uAA52SbqGk6h/6J45LaLhO64ByfcUzM39V7ZiAdS2yCePPEIQYvTUHqM/n7dgQNfBKWPjpF4ISk8q3J4nB11qw6X8l+FsF3EhlkEMfrjIer3wJTLwS2aCNcj4DbGxXTw00JmAuO+Ni6bBxVUCvS5d9aa04+so4pHW5jLTywuXAL7jJ+D06sl82Sgl2JuVBQn498zkc2bGKxULHjCnSMadBKYDYYHAtsby1EQ5lNGrQd4Y3v4Zo0XdGEmDno46yCM9Tk+RiJmUYHS/aXHPNTcjxcbTFna000PFJHIVZ5lFRqRpJWk9/+QtlOUYJj9HG5pVFEU7zqIYDVsw2s+AJaD8wTd2umgSCCyUxgGsS1Y6TBwXQQTFuZaHcd8gAGioE90hlsY+wMcs30RduYtxanjMGal8H5dMW67dmT1JFtYUEe8LiQLRsPZ6IIc7A4J5tqco3T0pnv/4u0kyzrYUq7gASuEyI8VXKvB9Odytv6jS/PNaZBln0nioJG/AVQRZvApOdhjj3Jt8QC8Im09SafwdBdvIpztpxWxpeKCC+EsFdS8DCyuCn2munFpL7ctHKp+Xc5cMybeIyMAN33SPL3ZR9QV1XVwLyzHm6Iv0/yeUuUb7PPlZC4D4HZkeu6dpF4v9j9MreGtMbxMMRLIcjJic9yHi7WQ3yVKzZVWUr5UrViJvn1FfUlwe/KYVfYyWRLSGNu16hR01U9IacajXPei0wx/5BqgInvJN+MMNtNme7ReU9SBbgntovn0kKHpFg7UogZvaZiOue/q1SBo9ktHzQAAAAASUVORK5CYII=",
          },
          {
            type: "bpmn:endEvent",
            text: "结束",
            label: "结束节点",
            icon:
              "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAAH6ji2bAAAABGdBTUEAALGPC/xhBQAAA1BJREFUOBFtVE1IVUEYPXOf+tq40Y3vPcmFIdSjIorWoRG0ERWUgnb5FwVhYQSl72oUoZAboxKNFtWiwKRN0M+jpfSzqJAQclHo001tKkjl3emc8V69igP3znzfnO/M9zcDcKT67azmjYWTwl9Vn7Vumeqzj1DVb6cleQY4oAVnIOPb+mKAGxQmKI5CWNJ2aLPatxWa3aB9K7/fB+/Z0jUF6TmMlFLQqrkECWQzOZxYGjTlOl8eeKaIY5yHnFn486xBustDjWT6dG7pmjHOJd+33t0iitTPkK6tEvjxq4h2MozQ6WFSX/LkDUGfFwfhEZj1Auz/U4pyAi5Sznd7uKzznXeVHlI/Aywmk6j7fsUsEuCGADrWARXXwjxWQsUbIupDHJI7kF5dRktg0eN81IbiZXiTESic50iwS+t1oJgL83jAiBupLDCQqwziaWSoAFSeIR3P5Xv5az00wyIn35QRYTwdSYbz8pH8fxUUAtxnFvYmEmgI0wYXUXcCCSpeEVpXlsRhBnCEATxWylL9+EKCAYhe1NGstUa6356kS9NVvt3DU2fd+Wtbm/+lSbylJqsqkSm9CRhvoJVlvKPvF1RKY/FcPn5j4UfIMLn8D4UYb54BNsilTDXKnF4CfTobA0FpoW/LSp306wkXM+XaOJhZaFkcNM82ASNAWMrhrUbRfmyeI1FvRBTpN06WKxa9BK0o2E4Pd3zfBBEwPsv9sQBnmLVbLEIZ/Xe9LYwJu/Er17W6HYVBc7vmuk0xUQ+pqxdom5Fnp55SiytXLPYoMXNM4u4SNSCFWnrVIzKG3EGyMXo6n/BQOe+bX3FClY4PwydVhthOZ9NnS+ntiLh0fxtlUJHAuGaFoVmttpVMeum0p3WEXbcll94l1wM/gZ0Ccczop77VvN2I7TlsZCsuXf1WHvWEhjO8DPtyOVg2/mvK9QqboEth+7pD6NUQC1HN/TwvydGBARi9MZSzLE4b8Ru3XhX2PBxf8E1er2A6516o0w4sIA+lwURhAON82Kwe2iDAC1Watq4XHaGQ7skLcFOtI5lDxuM2gZe6WFIotPAhbaeYlU4to5cuarF1QrcZ/lwrLaCJl66JBocYZnrNlvm2+MBCTmUymPrYZVbjdlr/BxlMjmNmNI3SAAAAAElFTkSuQmCC",
          },
        ]);
      }
      // 监听添加节点事件
      this.lf.on("node:add", ({ data }) => {
        return;
        //如果ID不符合需求，可以组定义节点ID   时间戳+随机数
        data.id =
          "customNodeId_" + Date.now() + "_" + Math.floor(Math.random() * 1000);
      });
      //监听节点点击事件
      this.lf.on("node:click", (event) => {
        // console.log("[ 节点点击node:click ] >", event);
        if (that.isSilentMode) {
          this.$emit("clickNode", event);
        }
      });
      // 监听连线添加事件
      // this.lf.on("edge:add", ({ data }) => {
      //   console.log("[ 连线添加edge:add ] >", data);
      //   const sourceNode = this.lf.getNodeModelById(data.sourceNodeId);
      //   if (sourceNode.type === "bpmn:startEvent") {
      //     // 获取目标节点ID
      //     const targetNodeId = data.targetNodeId;
      //     console.log("目标节点的key:", targetNodeId);
      //   }
      // });
      // // 监听节点的鼠标悬浮事件，显示节点信息弹框
      // this.lf.on('node:mouseenter', (event) => {
      //   if(that.isSilentMode){
      //     this.showNodePopup(event)
      //   }
      // });
      // // 监听节点的鼠标移出事件，隐藏节点信息弹框
      // this.lf.on('node:mouseleave', (event) => {
      //   if(that.isSilentMode){
      //     this.hideNodePopup(event)
      //   }
      // });

      //注册自定义节点
      multiInstanceLoopCharacteristics(this.lf);
      parallelGateway(this.lf);
      exclusiveGateway(this.lf);
      userTask(this.lf);

      console.log(this.data, "===初始化data");
      this.lf.render(this.data);

      if (this.isSilentMode) {
        setTimeout(() => {
          //回显节点全部显示在画布中
          this.lf.fitView();
        }, 500);
      }
    },
    //鼠标悬浮展示节点信息
    showNodePopup({ data }) {
      console.log(data);
      //并行排他网关不显示节点弹框信息
      if (
        data.type === "bpmn:exclusiveGateway" ||
        data.type === "bpmn:parallelGateway"
      ) {
        return;
      }
      // 获取节点信息
      this.currentNodeInfo = {
        name: "这是节点信息",
        startTime: "2023-04-23 12:34:22",
        endTime: "2023-04-25 12:34:22",
      }; // 在这里替换为你需要展示的节点信息

      // 获取鼠标位置，并计算弹框位置
      const x = data.x;
      const y = data.y;
      this.popupStyle.left = `${x - 50}px`;
      this.popupStyle.top = `${y + 180}px`;

      this.showNodeInfo = true;
    },
    hideNodePopup() {
      this.showNodeInfo = false;
    },
    getlfXml2Json(queryObj, hisTaskNodes = []) {
      let xmlData = queryObj.flowData;
      let json = lfXml2Json(xmlData);
      // this.processId = json["bpmn:definitions"]["bpmn:process"]["-id"];
      // console.log("query---json", this.processId);
      // console.log("query---xmlData", xmlData)
      // console.log("json1111", json)
      if (hisTaskNodes.length > 0) {
        let userTask =
          json["bpmn:definitions"]["bpmn:process"]["bpmn:userTask"];
        let mulTask =
          json["bpmn:definitions"]["bpmn:process"][
            "bpmn:multiInstanceLoopCharacteristics"
          ];
        let parallelGateway =
          json["bpmn:definitions"]["bpmn:process"]["bpmn:parallelGateway"];
        let exclusiveGateway =
          json["bpmn:definitions"]["bpmn:process"]["bpmn:exclusiveGateway"];

        //用户任务节点
        if (userTask && Array.isArray(userTask)) {
          userTask.forEach((element) => {
            hisTaskNodes.forEach((el) => {
              if (element["-id"] == el) {
                this.$set(element, "-status", "history");
              }
            });
          });
        } else {
          hisTaskNodes.forEach((el) => {
            if (userTask["-id"] == el) {
              this.$set(userTask, "-status", "history");
            }
          });
        }
        //会签节点
        if (mulTask && Array.isArray(mulTask)) {
          mulTask.forEach((element) => {
            hisTaskNodes.forEach((el) => {
              if (element["-id"] == el) {
                this.$set(element, "-status", "history");
              }
            });
          });
        } else if (mulTask) {
          hisTaskNodes.forEach((el) => {
            if (mulTask["-id"] == el) {
              this.$set(mulTask, "-status", "history");
            }
          });
        }
        //并行节点
        if (parallelGateway && Array.isArray(parallelGateway)) {
          parallelGateway.forEach((element) => {
            hisTaskNodes.forEach((el) => {
              if (element["-id"] == el) {
                this.$set(element, "-status", "history");
              }
            });
          });
        } else if (parallelGateway) {
          hisTaskNodes.forEach((el) => {
            if (parallelGateway["-id"] == el) {
              this.$set(parallelGateway, "-status", "history");
            }
          });
        }
        // 排他节点
        if (exclusiveGateway && Array.isArray(exclusiveGateway)) {
          exclusiveGateway.forEach((element) => {
            hisTaskNodes.forEach((el) => {
              if (element["-id"] == el) {
                this.$set(element, "-status", "history");
              }
            });
          });
        } else if (exclusiveGateway) {
          hisTaskNodes.forEach((el) => {
            if (exclusiveGateway["-id"] == el) {
              this.$set(exclusiveGateway, "-status", "history");
            }
          });
        }

        json["bpmn:definitions"]["bpmn:process"]["bpmn:userTask"] = userTask;
        console.log("json2222--userTask", userTask);
        console.log("json2222", json);
      }
      this.data = json;
      console.log(this.data, "=====data");
    },
  },
  mounted() {
    console.log("mounted", this.obj);
    this.init();
    // const { flowData } = this.obj;
    // if (flowData) {
    //   console.log(flowData, "----flowData");
    //   this.getlfXml2Json(flowData);
    //   // let xmlData = queryObj.flowData
    //   // let json = lfXml2Json(xmlData)
    //   // this.data = json

    //   // this.data = queryObj.flowData
    //   // console.log("this.data", this.data)
    //   // console.log("json", json)
    //   // let xml = lfJson2Xml(json)
    //   // console.log("xml", xml)
    // }

    // this.init();
  },
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: calc(100vh - 140px);
}
.mini-container {
  width: 100%;
  height: 330px;
}
/deep/.el-divider--horizontal {
  margin: 0;
}
</style>
