//自定义节点---并行节点
import { v4 as uuidv4 } from "uuid";
export default function parallelGateway (lf) {
  lf.register('bpmn:parallelGateway', ({ PolygonNode, PolygonNodeModel, h }) => {
    class Node extends PolygonNode {
      getIconShape () {
        const {model} = this.props
        const {stroke} = model.getNodeStyle()
        return h(
          'svg',
          {
            x: 12,
            y: 10,
            width: 30,
            height: 30,
            viewBox: '0 0 1126 1024'
          },
          h(
            'path',
            {
              fill: stroke,
              d: 'M469.333333 469.333333V170.666667h85.333334v298.666666h298.666666v85.333334h-298.666666v298.666666h-85.333334v-298.666666H170.666667v-85.333334h298.666666z'
            }
          )
        )
      }
      getShape () {
        const {model} = this.props
        const {width, height, x, y, points} = model
        const {fill, fillOpacity, strokeWidth, stroke, strokeOpacity,} = model.getNodeStyle()
        const transform = `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`
        const pointsPath = points.map(point => point.join(',')).join(' ')
        return h(
          'g',
          {
            transform
          },
          [
            h(
              'polygon',
              {
                points: pointsPath,
                fill,
                stroke,
                strokeWidth,
                strokeOpacity,
                fillOpacity
              }
            ),
            this.getIconShape()
          ]
        )
      }
    }
    class Model extends PolygonNodeModel {
      constructor (data, graphModel) {
        data.text = {
          value: (data.text && data.text.value) || '并行',
          x: data.x,
          y: data.y
        }
        super(data, graphModel)
        // 右键菜单自由配置，也可以通过边的properties或者其他属性条件更换不同菜单
        // this.menu = [
        //   {
        //     className: 'lf-menu-delete',
        //     text: 'delete',
        //     callback (node) {
        //       // const comfirm = window.confirm('你确定要删除吗？')
        //       lf.deleteNode(node.id)
        //     }
        //   },
        //   {
        //     text: 'edit',
        //     className: 'lf-menu-item',
        //     callback (node) {
        //       lf.editText(node.id)
        //     }
        //   },
        //   {
        //     text: 'copy',
        //     className: 'lf-menu-item',
        //     callback (node) {
        //       lf.cloneNode(node.id)
        //     }
        //   }
        // ]
      }

      createId() {
        let fullUUID = uuidv4(); // 生成完整的 UUID
        let uuid = fullUUID.substr(0, 7); // 截取指定长度的部分
        return "Para_" + uuid;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        const properties = super.getProperties();
        if(properties.status == "history") {
          //根据属性判断是否是当前节点
          style.fill = '#ccc';
          style.stroke = '#ccc';
        } else {
          style.stroke = '#177dff';
        }
        return style;
      }

      initNodeData(data) {
        data.x = data.x
        data.y = data.y
        super.initNodeData(data)
        const lenght = 25
        this.points = [
          [lenght, 0],
          [lenght * 2, lenght],
          [lenght, lenght * 2],
          [0, lenght]
        ]
      }
      // 自定义锚点样式
      getAnchorStyle() {
        const style = super.getAnchorStyle();
        style.hover.r = 8;
        style.hover.fill = "rgb(24, 125, 255)";
        style.hover.stroke = "rgb(24, 125, 255)";
        return style;
      }
    }
    return {
      view: Node,
      model: Model
    }
  })
}
