//自定义节点---用户任务节点
import { v4 as uuidv4 } from "uuid";
export default function userTask (lf) {
  lf.register('bpmn:userTask', ({RectNode, RectNodeModel, h}) => {
    class Node extends RectNode {
      getIconShape () {
        const {model} = this.props
        const {stroke} = model.getNodeStyle()
        return h(
          'svg',
          {
            x: 12,
            y: 10,
            width: 30,
            height: 30,
            viewBox: '0 0 1126 1024'
          },
          h(
            'path',
            {
              fill: stroke,
              d: 'M166.118564 8.906207c56.037851 0 105.591984 37.512942 124.295018 88.171444l356.782633-0.035625a10.509324 10.509324 0 0 0 10.509324-10.544948v-40.612302a10.580573 10.580573 0 0 1 14.819927-9.654328L886.345672 131.455608a10.580573 10.580573 0 0 1 0 19.237406l-213.92708 95.474534a10.580573 10.580573 0 0 1-14.819928-9.618703V195.437796a10.544949 10.544949 0 0 0-10.509324-10.509324l-356.568884 0.07125c-18.631784 50.801002-68.257167 88.456443-124.401892 88.456443-71.463401 0-132.381854-60.847203-132.381854-132.274979S94.655163 8.906207 166.118564 8.906207z m0 88.242694c-22.764264 0-44.174784 21.374896-44.174784 44.032285 0 22.693014 21.41052 44.06791 44.174784 44.067909 22.657389 0 43.99666-21.374896 43.99666-44.067909 0-22.693014-21.374896-44.032285-43.99666-44.032285zM166.118564 330.84776c56.037851 0 105.591984 37.512942 124.295018 88.171444l356.782633-0.035625a10.509324 10.509324 0 0 0 10.509324-10.544948v-40.612302a10.580573 10.580573 0 0 1 14.819927-9.654328l213.820206 95.22516a10.580573 10.580573 0 0 1 0 19.237406l-213.92708 95.474534a10.580573 10.580573 0 0 1-14.819928-9.618703v-41.111049a10.544949 10.544949 0 0 0-10.509324-10.509324l-356.568884 0.07125c-18.631784 50.801002-68.257167 88.456443-124.401892 88.456443-71.463401 0-132.381854-60.847203-132.381854-132.274979s60.918453-132.274979 132.381854-132.274979z m0 88.242694c-22.764264 0-44.174784 21.374896-44.174784 44.032285s21.41052 44.032285 44.174784 44.032285c22.657389 0 43.99666-21.374896 43.99666-44.032285 0-22.693014-21.374896-44.032285-43.99666-44.032285zM166.118564 652.789313c56.037851 0 105.591984 37.512942 124.295018 88.171444l356.782633-0.035625a10.509324 10.509324 0 0 0 10.509324-10.544948v-40.612302a10.580573 10.580573 0 0 1 14.819927-9.654328l213.820206 95.22516a10.580573 10.580573 0 0 1 0 19.237406l-213.92708 95.474534a10.580573 10.580573 0 0 1-14.819928-9.618703V839.320902a10.544949 10.544949 0 0 0-10.509324-10.544949l-356.568884 0.106875c-18.631784 50.801002-68.257167 88.456443-124.401892 88.456443-71.463401 0-132.381854-60.847203-132.381854-132.274979s60.918453-132.274979 132.381854-132.274979z m0 88.207069c-22.764264 0-44.174784 21.374896-44.174784 44.06791 0 22.657389 21.41052 44.032285 44.174784 44.032285 22.657389 0 43.99666-21.374896 43.99666-44.032285 0-22.693014-21.374896-44.06791-43.99666-44.06791z'
            }
          )
        )
      }
      // getShape () {
      //   const {model} = this.props
      //   const {width, height, x, y, points} = model
      //   const {fill, fillOpacity, strokeWidth, stroke, strokeOpacity,} = model.getNodeStyle()
      //   const transform = `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`
      //   // const pointsPath = points.map(point => point.join(',')).join(' ')
      //   console.log("points", points)
      //   return h(
      //     'g',
      //     {
      //       transform
      //     },
      //     [
      //       h(
      //         'rect',
      //         {
      //           // points: pointsPath,
      //           fill,
      //           stroke,
      //           strokeWidth,
      //           strokeOpacity,
      //           fillOpacity
      //         }
      //       ),
      //       this.getIconShape()
      //     ]
      //   )
      // }
    }
    class Model extends RectNodeModel {
      constructor (data, graphModel) {
        data.text = {
          value: (data.text && data.text.value) || '用户',
          x: data.x,
          y: data.y
        }
        // data.properties = {
        //  //自定义属性
        //   type: "用户",
        //   status: "current"
        // }
        super(data, graphModel)
      }

      createId() {
        let fullUUID = uuidv4(); // 生成完整的 UUID
        let uuid = fullUUID.substr(0, 7); // 截取指定长度的部分
        return "Utask_" + uuid;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        const properties = super.getProperties();
        if(properties.status == "history") {
          //根据属性判断是否是当前节点
          style.fill = '#ccc';
          style.stroke = '#ccc';
        } else {
          style.stroke = '#177dff';
        }
        return style;
      }
      initNodeData(data) {
        data.x = data.x;
        data.y = data.y
        super.initNodeData(data);
        this.width = 120;
        this.height = 80;
        // this.radius = 10;
      }
    }

    return {
      view: Node,
      model: Model
    }
  })
}