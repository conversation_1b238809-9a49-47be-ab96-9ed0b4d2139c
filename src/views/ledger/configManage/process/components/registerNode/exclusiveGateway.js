//自定义节点---排他节点
import { v4 as uuidv4 } from "uuid";
export default function exclusiveGateway (lf) {
  lf.register('bpmn:exclusiveGateway', ({ PolygonNode, PolygonNodeModel, h }) => {
    class Node extends PolygonNode {
      getIconShape () {
        const {model} = this.props
        const {stroke} = model.getNodeStyle()
        return h(
          'svg',
          {
            x: 12,
            y: 10,
            width: 30,
            height: 30,
            viewBox: '0 0 1126 1024'
          },
          h(
            'path',
            {
              fill: stroke,
              d: 'M561.17013333 509.06026667L858.02666667 213.73973333c14.03733333-13.968 14.1088-36.60053333 0.1408-50.63786666-13.99893333-14.06826667-36.592-14.10773333-50.62933334-0.1408L510.6048 458.31466667 216.256 163.06986667c-13.9328-13.96693333-36.59733333-14.03733333-50.63466667-0.07146667-14.00426667 13.96586667-14.03733333 36.63146667-0.0704 50.6688l294.27733334 295.1744-296.71466667 295.14026667c-14.0384 13.968-14.1088 36.59733333-0.14293333 50.63786666a35.7216 35.7216 0 0 0 25.3856 10.56c9.13066667 0 18.26666667-3.4688 25.25013333-10.4192l296.78613333-295.2128L807.4304 857.48266667c6.9824 7.02186667 16.15253333 10.53013333 25.35253333 10.53013333a35.72906667 35.72906667 0 0 0 25.28213334-10.45973333c13.99893333-13.96586667 14.03733333-36.592 0.07146666-50.62933334L561.17013333 509.06026667z m0 0'
            }
          )
        )
      }
      getShape () {
        const {model} = this.props
        const {width, height, x, y, points} = model
        const {fill, fillOpacity, strokeWidth, stroke, strokeOpacity,} = model.getNodeStyle()
        const transform = `matrix(1 0 0 1 ${x - width / 2} ${y - height / 2})`
        const pointsPath = points.map(point => point.join(',')).join(' ')
        return h(
          'g',
          {
            transform
          },
          [
            h(
              'polygon',
              {
                points: pointsPath,
                fill,
                stroke,
                strokeWidth,
                strokeOpacity,
                fillOpacity
              }
            ),
            this.getIconShape()
          ]
        )
      }
    }
    class Model extends PolygonNodeModel {
      constructor (data, graphModel) {
        data.text = {
          value: (data.text && data.text.value) || '排他',
          x: data.x,
          y: data.y
        }
        super(data, graphModel)
        // 右键菜单自由配置，也可以通过边的properties或者其他属性条件更换不同菜单
        // this.menu = [
        //   {
        //     className: 'lf-menu-delete',
        //     text: 'delete',
        //     callback (node) {
        //       // const comfirm = window.confirm('你确定要删除吗？')
        //       lf.deleteNode(node.id)
        //     }
        //   },
        //   {
        //     text: 'edit',
        //     className: 'lf-menu-item',
        //     callback (node) {
        //       lf.editText(node.id)
        //     }
        //   },
        //   {
        //     text: 'copy',
        //     className: 'lf-menu-item',
        //     callback (node) {
        //       lf.cloneNode(node.id)
        //     }
        //   }
        // ]
      }

      createId() {
        let fullUUID = uuidv4(); // 生成完整的 UUID
        let uuid = fullUUID.substr(0, 7); // 截取指定长度的部分
        return "Excl_" + uuid;
      }

      getNodeStyle() {
        const style = super.getNodeStyle();
        const properties = super.getProperties();
        if(properties.status == "history") {
          //根据属性判断是否是当前节点
          style.fill = '#ccc';
          style.stroke = '#ccc';
        } else {
          style.stroke = '#177dff';
        }
        return style;
      }

      initNodeData(data) {
        data.x = data.x
        data.y = data.y
        super.initNodeData(data)
        const lenght = 25
        this.points = [
          [lenght, 0],
          [lenght * 2, lenght],
          [lenght, lenght * 2],
          [0, lenght]
        ]
      }
      // 自定义锚点样式
      getAnchorStyle() {
        const style = super.getAnchorStyle();
        style.hover.r = 8;
        style.hover.fill = "rgb(24, 125, 255)";
        style.hover.stroke = "rgb(24, 125, 255)";
        return style;
      }
    }
    return {
      view: Node,
      model: Model
    }
  })
}
