<!-- 计划工单 -->
<template>
  <div class="app-container">
    <div class="queryParamsWrap">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-row :gutter="20">
          <el-col :span="19">
            <el-row>
              <el-col :span="6">
                <el-form-item label="计划名称" prop="planName">
                  <el-input
                    placeholder="请输入计划名称"
                    v-model="queryParams.planName"
                    size="mini"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="流程类型" prop="flowTypeCode">
                  <el-select
                    clearable
                    v-model="queryParams.flowTypeCode"
                    size="mini"
                  >
                    <el-option
                      v-for="dict in flowCateOptions"
                      :key="dict.typeLabel"
                      :label="dict.typeLabel"
                      :value="dict.typeCode"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="planStatus">
                  <el-select
                    clearable
                    v-model="queryParams.planStatus"
                    size="mini"
                  >
                    <el-option
                      v-for="dict in formStatusOptions"
                      :key="dict.dictValue"
                      :label="dict.dictLabel"
                      :value="dict.dictValue"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="5" style="padding-left: 15px; padding-bottom: 15px">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click.stop="handleQuery"
              >查询
            </el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click.stop="resetQuery"
              >重置
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            style="margin: 10px 0"
            @click="handleAdd"
            >新增
          </el-button>
        </el-row>
      </el-form>
    </div>

    <el-table v-loading="loading" ref="multipleTable" :data="tableData">
      <el-table-column
        label="计划名称"
        align="center"
        prop="planName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="流程类型"
        align="center"
        prop="flowTypeName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="间隔天数"
        align="center"
        prop="intervalDay"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="开始日期"
        align="center"
        prop="firstRunTime"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="时间范围"
        align="center"
        prop="expireDate"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建人"
        align="center"
        prop="operatorName"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <span>
            {{ scope.row.planStatus == 1 ? "启用" : "未启用" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-link
            type="primary"
            v-if="scope.row.planStatus == 0"
            @click.stop="changeStatus(scope.row)"
            style="margin-right: 10px"
            >启用
          </el-link>
          <el-link
            type="primary"
            v-if="scope.row.planStatus == 1"
            @click.stop="changeStatus(scope.row)"
            style="margin-right: 10px"
            >禁用
          </el-link>
          <el-link
            type="primary"
            @click.stop="editItem(scope.row)"
            style="margin-right: 10px"
            >编辑</el-link
          >
          <el-link type="primary" @click.stop="deleteItem(scope.row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="pageTotal > 0"
      :total="pageTotal"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="900px"
      append-to-body
      @close="cancel"
    >
      <el-row v-loading="dialogLoading">
        <el-col :span="12">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="计划名称" prop="planName">
              <el-input
                style="width:300px;"
                v-model="form.planName"
                placeholder="请输入计划名称"
              />
            </el-form-item>
            <el-form-item label="间隔天数" prop="intervalDay">
              <el-input-number
                :min="0"
                :precision="0"
                style="width:300px;"
                v-model="form.intervalDay"
                placeholder="请输入间隔天数"
              />
            </el-form-item>
            <el-form-item label="开始日期" prop="firstRunTime">
              <el-date-picker
                style="width:300px;"
                v-model="form.firstRunTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="时间范围" prop="daterange">
              <el-date-picker
                style="width:300px;"
                v-model="form.daterange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="紧急程度" prop="emergencyLevel">
              <el-select
                style="width:300px;"
                v-model="form.emergencyLevel"
                placeholder="请选择紧急程度"
              >
                <el-option
                  v-for="item in urgentLevelOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="工单类型" prop="flowTypeCode">
              <el-select
                style="width:300px;"
                v-model="form.flowTypeCode"
                placeholder="请选择工单类型"
                @change="flowTypeChange"
              >
                <el-option
                  v-for="item in flowTypeOptions"
                  :key="item.typeId"
                  :label="item.typeLabel"
                  :value="item.typeCode"
                />
              </el-select>
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="计划耗时" prop="flowExceptTime">
                  <el-input-number
                    :controls="false"
                    style="width:150px"
                    v-model="form.flowExceptTime"
                    :min="0"
                  >
                  </el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="" label-width="10px" prop="timeType">
                  <el-select style="width:150px" v-model="form.timeType">
                    <el-option
                      v-for="item in timeTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="工单说明" prop="flowRemark">
              <el-input
                style="width:300px;"
                type="textarea"
                v-model="form.flowRemark"
                placeholder="请输入工单说明"
                :rows="5"
              />
            </el-form-item>

            <el-col :span="16">
              <el-form-item label="状态:" prop="planStatus">
                <el-radio v-model="form.planStatus" :label="1">启用</el-radio>
                <el-radio v-model="form.planStatus" :label="0">禁用</el-radio>
              </el-form-item>
            </el-col>
          </el-form>
        </el-col>
        <el-col :span="12">
          <WorkFlow
            title="工单流程"
            :customHeight="500"
            :workFlowList="workFlowList"
          />
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import WorkFlow from "@/views/orderScheduling/installStation/components/workFlow.vue";
import {
  getNodeInfos,
  getWorkOrderTypeDetail,
  planFlowTypeList,
  orderPlanAdd,
  orderPlanList,
  orderPlanEdit,
  orderPlanEditStatus,
  orderPlanQueryById,
  orderPlanDeleteById,
} from "@/api/orderScheduling/workStation";
import { list as flowCateOptions } from "@/api/orderConfig/processTypeConfig.js";
export default {
  components: { WorkFlow },
  data() {
    return {
      title: "",
      queryParams: {
        planName: "",
        flowTypeCode: undefined,
        planStatus: undefined,
      },
      tableData: [],
      pageTotal: 0,
      pageNum: 1,
      pageSize: 10,
      visible: false,
      loading: false,
      dialogLoading: false,
      form: {
        planName: "",
        intervalDay: "",
        firstRunTime: "",
        daterange: undefined,
        emergencyLevel: undefined,
        flowTypeCode: undefined,
        flowRemark: "",
        flowExceptTime: "",
        timeType: 1,
        planStatus: 1,
      },
      // 表单校验
      rules: {
        planName: [
          { required: true, message: "计划名称不能为空", trigger: "blur" },
        ],
        intervalDay: [
          { required: true, message: "间隔天数不能为空", trigger: "change" },
        ],
        firstRunTime: [
          { required: true, message: "开始日期不能为空", trigger: "change" },
        ],
        expireDate: [
          { required: true, message: "时间范围不能为空", trigger: "change" },
        ],
        emergencyLevel: [
          { required: true, message: "紧急程度不能为空", trigger: "change" },
        ],
        flowTypeCode: [
          { required: true, message: "工单类型不能为空", trigger: "change" },
        ],
        daterange: [
          { required: true, message: "请选择日期", trigger: "change" },
        ],
      },
      flowTypeOptions: [], //工单类型
      workFlowList: [],
      flowCateOptions: [], //流程类型
      formStatusOptions: [
        {
          dictValue: "1",
          dictLabel: "已启用",
        },
        {
          dictValue: "0",
          dictLabel: "未启用",
        },
      ],
      urgentLevelOptions: [
        { id: "0", label: "不紧急" },
        { id: "1", label: "正常" },
        { id: "2", label: "紧急" },
        { id: "3", label: "非常紧急" },
      ],
      flowId: "",
      flowKey: "",
      timeTypeOptions: [
        { label: "分钟", value: 1 },
        { label: "小时", value: 2 },
        { label: "天", value: 3 },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
    changeStatus(row) {
      let params = {
        planId: row.planId,
        planStatus: row.planStatus == 1 ? 0 : 1,
      };
      orderPlanEditStatus(params).then((res) => {
        this.$message.success("状态修改成功");
        this.getList();
      });
    },
    deleteItem(row) {
      this.$confirm("确定删除该条数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          planId: row.planId,
        };
        orderPlanDeleteById(params).then((res) => {
          this.$message.success("删除成功!");
          this.getList();
        });
      });
    },
    editItem(row) {
      this.title = "编辑计划";
      this.visible = true;
      this.dialogLoading = true;
      let params = {
        planId: row.planId,
      };
      orderPlanQueryById(params).then((res) => {
        this.dialogLoading = false;
        this.form = res.data;
        this.form.daterange = [res.data.expireBegin, res.data.expireEnd];
        this.flowTypeChange(res.data.flowTypeCode);
      });
    },
    handleQuery() {
      this.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleAdd() {
      this.visible = true;
      this.title = "新增计划";
    },
    getList() {
      let params = {
        ...this.queryParams,
        pageSize: this.pageSize,
        pageNum: this.pageNum,
      };
      this.loading = true;
      orderPlanList(params)
        .then((res) => {
          this.tableData = res.data;
          this.pageTotal = res.total;
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.visible = false;
      this.resetForm("form");
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.dialogLoading = true;
          let params = {
            ...this.form,
            firstRunTime:
              this.moment(this.form.firstRunTime).format("YYYY-MM-DD") ?? "",
            expireBegin:
              this.moment(this.form.daterange[0]).format("YYYY-MM-DD") ?? "",
            expireEnd:
              this.moment(this.form.daterange[1]).format("YYYY-MM-DD") ?? "",
            flowId: this.flowId,
            flowKey: this.flowKey,
            flowTypeName: this.formatterFlowCateOptions(this.form.flowTypeCode),
          };
          let api = this.title === "编辑计划" ? orderPlanEdit : orderPlanAdd;
          api(params)
            .then((res) => {
              this.$message.success("新增成功");
              this.dialogLoading = false;
              this.visible = false;
              this.getList();
            })
            .catch((err) => {
              this.dialogLoading = false;
              this.visible = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    formatterFlowCateOptions(flowTypeCode) {
      let name = "";
      this.flowCateOptions.map((item) => {
        if (item.typeCode == flowTypeCode) {
          name = item.typeLabel;
        }
      });
      return name;
    },
    //获取流程类型
    getflowCateOptions() {
      let params = {
        typeStatus: "1",
      };
      flowCateOptions(params).then((res) => {
        this.flowCateOptions = res.data;
      });
    },
    //工单类型选中事件
    flowTypeChange(value) {
      this.dialogLoading = true;
      try {
        getWorkOrderTypeDetail({ typeCode: value }).then((response) => {
          if (response.data && response.data.flowKey) {
            const param = {
              flowId: response.data.flowId,
              flowKey: response.data.flowKey,
              deployId: response.data.deployId,
            };
            this.flowKey = param.flowKey;
            this.flowId = param.flowId;
            getNodeInfos(param).then((response) => {
              if (!!response.data) {
                if (response.data.length > 0) {
                  this.workFlowList = response.data;
                }
              }
              this.dialogLoading = false;
            });
          } else {
            this.dialogLoading = false;
          }
        });
      } catch (error) {
        this.dialogLoading = false;
      }
    },
  },
  created() {
    this.getflowCateOptions();

    let params = {
      typeStatus: "1",
    };
    planFlowTypeList(params).then((response) => {
      this.flowTypeOptions = response.data;
    });
    this.getList();
  },
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped></style>
