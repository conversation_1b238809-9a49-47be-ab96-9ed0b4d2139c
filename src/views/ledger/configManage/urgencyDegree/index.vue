//紧急程度
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['ledger:urgencyDegree:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template #statusChange="{ row }"
        ><el-switch
          v-model="row.status"
          :active-value="0"
          :inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['ledger:urgencyDegree:status'])"
        >
        </el-switch
      ></template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline :list="recordList"></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/urgencyDegree.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { queryTreeList } from "@/api/ledger/businessType.js";

export default {
  name: "urgencyConfigPage",
  components: { Timeline },
  mixins: [exportMixin],
  data() {
    return {
      recordList: [],
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "supportDept",
          title: "部门",
          width: 100,
          formatter: ({ cellValue }) => {
            return this.deptOptions?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
        },
        {
          field: "businessTypeName",
          title: "业务类型",
          width: 150,
        },
        {
          field: "urgencyName",
          title: "紧急程度",
          width: 150,
        },
        {
          field: "urgencyDefinition",
          title: "紧急程度定义",
          width: 200,
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
        {
          field: "createName",
          title: "创建人",
          width: 150,
          //   width: 250,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateName",
          title: "修改人",
          width: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "supportDept",
            element: "el-select",
            title: "部门",
            props: {
              options: this.deptOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "businessType",
            element: "el-select",
            title: "业务类型",
            props: {
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "停用", value: 1 },
                { label: "启用", value: 0 },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: checkPermission(["ledger:urgencyDegree:edit"]),
        editTitle: "编辑紧急程度",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "supportDept",
            title: "支持部门",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.deptOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择支持部门",
              },
            ],
          },
          {
            field: "businessTypeIds",
            title: "业务类型",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
              multiple: true,
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择业务类型",
              },
            ],
          },
          {
            field: "urgencyName",
            title: "紧急程度名称",
            // element: "el-autocomplete",
            // props: {
            //   fetchSuggestions: (queryString, cb) => {
            //     return this.querySearch(queryString, cb);
            //   },
            // },
            rules: [
              {
                required: true,
                message: "紧急程度名称不允许为空！",
              },
            ],
            attrs: {
              placeholder: "自定义紧急程度",
            },
          },
          {
            field: "urgencyDefinition",
            title: "紧急程度定义",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder: "500个字符以内",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
            },
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["ledger:urgencyDegree:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    Promise.all([
      this.getBusinessTypeOptions(),
      this.getDicts("support_dept").then((response) => {
        this.deptOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.queryLog({ urgencyId: row.urgencyId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    getBusinessTypeOptions() {
      queryTreeList({ pageSize: 99999, pageNum: 1 }).then((res) => {
        this.businessTypeOptions = res.data;
      });
    },
    querySearch(queryString, cb) {
      api
        .queryUrgencySearch({
          itemAttributeName: queryString || "",
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x };
          });
          cb(result);
        });
    },
    //状态切换
    handleStatusChange(row) {
      // if (row.status == "0") {
      //   // this.$message.warning("自动派单的站点不能为空，请配置站点！");
      //   row.status = row.status == 1 ? 0 : 1;
      //   return;
      // }
      // if (row.status == "1") {
      //   this.$confirm("是否确认停用？", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   })
      //     .then((res) => {
      //       this.updateStatus(row);
      //     })
      //     .catch(() => {
      //       row.status = row.status == "1" ? "0" : "1";
      //     });
      // } else {
      this.updateStatus(row);
      // }
    },
    updateStatus(row) {
      const text = row.status == "0" ? "启用" : "停用";
      api
        .changeStatus({ urgencyId: row.urgencyId, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.loadData();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const { updateTime, createTime, ...params } = formParams;
      api.update(params).then((res) => {
        if (res.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
        businessTypeIds: row.businessType?.split(",") || [],
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
