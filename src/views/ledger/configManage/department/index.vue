<!-- 部门管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['ledger:department:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/department.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "departmentPage",
  components: { Timeline },
  mixins: [exportMixin],
  data() {
    return {
      recordList: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "deptId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "deptName",
          title: "部门名称",
          width: 150,
        },
        {
          field: "avgSalary",
          title: "平均薪酬（元/月）",
          width: 150,
        },
        {
          field: "salaryCoefficient",
          title: "薪酬系数",
          width: 150,
        },
        {
          field: "realSalary",
          title: "实际薪酬（元/月）",
          width: 150,
          titlePrefix: {
            message: `实际薪酬=平均薪酬×薪酬系数`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "manageSharingCoefficient",
          title: "管理分摊系数",
          width: 150,
        },
        {
          field: "manageSharingFee",
          title: "管理分摊费用（元）",
          width: 150,
          titlePrefix: {
            message: `管理分摊费用=实际薪酬×管理分摊系数`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "totalSalary",
          title: "合计薪酬（元）",
          width: 150,
          titlePrefix: {
            message: `合计薪酬=实际薪酬+管理分摊费用`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "hourSalary",
          title: "时薪（元/h）",
          width: 150,
          titlePrefix: {
            message: `时薪=（合计薪酬÷21.75）÷8`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "hourSalaryRatio",
          title: "时薪调节比例",
          width: 150,
        },
        {
          field: "hourSalaryQuotation",
          title: "时薪报价（元/h）",
          width: 150,
          titlePrefix: {
            message: `时薪报价=（时薪×时薪调节比例）＋时薪`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "minSalaryQuotation",
          title: "分钟薪酬报价（元/min）",
          width: 200,
          titlePrefix: {
            message: `分钟薪酬报价=时薪报价÷60`,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "createByName",
          title: "创建人",
          width: 150,
          //   width: 250,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateByName",
          title: "修改人",
          width: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增",
        editBtn: checkPermission(["ledger:department:edit"]),
        editTitle: "编辑",
        delBtn: checkPermission(["ledger:department:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "dictValue",
            title: "部门名称",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.deptOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择部门",
              },
            ],
          },
          {
            field: "avgSalary",
            title: "平均薪酬",
            slots: {
              append: "元",
            },
            rules: [
              {
                required: true,
                message: "平均薪酬不允许为空！",
              },
              {
                pattern: /^\d{1,6}(\.\d{1,2})?$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "小数点后保留2位，长度999999以内",
            },
          },
          {
            field: "salaryCoefficient",
            title: "薪酬系数",
            slots: {
              append: "%",
            },
            rules: [
              {
                required: true,
                message: "薪酬系数不允许为空！",
              },
              {
                pattern: /^\d{1,6}(\.\d{1,2})?$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "小数点后保留2位，长度999999以内",
            },
          },
          {
            field: "manageSharingCoefficient",
            title: "管理分摊系数",
            slots: {
              append: "%",
            },
            rules: [
              {
                required: true,
                message: "管理分摊系数不允许为空！",
              },
              {
                pattern: /^\d{1,6}(\.\d{1,2})?$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "小数点后保留2位，长度999999以内",
            },
          },
          {
            field: "hourSalaryRatio",
            title: "时薪调节比例",
            slots: {
              append: "%",
            },
            rules: [
              {
                required: true,
                message: "时薪调节比例不允许为空！",
              },
              {
                pattern: /^-?\d{1,6}(\.\d{1,2})?$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "小数点后保留2位，长度999999以内",
            },
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["ledger:department:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    Promise.all([
      this.getDicts("support_dept").then((response) => {
        this.deptOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.queryLog({ deptId: row.deptId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    querySearch(queryString, cb) {
      api
        .queryUrgencySearch({
          itemAttributeName: queryString || "",
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x };
          });
          cb(result);
        });
    },
    //状态切换
    handleStatusChange(row) {
      // if (row.status == "0") {
      //   // this.$message.warning("自动派单的站点不能为空，请配置站点！");
      //   row.status = row.status == 1 ? 0 : 1;
      //   return;
      // }
      // if (row.status == "1") {
      //   this.$confirm("是否确认停用？", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   })
      //     .then((res) => {
      //       this.updateStatus(row);
      //     })
      //     .catch(() => {
      //       row.status = row.status == "1" ? "0" : "1";
      //     });
      // } else {
      this.updateStatus(row);
      // }
    },
    updateStatus(row) {
      const text = row.status == "0" ? "启用" : "停用";
      api
        .changeStatus({ urgencyId: row.urgencyId, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.loadData();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      return new Promise((resolve) => {
        let params = { ...formParams };
        params["deptName"] = this.deptOptions?.find(
          (x) => x.dictValue === params.dictValue
        )?.dictLabel;
        api
          .update(params)
          .then((res) => {
            if (res.code === "10000") {
              this.$message.success("提交成功");
              this.loadData();
              resolve(true);
            } else {
              resolve(false);
            }
          })
          .catch(() => {
            resolve(false);
          });
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          deptId: row.deptId,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
