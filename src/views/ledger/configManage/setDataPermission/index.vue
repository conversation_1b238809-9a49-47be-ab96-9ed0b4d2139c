<!-- 账号权限 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      @loadData="loadData"
      :modalConfig="modalConfig"
    >
      <template #toolbar_buttons> </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template slot="status" slot-scope="{ row }">
        <el-switch
          v-model="row.ledgerEnableFlag"
          @change="handleStatusChange(row)"
          active-value="0"
          inactive-value="1"
          v-loading="statusLoading"
          :disabled="!checkPermission(['ledger:permission:status'])"
        />
      </template>
    </BuseCrud>

    <EditDialog ref="editRef" @submit="loadData" />
    <LogDialog ref="logRef" :userOption="userOption" />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import { getUserList, setPermsStatus } from "@/api/ledger/setPermission.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import EditDialog from "./components/EditDialog.vue";
import LogDialog from "./components/LogDialog.vue";
import { listAllUser } from "@/api/common.js";
export default {
  name: "ledgerPermission",
  mixins: [exportMixin],
  components: { EditDialog, LogDialog },
  data() {
    return {
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "userId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      statusLoading: false,
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      userOption: [],
    };
  },

  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    this.loadData();
    this.getListUser();
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },

    checkPermission,
    //状态切换
    handleStatusChange(row) {
      if (row.ledgerEnableFlag == "1") {
        this.$confirm("是否确认停用？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.updateStatus(row);
          })
          .catch(() => {
            row.ledgerEnableFlag = row.ledgerEnableFlag == "1" ? "0" : "1";
          });
      } else {
        this.updateStatus(row);
      }
    },
    async updateStatus(row) {
      const res = await setPermsStatus({
        userId: row.userId,
        ledgerEnableFlag: row.ledgerEnableFlag,
      });
      if (res.code === "10000") {
        this.msgSuccess("操作成功");
      }
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.params,
      };
      this.loading = true;
      const res = await getUserList(params);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.tablePage.total = res.total;
        // this.loadStatus();
      }
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
  },
  computed: {
    tableColumn() {
      return [
        { field: "nickName", title: "用户姓名" },
        { field: "userName", title: "登录账号" },
        { field: "phonenumber", title: "手机号" },
        {
          field: "status",
          title: "状态",
          slots: {
            default: "status",
          },
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "nickName",
            element: "el-input",
            title: "用户姓名",
          },
          {
            field: "ledgerEnableFlag",
            element: "el-select",
            title: "状态",
            props: {
              options: [
                { label: "停用", value: "1" },
                { label: "启用", value: "0" },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        customOperationTypes: [
          {
            title: "编辑",
            typeName: "edit",
            event: (row) => {
              return new Promise((resolve) => {
                setTimeout(() => {
                  this.$refs.editRef.openDialog(row);
                  resolve();
                }, 0);
              });
            },
            condition: (row) => {
              return checkPermission(["ledger:permission:edit"]);
            },
          },
          {
            title: "日志",
            typeName: "log",
            event: (row) => {
              this.$refs.logRef.open(row.userId);
            },
            condition: (row) => {
              return checkPermission(["ledger:permission:log"]);
            },
          },
        ],
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
</style>
