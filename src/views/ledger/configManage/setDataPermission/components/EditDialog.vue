<template>
  <div class="app-container">
    <el-dialog
      title="编辑账号数据权限"
      width="70%"
      :visible.sync="visible"
      @close="closeDialog"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog-header">
        <div>{{ nickName + "-" + userName }}</div>
        <div>
          <el-switch
            v-model="ledgerMaxEnableFlag"
            active-text="最高权限"
            @change="handleStatusChange"
            active-value="0"
            inactive-value="1"
          >
          </el-switch>
          <el-tooltip
            effect="dark"
            content="最高权限：指的是不受数据权限控制，用户在工单台账模块可查看所有的工单类型、业务类型、部门数据"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="工单类型" name="order">
          <el-tabs v-model="deptActive" @tab-click="handleClick">
            <el-tab-pane
              :label="item.dictLabel"
              :name="item.dictValue"
              v-for="item in deptList"
              :key="item.dictValue"
            ></el-tab-pane>
          </el-tabs>
          <TransferTree
            :cascadeData="cascadeData"
            v-model="checkedData"
            ref="transferTree"
            :titles="['未配置', '已配置']"
          ></TransferTree>
        </el-tab-pane>
        <el-tab-pane label="业务类型" name="business">
          <TransferTree
            :cascadeData="businessList"
            v-model="businessChecked"
            ref="businessTree"
            :titles="['未配置', '已配置']"
          ></TransferTree>
        </el-tab-pane>
        <el-tab-pane label="部门" name="dept">
          <div
            style="display: flex;justify-content: flex-end;align-items: center;margin-bottom: 10px;"
          >
            <el-switch
              v-model="ledgerDeptOrderFlag"
              active-text="已配置的部门所有工单类型默认都可见"
              active-value="0"
              inactive-value="1"
            >
            </el-switch>
            <el-tooltip
              effect="dark"
              content="已配置的部门所有工单类型默认都可见：指的是配置了部门后，该部门新增的工单类型以后不需要给账号单独再配置，该部门所有的工单类型均在数据权限范围内"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <el-transfer
            v-model="deptChecked"
            :data="deptList"
            filterable
            :props="{ key: 'dictValue', label: 'dictLabel' }"
            :titles="['未配置', '已配置']"
          ></el-transfer>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryOrderTree,
  queryConfigOrder,
  submitOrder,
  submitDept,
  submitBusiness,
  queryBusinessTree,
  queryCheckedBusiness,
  queryDeptTree,
  queryCheckedDept,
  changeRoot,
} from "@/api/ledger/setPermission.js";

import { deptAllTreeSelect } from "@/api/system/dept";
import TransferTree from "@/components/TransferTree/index2.vue";
import { regionTreeList } from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
import { childrenList } from "@/api/operationWorkOrder";

export default {
  components: { TransferTree },
  data() {
    return {
      deptActive: "",
      visible: false,
      activeName: "order",
      checkedData: [],
      cascadeData: [],
      userId: "",
      deptChecked: [],
      deptList: [],
      businessChecked: [],
      businessList: [],
      nickName: "",
      userName: "",
      ledgerMaxEnableFlag: "1",
      ledgerDeptOrderFlag: "1",
    };
  },
  watch: {
    // deptActive: {
    //   async handler(val) {
    //     if (val) {
    //       await this.getTreeList();
    //       this.getOrderData();
    //     }
    //   },
    // },
  },
  computed: {},
  async created() {
    this.getDicts("support_dept").then((res) => {
      this.deptList = res.data?.map((x) => {
        return { ...x, supportDept: x.dictValue, supportDeptName: x.dictLabel };
      });
      this.deptActive = this.deptList[0]?.dictValue;
      this.getTreeList();
    });
  },
  methods: {
    async handleStatusChange(val) {
      const res = await changeRoot({
        userId: this.userId,
        ledgerMaxEnableFlag: val,
      }).catch(() => {
        this.ledgerMaxEnableFlag = this.ledgerMaxEnableFlag == "0" ? "1" : "0";
      });
      if (res.code === "10000") {
        this.msgSuccess("操作成功");
      } else {
        this.msgError("操作失败");
        this.ledgerMaxEnableFlag = this.ledgerMaxEnableFlag == "0" ? "1" : "0";
      }
    },
    async handleClick() {
      if (this.deptActive) {
        await this.getTreeList();
        this.getOrderData();
      }
    },
    //获取部门树及已选项
    async getDeptData() {
      queryCheckedDept({ userId: this.userId }).then((res) => {
        if (res?.code === "10000") {
          this.deptChecked = res.data?.map((x) => x.supportDept);
        }
      });
      // const res = await queryDeptTree({ userId: this.userId });
      // if (res) {

      // }
    },
    //获取业务类型及已选项
    async getBusinessData() {
      const res = await queryBusinessTree({ userId: this.userId });
      if (res) {
        this.businessList = res.data;
        this.traverseBusiness(this.businessList);
        queryCheckedBusiness({ userId: this.userId }).then((res) => {
          if (res?.code === "10000") {
            this.businessChecked = res.data;
            this.traverseBusiness(this.businessChecked);
            this.$nextTick(() => {
              this.$refs.businessTree.getDefaultLeftData();
            });
          }
        });
      }
    },
    //获取所有工单类型-树形结构
    async getTreeList() {
      const res = await queryOrderTree({
        supportDepts: [this.deptActive],
      });
      if (res) {
        this.cascadeData = res.data;
        this.traverseBusiness(this.cascadeData);
        // this.cascadeData = res.data?.map((x) => {
        //   return {
        //     ...x,
        //     id: x.id,
        //     label: x.typeName,
        //     children: x.childrenList?.map((i) => {
        //       return {
        //         ...i,
        //         id: i.id,
        //         label: i.typeName,
        //         pid: i.parentId,
        //         plabel: i.parentName,
        //       };
        //     }),
        //   };
        // });
        console.log(this.cascadeData, "this.cascadeData");
      }
    },
    //业务类型/工单类型转换通用
    traverseBusiness(arr, pid) {
      arr?.forEach((obj) => {
        // 添加id和label
        obj.label = obj.typeName;
        obj.pid = obj.parentId;
        if (pid) {
          obj.pid = pid;
        }
        // 继续遍历children
        obj["children"] = obj.childrenList || obj.childList || [];
        if (obj.children && obj.children.length > 0) {
          this.traverseBusiness(obj.children, obj.deptId);
        } else {
          delete obj.children;
        }
      });
    },
    traverseArr(arr) {
      arr.forEach((obj) => {
        obj.label = obj.typeName;
        obj.pid = obj.parentId;

        obj["children"] = obj.childrenList || obj.childList || [];

        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children);
        }
      });
    },
    async getOrderData() {
      const res = await queryConfigOrder({
        userId: this.userId,
        supportDept: this.deptActive,
      });
      if (res) {
        this.checkedData = res.data;
        //     ?.map((x) => {
        //   return {
        //     ...x,
        //     id: x.id,
        //     label: x.typeName,
        //     children: x.childrenList?.map((i) => {
        //       return {
        //         ...i,
        //         id: i.id,
        //         label: i.typeName,
        //         pid: i.parentId,
        //         plabel: i.parentName,
        //       };
        //     }),
        //   };
        // });
        this.traverseBusiness(this.checkedData);
        console.log(this.checkedData, "checkedData");
      }
      this.$nextTick(() => {
        this.$refs.transferTree.getDefaultLeftData();
      });
    },
    async openDialog(row) {
      this.visible = true;
      this.userId = row.userId;
      this.nickName = row.nickName || "";
      this.userName = row.userName || "";
      this.ledgerMaxEnableFlag = row.ledgerMaxEnableFlag;
      this.ledgerDeptOrderFlag = row.ledgerDeptOrderFlag;
      this.activeName = "order";
      this.getOrderData();
      this.getBusinessData();
      this.getDeptData();
    },
    closeDialog() {
      this.visible = false;
      this.$emit("submit");
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    //提交
    async handleSubmit() {
      let params = {};
      let res;
      if (this.activeName === "order") {
        // let arr = this.flattenArray(this.checkedData);
        // let demandIds = [];
        // arr.map((x) => {
        //   if (x.typeLevel === 2) {
        //     demandIds.push(x.id);
        //   }
        // });
        // params = { demandIds: demandIds };
        res = await submitOrder({
          orderTypeList: this.checkedData?.map((x) => {
            return {
              ...x,
              childrenList: x.children?.map((y) => {
                return { ...y, childrenList: y.children };
              }),
            };
          }),
          userId: this.userId,
          supportDept: this.deptActive,
        });
      } else if (this.activeName === "business") {
        params = { businessTypeList: this.businessChecked };
        res = await submitBusiness({ ...params, userId: this.userId });
      } else {
        params = {
          deptList: this.deptList?.filter((x) =>
            this.deptChecked.includes(x.dictValue)
          ),
          ledgerDeptOrderFlag: this.ledgerDeptOrderFlag,
        };
        res = await submitDept({ ...params, userId: this.userId });
      }
      if (res?.code == 10000) {
        this.$message.success("修改成功");
        if (this.activeName === "dept") {
          this.closeDialog();
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;

  .el-transfer-panel {
    flex: 1;
  }

  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}

/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
}
</style>
