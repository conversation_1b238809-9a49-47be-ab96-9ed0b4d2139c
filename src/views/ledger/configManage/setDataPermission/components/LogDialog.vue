<template>
  <div class="app-container">
    <el-dialog
      title="操作日志"
      width="50%"
      :visible.sync="visible"
      @close="cancel"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :center="false"
    >
      <Timeline :list="logs" v-loading="loading"  operateTypeTitle="operatorTypeName"
                operatorNameTitle="operatorUserName"
                createTimeTitle="operatorTime"
                operateDetailTitle="remark"></Timeline>
      <!-- <div class="center-content">
        <el-steps direction="vertical" :active="1" v-loading="loading">
          <el-step v-for="(i, k) in logs" :key="k" status="wait">
            <template slot="description">
              <div class="desc">
                <div class="desc-top">
                  <span>编辑</span>
                  <span>操作人{{ operator(i.operator) }}</span>
                  <span>{{ i.createTime }}</span>
                </div>
                <div>{{ logFormat(i) }}</div>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import { queryLog } from "@/api/ledger/setPermission";
import Timeline from "@/components/Timeline/index.vue";

export default {
  components: { Timeline },
  props: {
    userOption: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      logs: [],
      userId: "",
      total: 0,
    };
  },
  mounted() {},
  methods: {
    operator(cellValue = "-") {
      return (
        this.userOption.find((el) => el.dictValue == cellValue)?.dictLabel ||
        cellValue
      );
    },
    logFormat(row) {
      let preText = `更改了${row.operateType}，更改为：`;
      let context = [];
      try {
        context = JSON.parse(row.operateContext);
      } catch (e) {}
      let operaterText = "";
      if (row.operateType === "业务类型") {
        operaterText = context.map((i) => i.businessName).join("、");
      } else if (row.operateType === "组织") {
        operaterText = context
          .map(
            (i) =>
              `${i?.orgNoOneName || ""}${i?.orgNoTwoName ||
                ""}${i?.orgNoThreeObj || ""}`
          )
          .join("、");
      } else if (row.operateType === "城市") {
        operaterText = context
          .map((i) => `${i.provinceName}${i.cityName}${i.countryName}`)
          .join("、");
      } else if (row.operateType === "启用/停用") {
        operaterText = context.status;
      }

      return preText + operaterText;
    },
    handleClick() {
      console.log(this.activeName);
    },
    open(userId) {
      this.visible = true;
      this.userId = userId;
      this.loadData();
    },

    submit() {},
    cancel() {
      this.visible = false;
    },
    async loadData() {
      this.loading = true;
      const res = await queryLog({
        userId: this.userId,
        pageNum: 1,
        pageSize: 9999,
      });
      if (res.code === "10000") {
        this.logs = res.data
        console.log(this.logs, "======lsog");
        this.total = res.total;
      }
      this.loading = false;
    },
  },
};
</script>

<style lang="less" scoped>
.center-content {
}

/deep/.el-step__description {
  background: #f1f1f1;
  padding: 8px;
  color: #333;
}
.desc {
  .desc-top {
    margin-bottom: 8px;
    span {
      margin: 0 20px 0 0;
    }
  }
}
.center-content {
  overflow: auto;
  height: 60vh;
}
</style>
