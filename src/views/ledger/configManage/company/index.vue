//公司机构
<template>
  <div class="partner-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    />
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="goToAdd"
            v-has-permi="['ledger:company:add']"
          >
            新增
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchTypes"
            v-has-permi="['ledger:company:import']"
          >
            导入
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchTag"
            v-has-permi="['ledger:company:importTag']"
          >
            批量配置标签
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :value="!row.status"
            @change="(val) => handleStatusChange(val, row)"
            :disabled="!checkPermission(['ledger:company:status'])"
          >
          </el-switch>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <div class="button-group">
            <el-button
              size="large"
              type="text"
              @click.stop="editHandler(row)"
              v-has-permi="['ledger:company:edit']"
              >编辑</el-button
            >
            <el-button
              size="large"
              type="text"
              @click.stop="goDetail(row)"
              v-has-permi="['ledger:company:detail']"
              >详情</el-button
            >
            <el-button
              size="large"
              type="text"
              @click.stop="deleteHandler(row)"
              v-has-permi="['ledger:company:delete']"
              >删除</el-button
            >
          </div>
          <div class="button-group second-line">
            <el-button
              size="large"
              type="text"
              @click.stop="editTag(row, true)"
              v-has-permi="['ledger:company:addTag']"
              >增标签</el-button
            >
            <el-button
              size="large"
              type="text"
              @click.stop="editTag(row, false)"
              v-has-permi="['ledger:company:minusTag']"
              >减标签</el-button
            >
            <el-button
              size="large"
              type="text"
              @click.stop="handleLog(row)"
              v-has-permi="['ledger:company:log']"
              >日志</el-button
            >
          </div>
        </template>
        <template slot="companyTag" slot-scope="{ row, $index }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in row.companyTagList"
            >{{ item }}</el-tag
          >
        </template>
      </GridTable>
    </el-card>
    <TagEdit
      ref="tagEdit"
      @refreshDataList="getList"
    ></TagEdit>
    <el-dialog
      title="操作日志"
      :visible.sync="logVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="70%"
    >
      <Timeline
        :list="recordList"
        operateTypeTitle="operatorTypeName"
        operatorNameTitle="operatorUserName"
        createTimeTitle="operatorTime"
        operateDetailTitle="remark"
      ></Timeline>
    </el-dialog>
    <BatchUpload
      @uploadSuccess="getList"
      ref="batchUpload"
    ></BatchUpload>
    <BatchUploadTag
      @uploadSuccess="getList"
      ref="batchUploadTag"
      title="批量配置公司标签"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
    >
      <template #templateBtn>
        <el-button type="primary" @click="downTagExcel(1)"
          >点击下载增标签模板</el-button
        >
        <el-button type="primary" @click="downTagExcel(0)"
          >点击下载减标签模板</el-button
        >
      </template>
      <template #extraForm="{params}">
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="params.remark"
                rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
                placeholder="请输入具体的原因描述，500个字符以内"
              /> </el-form-item
          ></el-col>
        </el-row>
      </template>
    </BatchUploadTag>
  </div>
</template>

<script>
import BatchUpload from "./components/batchUpload.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import Timeline from "@/components/Timeline/index.vue";
import Api from "@/api/ledger/company.js";
import TagEdit from "./components/tagEdit.vue";
import BatchUploadTag from "@/components/BatchUpload/index.vue";

export default {
  name: "ledgerCompany",
  components: {
    AdvancedForm,
    GridTable,
    Timeline,
    BatchUpload,
    TagEdit,
    BatchUploadTag,
  },

  data() {
    return {
      uploadObj: {
        api: "/export/report/importStationTag",
        url: "/charging-maintenance-ui/static/公司批量增标签模板.xlsx",
        extraData: {
          remark: "",
        },
      },
      recordList: [],
      logVisible: false,
      tableId: "partnerManageList",
      loading: false,
      total: 0,
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        companyName: "",
        contact: "",
        status: "",
      },
    };
  },
  computed: {
    config() {
      return [
        {
          key: "companyName",
          title: "公司名称",
          type: "input",
          placeholder: "请输入公司名称",
        },
        {
          key: "companyCategory",
          title: "公司类别",
          type: "input",
          placeholder: "请输入公司类别",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "请选择状态",
          options: [
            {
              dictLabel: "启用",
              dictValue: 0,
            },
            {
              dictLabel: "禁用",
              dictValue: 1,
            },
          ],
        },
      ];
    },
    columns() {
      return [
        {
          field: "companyName",
          title: "公司名称",
          customWidth: 200,
        },
        {
          field: "companyCategory",
          title: "公司类别",
        },
        {
          field: "companyAttribute",
          title: "公司属性",
        },
        {
          field: "companyDesc",
          title: "公司说明",
        },
        {
          field: "contact",
          title: "联系人",
        },
        {
          field: "contactTel",
          title: "联系方式",
        },
        {
          field: "socialCreditCode",
          title: "统一社会信用代码",
          minWidth: 120,
        },
        {
          field: "priceType",
          title: "电费计价方式",
        },
        {
          field: "mailAddress",
          title: "发票邮寄地址",
        },
        {
          field: "companyTagList",
          title: "公司标签",
          slots: { default: "companyTag" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "status",
          title: "状态",
          slots: { default: "status" },
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
  },

  activated() {
    this.getList();
  },

  methods: {
    checkPermission,
    handleBatchTypes() {
      this.$refs.batchUpload.open();
    },
    downTagExcel(isAdd = 1) {
      window.location.href = isAdd
        ? "/charging-maintenance-ui/static/公司批量增标签模板.xlsx"
        : "/charging-maintenance-ui/static/公司批量减标签模板.xlsx";
    },
    handleBatchTag() {
      this.uploadObj = {
        api: "/export/report/importCompanyTag",
        extraData: {
          remark: "",
        },
      };
      this.$refs.batchUploadTag.open();
    },
    //日志
    handleLog(row) {
      this.logVisible = true;
      Api.queryLogList({ companyId: row.companyId }).then((res) => {
        this.recordList = res.data;
      });
    },
    closeDialog() {
      this.logVisible = false;
    },
    async getList() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };
      const res = await Api.queryList(params);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.total = res.total;
      }
    },
    changePage() {
      this.getList();
    },
    goToAdd() {
      this.$router.push({
        name: "companyManageAdd",
      });
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getList();
    },
    async handleStatusChange(val, { companyId }) {
      this.loading = true;
      const res = await Api.changeStatus({
        companyId,
        status: val ? 0 : 1,
      });
      this.loading = false;
      if (res.code === "10000") {
        this.getList();
      } else {
        this.$message.error(res.message);
      }
    },
    editHandler(row) {
      this.$router.push({
        name: "companyManageEdit",
        query: {
          companyId: row.companyId,
        },
      });
    },
    goDetail(row) {
      this.$router.push({
        name: "companyManageDetail",
        query: {
          companyId: row.companyId,
        },
      });
    },
    editTag(row, isPlus) {
      this.$refs.tagEdit.open(row, isPlus);
    },
    deleteHandler(row) {
      //   if (row.status == 0) {
      //     this.$message.warning("启用状态下无法删除，请先停用该合作商！");
      //     return;
      //   }
      this.$confirm("确定删除该公司机构吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await Api.remove({ companyId: row.companyId });
        if (res.code === "10000") {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getList();
        } else {
          this.$message({
            type: "error",
            message: res.message || "删除失败",
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
