<template>
  <el-dialog
    title="选择检查项"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="80%"
  >
    <BuseCrud
      ref="checkCrud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="true"
      :customSelectCount="selectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog" :loading="btnLoading"
        >取 消</el-button
      >
      <el-button
        type="primary"
        @click.stop="handleConfirm"
        :loading="btnLoading"
        >确认</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import * as api from "@/api/workOrderType/index.js";
import { list } from "@/api/operationMaintenanceManage/configManage/checkItems.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "timeConfigPage",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      btnLoading: false,
      visible: false,
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "defId",
          isCurrent: true,
        },
        checkboxConfig: {
          //   checkRowKeys: [],
          //   reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "formName",
          title: "检查项名称",
        },
        {
          field: "formDefDesc",
          title: "检查项描述",
        },
        {
          field: "businessType",
          title: "业务类型",
        },
        {
          field: "checkTarget",
          title: "检查对象",
        },
        // {
        //   field: "entrance",
        //   title: "备注",
        // },
        {
          field: "defCreatorName",
          title: "创建人",
        },
        {
          field: "createTime",
          title: "创建时间",
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedIds: [
        "511119155152490496",
        "501332994590711808",
        "501332680177295360",
      ],
      checkObjectOptions: [
        { dictLabel: "设备", dictValue: "1" },
        { dictLabel: "站点", dictValue: "2" },
      ],
      id: "",
    };
  },
  computed: {
    showSelectNum() {
      return this.selectedIds?.length > 0;
    },
    selectNum() {
      return this.selectedIds?.length || 0;
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "formName",
            element: "el-input",
            title: "检查项名称",
          },
          {
            field: "businessType",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.businessTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "checkTarget",
            title: "检查对象",
            element: "el-select",
            props: {
              options: this.checkObjectOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: true,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        //自定义操作按钮
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    Promise.all([
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          //   this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleConfirm() {
      const params = {
        id: this.id,
        defIds: this.selectedIds,
      };
      console.log(params);
      this.btnLoading = true;
      api
        .saveCheckItems(params)
        .then((res) => {
          this.btnLoading = false;
          if (res?.code === "10000") {
            this.$message.success("配置成功");
            this.closeDialog();
            this.$emit("handleSuccess");
          }
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    setSelectedRows() {
      this.$nextTick(() => {
        const table = this.$refs.checkCrud.getVxeTableRef();
        table.setAllCheckboxRow(false);
        this.selectedIds?.map((x) => {
          let selectRow = table.getRowById(x);
          if (selectRow) {
            table.setCheckboxRow(selectRow, true);
          }
        });
      });
    },
    open(row) {
      this.id = row.id;
      api.getConfiguredItem({ id: row.id }).then((res) => {
        if (res?.code === "10000") {
          this.visible = true;
          this.selectedIds = res.data;
          this.handleReset();
        }
      });
    },
    closeDialog() {
      this.visible = false;
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    handleBatchSelect(arr, isClearAll = false) {
      if (isClearAll) {
        this.selectedIds = [];
      } else {
        const reserveData = this.selectedIds?.filter(
          (x) => !this.tableData?.map((x) => x.defId)?.includes(x)
        );
        this.selectedIds = [...reserveData, ...arr?.map((x) => x.defId)];
      }
    },
    checkPermission,
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
      this.setSelectedRows();
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
