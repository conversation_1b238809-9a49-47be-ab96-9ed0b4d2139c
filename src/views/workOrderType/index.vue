// 工单类型
<template>
  <div class="app-container">
    <div class="page-header">
      <h3>工单类型</h3>
    </div>
    <el-radio-group v-model="activeName" style="margin:10px 0 20px;">
      <el-radio-button
        v-for="(item, index) in radioList"
        :key="index"
        :label="item.dictValue"
        >{{ item.dictLabel }}</el-radio-button
      >
    </el-radio-group>
    <div class="mb10">
      <el-button
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click.stop="handleAdd(1)"
        v-has-permi="['operation:workOrderType:add']"
        >新增</el-button
      >

      <span class="tip-text">
        自定义工单类型
        <el-tooltip
          effect="dark"
          placement="top"
          content="自定义工单类型指的是：处理表单完全自定义，且必须关联检查项，否则无法生效。"
        >
          <i class="el-icon-question"></i>
        </el-tooltip>
      </span>
    </div>
    <el-card v-loading="loading">
      <el-tree
        :data="data"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :props="defaultProps"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div class="node-label">{{ node.label }}</div>
          <div class="node-btn">
            <div class="custom-type">
              自定义工单类型<el-switch
                v-model="data.customizeFlag"
                :disabled="
                  !checkPermission(['operation:workOrderType:isCustom'])
                "
                @change="handleCustomChange(data)"
                style="margin-left: 4px;"
                active-value="Y"
                inactive-value="N"
              >
              </el-switch>
            </div>
            <el-button
              type="text"
              size="mini"
              @click="handleCheckItems(data)"
              v-has-permi="['operation:workOrderType:bind']"
              :style="{
                marginRight: '16px',
                color: data.configFlag == 'Y' ? '#029c7c' : '#c0c4cc',
              }"
            >
              {{ data.configFlag == "Y" ? "已关联检查项" : "未关联检查项" }}
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleAdd(data.typeLevel + 1, data)"
              :style="data.typeLevel > 2 ? 'visibility: hidden' : ''"
              icon="el-icon-plus"
              v-has-permi="['operation:workOrderType:add']"
            >
              新增
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(data.typeLevel, data)"
              icon="el-icon-edit"
              v-has-permi="['operation:workOrderType:edit']"
            >
              修改
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleDelete(data.id)"
              icon="el-icon-minus"
              style="color: red"
              v-has-permi="['operation:workOrderType:delete']"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-tree>
    </el-card>
    <el-dialog
      :title="title"
      :visible.sync="modalVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="modalForm"
        ref="modalForm"
        label-width="150px"
        :rules="rules"
      >
        <el-form-item label="一级类型：" prop="firstType">
          <span v-if="addLevel == 2 || addLevel == 3">{{
            modalForm.firstType
          }}</span>
          <el-input
            v-model="modalForm.firstType"
            placeholder="请输入一级工单类型，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item
          label="二级类型："
          prop="secondType"
          v-if="addLevel == 2 || addLevel == 3"
        >
          <span v-if="addLevel == 3">{{ modalForm.secondType }}</span>
          <el-input
            v-model="modalForm.secondType"
            placeholder="请输入二级工单类型，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item label="三级类型：" prop="thirdType" v-if="addLevel == 3">
          <el-input
            v-model="modalForm.thirdType"
            placeholder="请输入三级工单类型，长度100个字符以内"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="自定义工单类型：" prop="customizeFlag">
          <el-radio-group v-model="modalForm.customizeFlag">
            <el-radio label="N">否</el-radio>
            <el-radio label="Y">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="handleSubmit"
          :loading="submitLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <CheckItemsDialog
      ref="checkItemsDialog"
      @handleSuccess="getTreeData"
    ></CheckItemsDialog>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import * as api from "@/api/workOrderType/index.js";
import CheckItemsDialog from "./components/checkItemsDialog.vue";
export default {
  components: { CheckItemsDialog },
  data() {
    return {
      activeName: "1",
      modalVisible: false,
      title: "新增工单类型",
      submitLoading: false,
      data: [],
      modalForm: {
        firstType: "",
        secondType: "",
        thirdType: "",
        customizeFlag: "N",
      },
      rules: {
        firstType: [{ required: true, message: "工单类型不能为空" }],
        secondType: [{ required: true, message: "工单类型不能为空" }],
        thirdType: [{ required: true, message: "工单类型不能为空" }],
      },
      addLevel: 1,
      defaultProps: {
        children: "childrenList",
        label: "typeName",
      },
      flattenData: [],
      modalType: "add",
      parentId: undefined,
      editId: undefined,
      radioList: [
        // { label: "充电业务", value: "1" },
        // { label: "储能业务", value: "2" },
        // { label: "光伏业务", value: "3" },
      ],
      loading: false,
      configFlag: "N",
    };
  },
  mounted() {
    Promise.all([
      this.getDicts("order_business_type").then((response) => {
        this.radioList = response.data;
        this.activeName = this.radioList[0]?.dictValue;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getTreeData();
        });
      }, 500);
    });
  },
  watch: {
    activeName: {
      handler() {
        this.getTreeData();
      },
    },
  },
  methods: {
    checkPermission,
    handleCheckItems(row) {
      this.$refs.checkItemsDialog.open(row);
    },
    handleCustomChange(row) {
      const text = row.customizeFlag == "N" ? "非自定义类型" : "自定义工单类型";
      this.$confirm(`是否将该工单类型设置为${text}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = {
            id: row.id,
            customizeFlag: row.customizeFlag,
          };
          api.customChange(data).then((res) => {
            if (res?.success) {
              this.$message.success("设置成功");
              //更新列表
              this.getTreeData();
            } else {
              this.$message.error("设置失败");
            }
          });
        })
        .catch(() => {
          row.customizeFlag = row.customizeFlag == "Y" ? "N" : "Y";
        });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getTreeData() {
      this.loading = true;
      api
        .queryTreeList({ businessType: this.activeName })
        .then((res) => {
          this.loading = false;
          this.data = JSON.parse(JSON.stringify(res.data));
          this.flattenData = this.flattenArray(res.data);
          console.log("-----", this.flattenData);
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleAdd(level, data) {
      console.log(data);
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "新增工单类型";
      this.modalType = "add";
      this.modalForm.customizeFlag = "N";
      this.configFlag = "N";
      if (level == 1) {
        this.modalForm.firstType = "";
        this.parentId = undefined;
      } else if (level == 2) {
        this.modalForm.firstType = data.typeName;
        this.modalForm.secondType = "";
        this.parentId = data.id;
      } else if (level == 3) {
        this.modalForm.firstType = data.parentName; //树结构需提供父级的label 或调接口获取
        this.modalForm.secondType = data.typeName;
        this.modalForm.thirdType = "";
        this.parentId = data.id;
      }
    },
    handleEdit(level, data) {
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "修改工单类型";
      this.modalType = "edit";
      this.editId = data.id;
      this.modalForm.customizeFlag = data.customizeFlag;
      this.configFlag = data.configFlag;
      if (level == 1) {
        this.modalForm.firstType = data.typeName;
      } else if (level == 2) {
        this.modalForm.firstType = data.parentName;
        this.modalForm.secondType = data.typeName;
      } else if (level == 3) {
        this.modalForm.firstType = this.flattenData?.find(
          (x) => x.id == data.parentId
        )?.parentName; //树结构需提供父级的label 或调接口获取
        this.modalForm.secondType = data.parentName;
        this.modalForm.thirdType = data.typeName;
      }
    },
    //提交
    handleSubmit() {
      const arr = [
        { level: 1, value: "firstType" },
        { level: 2, value: "secondType" },
        { level: 3, value: "thirdType" },
      ];
      const method = this.modalType === "add" ? "addType" : "editType";
      const value = arr.find((x) => x.level === this.addLevel)?.value;

      this.$refs.modalForm.validate((valid) => {
        if (valid) {
          const params =
            this.modalType === "add"
              ? {
                  typeName: this.modalForm[value],
                  typeLevel: this.addLevel,
                  parentId: this.parentId,
                  businessType: this.activeName,
                  customizeFlag: this.modalForm.customizeFlag,
                }
              : {
                  typeName: this.modalForm[value],
                  id: this.editId,
                  customizeFlag: this.modalForm.customizeFlag,
                };
          console.log("提交", this.modalForm);
          this.submitLoading = true;
          api[method](params).then((res) => {
            if (res?.success) {
              this.$message.success("提交成功");
              this.getTreeData();
              this.submitLoading = false;
              if (
                this.configFlag === "N" &&
                this.modalForm.customizeFlag === "Y"
              ) {
                setTimeout(() => {
                  this.$message.warning(
                    "该工单类型未配置检查项，请先配置检查项！"
                  );
                }, 500);
              }
              this.closeDialog();
            }
          });
        } else {
          console.log("校验失败");
        }
      });
    },
    closeDialog() {
      console.log("关闭弹窗");
      this.$refs.modalForm.resetFields();
      this.modalVisible = false;
    },
    handleDelete(id) {
      this.$confirm(
        "删除后，已生成的工单不受影响！",
        "确定要删除该工单类型吗?",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }
      ).then(() => {
        let data = {
          id: id,
        };
        api.deleteType(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getTreeData();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  align-items: center;
  h3 {
    margin-right: 40px;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-label {
    max-width: 350px;
    word-wrap: break-word;
    white-space: normal;
  }
  .node-btn {
    min-width: 170px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
/deep/ .el-tree-node__content {
  padding-top: 10px;
  padding-bottom: 10px;
  height: auto;
}
/deep/ .el-card__body {
  max-height: 80vh;
  overflow-y: auto;
}
/deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 10px 24px;
  font-size: 14px;
}
.tip-text {
  margin-left: 20px;
  font-size: 14px;
  color: rgba(187, 187, 187, 1);
  i {
    margin-left: 2px;
  }
}
.custom-type {
  font-size: 12px;
  margin-right: 16px;
}
</style>
