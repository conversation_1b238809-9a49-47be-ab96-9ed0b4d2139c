<template>
  <div class="login" id="login">
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <div class="logoandname">
        <!-- <img
          :src="toptitleicon"
          alt=""
          style="width:30px;vertical-align: middle;margin-bottom: 5px;"
        /> -->
        <h3 class="title">{{ orgName }}</h3>
      </div>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          size="medium"
          auto-complete="off"
          placeholder="账号"
        >
          <svg-icon
            slot="prefix"
            icon-class="user"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          size="medium"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="password"
            class="el-input__icon input-icon"
          />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon
            slot="prefix"
            icon-class="validCode"
            class="el-input__icon input-icon"
          />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click.stop="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <!-- <el-checkbox
        v-model="loginForm.rememberMe"
        style="margin: 0px 0px 25px 0px"
        >记住密码</el-checkbox
      > -->
      <el-form-item style="width: 100%">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 100%"
          @click.native.prevent="handleLogin"
          class="login-btn"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>

    <el-dialog
      title="第一次登录，请修改密码"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form ref="form" :model="user" :rules="rules" label-width="80px">
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            v-model="user.oldPassword"
            placeholder="请输入旧密码"
            type="password"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="user.newPassword"
            placeholder="请输入新密码"
            type="password"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="user.confirmPassword"
            placeholder="请确认密码"
            type="password"
          />
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input
            v-model="user.code"
            auto-complete="off"
            placeholder="验证码"
            style="width: 63%"
            @keyup.enter.native="handleLogin"
          >
            <svg-icon
              slot="prefix"
              icon-class="validCode"
              class="el-input__icon input-icon"
            />
          </el-input>
          <div class="login-code">
            <img
              :src="userCodeUrl"
              @click.stop="getUserCode"
              class="login-code-img"
            />
          </div>
        </el-form-item>
      </el-form>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="closeUpdate">关闭</el-button>
    </el-dialog>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>© 2018-2023 邦道科技有限公司 版权所有. 苏ICP备15059919号</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg, firstLoginResetPwd } from "@/api/login";
import Cookies from "js-cookie";
// import Base64 from "js-base64";
// const Base64 = require('js-base64').Base64;
//引入AES加密解密工具类
import { Encrypt, Decrypt } from "@/utils/aes";
import { getQueryString } from "@/utils/comm.js";
import moment from "moment/moment";
import { LOGIN_USER_LOGIN_COUNT } from "@/utils/track/track-event-constants";

export default {
  name: "Login",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      orgName: "欢迎登录，能源维保通",
      toptitleicon: require("@/assets/logo/jnswlogo1.png"),
      codeUrl: "",
      userCodeUrl: "",
      cookiePassword: "",
      submitForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      user: {
        username: "",
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
        code: undefined,
        uuid: undefined,
      },
      firstResetPasswordForm: {
        username: "",
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
        code: undefined,
        uuid: undefined,
      },
      loading: false,
      redirect: undefined,
      dialogVisible: false,
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          {
            pattern: /^(?![0-9a-z]+$)(?![0-9A-Z]+$)(?![0-9\W]+$)(?![a-z\W]+$)(?![a-zA-Z]+$)(?![A-Z\W]+$)[a-zA-Z0-9\W_]{8,20}$/,
            message:
              "密码应由大写字母、小写字母、特殊符号和数字中的至少三种组成！且长度在8到20之间",
          },
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" },
        ],
        code: [{ required: true, message: "验证码不能为空", trigger: "blur" }],
      },
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  mounted() {},
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.data.image;
        this.loginForm.uuid = res.data.uuid;
      });
    },
    getUserCode() {
      getCodeImg().then((res) => {
        this.userCodeUrl = "data:image/gif;base64," + res.data.image;
        this.user.uuid = res.data.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username:
          username === undefined ? this.loginForm.username : Decrypt(username),
        password:
          password === undefined ? this.loginForm.password : Decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", this.loginForm.password, {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          this.submitForm.username = Encrypt(this.loginForm.username);
          let salt = Math.random();
          this.submitForm.salt = salt;
          this.submitForm.password = Encrypt(
            Encrypt(this.loginForm.password) + salt
          );
          this.submitForm.rememberMe = this.loginForm.rememberMe;
          console.log(this.loginForm.code);
          console.log(this.loginForm.uuid);
          this.submitForm.code = this.loginForm.code;
          this.submitForm.uuid = this.loginForm.uuid;
          this.$store
            .dispatch("Login", this.submitForm)
            .then(() => {
              //登录成功埋点用户绑定
              this.trackLogin();

              var firstlogin = this.$store.state.user.firstLogin;
              if (firstlogin === "") {
                console.log("不是第一次登录");
                this.loading = false;
                localStorage.setItem("loginName", this.loginForm.username);
                // this.$router.push({ path: this.redirect || '/' });
                this.$router.push("/index").catch((err) => {});
              } else {
                console.log("第一次登录");
                this.dialogVisible = true;
                this.getUserCode();
              }
              setTimeout("console.clear()", 300);
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
    submit() {
      this.user.username = this.loginForm.username;
      this.firstResetPasswordForm = JSON.parse(JSON.stringify(this.user));
      this.$refs.form.validate((valid) => {
        if (valid) {
          let form = this.firstResetPasswordForm;
          form.username = Encrypt(form.username);
          form.oldPassword = Encrypt(form.oldPassword);
          form.newPassword = Encrypt(form.newPassword);
          form.confirmPassword = Encrypt(form.confirmPassword);

          firstLoginResetPwd(form).then((res) => {
            if (res.code == 10000) {
              this.msgSuccess("修改成功");
              this.dialogVisible = false;
              this.loading = false;
            } else {
              this.msgError(res.message);
            }
          });
        }
      });
    },
    closeUpdate() {
      this.dialogVisible = false;
      location.reload();
    },
    //登录成功埋点用户绑定
    trackLogin() {
      _bzt.login({
        //设备ID，非必传，默认从公共参数中获取
        //deviceId: '',
        // 用户标识，必传
        distinctId: this.$store.state.user.userId,
        // 用户标识扩展集合，非必传
        // identityMap: {
        //   mobile: '', // 手机号
        //   alipayId: '', // 支付宝uid
        //   wxOpenId: '', // 微信openId
        //   merchantUid: '' // 三方商户uid
        // }
      });

      //登录频率上报
      _bzt.trackEvent(LOGIN_USER_LOGIN_COUNT, {
        immediate: true,
        loginAccount: this.submitForm.username,
        loginTime: moment().format("YYYY-MM-DD HH:mm:ss")
      });
    }
  },
};
</script>

<style lang="less" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("~@/assets/image/loginBg.png");
  // background-repeat: no-repeat;
  // background-size: 100%;
  // background-position-x: 100%;
  // background-position-y: 100%;
  background-size: cover;
}

.title {
  margin: 10px;
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
}

.login-form {
  // border: 2px solid ;
  border-radius: 10px;
  // border-image: linear-gradient(188deg, #1BFFBE 0%, #DAFFF7 26%, #1E6C5B 92%) 2;
  width: 400px;
  padding: 25px 25px 5px 25px;
  position: absolute;
  right: 150px;
  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }

  background: linear-gradient(91deg, #095755 43%, #1b6653 98%);
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #06ffca;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}

.logoandname {
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-btn {
  background: linear-gradient(
    180deg,
    #06ffca 0%,
    #029c7c 53%,
    #009677 78%,
    #07a785 96%
  );
  border: none;
}
</style>
