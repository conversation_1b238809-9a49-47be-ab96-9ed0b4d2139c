<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="pageTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            >新增
          </el-button>
        </template>
        <!-- 创建时间 -->
        <template slot="createTime" slot-scope="{ row, $index }">
          <span>{{ parseTime(row.createTime) }}</span>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button size="large" type="text" @click.stop="handleUpdate(row)"
            >配置</el-button
          >
          <el-button
            size="large"
            type="text"
            v-if="row.isEnabled === '1'"
            @click.stop="handleDisable(row)"
            >禁用
          </el-button>
          <el-button
            size="large"
            type="text"
            v-else
            @click.stop="handleDisable(row)"
            >启用
          </el-button>
          <el-button size="large" type="text" @click.stop="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="cancelDialog"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="业务名称" prop="compensationName">
          <el-input v-model="form.compensationName" placeholder="业务名称" />
        </el-form-item>
        <el-form-item label="监听类型" prop="listeningType">
          <el-radio-group v-model="form.listeningType">
            <el-radio
              v-for="dict in listeningTypeOptions"
              :key="dict.id"
              :label="dict.id"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="监听时机" prop="listeningTiming">
          <el-select
            v-model="form.listeningTiming"
            placeholder="请选择监听时机"
            clearable
            size="mini"
            style="width: 100%"
          >
            <el-option
              v-for="dict in listeningTimingOptions"
              :key="dict.id"
              :label="dict.label"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="补偿执行类型" prop="executionType">
          <el-radio-group v-model="form.executionType">
            <el-radio
              v-for="dict in executionTypeOptions"
              :key="dict.id"
              :label="dict.id"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="触发类型" prop="compensationType">
          <el-radio-group v-model="form.compensationType">
            <el-radio
              v-for="dict in compensationTypeOptions"
              :key="dict.id"
              :label="dict.id"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="补偿类型值" prop="compensationValue">
          <el-input v-model="form.compensationValue" placeholder="补偿类型值" />
        </el-form-item>
        <el-form-item label="补偿类型拓展值" prop="compensationExtendValue">
          <el-input
            v-model="form.compensationExtendValue"
            placeholder="补偿类型拓展值"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="isEnabled">
          <el-radio-group v-model="form.isEnabled">
            <el-radio
              v-for="dict in enabledOptions"
              :key="dict.id"
              :label="dict.id"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixin from "@/mixin/commonPage";
import {
  add,
  deleteFunc,
  list,
  update,
} from "@/api/orderConfig/businessConfig";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  mixins: [mixin],
  components: { GridTable, AdvancedForm },
  data() {
    return {
      pageInitQuery: false,
      // 遮罩层
      loading: false,
      queryApi: list,
      updateApi: update,
      addApi: add,
      // 状态数据字典
      listeningTypeOptions: [],
      enabledOptions: [],
      executionTypeOptions: [],
      compensationTypeOptions: [],
      listeningTimingOptions: [],
      dialogName: "业务服务",
      form: {
        compensationName: "",
        listeningType: "",
        listeningTiming: "01",
        executionType: "",
        compensationType: "",
        compensationValue: "",
        compensationExtendValue: "",
        isEnabled: "1",
      },
      finallySearch: null,
      rules: {
        compensationName: [
          { required: true, message: "业务名称不能为空", trigger: "blur" },
        ],
        listeningType: [
          { required: true, message: "监听类型不能为空", trigger: "blur" },
        ],
        listeningTiming: [
          { required: true, message: "监听时机不能为空", trigger: "blur" },
        ],
        compensationValue: [
          { required: true, message: "补偿类型值不能为空", trigger: "blur" },
        ],
      },
      dialogVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        compensationName: undefined,
        listeningType: undefined,
        serviceStatus: undefined,
      },
      tableData: [],
      pageTotal: 0,
      config: [],
      tableId: "businessConfigTable",
      columns: [
        {
          field: "compensationName",
          title: "接口服务名称",
        },
        {
          field: "listeningType", 
          title: "监听类型",
          minWidth: 120,
          formatter: ({ cellValue, row, column }) => {
            if (cellValue) {
              return this.oListeningType.filter(el => el.value == cellValue)[0].label
            }
          }
        },
        {
          field: "isEnabledValue",
          title: "状态",
          width: 100,
        },
        {
          field: "creator",
          title: "创建人",
        },
        {
          field: "createTime", 
          title: "创建时间",
          width: 180,
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      // oListeningType:Object.freeze({
      //   '1':'任务监听',
      //   '2':'执行监听'
      // }),
      oListeningType: [
        {
          value: "1",
          label: "任务监听"
        },
        {
          value: "2",
          label: "执行监听"
        }
      ]
    };
  },
  created() {
    this.enabledOptions = [
      { id: "0", label: "禁用" },
      { id: "1", label: "启用" },
    ];
    this.listeningTypeOptions = [
      { id: "1", label: "任务监听" },
      { id: "2", label: "执行监听" },
    ];
    this.executionTypeOptions = [{ id: "1", label: "restapi" }];
    this.compensationTypeOptions = [
      { id: "1", label: "Java类" },
      { id: "2", label: "表达式" },
      {
        id: "3",
        label: "委托表达式",
      },
    ];
    this.listeningTimingOptions = [
      { id: "01", label: "创建后" },
      { id: "02", label: "指派后" },
      { id: "03", label: "完成后" },
      { id: "04", label: "删除后" },
      { id: "11", label: "调用前" },
      { id: "12", label: "调用后" },
      { id: "13", label: "回退前" },
      { id: "14", label: "回退后" },
      { id: "15", label: "召回前" },
      { id: "16", label: "召回后" },
    ];
    this.initConfig();
    this.getList();
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "compensationName",
          title: "服务名称",
          type: "input",
          placeholder: "请输入服务名称",
        },
        {
          key: "listeningType",
          title: "监听类型",
          type: "select",
          options: this.listeningTypeOptions,
          optionLabel: "label",
          optionValue: "id",
          placeholder: "请选择监听类型",
        },
        {
          key: "serviceStatus",
          title: "状态",
          type: "select",
          options: this.enabledOptions,
          optionLabel: "label",
          optionValue: "id",
          placeholder: "请选择状态",
        },
      ];
    },
    handleAdd() {
      this.dialogTitle = this.dialogName + "新增";
      this.pageState = "add";
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        compensationName: "",
        listeningType: "1",
        listeningTiming: "",
        executionType: "1",
        compensationType: "1",
        compensationValue: "",
        compensationExtendValue: "",
        isEnabled: "1",
      };
      this.dialogVisible = true;
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1
      params.pageSize = this.queryParams.pageSize
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery(params) {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    getList(params) {
      const args = this._.cloneDeep(params?params:this.queryParams)
      this.loading = true;
      this.finallySearch = args
      this.queryApi(args)
        .then((response) => {
          this.tableData = response.data;
          this.pageTotal = response.total;
          this.loading = false;
          this.tableData.forEach((element) => {
            //状态
            this.$set(element, "isEnabledValue", this.formatEnabled(element));
            //监听类型
            // this.$set(element, "listeningTypeValue", this.oListeningType(element.listeningType));
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.log("调取查询接口错误" + err);
          this.loading = false;
        });
    },
    handleUpdate(row) {
      this.form = {};
      this.$refs.form && this.$refs.form.resetFields();
      this.dialogVisible = true;
      this.dialogTitle = this.dialogName + "编辑";
      this.pageState = "update";
      this.form = this._.cloneDeep(row);
    },
    handleDelete(row) {
      let _this = this;
      this.$confirm("确定删除选择数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteFunc({ compensationId: row.compensationId }).then(() => {
          _this.$message.success("删除成功");
          _this.getList();
        });
      });
    },
    handleDisable(row) {
      this.form = {};
      this.$refs.form && this.$refs.form.resetFields();
      this.pageState = "update";
      this.form = this._.cloneDeep(row);
      if (this.form.isEnabled === "1") {
        this.form.isEnabled = "0";
      } else {
        this.form.isEnabled = "1";
      }
      this.updateApi(this.form).then(() => {
        this.$message.success("编辑成功");
        this.dialogVisible = false;
        this.getList();
      });
    },
    formatEnabled(row) {
      return this.enabledOptions.find((item) => item.id === row.isEnabled)
        .label;
    },
    //分页切换
    changePage() {
      if(this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum
        this.finallySearch.pageSize = this.queryParams.pageSize
      }
      this.getList(this.finallySearch);
    },
  },
};
</script>
