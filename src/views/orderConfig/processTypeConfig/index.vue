<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="pageTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            >新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button size="large" type="text" @click.stop="handleUpdate(row)"
            >编辑</el-button
          >
          <el-button
            size="large"
            type="text"
            v-if="row.typeStatus === '1'"
            @click.stop="handleDisable(row)"
            >禁用
          </el-button>
          <el-button
            size="large"
            type="text"
            v-else
            @click.stop="handleDisable(row)"
            >启用
          </el-button>
          <el-button
            size="large"
            v-if="row.typeStatus !== '1'"
            type="text"
            @click.stop="handleDelete(row)"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="cancelDialog"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="类型编码" prop="typeCode">
          <el-input v-model="form.typeCode" placeholder="类型编码" />
        </el-form-item> -->
        <el-form-item label="类型名称" prop="typeLabel">
          <el-input v-model="form.typeLabel" placeholder="请输入类型名称" />
        </el-form-item>
        <!--        <el-form-item label="类型标识" prop="typeCode">-->
        <!--          <el-input v-model="form.typeCode" placeholder="请输入类型名称" />-->
        <!--        </el-form-item>-->
        <el-form-item label="业务类型" prop="businessType">
          <el-select filterable
                     clearable
                     v-model="form.businessType"
                     placeholder="请选择业务类型">
            <el-option
              v-for="dict in businessTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="typeStatus">
          <el-radio-group v-model="form.typeStatus">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.id"
              :label="dict.id"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixin from "@/mixin/commonPage";
import {
  add,
  deleteFunc,
  list,
  update,
} from "@/api/orderConfig/processTypeConfig";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";

export default {
  components: { GridTable, AdvancedForm },
  mixins: [mixin],
  data() {
    return {
      pageInitQuery: false,
      // 遮罩层
      loading: false,
      queryApi: list,
      updateApi: update,
      addApi: add,
      // 状态数据字典
      statusOptions: [],
      businessTypeOptions: [],
      dialogName: "工序项",
      form: {
        // typeCode: "",
        typeLabel: "",
        typeStatus: "1",
        businessType: "normal"
      },
      finallySearch: null,
      rules: {
        businessType: [
          { required: true, message: "业务类型不能为空", trigger: "blur" }
        ],
        typeLabel: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        typeStatus: [
          { required: true, message: "状态不能为空", trigger: "blur" },
        ],
      },
      dialogVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeLabel: undefined,
        typeStatus: undefined,
      },
      tableData: [],
      pageTotal: 0,
      config: [],
      tableId: "processTypeConfigTable",
      columns: [
        {
          field: "typeLabel",
          title: "类型名称",
          showOverflowTooltip: true,
        },
        {
          field: "typeCode",
          title: "类型编码",
          showOverflowTooltip: true,
        },
        {
          field: "creator",
          title: "创建人",
          minWidth: 180,
          showOverflowTooltip: true,
        },
        {
          field: "createTimeValue",
          title: "创建时间",
          width: 180,
          showOverflowTooltip: true,
        },
        {
          field: "lastOperator",
          title: "修改人",
          minWidth: 180,
          showOverflowTooltip: true,
        },
        {
          field: "updateTimeValue",
          title: "修改时间",
          width: 180,
          showOverflowTooltip: true,
        },
        {
          field: "typeStatusValue",
          title: "状态",
          width: 90,
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  created() {
    this.statusOptions = [
      { id: "0", label: "禁用" },
      { id: "1", label: "启用" },
    ];
    this.getDicts("flow_business_type").then(response => {
      this.businessTypeOptions = response.data;
    });

    this.initConfig()
    this.getList();

  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "typeLabel",
          title: "类型名称",
          type: "input",
          placeholder: "请输入类型名称",
        },
        {
          key: "typeStatus",
          title: "状态",
          type: "select",
          options: this.statusOptions,
          optionLabel: "label",
          optionValue: "id",
          placeholder: "请选择状态",
        },
      ];
    },
    handleAdd() {
      this.dialogTitle = this.dialogName + "新增";
      this.pageState = "add";
      this.$refs.form && this.$refs.form.resetFields();
      this.form = {
        // typeCode: "",
        typeLabel: "",
        typeStatus: "1",
      };
      this.dialogVisible = true;
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1
      params.pageSize = this.queryParams.pageSize
      this.getList(params);
    },
    resetQuery(){
      this.queryParams = this.$options.data.call(this).queryParams
      this.getList()
    },
    getList(params) {
      const args = this._.cloneDeep(params?params:this.queryParams)
      this.finallySearch = args
      this.loading = true;
      this.queryApi(args)
        .then((response) => {
          this.tableData = response.data;
          this.pageTotal = response.total;
          this.loading = false;
          this.tableData.forEach((element) => {
            //状态
            this.$set(
              element,
              "typeStatusValue",
              this.formatTypeStatus(element)
            );
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
            //修改时间
            this.$set(
              element,
              "updateTimeValue",
              this.parseTime(element.updateTime)
            );
          });
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.log("调取查询接口错误" + err);
          this.loading = false;
        });
    },
    handleUpdate(row) {
      this.form = {};
      this.$refs.form && this.$refs.form.resetFields();
      this.dialogVisible = true;
      this.dialogTitle = this.dialogName + "编辑";
      this.pageState = "update";
      this.form = this._.cloneDeep(row);
    },
    handleDelete(row) {
      let _this = this;
      this.$confirm("确定删除选择数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteFunc({ typeId: row.typeId }).then((err) => {
          _this.$message.success("删除成功");
          _this.getList();
        });
      });
    },
    handleDisable(row) {
      this.form = {};
      this.$refs.form && this.$refs.form.resetFields();
      this.pageState = "update";
      this.form = this._.cloneDeep(row);
      if (this.form.typeStatus === "1") {
        this.form.typeStatus = "0";
      } else {
        this.form.typeStatus = "1";
      }
      this.updateApi(this.form).then(() => {
        this.$message.success("编辑成功");
        this.dialogVisible = false;
        this.getList();
      });
    },
    formatTypeStatus(row) {
      return this.statusOptions.find((item) => item.id === row.typeStatus)
        .label;
    },
    //分页切换
    changePage() {
      if(this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum
        this.finallySearch.pageSize = this.queryParams.pageSize
      }
      this.getList(this.finallySearch);
    },
  },
};
</script>
