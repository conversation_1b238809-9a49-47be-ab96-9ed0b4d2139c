<!-- 项目管理 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="deviceTable"
        :batchDelete="true"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="deviceTableTotal"
        @changePage="changePage"
        @handleSelectionChange="tableSelect"
        :loading="loading"
        :tableId="tableId"
        row-id="reportId"
        :checkMethod="checkMethod"
      >
        <template slot="ignoreRemark" slot-scope="{ row }">
          <div style="display:flex;align-items:center;justify-content:center">
            <span>{{ row.handleResult }}</span>
            <el-tooltip placement="top" v-if="row.handleResult == '已忽略'">
              <div slot="content">{{ row.ignoreRemark }}</div>
              <img
                :src="require('@/assets/icons/icon_question_mark.png')"
                width="15"
                height="15"
                style="marginLeft:5px;"
              />
            </el-tooltip>
          </div>
        </template>

        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-if="checkPermission(['errorPush:batchAdd'])"
            @click.stop="handleCreate"
          >
            批量转工单
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleIgnore"
            v-if="checkPermission(['errorPush:batchIgnore'])"
          >
            批量忽略
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleRules"
            v-if="checkPermission(['errorPush:rules'])"
          >
            建单规则
          </el-button>

          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBlackListStation"
            v-if="checkPermission(['errorPush:blackStation'])"
          >
            维护站点
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBlackList"
            v-if="checkPermission(['errorPush:blackUser'])"
          >
            用户短信拦截
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            type="text"
            size="large"
            v-if="
              checkPermission(['errorPush:ignore']) &&
                row.handleResult == '未处理'
            "
            @click="handleEdit(row)"
          >
            忽略
          </el-button>
          <el-button
            @click="handleAdd(row)"
            type="text"
            size="large"
            v-if="
              checkPermission(['errorPush:add']) && row.handleResult == '未处理'
            "
          >
            转工单
          </el-button>
          <el-button
            @click="handleDetail(row)"
            type="text"
            size="large"
            v-if="checkPermission(['errorPush:detail'])"
          >
            故障详情
          </el-button>
        </template>
        <template slot="businessNo" slot-scope="{ row, $index }">
          <el-button type="text" @click="jumpToOperationWorkOrder(row)"
            >{{ row.businessNo }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <el-dialog
      title="转工单"
      :visible.sync="addOrderVisible"
      :close-on-click-modal="false"
      @close="closeAddOrderVisible"
      append-to-body
      width="50%"
    >
      <div class="queryParamsWrap">
        <el-form
          :model="addForm"
          ref="addForm"
          :inline="true"
          label-width="110px"
        >
          <el-row>
            <el-col>
              <el-form-item label="站点名称:">
                <el-input v-model="handRow.stationName" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="站点地址:">
                <el-input v-model="handRow.stationAddr" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col>
              <el-form-item label="异常描述:">
                <el-input v-model="handRow.errorName" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row>
            <el-col>
              <el-form-item
                label="业务类型:"
                prop="businessTypeId"
                :rules="{
                  required: true,
                  message: '业务类型不能为空',
                  trigger: 'blur',
                }"
              >
                <el-select
                  v-model="addForm.businessTypeId"
                  @change="changeRelaBizType(addForm)"
                  size="mini"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in businessTypeOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item
                label="工单类型:"
                prop="orderTypeParentId"
                :rules="{
                  required: true,
                  message: '工单类型不能为空',
                  trigger: 'blur',
                }"
              >
                <el-row>
                  <el-col :span="8">
                    <el-select
                      v-model="addForm.orderTypeParentId"
                      style="width: 100%"
                      @change="
                        (val) => {
                          orderTypeParentChange(val, 2);
                        }
                      "
                    >
                      <el-option
                        v-for="item in orderTypeParentOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="addForm.orderTypeId"
                      style="width: 100%"
                      @change="
                        (val) => {
                          orderTypeParentChange(val, 3);
                        }
                      "
                      clearable
                    >
                      <el-option
                        v-for="item in orderTypeOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="addForm.threeOrderTypeId"
                      clearable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in threeOrderTypeOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <!-- <el-row>
                  <el-col :span="12">
                    <el-select
                      v-model="addForm.orderTypeParentName"
                      style="width:100%"
                    >
                      <el-option
                        v-for="item in orderTypeParentOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <el-select
                      v-model="addForm.orderTypeName"
                      style="width:100%"
                    >
                      <el-option
                        v-for="item in orderTypeOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                </el-row> -->
              </el-form-item>
              <el-form-item
                label="问题描述:"
                prop="orderDesc"
                :rules="{
                  required: true,
                  message: '问题描述不能为空',
                  trigger: 'blur',
                }"
              >
                <el-input
                  v-model="addForm.orderDesc"
                  type="textarea"
                  maxlength="500"
                  show-word-limit
                  size="mini"
                  rows="5"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
              <el-form-item label="紧急程度:" prop="urgencyLevel">
                <el-radio-group v-model="addForm.urgencyLevel">
                  <el-radio label="0">紧急</el-radio>
                  <el-radio label="1">一般</el-radio>
                </el-radio-group>
              </el-form-item>
              <!--              <el-form-item label="工单类型:"-->
              <!--                            prop="orderType"-->
              <!--                            :rules="{-->
              <!--                  required: true, message: '工单类型不能为空', trigger: 'blur'-->
              <!--                }">-->
              <!--                <el-select v-model="addForm.orderType" size="mini" placeholder="请选择">-->
              <!--                  <el-option-->
              <!--                    v-for="item in addForm.useFlowList"-->
              <!--                    :key="item.flowType"-->
              <!--                    :label="item.flowTypeName"-->
              <!--                    :value="item.flowType">-->
              <!--                  </el-option>-->
              <!--                </el-select>-->
              <!--              </el-form-item>-->
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item
                label="处理组:"
                prop="groupIds"
                :rules="{
                  required: true,
                  message: '处理组不能为空',
                  trigger: 'blur',
                }"
              >
                <el-select
                  v-model="addForm.groupIds"
                  multiple
                  @change="handleGroupChange"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in groupOptions"
                    :label="item.groupName"
                    :value="item.groupId"
                    :key="item.groupId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="通知人:" prop="userIds">
                <el-select
                  v-model="addForm.userIds"
                  multiple
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in groupMemberOptions"
                    :label="item.nickName + '-' + item.userName"
                    :value="item.userId"
                    :key="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeAddOrderVisible">取 消</el-button>
        <el-button type="primary" @click.stop="submitForm">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="批量转工单"
      :visible.sync="addAllOrderVisible"
      :close-on-click-modal="false"
      @close="closeAllAddOrderVisible"
      append-to-body
      width="50%"
    >
      <div class="queryParamsWrap">
        <el-form
          :model="addForm"
          ref="addForm"
          :inline="true"
          label-width="110px"
        >
          <el-row>
            <el-col>
              <el-form-item label="站点名称:">
                <el-input v-model="handRow.stationName" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="站点地址:">
                <el-input v-model="handRow.stationAddr" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="故障总数:">
                <el-input v-model="handRow.count" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item
                label="业务类型:"
                prop="businessTypeId"
                :rules="{
                  required: true,
                  message: '业务类型不能为空',
                  trigger: 'blur',
                }"
              >
                <el-select
                  v-model="addForm.businessTypeId"
                  @change="changeRelaBizType(addForm)"
                  size="mini"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in businessTypeOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item
                label="工单类型:"
                prop="orderTypeParentId"
                :rules="{
                  required: true,
                  message: '工单类型不能为空',
                  trigger: 'blur',
                }"
              >
                <el-row>
                  <el-col :span="8">
                    <el-select
                      v-model="addForm.orderTypeParentId"
                      style="width: 100%"
                      @change="
                        (val) => {
                          orderTypeParentChange(val, 2);
                        }
                      "
                    >
                      <el-option
                        v-for="item in orderTypeParentOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="addForm.orderTypeId"
                      style="width: 100%"
                      @change="
                        (val) => {
                          orderTypeParentChange(val, 3);
                        }
                      "
                      clearable
                    >
                      <el-option
                        v-for="item in orderTypeOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select
                      v-model="addForm.threeOrderTypeId"
                      clearable
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in threeOrderTypeOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                </el-row>
                <!-- <el-row>
                  <el-col :span="12">
                    <el-select
                      v-model="addForm.orderTypeParentName"
                      style="width:100%"
                    >
                      <el-option
                        v-for="item in orderTypeParentOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <el-select
                      v-model="addForm.orderTypeName"
                      style="width:100%"
                    >
                      <el-option
                        v-for="item in orderTypeOptions"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      />
                    </el-select>
                  </el-col>
                </el-row> -->
              </el-form-item>
              <el-form-item
                label="问题描述:"
                prop="orderDesc"
                :rules="{
                  required: true,
                  message: '问题描述不能为空',
                  trigger: 'blur',
                }"
              >
                <el-input
                  v-model="addForm.orderDesc"
                  type="textarea"
                  maxlength="500"
                  show-word-limit
                  size="mini"
                  rows="5"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
              <el-form-item label="紧急程度:" prop="urgencyLevel">
                <el-radio-group v-model="addForm.urgencyLevel">
                  <el-radio label="0">紧急</el-radio>
                  <el-radio label="1">一般</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item
                label="处理组:"
                prop="groupIds"
                :rules="{
                  required: true,
                  message: '处理组不能为空',
                  trigger: 'blur',
                }"
              >
                <el-select
                  v-model="addForm.groupIds"
                  multiple
                  @change="handleGroupChange"
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in groupOptions"
                    :label="item.groupName"
                    :value="item.groupId"
                    :key="item.groupId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="通知人:" prop="userIds">
                <el-select
                  v-model="addForm.userIds"
                  multiple
                  style="width: 100%;"
                >
                  <el-option
                    v-for="item in groupMemberOptions"
                    :label="item.nickName + '-' + item.userName"
                    :value="item.userId"
                    :key="item.userId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeAllAddOrderVisible">取 消</el-button>
        <el-button type="primary" @click.stop="submitAllForm">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="忽略异常信息"
      :visible.sync="ignoreVisible"
      :close-on-click-modal="false"
      @close="closeIgnoreVisible"
      append-to-body
      width="50%"
    >
      <div class="queryParamsWrap">
        <el-form
          :model="ignoreForm"
          :inline="true"
          ref="ignoreForm"
          label-width="110px"
        >
          <el-form-item
            label="忽略原因:"
            prop="ignoreRemark"
            :rules="{
              required: true,
              message: '忽略原因不能为空',
              trigger: 'blur',
            }"
          >
            <el-input
              v-model="ignoreForm.ignoreRemark"
              placeholder="500字符以内"
              type="textarea"
              rows="5"
              maxlength="500"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeIgnoreVisible">取 消</el-button>
        <el-button type="primary" @click.stop="ignoreMsg">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="故障详情"
      :visible.sync="detailVisible"
      :close-on-click-modal="false"
      @close="detailVisible = false"
      append-to-body
      width="50%"
    >
      <el-descriptions class="descriptions" :column="2" border>
        <el-descriptions-item
          v-for="(item, index) in faultInfoList"
          :key="index"
          :label="item.title"
        >
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import AddProject from "@/views/projectManage/addProject.vue";
import {
  createErrorOrder,
  fistLevelList,
  childrenList,
  getDeptList,
} from "@/api/operationWorkOrder/index.js";
import {
  ignore,
  batchIgnore,
  queryErrorInfoByPage,
  queryFaultDetail,
} from "@/api/errorPush/index.js";
import { useFlowList as useFlowListApi } from "@/api/orderScheduling/workStation.js";
import { regionData } from "element-china-area-data";
import { listAllUser } from "@/api/common.js";
import checkPermission from "@/utils/permission.js";
import { queryGroupList, queryGroupMemberList } from "@/api/errorPush/index.js";
import {
  ERROR_CLICK_BATCH_CREATE_ORDER,
  ERROR_CLICK_BATCH_IGNORE,
  ERROR_CLICK_CREATE_ORDER,
  ERROR_CLICK_IGNORE,
} from "@/utils/track/track-event-constants";

export default {
  name: "errorPush",
  components: {
    AdvancedForm,
    GridTable,
    AddProject,
  },
  data() {
    return {
      groupOptions: [],
      groupMemberOptions: [],
      faultType: "",
      loading: false,
      searchForm: {
        errorNo: "",
        deviceName: "",
        realDeviceName: "",
        deviceTypeNo: null,
        deviceModelId: "",
        pumpHouseId: "",
        pageNum: 1,
        pageSize: 10,
      },
      ignoreForm: {
        ignoreRemark: "",
      },
      addForm: {
        businessTypeId: "",
        orderTypeParentId: "",
        orderTypeId: "",
        threeOrderTypeId: "",
        urgencyLevel: "0",
        orderDesc: "",
        userIds: [],
        groupIds: [],
      },
      businessTypeOption: [], //业务类型
      errorTypeOptions: [], //业务类型
      orderTypeParentOptions: [
        // { label: "故障工单", value: "故障工单" },
        // { label: "抄表工单", value: "抄表工单" },
        // { label: "真车充电测试派单", value: "真车充电测试派单" },
      ],
      orderTypeOptions: [
        // { label: "急停故障", value: "急停故障" },
        // { label: "离线断电", value: "离线断电" },
        // { label: "离线断网", value: "离线断网" },
        // {
        //   label: "电桩上报故障(不含急停)",
        //   value: "电桩上报故障(不含急停)",
        // },
        // {
        //   label: "离线&故障(不含急停)同时发生",
        //   value: "离线&故障(不含急停)同时发生",
        // },
        // { label: "潜藏故障", value: "潜藏故障" },
        // { label: "抄表工单", value: "抄表工单" },
        // { label: "真车充电测试派单", value: "真车充电测试派单" },
      ],
      userOption: [], //用户列表
      noUseBusinessType: [
        "otherRoadTest",
        "acceptance",
        "online",
        "pileRoadTest",
        "construction",
      ],
      useFlowList: [], //工单类型
      options: [],
      finallySearch: null,
      deviceTable: [],
      deviceTableTotal: 0,
      addProjectDialogVisible: false, //显示增加设备档案弹框
      addOrderVisible: false, //添加工单
      addAllOrderVisible: false, //添加工单
      ignoreVisible: false, //忽略
      projectId: undefined, //项目id
      handRow: {
        stationName: undefined,
        stationAddr: undefined,
        errorName: undefined,
        count: 0,
      }, //项目id
      ignoreRemark: "", // 忽略 原因
      recordId: undefined,
      faultCodeId: undefined,
      deviceNo: "", //设备编号
      deviceTypeList: [], //设备类型列表
      deviceModelList: [], //设备型号列表
      statusDict: [], //泵房状态字典
      pumpHouseList: [], //泵房列表
      pageType: "add",
      status: "0",
      dialogTitle: "新增项目",
      // config: [],
      areaData: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
          fixed: "left",
          // slots: { default: "projectCode" }
        },
        {
          field: "equipNo",
          title: "设备编号",
        },
        {
          field: "deviceName",
          title: "设备名称",
        },
        {
          field: "equipType",
          title: "设备类型",
        },
        {
          field: "type",
          title: "告警类型",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.errorTypeOptions.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "errorName",
          title: "故障告警名称",
        },
        {
          field: "faultCode",
          title: "故障码",
        },
        {
          field: "alarmDesc",
          title: "故障描述",
        },
        {
          field: "triggerCount",
          title: "故障次数",
        },
        {
          field: "faultLevel",
          title: "告警等级",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.faultLevelOptions?.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "alarmDurationDesc",
          title: "告警持续时间",
        },
        {
          field: "gmtClear",
          title: "告警清除时间",
        },
        {
          field: "orgNo",
          title: "能投大区",
          formatter: ({ cellValue }) => {
            return (
              this.deptOptionList?.find((item) => item.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "belongPlace",
          title: "省市区",
          // formatter: ({ cellValue, row, column }) => {
          //   const province = this.areaData.find(
          //     (el) => el.value === row.province
          //   )?.label;
          //   const city = this.areaData.find((el) => el.value === row.city)
          //     ?.label;
          //   const county = this.areaData.find((el) => el.value === row.county)
          //     ?.label;
          //   return province + "-" + city + "-" + county;
          // },
          // customWidth: 250,
        },
        {
          field: "stationAddr",
          title: "详细地址",
        },
        {
          field: "pushTime",
          title: "推送时间",
        },
        {
          field: "status",
          title: "恢复类型",
          formatter: ({ cellValue }) => {
            return (
              this.alarmStatusOptions?.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },

        //
        //
        //
        // {
        //   field: "gmtCreate",
        //   title: "告警创建时间"
        // },
        //
        // {
        //   field: "gmtEnd",
        //   title: "告警结束时间"
        // },
        // {
        //   field: "errorFrom",
        //   title: "异常来源"
        //   // slots: { default: "projectCode" }
        // },
        // {
        //   field: "faultCode",
        //   title: "异常编号"
        // },
        // {
        //   field: "errorName",
        //   title: "异常信息"
        // },
        // {
        //   field: "stationName",
        //   title: "站点名称"
        // },

        {
          field: "handleResult",
          title: "处理结果",
          slots: { default: "ignoreRemark" },
        },
        {
          field: "businessNo",
          title: "运维工单",
          slots: { default: "businessNo" },
        },
        {
          field: "handleAssignee",
          title: "操作人",
          formatter: ({ cellValue, row, column }) => {
            return this.userOption.find((el) => el.dictValue == cellValue)
              ? this.userOption.find((el) => el.dictValue == cellValue)
                  .dictLabel
              : cellValue;
          },
        },
        {
          field: "handleTime",
          title: "操作时间",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "projectManageTable", //tableId必须项目唯一，用于缓存展示列
      alarmStatusOptions: [],
      alarmTypeOptions: [],
      faultLevelOptions: [],
      detailVisible: false,
      faultInfoList: [],
      faultDictArr: [
        { title: "故障名称", code: "errorName" },
        { title: "推送时间", code: "pushTime" },
        { title: "故障码", code: "faultCode" },
        { title: "告警类型", code: "type" },
        { title: "故障描述", code: "gzms" },
        { title: "是否恢复", code: "recover" },
        { title: "影响", code: "alarmInfluence" },
        { title: "恢复类型", code: "status" },
        { title: "处理建议", code: "alarmSuggestions" },
        { title: "故障次数", code: "triggerCount" },
        { title: "上报报文", code: "sbbw" },
        { title: "告警持续时间", code: "alarmDurationDesc" },
        { title: "告警等级", code: "faultLevel" },
        { title: "告警创建时间", code: "gmtCreate" },
        { title: "告警清除时间", code: "gmtClear" },
        { title: "最新告警时间", code: "gmtLastTrigger" },
        { title: "告警首次触发时间", code: "gjsccfsj" },
        { title: "告警结束时间", code: "gmtEnd" },
      ],
      businessDictArr: [
        { title: "故障名称", code: "errorName" },
        { title: "站点编码", code: "stationCode" },
        { title: "故障详情", code: "bizAlarmDesc" },
        { title: "站点名称", code: "stationName" },
        { title: "推送时间", code: "pushTime" },
        { title: "是否恢复", code: "recover" },
        { title: "详细地址", code: "stationAddr" },
        { title: "恢复类型", code: "status" },
        { title: "告警类型", code: "type" },
        { title: "", code: "" },
      ],
      faultDetailList: [],
      businessDetailList: [],
      isBatchIgnore: false,
      handleResultOptions: [],
      threeOrderTypeOptions: [],
      deptOptionList: [],
    };
  },
  activated() {
    if (this.$route.params) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    this.addForm.urgencyLevel = "0";

    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getDicts("handle_result").then((response) => {
        this.handleResultOptions = response.data;
      }),
      //获取业务类型字典
      this.getDicts("business_type").then((response) => {
        // if (response?.data && response?.data.length > 0) {
        // response.data.forEach((item) => {
        //   if (this.noUseBusinessType.indexOf(item.dictValue) === -1) {
        //     this.businessTypeOption.push(item);
        //   }
        // });
        // }
        this.businessTypeOption = response.data;
      }),
      this.getDicts("fault_level").then((response) => {
        this.faultLevelOptions = response.data;
      }),

      this.getDicts("error_type").then((response) => {
        this.errorTypeOptions = response.data;
      }),

      this.getDicts("alarm_status").then((response) => {
        this.alarmStatusOptions = response.data;
      }),
      this.getDicts("alarm_type").then((response) => {
        this.alarmTypeOptions = response.data;
      }),
      queryGroupList({ pageNum: 1, pageSize: 9999, status: 0 }).then((res) => {
        this.groupOptions = res?.data;
      }),
      this.getOrderTypeOptions(),
      this.getListUser(),
      this.getAreaData(regionData),
      this.getDeptList(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.initConfig();
          this.queryErrorInfoByPage();
        });
      }, 500);
    });
  },
  methods: {
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    orderTypeParentChange(val, level) {
      if (level == 2) {
        this.addForm.orderTypeId = "";
        this.addForm.threeOrderTypeId = "";
        this.threeOrderTypeOptions = [];
        childrenList({ id: val }).then((res) => {
          this.orderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      } else {
        this.addForm.threeOrderTypeId = "";
        childrenList({ id: val }).then((res) => {
          this.threeOrderTypeOptions = res?.data?.map((x) => {
            return { label: x.typeName, value: x.id };
          });
        });
      }
    },
    getOrderTypeOptions() {
      fistLevelList().then((res) => {
        this.orderTypeParentOptions = res?.data?.map((x) => {
          return { label: x.typeName, value: x.id };
        });
      });
    },
    async handleGroupChange(val) {
      console.log("通知组", val);
      if (!val || val?.length === 0) {
        this.groupMemberOptions = [];
      } else {
        const res = await queryGroupMemberList({ groupIds: val });
        this.groupMemberOptions = res?.data;
      }
      const arr = this.groupMemberOptions?.map((x) => x.userId);
      this.addForm.userIds = this.addForm.userIds?.filter(
        (item) => arr.indexOf(item) > -1
      );
    },
    handleDetail(row) {
      this.detailVisible = true;
      this.faultType = row.type;
      queryFaultDetail({ reportId: row.reportId }).then((res) => {
        if (res?.code == "10000") {
          const { alramList, ...rest } = res.data;
          if (this.faultType === "DEVICE") {
            this.faultInfoList = [
              { title: "故障名称", value: alramList[0]?.errorName || "" },
              { title: "设备编码", value: rest?.equipNo || "" },
              { title: "故障码", value: alramList[0]?.faultCode || "" },
              { title: "设备名称", value: rest?.deviceName || "" },
              { title: "故障描述", value: alramList[0]?.alarmDesc || "" },
              { title: "设备类型", value: rest?.equipType || "" },
              { title: "影响", value: alramList[0]?.alarmInfluence || "" },
              { title: "分组", value: rest?.catalogName || "" },
              {
                title: "处理建议",
                value: alramList[0]?.alarmSuggestions || "",
              },
              {
                title: "品牌-型号",
                value:
                  (rest?.pileBrandName || "") +
                  "-" +
                  (rest?.pileModelName || ""),
              },
              { title: "上报报文", value: "" },
              { title: "站点编码", value: alramList[0]?.stationCode || "" },
              { title: "故障次数", value: alramList[0]?.triggerCount || "" },
              { title: "站点名称", value: alramList[0]?.stationName || "" },
              {
                title: "告警持续时间",
                value: alramList[0]?.alarmDurationDesc || "",
              },
              {
                title: "告警等级",
                value:
                  this.faultLevelOptions?.find(
                    (el) => el.dictValue == alramList[0]?.faultLevel
                  )?.dictLabel || alramList[0]?.faultLevel,
              },
              { title: "详细地址", value: alramList[0]?.stationAddr || "" },
              { title: "告警创建时间", value: alramList[0]?.gmtCreate || "" },
              { title: "推送时间", value: alramList[0]?.pushTime || "" },
              {
                title: "最新告警时间",
                value: alramList[0]?.gmtLastTrigger || "",
              },
              { title: "告警类型", value: alramList[0]?.type || "" },
              { title: "告警清除时间", value: alramList[0]?.gmtClear || "" },
              { title: "是否恢复", value: alramList[0]?.recover || "" },
              { title: "告警首次触发时间", value: "" },
              { title: "恢复类型", value: alramList[0]?.status || "" },
              { title: "告警结束时间", value: alramList[0]?.gmtEnd || "" },
            ];
          } else {
            this.faultInfoList = [
              { title: "故障名称", value: alramList[0]?.errorName || "" },
              { title: "站点编码", value: alramList[0]?.stationCode || "" },
              { title: "故障详情", value: alramList[0]?.bizAlarmDesc || "" },
              { title: "站点名称", value: alramList[0]?.stationName || "" },
              { title: "推送时间", value: alramList[0]?.pushTime || "" },
              { title: "是否恢复", value: alramList[0]?.recover || "" },
              { title: "详细地址", value: alramList[0]?.stationAddr || "" },
              { title: "恢复类型", value: alramList[0]?.status || "" },
              { title: "告警类型", value: alramList[0]?.type || "" },
              { title: "", value: "" },
            ];
          }
        }
      });
    },
    handleRules() {
      this.$router.push({
        path: "/errorPush/orderRules",
        query: {},
      });
    },
    handleBlackList() {
      this.$router.push({
        path: "/errorPush/blackList",
        query: {},
      });
    },
    handleBlackListStation() {
      this.$router.push({
        path: "/errorPush/blackListStation",
        query: {},
      });
    },
    checkPermission,
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    closeAddOrderVisible() {
      this.$refs.addForm.resetFields();
      this.addForm = {
        businessTypeId: "",
        orderTypeParentId: "",
        orderTypeId: "",
        threeOrderTypeId: "",
        urgencyLevel: "0",
        orderDesc: "",
        userIds: [],
        groupIds: [],
      };
      this.handRow = {
        stationName: undefined,
        stationAddr: undefined,
        errorName: undefined,
        count: 0,
      };
      this.orderTypeOptions = [];
      this.threeOrderTypeOptions = [];
      this.groupMemberOptions = [];
      this.addOrderVisible = false;
    },
    closeAllAddOrderVisible() {
      this.$refs.addForm.resetFields();
      this.addForm = {
        businessTypeId: "",
        orderTypeParentId: "",
        orderTypeId: "",
        threeOrderTypeId: "",
        urgencyLevel: "0",
        orderDesc: "",
        userIds: [],
        groupIds: [],
      };
      this.handRow = {
        stationName: undefined,
        stationAddr: undefined,
        errorName: undefined,
        count: 0,
      };
      this.orderTypeOptions = [];
      this.threeOrderTypeOptions = [];
      this.groupMemberOptions = [];
      //取消勾选
      this.$refs.gridTable.clearTips();
      this.addAllOrderVisible = false;
    },
    getBusinessTypeName(e) {
      this.addForm.businessTypeName = this.businessTypeOption.find(
        (item) => item.dictValue === e
      ).dictLabel;
    },
    async submitAllForm() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          const vm = this;
          const set = new Set();
          set.add(vm.handRow.projectIds);
          if (set.size > 1) {
            this.$message.error("请选择同一场站的 异常发起工单");
            return;
          }
          let form = { ...vm.addForm };
          form.reportIds = this.handRow.reportIds;
          form.stationCode = this.handRow.stationCode;
          form.stationName = this.handRow.stationName;
          form["businessTypeName"] = this.businessTypeOption?.find(
            (x) => x.dictValue == form.businessTypeId
          )?.dictLabel;
          form["orderTypeParentName"] = this.orderTypeParentOptions?.find(
            (x) => x.value == form.orderTypeParentId
          )?.label;
          form["orderTypeName"] = this.orderTypeOptions?.find(
            (x) => x.value == form.orderTypeId
          )?.label;
          form["threeOrderTypeName"] = this.threeOrderTypeOptions?.find(
            (x) => x.value == form.threeOrderTypeId
          )?.label;
          // let params = {
          //   projectIds: this.handRow.projectIds,
          //   reportIds: this.handRow.reportIds,
          //   businessFlowList
          // };
          const { code, data } = await createErrorOrder(form);
          if (code != 10000) return;
          this.$message.success("转工单成功");
          this.closeAllAddOrderVisible();
          this.queryErrorInfoByPage();
          //取消勾选
          this.$refs.gridTable.clearTips();

          this.reportTrackEvent(ERROR_CLICK_BATCH_CREATE_ORDER);
        }
      });
    },
    //省市区转换
    getRegionText(row) {
      const province =
        this.areaData.find((el) => el.value === row?.province)?.label || "";
      const city =
        this.areaData.find((el) => el.value === row?.city)?.label || "";
      const county =
        this.areaData.find((el) => el.value === row?.county)?.label || "";
      return province + "-" + city + "-" + county;
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {};
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        let regionText = this.getRegionText(tableData[0]);
        this.handRow.stationName = tableData[0].stationName;
        this.handRow.stationCode = tableData[0].stationCode;
        this.handRow.stationAddr = regionText + " " + tableData[0].stationAddr;
        this.handRow.projectIds = tableData.map((v) => v.projectId);
        this.handRow.reportIds = tableData.map((v) => v.reportId);
        this.handRow.stationCodes = tableData.map((v) => v.stationCode);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    //添加工单
    async submitForm() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          const vm = this;
          const set = new Set();
          set.add(vm.handRow.projectIds);
          if (set.size > 1) {
            this.$message.error("请选择同一场站的 异常发起工单");
            return;
          }
          let form = { ...vm.addForm };
          form.reportIds = [this.reportId];
          form.stationCode = this.handRow.stationCode;
          form.stationName = this.handRow.stationName;
          form["businessTypeName"] = this.businessTypeOption?.find(
            (x) => x.dictValue == form.businessTypeId
          )?.dictLabel;
          form["orderTypeParentName"] = this.orderTypeParentOptions?.find(
            (x) => x.value == form.orderTypeParentId
          )?.label;
          form["orderTypeName"] = this.orderTypeOptions?.find(
            (x) => x.value == form.orderTypeId
          )?.label;
          form["threeOrderTypeName"] = this.threeOrderTypeOptions?.find(
            (x) => x.value == form.threeOrderTypeId
          )?.label;
          const { code, data } = await createErrorOrder(form);
          if (code != 10000) return;
          this.$message.success("转工单成功");
          this.closeAddOrderVisible();
          this.queryErrorInfoByPage();

          this.reportTrackEvent(ERROR_CLICK_CREATE_ORDER);
        }
      });
    },
    //获取工单类型列表
    async getUseFlowList(businessType) {
      let params = {
        businessTypeList: businessType,
      };
      const { code, data } = await useFlowListApi(params);
      if (code != 10000) return;
      return data;
    },
    //切换业务类型，查询工单类型
    async changeRelaBizType(item) {
      // this.addForm.businessTypeId = item.businessTypeId;
      // let list = await this.getUseFlowList(item.businessTypeId);
      // this.$set(this.addForm, "useFlowList", list);
    },
    //初始化
    initConfig() {
      // this.
    },
    getAreaData(list) {
      list.forEach((item) => {
        this.areaData.push({ label: item.label, value: item.value });
        if (item?.children) {
          this.getAreaData(item.children);
        }
      });
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryErrorInfoByPage(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryErrorInfoByPage();
    },

    //获取项目列表
    queryErrorInfoByPage() {
      // const args = this._.cloneDeep(params ? params : this.searchForm);
      // args.deviceTypeNo = this.searchForm.errorNo; //设备类型
      //
      // if (this.searchForm.region != undefined) {
      //   args["provinceList"] = [];
      //   args["cityList"] = [];
      //   args["countyList"] = [];
      //   this.searchForm.region.forEach(item => {
      //     if (item[0]) {
      //       args["provinceList"].push(item[0]);
      //     }
      //     if (item[1]) {
      //       args["cityList"].push(item[1]);
      //     }
      //     if (item[2]) {
      //       args["countyList"].push(item[2]);
      //     }
      //   });
      //   delete this.searchForm.region;
      // }
      //

      this.loading = true;

      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      if (Array.isArray(params.pushRangeTime)) {
        params.pushStartTime = params.pushRangeTime[0] + " 00:00:00";
        params.pushEndTime = params.pushRangeTime[1] + " 23:59:59";
        delete params.pushRangeTime;
      }
      if (Array.isArray(params.handleRangeTime)) {
        params.handleStartTime = params.handleRangeTime[0] + " 00:00:00";
        params.handleEndTime = params.handleRangeTime[1] + " 23:59:59";
        delete params.handleRangeTime;
      }

      // this.finallySearch = args;
      queryErrorInfoByPage(params)
        .then((res) => {
          this.loading = false;
          this.deviceTable = res?.data;
          this.deviceTableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleAdd(row) {
      this.$refs.gridTable.clearTips();
      this.handRow = { ...row };
      let regionText = this.getRegionText(row);
      const { stationAddr } = row;
      this.handRow.stationAddr = regionText + " " + stationAddr;
      this.projectId = row.projectId;
      this.reportId = row.reportId;
      const faultLevelText =
        this.faultLevelOptions?.find((el) => el.dictValue == row.faultLevel)
          ?.dictLabel || row.faultLevel;
      this.addForm.orderDesc = `桩名称：${row.deviceName ??
        "暂无"}，桩编号：${row.equipNo ?? "暂无"}，设备类型：${row.equipType ??
        "暂无"}，数据来源：${row.errorFrom ??
        "暂无"}，异常名称：${row.errorName ??
        "暂无"}，故障码：${row.faultCode ??
        "暂无"}，故障等级：${faultLevelText ??
        "暂无"}，告警创建时间：${row.gmtCreate ??
        "暂无"}，告警结束时间：${row.gmtEnd ??
        "暂无"}，告警开始时间：${row.gmtStart ??
        "暂无"}，告警持续时间：${row.alarmDurationDesc ?? "暂无"}`;
      this.addOrderVisible = true;
    },
    closeIgnoreVisible() {
      this.reportId = "";
      this.ignoreForm.ignoreRemark = "";
      this.ignoreVisible = false;
    },
    handleCreate() {
      if (this.handRow.count == 0) {
        this.$message.warning("请勾选异常数据");
        return;
      }
      if (
        this.handRow.stationCodes?.some(
          (item) => item !== this.handRow.stationCodes[0]
        )
      ) {
        this.$message.warning("请选择同一场站的告警");
        return;
      }
      this.addForm.orderDesc = `共计上报${this.handRow.count}个告警。`;
      this.addAllOrderVisible = true;
    },
    ignoreMsg() {
      this.$refs.ignoreForm.validate((valid) => {
        if (valid) {
          this.$confirm("确定忽略该异常吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            if (this.isBatchIgnore) {
              let data = {
                reportIds: this.handRow.reportIds,
                ignoreRemark: this.ignoreForm.ignoreRemark,
              };
              batchIgnore(data).then((res) => {
                if (res?.success) {
                  this.$message.success("忽略成功");
                  this.ignoreForm.ignoreRemark = "";
                  this.ignoreVisible = false;
                  //更新列表
                  this.queryErrorInfoByPage();
                  this.$refs.gridTable.clearTips();

                  this.reportTrackEvent(ERROR_CLICK_BATCH_IGNORE);
                } else {
                  this.ignoreForm.ignoreRemark = "";
                  this.ignoreVisible = false;
                  this.$message.error("更新失败");
                }
              });
            } else {
              let data = {
                reportId: this.reportId,
                faultCodeId: this.faultCodeId,
                ignoreRemark: this.ignoreForm.ignoreRemark,
              };
              ignore(data).then((res) => {
                if (res?.success) {
                  this.$message.success("忽略成功");
                  this.reportId = "";
                  this.faultCodeId = "";
                  this.ignoreForm.ignoreRemark = "";
                  this.ignoreVisible = false;
                  //更新列表
                  this.queryErrorInfoByPage();

                  this.reportTrackEvent(ERROR_CLICK_IGNORE);
                } else {
                  this.reportId = "";
                  this.faultCodeId = "";
                  this.ignoreForm.ignoreRemark = "";
                  this.ignoreVisible = false;
                  this.$message.error("更新失败");
                }
              });
            }
          });
        }
      });
    },
    //设备信息修改
    handleEdit(row) {
      this.reportId = row.reportId;
      this.faultCodeId = row.faultCodeId;
      this.ignoreVisible = true;
      this.isBatchIgnore = false;
    },
    handleIgnore() {
      if (this.handRow?.count == 0) {
        return this.$message.warning("请勾选至少一条数据");
      } else {
        this.ignoreVisible = true;
        this.isBatchIgnore = true;
      }
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryErrorInfoByPage(this.finallySearch);
    },
    //是否能够勾选
    checkMethod({ row }) {
      return row.handleResult == "未处理";
    },
    jumpToOperationWorkOrder(row) {
      this.$router.push({
        path: "/order/operationWorkOrder/list",
        query: { businessNo: row.businessNo },
      });
    },
  },
  computed: {
    config() {
      return [
        {
          key: "errorName",
          title: "故障名称",
          type: "input",
          placeholder: "请输入故障名称",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请输入设备名称",
        },
        {
          key: "handleResult",
          title: "处理结果",
          type: "select",
          placeholder: "请选择处理结果",
          options: this.handleResultOptions,
        },
        {
          key: "pushRangeTime",
          title: "推送时间",
          type: "dateRange",
          placeholder: "请选择推送时间",
        },
        {
          key: "faultCode",
          title: "故障码",
          type: "input",
          placeholder: "请输入故障码",
        },
        {
          key: "stationNo",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "equipNo",
          title: "设备编号",
          type: "input",
          placeholder: "请输入设备编号",
        },
        {
          key: "businessNo",
          title: "工单编号",
          type: "input",
          placeholder: "请输入工单编号",
        },
        {
          key: "handleRangeTime",
          title: "处理时间",
          type: "dateRange",
          placeholder: "请选择处理时间",
        },
        {
          key: "faultLevel",
          title: "告警等级",
          type: "select",
          placeholder: "请选择告警等级",
          options: this.faultLevelOptions,
        },
        {
          key: "status",
          title: "恢复类型",
          type: "select",
          placeholder: "请选择恢复类型",
          options: this.alarmStatusOptions,
        },
        {
          key: "handleAssignee",
          title: "操作人",
          type: "select",
          placeholder: "请选择操作人",
          options: this.userOption,
        },
        {
          key: "region",
          title: "省市区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "type",
          title: "告警类型",
          type: "select",
          placeholder: "请选择告警类型",
          options: this.alarmTypeOptions,
        },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
      ];
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: pre-line;
    // text-overflow: ellipsis;
  }
}
</style>
