// 黑名单用户（用户短信拦截）
<!-- 项目管理 -->
<template>
  <div class="app-container">
    <div class="page-title">
      <h3>用户短信拦截</h3>
      <div class="page-title-tips">
        (指的是该手机号在限制时间范围内，不用给该用户推送低电量进度提醒短信)
      </div>
    </div>
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="deviceTable"
        :batchDelete="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="deviceTableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        row-id="reportId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleAdd"
            v-has-permi="['errorPush:blackUser:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            v-if="row.statusName == '未生效'"
            @click="handleEdit(row)"
            v-has-permi="['errorPush:blackUser:edit']"
          >
            修改
          </el-button>
          <el-button
            @click="handleCancel(row)"
            v-if="row.statusName == '未生效' || row.statusName == '生效中'"
            type="text"
            size="large"
            v-has-permi="['errorPush:blackUser:cancel']"
          >
            解除
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <el-dialog
      title="用户短信拦截配置"
      :visible.sync="addVisible"
      :close-on-click-modal="false"
      @close="closeAddDialog"
      append-to-body
      width="80%"
    >
      <div class="queryParamsWrap">
        <el-form
          :model="item"
          :ref="'addForm_' + index"
          :inline="true"
          v-for="(item, index) in addForm"
          :key="index"
          :rules="rules"
        >
          <el-row style="display: flex;align-items:center">
            <el-col :span="7">
              <el-form-item label="手机号:" prop="mobile">
                <el-input v-model="item.mobile" maxlength="20"> </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="限制日期:" prop="dateRange">
                <el-date-picker
                  v-model="item.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="截止日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="原因:" prop="reason">
                <el-input
                  v-model="item.reason"
                  type="textarea"
                  :rows="2"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="1" :offset="1">
              <el-button
                type="primary"
                circle
                icon="el-icon-plus"
                @click="addItem"
                v-if="index === 0"
                style="margin-bottom: 18px;"
              />
              <el-button
                type="primary"
                circle
                icon="el-icon-minus"
                style="background:red;border:1px solid red;margin-bottom: 18px;"
                @click="removeItem(index)"
                v-else
              />
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeAddDialog">取 消</el-button>
        <el-button type="primary" @click.stop="submitAddForm">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="修改黑名单站点"
      :visible.sync="editVisible"
      :close-on-click-modal="false"
      @close="closeEditDialog"
      append-to-body
      width="50%"
    >
      <div class="queryParamsWrap">
        <el-form
          :model="editForm"
          :inline="true"
          ref="editForm"
          :rules="editRules"
          label-width="110px"
        >
          <el-row>
            <el-form-item label="手机号:">
              <!-- <el-input v-model="editForm.stationName"></el-input> -->
              <span style="font-size: 13px;">{{ editForm.mobile }}</span>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="限制日期:" prop="dateRange">
              <el-date-picker
                v-model="editForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="原因:" prop="reason">
              <el-input
                v-model="editForm.reason"
                type="textarea"
                maxlength="500"
                :rows="5"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeEditDialog">取 消</el-button>
        <el-button type="primary" @click.stop="submitEditForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import AddProject from "@/views/projectManage/addProject.vue";
import { createErrorOrder } from "@/api/operationWorkOrder/index.js";
import {
  queryBlackUser,
  stationList,
  submitAddUser,
  submitEditUser,
  removeBlackUser,
} from "@/api/errorPush/index.js";
import { regionData } from "element-china-area-data";
import { listAllUser } from "@/api/common.js";
import checkPermission from "@/utils/permission.js";

export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
    AddProject,
  },
  data() {
    return {
      stationOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      editForm: { stationName: "", dateRange: [], reason: "" },
      addForm: [{ mobile: "", dateRange: [], reason: "" }],
      rules: {
        mobile: [
          { required: true, message: "请输入手机号", trigger: "change" },
        ],
        dateRange: [
          { required: true, message: "请选择日期", trigger: "change" },
        ],
      },
      editRules: {
        dateRange: [
          { required: true, message: "请选择日期", trigger: "change" },
        ],
      },
      orderTypeParentOptions: [
        { label: "故障工单", value: "故障工单" },
        { label: "抄表工单", value: "抄表工单" },
        { label: "真实充电派单", value: "真实充电派单" },
      ],
      orderTypeOptions: [
        { label: "供电故障", value: "供电故障" },
        { label: "网络故障", value: "网络故障" },
        { label: "电桩故障", value: "电桩故障" },
        { label: "其他故障", value: "其他故障" },
      ],
      userOption: [], //用户列表

      useFlowList: [], //工单类型
      options: [],
      finallySearch: null,
      deviceTable: [],
      deviceTableTotal: 0,
      addProjectDialogVisible: false, //显示增加设备档案弹框
      addVisible: false, //添加站点
      addAllOrderVisible: false, //添加工单
      editVisible: false, //忽略
      projectId: undefined, //项目id
      handRow: {
        stationName: undefined,
        stationAddr: undefined,
        errorName: undefined,
        count: 0,
      }, //项目id
      ignoreRemark: "", // 忽略 原因
      recordId: undefined,
      faultCodeId: undefined,
      deviceNo: "", //设备编号
      deviceTypeList: [], //设备类型列表
      deviceModelList: [], //设备型号列表
      statusDict: [], //泵房状态字典
      pumpHouseList: [], //泵房列表
      pageType: "add",
      status: "0",
      dialogTitle: "新增项目",
      config: [],
      areaData: [],
      columns: [
        {
          field: "mobile",
          title: "用户手机号",
        },
        {
          field: "dateRange",
          title: "限制日期",
        },
        {
          field: "reason",
          title: "原因",
          customWidth: 200,
        },
        {
          field: "statusName",
          title: "状态",
        },

        {
          field: "createTime",
          title: "配置时间",
        },
        {
          field: "createBy",
          title: "配置人",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "projectManageTable", //tableId必须项目唯一，用于缓存展示列
    };
  },
  mounted() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getStationList(),
      this.getAreaData(regionData),
      this.queryData(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    addItem() {
      let validCount = 0;
      this.addForm.map((item, index) => {
        console.log(this.$refs);
        this.$refs["addForm_" + index][0].validate(async (valid) => {
          if (valid) {
            validCount++;
          }
        });
      });
      if (validCount == this.addForm.length) {
        this.addForm.push({ mobile: "", dateRange: [], reason: "" });
      }
    },
    removeItem(index) {
      this.addForm.splice(index, 1);
    },
    handleBlackList() {
      this.$router.push({
        path: "/errorPush/blackList",
        query: {},
      });
    },
    handleBlackListStation() {
      this.$router.push({
        path: "/errorPush/blackListStation",
        query: {},
      });
    },
    handleProcessGroup() {
      this.$router.push({
        path: "/errorPush/setProcessGroup",
        query: {},
      });
    },
    checkPermission,
    //获取用户列表
    async getStationList() {
      const { code, data } = await stationList();
      if (code != 10000) return;
      this.stationOptions = data;
    },
    closeAddDialog() {
      this.addForm = [{ mobile: "", dateRange: [], reason: "" }];
      this.addVisible = false;
    },
    closeAllAddOrderVisible() {
      this.$refs.addForm.resetFields();
      this.addAllOrderVisible = false;
    },

    async submitEditForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          this.$confirm("是否确认保存设置？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              const { id, reason, dateRange, ...rest } = this.editForm;
              const form = {
                id,
                reason,
                startDate: dateRange ? dateRange[0] : "",
                endDate: dateRange ? dateRange[1] : "",
              };
              const res = await submitEditUser(form);
              if (res?.code == 10000) {
                this.$message.success("修改成功");
                this.closeEditDialog();
                this.queryData();
              }
            })
            .catch(() => {});
        }
      });
    },
    //添加站点-提交
    async submitAddForm() {
      let validCount = 0;
      this.addForm.map((item, index) => {
        console.log(this.$refs);
        this.$refs["addForm_" + index][0].validate(async (valid) => {
          if (valid) {
            validCount++;
          }
        });
      });
      if (validCount == this.addForm.length) {
        this.$confirm("是否确认保存设置？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            const form = this.addForm.map((item) => {
              return {
                ...item,
                startDate: item.dateRange ? item.dateRange[0] : "",
                endDate: item.dateRange ? item.dateRange[1] : "",
              };
            });
            const res = await submitAddUser(form);
            if (res?.code == 10000) {
              this.$message.success("新增成功");
              this.closeAddDialog();
              this.queryData();
            }
          })
          .catch(() => {});
      }
    },

    //初始化
    initConfig() {
      this.config = [
        {
          key: "mobile",
          title: "用户手机号",
          type: "input",
          placeholder: "请输入11位手机号",
        },
      ];
    },
    getAreaData(list) {
      list.forEach((item) => {
        this.areaData.push({ label: item.label, value: item.value });
        if (item?.children) {
          this.getAreaData(item.children);
        }
      });
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryData(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };

      // this.finallySearch = args;
      queryBlackUser(params)
        .then((res) => {
          this.loading = false;
          this.deviceTable = res?.data;
          this.deviceTableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleAdd(row) {
      this.handRow = row;
      this.projectId = row.projectId;
      this.reportId = row.reportId;
      this.addVisible = true;
    },
    closeEditDialog() {
      this.reportId = "";
      this.editForm.ignoreRemark = "";
      this.editVisible = false;
    },
    handleCreate() {
      if (this.handRow.count == 0) {
        this.$message.warning("请勾选异常数据");
        return;
      }
      this.addAllOrderVisible = true;
    },
    //解除
    handleCancel(row) {
      this.$confirm("确定解除该站点的黑名单限制吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        removeBlackUser({ id: row.id }).then((res) => {
          if (res?.success) {
            this.$message.success("解除成功");
            //更新列表
            this.queryData();
          }
        });
      });
    },
    //设备信息修改
    handleEdit(row) {
      this.editForm = { ...row };
      this.editForm.dateRange = [
        this.editForm.startDate,
        this.editForm.endDate,
      ];
      this.editVisible = true;
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.page-title {
  display: flex;
  align-items: center;
  &-tips {
    color: gray;
    font-size: 12px;
    margin-left: 20px;
  }
}
/deep/ .el-date-editor .el-range-separator {
  width: 8%;
}
</style>
