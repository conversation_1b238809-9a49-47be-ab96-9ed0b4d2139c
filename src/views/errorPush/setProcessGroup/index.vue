// 黑名单站点
<!-- 项目管理 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        row-id="reportId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleAdd('add')"
            v-has-permi="['operation:progressGroup:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <!-- v-if="checkPermission(['errorPush:ignore'])" -->
          <el-button
            type="text"
            size="large"
            @click="handleAdd('edit', row)"
            v-has-permi="['operation:progressGroup:edit']"
          >
            修改
          </el-button>
          <el-button
            @click="handleLog(row)"
            type="text"
            size="large"
            v-has-permi="['operation:progressGroup:log']"
          >
            日志
          </el-button>
        </template>
        <template slot="groupMember" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="handleMemberView(row)"
            v-has-permi="['operation:progressGroup:member']"
            >查看</el-button
          >
        </template>
        <template slot="status" slot-scope="{ row }"
          ><el-switch
            v-model="row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(row)"
          >
          </el-switch>
        </template>
      </GridTable>
    </el-card>
    <UpdateModal ref="updateModal" @submit="queryData"></UpdateModal>
    <MemberModal ref="memberModal"></MemberModal>
    <el-dialog
      title="操作日志"
      :visible.sync="logVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="70%"
    >
      <Timeline :list="recordList"></Timeline>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import Timeline from "@/components/Timeline/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryGroupList,
  changeStatus,
  queryLogList,
} from "@/api/errorPush/index.js";

import checkPermission from "@/utils/permission.js";
import UpdateModal from "./components/updateModal.vue";
import MemberModal from "./components/memberModal.vue";
export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
    UpdateModal,
    MemberModal,
    Timeline,
  },
  data() {
    return {
      logVisible: false,
      groupStatusOptions: [
        // { label: "全部", value: "all" },
        { label: "停用", value: 1 },
        { label: "启用", value: 0 },
      ],
      stationOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      editForm: { stationName: "", dateRange: [], reason: "" },
      addForm: [{ stationId: "", dateRange: [], reason: "" }],

      options: [],
      finallySearch: null,
      tableData: [],
      tableTotal: 0,
      addVisible: false, //添加站点
      editVisible: false, //忽略

      config: [],
      columns: [
        {
          field: "groupName",
          title: "分组名称",
        },
        {
          field: "groupType",
          title: "分组类别",
          formatter: ({ cellValue }) => {
            switch (cellValue) {
              case 1:
                return "手动组";
              case 0:
                return "自动组";
              default:
                return "";
            }
          },
        },
        {
          field: "groupMember",
          title: "组内人员",
          slots: { default: "groupMember" },
        },
        {
          field: "status",
          title: "状态",
          slots: { default: "status" },
        },
        {
          field: "operateTime",
          title: "配置时间",
        },

        {
          field: "operatorName",
          title: "配置人",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "processGroupTable", //tableId必须项目唯一，用于缓存展示列
      recordList: [],
    };
  },
  mounted() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([this.queryData()]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    //状态切换
    handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";
      changeStatus({ status: row.status, groupId: row.groupId })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
          } else {
            row.status = row.status == 0 ? 1 : 0;
          }
        })
        .catch(function() {
          row.status = row.status == 0 ? 1 : 0;
        });
    },
    //修改分组-提交
    async submitEditForm() {
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          const { id, reason, dateRange, ...rest } = this.editForm;
          const form = {
            id,
            reason,
            startDate: dateRange ? dateRange[0] : "",
            endDate: dateRange ? dateRange[1] : "",
          };
          const res = await submitEditStation(form);
          if (res?.code == 10000) {
            this.$message.success("修改成功");
            this.closeEditDialog();
            this.queryData();
          }
        }
      });
    },

    //初始化
    initConfig() {
      this.config = [
        {
          key: "groupName",
          title: "分组名称",
          type: "input",
          placeholder: "分组名称",
        },
        {
          key: "status",
          title: "分组状态",
          type: "select",
          placeholder: "分组状态",
          options: this.groupStatusOptions,
          optionLabel: "label",
          optionValue: "value",
        },
      ];
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryData(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };

      // this.finallySearch = args;
      queryGroupList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
      // this.tableData = [
      //   {
      //     groupName: "名称1",
      //     groupType: "手动组",
      //     groupMember: "1",
      //     status: "0",
      //     configTime: "2023-10-10 10:12:32",
      //     createBy: "张三",
      //   },
      // ];
      // this.loading = false;
    },
    //新增/修改
    handleAdd(type, row) {
      this.$refs.updateModal.openDialog(type, row);
    },
    //日志
    handleLog(row) {
      this.logVisible = true;
      queryLogList({ groupId: row.groupId }).then((res) => {
        this.recordList = res.data;
      });
    },

    //查看组内人员
    handleMemberView(row) {
      this.$refs.memberModal.openDialog(row);
    },
    closeEditDialog() {
      this.editVisible = false;
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
    closeDialog() {
      this.logVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
