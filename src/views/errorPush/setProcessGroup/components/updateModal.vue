<template>
  <el-dialog
    :title="title"
    :visible.sync="addVisible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="70%"
  >
    <div class="queryParamsWrap">
      <el-form
        :model="editForm"
        :inline="true"
        ref="editForm"
        :rules="editRules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="设置组名称:" prop="groupName">
              <el-input
                v-model="editForm.groupName"
                placeholder="请输入名称"
                size="mini"
                style="width:80%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分组类别:" prop="groupType">
              <div style="display: flex;align-items: center;">
                <el-radio-group v-model="editForm.groupType">
                  <el-radio :label="1">手动组</el-radio>
                  <el-radio :label="0">自动组</el-radio>
                </el-radio-group>
                <span style="color: #999">（自动组只能设置一个）</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <TransferTree
        :cascadeData="cascadeData"
        v-model="checkedData"
        ref="transferTree"
      ></TransferTree>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="handleSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
// const cascadeData = [
//   {
//     ancestors: "0",
//     createBy: "SA",
//     createTime: "2023-10-01 00:00:01",
//     delFlag: "0",
//     deptId: "10000",
//     deptName: "江南水务",
//     leader: "江南水务",
//     orderNum: 1,
//     orgType: "company",
//     parentId: "0",
//     phone: "",
//     status: "0",
//     tenantId: "10000",
//     updateBy: "10000",
//     updateTime: "2023-12-29 09:51:18",
//     children: [
//       {
//         avatar:
//           "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/system/10000/db4f9e2dc427c4b8c6f16e83c06a3de9.html",
//         createBy: "系统管理员",
//         createTime: "2023-09-27 15:32:27",
//         createUser: "10035",
//         createUserTime: "2024-01-31 16:21:10",
//         delFlag: "0",
//         deptId: "10000",
//         email: "<EMAIL>",
//         lockStatus: "0",
//         loginDate: "2024-02-27 14:09:13",
//         loginIp: "*************, ************",
//         nickName: "系统管理员",
//         password:
//           "$2a$10$u3mo5p0SUa485VTmdgdcZuq9H/z/2VYg6h.4DGuQwRUQTrKi/6cym",
//         passwordChangeDate: "2023-10-01 00:00:01",
//         phonenumber: "15662127651",
//         sex: 0,
//         status: "0",
//         tenantId: "10000",
//         updateBy: "系统管理员",
//         updateTime: "2024-02-27 14:09:13",
//         userId: "10000",
//         userName: "jnswAdmin",
//         userType: 0,
//       },

//       {
//         ancestors: "0,10000",
//         createBy: "系统管理员2",
//         createTime: "2023-12-04 21:55:55",
//         delFlag: "0",
//         deptId: "10008",
//         deptName: "公司1",
//         leader: "yan",
//         orderNum: 1,
//         orgType: "company",
//         parentId: "10000",
//         status: "0",
//         tenantId: "10000",
//         updateBy: "10000",
//         updateTime: "2023-12-29 09:51:18",
//         children: [
//           {
//             createBy: "系统管理员",
//             createTime: "2024-01-18 14:57:04",
//             createUser: "10000",
//             createUserTime: "2024-02-27 10:26:57",
//             delFlag: "0",
//             deptId: "10008",
//             email: "<EMAIL>",
//             loginDate: "2024-02-27 13:49:33",
//             loginIp: "*************, ************",
//             nickName: "张引测试 ",
//             password:
//               "$2a$10$JnRBodgbOnSaCVbMLYxW2uwpBXwdkbpAcSbpZ91DFHNDEaKNA9uGi",
//             passwordChangeDate: "2024-02-05 15:54:07",
//             phonenumber: "15523241147",
//             sex: 0,
//             status: "0",
//             tenantId: "10000",
//             updateBy: "系统管理员",
//             updateTime: "2024-02-27 13:49:33",
//             userId: "10035",
//             userName: "zy001",
//           },
//           {
//             createBy: "系统管理员",
//             createTime: "2024-01-18 14:57:04",
//             createUser: "10000",
//             createUserTime: "2024-02-27 10:26:57",
//             delFlag: "0",
//             deptId: "10008",
//             email: "<EMAIL>",
//             loginDate: "2024-02-27 13:49:33",
//             loginIp: "*************, ************",
//             nickName: "张引测试222 ",
//             password:
//               "$2a$10$JnRBodgbOnSaCVbMLYxW2uwpBXwdkbpAcSbpZ91DFHNDEaKNA9uGi",
//             passwordChangeDate: "2024-02-05 15:54:07",
//             phonenumber: "15523241147",
//             sex: 0,
//             status: "0",
//             tenantId: "10000",
//             updateBy: "系统管理员4",
//             updateTime: "2024-02-27 13:49:33",
//             userId: "10036",
//             userName: "zy001",
//           },
//         ],
//       },
//     ],
//   },
//   {
//     ancestors: "0",
//     createBy: "SA",
//     createTime: "2023-10-01 00:00:01",
//     delFlag: "0",
//     deptId: "100001",
//     deptName: "江南水务1",
//     leader: "江南水务1",
//     orderNum: 1,
//     orgType: "company",
//     parentId: "0",
//     phone: "",
//     status: "0",
//     tenantId: "10000",
//     updateBy: "10000",
//     updateTime: "2023-12-29 09:51:18",
//     children: [
//       {
//         avatar:
//           "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com/system/10000/db4f9e2dc427c4b8c6f16e83c06a3de9.html",
//         createBy: "系统管理员",
//         createTime: "2023-09-27 15:32:27",
//         createUser: "10035",
//         createUserTime: "2024-01-31 16:21:10",
//         delFlag: "0",
//         deptId: "100001",
//         email: "<EMAIL>",
//         lockStatus: "0",
//         loginDate: "2024-02-27 14:09:13",
//         loginIp: "*************, ************",
//         nickName: "系统管理员3",
//         password:
//           "$2a$10$u3mo5p0SUa485VTmdgdcZuq9H/z/2VYg6h.4DGuQwRUQTrKi/6cym",
//         passwordChangeDate: "2023-10-01 00:00:01",
//         phonenumber: "15662127651",
//         sex: 0,
//         status: "0",
//         tenantId: "10000",
//         updateBy: "系统管理员",
//         updateTime: "2024-02-27 14:09:13",
//         userId: "100002",
//         userName: "jnswAdmin",
//         userType: 0,
//       },
//     ],
//   },
// ];
import TransferTree from "@/components/TransferTree/index2.vue";
import {
  queryTreeList,
  queryGroupUser,
  submitEdit,
  submitAdd,
} from "@/api/errorPush/index.js";
import { MAINTENANCE_ORDER_SET_GROUP } from "@/utils/track/track-event-constants";

export default {
  components: { TransferTree },
  data() {
    return {
      title: "新增组",
      updateType: "add",
      checkedData: [],
      addVisible: false,
      editForm: { groupName: "", groupType: "" },
      editRules: {
        groupName: [
          { required: true, message: "请输入名称", trigger: "change" },
        ],
        groupType: [
          { required: true, message: "请选择类别", trigger: "change" },
        ],
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      cascadeData: [],
      groupId: "",
    };
  },
  async created() {
    await this.getTreeList();
  },
  methods: {
    //获取所有组织-用户树形结构
    async getTreeList() {
      const res = await queryTreeList({});
      if (res) {
        this.cascadeData = res.data;
        this.traverseArr(this.cascadeData);
      }
    },
    traverseArr(arr) {
      arr.forEach((obj) => {
        // 添加id和label
        if (obj.userId) {
          obj.id = obj.userId;
          obj.label = obj.nickName;
          obj.pid = "-" + obj.deptId;
        } else if (obj.deptId) {
          obj.id = "-" + obj.deptId;
          obj.label = obj.deptName;
          obj.pid = "-" + obj.parentId;
          obj["children"] = obj.childrenList;
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children);
        }
      });
    },
    async openDialog(type, row) {
      this.addVisible = true;
      this.updateType = type;
      if (type === "add") {
        this.title = "新增组";
        this.checkedData = [];
        this.editForm = { groupName: "", groupType: "" };
      } else {
        this.title = "修改组";
        this.editForm = row;
        this.groupId = row.groupId;
        //获取已绑定的用户
        const res = await queryGroupUser({ groupId: row.groupId });
        if (res) {
          this.checkedData = res.data;
          this.traverseArr(this.checkedData);
        }
      }
      this.$nextTick(() => {
        this.$refs.transferTree.getDefaultLeftData();
      });
    },
    closeDialog() {
      this.addVisible = false;
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    //提交
    async handleSubmit() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        let arr = this.flattenArray(this.checkedData);
        console.log(this.checkedData, arr, "---arr");
        let userIds = [];
        arr.map((x) => {
          if (x.hasOwnProperty("userId")) {
            userIds.push(x.userId);
          }
        });
        const params = { ...this.editForm, userIds: userIds };
        if (this.updateType === "add") {
          const res = await submitAdd(params);
          if (res?.code == 10000) {
            this.$message.success("新增成功");
            this.closeDialog();
            this.$emit("submit");

            this.reportTrackEvent(MAINTENANCE_ORDER_SET_GROUP, {
              operateType: "新增"
            });
          }
        } else {
          const res = await submitEdit({ ...params, groupId: this.groupId });
          if (res?.code == 10000) {
            this.$message.success("修改成功");
            this.closeDialog();
            this.$emit("submit");

            this.reportTrackEvent(MAINTENANCE_ORDER_SET_GROUP, {
              operateType: "修改"
            });
          }
        }
      });
      //   let validCount = 0;
      //   this.addForm.map((item, index) => {
      //     console.log(this.$refs);
      //     this.$refs["addForm_" + index][0].validate(async (valid) => {
      //       if (valid) {
      //         validCount++;
      //       }
      //     });
      //   });
      //   if (validCount == this.addForm.length) {
      //     console.log(this.addForm);
      //     const form = this.addForm.map((item) => {
      //       return {
      //         ...item,
      //         startDate: item.dateRange ? item.dateRange[0] : "",
      //         endDate: item.dateRange ? item.dateRange[1] : "",
      //       };
      //     });
      //     const res = await submitAddStation(form);
      //     if (res?.code == 10000) {
      //       this.$message.success("新增成功");
      //       this.closeAddDialog();
      //       this.queryData();
      //     }
      //   }
    },
  },
};
</script>

<style></style>
