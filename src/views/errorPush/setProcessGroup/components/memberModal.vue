<template>
  <el-dialog
    title="查看组内成员"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="70%"
  >
    <div class="app-container">
      <AdvancedForm
        :config="config"
        :queryParams="searchForm"
        ref="AdvancedForm"
        @confirm="handleQuery"
        @resetQuery="resetQuery"
        v-if="config.length"
      >
      </AdvancedForm>

      <el-card>
        <GridTable
          ref="gridTable"
          :columns="columns"
          :tableData="tableData"
          :batchDelete="true"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="tableTotal"
          @changePage="changePage"
          :loading="loading"
          :tableId="tableId"
          row-id="reportId"
        >
        </GridTable>
      </el-card>
    </div>
  </el-dialog>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { queryGroupMemberList } from "@/api/errorPush/index.js";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      loading: false,
      visible: false,
      config: [],
      groupId: "",
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      tableTotal: 0,
      columns: [
        {
          type: "seq",
          title: "序号",
        },
        {
          field: "nickName",
          title: "用户昵称",
        },
        {
          field: "phonenumber",
          title: "用户手机号",
        },
      ],
      tableId: "processGroupMemberTable", //tableId必须项目唯一，用于缓存展示列
    };
  },
  mounted() {
    // this.initConfig();
    // this.queryData();
  },
  methods: {
    openDialog(row) {
      this.visible = true;
      this.groupId = row.groupId;
      this.initConfig();
      this.queryData();
    },
    closeDialog() {
      this.visible = false;
      this.resetQuery();
    },
    //初始化
    initConfig() {
      this.config = [
        {
          key: "searchParam",
          title: "请输入用户昵称或手机号搜索",
          type: "input",
          placeholder: "请输入用户昵称或手机号搜索",
          colNum: 8,
        },
      ];
    }, //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
        groupIds: [this.groupId],
      };

      // this.finallySearch = args;
      queryGroupMemberList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryData(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },
  },
};
</script>

<style></style>
