//站点清分规则
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      @handleExport="handleExport"
      :showExportButton="checkPermission(['sortingRules:list:export'])"
    >
      <!-- showExportButton -->
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="false"
        @handleSelectionChange="tableSelect"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCreate"
            v-has-permi="['sortingRules:list:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            v-for="(item, index) in btnArr"
            :key="index"
            type="text"
            size="large"
            @click="item.clickFn(row)"
            v-has-permi="[item.permission]"
            v-show="item.show(row)"
          >
            {{ item.title }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <el-dialog
      title="确认需求单"
      :visible.sync="visible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="confirmForm" ref="confirmForm" label-width="110px">
        <el-form-item label="原因" prop="verifyRemark">
          <el-input
            v-model="confirmForm.verifyRemark"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内。"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="submitConfirm(false)">不通过</el-button>
        <el-button
          :loading="confirmLoading"
          @click.stop="submitConfirm(true)"
          type="primary"
          >通过
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import { mapGetters } from "vuex";
import {
  queryRuleList,
  deleteApply,
  exportExcel,
  submitConfirmResult,
} from "@/api/sortingRules/index.js";
import { getToken } from "@/utils/auth";
import moment from "moment";
export default {
  name: "sortingRules",
  components: { AdvancedForm, GridTable },
  data() {
    return {
      confirmLoading: false,
      visible: false,
      confirmForm: {
        verifyRemark: "",
      },
      // config: [],
      columns: [
        // {
        //   type: "checkbox",
        //   customWidth: 60,
        // },
        {
          field: "applyNo",
          title: "申请单号",
        },
        {
          field: "contractNo",
          title: "合同编号",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "businessLicenseName",
          title: "营业执照名称",
        },
        {
          field: "ratepayerNumber",
          title: "纳税人识别号",
        },
        {
          field: "bankNo",
          title: "银行卡号",
        },
        {
          field: "bankCardNumber",
          title: "银行卡联行号",
        },
        {
          field: "commissionCommissionFlag",
          title: "渠道是否抽成",
          formatter: ({ cellValue }) => {
            return cellValue == "1"
              ? "是"
              : cellValue == "0"
              ? "否"
              : cellValue;
          },
        },
        {
          field: "commissionRate",
          title: "渠道抽成比例",
          formatter: ({ cellValue }) => {
            return cellValue || cellValue == 0 ? cellValue + "%" : "--";
          },
        },
        {
          field: "settlementInstruction",
          title: "结算说明",
          customWidth: 200,
        },
        {
          field: "platform",
          title: "平台",
        },
        {
          field: "clearingRule",
          title: "清分规则",
          customWidth: 200,
        },
        {
          field: "status",
          title: "申请单状态",
          formatter: ({ cellValue }) => {
            return this.applyStatusOpts?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
        },
        {
          field: "createBy",
          title: "提交人",
        },
        {
          field: "createTime",
          title: "提交时间",
        },
        {
          field: "verifyBy",
          title: "确认人",
        },
        {
          field: "verifyTime",
          title: "确认时间",
        },
        {
          field: "verifyRemark",
          title: "确认备注",
          customWidth: 200,
        },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "sortingRulesList",
      handRow: {
        stationIds: [],
      },
      //操作列 更多下按钮
      btnArr: [
        {
          title: "编辑",
          clickFn: (row) => {
            this.handleCreate(row);
          },
          permission: "sortingRules:list:edit",
          show: (row) => {
            return ["1", "3"].includes(row.status) && this.isCreateRole;
          },
        },
        {
          title: "确认",
          clickFn: (row) => {
            this.handleConfirm(row);
          },
          permission: "sortingRules:list:confirm",
          show: (row) => {
            return (
              ["1", "3"].includes(row.status) &&
              (this.isImplementRole || this.isSettlementRole)
            );
          },
        },
        {
          title: "删除",
          clickFn: (row) => {
            this.handleDelete(row);
          },
          permission: "sortingRules:list:delete",
          show: (row) => {
            return ["1"].includes(row.status) && this.isCreateRole;
          },
        },
      ],
      token: "",
      applyStatusOpts: [],
      applyId: "",
    };
  },
  created() {
    this.token = getToken();
  },
  activated() {
    // if (this.$route.params) {
    //   this.searchForm = { ...this.searchForm, ...this.$route.params };
    // }
    Promise.all([
      this.getDicts("apply_status").then((response) => {
        this.applyStatusOpts = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    handleCreate(row) {
      this.$router.push({
        path: "/sortingRules/addRules",
        query: { applyId: row.applyId },
      });
    },
    async submitConfirm(isApproved) {
      if (!isApproved && this.confirmForm.verifyRemark?.length === 0) {
        this.$message.error("审核不通过原因不能为空！");
        return;
      }
      const params = {
        applyId: this.applyId,
        status: isApproved ? "2" : "3",
        verifyRemark: this.confirmForm?.verifyRemark,
      };
      this.confirmLoading = true;
      const res = await submitConfirmResult(params);
      this.confirmLoading = false;
      if (res?.code === "10000") {
        this.$message.success("确认成功！");
        this.closeDialog();
        this.getList();
      }
    },
    closeDialog() {
      this.visible = false;
    },
    handleDelete(row) {
      this.$confirm("确认删除该申请单吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          applyId: row.applyId,
        };
        deleteApply(params).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    handleConfirm(row) {
      this.visible = true;
      this.applyId = row.applyId;
      this.confirmForm = { verifyRemark: "" };
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startCreateTime = params.rangeTime[0] + " 00:00:00";
        params.endCreateTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      if (Array.isArray(params.rangeTime2)) {
        params.startVerifyTime = params.rangeTime2[0] + " 00:00:00";
        params.endVerifyTime = params.rangeTime2[1] + " 23:59:59";
        delete params.rangeTime2;
      }
      console.log("查询", params);
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startCreateTime = params.rangeTime[0] + " 00:00:00";
        params.endCreateTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      if (Array.isArray(params.rangeTime2)) {
        params.startVerifyTime = params.rangeTime2[0] + " 00:00:00";
        params.endVerifyTime = params.rangeTime2[1] + " 23:59:59";
        delete params.rangeTime2;
      }
      console.log("查询", params);

      queryRuleList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    //需求提交者
    isCreateRole() {
      return !this.isImplementRole && !this.isSettlementRole;
    },
    //实施角色
    isImplementRole() {
      console.log(this.roles, "----roles");
      return this.roles?.includes("implement");
    },
    //结算角色
    isSettlementRole() {
      return this.roles?.includes("settlement");
    },
    config() {
      return [
        {
          key: "applyNo",
          title: "申请单号",
          type: "input",
          placeholder: "请输入申请单号",
        },
        {
          key: "createBy",
          title: "提交人",
          type: "input",
          placeholder: "请输入提交人",
        },
        {
          key: "contractNo",
          title: "合同编号",
          type: "input",
          placeholder: "请输入合同编号",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "rangeTime",
          title: "提交时间",
          type: "dateRange",
          placeholder: "请选择提交时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "status",
          title: "申请状态",
          type: "select",
          placeholder: "请选择申请状态",
          options: this.applyStatusOpts,
        },
        {
          key: "verifyBy",
          title: "确认人",
          type: "input",
          placeholder: "请输入确认人",
        },
        {
          key: "businessLicenseName",
          title: "营业执照名称",
          type: "input",
          placeholder: "请输入营业执照名称",
        },
        {
          key: "ratepayerNumber",
          title: "纳税人识别号",
          type: "input",
          placeholder: "请输入纳税人识别号",
        },
        {
          key: "rangeTime2",
          title: "确认时间",
          type: "dateRange",
          placeholder: "请选择确认时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "commissionCommissionFlag",
          title: "渠道是否抽成",
          type: "select",
          options: [
            { dictLabel: "是", dictValue: "1" },
            { dictLabel: "否", dictValue: "0" },
          ],
          placeholder: "请选择渠道是否抽成",
        },
      ];
    },
  },
};
</script>

<style></style>
