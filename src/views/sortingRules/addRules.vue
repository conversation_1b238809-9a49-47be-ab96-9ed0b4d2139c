<template>
  <div class="app-container">
    <h3>{{ typeTitle }}场站清分申请</h3>
    <el-form
      :model="basicForm"
      ref="basicForm"
      label-width="110px"
      :rules="basicRules"
    >
      <el-card>
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>基础信息</span>
        </div>
        <el-form-item label="合同编号" prop="contractNo">
          <el-input
            v-model="basicForm.contractNo"
            placeholder="请输入运管已归档的合同编号"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="营业执照名称" prop="businessLicenseName">
          <el-input
            v-model="basicForm.businessLicenseName"
            placeholder="请输入营业执照中的企业名称"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="站点名称" prop="stationCode">
          <el-select
            v-model="basicForm.stationCode"
            placeholder="请选择或模糊搜索站点名称"
            filterable
            style="width: 100%"
            @change="stationChange"
            clearable
          >
            <el-option
              v-for="item in stationOptions"
              :key="item.stationCode"
              :label="item.stationCode + ' - ' + item.stationName"
              :value="item.stationCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="纳税识别号" prop="ratepayerNumber">
          <el-input
            v-model="basicForm.ratepayerNumber"
            placeholder="请输入营业执照中的统一社会信用代码"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="银行卡号" prop="bankNo">
          <el-input
            v-model="basicForm.bankNo"
            placeholder="请输入银行卡号"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="银行卡联行号" prop="bankCardNumber">
          <el-input
            v-model="basicForm.bankCardNumber"
            placeholder="请输入银行卡联行号"
            clearable
          >
          </el-input>
        </el-form-item>
      </el-card>
      <el-card>
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>结算信息</span>
        </div>
        <el-form-item label="渠道是否抽成" prop="commissionCommissionFlag">
          <el-select
            v-model="basicForm.commissionCommissionFlag"
            placeholder="请选择渠道是否抽成"
            style="width: 100%;"
            clearable
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道抽成比例" prop="commissionRate">
          <el-input-number
            v-model="basicForm.commissionRate"
            placeholder="请输入渠道抽成比例"
            controls-position="right"
            style="width: 95%;"
            :min="0"
            :max="99.99"
            :precision="2"
            clearable
          >
          </el-input-number>
          <span style="display: inline-block;width: 4%;text-align: center;"
            >%</span
          >
        </el-form-item>
        <el-form-item label="结算详细说明" prop="settlementInstruction">
          <el-input
            v-model="basicForm.settlementInstruction"
            type="textarea"
            maxlength="500"
            show-word-limit
            :rows="5"
            clearable
            placeholder="抽成计费方式、支付手续费比例等需要详细在此说明"
          >
          </el-input>
        </el-form-item>
      </el-card>
      <el-card>
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>清分信息</span>
        </div>
        <el-form-item label="平台" prop="platform">
          <el-input
            v-model="basicForm.platform"
            placeholder="如“能源投资”"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="清分规则" prop="clearingRule">
          <el-input
            v-model="basicForm.clearingRule"
            type="textarea"
            maxlength="500"
            show-word-limit
            :rows="5"
            placeholder="请详细说明清分规则需要配置的参数值"
            clearable
          >
          </el-input>
        </el-form-item>
      </el-card>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeVisible" size="medium" :loading="loading"
        >取 消</el-button
      >
      <el-button
        @click="handleSubmit"
        type="primary"
        size="medium"
        :loading="loading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import { stationList } from "@/api/workOrderWorkbench";
import { queryDetail, submitForm } from "@/api/sortingRules/index.js";
export default {
  data() {
    return {
      typeTitle: "新增",
      applyId: "",
      basicForm: {
        contractNo: undefined,
        businessLicenseName: undefined,
        stationCode: undefined,
        ratepayerNumber: undefined,
        bankNo: undefined,
        bankCardNumber: undefined,
        commissionCommissionFlag: undefined,
        commissionRate: undefined,
        settlementInstruction: undefined,
        platform: undefined,
        clearingRule: undefined,
      },
      stationOptions: [],
      loading: false,
    };
  },
  created() {
    this.applyId = this.$route.query.applyId || "";
    if (this.applyId) {
      this.typeTitle = "修改";
      this.getDetail();
    }
    this.getStationList();
  },
  computed: {
    basicRules() {
      return {
        contractNo: [
          { required: true, message: "请输入合同编号", trigger: "blur" },
        ],
        businessLicenseName: [
          { required: true, message: "请输入营业执照名称", trigger: "blur" },
        ],
        stationCode: [
          { required: true, message: "请选择站点名称", trigger: "blur" },
        ],
        ratepayerNumber: [
          { required: true, message: "请输入纳税识别号", trigger: "blur" },
        ],
        bankNo: [
          { required: true, message: "请输入银行卡号", trigger: "blur" },
        ],
        bankCardNumber: [
          { required: true, message: "请输入银行卡联行号", trigger: "blur" },
        ],
        commissionCommissionFlag: [
          { required: true, message: "请输入渠道是否抽成", trigger: "blur" },
        ],
        commissionRate: [
          {
            required: this.basicForm.commissionCommissionFlag === "1",
            message: "请输入渠道抽成比例",
            trigger: "blur",
          },
        ],
        platform: [{ required: true, message: "请输入平台", trigger: "blur" }],
        clearingRule: [
          { required: true, message: "请输入清分规则", trigger: "blur" },
        ],
      };
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.basicForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        console.log(this.basicForm, "basicform");
        const params = {
          ...this.basicForm,
          commissionRate: this.basicForm.commissionRate ?? "",
          applyId: this.applyId,
          stationName: this.stationOptions?.find(
            (x) => x.stationCode === this.basicForm.stationCode
          )?.stationName,
        };
        console.log(params, "params");
        this.loading = true;
        const res = await submitForm(params);
        this.loading = false;
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.$router.push({
            path: "/operationManage/sortingRules",
          });
        }
      });
    },
    async getStationList() {
      const res = await stationList();
      this.stationOptions = res?.data;
    },
    getDetail() {
      queryDetail({ applyId: this.applyId }).then((res) => {
        if (res?.code === "10000") {
          this.basicForm = { ...this.basicForm, ...res.data };
        }
      });
    },
    stationChange(val) {
      if (val) {
        queryDetail({ stationCode: val }).then((res) => {
          if (res?.code === "10000") {
            const { contractNo, ...rest } = res.data;
            this.basicForm = { ...this.basicForm, ...rest };
          }
        });
      }
    },
    closeVisible() {
      this.$router.push({
        path: "/operationManage/sortingRules",
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
.el-card.is-always-shadow {
  margin-bottom: 10px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}
</style>
