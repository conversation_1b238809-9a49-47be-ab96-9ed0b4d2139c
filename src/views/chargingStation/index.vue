// 充电桩
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['chargingStation:list:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="pileId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleChargingGun"
            v-has-permi="['chargingStation:info:gun']"
          >
            充电枪
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['chargingStation:list:detail']"
          >
            详情
          </el-button>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showDetail(row)">
            {{ row.pileNo }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryStationList,
  exportStationExcel,
  queryBrandOpts,
  queryModelOpts,
  queryChargingDict,
} from "@/api/chargingStation/index.js";
import checkPermission from "@/utils/permission.js";
import { regionData } from "element-china-area-data";
import { getToken } from "@/utils/auth";
import { DATA_INFO_CLICK_CHARING_STATION_REPORT } from "@/utils/track/track-event-constants";

export default {
  name: "chargingStation",
  components: { AdvancedForm, GridTable },
  data() {
    return {
      // config: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "pileNo",
          title: "充电桩编号",
          slots: { default: "jump" },
        },
        {
          field: "pileName",
          title: "充电桩名称",
        },
        {
          field: "subTypeName",
          title: "设备类型",
        },
        {
          field: "chargingGunCount",
          title: "充电枪数量",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "provinceInfo",
          title: "省市区",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 170,
        },
        {
          field: "operDate",
          title: "桩投运日期",
        },
        {
          field: "assetCode",
          title: "资产编号",
        },
        {
          field: "brandName",
          title: "设备品牌",
        },
        {
          field: "modelName",
          title: "设备型号",
        },
        {
          field: "pendingFault",
          title: "待处理故障数",
        },
        {
          field: "faultCount",
          title: "累计故障总数",
        },
        {
          field: "chargingOrderCount",
          title: "充电订单数",
        },
        {
          field: "stationNo",
          title: "站点编码",
        },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "chargingStationList",
      recordList: [],
      deviceModelList: [],
      handRow: {
        stationIds: [],
      },
      modelOptions: [],
      brandOptions: [],
      subTypeOptions: [],
      token: "",
    };
  },
  created() {
    this.token = getToken();
  },
  mounted() {
    // if (this.$route.params) {
    //   this.searchForm = { ...this.searchForm, ...this.$route.params };
    // }
    // Promise.all([
    //   this.querySubTypeOpts(),
    //   this.queryModelOpts(),
    //   this.queryBrandOpts(),
    // ]).then(() => {
    //   setTimeout(() => {
    //     this.$nextTick(() => {
    //       this.getList();
    //       this.initConfig();
    //     });
    //   }, 500);
    // });
  },
  activated() {
    if (this.$route.params) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    Promise.all([
      this.querySubTypeOpts(),
      this.queryModelOpts(),
      this.queryBrandOpts(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
        });
      }, 500);
    });
  },

  methods: {
    checkPermission,
    querySubTypeOpts() {
      queryChargingDict({ dictCode: "subType" }).then((res) => {
        this.subTypeOptions = res.data;
      });
    },
    queryModelOpts() {
      queryModelOpts({ modelName: "" }).then((res) => {
        this.modelOptions = res.data;
      });
    },
    queryBrandOpts() {
      queryBrandOpts({ brandName: "" }).then((res) => {
        this.brandOptions = res.data;
      });
    },
    showDetail(row) {
      this.$router.push({
        path: "/chargingStation/components/stationDetail",
        query: {
          pileNo: row.pileNo,
          pileId: row.pileId,
          stationNo: row.stationNo,
        },
      });
    },

    handleChargingGun() {
      this.$router.push({
        path: "/chargingStation/gun",
        query: {
          // stationId: this.stationId,
        },
      });
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      // this.handRow.stationIds.length == 0
      //   ? "是否确认导出所有数据?"
      //   : "是否确认导出所选数据?";
      const params = {
        ...this.searchForm,
        // stationIds: this.handRow.stationIds,
      };
      if (Array.isArray(params.rangeTime)) {
        params.beginTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0]
          ? params.region[0] + "0000"
          : undefined;
        params["city"] = params.region[1] ? params.region[1] + "00" : undefined;
        params["county"] = params.region[2];
        delete params.region;
      }
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportStationExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          this.reportTrackEvent(DATA_INFO_CLICK_CHARING_STATION_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    padZeros(str, length) {
      return (str + "0".repeat(length)).slice(0, length);
    },

    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.beginTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      console.log(params.region, "region");
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0]
          ? params.region[0] + "0000"
          : undefined;
        params["city"] = params.region[1] ? params.region[1] + "00" : undefined;
        params["county"] = params.region[2];
        delete params.region;
      }
      console.log("查询", params);

      queryStationList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
  },
  computed: {
    config() {
      return [
        {
          key: "pileNo",
          title: "充电桩编号",
          type: "input",
          placeholder: "请输入充电桩编号",
        },
        {
          key: "pileName",
          title: "充电桩名称",
          type: "input",
          placeholder: "请输入充电桩名称",
        },
        {
          key: "brandId",
          title: "设备品牌",
          type: "select",
          options: this.brandOptions,
          optionLabel: "brandName",
          optionValue: "brandId",
          placeholder: "请选择设备品牌",
        },
        {
          key: "rangeTime",
          title: "桩投运日期",
          type: "dateRange",
          placeholder: "请选择桩投运日期",
          startPlaceholder: "桩投运开始日期",
          endPlaceholder: "桩投运结束日期",
        },
        {
          key: "stationNo",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "modelId",
          title: "设备型号",
          type: "select",
          options: this.modelOptions,
          optionLabel: "modelName",
          optionValue: "modelId",
          placeholder: "请选择设备型号",
        },
        {
          key: "region",
          title: "省市区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
          props: {
            multiple: false,
          },
        },
        {
          key: "subTypeCode",
          title: "设备类型",
          type: "select",
          placeholder: "请选择设备类型",
          options: this.subTypeOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
        },
      ];
    },
  },
};
</script>

<style></style>
