//充电桩详情页
<template>
  <div class="app-container">
    <h4>充电桩编号:{{ pileNo }}</h4>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <!-- 基本信息tab-S -->
      <el-tab-pane label="基本信息" name="info">
        <!-- 基础信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>基础信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in infoList"
              :key="index"
              :label="item.title"
            >
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 基础信息-E -->
        <!-- 桩计费信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>桩计费信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item v-for="(item, index) in feeList" :key="index">
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 桩计费信息-E -->
        <!-- 充电枪信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电枪信息</span>
          </div>
          <el-descriptions
            class="descriptions"
            :column="4"
            border
            v-for="(items, index) in chargingList"
            :key="index"
          >
            <el-descriptions-item>
              <template slot="label">
                编号
              </template>
              <el-tooltip :content="items.gunNo" placement="top-start">
                <span>{{ items.gunNo }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                名称
              </template>
              <el-tooltip :content="items.gunName" placement="top-start">
                <span>{{ items.gunName }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                品牌
              </template>
              <el-tooltip :content="items.gunBrandName" placement="top-start">
                <span>{{ items.gunBrandName }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                型号
              </template>
              <el-tooltip :content="items.gunModelName" placement="top-start">
                <span>{{ items.gunModelName }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                充电接口类型
              </template>
              <el-tooltip
                :content="items.couplertypeName"
                placement="top-start"
              >
                <span>{{ items.couplertypeName }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                均摊功率
              </template>
              <el-tooltip :content="items.amortizedPower" placement="top-start">
                <span>{{ items.amortizedPower }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                额定功率
              </template>
              <el-tooltip :content="items.ratePower" placement="top-start">
                <span>{{ items.ratePower }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                充电堆枪口编号
              </template>
              <el-tooltip :content="items.stackGunNo" placement="top-start">
                <span>{{ items.stackGunNo }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                运行状态
              </template>
              <el-tooltip :content="items.runStatusName" placement="top-start">
                <span>{{ items.runStatusName }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                运营状态
              </template>
              <el-tooltip :content="items.operStatusName" placement="top-start">
                <span>{{ items.operStatusName }}</span>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item>
              <span></span>
            </el-descriptions-item>
            <el-descriptions-item>
              <span></span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 充电枪信息-E -->
      </el-tab-pane>
      <!-- 基本信息tab-E -->
      <!-- 异常记录tab-S -->
      <el-tab-pane label="异常记录" name="record">
        <!-- 故障统计-S -->
        <el-card style="margin-bottom: 10px;">
          <div
            slot="header"
            class="card-title-wrap"
            style="justify-content: space-between"
          >
            <div style="display: flex;">
              <div class="card-title-line"></div>
              <span>异常统计</span>
            </div>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="float: right"
              value-format="yyyy-MM-dd"
              @change="handleDateChange"
              :clearable="false"
              :picker-options="pickerOptions"
              @blur="resetDisableDate"
            >
            </el-date-picker>
          </div>
          <div class="statistics-box" v-loading="loading">
            <div v-for="(item, index) in statisticsList" :key="index">
              <el-statistic
                group-separator=","
                :precision="0"
                :value="item.value"
              >
                <template slot="suffix">
                  <span style="font-size:12px">{{ item.unit }}</span>
                </template>
                <template slot="title">
                  {{ item.title }}
                  <el-tooltip
                    effect="dark"
                    :content="item.tooltip"
                    placement="top"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </template>
              </el-statistic>
            </div>
          </div>
        </el-card>
        <!-- 故障统计-E -->
        <!-- 故障记录-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>异常记录</span>
          </div>
          <GridTable
            :columns="columns"
            :tableData="tableData"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="total"
            @changePage="getRecord"
            :loading="loading"
            :tableId="tableId"
          >
            <!-- <template slot="jump" slot-scope="{ row }">
              <el-button type="text" size="large" @click="showDetail(row)">
                {{ row.pileNo }}
              </el-button>
            </template> -->
          </GridTable>
        </el-card>
        <!-- 故障记录-E -->
      </el-tab-pane>
      <!-- 异常记录tab-E -->
    </el-tabs>
  </div>
</template>

<script>
import {
  queryPileInfo,
  queryPileRecord,
  queryPileStatistics,
} from "@/api/chargingStation/index.js";
import moment from "moment";
import GridTable from "@/components/GridTable/index.vue";
export default {
  components: {
    GridTable,
  },
  data() {
    return {
      stationNo: undefined,
      pileId: undefined,
      columns: [
        {
          field: "equipType",
          title: "告警类型",
        },
        {
          field: "errorName",
          title: "故障名称",
        },
        {
          field: "faultCode",
          title: "故障码",
        },
        {
          field: "alarmDesc",
          title: "故障描述",
        },
        {
          field: "triggerCount",
          title: "故障次数",
        },
        {
          field: "faultLevel",
          title: "告警等级",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.faultLevelOptions?.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "alarmDuration",
          title: "告警持续时间",
        },
        {
          field: "gmtClear",
          title: "告警清除时间",
        },
        {
          field: "gmtCreate",
          title: "推送时间",
        },
        {
          field: "recoverFlag",
          title: "是否恢复",
        },
        {
          field: "recoverType",
          title: "恢复类型",
        },
        {
          field: "handleResult",
          title: "处理结果",
        },
        {
          field: "businessNo",
          title: "运维工单",
          // slots: { default: "jump" },
        },
        {
          field: "handleAssigneeName",
          title: "处理人",
        },
        {
          field: "handleTime",
          title: "处理时间",
        },
      ],
      tableData: [],
      dateRange: ["", ""],
      searchForm: { pageNum: 1, pageSize: 10 },
      total: 0,
      loading: false,
      tableId: "stationDetailRecordList",
      activeName: "info",
      pileNo: undefined,
      statisticsList: [],
      infoList: [],
      feeList: [],
      chargingList: [],
      disabledCurrent: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log("onPick", maxDate, minDate);
          if (!maxDate) {
            this.disabledCurrent = minDate;
          }
        },
        disabledDate: (current) => {
          if (!this.disabledCurrent) return false;
          return (
            (current &&
              current <
                moment(this.disabledCurrent)
                  .subtract(1, "Y")
                  .startOf("day")) ||
            current >
              moment(this.disabledCurrent)
                .add(1, "Y")
                .endOf("day")
          );
        },
      },
      faultLevelOptions: [],
    };
  },
  created() {
    this.getDicts("fault_level").then((response) => {
      this.faultLevelOptions = response.data;
    });

    this.pileNo = this.$route.query.pileNo;
    this.stationNo = this.$route.query.stationNo;
    this.pileId = this.$route.query.pileId;
    this.getInfo();
  },
  methods: {
    //  showDetail(row) {
    //   this.$router.push({
    //     path: "/chargingStation/components/stationDetail",
    //     query: {
    //       pileNo: row.pileNo,
    //       pileId: row.pileId,
    //       stationNo: row.stationNo,
    //     },
    //   });
    // },
    // 每次失焦重置disableDate
    resetDisableDate() {
      this.disabledCurrent = null;
    },
    getInfo() {
      queryPileInfo({
        pileNo: this.pileNo,
        stationNo: this.stationNo,
        pileId: this.pileId,
      }).then((res) => {
        const { gunDetailVOS, chargingPileBillingVO, ...rest } = res.data;
        this.infoList = [
          { title: "充电桩编号", value: rest?.pileNo || "" },
          { title: "充电桩名称", value: rest?.pileName || "" },
          { title: "站点名称", value: rest?.stationName || "" },
          { title: "站点编码", value: rest?.stationNo || "" },
          { title: "设备类型", value: rest?.subTypeName || "" },
          { title: "设备型号", value: rest?.modelName || "" },
          { title: "设备品牌", value: rest?.brandName || "" },
          { title: "资产编号", value: rest?.assetCode || "" },
          {
            title: "运营类型",
            value:
              rest?.operationMode == "1"
                ? "自营"
                : rest?.operationMode == "2"
                ? "代运营"
                : "",
          },
          { title: "资产单位", value: rest?.operatorName || "" },
          { title: "站点运营状态", value: rest?.operationStatusName || "" },
          { title: "站点上线日期", value: rest?.onlineDate || "" },
          { title: "桩投运日期", value: rest?.operDate || "" },
          { title: "通讯模块协议", value: rest?.protocolName || "" },
          { title: "桩通讯地址", value: rest?.pilePostalAddress || "" },
          {
            title: "是否开放",
            value:
              rest?.openFlag == "1" ? "是" : rest?.openFlag == "0" ? "否" : "",
          },
        ];
        this.feeList = [
          {
            title: "当前计费名称",
            value:
              (chargingPileBillingVO?.chcName || "") +
              "-" +
              (chargingPileBillingVO?.chcNo || ""),
          },
          { title: "计费模型编码", value: chargingPileBillingVO?.chcNo },
          {
            title: "操作人",
            value: chargingPileBillingVO?.updateUser,
          },
          { title: "操作时间", value: chargingPileBillingVO?.updateTime },
          {
            title: "最后下发计费名称",
            value:
              (chargingPileBillingVO?.planChcName || "") +
              "-" +
              (chargingPileBillingVO?.planChcNo || ""),
          },
          {
            title: "最后下发时间",
            value: chargingPileBillingVO?.lastDistributeTime,
          },
          {
            title: "最后下发状态",
            value: chargingPileBillingVO?.issueStatusName,
          },
        ];
        this.chargingList = gunDetailVOS;
      });
    },
    getRecord() {
      const params = {
        equipNo: this.pileNo,
        stationNo: this.stationNo,
        startDate: this.dateRange?.[0] || "",
        endDate: this.dateRange?.[1] || "",
        ...this.searchForm,
      };
      this.loading = true;
      console.log(params, "params");
      queryPileRecord(params).then((res) => {
        if (res.code === "10000") {
          this.tableData = res.data;
          this.total = res.total;
          this.loading = false;
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
      queryPileStatistics(params).then((res) => {
        if (res.code === "10000") {
          const { faultCount = 0, pendingFault = 0 } = res.data;
          this.statisticsList = [
            {
              title: "待处理异常",
              value: pendingFault,
              unit: "个",
              tooltip:
                "指的是时间筛选范围内的该充电桩的异常告警状态为【未处理】的总数，单位为【个】",
            },
            {
              title: "累计异常",
              value: faultCount,
              unit: "个",
              tooltip:
                "指的是时间筛选范围内的该充电桩的异常告警总数，包含所有状态的告警数据。单位为【个】",
            },
          ];
          console.log(this.statisticsList);
          this.loading = false;
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    handleClick(val) {
      console.log(val.name, "点击");
      if (val.name === "info") {
        this.getInfo();
      } else {
        this.dateRange = [
          moment()
            .subtract(6, "days")
            .format("YYYY-MM-DD"),
          moment().format("YYYY-MM-DD"),
        ];
        this.getRecord();
      }
    },

    handleDateChange() {
      this.searchForm = { pageNum: 1, pageSize: 10 };
      this.getRecord();
    },
  },
};
</script>

<style lang="less" scoped>
.descriptions {
  margin: 10px 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
  }
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic .head {
  margin-bottom: 12px;
}
</style>
