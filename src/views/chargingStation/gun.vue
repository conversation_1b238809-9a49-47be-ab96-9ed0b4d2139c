//充电枪
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['chargingStation:gun:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="gunId"
      >
        <!-- <template slot="xToolbarBtn" slot-scope="{}">
          <el-button size="mini" type="primary" @click.stop="handleChargingGun">
            充电枪
          </el-button>
        </template> -->
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['chargingStation:gun:detail']"
          >
            详情
          </el-button>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showDetail(row)">
            {{ row.gunNo }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryGunList,
  exportGunExcel,
  queryChargingDict,
} from "@/api/chargingStation/index.js";
import { regionData } from "element-china-area-data";
import checkPermission from "@/utils/permission.js";
import { getToken } from "@/utils/auth";
import { DATA_INFO_CLICK_CHARING_GUN_REPORT } from "@/utils/track/track-event-constants";

export default {
  name: "chargingGun",
  components: { AdvancedForm, GridTable },
  data() {
    return {
      // config: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "gunNo",
          title: "充电枪编号",
          slots: { default: "jump" },
        },
        {
          field: "gunName",
          title: "充电枪名称",
        },
        {
          field: "runStatusName",
          title: "枪运行状态",
        },
        {
          field: "operStatusName",
          title: "枪运营状态",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "provinceInfo",
          title: "省市区",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 170,
        },
        {
          field: "subTypeName",
          title: "设备类型",
        },
        {
          field: "stationNo",
          title: "站点编码",
        },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "chargingStationList",
      recordList: [],
      operStatusOptions: [],
      subTypeOptions: [],
      runStatusOptions: [],
      handRow: {
        stationIds: [],
      },
      token: "",
    };
  },
  created() {},
  activated() {
    if (this.$route.params) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    queryChargingDict({ dictCode: "subType" }).then((res) => {
      this.subTypeOptions = res.data;
    });
    queryChargingDict({ dictCode: "pileGunOperStatus" }).then((res) => {
      this.operStatusOptions = res.data;
    });
    queryChargingDict({ dictCode: "gunCouplerRunStatus" }).then((res) => {
      this.runStatusOptions = res.data;
    });
    Promise.all([]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          // this.initConfig();
        });
      }, 500);
    });
    this.token = getToken();
  },
  methods: {
    checkPermission,
    showDetail(row) {
      this.$router.push({
        path: "/chargingStation/components/gunDetail",
        query: {
          pileId: row.pileId,
          gunId: row.gunId,
          stationNo: row.stationNo,
          gunNo: row.gunNo,
        },
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      // this.handRow.stationIds.length == 0
      //   ? "是否确认导出所有数据?"
      //   : "是否确认导出所选数据?";
      const params = {
        ...this.searchForm,
        // stationIds: this.handRow.stationIds,
      };
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0]
          ? params.region[0] + "0000"
          : undefined;
        params["city"] = params.region[1] ? params.region[1] + "00" : undefined;
        params["county"] = params.region[2];
        delete params.region;
      }
      console.log("查询", params);

      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportGunExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          this.reportTrackEvent(DATA_INFO_CLICK_CHARING_GUN_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0]
          ? params.region[0] + "0000"
          : undefined;
        params["city"] = params.region[1] ? params.region[1] + "00" : undefined;
        params["county"] = params.region[2];
        delete params.region;
      }
      console.log("查询", params);

      queryGunList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
  },
  computed: {
    config() {
      return [
        {
          key: "gunNo",
          title: "充电枪编号",
          type: "input",
          placeholder: "请输入充电枪编号",
        },
        {
          key: "gunName",
          title: "充电枪名称",
          type: "input",
          placeholder: "请输入充电枪名称",
        },
        {
          key: "runStatus",
          title: "枪运行状态",
          type: "select",
          placeholder: "请选择枪运行状态",
          options: this.runStatusOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
        },
        {
          key: "region",
          title: "省市区",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
          props: {
            multiple: false,
          },
        },
        {
          key: "pileNo",
          title: "充电桩编号",
          type: "input",
          placeholder: "请输入充电桩编号",
        },
        {
          key: "pileName",
          title: "充电桩名称",
          type: "input",
          placeholder: "请输入充电桩名称",
        },
        {
          key: "operStatus",
          title: "枪运营状态",
          type: "select",
          options: this.operStatusOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
          placeholder: "请选择枪运营状态",
        },
        {
          key: "stationNo",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "subTypeCode",
          title: "设备类型",
          type: "select",
          placeholder: "请选择设备类型",
          options: this.subTypeOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
        },
      ];
    },
  },
};
</script>

<style></style>
