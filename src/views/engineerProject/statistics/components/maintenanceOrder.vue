<template>
  <div class="common-box" v-loading="loading">
    <div class="mb10">
      <!-- 项目概况 -->
      <el-card class="common-flex-item">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>项目概况</span>
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              <div v-if="isEnergyStorage">
                <p>
                  累计项目数：所有项目数总和
                </p>
                <p>
                  累计储能设备数：所有项目中的储能设备数总和
                </p>
                <p>
                  在途项目：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有项目数，占比=（项目状态除了【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的项目数/所有项目数）*100%
                </p>
                <p>
                  待上线项目：项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目占比，占比=项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目数/所有项目数）*100%
                </p>
                <p>
                  已上线项目：项目流程节点为【已上线】的项目占比，占比=（项目流程节点为【已上线】的项目数/所有项目数）*100%
                </p>
                <p>
                  在途储能设备：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有储能设备数，占比=（项目状态除了【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的项目的储能设备数/所有项目储能设备数）*100%
                </p>
                <p>
                  待上线储能设备：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】的所有储能设备数，占比=（项目状态除了【待上线、竣工验收、竣工验收不通过、待评价、已评价】的项目的储能设备数/所有项目储能设备数）*100%
                </p>
                <p>
                  已上线储能设备：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目流程节点为【已上线】的所有储能设备数，占比=（项目流程节点为【已上线】的项目储能设备数/所有项目储能设备数）*100%
                </p>
              </div>
              <div v-else>
                <p>
                  累计项目数：所有项目数总和
                </p>
                <p>
                  累计桩数：所有项目中的设备数总和
                </p>
                <p>
                  在途项目：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有项目数，占比=（项目状态除了【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的项目数/所有项目数）*100%；
                </p>
                <p>
                  待上线项目：项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目占比，占比=项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目数/所有项目数）*100%；
                </p>
                <p>
                  已上线项目：项目流程节点为【已上线】的项目占比，占比=（项目流程节点为【已上线】的项目数/所有项目数）*100%；
                </p>
                <p>
                  在途桩：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有设备数，占比=（项目状态除了【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的项目的设备数/所有项目设备数）*100%；（查项目批次表的设备数）；
                </p>
                <p>
                  待上线桩：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】的所有设备数，占比=（项目状态除了【待上线、竣工验收、竣工验收不通过、待评价、已评价】的项目的设备数/所有项目设备数）*100%；（查项目批次表的设备数）；
                </p>
                <p>
                  已上线桩：饼图按占比显示，鼠标放上去后需显示占比+数量，统计项目流程节点为【已上线】的所有设备数，占比=（项目流程节点为【已上线】的项目设备数/所有项目设备数）*100%；（查项目批次表的设备数）
                </p>
              </div>
            </div>

            <i class="el-icon-question ml5"></i>
          </el-tooltip>
        </div>
        <div class="common-flex">
          <div class="common-flex-item mr10">
            <el-statistic
              group-separator=","
              :value="dataObj.totalCount"
              title="累计项目数"
              suffix="个"
              class="mb20"
            ></el-statistic>
            <PieChartSolid
              :list="projectPieList"
              v-if="projectPieList && projectPieList.length > 0"
              :pieRadius="['0%', '60%']"
              showLegend
            ></PieChartSolid>
            <el-empty v-else></el-empty>
          </div>
          <div class="common-flex-item">
            <el-statistic
              group-separator=","
              :value="dataObj.totalDeviceCount"
              :title="isEnergyStorage ? '累计储能设备数' : '累计桩数'"
              suffix="个"
              class="mb20"
              :value-style="{ color: '#f3c042' }"
            ></el-statistic>
            <PieChartSolid
              :list="pilePieList"
              v-if="pilePieList && pilePieList.length > 0"
              :pieRadius="['0%', '60%']"
              showLegend
            ></PieChartSolid>
            <el-empty v-else></el-empty>
          </div>
        </div>
      </el-card>
      <!-- 项目进度 -->
      <el-card class="common-flex-item">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>项目进度</span>
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              <div v-if="isEnergyStorage">
                <p>
                  在途项目：统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有项目数
                </p>
                <p>
                  在途储能设备数：统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有储能设备数（查项目批次表的储能设备数）
                </p>
                <p>
                  已上线项目：项目流程节点为【已上线】的项目数总和
                </p>
                <p>
                  已上线项目：项目流程节点为【已上线】的项目数总和
                </p>
                <p>
                  施工中项目：项目状态为【施工中】的项目数总和
                </p>
                <p>
                  施工中项目：项目状态为【施工中】的项目数总和
                </p>
                <p>
                  发包中项目：项目流程节点为【施工发包中】的项目数总和
                </p>
                <p>
                  商签中项目：项目流程节点为【商签中】的项目数总和
                </p>
                <p>
                  已上线储能设备数：项目流程节点为【已上线】的项目储能设备数总和
                </p>
                <p>
                  完工待上线储能设备数：项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目储能设备数总和
                </p>
                <p>
                  完工待上线储能设备数：项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目储能设备数总和
                </p>
                <p>
                  开工准备阶段储能设备数：项目流程节点为【开工准备阶段】的项目储能设备数总和
                </p>
                <p>
                  发包中储能设备数：项目流程节点为【施工发包中】的项目储能设备数总和
                </p>
                <p>
                  商签中储能设备数：项目流程节点为【商签中】的项目储能设备数总和
                </p>
              </div>
              <div v-else>
                <p>
                  在途项目：统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有项目数，
                </p>
                <p>
                  在途桩数：统计项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队、施工中、待验收、验收不通过】的所有设备数（查项目批次表的设备数）；
                </p>
                <p>已上线项目：项目流程节点为【已上线】的项目数总和；</p>
                <p>
                  完工待上线项目：项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目数总和；
                </p>
                <p>施工中项目：项目状态为【施工中】的项目数总和；</p>
                <p>
                  开工准备阶段项目：项目流程节点为【开工准备阶段】的项目数总和；
                </p>
                <p>发包中项目：项目流程节点为【施工发包中】的项目数总和；</p>
                <p>商签中项目：项目流程节点为【商签中】的项目数总和；</p>
                <p>
                  已上线桩数：项目流程节点为【已上线】的项目设备数总和；（查项目批次表的设备数）
                </p>
                <p>
                  完工待上线桩数：项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】且项目流程节点为【已完工】的项目设备数总和；（查项目批次表的设备数）
                </p>
                <p>
                  施工中桩数：项目状态为【施工中】的项目设备数总和；（查项目批次表的设备数）
                </p>
                <p>
                  开工准备阶段桩数：项目流程节点为【开工准备阶段】的项目设备数总和；（查项目批次表的设备数）
                </p>
                <p>
                  发包中桩数：项目流程节点为【施工发包中】的项目设备数总和；（查项目批次表的设备数）
                </p>
                <p>
                  商签中桩数：项目流程节点为【商签中】的项目设备数总和；（查项目批次表的设备数）
                </p>
              </div>
            </div>

            <i class="el-icon-question ml5"></i>
          </el-tooltip>
        </div>
        <div class="common-flex">
          <div class="common-flex-item mr10">
            <el-statistic
              group-separator=","
              :value="dataObj.inTransitCount"
              title="在途项目"
              suffix="个"
              class="mb20"
            ></el-statistic>
            <PieChartSolid
              :list="projectTypePieList"
              v-if="projectTypePieList && projectTypePieList.length > 0"
              :pieRadius="['40%', '60%']"
              showLegend
            ></PieChartSolid>
            <el-empty v-else></el-empty>
          </div>
          <div class="common-flex-item">
            <el-statistic
              group-separator=","
              :value="dataObj.inTransitDeviceCount"
              :title="isEnergyStorage ? '在途储能设备数' : '在途桩数'"
              suffix="个"
              class="mb20"
              :value-style="{ color: '#f3c042' }"
            ></el-statistic>
            <PieChartSolid
              :list="pileTypePieList"
              v-if="pileTypePieList && pileTypePieList.length > 0"
              :pieRadius="['40%', '60%']"
              showLegend
            ></PieChartSolid>
            <el-empty v-else></el-empty>
          </div>
        </div>
      </el-card>
    </div>
    <!-- 项目时效 -->
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>项目时效</span>
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <p>
              上线平均耗时：工序项终审通过的时间→项目流程节点变成已上线之间的总时长，项目总数取项目状态为【待上线、竣工验收、竣工验收不通过、待评价、已评价】项目数总和。
            </p>
            <p>
              竣工验收平均耗时：施工工序项全部提交的最新时间→工序项终审通过的时间，项目总数取项目状态为【待验收、验收不通过、待上线、竣工验收、竣工验收不通过、待评价、已评价】项目数总和。
            </p>
            <p>
              施工平均耗时：开工申请审核通过时间→施工工序项全部提交的最新时间，项目总数取项目状态为【施工中、待验收、验收不通过】项目数总和。
            </p>
            <p>
              开工申请审批平均耗时：开工申请首次提交时间→开工申请审核通过时间之间的总时长；项目总数取开工申请状态为【待审核、审核不通过、审核通过、已确认】项目数总和。
            </p>
            <p>
              开工申请提交平均耗时：项目最早绑定施工队的时间→开工申请首次提交的时间之间的总时长；项目总数取开工申请状态为【待审核】项目数总和。
            </p>
            <p>
              采购发包平均耗时：项目批次操作【供应商下单通知】的确认时间→项目最早绑定施工队的时间之间的总时长；项目总数取项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知、已指定施工队】项目数总和。
            </p>
            <p>
              采购发包通知平均耗时：项目批次操作【投建协议已盖章】确认操作的时间→项目批次操作【供应商下单通知】的确认时间之间的总时长：项目总数取项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成、施工供应商下单已通知】项目数总和。
            </p>
            <p>
              投建协议商服复审平均耗时：项目批次操作【投建协议已盖章】中手动填写的时间→项目批次操作【投建协议已盖章】确认操作的时间之间的总时长；项目总数取项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成】项目数总和。
            </p>
            <p>
              投建协议双章平均耗时：投建协议运管审批通过时间→项目批次操作【投建协议已盖章】中手动填写的时间之间的总时长，有可能出现负值，按负值显示即可；项目总数取项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成】项目数总和。
            </p>
            <p>
              投建协议运管审批平均耗时：投建协议运管提交时间→投建协议运管审批通过时间之间的总时长；项目总数取项目状态为【踏勘审批通过、场地双章审批中、场地双章审批完成】项目数总和。
            </p>
            <p>
              投建协议运管提交平均耗时：投建协议商服审核通过时间→投建协议运管提交时间之间的总时长；项目总数取项目状态为【踏勘审批通过、场地双章审批中】项目数总和。
            </p>
            <p>
              投建协议商服审核平均耗时：投建协议需求池提交时间（取最新的提交时间）→投建协议商服审核通过时间之间的总时长；项目总数取项目状态为【踏勘审批通过】项目数总和。
            </p>
            <p>
              投建协议提交平均耗时：踏勘审批通过时间→投建协议需求池提交时间（取最新的提交时间）之间的总时长；项目总数取项目状态为【踏勘审批通过】项目数总和。
            </p>
            <p>
              踏勘平均耗时：踏勘单的创建时间—终审通过的总时长，项目总数取项目状态为【踏勘审批通过】项目数总和。
            </p>
            <p>项目投建平均耗时：踏勘单的创建时间—工序项终审通过的时间</p>
          </div>

          <i class="el-icon-question ml5"></i>
        </el-tooltip>
      </div>
      <div>
        <el-statistic
          group-separator=","
          :value="dataObj.projectConstructDurationAvg"
          title="项目投建平均耗时"
          suffix="h"
        ></el-statistic>
        <LineChart
          :axisData="TimeBarObj.yAxis"
          :serieData="TimeBarObj.series"
          lineType="bar"
          :isHorizontalBar="true"
          v-if="TimeBarObj.yAxis && TimeBarObj.yAxis.length > 0"
          :lineColor="['#45997e']"
          height="500px"
          :showLegend="false"
          showLabel
          unit="单位：h"
        ></LineChart>
        <el-empty v-else></el-empty>
      </div>
    </el-card>
    <div class="mb10">
      <!-- 项目成本 -->
      <el-card class="common-flex-item">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>项目成本</span>
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              <div v-if="isEnergyStorage">
                <p>
                  累计施工预计总成本：所有项目的踏勘单中的工程成本之和，取整数，单位万元
                </p>
                <p>
                  累计施工实际总成本：取项目成本表里的施工实际总价，取整数，单位万元
                </p>
                <p>
                  成本偏差率=（累计施工实际总成本－累计施工预计总成本
                  ）/累计施工预计总成本*100%，小数点后保留2位，四舍五入，单位%
                </p>
                <p>
                  储能设备平均单价：所有项目的踏勘单中的储能设备单价平均值，储能设备单价平均值=踏勘单所有储能设备单价总和/踏勘单数，小数点后保留2位，四舍五入，单位元/wh
                </p>
                <p>
                  储能设备平均单价：所有项目的踏勘单中的储能设备单价平均值，储能设备单价平均值=踏勘单所有储能设备单价总和/踏勘单数，小数点后保留2位，四舍五入，单位元/wh
                </p>
                <p>
                  项目平均施工成本：所有项目的施工实际总价总和/项目数，小数点后保留2位，四舍五入，单位万元
                </p>
              </div>
              <div v-else>
                <p>
                  累计项目预计总成本：所有项目的踏勘单中的施工总成本+商务总成本+设备总成本之和，取整数，单位万元
                </p>
                <p>
                  累计项目实际总成本：取项目成本表里的项目实际总成本，取整数，单位万元
                </p>
                <p>
                  成本偏差率=（累计项目实际总成本－累计项目预计总成本）/累计项目预计总成本*100%，小数点后保留2位，四舍五入，单位%。
                </p>
                <p>
                  累计主缆预计使用长度：取项目成本表里的主缆预计长度总和，小数点后保留2位，四舍五入，单位米；
                </p>
                <p>
                  累计主缆实际使用长度：取项目成本表里的主缆实际长度总和，小数点后保留2位，四舍五入，单位米；
                </p>
                <p>
                  主缆偏差率=（累计主缆实际使用长度－累计主缆预计使用长度）/累计主缆预计使用长度*100%，小数点后保留2位，四舍五入，单位%。
                </p>
              </div>
            </div>

            <i class="el-icon-question ml5"></i>
          </el-tooltip>
        </div>
        <div class="cost-box">
          <div
            class="cost-box-item mr10 mt20"
            v-for="(item, index) in costList"
            :key="index"
          >
            <el-statistic
              group-separator=","
              :value="item.value"
              :title="item.title"
              :suffix="item.unit"
              class="mb20"
              :precision="item.precision"
            ></el-statistic>
          </div>
        </div>
      </el-card>
      <!-- 成本偏差 -->
      <el-card class="common-flex-item" v-if="!isEnergyStorage">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>成本偏差</span>
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              <p>
                商务预计总成本：所有项目踏勘单中的商务总成本之和；取整数，四舍五入，单位万元
              </p>
              <p>
                商务实际总成本：取项目成本表里的商务总成本之和；取整数，四舍五入，单位万元
              </p>
              <p>
                设备预计总成本：所有项目踏勘单中的设备总成本之和；取整数，四舍五入，单位万元
              </p>
              <p>
                设备实际总成本：取项目成本表里的设备总成本之和；取整数，四舍五入，单位万元
              </p>
              <p>
                施工预计总成本：取项目成本表里的施工预计总价之和；取整数，四舍五入，单位万元
              </p>
              <p>
                施工实际总成本：取项目成本表里的施工实际总价之和；取整数，四舍五入，单位万元
              </p>
            </div>

            <i class="el-icon-question ml5"></i>
          </el-tooltip>
        </div>
        <LineChart
          :axisData="offsetBarObj.xAxis"
          :serieData="offsetBarObj.series"
          lineType="bar"
          v-if="TimeBarObj.yAxis && TimeBarObj.yAxis.length > 0"
          :lineColor="['#5d86e4', '#7cb9c2']"
          unit="单位：万元"
        ></LineChart>
        <el-empty v-else></el-empty>
      </el-card>
    </div>
    <!-- 平均施工成本明细 -->
    <el-card v-if="!isEnergyStorage">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>平均施工成本明细</span>
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <p>
              平均主缆预计使用长度=所有项目主缆预计长度之和/项目数，取整数，四舍五入，单位米；主缆预计长度取项目成本表的主缆预计长度；项目数取所有项目状态。
            </p>
            <p>
              平均主缆实际使用长度=所有项目主缆实际长度之和/项目数，取整数，四舍五入，单位米；主缆实际长度取项目成本表的主缆实际长度；
            </p>
            <p>
              平均单桩预计施工成本=所有项目单桩预计施工成本之和/项目数，取整数，四舍五入，单位元；单桩预计施工成本取项目成本表的单桩施工成本预计总价；
            </p>
            <p>
              平均单桩实际施工成本=所有项目单桩实际施工成本之和/项目数，取整数，四舍五入，单位元；单桩实际施工成本取项目成本表的单桩施工成本实际总价；
            </p>
            <p>
              平均单瓦预计施工成本=所有项目单瓦预计施工成本之和/项目数，取整数，四舍五入，单位元；单瓦预计施工成本取项目成本表的单瓦施工成本预计总价；
            </p>
            <p>
              平均单瓦实际施工成本=所有项目单瓦实际施工成本之和/项目数，取整数，四舍五入，单位元；单瓦实际施工成本取项目成本表的单瓦施工成本实际总价；
            </p>
            <p>
              平均单桩商务预计总成本=所有项目单桩商务预计成本之和/项目数，取整数，四舍五入，单位元；单桩商务预计成本取简道云的单桩商务成本；
            </p>
            <p>
              平均单桩商务实际总成本=所有项目单桩商务实际成本之和/项目数，取整数，四舍五入，单位元；单桩商务实际成本=简道云的单桩商务成本；
            </p>
          </div>

          <i class="el-icon-question ml5"></i>
        </el-tooltip>
      </div>
      <div class="detail-box">
        <div
          class="detail-box-item"
          style="border-bottom: 1px dotted #459a7e;border-right: 1px dotted #459a7e;"
        >
          <h3 style="color: #469a7e; margin-left: 20px;">主缆</h3>
          <LineChart
            :axisData="MainCableBarObj.yAxis"
            :serieData="MainCableBarObj.series"
            lineType="bar"
            :isHorizontalBar="true"
            v-if="MainCableBarObj.yAxis && MainCableBarObj.yAxis.length > 0"
            :lineColor="['#5d86e4', '#7cb9c2']"
            height="300px"
            unit="单位：米"
          ></LineChart>
          <el-empty v-else></el-empty>
        </div>
        <div class="detail-box-item" style="border-bottom: 1px dotted #459a7e;">
          <h3 style="color: #469a7e; margin-left: 20px;">单桩</h3>
          <LineChart
            :axisData="pileBarObj.yAxis"
            :serieData="pileBarObj.series"
            lineType="bar"
            :isHorizontalBar="true"
            v-if="pileBarObj.yAxis && pileBarObj.yAxis.length > 0"
            :lineColor="['#5d86e4', '#7cb9c2']"
            height="300px"
            unit="单位：元"
          ></LineChart>
          <el-empty v-else></el-empty>
        </div>
        <div class="detail-box-item" style="border-right: 1px dotted #459a7e;">
          <h3 style="color: #469a7e; margin-left: 20px;">单瓦</h3>
          <LineChart
            :axisData="wattBarObj.yAxis"
            :serieData="wattBarObj.series"
            lineType="bar"
            :isHorizontalBar="true"
            v-if="wattBarObj.yAxis && wattBarObj.yAxis.length > 0"
            :lineColor="['#5d86e4', '#7cb9c2']"
            height="300px"
            unit="单位：元"
          ></LineChart>
          <el-empty v-else></el-empty>
        </div>
        <div class="detail-box-item">
          <h3 style="color: #469a7e; margin-left: 20px;">商务</h3>
          <LineChart
            :axisData="businessBarObj.yAxis"
            :serieData="businessBarObj.series"
            lineType="bar"
            :isHorizontalBar="true"
            v-if="businessBarObj.yAxis && businessBarObj.yAxis.length > 0"
            :lineColor="['#5d86e4', '#7cb9c2']"
            height="300px"
            unit="单位：元"
          ></LineChart>
          <el-empty v-else></el-empty>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from "@/api/engineerProject/investBatchList/index.js";
import PieChartSolid from "@/components/Echarts/pieChartSolid.vue";
import LineChart from "@/components/Echarts/LineChart.vue";

export default {
  components: { PieChartSolid, LineChart },
  data() {
    return {
      loading: false,
      isEnergyStorage: false,
      dataObj: {
        totalCount: 0,
        totalDeviceCount: 0,
        inTransitCount: 0,
        inTransitDeviceCount: 0,
        projectConstructDurationAvg: 0,
      },
      projectPieList: [
        // {
        //   value: 2,
        //   name: "在途项目",
        //   itemStyle: { color: "#f3c042", borderRadius: "3%" },
        // },
        // {
        //   value: 12,
        //   name: "待上线项目",
        //   itemStyle: { color: "#459a7e", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "已上线项目",
        //   itemStyle: { color: "#6ba364", borderRadius: "3%" },
        // },
      ],
      pilePieList: [
        // {
        //   value: 2,
        //   name: "在途桩",
        //   itemStyle: { color: "#f3c042", borderRadius: "3%" },
        // },
        // {
        //   value: 12,
        //   name: "待上线桩",
        //   itemStyle: { color: "#459a7e", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "已上线桩",
        //   itemStyle: { color: "#6ba364", borderRadius: "3%" },
        // },
      ],
      projectTypePieList: [
        // {
        //   value: 10,
        //   name: "已上线项目",
        //   itemStyle: { color: "#c95948", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "完工待上线项目",
        //   itemStyle: { color: "#df7c41", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "施工中项目",
        //   itemStyle: { color: "#eabf5a", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "开工准备阶段项目",
        //   itemStyle: { color: "#6ba264", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "发包中项目",
        //   itemStyle: { color: "#7cb9c2", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "商签中项目",
        //   itemStyle: { color: "#5d86e5", borderRadius: "3%" },
        // },
      ],
      pileTypePieList: [
        // {
        //   value: 10,
        //   name: "已上线桩数",
        //   itemStyle: { color: "#c95948", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "完工待上线桩数",
        //   itemStyle: { color: "#df7c41", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "施工中桩数",
        //   itemStyle: { color: "#eabf5a", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "开工准备阶段桩数",
        //   itemStyle: { color: "#6ba264", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "发包中桩数",
        //   itemStyle: { color: "#7cb9c2", borderRadius: "3%" },
        // },
        // {
        //   value: 10,
        //   name: "商签中桩数",
        //   itemStyle: { color: "#5d86e5", borderRadius: "3%" },
        // },
      ],
      TimeBarObj: {
        // yAxis: [
        //   "上线平均耗时",
        //   "竣工验收平均耗时",
        //   "施工平均耗时",
        //   "开工申请审批平均耗时",
        //   "开工申请提交平均耗时",
        //   "采购发包平均耗时",
        //   "采购发包通知平均耗时",
        //   "投建协议商服复审平均耗时",
        //   "投建协议双章平均耗时",
        //   "投建协议运管审批平均耗时",
        //   "投建协议运管提交平均耗时",
        //   "投建协议商服审核平均耗时",
        //   "投建协议提交平均耗时",
        //   "踏勘平均耗时",
        // ],
        // series: [
        //   {
        //     name: "单位：h",
        //     data: [
        //       120,
        //       221,
        //       123,
        //       123,
        //       432,
        //       123,
        //       432,
        //       124,
        //       124,
        //       153,
        //       135,
        //       145,
        //       132,
        //       164,
        //     ],
        //   },
        // ],
      },
      offsetBarObj: {
        // xAxis: ["商务总成本", "设备总成本", "施工总成本"],
        // series: [
        //   { name: "预计", data: [100, 120, 124] },
        //   { name: "实际", data: [130, 140, 100] },
        // ],
      },
      MainCableBarObj: {
        // yAxis: ["长度"],
        // series: [
        //   {
        //     name: "平均主缆预计使用长度",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        //   {
        //     name: "平均主缆实际使用长度",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        // ],
      },
      pileBarObj: {
        // yAxis: ["成本"],
        // series: [
        //   {
        //     name: "平均单桩预计施工成本",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        //   {
        //     name: "平均主缆实际施工成本",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        // ],
      },
      wattBarObj: {
        // yAxis: ["成本"],
        // series: [
        //   {
        //     name: "平均单瓦施工预计成本",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        //   {
        //     name: "平均单瓦施工实际成本",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        // ],
      },
      businessBarObj: {
        // yAxis: ["成本"],
        // series: [
        //   {
        //     name: "平均单桩商务预计总成本",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        //   {
        //     name: "平均单桩商务实际总成本",
        //     data: [120],
        //     barWidth: "50px",
        //   },
        // ],
      },
    };
  },
  computed: {
    costList() {
      return this.isEnergyStorage
        ? [
            {
              value: this.dataObj.totalProjectPlanCostAll || 0,
              title: "累计施工预计总成本",
              unit: "万",
            },
            {
              value: this.dataObj.totalProjectRealCostAll || 0,
              title: "累计施工实际总成本",
              unit: "万",
            },
            {
              value: this.dataObj.costDeviationRate || 0,
              title: "成本偏差率",
              unit: "%",
              precision: 2,
            },
            {
              value: this.dataObj.unitStorageDevicesCostAvg || 0,
              title: "储能设备平均单价",
              unit: "元/wh",
              precision: 2,
            },
            {
              value: this.dataObj.unitConstructCostAvg || 0,
              title: "工程平均单价",
              unit: "元/wh",
              precision: 2,
            },
            {
              value: this.dataObj.projectConstructCostAvg || 0,
              title: "项目平均施工成本",
              unit: "元",
              precision: 2,
            },
          ]
        : [
            {
              value: this.dataObj.totalProjectPlanCostAll || 0,
              title: "累计项目预计总成本",
              unit: "万",
            },
            {
              value: this.dataObj.totalProjectRealCostAll || 0,
              title: "累计项目实际总成本",
              unit: "万",
            },
            {
              value: this.dataObj.costDeviationRate || 0,
              title: "成本偏差率",
              unit: "%",
              precision: 2,
            },
            {
              value: this.dataObj.totalMainCableLengthPlanAll || 0,
              title: "累计主缆预计使用长度",
              unit: "米",
            },
            {
              value: this.dataObj.totalMainCableLengthRealAll || 0,
              title: "累计主缆实际使用长度",
              unit: "米",
            },
            {
              value: this.dataObj.mainCableLengthDeviationRate || 0,
              title: "主缆偏差率",
              unit: "%",
              precision: 2,
            },
          ];
    },
  },
  methods: {
    jumpToPage(item) {
      if (item.params) {
        this.$router.push({
          name: "investBatchList",
          params: { ...item.params },
        });
      }
    },
    jumpToCost() {
      this.$router.push({ path: "/engineerProject/projectCost" });
    },
    queryData(params) {
      this.loading = true;
      api
        .queryStatisticsData(params)
        .then((res) => {
          this.loading = false;
          if (res?.data) {
            this.dataObj = { ...res.data };
            this.isEnergyStorage = this.dataObj?.businessType == "2";
            this.projectPieList = [
              {
                value: this.dataObj.inTransitCount,
                name: "在途项目",
                itemStyle: { color: "#f3c042", borderRadius: "3%" },
              },
              {
                value: this.dataObj.completedCount,
                name: "待上线项目",
                itemStyle: { color: "#459a7e", borderRadius: "3%" },
              },
              {
                value: this.dataObj.onlineCount,
                name: "已上线项目",
                itemStyle: { color: "#6ba364", borderRadius: "3%" },
              },
            ];
            this.pilePieList = [
              {
                value: this.dataObj.inTransitDeviceCount,
                name: this.isEnergyStorage ? "在途储能设备" : "在途桩",
                itemStyle: { color: "#f3c042", borderRadius: "3%" },
              },
              {
                value: this.dataObj.completedDeviceCount,
                name: this.isEnergyStorage ? "待上线储能设备" : "待上线桩",
                itemStyle: { color: "#459a7e", borderRadius: "3%" },
              },
              {
                value: this.dataObj.onlineDeviceCount,
                name: this.isEnergyStorage ? "已上线储能设备" : "已上线桩",
                itemStyle: { color: "#6ba364", borderRadius: "3%" },
              },
            ];
            this.projectTypePieList = [
              {
                value: this.dataObj.onlineCount,
                name: "已上线项目",
                itemStyle: { color: "#c95948", borderRadius: "3%" },
              },
              {
                value: this.dataObj.completedCount,
                name: "完工待上线项目",
                itemStyle: { color: "#df7c41", borderRadius: "3%" },
              },
              {
                value: this.dataObj.constructionCount,
                name: "施工中项目",
                itemStyle: { color: "#eabf5a", borderRadius: "3%" },
              },
              {
                value: this.dataObj.workPrepareCount,
                name: "开工准备阶段项目",
                itemStyle: { color: "#6ba264", borderRadius: "3%" },
              },
              {
                value: this.dataObj.contractCount,
                name: "发包中项目",
                itemStyle: { color: "#7cb9c2", borderRadius: "3%" },
              },
              {
                value: this.dataObj.businessSignCount,
                name: "商签中项目",
                itemStyle: { color: "#5d86e5", borderRadius: "3%" },
              },
            ];
            this.pileTypePieList = [
              {
                value: this.dataObj.onlineDeviceCount,
                name: this.isEnergyStorage ? "已上线储能设备数" : "已上线桩数",
                itemStyle: { color: "#c95948", borderRadius: "3%" },
              },
              {
                value: this.dataObj.completedDeviceCount,
                name: this.isEnergyStorage
                  ? "完工待上线储能设备数"
                  : "完工待上线桩数",
                itemStyle: { color: "#df7c41", borderRadius: "3%" },
              },
              {
                value: this.dataObj.constructionDeviceCount,
                name: this.isEnergyStorage ? "施工中储能设备数" : "施工中桩数",
                itemStyle: { color: "#eabf5a", borderRadius: "3%" },
              },
              {
                value: this.dataObj.workPrepareDeviceCount,
                name: this.isEnergyStorage
                  ? "开工准备阶段储能设备数"
                  : "开工准备阶段桩数",
                itemStyle: { color: "#6ba264", borderRadius: "3%" },
              },
              {
                value: this.dataObj.contractDeviceCount,
                name: this.isEnergyStorage ? "发包中储能设备数" : "发包中桩数",
                itemStyle: { color: "#7cb9c2", borderRadius: "3%" },
              },
              {
                value: this.dataObj.businessSignDeviceCount,
                name: this.isEnergyStorage ? "商签中储能设备数" : "商签中桩数",
                itemStyle: { color: "#5d86e5", borderRadius: "3%" },
              },
            ];
            this.TimeBarObj = {
              yAxis: [
                "上线平均耗时",
                "竣工验收平均耗时",
                "施工平均耗时",
                "开工申请审批平均耗时",
                "开工申请提交平均耗时",
                "采购发包平均耗时",
                "采购发包通知平均耗时",
                "投建协议商服复审平均耗时",
                "投建协议双章平均耗时",
                "投建协议运管审批平均耗时",
                "投建协议运管提交平均耗时",
                "投建协议商服审核平均耗时",
                "投建协议提交平均耗时",
                "踏勘平均耗时",
              ],
              series: [
                {
                  name: "单位：h",
                  data: [
                    this.dataObj.onlineDurationAvg,
                    this.dataObj.acceptanceDurationAvg,
                    this.dataObj.constructionDurationAvg,
                    this.dataObj.workApplyApprovalDurationAvg,
                    this.dataObj.workApplySubmitDurationAvg,
                    this.dataObj.supplierOrderApprovalDurationAvg,
                    this.dataObj.supplierOrderNoticeDurationAvg,
                    this.dataObj.agreementStampApprovalDurationAvg,
                    this.dataObj.agreementStampDurationAvg,
                    this.dataObj.consAgreementApprovalDurationAvg,
                    this.dataObj.consAgreementSubmitDurationAvg,
                    this.dataObj.sfAgreementApprovalDurationAvg,
                    this.dataObj.sfAgreementSubmitDurationAvg,
                    this.dataObj.surveyDurationAvg,
                  ],
                },
              ],
            };
            this.offsetBarObj = {
              xAxis: ["商务总成本", "设备总成本", "施工总成本"],
              series: [
                {
                  name: "预计",
                  data: [
                    this.dataObj.businessPlanCostAll,
                    this.dataObj.devicePlanCostAll,
                    this.dataObj.constructionPlanCostAll,
                  ],
                },
                {
                  name: "实际",
                  data: [
                    this.dataObj.businessRealCostAll,
                    this.dataObj.deviceRealCostAll,
                    this.dataObj.constructionRealCostAll,
                  ],
                },
              ],
            };
            this.MainCableBarObj = {
              yAxis: ["长度"],
              series: [
                {
                  name: "平均主缆预计使用长度",
                  data: [this.dataObj.mainCableLengthPlanAvg],
                  barWidth: "50px",
                },
                {
                  name: "平均主缆实际使用长度",
                  data: [this.dataObj.mainCableLengthRealAvg],
                  barWidth: "50px",
                },
              ],
            };
            this.pileBarObj = {
              yAxis: ["成本"],
              series: [
                {
                  name: "平均单桩预计施工成本",
                  data: [this.dataObj.singlePilePlanCostAvg],
                  barWidth: "50px",
                },
                {
                  name: "平均单桩实际施工成本",
                  data: [this.dataObj.singlePileRealCostAvg],
                  barWidth: "50px",
                },
              ],
            };
            this.wattBarObj = {
              yAxis: ["成本"],
              series: [
                {
                  name: "平均单瓦施工预计成本",
                  data: [this.dataObj.singleTilePlanCostAvg],
                  barWidth: "50px",
                },
                {
                  name: "平均单瓦施工实际成本",
                  data: [this.dataObj.singleTileRealCostAvg],
                  barWidth: "50px",
                },
              ],
            };
            this.businessBarObj = {
              yAxis: ["成本"],
              series: [
                {
                  name: "平均单桩商务预计总成本",
                  data: [this.dataObj.singlePileBusinessPlanCostAvg],
                  barWidth: "50px",
                },
                {
                  name: "平均单桩商务实际总成本",
                  data: [this.dataObj.singlePileBusinessRealCostAvg],
                  barWidth: "50px",
                },
              ],
            };
          } else {
            this.initData();
          }
        })
        .catch(() => {
          this.loading = false;
          this.initData();
        });
    },
    initData() {
      this.dataObj = {
        totalCount: 0,
        totalDeviceCount: 0,
        inTransitCount: 0,
        inTransitDeviceCount: 0,
        projectConstructDurationAvg: 0,
      };
      this.projectPieList = [];
      this.pilePieList = [];
      this.projectTypePieList = [];
      this.pileTypePieList = [];
      this.TimeBarObj = {};
      this.offsetBarObj = {};
      this.MainCableBarObj = {};
      this.pileBarObj = {};
      this.wattBarObj = {};
      this.businessBarObj = {};
    },
  },
};
</script>

<style scoped lang="less">
.common-box {
  padding: 0 20px;
}
.common-flex {
  display: flex;
  flex-wrap: wrap;
  &-item {
    flex: 1;
  }
}
.detail-box {
  display: grid;
  grid-template-columns: 50% 50%;
  &-item {
    padding: 20px;
  }
}
.cost-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}

/deep/ .el-statistic {
  .con {
    align-items: baseline;
    .number {
      font-size: 48px;
      color: rgb(2, 156, 124);
      line-height: 70px;
    }
    .suffix {
      font-size: 18px;
      line-height: 26px;
      color: rgb(16, 16, 16);
    }
  }
  .head .title {
    font-size: 18px;
    color: rgb(16, 16, 16);
    line-height: 26px;
  }
}
</style>
