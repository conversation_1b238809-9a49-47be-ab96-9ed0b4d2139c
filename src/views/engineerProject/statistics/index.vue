// 数据看板
<template>
  <div>
    <div class="box-menu">
      <h3>工程项目数据看板</h3>
      <!-- <el-button @click="jumpToProject" type="primary">场站项目汇总</el-button> -->
      <!-- <el-radio-group v-model="activeName" style="margin: 20px;">
        <el-radio-button label="order">工程项目数据看板</el-radio-button>
      </el-radio-group> -->
    </div>
    <el-radio-group
      v-model="tabActiveTab"
      style="margin:0 20px 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in topTabList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <div v-if="tabActiveTab">
      <div class="form-content">
        <el-form :model="form" ref="form">
          <el-row>
            <el-col :span="12">
              <el-form-item label="省市" prop="region" label-width="120px">
                <CustomCascader
                  v-model="form.region"
                  :options="regionData"
                  :props="{
                    checkStrictly: true,
                    value: 'areaCode',
                    label: 'areaName',
                  }"
                  placeholder="请选择省市"
                  clearable
                  change-on-select
                  @change="handleOrgChange"
                  style="width: 100%"
                ></CustomCascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属组织" prop="orgNo" label-width="120px">
                <!-- <el-select
              v-model="form.orgNo"
              placeholder="请选择数据范围"
              clearable
              size="mini"
              style="width: 100%"
              @change="queryPageData"
            >
              <el-option
                v-for="dict in dataRangeOptions"
                :key="dict.stationId"
                :label="dict.stationName"
                :value="dict.stationId"
            /></el-select> -->
                <treeselect
                  v-model="form.orgNo"
                  :options="deptOptions"
                  placeholder="请选择组织"
                  @select="handleSelect"
                  :beforeClearAll="beforeClearAll"
                  :default-expand-level="1"
                  :normalizer="normalizer"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24" style="margin-top: 20px;">
              <el-form-item label="时间" prop="timeRange" label-width="120px">
                <!-- <el-col :span="8"> -->
                <el-date-picker
                  v-model="form.timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="截止日期"
                  value-format="yyyy-MM-dd"
                  @change="queryPageData"
                ></el-date-picker>
                <!-- </el-col>
                  <el-col :span="12"> -->
                <el-radio-group
                  v-model="form.timeRange"
                  @change="queryPageData"
                >
                  <el-radio-button
                    v-for="(x, i) in timeArr"
                    :label="x.date"
                    :key="i"
                    >{{ x.title }}</el-radio-button
                  >
                </el-radio-group>
                <!-- </el-col> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <MaintenanceOrder ref="maintenanceOrder"></MaintenanceOrder>
    </div>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import moment from "moment";
import { listDept } from "@/api/common";
import Treeselect from "@riophae/vue-treeselect";
import { queryCityTree } from "@/api/common.js";
import checkPermission from "@/utils/permission.js";
import MaintenanceOrder from "./components/maintenanceOrder";
export default {
  components: { MaintenanceOrder, Treeselect },
  data() {
    return {
      tabActiveTab: "charge",
      topTabList: [],
      topTabDict: [
        {
          value: "charge",
          label: "充电业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:chargeTab",
            ]);
          },
        },
        {
          value: "storage",
          label: "储能业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:storageTab",
            ]);
          },
        },
      ],
      activeName: "order",
      form: { orgNo: undefined, timeRange: [] },
      dataRangeOptions: [],
      deptOptions: [],
      regionData: [],
    };
  },
  computed: {
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD 00:00:00"),
            moment().format("YYYY-MM-DD 23:59:59"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近一年",
          date: [
            moment()
              .subtract(12, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
  },
  watch: {
    tabActiveTab: {
      handler(val) {
        if (val) {
          this.queryPageData();
        }
      },
    },
  },
  created() {
    this.topTabList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.topTabList[0]?.value || "";
    this.getTreeselect();
    this.getCityRegionData();
    this.form.timeRange = [
      moment()
        .subtract(12, "months")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
  },
  mounted() {
    this.queryPageData();
  },
  methods: {
    checkPermission,
    //跳转至场站项目列表
    jumpToProject() {
      this.$router.push({ path: "/engineerProject/station" });
    },
    handleOrgChange(val) {
      this.form.region = val;
      this.queryPageData();
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
        console.log(this.deptOptions, "deptOptions");
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    //去除children为空的children字段
    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children) {
          newItem.children = this.cleanTree(newItem.children);
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }
        return newItem;
      });
    },
    handleSelect(item) {
      this.form.orgNo = item.deptId;
      this.queryPageData();
    },
    beforeClearAll() {
      this.form.orgNo = undefined;
      this.queryPageData();
    },
    queryPageData() {
      let params = {
        ...this.form,
        businessType: this.tabActiveTab === "charge" ? "1" : "2",
      };
      //选了省
      if (params.region?.length === 1) {
        params["provinceList"] = [params.region[0]];
        params["provinceNameList"] = [
          this.regionData.find((x) => x.areaCode === params.region[0])
            ?.areaName,
        ];
      }
      //选了市 单独只传市
      if (params.region?.length === 2) {
        params["cityList"] = [params.region[1]];
        params["cityNameList"] = [
          this.regionData
            .find((x) => x.areaCode === params.region[0])
            ?.children?.find((i) => i.areaCode === params.region[1])?.areaName,
        ];
      }
      delete params.region;
      if (this.form.timeRange?.length > 0) {
        params["startTime"] = moment(this.form.timeRange[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        params["endTime"] = moment(this.form.timeRange[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }

      this.$refs.maintenanceOrder?.queryData(params);

      console.log("----params----", params);
    },
  },
};
</script>

<style lang="less" scoped>
.box-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}
.form-content {
  padding: 0 20px;
  margin-bottom: 18px;
  margin-right: 100px;
  /deep/ .el-form {
    display: flex;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 10px;
  }
  /deep/ .el-date-editor .el-range-separator {
    width: 7%;
  }
  /deep/ .el-form-item--small.el-form-item {
    margin-bottom: 0;
    // margin-right: 20px;
    // display: flex;
  }
  /deep/ .vue-treeselect__control {
    table-layout: auto;
    height: 32px !important;
  }
  /deep/ .el-date-editor--daterange.el-input__inner {
    width: 250px;
  }
}
</style>
