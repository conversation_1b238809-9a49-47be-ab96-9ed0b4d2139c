<!-- //项目投建批次列表 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
      :disabled="loading"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in topTabList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      tabType="card"
      v-if="tabActiveTab"
    >
      <template #toolbar_buttons> </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['engineerProject:projectCost:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="fold_header" slot-scope="{ row, column }">
        <div @click="handleCollapse(column.property)" style="cursor:pointer">
          <span>{{ column.title }}</span>
          <i
            :class="
              foldObj[column.property]
                ? 'el-icon-caret-right'
                : 'el-icon-caret-left'
            "
          ></i>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/engineerProject/projectCost.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";
import moment from "moment";
import dictMixin from "../dictMixin";
export default {
  name: "projectCost",
  mixins: [exportMixin, dictMixin],
  data() {
    return {
      // activeTab: "全部",
      //buse参数-s
      tabActiveTab: "charge",
      topTabList: [],
      topTabDict: [
        {
          value: "charge",
          label: "充电业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:chargeTab",
            ]);
          },
        },
        {
          value: "storage",
          label: "储能业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:storageTab",
            ]);
          },
        },
      ],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "groupId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [{ id: 1, stationName: "1" }],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "orderNotification",
      //buse参数-e
      modalOrderTypeOptions: [],
      projectStatusOptions: [],
      selectedData: [],
      foldObj: {
        baseInfo: false,
        timeInfo: false,
        costInfo: false,
        statusInfo: false,
      },
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        if (val) {
          this.handleQuery();
        }
      },
    },
  },
  created() {
    this.topTabList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.topTabList[0]?.value || "";
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    this.getDicts("project_status").then((response) => {
      this.projectStatusOptions = response.data;
    });
    this.$nextTick(() => {
      this.loadData();
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    getTagType(projectType) {
      const arr = [
        { type: "success", status: "新建" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
    toDetail(row) {
      this.$router.push({
        name: "investBatchList",
        params: {
          surveyCodeMain: row.surveyCodeMain,
          surveyCodeSub: row.surveyCodeSub,
          businessType: row.businessType,
          tabActiveTab: row.businessType == "2" ? "storage" : "charge",
        },
      });
    },

    handleCollapse(type) {
      this.foldObj[type] = !this.foldObj[type];
    },

    checkPermission,

    handleExport() {
      const params = {
        ...this.params,
        businessType: this.tabActiveTab === "charge" ? "1" : "2",
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleCommonExport(api.exportCostList, params);
    },

    async loadData() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        businessType: this.tabActiveTab === "charge" ? "1" : "2",
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.loading = true;
      const res = await api.queryCostList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      api[crudOperationType](formParams).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
  },
  computed: {
    tableColumn() {
      return this.tabActiveTab === "charge"
        ? this.chargeColumn
        : this.storageColumn;
    },
    storageColumn() {
      return [
        {
          field: "businessTypeName",
          title: "业务类型",
          width: 150,
        },
        {
          field: "stationName",
          fixed: "left",
          title: "场站名称",
          width: 180,
        },
        { field: "stationLocation", title: "场站位置", width: 150 },
        {
          field: "surveyCodeMain",
          title: "踏勘主编码",
          width: 220,
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toDetail(row),
                  }}
                >
                  {row.surveyCodeMain}
                </el-link>,
              ];
            },
          },
        },
        {
          field: "surveyCodeSub",
          title: "踏勘子编码",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toDetail(row),
                  }}
                >
                  {row.surveyCodeSub}
                </el-link>,
              ];
            },
          },
        },
        {
          field: "projectType",
          title: "项目类型",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-tag
                  type={this.getTagType(row.projectType)}
                  size="medium"
                  effect="plain"
                  style={"font-size: 16px;"}
                >
                  {row.projectType}
                </el-tag>,
              ];
            },
          },
        },

        { field: "constructType", title: "投建类型", width: 100 },
        { field: "stationCode", title: "场站编码", width: 180 },
        { field: "projectCode", title: "项目编码", width: 150 },
        { field: "storageType", title: "储能类型", width: 100 },
        {
          field: "transformerCapacityTotal",
          title: "变压器总容量(kVA)",
          width: 100,
        },
        {
          field: "storageDevicesCount",
          title: "储能设备数(台)",
          width: 100,
        },
        { field: "totalCapacity", title: "总容量(kWh)", width: 100 },
        {
          field: "meshCabinetsCount",
          title: "并网柜数量(台)",
          width: 100,
        },
        {
          field: "constructionTotalCost",
          title: "施工预计总价(元)",
          width: 150,
        },
        { field: "consRealTotalPrice", title: "施工实际总价(元)", width: 150 },
        {
          field: "mainCableRealLength",
          title: "线缆实际长度（米）",
          width: 150,
        },
        {
          field: "deviceTotalCost",
          title: "设备总成本(元)",
          width: 150,
        },
        {
          field: "totalAmount",
          title: "施工总金额(元)",
          width: 150,
        },
        {
          field: "paidAmount",
          title: "已付金额(元)",
          width: 150,
        },
        {
          field: "unpaidAmount",
          title: "未付金额(元)",
          width: 150,
        },
        {
          field: "paidRatio",
          title: "已付比例(%)",
          width: 150,
        },
      ];
    },
    chargeColumn() {
      return [
        {
          field: "businessTypeName",
          title: "业务类型",
          width: 150,
        },
        {
          field: "stationName",
          fixed: "left",
          title: "场站名称",
          width: 180,
        },
        { field: "stationLocation", title: "场站位置", width: 150 },
        {
          field: "surveyCodeMain",
          title: "踏勘主编码",
          width: 220,
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toDetail(row),
                  }}
                >
                  {row.surveyCodeMain}
                </el-link>,
              ];
            },
          },
        },
        {
          field: "surveyCodeSub",
          title: "踏勘子编码",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toDetail(row),
                  }}
                >
                  {row.surveyCodeSub}
                </el-link>,
              ];
            },
          },
        },
        {
          field: "projectType",
          title: "项目类型",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-tag
                  type={this.getTagType(row.projectType)}
                  size="medium"
                  effect="plain"
                  style={"font-size: 16px;"}
                >
                  {row.projectType}
                </el-tag>,
              ];
            },
          },
        },

        { field: "constructType", title: "投建类型", width: 100 },
        { field: "stationCode", title: "场站编码", width: 180 },
        { field: "projectCode", title: "项目编码", width: 150 },
        {
          field: "projectStatusName",
          title: "项目状态",
          width: 150,
        },
        { field: "orgName", title: "归属大区", width: 150 },

        { field: "totalPileCount", title: "设备数", width: 150 },
        { field: "totalPower", title: "总功率(kW)", width: 150 },
        { field: "consTeamName", title: "施工队", width: 200 },
        {
          field: "completionReportPassTime",
          title: "竣工报告审核通过时间",
          width: 200,
        },
        {
          field: "consExpectTotalPrice",
          title: "施工预计总价(元)",
          width: 150,
        },
        { field: "consRealTotalPrice", title: "施工实际总价(元)", width: 150 },
        { field: "pileExpectCost", title: "单桩施工预计成本(元)", width: 150 },
        { field: "pileRealCost", title: "单桩施工实际成本(元)", width: 150 },

        {
          field: "tileExpectCost",
          title: "单瓦施工预计成本(元)",
          width: 150,
        },
        { field: "tileRealCost", title: "单瓦施工实际成本(元)", width: 150 },
        {
          field: "mainCableExpectLength",
          title: "主缆预计长度（米）",
          width: 150,
        },
        {
          field: "mainCableRealLength",
          title: "主缆实际长度（米）",
          width: 150,
        },
        {
          field: "deviceTotalCostReal",
          title: "设备实际总成本(元)",
          width: 150,
        },
        {
          field: "totalBusinessCostReal",
          title: "商务实际总成本(元)",
          width: 150,
        },
        {
          field: "projectTotalCost",
          title: "项目实际总成本(元)",
          width: 150,
        },
        {
          field: "paidAmount",
          title: "已付金额(元)",
          width: 150,
        },
        {
          field: "unpaidAmount",
          title: "未付金额(元)",
          width: 150,
        },
        {
          field: "paidRatio",
          title: "已付比例(%)",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "场站编码",
          },
          {
            field: "stationName",
            element: "el-input",
            title: "场站名称",
          },
          {
            field: "region",
            title: "所在区域",
            element: "custom-cascader",
            attrs: {
              options: regionData,
              filterable: true,
              clearable: true,
              collapseTags: true,
              props: {
                checkStrictly: true,
                multiple: true,
                collapseTags: true,
              },
            },
          },
          {
            field: "surveyCodeMain",
            element: "el-input",
            title: "踏勘编码",
          },
          {
            field: "projectStatus",
            title: "项目状态",
            element: "el-select",
            props: {
              options: this.projectStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "orgNo",
            element: "el-select",
            title: "归属大区",
            props: {
              options: this.allOptions.deptAllOptionList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "projectCode",
            element: "el-input",
            title: "项目编码",
          },
          {
            field: "surveyCodeSub",
            element: "el-input",
            title: "踏勘子编码",
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        menu: false,
        addBtn: false,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
</style>
