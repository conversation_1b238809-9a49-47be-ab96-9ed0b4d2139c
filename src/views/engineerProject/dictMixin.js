import { getAllDeptList } from "@/api/operationWorkOrder/index.js";
import api from "@/api/engineerProject/investBatchList/index";

const dictMaps = {
  project_business_type: [], //业务类型
  project_status: [], //项目状态
  work_apply_status: [], //开工申请状态
  project_process_node: [], //项目节点
  construct_type: [], //投建类型
  device_order_status: [], //设备下单状态
  cm_sub_type: [], // 设备类型
  install_mode: [], // 安装方式
};

export default {
  data() {
    return {
      allOptions: {
        deptAllOptionList: [], //大区列表
        managerList: [], //项目经理列表
      },
    };
  },
  mounted() {
    // 字典
    const dictsArr = Object.keys(dictMaps);
    for (let index = 0; index < dictsArr.length; index++) {
      const dictName = dictsArr[index];

      this.getDicts(dictName).then((response) => {
        this.$set(this.allOptions, dictName, response.data);
      });
    }

    // 大区
    getAllDeptList({}).then((res) => {
      this.$set(
        this.allOptions,
        "deptAllOptionList",
        res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        })
      );
    });
    // 工程经理列表
    api.listRoleAccount({ roleKey: "projectManage" }).then((res) => {
      this.allOptions.roleAllOptionList = res.data;

      this.$set(
        this.allOptions,
        "managerList",
        res.data?.map((x) => {
          return { ...x, value: x.userId, label: x.nickName };
        })
      );
    });
  },
  methods: {
    format(dictName, data) {
      if (this.allOptions[dictName]) {
        return this.selectDictLabel(this.allOptions[dictName], data);
      }
      return "";
    },
  },
};
