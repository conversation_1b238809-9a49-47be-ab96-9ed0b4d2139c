<!-- 场站详情 -->
<template>
  <div class="app-container">
    <h3>{{ stationName }}</h3>
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="item in tabList"
        :key="item.name"
      >
        <template v-if="item.data.length">
          <el-card v-for="(i, k) in item.data" :key="k">
            <LineTitle
              :title="i.title"
              fontSize="18"
              v-if="i.title"
            ></LineTitle>
            <el-button
              type="text"
              class="desc"
              v-if="i.desc"
              @click="jump(i.data)"
            >
              <div v-html="i.desc"></div>
            </el-button>
            <component
              :is="i.component"
              v-bind="{
                config: i.config,
                data: i.data,
                dicts: allOptions,
                ...(i.attrs || {}),
              }"
            ></component>
          </el-card>
        </template>
        <el-empty v-else></el-empty>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as allConfig from "./config";
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import dictMixin from "../dictMixin";
export default {
  components: { LineTitle },
  mixins: [dictMixin],
  props: {
    stationName: {
      type: String,
      default: "场站详情",
    },
  },
  data() {
    return {
      activeTab: "base",
      data: {}, // 基础信息+明细
      deviseList: [], // 设备明细
    };
  },
  computed: {
    tabList() {
      return [
        // -------------基本信息
        {
          label: "基本信息",
          name: "base",

          data: [
            {
              title: "场站信息汇总",
              data: this.data,
              ...allConfig.baseConfig,
            },
          ],
        },
        // -------------投建信息
        {
          label: "投建信息",
          name: "construction",
          data: (this.data?.projectBatchList || []).map((i, k) => ({
            ...allConfig.constructionConfig,
            title: k === 0 ? "项目投建明细" : "",
            desc: `项目编号：${i.projectCode}`,
            data: i,
          })),
        },
        // -------------设备信息
        {
          label: "设备信息",
          name: "device",
          data: [
            // 汇总
            {
              title: "设备信息汇总",
              data: this.data,
              ...allConfig.deviceProjectInfoConfig,
            },

            // 设备明细
            {
              title: "设备明细",
              data: [...this.deviseList],
              ...allConfig.deviceDetailConfig,
            },

            // 项目设备明细
            ...(this.data?.projectBatchList || []).map((i, k) => ({
              title: k === 0 ? "项目设备明细" : "",
              desc: `项目编号：${i.projectCode}` + this.tag(i.projectType, 0),
              data: i,
              ...allConfig.deviceProjectInfoConfig,
            })),
          ],
        },
        // ------------成本信息
        {
          label: "成本信息",
          name: "cost",
          data: [
            {
              title: "场站成本汇总",
              data: this.data,
              ...allConfig.costConfig,
            },

            // 成本明细
            ...(this.data?.projectBatchList || []).map((i, k) => ({
              title: k === 0 ? "场站成本明细" : "",
              desc: `项目编号：${i.projectCode}`,
              data: i,
              ...allConfig.costlistConfig,
            })),
          ],
        },
      ];
    },
  },

  mounted() {
    this.loadData();
    this.loadDeviceList();
  },
  methods: {
    jump(i) {
      this.$router.push({
        path: "/engineerProject/investBatchList/detail",
        query: {
          projectBatchId: i.projectBatchId,
          projectCode: i.projectCode,
          stationName: i.stationName,
        },
      });
    },
    async loadData() {
      this.loading = true;
      const res = await api.queryStationProjectDetail({
        stationId: this.$route.query.stationId,
      });
      if (res.code === "10000") {
        this.data = res.data;
      }
    },
    // 设备明细
    async loadDeviceList() {
      this.loading = true;
      const res = await api.deviceList({
        stationCode: this.$route.query.stationCode,
        installedFlag: "0",
      });
      if (res.code === "10000") {
        this.deviseList = res.data;
      }
    },
    tag(name, type) {
      return `<span class="tag-${type}">${name}</span>`;
    },
  },
};
</script>

<style lang="less" scoped>
.desc {
  color: #029c7c;
  margin-bottom: 12px;
}

.tag {
  padding: 2px 8px;
  margin-left: 12px;
  border-width: 1px;
  border-style: solid;
}
/deep/ .tag {
  &-2 {
    color: rgb(255, 77, 77);
    border-color: rgb(255, 77, 77);
    .tag;
  }
  &-0 {
    color: rgb(2, 114, 106);
    border: 1px solid rgb(2, 114, 106);
    padding: 2px 8px;
    margin-left: 12px;
  }
  &-1 {
    color: rgb(248, 129, 10);
    border: 1px solid rgb(248, 129, 10);
    padding: 2px 8px;
    margin-left: 12px;
  }
}
</style>
