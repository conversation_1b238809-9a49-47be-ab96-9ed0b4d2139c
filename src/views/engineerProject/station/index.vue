<!-- //项目投建批次列表 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      :tabRadioList="tabRadioList"
      @tabRadioChange="tabRadioChange"
      tabType="card"
    >
      <template #toolbar_buttons> </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['engineerProject:station:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="fold_header" slot-scope="{ row, column }">
        <div @click="handleCollapse(column.property)" style="cursor:pointer">
          <span>{{ column.title }}</span>
          <i
            :class="
              foldObj[column.property]
                ? 'el-icon-caret-right'
                : 'el-icon-caret-left'
            "
          ></i>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/engineerProject/investBatchList/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";
import moment from "moment";
import dictMixin from "../dictMixin";
export default {
  name: "engineerStationPage",
  mixins: [exportMixin, dictMixin],
  data() {
    return {
      activeTab: "全部",
      //buse参数-s

      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "groupId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [{ id: 1, stationName: "1" }],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "orderNotification",
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedData: [],
      foldObj: {
        baseInfo: false,
        timeInfo: false,
        costInfo: false,
        statusInfo: false,
      },
    };
  },

  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    this.$nextTick(() => {
      this.loadData();
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    toProjectlist(row) {
      this.$router.push({
        path: `/engineerProject/investBatchList?stationCode=${row.stationCode}&stationName=${row.stationName}`,
      });
    },
    toDetail(row) {
      this.$router.push({
        path: `/engineerProject/station/detail?stationId=${row.stationId}&stationCode=${row.stationCode}`,
      });
    },
    toProjectDetail(row, item) {
      this.$router.push({
        path: "/engineerProject/investBatchList/detail",
        query: {
          projectBatchId: item.projectBatchId,
          projectCode: item.projectCode,
          stationName: row.stationName,
        },
      });
    },
    tabRadioChange(val) {
      this.activeTab = val;
      this.handleQuery();
    },
    handleCollapse(type) {
      this.foldObj[type] = !this.foldObj[type];
    },

    checkPermission,

    handleExport() {
      const params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
        projectProcessNode: this.activeTab === "全部" ? null : this.activeTab,
        ...(this.params?.surveyPassStartTime
          ? {
              surveyPassStartTime: moment(
                this.params.surveyPassStartTime[0]
              ).format("YYYY-MM-DD 00:00:00"),
              surveyPassEndTime: moment(
                this.params.surveyPassStartTime[1]
              ).format("YYYY-MM-DD 23:59:59"),
            }
          : {}),
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleCommonExport(api.exportStationProject, params);
    },

    async loadData() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        projectProcessNode: this.activeTab === "全部" ? null : this.activeTab,
        ...(this.params?.surveyPassStartTime
          ? {
              surveyPassStartTime: moment(
                this.params.surveyPassStartTime[0]
              ).format("YYYY-MM-DD 00:00:00"),
              surveyPassEndTime: moment(
                this.params.surveyPassStartTime[1]
              ).format("YYYY-MM-DD 23:59:59"),
            }
          : {}),
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.loading = true;
      const res = await api.queryStationProject(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      api[crudOperationType](formParams).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
  },
  computed: {
    tabRadioList() {
      return [
        { value: "全部", label: "全部" },
        ...(this.allOptions?.project_process_node?.map((i) => ({
          value: i.dictValue,
          label: i.dictLabel,
        })) || []),
      ];
    },
    tableColumn() {
      return [
        {
          field: "baseInfo",
          title: "基本信息",
          minWidth: 100,
          children: this.foldObj.baseInfo
            ? []
            : [
                {
                  field: "businessType",
                  title: "业务类型",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.format("project_business_type", cellValue);
                  },
                },
                {
                  field: "stationName",
                  title: "场站名称",
                  width: 180,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-link
                          type="primary"
                          on={{
                            click: () => this.toDetail(row),
                          }}
                        >
                          {row.stationName}
                        </el-link>,
                      ];
                    },
                  },
                },
                { field: "stationLocation", title: "场站位置", width: 150 },
                { field: "surveyCodeMain", title: "踏勘编码", width: 220 },
                { field: "stationCode", title: "场站编码", width: 180 },
                {
                  field: "projectCode",
                  title: "项目编码",
                  width: 180,
                  showOverflow: false,
                  slots: {
                    default: ({ row }) => {
                      return (
                        row.projectCodeList &&
                        [...row.projectCodeList].map((i) => (
                          <span>
                            <el-link
                              type="primary"
                              on={{
                                click: () => this.toProjectDetail(row, i),
                              }}
                            >
                              {i.projectCode}
                            </el-link>
                          </span>
                        ))
                      );
                    },
                  },
                },
                { field: "totalPileCount", title: "设备数", width: 150 },
                { field: "totalPower", title: "总功率(kW)", width: 150 },
                {
                  field: "constructionTotalCost",
                  title: "施工总成本(元)",
                  width: 150,
                },
                {
                  field: "deviceTotalCost",
                  title: "设备总成本(元)",
                  width: 150,
                },
                { field: "paidAmount", title: "已付金额(元)", width: 150 },
                { field: "unpaidAmount", title: "未付金额(元)", width: 150 },
                {
                  field: "projectBatchCount",
                  title: "项目投建批次",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-link
                          type="primary"
                          on={{
                            click: () => this.toProjectlist(row),
                          }}
                        >
                          {row.projectBatchCount}
                        </el-link>,
                      ];
                    },
                  },
                },
                {
                  field: "hasUnfinishedProjectBatch",
                  title: "该场站是否有未处理完成的项目批次",
                  width: 150,
                },
              ],
          slots: {
            header: "fold_header",
          },
        },
        {
          field: "timeInfo",
          title: "项目批次最新消息",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.timeInfo
            ? []
            : [
                { field: "constructType", title: "投建类型", width: 150 },
                { field: "orgName", title: "归属大区", width: 150 },
                {
                  field: "engineeringManagerName",
                  title: "工程经理",
                  width: 150,
                },
                {
                  field: "businessDevelopmentName",
                  title: "商务BD",
                  width: 150,
                },
                {
                  field: "isReportPower",
                  title: "是否报电",
                  width: 150,
                },
                {
                  field: "constructionContractMode",
                  title: "施工发包模式",
                  width: 150,
                },
                {
                  field: "projectProcessNodeName",
                  title: "项目流程节点",
                  width: 150,
                },
                {
                  field: "surveyPassTime",
                  title: "踏勘通过时间",
                  width: 150,
                },
                {
                  field: "surveyDuration",
                  title: "踏勘耗时",
                  width: 150,
                },
                {
                  field: "consAgreementPassTime",
                  title: "投建协议审批通过时间",
                  width: 150,
                },
                {
                  field: "consAgreementApprovalDuration",
                  title: "投建协议审批耗时",
                  width: 150,
                },
                {
                  field: "supplierNotifyTime",
                  title: "施工供应商通知时间",
                  width: 150,
                },
                {
                  field: "supplierOrderPassTime",
                  title: "施工供应商下单审批通过时间",
                  width: 150,
                },
                {
                  field: "supplierOrderApprovalDuration",
                  title: "施工供应商下单审批耗时",
                  width: 150,
                },
                {
                  field: "workApplyTime",
                  title: "开工申请发起时间",
                  width: 150,
                },
                {
                  field: "workApplyPassTime",
                  title: "开工申请通过时间",
                  width: 150,
                },
                {
                  field: "workApplyDuration",
                  title: "开工申请耗时",
                  width: 150,
                },
                {
                  field: "deviceOrderPassTime",
                  title: "设备下单审批通过时间",
                  width: 150,
                },
                {
                  field: "deviceOrderApprovalDuration",
                  title: "设备下单审批耗时",
                  width: 150,
                },
                {
                  field: "consCompleteTime",
                  title: "施工完成时间",
                  width: 150,
                },

                {
                  field: "constructionDuration",
                  title: "施工耗时",
                  width: 150,
                },
                {
                  field: "consCheckPassTime",
                  title: "施工验收通过时间",
                  width: 150,
                },
                {
                  field: "constructionCheckDuration",
                  title: "施工验收耗时",
                  width: 150,
                },
                {
                  field: "completionReportUploadTime",
                  title: "竣工报告上传时间",
                  width: 150,
                },
                {
                  field: "completionReportPassTime",
                  title: "竣工报告审核通过时间",
                  width: 150,
                },
                { field: "onlineTime", title: "上线时间", width: 150 },
                {
                  field: "warrantyExpiresTime",
                  title: "质保到期时间",
                  width: 150,
                },
              ],
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "场站编码",
          },
          {
            field: "stationName",
            element: "el-input",
            title: "场站名称",
          },
          {
            field: "region",
            title: "所在区域",
            element: "custom-cascader",
            attrs: {
              options: regionData,
              filterable: true,
              clearable: true,
              collapseTags: true,
              props: {
                checkStrictly: true,
                multiple: true,
                collapseTags: true,
              },
            },
          },
          {
            field: "surveyCodeMain",
            element: "el-input",
            title: "踏勘编码",
          },
          {
            field: "surveyPassStartTime",
            title: "踏勘通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
            },
          },

          {
            field: "projectCode",
            element: "el-input",
            title: "项目编码",
          },
          {
            field: "orgNo",
            element: "el-select",
            title: "归属大区",
            props: {
              options: this.allOptions.deptAllOptionList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "isReportPower",
            element: "el-select",
            title: "是否报电",
            props: {
              options: [
                { label: "是", value: "是" },
                { label: "否", value: "否" },
              ],
            },
          },

          {
            field: "engineeringManagerName",
            title: "工程经理",
            element: "el-select",
            props: {
              options: this.allOptions.managerList,
              filterable: true,
            },
          },
          {
            field: "businessTypeList",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.allOptions.project_business_type,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              multiple: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        menu: false,
        addBtn: false,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
</style>
