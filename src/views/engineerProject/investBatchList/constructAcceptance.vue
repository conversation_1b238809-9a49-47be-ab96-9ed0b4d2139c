<!-- // 施工验收 -->
<template>
  <WorkCheck :projectId="projectId" :isEnergyStorage="isEnergyStorage" />
</template>

<script>
import WorkCheck from "../components/WorkCheck";
export default {
  components: { WorkCheck },
  data() {
    return {
      loading: false,
      projectId: this.$route.query.projectId,
      isEnergyStorage: JSON.parse(this.$route.query.isEnergyStorage),
    };
  },
  mounted() {},
  methods: {},
};
</script>
