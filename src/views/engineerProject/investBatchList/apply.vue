//开工申请
<template>
  <div class="app-container">
    <h3>开工申请</h3>
    <el-card>
      <CommonTitle class="mb10" title="场地信息" />
      <DynamicForm
        :config="placeForm"
        :params="placeParams"
        ref="placeForm"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="200px"
        :preview="true"
      ></DynamicForm>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="施工队信息" />
      <DynamicForm
        :config="constructionForm"
        :params="constructionParams"
        ref="constructionForm"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="200px"
      >
        <template slot="upload">
          <MultiFileUpload
            ref="attachments"
            @uploadSuccess="updateAttachments"
            accept=".jpg, .png, .doc, .docx, .xls, .xlsx, .pdf"
            :maxSize="20480"
            :limit="20"
          >
            <template #customTip>
              支持批量上传，上传格式支持jpg、png、doc、docx、xls、xlsx、pdf文件，单个文件20G以内
            </template>
          </MultiFileUpload>
        </template>
      </DynamicForm>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="进度计划" />
      <DynamicForm
        :config="planForm"
        :params="planParams"
        ref="planForm"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="200px"
      ></DynamicForm>
    </el-card>
    <div slot="footer" class="dialog-footer">
      <el-button size="medium" :loading="loading" @click="handleCancel"
        >取 消</el-button
      >
      <el-button
        @click="handleSubmit"
        type="primary"
        size="medium"
        :loading="loading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import MultiFileUpload from "@/components/MultipleFileUpload";
import CommonTitle from "@/components/commonTitle";
import { initParams } from "@/utils/buse.js";
import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  components: { CommonTitle, MultiFileUpload },
  data() {
    return {
      loading: false,
      placeParams: {},
      constructionParams: {},
      planParams: {},
      managerOptions: [],
      projectBatchId: "",
      projectCode: "",
      operateType: "add",
      applyId: "",
      isEnergyStorage: false,
    };
  },

  created() {
    this.projectBatchId = this.$route.query.projectBatchId;
    this.projectCode = this.$route.query.projectCode;
    this.operateType = this.$route.query.type;
    this.isEnergyStorage = JSON.parse(this.$route.query.isEnergyStorage);
    this.placeParams = initParams(this.placeForm);
    this.constructionParams = initParams(this.constructionForm);
    this.planParams = initParams(this.planForm);
    // const { type } = this.$route.query;
    this.permissionUserList();
    this.getDetail();
  },
  methods: {
    permissionUserList() {
      api
        .permissionUserList({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.managerOptions = res.data.map((x) => {
            return {
              ...x,
              value: x.userId,
              label: x.userName + "-" + x.nickName,
            };
          });
        });
    },
    getDetail() {
      api.applyDetail({ projectBatchId: this.projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          Object.keys(this.placeParams).forEach((key) => {
            this.placeParams[key] = res.data[key];
          });
          Object.keys(this.constructionParams).forEach((key) => {
            this.constructionParams[key] = res.data[key];
          });
          Object.keys(this.planParams).forEach((key) => {
            this.planParams[key] = res.data[key];
          });
          this.operateType === "edit" && (this.applyId = res.data?.applyId);
          const file = res.data?.constructionFile
            ? JSON.parse(res.data?.constructionFile)
            : [];
          this.$refs.attachments.setAttachments(file);
          this.constructionParams.constructionFile = file;
        }
      });
    },
    handleSubmit() {
      this.$refs.constructionForm.validate((valid) => {
        if (!valid) return;
        this.$refs.planForm.validate((valid) => {
          if (!valid) return;
          this.$confirm("是否确认提交？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              const params = {
                ...this.constructionParams,
                ...this.planParams,
                constructionFile: JSON.stringify(
                  this.constructionParams.constructionFile
                ),
                projectBatchId: this.projectBatchId,
                constructionTeamManagerName: this.managerOptions?.find(
                  (x) =>
                    x.userId ===
                    this.constructionParams.constructionTeamManagerId
                )?.nickName,
                applyId: this.applyId,
              };
              api.saveApply(params).then((res) => {
                if (res?.code === "10000") {
                  this.$message.success("提交成功");
                  this.$store.dispatch("tagsView/delView", this.$route);
                  this.handleCancel();
                }
              });
            })
            .catch(() => {});
        });
      });
    },
    handleCancel() {
      this.$router.go(-1);
    },
    updateAttachments(attachments) {
      this.constructionParams.constructionFile = attachments;
    },
  },
  computed: {
    placeForm() {
      return [
        {
          field: "stationCode",
          title: "场站编码：",
        },
        {
          field: "stationName",
          title: "场站名称：",
        },
        {
          field: "stationAddress",
          title: "场站位置：",
        },
      ];
    },
    constructionForm() {
      return [
        {
          field: "consTeamName",
          title: "施工队名称：",
          preview: true,
        },
        {
          field: "consTeamLeaderName",
          title: "施工队负责人：",
          preview: true,
        },
        {
          field: "constructionTeamManagerId",
          title: "施工队项目经理：",
          element: "el-select",
          props: {
            options: this.managerOptions,
            placeholder: "请选择施工队项目经理",
            filterable: true,
            clearable: true,
          },
          rules: [
            {
              required: true,
              message: "请选择施工队项目经理",
              trigger: "change",
            },
          ],
          on: {
            change: (val) => {
              const phone = this.managerOptions?.find((x) => x.value == val)
                .phonenumber;
              this.constructionParams.constructionTeamManagerTel = phone;
            },
          },
        },
        {
          field: "constructionTeamManagerTel",
          title: "施工队项目经理联系方式：",
          attrs: {
            placeholder: "请输入施工队项目经理联系方式",
          },
          rules: [
            {
              required: true,
              message: "请输入施工队项目经理联系方式",
              trigger: "change",
            },
          ],
        },
        {
          field: "constructionFile",
          element: "slot",
          title: "施工合同（上传附件）：",
          slotName: "upload",
          defaultValue: [],
          rules: [
            {
              required: !this.isEnergyStorage,
              message: "请上传",
              trigger: "change",
            },
          ],
        },
        // {
        //   field: "contractWarrantyPeriod",
        //   element: "el-date-picker",
        //   title: "合同质保期：",
        //   rules: [
        //     { required: true, message: "请选择合同质保期", trigger: "change" },
        //   ],
        //   props: { valueFormat: "yyyy-MM-dd", placeholder: "请选择合同质保期" },
        // },
      ];
    },
    planForm() {
      return [
        {
          field: "scheduledStartTime",
          element: "el-date-picker",
          title: "计划开工时间：",
          rules: [
            {
              required: true,
              message: "请选择计划开工时间",
              trigger: "change",
            },
          ],
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择计划开工时间",
          },
        },
        {
          field: "scheduledEndTime",
          element: "el-date-picker",
          title: "计划竣工时间：",
          rules: [
            {
              required: true,
              message: "请选择计划竣工时间",
              trigger: "change",
            },
          ],
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择计划竣工时间",
          },
        },
        {
          field: "equipmentArrivalTime",
          element: "el-date-picker",
          title: "设备需求到场时间：",
          rules: [
            {
              required: true,
              message: "请选择设备需求到场时间",
              trigger: "change",
            },
          ],
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择设备需求到场时间",
          },
        },
        {
          field: "equipmentDeliveryAddress",
          title: "设备收货地址：",
          rules: [
            {
              required: true,
              message: "请输入设备收货地址",
              trigger: "change",
            },
          ],
          attrs: {
            placeholder: "请输入设备收货地址",
            maxlength: 100,
          },
        },
        {
          field: "consigneeName",
          title: "收货人姓名：",
          rules: [
            {
              required: true,
              message: "请输入收货人姓名",
              trigger: "change",
            },
          ],
          attrs: {
            placeholder: "请输入收货人姓名",
            maxlength: 100,
          },
        },
        {
          field: "consigneeTel",
          title: "收货人电话：",
          rules: [
            {
              required: true,
              message: "请输入收货人电话",
              trigger: "change",
            },
          ],
          attrs: {
            placeholder: "请输入收货人电话",
            maxlength: 50,
          },
        },
        {
          field: "remark",
          title: "备注：",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "请输入备注",
          },
        },
      ];
    },
  },
};
</script>

<style lang="less" scoped>
.el-card.is-always-shadow {
  margin-bottom: 10px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}
</style>
