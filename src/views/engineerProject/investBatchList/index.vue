//项目投建批次列表
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      @input="handleReset"
      style="margin-bottom: 20px;"
      size="medium"
      :disabled="loading"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in topTabList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      :tabRadioList="tabRadioList"
      @tabRadioChange="tabRadioChange"
      tabType="card"
      class="buse-curd"
      v-if="tabActiveTab"
    >
      <template #toolbar_buttons>
        <!-- <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['maintenance:timeConfig:add']"
          >新增</el-button
        > -->
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['engineerProject:investBatchList:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="fold_header" slot-scope="{ row, column }">
        <div style="cursor:pointer" @click="handleCollapse(column.property)">
          <span>{{ column.title }}</span>
          <i
            :class="
              foldObj[column.property]
                ? 'el-icon-caret-right'
                : 'el-icon-caret-left'
            "
          ></i>
        </div>
      </template>
      <template #deviceDemandList="{ item, params }">
        <EditTable ref="editTable" v-model="params[item.field]"></EditTable>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'check'">
          <el-button @click="submitCheck(row, false)" :loading="checkLoading"
            >审核不通过</el-button
          >
          <el-button
            @click="submitCheck(row, true)"
            type="primary"
            :loading="checkLoading"
            >审核通过
          </el-button>
        </div>
        <div
          v-else-if="
            crudOperationType === 'record' || crudOperationType === 'file'
          "
        >
          <el-button @click="handleCancelCustom">关闭</el-button>
        </div>
      </template>
      <!-- 上传竣工报告 -->
      <template #upload="{ row, operationType }">
        <MultiFileUpload
          ref="attachments"
          @uploadSuccess="
            (attachments) => {
              return updateAttachments(attachments, row);
            }
          "
          accept=".pdf"
          :maxSize="20480"
          :limit="50"
        >
          <template #customTip>
            支持批量上传，上传格式支持pdf文件，单个文件20G以内
          </template>
        </MultiFileUpload>
      </template>
      <!-- 审批查询 -->
      <template #record="{ row, operationType }">
        <AutoFilters
          :config="recordConfig"
          @handleSubmit="handleRecordSubmit"
          @handleReset="handleRecordReset"
          :params="recordParams"
          :gridCol="{ span: 8 }"
          layout="right"
          inline
        ></AutoFilters>
        <Timeline
          :list="recordList"
          operateTypeTitle="itemCodeName"
          operatorNameTitle="operateEmpName"
          createTimeTitle="operateTime"
          operateDetailTitle="operateRemark"
        ></Timeline>
      </template>
      <!-- 附件 -->
      <template #file="{ row, operationType }">
        <!-- <el-radio-group v-model="fileActiveName" class="radio-tab">
          <el-radio-button
            v-for="(item, index) in radioList"
            :key="index"
            :label="item.dictValue"
            >{{ item.dictLabel }}</el-radio-button
          >
        </el-radio-group> -->
        <div
          v-for="(item, index) in row.docList"
          :key="index"
          class="file-link"
        >
          <el-link @click="handlePreview(index)">{{ item.name }}</el-link>
        </div>
        <PreviewFiles
          :initial-index="previewIndex"
          v-if="showViewer"
          :on-close="
            () => {
              showViewer = false;
            }
          "
          :url-list="row.docList"
          :fileOptions="{ url: 'url', name: 'name' }"
        />
      </template>
      <template #itemDetail="{row,column}">
        <el-link type="primary" @click="handleItemDetailDialog(row)">
          {{ row[column.property] }}
        </el-link>
      </template>
    </BuseCrud>
    <el-empty v-else></el-empty>
    <CompleteReport ref="completeReport"></CompleteReport>
    <!-- 工序详情 -->
    <WorkCheckDrawer ref="workCheckDrawer"></WorkCheckDrawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/engineerProject/investBatchList/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
// import { regionData } from "element-china-area-data";
import MultiFileUpload from "@/components/MultipleFileUpload";
import { listAllUser, queryCityTree } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import CompleteReport from "./components/completeReport.vue";
import { queryBuildTeamByPage } from "@/api/buildTeam/buildTeam.js";
import WorkCheckDrawer from "./components/workCheckDrawer.vue";
import moment from "moment";
import EditTable from "./components/EditTable.vue";
export default {
  name: "investBatchList",
  components: {
    MultiFileUpload,
    Timeline,
    CompleteReport,
    PreviewFiles,
    EditTable,
    WorkCheckDrawer,
  },
  mixins: [exportMixin],
  data() {
    return {
      tabActiveTab: "charge",
      topTabList: [],
      topTabDict: [
        {
          value: "charge",
          label: "充电业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:chargeTab",
            ]);
          },
        },
        {
          value: "storage",
          label: "储能业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:storageTab",
            ]);
          },
        },
      ],
      checkLoading: false,
      projectStatus: "",
      projectBatchId: "",
      workCheckVis: false,
      scheduledEndTime: "",
      scheduledStartTime: "",
      equipmentArrivalTime: "",
      showViewer: false,
      previewIndex: 0,
      radiusStatus: 0,
      fileActiveName: "01",
      radioList: [
        { dictValue: "01", dictLabel: "踏勘单" },
        { dictValue: "02", dictLabel: "场地双章投建协议" },
        { dictValue: "03", dictLabel: "采购下单" },
        { dictValue: "04", dictLabel: "付款单" },
      ],
      recordList: [],
      recordParams: {},
      businessTypeOptions: [],
      orderTypeOptions: [],
      activeTab: "0",
      //buse参数-s
      tabRadioList: [
        { value: "0", id: "0", label: "全部" },
        { value: "1", id: "1", label: "进行中" },
      ],
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        scrollX: { gt: 100 },
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "projectBatchId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "orderNotification",
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedData: [],
      foldObj: {
        baseInfo: false,
        takan: false,
        shangqian: false,
        fabao: false,
        kaigong: false,
        shigong: false,
        energyBaseInfo: false,
        energyTimeNode: false,
        energyCostInfo: false,
      },
      deptOptionList: [],
      projectStatusOptions: [],
      allProjectStatusOptions: [],
      workApplyStatusOptions: [],
      processNodeOptions: [],
      constructTypeOptions: [],
      deviceSourceTypeList: [],
      engineeringManagerOptions: [],
      consTeamManagerOptions: [],
      consTeamLeaderOptions: [],
      workTypeOptions: [],
      managerOptions: [],
      applyFormTypeOptions: [],
      omApplyNoOptions: [],
      consTeamAuditStatusOptions: [],
      deviceOrderStatusOptions: [],
      constructionTeamList: [],
      consUserOptions: [],
      isEnergyStorage: false,
      projectAttributeOptions: [],
    };
  },

  watch: {
    // tabActiveTab: {
    //   handler(val) {
    //     this.handleReset();
    //   },
    // },
  },
  created() {
    this.topTabList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.topTabList[0]?.value || "";
    this.params = initParams(this.filterOptions.config);
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    this.params.stationCode = this.$route.query.stationCode || null;
    this.params.stationName = this.$route.query.stationName || null;
    this.getDeptList();
    this.getDicts("device_source_type").then((response) => {
      this.deviceSourceTypeList = response.data;
    });
    this.getDicts("apply_form_type").then((response) => {
      this.applyFormTypeOptions = response.data;
    });
    this.getDicts("project_attribute").then((response) => {
      this.projectAttributeOptions = response.data;
    });
    this.getManagerOptions();
    this.listAllUser();
    this.getConsUserOptions();
    this.getConsTeamOptions();
    this.getCityRegionData();
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    this.params.stationCode =
      this.$route.query.stationCode || this.params.stationCode;
    this.params.stationName =
      this.$route.query.stationName || this.params.stationName;
    this.tabActiveTab = this.$route.params.tabActiveTab || this.tabActiveTab;
    this.getDicts("project_process_node").then((response) => {
      this.processNodeOptions = response.data;
    });
    this.getDicts("construct_type").then((response) => {
      this.constructTypeOptions = response.data;
    });
    this.getDicts("cons_team_audit_status").then((response) => {
      this.consTeamAuditStatusOptions = response.data;
    });
    this.getDicts("device_order_status").then((response) => {
      this.deviceOrderStatusOptions = response.data;
    });
    this.loading = true;
    Promise.all([
      this.getDicts("project_business_type").then((response) => {
        this.businessTypeOptions = response.data;
      }),
      this.getDicts("project_status").then((response) => {
        this.allProjectStatusOptions = response.data;
        this.projectStatusOptions = this.allProjectStatusOptions;
      }),
      this.getDicts("work_apply_status").then((response) => {
        this.workApplyStatusOptions = response.data;
      }),
    ]).then(() => {
      this.$nextTick(() => {
        this.loadData();
      });
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    getConsTeamOptions() {
      queryBuildTeamByPage({
        pageNum: 1,
        pageSize: 99999,
        status: "0",
      }).then((res) => {
        this.constructionTeamList = res.data;
      });
    },
    getConsUserOptions() {
      api.listRoleAccount({ roleKey: "" }).then((res) => {
        this.consUserOptions = res.data?.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
    },
    //打开工序项详情
    handleItemDetailDialog(row) {
      this.$refs.workCheckDrawer.open(row);
    },
    //指定施工队
    handleSetTeam(row) {
      this.operationType = "setTeam";

      this.$refs.crud.switchModalView(true, "setTeam", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        consTeamName: row.consTeamCode,
        initialConsCode: row.consTeamCode,
      });
    },
    handlePreview(index) {
      this.previewIndex = index;
      this.showViewer = true;
    },
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    //获取转派-施工队绑定的项目经理
    getProjectManagerOptions(projectBatchId) {
      api.permissionUserList({ projectBatchId: projectBatchId }).then((res) => {
        this.managerOptions = res.data?.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName + "-" + (x.phonenumber || ""),
          };
        });
      });
    },
    //获取经理角色下拉选项
    getManagerOptions() {
      api.listRoleAccount({ roleKey: "projectManage" }).then((res) => {
        this.engineeringManagerOptions = res.data?.map((x) => {
          return {
            value: x.nickName,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
      api.listRoleAccount({ roleKey: "consTeamProjectManager" }).then((res) => {
        this.consTeamManagerOptions = res.data?.map((x) => {
          return {
            value: x.nickName,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
      api.listRoleAccount({ roleKey: "consTeamLeader" }).then((res) => {
        this.consTeamLeaderOptions = res.data?.map((x) => {
          return {
            value: x.nickName,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
    },
    //详情
    handleDetail(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/detail",
        query: {
          projectBatchId: row.projectBatchId,
          projectCode: row.projectCode,
          stationName: row.stationName,
          isEnergyStorage: JSON.stringify(row.businessType == "2"),
        },
      });
    },
    //施工验收
    handleConstructAcceptance(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/constructAcceptance",
        query: {
          projectId: row.projectBatchId,
          isEnergyStorage: JSON.stringify(row.businessType == "2"),
        },
      });
    },
    //竣工验收
    handleCompleteAcceptance(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/completeAcceptance",
        query: { ...row },
      });
    },
    //查看竣工报告
    handleReport(row) {
      this.$refs.completeReport.open(row);
    },
    //开工申请
    handleApply(row, type = "add") {
      this.$router.push({
        path: "/engineerProject/investBatchList/apply",
        query: {
          type: type,
          projectBatchId: row.projectBatchId,
          projectCode: row.projectCode,
          isEnergyStorage: JSON.stringify(row.businessType == "2"),
        },
      });
    },
    //协议附件
    handleFile(row) {
      this.operationType = "file";
      // this.fileActiveName = "01";

      this.$refs.crud.switchModalView(true, "file", { docList: row.docList });
    },
    //审批查询
    async handleRecord(row) {
      this.operationType = "record";
      this.projectBatchId = row.projectBatchId;
      this.recordParams = {
        ...initParams(this.recordConfig),
        applyFormType: this.applyFormTypeOptions[0]?.dictValue,
      };
      await this.getOmApproveNoOptions(
        this.applyFormTypeOptions[0]?.dictValue,
        true
      );
      this.$refs.crud.switchModalView(true, "record");
      this.handleRecordSubmit();
    },
    //查询运管申请单号
    async getOmApproveNoOptions(val, isInitial = false) {
      const res = await api.omApplyNo({
        applyFormType: val,
        projectBatchId: this.projectBatchId,
      });
      if (res?.code === "10000") {
        this.omApplyNoOptions = res.data?.map((x) => {
          return { value: x, label: x };
        });
        if (val && !isInitial) {
          this.recordParams.omApproveNo = "";
        } else {
          //首次打开，默认选中第一个
          this.recordParams.omApproveNo = res.data?.[0] || "";
        }
      }
    },
    async handleRecordReset() {
      this.recordParams = {
        ...this.recordParams,
        ...initParams(this.recordConfig),
        applyFormType: this.applyFormTypeOptions[0]?.dictValue,
      };
      await this.getOmApproveNoOptions(
        this.applyFormTypeOptions[0]?.dictValue,
        true
      );
      this.handleRecordSubmit();
    },
    handleRecordSubmit() {
      api
        .record(this.recordParams)
        .then((res) => {
          if (res?.code === "10000") {
            this.recordList = res.data;
          } else {
            this.recordList = [];
          }
        })
        .catch(() => {
          this.recordList = [];
        });
    },
    //上传竣工报告
    handleUpload(row) {
      console.log(row, "上传");
      this.operationType = "upload";
      this.$refs.crud.switchModalView(true, "upload", {
        ...row,
        docList: [],
      });
      this.$refs.attachments.setAttachments([]);
    },
    updateAttachments(attachments, row) {
      row.docList = attachments;
    },
    //处理半径
    handleRadius(row) {
      this.operationType = "radius";
      this.radiusStatus = row.handleRadiusStatus ?? 0; //用于判断启用时处理半径不允许为空
      this.$refs.crud.switchModalView(true, "radius", {
        ...initParams(this.modalConfig.formConfig),
        handleRadius: row.handleRadius ?? 2000,
        handleRadiusStatus: row.handleRadiusStatus ?? 0,
        projectBatchId: row.projectBatchId,
      });
    },
    //确认
    handleConfirm(row) {
      this.operationType = "confirm";
      //businessType：1充电 2储能
      this.isEnergyStorage = row.businessType == "2";
      api.pileCount({ projectBatchId: row.projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          this.$refs.crud.switchModalView(true, "confirm", {
            ...initParams(this.modalConfig.formConfig),
            projectBatchId: row.projectBatchId,
            deviceTotal: res.data,
          });
        }
      });
    },
    //投建协议已盖章
    handleConfirmStamp(row) {
      //businessType：1充电 2储能
      this.isEnergyStorage = row.businessType == "2";
      this.operationType = "stamp";
      this.$refs.crud.switchModalView(true, "stamp", {
        ...initParams(this.modalConfig.formConfig),
        projectBatchId: row.projectBatchId,
      });
      // this.$confirm("", "场地双章投建协议确定已盖章?", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      //   center: true,
      // }).then(() => {
      //   api.confirmStamp({ projectBatchId: row.projectBatchId }).then((res) => {
      //     if (res?.code == "10000") {
      //       this.$message.success("提交成功");
      //       this.loadData();
      //     }
      //   });
      // });
    },
    //审核
    handleCheck(row) {
      this.operationType = "check";
      //businessType：1充电 2储能
      this.isEnergyStorage = row.businessType == "2";
      //储能传3
      const type = this.isEnergyStorage
        ? "3"
        : row.projectType === "新建"
        ? "1"
        : "2";
      this.getWorkType(type);
      this.getWorkTime(row.projectBatchId);
      this.$refs.crud.switchModalView(true, "check", {
        ...initParams(this.modalConfig.formConfig),
        projectBatchId: row.projectBatchId,
        type: type,
      });
    },
    //根据工序项类型查询工序模版名称
    getWorkType(type) {
      api.workType({ type: type }).then((res) => {
        if (res?.code === "10000") {
          this.workTypeOptions = res.data;
        }
      });
    },
    // 根据项目批次id  查询 开工申请时填写的 计划开工时间  计划竣工时间  设备需求当到场时间
    getWorkTime(projectBatchId) {
      api.workTime({ projectBatchId: projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          this.scheduledStartTime = res.data.scheduledStartTime;
          this.scheduledEndTime = res.data.scheduledEndTime;
          this.equipmentArrivalTime = res.data.equipmentArrivalTime;
        }
      });
    },
    async submitCheck(row, flag) {
      //储能项目 审核不通过时不校验表单和表格
      if (flag && this.isEnergyStorage) {
        const errMap = await this.$refs.editTable.validTable();
        if (errMap) {
          this.$message.warning("请填写表格必填项！");
          return;
        }
      }
      this.$refs.crud.getModalFormRef()?.validate((valid) => {
        //什么情况需要校验表单？非储能项目+储能项目审核通过时
        //不需要校验？储能项目且审核不通过
        if (!valid && (!this.isEnergyStorage || flag)) return;
        if (!flag && !row.auditReason) {
          this.$message.warning("请填写审核不通过原因！");
          return;
        }
        const params = {
          ...row,
          auditResult: flag ? "1" : "2",
          templateList: row.templateList.map((x) => {
            return this.workTypeOptions?.find((y) => y.workTemplateId === x);
          }),
        };
        // console.log(params, "params");
        this.checkLoading = true;
        api
          .check(params)
          .then((res) => {
            this.checkLoading = false;
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              this.$refs.crud.switchModalView(false);
              this.loadData();
            }
          })
          .catch(() => {
            this.checkLoading = false;
          });
        console.log(params, "到这说明通过校验了");
      });
    },
    //备注
    handleRemark(row) {
      this.operationType = "remark";
      this.$refs.crud.switchModalView(true, "remark", {
        ...initParams(this.modalConfig.formConfig),
        content: row.content,
        projectBatchId: row.projectBatchId,
      });
    },
    //转派项目经理
    handleTransfer(row) {
      this.operationType = "transfer";
      this.getProjectManagerOptions(row.projectBatchId);
      this.$refs.crud.switchModalView(true, "transfer", {
        ...initParams(this.modalConfig.formConfig),
        projectBatchId: row.projectBatchId,
      });
    },
    //下单通知
    handleOrderNotification(row) {
      this.operationType = "orderNotification";
      //businessType：1充电 2储能
      this.isEnergyStorage = row.businessType == "2";
      this.$refs.crud.switchModalView(true, "orderNotification", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    tabRadioChange(val) {
      this.activeTab = val;
      if (val == "1") {
        this.projectStatusOptions = this.allProjectStatusOptions?.filter(
          (x) => x.dictLabel !== "待评价" && x.dictLabel !== "已评价"
        );
      } else {
        this.projectStatusOptions = this.allProjectStatusOptions;
      }
      this.handleQuery();
    },
    handleCollapse(type) {
      this.foldObj[type] = !this.foldObj[type];
    },
    handleCancelCustom() {
      this.$refs.crud.switchModalView(false);
    },

    checkPermission,

    handleExport() {
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
        projectStatusList: this.projectStatusOptions?.map((x) => x.dictValue),
        businessType: this.tabActiveTab === "charge" ? "1" : "2",
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "surveyPassTime",
          title: "踏勘通过时间",
          startFieldName: "surveyPassStartTime",
          endFieldName: "surveyPassEndTime",
        },
        {
          field: "consCheckPassTime",
          title: "施工验收通过时间",
          startFieldName: "consCheckPassStartTime",
          endFieldName: "consCheckPassEndTime",
        },
        {
          field: "onlineTime",
          title: "上线时间",
          startFieldName: "onlineStartTime",
          endFieldName: "onlineEndTime",
        },
        {
          field: "warrantyExpiresTime",
          title: "质保到期时间",
          startFieldName: "warrantyExpiresStartTime",
          endFieldName: "warrantyExpiresEndTime",
        },
        {
          field: "consCompleteTime",
          title: "施工完成时间",
          startFieldName: "consCompleteStartTime",
          endFieldName: "consCompleteEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        projectStatusList: this.projectStatusOptions?.map((x) => x.dictValue),
        businessType: this.tabActiveTab === "charge" ? "1" : "2",
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      // return false;
      // console.log(crudOperationType, formParams, "提交");
      // if (crudOperationType === "transfer") {
      //   api.transfer(formParams).then((res) => {
      //     if (res?.code === "10000") {
      //       this.$message.success("提交成功");
      //       this.loadData();
      //     }
      //   });
      // }
      let params = { ...formParams };
      if (crudOperationType === "setTeam") {
        params = {
          ...params,
          consTeamName: this.constructionTeamList?.find(
            (x) => x.constructCode == formParams.consTeamCode
          )?.constructName,
          userList: this.consUserOptions.filter((x) => {
            return formParams.userList.includes(x.userId);
          }),
        };
        return new Promise((resolve) => {
          //该项目已绑定施工队，再次进入指定施工队界面更换施工队名称后，在该界面点击【确定】，二次弹窗提示【确定要更换施工队吗？】
          if (
            formParams.projectStatus == "15" &&
            formParams.initialConsCode &&
            formParams.initialConsCode !== formParams.consTeamCode
          ) {
            this.$confirm("确定要更换施工队吗？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
              center: true,
            })
              .then(() => {
                api
                  .setTeam(params)
                  .then((res) => {
                    if (res?.code == "10000") {
                      this.$message.success("提交成功");
                      this.loadData();
                      resolve(true);
                    } else {
                      resolve(false);
                    }
                  })
                  .catch(() => {
                    resolve(false);
                  });
              })
              .catch(() => {
                resolve(false);
              });
          }
          //如果手动指定的施工队与运管同步过来的项目施工队信息不一致(该项目还未指定施工队，但运管已有了【固定资产采购申请单的合同名称带（施工合作协议）的该项目数据】)
          else if (
            formParams.projectStatus == "4" &&
            formParams.factoryNo &&
            formParams.consTeamCode !== formParams.factoryNo
          ) {
            this.$confirm(
              "与运管施工下单中的施工队名称不一致，确定要继续指定当前所选的施工队吗？",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                center: true,
              }
            )
              .then(() => {
                api.setTeam(params).then((res) => {
                  if (res?.code == "10000") {
                    this.$message.success("提交成功");
                    this.loadData();
                    resolve(true);
                  } else {
                    resolve(false);
                  }
                });
              })
              .catch(() => {
                resolve(false);
              });
          } else {
            api.setTeam(params).then((res) => {
              if (res?.code == "10000") {
                this.$message.success("提交成功");
                this.loadData();
                resolve(true);
              } else {
                resolve(false);
              }
            });
          }
        });
      } else if (crudOperationType === "upload") {
        if (!formParams.docList || formParams.docList.length == 0) {
          this.$message.warning("请上传至少一个文件！");
          return false;
        }
        params = formParams.docList.map((x) => {
          return {
            ...x,
            relaBizId: formParams.projectBatchId,
            businessType: "竣工报告",
          };
        });
        const res = await api.upload(params);
        if (res?.code === "10000") {
          api
            .completeAcceptance({
              projectBatchId: formParams.projectBatchId,
              projectStatus: "11",
              completionReportUploadTime: moment().format("YYYY-MM-DD"),
            })
            .then((res1) => {
              if (res1?.code === "10000") {
                this.$message.success("提交成功");
                this.loadData();
              }
            });
        }
      } else {
        if (crudOperationType === "transfer") {
          params["consTeamManagerName"] = this.managerOptions?.find(
            (x) => x.userId === params.consTeamManager
          )?.nickName;
        }
        //确认-储能设备清单表格内必填校验
        if (crudOperationType === "confirm" && this.isEnergyStorage) {
          const errMap = await this.$refs.editTable.validTable();
          if (errMap) {
            this.$message.warning("请填写表格必填项！");
            return false;
          }
        }
        // crudOperationType:remark/confirm/orderNotification/radius/stamp
        const res = await api[crudOperationType](params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        } else {
          return false;
        }
      }
    },
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    getTagType(projectType) {
      const arr = [
        { type: "success", status: "新建" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
    handleJumpNotice(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/notice",
        query: {
          projectBatchId: row.projectBatchId,
        },
      });
    },
    handleJumpOperationConfirm(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/operateConfirm",
        query: {
          projectBatchId: row.projectBatchId,
          projectName: row.projectName,
        },
      });
    },
  },
  computed: {
    tableColumn() {
      return this.tabActiveTab === "charge"
        ? this.chargeColumn
        : this.storageColumn;
    },
    storageColumn() {
      return [
        {
          field: "energyBaseInfo",
          title: "基本信息",
          minWidth: 120,
          children: this.foldObj.energyBaseInfo
            ? []
            : [
                { field: "stationName", title: "场站名称", width: 150 },
                { field: "stationLocation", title: "场站位置", width: 150 },
                { field: "surveyCodeMain", title: "踏勘主编码", width: 220 },
                { field: "surveyCodeSub", title: "踏勘子编码", width: 150 },
                {
                  field: "projectType",
                  title: "项目类型",
                  width: 100,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-tag
                          type={this.getTagType(row.projectType)}
                          size="medium"
                          effect="plain"
                          style={"font-size: 16px;"}
                        >
                          {row.projectType}
                        </el-tag>,
                      ];
                    },
                  },
                },
                { field: "constructType", title: "投建类型", width: 100 },
                {
                  field: "surveyFormIsCancel",
                  title: "踏勘单是否已作废",
                  width: 100,
                },
                { field: "stationCode", title: "场站编码", width: 150 },
                { field: "projectCode", title: "项目编码", width: 150 },
                {
                  field: "projectProcessNodeName",
                  title: "项目流程节点",
                  width: 150,
                },
                {
                  field: "projectStatus",
                  title: "项目状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.allProjectStatusOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                {
                  field: "workApplyStatus",
                  title: "开工申请状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.workApplyStatusOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                {
                  field: "consTeamAuditStatusName",
                  title: "施工供应商审批状态",
                  width: 150,
                },
                {
                  field: "deviceOrderStatusName",
                  title: "设备下单状态",
                  width: 150,
                },
                {
                  field: "allWorkNum",
                  title: "工序项总数",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "pendingWorkNum",
                  title: "待处理工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "reviewedWorkNum",
                  title: "待审核工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "approveWorkNum",
                  title: "审核通过工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "transformerCapacityTotal",
                  title: "变压器总容量(kVA)",
                  width: 100,
                },
                {
                  field: "storageDevicesCount",
                  title: "储能设备数(台)",
                  width: 100,
                },
                { field: "totalCapacity", title: "总容量(kWh)", width: 100 },
                {
                  field: "meshCabinetsCount",
                  title: "并网柜数量(台)",
                  width: 100,
                },
                { field: "platformName", title: "平台方名称", width: 100 },
                {
                  field: "isXdtConnect",
                  title: "是否与新电途互联互通",
                  width: 100,
                },
                { field: "storageType", title: "储能类型", width: 100 },
                {
                  field: "businessDevelopmentName",
                  title: "商务BD",
                  width: 150,
                },
                { field: "consTeamName", title: "施工队", width: 150 },
                {
                  field: "consTeamLeaderName",
                  title: "施工队负责人",
                  width: 150,
                },
                {
                  field: "consTeamManagerName",
                  title: "施工队项目经理",
                  width: 150,
                },
              ],
          slots: {
            header: "fold_header",
          },
        },
        {
          field: "energyTimeNode",
          title: "时间节点",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.energyTimeNode
            ? []
            : [
                { field: "surveyPassTime", title: "踏勘通过时间", width: 150 },
                { field: "surveyDuration", title: "踏勘耗时", width: 150 },
                {
                  field: "consAgreementPassTime",
                  title: "投建协议审批通过时间",
                  width: 150,
                },
                {
                  field: "consAgreementApprovalDuration",
                  title: "投建协议审批耗时",
                  width: 150,
                },
                {
                  field: "supplierOrderNotifyTime",
                  title: "施工供应商通知时间",
                  width: 150,
                },
                {
                  field: "supplierOrderPassTime",
                  title: "施工供应商下单审批通过时间",
                  width: 150,
                },
                {
                  field: "supplierOrderApprovalDuration",
                  title: "施工供应商下单审批耗时",
                  width: 150,
                },
                {
                  field: "workApplyTime",
                  title: "开工申请发起时间",
                  width: 150,
                },
                {
                  field: "workApplyPassTime",
                  title: "开工申请通过时间",
                  width: 150,
                },
                {
                  field: "workApplyDuration",
                  title: "开工申请耗时",
                  width: 150,
                },
                {
                  field: "deviceOrderPassTime",
                  title: "设备下单审批通过时间",
                  width: 150,
                },
                {
                  field: "deviceOrderApprovalDuration",
                  title: "设备下单审批耗时",
                  width: 150,
                },
                {
                  field: "consCompleteTime",
                  title: "施工完成时间",
                  width: 150,
                },
                {
                  field: "completedCheckDuration",
                  title: "施工耗时",
                  width: 150,
                },
                {
                  field: "consCheckPassTime",
                  title: "施工验收通过时间",
                  width: 150,
                },
                {
                  field: "constructionCheckDuration",
                  title: "施工验收耗时",
                  width: 150,
                },
                {
                  field: "completionReportUploadTime",
                  title: "竣工报告上传时间",
                  width: 150,
                },
                {
                  field: "completionReportPassTime",
                  title: "竣工报告审核通过时间",
                  width: 150,
                },
                { field: "onlineTime", title: "上线时间", width: 150 },
                {
                  field: "warrantyExpiresTime",
                  title: "质保到期时间",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <span
                          style={{
                            color:
                              row.warrantyExpiresTimeFlag == "1" ? "red" : "",
                          }}
                        >
                          {row.warrantyExpiresTime}
                        </span>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "energyCostInfo",
          title: "成本信息",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.energyCostInfo
            ? []
            : [
                {
                  field: "constructionTotalCost",
                  title: "施工总成本(元)",
                  width: 150,
                },
                {
                  field: "deviceTotalCost",
                  title: "设备总成本(元)",
                  width: 150,
                },
                { field: "paidAmount", title: "已付金额(元)", width: 150 },
                { field: "unpaidAmount", title: "未付金额(元)", width: 150 },
              ],
        },
      ];
    },
    chargeColumn() {
      return [
        { field: "stationName", title: "场站名称", width: 150, fixed: "left" },
        {
          field: "baseInfo",
          title: "基本信息",
          minWidth: 120,
          children: this.foldObj.baseInfo
            ? []
            : [
                {
                  field: "businessType",
                  title: "业务类型",
                  width: 100,
                  formatter: ({ cellValue }) => {
                    return this.businessTypeOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                { field: "stationLocation", title: "场站位置", width: 150 },
                { field: "surveyCodeMain", title: "踏勘主编码", width: 220 },
                { field: "surveyCodeSub", title: "踏勘子编码", width: 150 },
                {
                  field: "projectType",
                  title: "项目类型",
                  width: 100,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-tag
                          type={this.getTagType(row.projectType)}
                          size="medium"
                          effect="plain"
                          style={"font-size: 16px;"}
                        >
                          {row.projectType}
                        </el-tag>,
                      ];
                    },
                  },
                },
                { field: "constructType", title: "投建类型", width: 100 },
                {
                  field: "ifVipProject",
                  title: "是否大客户项目",
                  width: 100,
                },
                {
                  field: "surveyFormIsCancel",
                  title: "踏勘单是否已作废",
                  width: 100,
                },
                { field: "stationCode", title: "场站编码", width: 150 },
                { field: "projectCode", title: "项目编码", width: 150 },
                {
                  field: "projectProcessNodeName",
                  title: "项目流程节点",
                  width: 150,
                },
                {
                  field: "projectStatus",
                  title: "项目状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.allProjectStatusOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                {
                  field: "workApplyStatus",
                  title: "开工申请状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.workApplyStatusOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                {
                  field: "consTeamAuditStatusName",
                  title: "施工供应商审批状态",
                  width: 150,
                },
                {
                  field: "deviceOrderStatusName",
                  title: "设备下单状态",
                  width: 150,
                },
                {
                  field: "allWorkNum",
                  title: "工序项总数",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "pendingWorkNum",
                  title: "待处理工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "reviewedWorkNum",
                  title: "待审核工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "approveWorkNum",
                  title: "审核通过工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                { field: "totalPileCount", title: "设备数", width: 100 },
                { field: "pilesErectedCount", title: "立柱数", width: 100 },
                { field: "totalPower", title: "总功率(kW)", width: 100 },
                { field: "orgName", title: "归属大区", width: 150 },
                {
                  field: "engineeringManagerName",
                  title: "工程经理",
                  width: 150,
                },
                {
                  field: "businessDevelopmentName",
                  title: "商务BD",
                  width: 150,
                },
                { field: "isReportPower", title: "是否报电", width: 100 },
                { field: "consTeamName", title: "施工队", width: 150 },
                {
                  field: "consTeamLeaderName",
                  title: "施工队负责人",
                  width: 150,
                },
                {
                  field: "consTeamManagerName",
                  title: "施工队项目经理",
                  width: 150,
                },
                {
                  field: "constructionContractMode",
                  title: "施工发包模式",
                  width: 150,
                },
              ],
          slots: {
            header: "fold_header",
          },
        },
        {
          field: "takan",
          title: "踏勘阶段",
          minWidth: 120,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.takan
            ? []
            : [
                {
                  field: "surveySubmitTime",
                  title: "踏勘提交时间",
                  width: 150,
                },
                { field: "surveyPassTime", title: "踏勘通过时间", width: 150 },
                { field: "surveyDuration", title: "踏勘耗时", width: 150 },
                {
                  field: "surveyTimeoutDuration",
                  title: "踏勘超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.surveyTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "shangqian",
          title: "商签阶段",
          minWidth: 120,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.shangqian
            ? []
            : [
                {
                  field: "consSubmitTime",
                  title: "投建协议需求池提交时间",
                  width: 150,
                },
                {
                  field: "consSubmitDuration",
                  title: "投建协议需求池提交耗时",
                  width: 150,
                },
                {
                  field: "consSubmitTimeoutDuration",
                  title: "投建协议需求池提交超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.consSubmitTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "sfApprovalTime",
                  title: "投建协议商服审核时间",
                  width: 150,
                },
                {
                  field: "sfApprovalDuration",
                  title: "投建协议商服审核耗时",
                  width: 150,
                },
                {
                  field: "sfApprovalTimeoutDuration",
                  title: "投建协议商服审核超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.sfApprovalTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "consAgreementApplyTime",
                  title: "投建协议运管提交时间",
                  width: 150,
                },
                {
                  field: "consAgreementApplyTimeDuration",
                  title: "投建协议运管提交耗时",
                  width: 150,
                },
                {
                  field: "consAgreementApplyTimeoutDuration",
                  title: "投建协议运管提交超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.consAgreementApplyTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "consAgreementPassTime",
                  title: "投建协议运管审批通过时间",
                  width: 150,
                },
                {
                  field: "consAgreementApprovalDuration",
                  title: "投建协议运管审批耗时",
                  width: 150,
                },
                {
                  field: "consApprovalTimeoutDuration",
                  title: "投建协议运管审批超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.consApprovalTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "agreementStampWriteTime",
                  title: "投建协议双章时间",
                  width: 150,
                },
                {
                  field: "stampDuration",
                  title: "投建协议双章耗时",
                  width: 150,
                },
                {
                  field: "stampTimeoutDuration",
                  title: "投建协议双章超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.stampTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "consAgreementStampTime",
                  title: "投建协议商服复核时间",
                  width: 150,
                },
                {
                  field: "sfCheckDuration",
                  title: "投建协议商服复核耗时",
                  width: 150,
                },
                {
                  field: "sfCheckTimeoutDuration",
                  title: "投建协议商服复核超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.sfCheckTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "fabao",
          title: "施工发包中",
          minWidth: 120,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.fabao
            ? []
            : [
                {
                  field: "supplierOrderNotifyTime",
                  title: "采购发包通知时间",
                  width: 150,
                },
                {
                  field: "supplierOrderNotifyTimeDuration",
                  title: "采购发包通知耗时",
                  width: 150,
                },
                {
                  field: "supplierOrderNotifyTimeoutDuration",
                  title: "采购发包通知超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.supplierOrderNotifyTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "designateConsTeamTime",
                  title: "采购发包时间",
                  width: 150,
                },
                {
                  field: "purchaseDuration",
                  title: "采购发包耗时",
                  width: 150,
                },
                {
                  field: "purchaseTimeoutDuration",
                  title: "采购发包超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.purchaseTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "kaigong",
          title: "开工准备阶段",
          minWidth: 120,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.kaigong
            ? []
            : [
                {
                  field: "workApplyTime",
                  title: "开工申请提交时间",
                  width: 150,
                },
                {
                  field: "workApplySubmitTimeDuration",
                  title: "开工申请提交耗时",
                  width: 150,
                },
                {
                  field: "workApplySubmitTimeoutDuration",
                  title: "开工申请提交超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.workApplySubmitTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
                {
                  field: "workApplyPassTime",
                  title: "开工申请审批时间",
                  width: 150,
                },
                {
                  field: "workApplyDuration",
                  title: "开工申请审批耗时",
                  width: 150,
                },
                {
                  field: "workApplyTimeoutDuration",
                  title: "开工申请审批超时时长",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <div style={{ color: "red" }}>
                          {row.workApplyTimeoutDuration}
                        </div>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "shigong",
          title: "施工阶段",
          minWidth: 120,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.shigong
            ? []
            : [
                {
                  field: "deviceOrderPassTime",
                  title: "设备到场时间",
                  width: 150,
                },
                {
                  field: "deviceOrderApprovalDuration",
                  title: "设备发货耗时",
                  width: 150,
                },
                {
                  field: "consCheckPassTime",
                  title: "竣工验收通过时间",
                  width: 150,
                },
                {
                  field: "completedCheckDuration",
                  title: "施工耗时",
                  width: 150,
                },
              ],
        },
        // {
        //   field: "timeInfo",
        //   title: "时间节点",
        //   minWidth: 100,
        //   slots: {
        //     header: "fold_header",
        //   },
        //   children: this.foldObj.timeInfo
        //     ? []
        //     : [
        //         { field: "surveyPassTime", title: "踏勘通过时间", width: 150 },
        //         { field: "surveyDuration", title: "踏勘耗时", width: 150 },
        //         {
        //           field: "consAgreementPassTime",
        //           title: "投建协议审批通过时间",
        //           width: 150,
        //         },
        //         {
        //           field: "consAgreementApprovalDuration",
        //           title: "投建协议审批耗时",
        //           width: 150,
        //         },
        //         {
        //           field: "supplierOrderNotifyTime",
        //           title: "施工供应商通知时间",
        //           width: 150,
        //         },
        //         {
        //           field: "supplierOrderPassTime",
        //           title: "施工供应商下单审批通过时间",
        //           width: 150,
        //         },
        //         {
        //           field: "supplierOrderApprovalDuration",
        //           title: "施工供应商下单审批耗时",
        //           width: 150,
        //         },
        //         {
        //           field: "workApplyTime",
        //           title: "开工申请发起时间",
        //           width: 150,
        //         },
        //         {
        //           field: "workApplyPassTime",
        //           title: "开工申请通过时间",
        //           width: 150,
        //         },
        //         {
        //           field: "workApplyDuration",
        //           title: "开工申请耗时",
        //           width: 150,
        //         },
        //         {
        //           field: "deviceOrderPassTime",
        //           title: "设备下单审批通过时间",
        //           width: 150,
        //         },
        //         {
        //           field: "deviceOrderApprovalDuration",
        //           title: "设备下单审批耗时",
        //           width: 150,
        //         },
        //         {
        //           field: "consCompleteTime",
        //           title: "施工完成时间",
        //           width: 150,
        //         },
        //         {
        //           field: "constructionDuration",
        //           title: "施工耗时",
        //           width: 150,
        //         },
        //         {
        //           field: "consCheckPassTime",
        //           title: "施工验收通过时间",
        //           width: 150,
        //         },
        //         {
        //           field: "constructionCheckDuration",
        //           title: "施工验收耗时",
        //           width: 150,
        //         },
        //         {
        //           field: "completionReportUploadTime",
        //           title: "竣工报告上传时间",
        //           width: 150,
        //         },
        //         {
        //           field: "completionReportPassTime",
        //           title: "竣工报告审核通过时间",
        //           width: 150,
        //         },
        //         { field: "onlineTime", title: "上线时间", width: 150 },
        //         {
        //           field: "warrantyExpiresTime",
        //           title: "质保到期时间",
        //           width: 150,
        //           slots: {
        //             default: ({ row }) => {
        //               return [
        //                 <span
        //                   style={{
        //                     color:
        //                       row.warrantyExpiresTimeFlag == "1" ? "red" : "",
        //                   }}
        //                 >
        //                   {row.warrantyExpiresTime}
        //                 </span>,
        //               ];
        //             },
        //           },
        //         },
        //       ],
        // },
        // {
        //   field: "costInfo",
        //   title: "成本信息",
        //   minWidth: 100,
        //   slots: {
        //     header: "fold_header",
        //   },
        //   children: this.foldObj.costInfo
        //     ? []
        //     : [
        //         {
        //           field: "constructionTotalCost",
        //           title: "施工总成本(元)",
        //           width: 150,
        //         },
        //         {
        //           field: "deviceTotalCost",
        //           title: "设备总成本(元)",
        //           width: 150,
        //         },
        //         { field: "paidAmount", title: "已付金额(元)", width: 150 },
        //         { field: "unpaidAmount", title: "未付金额(元)", width: 150 },
        //       ],
        // },
        {
          field: "completionReportUploadTime",
          title: "竣工报告上传时间",
          width: 150,
        },
        {
          field: "completionReportPassTime",
          title: "竣工报告审核通过时间",
          width: 150,
        },
        { field: "onlineTime", title: "上线时间", width: 150 },
        { field: "onlineDuration", title: "上线耗时", width: 150 },
        { field: "warrantyExpiresTime", title: "质保到期时间", width: 150 },
        {
          field: "content",
          title: "备注",
          width: 150,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "surveyCodeMain",
            element: "el-input",
            title: "踏勘编码",
          },
          {
            field: "stationCode",
            element: "el-input",
            title: "场站编码",
          },
          {
            field: "stationName",
            element: "el-input",
            title: "场站名称",
          },
          {
            field: "region",
            title: "所在区域",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              clearable: true,
              filterable: true,
              props: {
                checkStrictly: false,
                multiple: true,
                value: "areaCode",
                label: "areaName",
              },
              options: this.regionData,
            },
          },
          {
            field: "projectProcessNode",
            title: "项目节点",
            element: "el-select",
            props: {
              options: this.processNodeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "projectStatus",
            title: "项目状态",
            element: "el-select",
            props: {
              options: this.projectStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "surveyPassTime",
            title: "踏勘通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "workApplyStatus",
            title: "开工申请状态",
            element: "el-select",
            props: {
              options: this.workApplyStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "consTeamAuditStatus",
            title: "施工供应商审批状态",
            element: "el-select",
            props: {
              options: this.consTeamAuditStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "consCompleteTime",
            title: "施工完成时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },

          {
            field: "surveyCodeSub",
            element: "el-input",
            title: "踏勘子编码",
          },
          {
            field: "consCheckPassTime",
            title: "验收通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "projectCode",
            element: "el-input",
            title: "项目编码",
          },
          {
            field: "orgNo",
            title: "归属大区",
            element: "el-select",
            props: {
              options: this.deptOptionList,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "onlineTime",
            title: "上线时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "engineeringManagerName",
            title: "工程经理",
            element: "el-select",
            props: {
              options: this.engineeringManagerOptions,
              filterable: true,
              clearable: true,
            },
          },
          {
            field: "consTeamName",
            element: "el-input",
            title: "施工队",
          },
          {
            field: "warrantyExpiresTime",
            title: "质保到期时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "consTeamManagerName",
            title: "施工队项目经理",
            element: "el-select",
            props: {
              options: this.consTeamManagerOptions,
              filterable: true,
              clearable: true,
            },
          },
          {
            field: "consTeamLeaderName",
            title: "施工队负责人",
            element: "el-select",
            props: {
              options: this.consTeamLeaderOptions,
              filterable: true,
              clearable: true,
            },
          },
          {
            field: "constructType",
            title: "投建类型",
            element: "el-select",
            props: {
              options: this.constructTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "deviceOrderStatus",
            title: "设备下单状态",
            element: "el-select",
            props: {
              options: this.deviceOrderStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "ifVipProject",
            element: "el-select",
            title: "是否大客户项目",
            props: {
              options: [
                { label: "是", value: "是" },
                { label: "否", value: "否" },
              ],
            },
            show: this.tabActiveTab === "charge",
          },
          {
            field: "projectType",
            element: "el-select",
            title: "项目类型",
            props: {
              options: [
                { label: "新建", value: "新建" },
                { label: "加桩", value: "加桩" },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    recordConfig() {
      return [
        {
          field: "applyFormType",
          title: "类型",
          element: "el-select",
          props: {
            options: this.applyFormTypeOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
          on: {
            change: (val) => {
              this.getOmApproveNoOptions(val);
            },
          },
          itemProps: {
            labelWidth: "80px",
          },
        },
        {
          field: "omApproveNo",
          title: "运管申请单号",
          element: "el-select",
          props: {
            options: this.omApplyNoOptions,
            filterable: true,
          },
        },
      ];
    },
    modalConfig() {
      const form = {
        //施工供应商下单通知
        orderNotification: [
          {
            field: "surveyCodeMain",
            title: "踏勘编码",
            preview: true,
          },
          {
            field: "surveyCodeSub",
            title: "踏勘子编码",
            preview: true,
          },
          {
            field: "consAgreementApplyNo",
            title: "运管投建协议申请单号",
            preview: true,
          },
          {
            field: "projectName",
            title: "项目名称",
            preview: true,
          },
          {
            field: "projectCode",
            title: "项目编码",
            preview: true,
          },
          {
            field: "supplierOrderPileCount",
            title: this.isEnergyStorage ? "储能柜总数" : "设备总数",
            element: "el-input-number",
            rules: [
              { required: true, message: "请输入设备总数" },
              {
                pattern: /^([1-9]\d{0,5}|99999)$/,
                message: "请输入正确的数字",
              },
            ],
            props: {
              controlsPosition: "right",
            },
          },
          {
            field: "supplierOrderPileOldCount",
            title: "利旧数量",
            element: "el-input-number",
            props: {
              controlsPosition: "right",
            },
            rules: [
              {
                pattern: /^(0|[1-9]\d{0,5}|99999)$/,
                message: "请输入正确的数字",
              },
            ],
          },
          {
            field: "userIds",
            element: "el-select",
            title: "通知人",
            props: {
              options: this.userOptions,
              multiple: true,
              filterable: true,
            },
            rules: [{ required: true, message: "请选择通知人" }],
          },
          {
            field: "messageTypes",
            element: "el-checkbox-group",
            title: "通知方式",
            props: {
              options: [
                { label: "钉钉", value: "01" },
                { label: "短信", value: "02" },
                { label: "APP", value: "03" },
              ],
            },
            rules: [{ required: true, message: "请选择通知方式" }],
          },
          {
            field: "remark",
            element: "el-input",
            title: "备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
            },
          },
        ],
        //转派项目经理
        transfer: [
          {
            field: "consTeamManager",
            element: "el-select",
            title: "施工队项目经理",
            props: {
              options: this.managerOptions,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "reason",
            element: "el-input",
            title: "转派原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
          {
            field: "messageTypes",
            element: "el-checkbox-group",
            title: "通知方式",
            props: {
              options: [
                { label: "钉钉", value: "01" },
                { label: "短信", value: "02" },
                { label: "APP", value: "03" },
              ],
            },
            // defaultValue: [],
          },
        ],
        //审核
        check: [
          {
            field: "type",
            element: "el-radio-group",
            title: "确定工序项",
            props: {
              options: [
                { label: "新建项目（12项工序）", value: "1" },
                { label: "换桩项目（2项工序）", value: "2" },
              ],
            },
            rules: [{ required: !this.isEnergyStorage, message: "请选择" }],
            on: {
              change: (val) => {
                this.$refs.crud.setFormFields({ templateList: [] });
                this.getWorkType(val);
              },
            },
            show: !this.isEnergyStorage,
          },
          {
            field: "templateList",
            element: "el-select",
            title: "选择工序项",
            props: {
              options: this.workTypeOptions,
              optionLabel: "workTemplateName",
              optionValue: "workTemplateId",
              multiple: true,
            },
            defaultValue: [],
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "deviceDemandList",
            element: "slot",
            slotName: "deviceDemandList",
            title: "储能设备需求清单",
            defaultValue: [],
            show: this.isEnergyStorage,
            rules: [{ required: this.isEnergyStorage, message: "请选择" }],
          },
          {
            field: "projectAttribute",
            element: "el-select",
            title: "项目属性",
            props: {
              options: this.projectAttributeOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
            show: this.isEnergyStorage,
            defaultValue: "1",
          },
          {
            field: "whetherNeedRoadItem",
            element: "el-radio-group",
            title: "是否需要路书",
            props: {
              options: [
                { label: "需要", value: "0" },
                { label: "不需要", value: "1" },
              ],
            },
            defaultValue: "1",
            show: !this.isEnergyStorage,
          },
          {
            field: "auditReason",
            element: "el-input",
            title: "审核不通过原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
        //确认
        confirm: [
          {
            field: "productSpecification",
            title: "产品规格",
            rules: [
              { required: !this.isEnergyStorage, message: "请输入产品规格" },
            ],
            show: !this.isEnergyStorage,
          },
          {
            field: "manufacturer",
            title: "生产厂家",
            rules: [
              { required: !this.isEnergyStorage, message: "请输入生产厂家" },
            ],
            show: !this.isEnergyStorage,
          },
          {
            field: "deviceTotal",
            title: "设备数量",
            element: "el-input-number",
            defaultValue: 9,
            rules: [
              { required: !this.isEnergyStorage, message: "请输入设备数量" },
            ],
            props: {
              controlsPosition: "right",
              disabled: true,
            },
            show: !this.isEnergyStorage,
          },
          {
            field: "uprightTotal",
            title: "立柱数量",
            element: "el-input-number",
            props: {
              controlsPosition: "right",
            },

            rules: [
              { required: !this.isEnergyStorage, message: "请输入立柱数量" },
              {
                pattern: /^(0|[1-9]\d{0,5}|999999)$/,
                message: "请输入正确的数字",
              },
            ],
            show: !this.isEnergyStorage,
          },
          {
            field: "deviceSource",
            title: "设备来源",
            element: "el-select",
            props: {
              options: this.deviceSourceTypeList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
            rules: [{ required: true, message: "请选择设备来源" }],
          },
          {
            field: "deviceSourceInstruction",
            title: "设备来源说明",
            element: "el-input",
            props: {
              type: "textarea",
            },
            rules: [
              {
                required: ["2", "3"].includes(
                  this.$refs.crud?.getFormFields()?.deviceSource
                ),
                message: "请输入设备来源说明",
              },
            ],
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
          {
            field: "plannedArrivalTime",
            title: "计划到货时间",
            element: "el-date-picker",
            props: { valueFormat: "yyyy-MM-dd" },
            rules: [{ required: true, message: "请选择计划到货时间" }],
          },
          {
            field: "deviceDemandList",
            element: "slot",
            slotName: "deviceDemandList",
            title: "设备清单",
            defaultValue: [],
            show: this.isEnergyStorage,
            rules: [{ required: this.isEnergyStorage, message: "请选择" }],
          },
        ],
        //处理半径
        radius: [
          {
            field: "handleRadius",
            title: "处理半径",
            slots: {
              append: "m",
            },
            rules: [
              {
                required: this.radiusStatus == 0,
                message: "启用时处理半径不允许为空！",
              },
              {
                pattern: /^(0|[1-9]\d{0,5}|999999)$/,
                message: "请输入正确的数字",
              },
            ],
          },
          {
            field: "handleRadiusStatus",
            title: "状态",
            element: "el-radio-group",
            props: {
              options: [
                { label: "启用", value: 0 },
                { label: "关闭", value: 1 },
              ],
            },
            on: {
              change: (val) => {
                this.radiusStatus = val;
              },
            },
          },
        ],
        //投建协议已盖章
        stamp: [
          {
            field: "agreementStampWriteTime",
            title: this.isEnergyStorage ? "协议三章时间" : "协议双章时间",
            element: "el-date-picker",
            props: { valueFormat: "yyyy-MM-dd" },
            rules: [{ required: true, message: "请选择" }],
          },
        ],
        //指定施工队
        setTeam: [
          {
            field: "projectName",
            title: "项目名称",
            preview: true,
          },
          {
            field: "projectCode",
            title: "项目编码",
            preview: true,
          },
          {
            field: "consTeamName",
            title: "施工队名称",
            element: "el-select",
            props: {
              placeholder: "请选择或输入施工队名称",
              options: this.constructionTeamList,
              optionLabel: "constructName",
              optionValue: "constructCode",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择施工队" }],
            on: {
              change: (val) => {
                this.$refs.crud.setFormFields({
                  consTeamCode: val,
                });
              },
            },
          },
          {
            field: "consTeamCode",
            title: "施工队编码",
            preview: true,
          },
          {
            field: "userList",
            element: "el-select",
            title: "通知人",
            props: {
              options: this.consUserOptions,
              multiple: true,
              filterable: true,
            },
            rules: [{ required: true, message: "请选择通知人" }],
          },
          {
            field: "messageTypes",
            element: "el-checkbox-group",
            title: "通知方式",
            props: {
              options: [
                { label: "钉钉", value: "01" },
                { label: "短信", value: "02" },
                // { label: "APP", value: "03" },
              ],
            },
            rules: [{ required: true, message: "请选择通知方式" }],
          },
          {
            field: "remark",
            element: "el-input",
            title: "备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的原因描述，500个字符以内",
            },
          },
        ],
        //备注
        remark: [
          {
            field: "content",
            element: "el-input",
            title: "备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 1000,
              showWordLimit: true,
              placeholder: "1000个字符以内",
            },
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 300,
        menuFixed: "right",
        modalWidth: this.operationType === "upload" ? "60%" : "50%",
        formConfig: form[this.operationType] || [],
        customOperationTypes: [
          {
            title: "项目投运确认单",
            typeName: "operationConfirm",
            showForm: false,
            event: (row) => {
              return this.handleJumpOperationConfirm(row);
            },
            condition: (row) => {
              //待验收、验收不通过、待上线、竣工验收、竣工验收不通过、待评价、已评价
              const condition1 = [
                "8",
                "9",
                "10",
                "11",
                "12",
                "13",
                "14",
              ].includes(row.projectStatus);
              // 储能项目
              const condition2 = row.businessType == "2";

              return (
                condition1 &&
                condition2 &&
                checkPermission([
                  "engineerProject:investBatchList:operationConfirm",
                ])
              );
            },
          },
          {
            title: "工程整改通知",
            typeName: "notice",
            showForm: false,
            event: (row) => {
              return this.handleJumpNotice(row);
            },
            condition: (row) => {
              //待验收、验收不通过、待上线、竣工验收、竣工验收不通过、待评价、已评价
              const condition1 = [
                "8",
                "9",
                "10",
                "11",
                "12",
                "13",
                "14",
              ].includes(row.projectStatus);
              // 储能项目
              const condition2 = row.businessType == "2";
              return (
                condition1 &&
                condition2 &&
                checkPermission(["engineerProject:investBatchList:notice"])
              );
            },
          },
          {
            title: "开工申请",
            typeName: "apply",
            slotName: "apply",
            showForm: false,
            event: (row) => {
              return this.handleApply(row);
            },
            condition: (row) => {
              //待申请
              const condition1 = row.workApplyStatus == "1";

              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:apply"])
              );
            },
          },
          {
            title: "转派项目经理",
            typeName: "transfer",
            event: (row) => {
              return this.handleTransfer(row);
            },
            condition: (row) => {
              //待审核/审核不通过
              const condition1 = ["2", "5"].includes(row.workApplyStatus);
              //施工中
              const condition2 = row.projectStatus == "7";

              return (
                (condition1 || condition2) &&
                checkPermission(["engineerProject:investBatchList:transfer"])
              );
            },
          },
          {
            title: "审核",
            typeName: "check",
            modalTitle: "工程审核结果",
            modalWidth: "70%",
            event: (row) => {
              return this.handleCheck(row);
            },
            condition: (row) => {
              //待审核
              const condition1 = row.workApplyStatus == "2";

              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:check"])
              );
            },
          },
          {
            title: "编辑",
            typeName: "edit",
            event: (row) => {
              return this.handleApply(row, "edit");
            },
            condition: (row) => {
              //审核不通过
              const condition1 = ["5"].includes(row.workApplyStatus);
              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:edit"])
              );
            },
          },
          {
            title: "确认",
            typeName: "confirm",
            modalTitle: "采购确认结果",
            modalWidth: "70%",
            event: (row) => {
              return this.handleConfirm(row);
            },
            condition: (row) => {
              //审核通过
              const condition1 = row.workApplyStatus == "3";
              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:confirm"])
              );
            },
          },
          //   {
          //     title: "设备下单",
          //     typeName: "equipmentOrder",
          //     event: (row) => {
          //       return this.handleEquipmentOrder(row);
          //     },
          //     condition: (row) => {
          //       return true;
          //     },
          //   },
          {
            title: "投建协议已盖章",
            typeName: "stamp",
            modalTitle: this.isEnergyStorage
              ? "投建协议三章确认时间"
              : "请确认投建协议对方已完成盖章",
            // showForm: false,
            event: (row) => {
              return this.handleConfirmStamp(row);
            },
            condition: (row) => {
              //场地双章审批完成
              const statusCondition =
                (row.projectStatus == "1" ||
                  row.projectStatus == "2" ||
                  row.projectStatus == "3" ||
                  row.projectStatus == "15") &&
                !row.consAgreementStampTime &&
                row.projectProcessNode == "1";
              return (
                statusCondition &&
                checkPermission(["engineerProject:investBatchList:stamp"])
              );
            },
          },
          {
            title: "施工供应商下单通知",
            typeName: "orderNotification",
            event: (row) => {
              return this.handleOrderNotification(row);
            },
            condition: (row) => {
              //场地双章审批完成+确认盖章时间 不为空 并且 项目节点是在发包中
              const statusCondition =
                row.projectStatus == "3" &&
                !!row.consAgreementStampTime &&
                row.projectProcessNode == "2" &&
                (row.constructionContractMode === "自主发包" ||
                  row.businessType === "2");
              return (
                statusCondition &&
                checkPermission([
                  "engineerProject:investBatchList:orderNotification",
                ])
              );
            },
          },
          {
            title: "施工验收",
            typeName: "constructAcceptance",
            event: (row) => {
              return this.handleConstructAcceptance(row);
            },
            condition: (row) => {
              //待验收/施工中
              const condition1 = ["7", "8", "9"].includes(row.projectStatus);

              return (
                condition1 &&
                checkPermission([
                  "engineerProject:investBatchList:constructAccept",
                ])
              );
            },
          },
          {
            title: "竣工验收",
            typeName: "completeAcceptance",
            event: (row) => {
              return this.handleCompleteAcceptance(row);
            },
            condition: (row) => {
              //竣工验收
              const condition1 = row.projectStatus == "11";
              return (
                condition1 &&
                checkPermission([
                  "engineerProject:investBatchList:completeAccept",
                ])
              );
            },
          },
          {
            title: "竣工报告",
            typeName: "report",
            event: (row) => {
              return this.handleReport(row);
            },
            condition: (row) => {
              //待上线/竣工验收/竣工验收不通过/待评价/已评价
              const condition1 = ["10", "11", "12", "13", "14"].includes(
                row.projectStatus
              );
              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:report"])
              );
            },
          },
          {
            title: "上传",
            typeName: "upload",
            showForm: false,
            slotName: "upload",
            modalTitle: "上传竣工报告",
            event: (row) => {
              return this.handleUpload(row);
            },
            condition: (row) => {
              //待上线/竣工验收/竣工验收不通过
              const condition1 = ["10", "11", "12"].includes(row.projectStatus);
              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:upload"])
              );
            },
          },
          {
            title: "处理半径",
            typeName: "radius",
            event: (row) => {
              return this.handleRadius(row);
            },
            condition: (row) => {
              //施工中/待验收/验收不通过
              const condition1 = ["7", "8", "9"].includes(row.projectStatus);
              return (
                condition1 &&
                checkPermission(["engineerProject:investBatchList:radius"])
              );
            },
          },
          {
            title: "指定施工队",
            typeName: "setTeam",
            modalTitle: "采购指定施工队",
            event: (row) => {
              return this.handleSetTeam(row);
            },
            condition: (row) => {
              //踏勘审批通过～施工中/验收不通过/已指定施工队
              const condition1 =
                (row.constructionContractMode === "自主发包" ||
                  row.businessType === "2") &&
                ["4", "5", "6", "7", "9", "15"].includes(row.projectStatus);
              const condition2 =
                row.constructionContractMode === "指定施工商" &&
                ["3", "4", "5", "6", "7", "9", "15"].includes(
                  row.projectStatus
                );
              return (
                (condition1 || condition2) &&
                checkPermission(["engineerProject:investBatchList:setTeam"])
              );
            },
          },
          {
            title: "审批查询",
            typeName: "record",
            slotName: "record",
            showForm: false,
            modalWidth: "70%",
            modalTitle: "审批进度查询",
            event: (row) => {
              return this.handleRecord(row);
            },
            condition: (row) => {
              return checkPermission([
                "engineerProject:investBatchList:record",
              ]);
            },
          },
          {
            title: "详情",
            typeName: "detail",
            event: (row) => {
              return this.handleDetail(row);
            },
            condition: (row) => {
              return checkPermission([
                "engineerProject:investBatchList:detail",
              ]);
            },
          },
          {
            title: "附件",
            typeName: "file",
            slotName: "file",
            showForm: false,
            modalTitle: "协议附件",
            event: (row) => {
              return this.handleFile(row);
            },
            condition: (row) => {
              return checkPermission(["engineerProject:investBatchList:file"]);
            },
          },
          {
            title: "备注",
            typeName: "remark",
            event: (row) => {
              return this.handleRemark(row);
            },
            condition: (row) => {
              return checkPermission([
                "engineerProject:investBatchList:remark",
              ]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          header: () => {
            const vnode = (
              <div class="checkFlex">
                <div>计划开工时间：{this.scheduledStartTime}</div>
                <div>计划竣工时间：{this.scheduledEndTime}</div>
                <div>设备需求到场时间：{this.equipmentArrivalTime}</div>
              </div>
            );
            return this.operationType === "check" ? vnode : "";
          },
          labelWidth: ["orderNotification", "transfer", "check"].includes(
            this.operationType
          )
            ? "160px"
            : "110px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.checkFlex {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  border: 1px dotted green;
  margin: 0px 20px 20px 20px;
}

.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
/deep/ .vxe-toolbar {
  width: 100px;
  position: absolute;
  right: 0;
  top: -70px;
}
</style>
