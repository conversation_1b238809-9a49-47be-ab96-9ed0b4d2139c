<template>
  <el-drawer
    title=""
    :visible.sync="workCheckVis"
    :show-close="false"
    size="80%"
    append-to-body
    :wrapperClosable="true"
  >
    <WorkCheck
      :projectId="projectBatchId"
      :projectStatus="projectStatus"
      :edit="false"
      :isEnergyStorage="isEnergyStorage"
      @closeDrawer="workCheckVis = false"
    />
  </el-drawer>
</template>

<script>
import WorkCheck from "../../components/WorkCheck";

export default {
  components: { WorkCheck },
  data() {
    return {
      workCheckVis: false,
      projectBatchId: "",
      projectStatus: "",
      isEnergyStorage: false,
    };
  },
  methods: {
    open(row) {
      this.workCheckVis = true;
      this.projectBatchId = row.projectBatchId;
      this.projectStatus = row.projectStatus;
      this.isEnergyStorage = row.businessType == "2";
    },
  },
};
</script>

<style></style>
