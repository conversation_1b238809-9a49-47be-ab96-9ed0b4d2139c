//详情-上线单详情抽屉
<template>
  <el-drawer title="上线单详情" :visible.sync="visible" size="60%">
    <div class="drawer-content">
      <el-card>
        <CommonTitle class="mb10" title="基本信息" />
        <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="BOM单" />
        <vxe-grid
          :columns="tableColumns"
          :data="tableData"
          resizable
          align="center"
        ></vxe-grid>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="电价信息" />
        <BaseDescriptions :list="powerList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="服务费信息" />
        <BaseDescriptions :list="serviceList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="附件信息" />
        <div v-for="(item, index) in docList" :key="index">
          <el-link :href="item.storePath" target="_blank">{{
            item.docName
          }}</el-link>
        </div>
      </el-card>
    </div>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import CommonTitle from "@/components/commonTitle";
export default {
  components: { CommonTitle, BaseDescriptions },
  data() {
    return {
      visible: false,
      baseList: [],
      powerList: [],
      serviceList: [],
      tableColumns: [
        { title: "本次采购数", field: "installType" },
        { title: "单位", field: "installType" },
        { title: "产品编码", field: "installType" },
        { title: "产品名称", field: "installType" },
        { title: "规格型号", field: "installType" },
        { title: "备注", field: "installType" },
      ],
      tableData: [],
      docList: [
        {
          docId: "******************",
          docName: "1.pdf",
          docType: "4",
          remark: "0.06M",
          storePath:
            "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-05/1720160311563_41f122fd-9849-430c-89ea-59c545a82850.pdf",
          storeType: "1",
          tenantId: "10000",
          uploadTime: "2024-07-05 14:18:31",
          uploader: "10000",
        },
        {
          createTime: "2024-07-04 13:51:21",
          delFlag: "0",
          docId: "464065685258194944",
          docName: "1.jpg",
          docType: "1",
          relaBizId: "464065340389937153",
          remark: "0.08M",
          storePath:
            "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-04/1720072277935_b73ce6b4-67df-4bb1-98ee-7472e25d9fca.jpg",
          storeType: "1",
          tenantId: "10000",
          uploadTime: "2024-07-04 13:51:18",
          uploader: "10054",
        },
      ],
    };
  },
  methods: {
    open(row) {
      this.visible = true;
      api.getAgreementDetail({ applyNo: row.applyNo }).then((res) => {
        this.baseList = [
          {
            title: "单号",
            value: res.data?.stationName,
          },
          {
            title: "场站编码",
            value: res.data?.stationName,
          },
          {
            title: "运营模式",
            value: res.data?.stationName,
          },
          {
            title: "场站名称",
            value: res.data?.stationName,
          },
          {
            title: "协议编码",
            value: res.data?.stationName,
          },
          {
            title: "项目公司",
            value: res.data?.stationName,
          },
          {
            title: "归属大区",
            value: res.data?.stationName,
          },
          {
            title: "资产运营",
            value: res.data?.stationName,
          },
          {
            title: "项目商务负责人",
            value: res.data?.stationName,
          },
          {
            title: "工程踏勘人员",
            value: res.data?.stationName,
          },
          {
            title: "是否提供施工",
            value: res.data?.stationName,
          },
        ];
        this.tableData = [];
        this.powerList = [
          {
            title: "电价类型",
            value: res.data?.stationName,
          },
          {
            title: "基础电价（元/度）",
            value: res.data?.stationName,
          },
          {
            title: "基础电价是否上浮",
            value: res.data?.stationName,
          },
          {
            title: "基础电价是否周期性调整",
            value: res.data?.stationName,
          },
          {
            title: "尖电价",
            value: res.data?.stationName,
          },
          {
            title: "峰电价",
            value: res.data?.stationName,
          },
          {
            title: "平电价",
            value: res.data?.stationName,
          },
          {
            title: "谷电价",
            value: res.data?.stationName,
          },
        ];
        this.serviceList = [
          {
            title: "踏勘时预计度电服务费",
            value: res.data?.stationName,
          },
          {
            title: "场地方分成比例",
            value: res.data?.stationName,
          },
          {
            title: "预计度电净服务费",
            value: res.data?.stationName,
          },
          {
            title: "居间商分成比例",
            value: res.data?.stationName,
          },
          {
            title: "上线配置度电服务费（元/度）",
            value: res.data?.stationName,
          },
          {
            title: "总分成比例（场地方+居间商%）",
            value: res.data?.stationName,
          },
          {
            title: "波动幅度",
            value: res.data?.stationName,
          },
          {
            title: "上线度电净服务费（元/度）",
            value: res.data?.stationName,
          },
        ];
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
</style>
