//详情-投建协议信息抽屉
<template>
  <el-drawer title="投建协议信息" :visible.sync="visible" size="60%">
    <div class="drawer-content">
      <el-card>
        <CommonTitle class="mb10" title="基本信息" />
        <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="投建目的信息" />
        <vxe-grid
          :columns="columns"
          :data="tableData"
          resizable
          align="center"
        ></vxe-grid>
      </el-card>
    </div>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import CommonTitle from "@/components/commonTitle";
export default {
  components: { CommonTitle, BaseDescriptions },
  data() {
    return {
      visible: false,
      baseList: [],
      tableColumns: [
        { title: "场所类型", field: "siteTypeName", width: 100 },
        { title: "场所名称", field: "siteName", width: 100 },
        { title: "场所编码", field: "locationCode", width: 100 },
        { title: "第几批次", field: "times", width: 100 },
        { title: "商务踏勘编码", field: "businessCode", width: 100 },
        { title: "工程踏勘编码", field: "engineeringCode", width: 100 },
        { title: "归属大区", field: "areaZone", width: 100 },
        { title: "项目经理", field: "projManagerName", width: 100 },
        {
          title: "预计投资金额-含税（元）",
          field: "investmentAmount",
          width: 100,
        },
        { title: "踏勘编号", field: "engineeringCode", width: 100 },
        { title: "直流充电桩个数", field: "installType", width: 100 },
        { title: "交流充电桩个数", field: "installType", width: 100 },
        { title: "场地合同年限", field: "effectiveTime", width: 100 },
        { title: "生效时间", field: "startTime", width: 100 },
        { title: "失效时间", field: "endTime", width: 100 },
        { title: "场地租赁费/分成比例", field: "rentalFee", width: 100 },
        { title: "预计投建日期", field: "constructDate", width: 100 },
        { title: "资产建设类型", field: "constructTypeName", width: 100 },
      ],
      energyColumns: [
        { title: "场所类型", field: "siteTypeName", width: 100 },
        { title: "场所名称", field: "siteName", width: 100 },
        { title: "场所编码", field: "locationCode", width: 100 },
        { title: "第几批次", field: "times", width: 100 },
        { title: "场站ID码", field: "czid", width: 100 },
        { title: "项目经理", field: "projManagerName", width: 100 },
        { title: "省市区", field: "provincEname", width: 100 },
        { title: "街道", field: "jd", width: 100 },
        {
          title: "预计投资金额-含税（元）",
          field: "investmentAmount",
          width: 100,
        },
        { title: "储能设备总容量/MWh", field: "cnCapacity", width: 100 },
        { title: "储能设备个数", field: "cnCount", width: 100 },
        { title: "协议生效时长", field: "effectiveTime", width: 100 },
        { title: "生效时间", field: "starTtime", width: 100 },
        { title: "失效时间", field: "endTime", width: 100 },
        { title: "预计投建日期", field: "cnsbzrl", width: 100 },
        { title: "资产建设类型", field: "constructTypeName", width: 100 },
        { title: "场地提供方结算模式", field: "cnsbzrl", width: 100 },
        { title: "应收场地方费用", field: "recivelOcalFee", width: 100 },
        { title: "收货人", field: "consigneeName", width: 100 },
        { title: "收货人地址", field: "cnsbzrl", width: 100 },
        { title: "踏勘编号", field: "engineeringApplyNo", width: 100 },
      ],
      tableData: [],
      isEnergyStorage: false,
    };
  },
  computed: {
    columns() {
      return this.isEnergyStorage ? this.energyColumns : this.tableColumns;
    },
  },
  methods: {
    open(row) {
      this.visible = true;
      this.isEnergyStorage = row.isEnergyStorage;
      const method1 = row.isEnergyStorage
        ? "getEnergyAgreementList"
        : "getAgreementDetail";
      api[method1]({
        businessId: row.businessId,
        applyNo: row.applyNo,
      }).then((res) => {
        const { constructList, ...rest } = res.data;
        this.baseList = this.isEnergyStorage
          ? [
              {
                title: "申请单号",
                value: rest?.applyNo,
              },
              {
                title: "申请类型",
                value: rest?.applyTypeName,
              },
              {
                title: "项目编码",
                value: rest?.constructProjNo,
              },
              {
                title: "项目名称",
                value: rest?.projName,
              },
              {
                title: "场站名称",
                value: rest?.siteName,
              },
              {
                title: "场站编码",
                value: rest?.locationCode,
              },
              {
                title: "批次",
                value: rest?.times,
              },
              {
                title: "投建协议编码",
                value: rest?.constructProtocalCode,
              },
              {
                title: "投建模型",
                value: rest?.cnConstructModelName,
              },
              {
                title: "销售合同编号",
                value: rest?.salescontractno,
              },
              {
                title: "申请状态",
                value: rest?.applyStatusName,
              },
              {
                title: "商务经理",
                value: rest?.businessManageRname,
              },
            ]
          : [
              {
                title: "申请单号",
                value: res.data?.applyNo,
              },
              {
                title: "申请类型",
                value: res.data?.applyType,
              },
              {
                title: "项目编码",
                value: res.data?.projCode,
              },
              {
                title: "项目名称",
                value: res.data?.projName,
              },
              {
                title: "场站名称",
                value: res.data?.siteName,
              },
              {
                title: "场站编码",
                value: res.data?.stationNo,
              },
              {
                title: "批次",
                value: res.data?.times,
              },
              {
                title: "投建协议编码",
                value: res.data?.constructProtocalCode,
              },
              {
                title: "归属大区",
                value: res.data?.areaZone,
              },
              {
                title: "电表户号归属方",
                value: res.data?.stationName1,
              },
              {
                title: "申请状态",
                value: res.data?.applyStatus,
              },
              {
                title: "场地租赁费/分成比例",
                value: res.data?.rentalFee,
              },
              {
                title: "协议生效时间",
                value: res.data?.startTime,
              },
              {
                title: "协议失效时间",
                value: res.data?.endTime,
              },
              {
                title: "直流桩个数",
                value: res.data?.zhiliuPileNum,
              },
              {
                title: "交流桩个数",
                value: res.data?.jiaoliuPileNum,
              },
              {
                title: "电费计费方式",
                value: res.data?.elecBillMethod,
              },
              {
                title: "电费计费标准",
                value: res.data?.elecBillStandards,
              },
              {
                title: "结算主体名称",
                value: res.data?.elecBillCompany,
              },
              {
                title: "结算周期",
                value: res.data?.elecBillPeriod,
              },
            ];
        this.tableData = constructList;
      });

      if (!row.isEnergyStorage) {
        api
          .getAgreementDetailList({
            businessId: row.businessId,
            applyNo: row.applyNo,
          })
          .then((res) => {
            this.tableData = res.data;
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
</style>
