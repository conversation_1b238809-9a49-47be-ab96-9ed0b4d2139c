//竣工报告弹窗
<template>
  <el-drawer title="竣工报告" :visible.sync="visible" size="70%">
    <div class="drawer-content">
      <el-tabs v-model="tabType">
        <el-tab-pane label="竣工报告初始电子版" name="1"> </el-tab-pane>
        <el-tab-pane label="竣工报告用户上传版" name="2"> </el-tab-pane>
      </el-tabs>
      <el-button
        type="text"
        icon="el-icon-download"
        @click="handleDownload"
        :loading="downloadLoading"
        >下载电子版报告</el-button
      >
    </div>
    <el-tabs
      v-model="activeName"
      type="card"
      v-show="tabType === '1'"
      style="padding:10px 20px"
      v-loading="loading"
      @tab-click="getOriginReport"
    >
      <el-tab-pane
        v-for="(item, index) in originTabList"
        :label="item.label"
        :name="item.name"
        :key="index"
        type="card"
      >
        <div v-if="activeName === item.name">
          <!-- 施工合同页-取url -->
          <div v-if="activeName === 'contract'">
            <FilePreview :fileList="contractList"></FilePreview>
          </div>
          <!-- 非施工合同页-取blob -->
          <div v-else>
            <iframe
              :src="originUrl"
              width="100%"
              style="height:70vh"
              v-if="originUrl"
            />
            <el-empty v-else></el-empty>
            <iframe
              :src="originExtraUrl"
              width="100%"
              height="600"
              v-if="originExtraUrl"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <FilePreview :fileList="uploadList" v-show="tabType === '2'"></FilePreview>
  </el-drawer>
</template>

<script>
import api from "@/api/engineerProject/investBatchList/index.js";
import { fileDownLoad, downloadUrl } from "@/utils/downLoad.js";
import FilePreview from "@/components/Upload/filePreview.vue";
export default {
  components: { FilePreview },
  data() {
    return {
      isEnergyStorage: false,
      downloadLoading: false,
      loading: false,
      tabType: "1",
      activeName: "home",
      visible: false,
      originTabList: [],
      docList: [
        {
          docId: "464434911638704128",
          docName: "1.pdf",
          docType: "4",
          remark: "0.06M",
          storePath:
            "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-05/1720160311563_41f122fd-9849-430c-89ea-59c545a82850.pdf",
          storeType: "1",
          tenantId: "10000",
          uploadTime: "2024-07-05 14:18:31",
          uploader: "10000",
        },
        {
          createTime: "2024-07-04 13:51:21",
          delFlag: "0",
          docId: "464065685258194944",
          docName: "1.jpg",
          docType: "1",
          relaBizId: "464065340389937153",
          remark: "0.08M",
          storePath:
            "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-04/1720072277935_b73ce6b4-67df-4bb1-98ee-7472e25d9fca.jpg",
          storeType: "1",
          tenantId: "10000",
          uploadTime: "2024-07-04 13:51:18",
          uploader: "10054",
        },
      ],
      originUrl: "",
      originExtraUrl: "",
      uploadList: [],
      projectBatchId: "",
      hasExtraList: [],
      tabDict: [
        { label: "竣工报告首页", name: "home", apiFn: "exportHomePage" },
        { label: "目录", name: "catalogue", apiFn: "exportCatalogue" },
        { label: "施工合同", name: "contract", apiFn: "applyDetail" },
        { label: "安全交底", name: "safety", apiFn: "exportSafety" },
        { label: "竣工图", name: "pic", apiFn: "exportProjectComplete" },
        { label: "资产验收单", name: "check", apiFn: "exportFixedAssets" },
        { label: "决算单", name: "calculate", apiFn: "exportBalanceReport" },
        { label: "变更项", name: "change", apiFn: "exportChangeReport" },
        {
          label: "电缆、并网箱进场验收单",
          name: "acceptance",
          apiFn: "exportAcceptanceReport",
        },
        {
          label: "电气绝缘电阻测试记录",
          name: "workCable",
          apiFn: "exportWorkCable",
        },
        {
          label: "电气接地电阻测试记录",
          name: "workResistance",
          apiFn: "exportWorkResistance",
        },
      ],
      contractList: [],
    };
  },
  watch: {
    // activeName: {
    //   handler(val) {
    //     if (val) {
    //       this.getOriginReport(val);
    //     }
    //   },
    //   immediate: true,
    // },
  },
  computed: {
    activeLabel() {
      return this.originTabList.find((x) => x.name === this.activeName)?.label;
    },
  },
  created() {},
  methods: {
    //下载电子报告 在不同tab下载不同报告
    handleDownload() {
      // if (this.tabType == "1") {
      //   this.downloadOriginal();
      // } else {
      //   this.downloadUploaded();
      // }
      this.downloadAllZip();
    },
    //下载压缩包
    async downloadAllZip() {
      this.downloadLoading = true;
      const method =
        this.tabType === "1" ? "exportAllOriginal" : "exportAllUploaded";
      const res = await api[method]({
        projectBatchId: this.projectBatchId,
      }).catch(() => {
        this.downloadLoading = false;
      });
      if (res) {
        api.recordExport({
          projectBatchId: this.projectBatchId,
          operateTypeName: "下载竣工报告",
        });
        this.downloadLoading = false;
        if (method === "exportAllOriginal") {
          const fileName = res.data?.replace(/^.*\/([^\/]+)$/, "\$1");
          downloadUrl(res.data, fileName);
        } else {
          fileDownLoad(res);
        }
      }
    },
    //下载用户上传报告
    async downloadUploaded() {
      for (const x of this.uploadList || []) {
        console.log(x, "======");
        await downloadUrl(x.storePath, x.docName);
      }
    },
    //依次下载初始报告
    async downloadOriginal() {
      for (const x of this.originTabList) {
        let params = { projectBatchId: this.projectBatchId };
        if (x.apiFn == "exportItem") {
          params["workId"] = x.workId;
        }

        const res = await api[x.apiFn](params);
        //施工合同 取开工申请详情中的附件
        if (x.name === "contract") {
          const list = res.data?.constructionFile
            ? JSON.parse(res.data?.constructionFile)
            : [];
          // list.map((x) => {
          //   downloadUrl(x.storePath, x.docName);
          // });
          for (const y of list) {
            await downloadUrl(y.storePath, y.docName);
          }
        } else {
          await fileDownLoad(res);
        }

        //处理附表
        if (x.attachType == "1") {
          api.exportWorkResistance(params).then(async (res) => {
            await fileDownLoad(res);
          });
        } else if (x.attachType == "2") {
          api.exportWorkCable(params).then(async (res) => {
            await fileDownLoad(res);
          });
        }
      }
      // this.originTabList.map((x) => {
      //   let params = { projectBatchId: this.projectBatchId };
      //   if (x.apiFn == "exportItem") {
      //     params["workId"] = x.workId;
      //   }

      //   api[x.apiFn](params).then(async (res) => {
      //     //施工合同 取开工申请详情中的附件
      //     if (x.name === "contract") {
      //       const list = res.data?.constructionFile
      //         ? JSON.parse(res.data?.constructionFile)
      //         : [];
      //       // list.map((x) => {
      //       //   downloadUrl(x.storePath, x.docName);
      //       // });
      //       for (const x of list) {
      //         await downloadUrl(x.storePath, x.docName);
      //       }
      //     } else {
      //       fileDownLoad(res);
      //     }
      //   });
      //   //处理附表
      //   if (x.attachType == "1") {
      //     api.exportWorkResistance(params).then((res) => {
      //       fileDownLoad(res);
      //     });
      //   } else if (x.attachType == "2") {
      //     api.exportWorkCable(params).then((res) => {
      //       fileDownLoad(res);
      //     });
      //   }
      // });
      // const apiFn =
      //   this.tabDict.find((x) => x.name === this.activeName)?.apiFn ||
      //   "exportItem";
      // let params = { projectBatchId: this.projectBatchId };

      // //施工合同 取开工申请详情中的附件
      // if (this.activeName === "contract") {
      //   this.contractList.map((x) => {
      //     downloadUrl(x.storePath, x.docName);
      //   });
      //   return;
      // }

      // if (apiFn === "exportItem") {
      //   params["workId"] = this.activeName;
      // }
      // api[apiFn](params).then((res) => {
      //   fileDownLoad(res);
      // });
      // //处理附表
      // const extraType = this.hasExtraList?.find(
      //   (x) => x.workId === this.activeName
      // )?.attachType;
      // if (extraType == "1") {
      //   api.exportWorkResistance(params).then((res) => {
      //     fileDownLoad(res);
      //   });
      // } else if (extraType == "2") {
      //   api.exportWorkCable(params).then((res) => {
      //     fileDownLoad(res);
      //   });
      // }
    },
    open(row) {
      this.projectBatchId = row.projectBatchId;
      this.isEnergyStorage = row.businessType == "2";
      this.activeName = "";
      this.getOriginTabList();
      this.getUploadReport();
      this.tabType = "1";
      this.visible = true;
    },
    //获取工序项tab
    getOriginTabList() {
      api
        .constructionWorkDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          const headList = [
            { label: "竣工报告首页", name: "home", apiFn: "exportHomePage" },
            { label: "目录", name: "catalogue", apiFn: "exportCatalogue" },
            { label: "施工合同", name: "contract", apiFn: "applyDetail" },
          ];
          let tailList = [
            { label: "竣工图", name: "pic", apiFn: "exportProjectComplete" },
            { label: "资产验收单", name: "check", apiFn: "exportFixedAssets" },
            {
              label: "决算单",
              name: "calculate",
              apiFn: "exportBalanceReport",
            },
            { label: "变更项", name: "change", apiFn: "exportChangeReport" },
          ];
          //储能不需要安全交底
          if (!this.isEnergyStorage) {
            tailList.unshift({
              label: "安全交底",
              name: "safety",
              apiFn: "exportSafety",
            });
          }
          const itemList = res.data?.map((x) => {
            return {
              ...x,
              label: x.workName,
              name: x.workId,
              apiFn: "exportItem",
            };
          });
          //有附表的工序项
          this.hasExtraList = res.data?.filter((x) => x.attachType);
          this.originTabList = [...headList, ...itemList, ...tailList];
          this.activeName = this.originTabList[0].name;
          this.getOriginReport();
        });
    },
    //获取每页对应的报告
    getOriginReport() {
      this.originUrl = "";
      this.loading = true;
      const apiFn =
        this.tabDict.find((x) => x.label === this.activeLabel)?.apiFn ||
        "exportItem";
      let params = { projectBatchId: this.projectBatchId };
      //施工合同 取开工申请详情中的附件
      if (this.activeName === "contract") {
        api[apiFn](params)
          .then((res) => {
            this.loading = false;
            this.contractList = res.data?.constructionFile
              ? JSON.parse(res.data?.constructionFile)
              : [];
            console.log(this.contractList, "施工合同");
          })
          .catch(() => {
            this.loading = false;
          });
        return;
      }
      //动态工序项
      if (
        apiFn === "exportItem" ||
        [
          "电缆、并网箱进场验收单",
          "电气绝缘电阻测试记录",
          "电气接地电阻测试记录",
        ].includes(this.activeLabel)
      ) {
        params["workId"] = this.activeName;
      }
      api[apiFn](params)
        .then((res) => {
          this.loading = false;
          this.$nextTick(() => {
            if (apiFn === "exportFixedAssets") {
              //资产验收单改为url预览
              this.originUrl = res.data;
            } else {
              this.originUrl = this.previewPdf(res);
              console.log(this.originUrl, "originUrl");
            }
          });
        })
        .catch(() => {
          this.loading = false;
        });
      //处理附表
      const extraType = this.hasExtraList?.find(
        (x) => x.workId === this.activeName
      )?.attachType;
      if (extraType == "1") {
        api.exportWorkResistance(params).then((res) => {
          this.originExtraUrl = this.previewPdf(res);
        });
      } else if (extraType == "2") {
        api.exportWorkCable(params).then((res) => {
          this.originExtraUrl = this.previewPdf(res);
        });
      } else {
        this.originExtraUrl = "";
      }
    },
    previewPdf(res) {
      const blob = new Blob([res.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      return url;
    },
    //获取已盖章版报告
    getUploadReport() {
      api
        .uploadReport({
          projectBatchId: this.projectBatchId,
          businessType: "竣工报告",
        })
        .then((res) => {
          this.uploadList = res.data?.cmDocList;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
}
// /deep/ .el-tabs__nav-wrap::after {
//   display: none;
// }
/deep/ .el-tabs__nav-scroll {
  overflow-x: auto;
}
/deep/ .el-tabs__nav-prev,
/deep/ .el-tabs__nav-next {
  // display: none;
}
/deep/ .el-tabs__nav-wrap.is-scrollable {
  // padding: 0 0;
}
</style>
