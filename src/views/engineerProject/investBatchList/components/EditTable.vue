<!-- 弹窗添加表格行+表格可编辑 -->
<template>
  <div>
    <div class="mb20">
      <el-button @click="handleAdd" type="primary">添加</el-button>
      <el-button @click="handleClearAll" type="primary">全部清空</el-button>
      <el-button @click="handleClear" type="primary">清空</el-button>
    </div>
    <vxe-grid
      :columns="columns"
      :data="selectedRows"
      align="center"
      resizable
      max-height="500"
      show-overflow
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      :rowConfig="{
        keyField: 'materialId',
        isCurrent: true,
      }"
      ref="baseTable"
      :edit-rules="validRules"
      @edit-closed="handleEditClosed"
    >
      <template #operation="{ row }">
        <el-button @click="rowDelete(row)" type="text">删除</el-button>
      </template>

      <template #edit_number="{ row,column }">
        <el-input-number
          v-model="row[column.property]"
          :max="999999"
          :min="0"
          :controls="false"
          size="small"
        ></el-input-number>
      </template>
      <template #edit_textarea="{ row,column }">
        <el-input
          v-model="row[column.property]"
          type="textarea"
          size="small"
        ></el-input>
      </template>
    </vxe-grid>
    <el-dialog
      title="添加设备"
      :visible="visible"
      :close-on-click-modal="false"
      @close="closeVisible"
      append-to-body
      width="50%"
    >
      <BuseCrud
        ref="deviceTable"
        :loading="loading"
        :tablePage="tablePage"
        :pagerProps="pagerProps"
        :tableColumn="deviceColumns"
        :tableData="deviceTableData"
        :tableProps="tableProps"
        :modalConfig="modalConfig"
        @loadData="getDeviceList"
        selectStyleType="infoTip"
        @handleBatchSelect="handleBatchSelect"
        :showSelectNum="true"
        :customSelectCount="selectNum"
      ></BuseCrud>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeVisible">取 消</el-button>
        <el-button type="primary" @click.stop="handleConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import deviceApi from "@/api/operationMaintenanceManage/materiel/index.js";

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    customColumns: {
      type: Array,
      default: () => null,
    },
    customRules: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      validRules: this.customRules || {
        num: [{ required: true, message: "请输入" }],
        meshCabinetsNum: [{ required: true, message: "请输入" }],
      },
      visible: false,
      unitOptions: [],
      columns: this.customColumns || [
        { type: "checkbox", width: 60 },
        { type: "seq", title: "序号", width: 60 },
        { field: "materialName", title: "设备名称" },
        { field: "materialModel", title: "规格型号" },
        {
          field: "unit",
          title: "单位",
          formatter: ({ cellValue }) => {
            return this.unitOptions?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
        },
        {
          field: "num",
          title: "数量",
          editRender: { placeholder: "请输入", autofocus: ".el-input__inner" },
          slots: { edit: "edit_number" },
        },
        {
          field: "totalCapacity",
          title: "总容量（kWh)",
        },
        {
          field: "meshCabinetsNum",
          title: "并网柜数量（台)",
          editRender: { placeholder: "请输入", autofocus: ".el-input__inner" },
          slots: { edit: "edit_number" },
        },
        {
          field: "operation",
          title: "操作",
          fixed: "right",
          slots: { default: "operation" },
          showOverflow: false,
        },
      ],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        rowConfig: {
          keyField: "materialId",
          isCurrent: true,
        },
      },
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      loading: false,
      deviceColumns: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "materialName",
          title: "物料名称",
        },
        {
          field: "materialModel",
          title: "规格型号",
        },
        {
          field: "unit",
          title: "单位",
          formatter: ({ cellValue }) => {
            return this.unitOptions?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
        },
      ],
      deviceTableData: [],
      //buse参数-e
      tableData: [],
      selectedRows: this.value,
    };
  },
  computed: {
    selectNum() {
      return this.selectedRows?.length || 0;
    },
    modalConfig() {
      return {
        menu: false,
        addBtn: false,
      };
    },
  },
  watch: {
    value(newValue) {
      this.selectedRows = newValue;
    },
    selectedRows(newInternalValue) {
      this.$emit("input", newInternalValue);
      console.log("变了", newInternalValue);
    },
  },
  created() {
    this.getDicts("material_unit").then((response) => {
      this.unitOptions = response?.data;
    });
  },
  methods: {
    async validTable() {
      const table = this.$refs.baseTable;
      const errMap = await table.validate(true).catch((errMap) => errMap);
      return errMap;
    },
    handleEditClosed({ row, rowIndex }) {
      this.selectedRows[rowIndex]["totalCapacity"] = this.calculateCap(row);
    },
    calculateCap(row) {
      // 正则表达式：匹配以数字开头，后面跟着 'kwh' 或 'kw.h'，支持大小写
      const regex = /^(\d+(\.\d+)?)\s*(kwh|kw\.h|kw·h)$/i;

      const match = row.materialModel?.match(regex);

      if (match && row.num) {
        return parseFloat(match[1] * +row.num); // 返回匹配到的数字部分
      }
      return null; // 如果不匹配，返回 null
    },
    clearTips() {
      this.selectedRows = [];
      const table = this.$refs.deviceTable.getVxeTableRef();
      table?.setAllCheckboxRow(false);
    },
    //选好设备了
    handleConfirm() {
      this.tableData = this.selectedRows;
      this.closeVisible();
      console.log(this.selectedRows, "selected");
    },
    closeVisible() {
      this.visible = false;
    },
    handleAdd() {
      this.visible = true;
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.getDeviceList();
    },
    handleClearAll() {
      this.$confirm("确认删除所有设备吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.selectedRows = [];
      });
    },
    handleClear() {
      console.log("tabledata", this.selectedRows);
      const deleteRowIds = this.$refs.baseTable
        .getCheckboxRecords()
        ?.map((x) => x.materialId);
      if (deleteRowIds.length === 0) {
        this.$message.warning("请勾选至少一条数据");
        return;
      }
      this.$confirm("确认删除所选择的设备吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.selectedRows = this.selectedRows?.filter(
          (x) => !deleteRowIds.includes(x.materialId)
        );
      });
    },
    rowDelete(row) {
      this.$confirm("确认删除该设备吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.selectedRows = this.selectedRows?.filter(
          (x) => x.materialId !== row.materialId
        );
      });
    },
    getDeviceList() {
      this.loading = true;
      let params = {
        status: 0,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      deviceApi
        .getTableData(params)
        .then((res) => {
          if (res.code === "10000") {
            this.deviceTableData = res.data;
            this.tablePage.total = res.total;
            this.loading = false;
            this.setSelectedRows();
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    setSelectedRows() {
      this.$nextTick(() => {
        const table = this.$refs.deviceTable.getVxeTableRef();
        table.setAllCheckboxRow(false);
        this.selectedRows?.map((x) => {
          let selectRow = table.getRowById(x.materialId);
          if (selectRow) {
            table.setCheckboxRow(selectRow, true);
          }
        });
      });
    },
    handleBatchSelect(arr, isClearAll = false) {
      if (isClearAll) {
        this.selectedRows = [];
      } else {
        // const reserveData = this.selectedRows?.filter(
        //   (x) =>
        //     !this.deviceTableData
        //       ?.map((y) => y.materialId)
        //       ?.includes(x.materialId)
        // );
        // this.selectedRows = [...reserveData, ...arr];
        // 1. 找出当前页中取消勾选的数据（在 deviceTableData 中存在但在 arr 中不存在）
        const unselectedItems = this.deviceTableData.filter(
          (item) =>
            !arr.some((selected) => selected.materialId === item.materialId)
        );

        // 2. 从 selectedRows 中移除取消勾选的数据
        const updatedSelectedRows = this.selectedRows.filter(
          (row) =>
            !unselectedItems.some((item) => item.materialId === row.materialId)
        );

        // 3. 找出 arr 中新增的选项（在 selectedRows 中不存在）
        const newItems = arr.filter(
          (item) =>
            !this.selectedRows.some((row) => row.materialId === item.materialId)
        );

        // 4. 更新 selectedRows
        this.selectedRows = [...updatedSelectedRows, ...newItems];
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number--small {
  width: 100px;
}
</style>
