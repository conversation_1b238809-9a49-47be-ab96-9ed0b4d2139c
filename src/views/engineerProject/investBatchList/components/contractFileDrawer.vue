<template>
  <el-drawer title="附件信息-施工合同" :visible.sync="visible" size="60%">
    <div class="drawer-content">
      <FilePreview :fileList="uploadList"></FilePreview>
      <!-- <div
        v-for="(item, index) in uploadList"
        :key="index"
        style="margin-bottom: 10px;"
      >
        <embed
          :src="item.storePath"
          width="100%"
          height="600"
          v-if="item.docName.split('.')[1] === 'pdf'"
        />
        <el-link :href="item.storePath" target="_blank" v-else>{{
          item.docName
        }}</el-link>
      </div> -->
    </div>
  </el-drawer>
</template>

<script>
import FilePreview from "@/components/Upload/filePreview.vue";

export default {
  components: { FilePreview },
  data() {
    return { visible: false, uploadList: [] };
  },
  methods: {
    open(val) {
      console.log(val, "附件");
      this.visible = true;
      this.uploadList = val;
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
</style>
