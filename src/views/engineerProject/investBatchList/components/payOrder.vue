//详情-付款单详情抽屉
<template>
  <el-drawer title="付款单详情" :visible.sync="visible" size="60%">
    <div class="drawer-content">
      <el-card>
        <CommonTitle class="mb10" title="基本信息" />
        <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="采购清单" />
        <vxe-grid
          :columns="tableColumns"
          :data="tableData"
          resizable
          align="center"
        ></vxe-grid>
      </el-card>
    </div>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import CommonTitle from "@/components/commonTitle";
export default {
  components: { CommonTitle, BaseDescriptions },
  data() {
    return {
      visible: false,
      baseList: [],
      tableColumns: [
        { title: "采购申请单号", field: "purchaseNo" },
        { title: "项目编码", field: "projNo" },
        { title: "项目名称", field: "projName" },
        { title: "采购合同编码", field: "purchaseConNo" },
        { title: "采购合同名称", field: "purchaseConName" },
        { title: "成本中心", field: "costCenterName" },
        { title: "采购内容", field: "installType" },
        { title: "货物（劳务）名称", field: "deviceName" },
        { title: "数量", field: "deviceCount" },
        { title: "单价", field: "price" },
        { title: "累计申请付款金额（不含本次申请金额）", field: "totalPaymentAmount" },
        { title: "合同金额", field: "paymentMoney" },
      ],
      tableData: [],
    };
  },
  methods: {
    open(row) {
      console.log(row, "打开");
      this.visible = true;
      api.getPayDetail({ applyNo: row.applyNo }).then((res) => {
        this.baseList = [
          {
            title: "付款申请单号",
            value: res.data?.payApplyNo,
          },
          {
            title: "项目编码",
            value: res.data?.projNo,
          },
          {
            title: "采购合同编码",
            value: res.data?.purchaseConNo,
          },
          {
            title: "项目名称",
            value: res.data?.projName,
          },
          {
            title: "采购合同名称",
            value: res.data?.purchaseConName,
          },
          {
            title: "项目所属公司",
            value: res.data?.projCompanyName,
          },
          {
            title: "供应商",
            value: res.data?.factoryName,
          },
          {
            title: "项目所属成本中心",
            value: res.data?.costCenterName,
          },
          {
            title: "采购类型",
            value: "",
          },
          {
            title: "付款申请金额",
            value: res.data?.practicalTotal,
          },
          {
            title: "是否固定资产",
            value: "",
          },
          {
            title: "付款方式",
            value: "",
          },
          {
            title: "审批状态",
            value: res.data?.fbillStatus,
          },
          {
            title: "金蝶付款单状态",
            value: res.data?.fbillStatus,
          },
          {
            title: "申请时间",
            value: res.data?.createrTime,
          },
          {
            title: "预计付款日期",
            value: "",
          },
          {
            title: "备注",
            value: "",
          },
          {
            title: "",
            value: "",
          },
        ];
        // this.tableData = [];
      });


      api.getPayDetailList({ applyNo: row.applyNo }).then((res) => {
        this.tableData = res.data;
      });


    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
</style>
