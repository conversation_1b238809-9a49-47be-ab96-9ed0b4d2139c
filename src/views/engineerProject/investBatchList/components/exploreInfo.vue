//详情-踏勘信息抽屉
<template>
  <el-drawer title="踏勘信息" :visible.sync="visible" size="70%">
    <div class="drawer-content">
      <el-tabs v-model="activeName" class="anchor" @tab-click="anchorFn">
        <el-tab-pane
          :label="item.label"
          :name="item.name"
          v-for="(item, index) in tabList"
          :key="index"
        ></el-tab-pane>
      </el-tabs>
      <el-card id="base" style="margin-top: 20px;">
        <CommonTitle class="mb10" title="基本信息" />
        <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
      </el-card>
      <div v-if="!isEnergyStorage">
        <el-card>
          <CommonTitle class="mb10" title="居间商信息" />
          <BaseDescriptions :list="middleBusinessList" :column="2">
          </BaseDescriptions>
        </el-card>
        <el-card>
          <CommonTitle class="mb10" title="场地方信息" />
          <BaseDescriptions :list="placeList" :column="2"> </BaseDescriptions>
        </el-card>
        <el-card>
          <CommonTitle class="mb10" title="配置信息" />
          <BaseDescriptions :list="configList" :column="2" class="mb10">
          </BaseDescriptions>
          <vxe-grid
            :columns="configColumns"
            :data="configTableData"
            align="center"
            resizable
          ></vxe-grid>
        </el-card>
        <el-card id="business">
          <CommonTitle class="mb10" title="商务信息" />
          <BaseDescriptions :list="businessList" :column="2">
          </BaseDescriptions>
          <h5>场地属性</h5>
          <vxe-grid
            resizable
            align="center"
            :columns="placeAttrColumns"
            :data="placeAttrTableData"
          ></vxe-grid>
          <h5>场地信息</h5>
          <vxe-grid
            :columns="placeInfoColumns"
            :data="placeInfoTableData"
            resizable
            align="center"
          >
            <template #file="{row,column}">
              <FileIcons :list="JSON.parse(row[column.property])"></FileIcons>
            </template>
          </vxe-grid>
          <h5>共存的竞品信息</h5>
          <vxe-grid
            :columns="productColumns"
            :data="productTableData"
            resizable
            align="center"
          ></vxe-grid>
          <h5>用户信息</h5>
          <BaseDescriptions :list="userInfoList" :column="2">
          </BaseDescriptions>
          <h5>商方条件</h5>
          <BaseDescriptions :list="businessConditionList" :column="2">
            <template #links="{itemVal}">
              <FileIcons :list="itemVal"></FileIcons>
            </template>
          </BaseDescriptions>
          <h5>项目电价</h5>
          <BaseDescriptions :list="powerPriceList" :column="2">
          </BaseDescriptions>
          <h5>尖峰平谷电价</h5>
          <vxe-grid
            resizable
            :columns="powerPriceColumns"
            :data="powerPriceTableData"
            align="center"
          ></vxe-grid>
          <h5>安装环境</h5>
          <BaseDescriptions :list="installEnvList" :column="2">
            <template #links="{itemVal}">
              <FileIcons :list="itemVal"></FileIcons>
            </template>
          </BaseDescriptions>
          <h5>施工发包模式</h5>
          <BaseDescriptions :list="packageList" :column="2"> </BaseDescriptions>
        </el-card>
        <el-card>
          <CommonTitle class="mb10" title="工程信息" id="engineer" />
          <BaseDescriptions :list="engineerList" :column="2">
            <template #links="{itemVal}">
              <FileIcons :list="itemVal"></FileIcons>
            </template>
          </BaseDescriptions>
        </el-card>
        <el-card>
          <CommonTitle class="mb10" title="经营初审" id="examine" />
          <BaseDescriptions :list="firstExamineList" :column="2" class="mb10">
          </BaseDescriptions>
          <vxe-grid
            resizable
            :columns="firstExamineColumns"
            :data="firstExamineTableData"
            align="center"
          ></vxe-grid>
        </el-card>
        <el-card>
          <CommonTitle class="mb10" title="投建审批和终审" id="check" />
          <BaseDescriptions :list="finalExamineList" :column="2" class="mb10">
          </BaseDescriptions>
          <h5>投建审批和终审汇总数据</h5>
          <vxe-grid
            resizable
            :columns="finalExamineColumns"
            :data="finalExamineTableData"
            align="center"
          ></vxe-grid>
        </el-card>
        <el-card>
          <CommonTitle class="mb10" title="补充信息" id="more" />
          <BaseDescriptions :list="supplyList" :column="2"> </BaseDescriptions>
        </el-card>
        <!--      <el-card>-->
        <!--        <CommonTitle class="mb10" title="审批信息" id="check" />-->
        <!--        <BaseDescriptions :list="checkList" :column="2"> </BaseDescriptions>-->
        <!--      </el-card>-->
        <!--      <el-card>-->
        <!--        <CommonTitle class="mb10" title="评论信息" id="remark" />-->
        <!--        <BaseDescriptions :list="remarkList" :column="1"> </BaseDescriptions>-->
        <!--      </el-card>-->
      </div>
      <div v-else>
        <el-card id="billing">
          <CommonTitle class="mb10" title="分段计费" />
          <BaseDescriptions :list="segmentedBillingList" :column="2">
          </BaseDescriptions>
        </el-card>
        <el-card id="config">
          <CommonTitle class="mb10" title="配置信息" />
          <BaseDescriptions :list="configInfoList" :column="2" class="mb20">
          </BaseDescriptions>
          <vxe-grid
            resizable
            :columns="configInfoColumns"
            :data="configInfoTableData"
            align="center"
          ></vxe-grid>
        </el-card>
        <el-card id="load">
          <CommonTitle class="mb10" title="负荷信息" />
          <BaseDescriptions :list="loadInfoList" :column="2" class="mb20">
            <template #links="{itemVal}">
              <FileIcons :list="itemVal"></FileIcons>
            </template>
          </BaseDescriptions>
          <vxe-grid
            resizable
            :columns="energyUseColumns"
            :data="energyUseTableData"
            align="center"
          ></vxe-grid>
        </el-card>
        <el-card id="volume">
          <CommonTitle class="mb10" title="容量确认" />
          <BaseDescriptions :list="volumeConfirmList" :column="2">
          </BaseDescriptions>
        </el-card>
        <el-card id="cost">
          <CommonTitle class="mb10" title="采购成本" />
          <BaseDescriptions :list="purchaseCostList" :column="2">
          </BaseDescriptions>
        </el-card>
        <el-card id="invest">
          <CommonTitle class="mb10" title="投资确认" />
          <BaseDescriptions :list="investList" :column="2"> </BaseDescriptions>
        </el-card>
        <el-card id="time">
          <CommonTitle class="mb10" title="时间信息" />
          <BaseDescriptions :list="timeInfoList" :column="2">
          </BaseDescriptions>
        </el-card>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import CommonTitle from "@/components/commonTitle";
import FileIcons from "@/components/FileIcons/index.vue";
import { getNameFromUrl } from "@/utils/downLoad.js";
export default {
  components: { CommonTitle, BaseDescriptions, FileIcons },
  data() {
    return {
      visible: false,
      activeName: "base",
      configColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "安装方式", field: "安装方式" },
        { title: "桩类型", field: "桩类型" },
        { title: "单桩功率（kW）", field: "单桩功率_kw" },
        { title: "桩数", field: "桩数" },
        { title: "枪数", field: "枪数" },
        { title: "桩型号", field: "桩型号" },
        { title: "设备成本（含税）", field: "设备成本_(含税)" },
        { title: "总功率（kW）", field: "总功率_kw" },
      ],
      configTableData: [],
      placeAttrColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "场地已投入使用年限", field: "场地已投入使用年限" },
        { title: "当前小区入住数", field: "当前小区入住户数" },
        { title: "入住率", field: "入住率_大概即可" },
        {
          title: "安装层数",
          field: "安装层数",
          formatter: ({ cellValue }) => {
            return cellValue ? JSON.parse(cellValue).toString() : cellValue;
          },
        },
        { title: "预计油车占位问题程度", field: "预计油车占位问题程度" },
        { title: "当前公共停车位个数", field: "当前公共停车位个数" },
        { title: "找车时间", field: "找车位时间" },
        {
          title: "停车内外道闸",
          field: "停车内外道闸",
          formatter: ({ cellValue }) => {
            return cellValue ? JSON.parse(cellValue).toString() : cellValue;
          },
        },
      ],
      placeAttrTableData: [],
      placeInfoColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "合作方角色", field: "合作方角色", width: 100 },
        { title: "场地负责人", field: "场地负责人", width: 100 },
        { title: "负责人联系方式", field: "负责人联系方式", width: 120 },
        { title: "停车收费类型", field: "停车收费类型", width: 120 },
        {
          title: "所有车辆停车收费标准",
          field: "所有车辆停车收费标准",
          width: 100,
        },
        {
          title: "充电车辆停车收费标准",
          field: "充电车辆停车收费标准",
          width: 100,
        },
        { title: "停车收费（元）", field: "停车收费/元", width: 100 },
        {
          title: "电源及车位产权归属",
          field: "电源及车位产权归属",
          width: 100,
        },
        {
          title: "产权方是否同意出具委托函",
          field: "产权方是否同意出具委托函",
          width: 100,
        },
        {
          title: "附件-合作方与产权方的关系证明",
          field: "附件_合作方与产权方的关系证明",
          slots: { default: "file" },
          width: 100,
        },
        {
          title: "附件-土地产权证明",
          field: "附件_土地产权证明",
          slots: { default: "file" },
          width: 100,
        },
      ],
      placeInfoTableData: [
        // {
        //   docList: [
        //     {
        //       createTime: "2024-07-04 13:49:59",
        //       delFlag: "0",
        //       docId: "464065332122963968",
        //       docName: "1.jpg",
        //       docType: "1",
        //       previewPath:
        //         "https://shangfu-charge.bangdao-tech.com/charging-maintenance-test/10000/DOC/2024-07-04/1720072196805_13a22dcc-12c2-460d-a215-fdbcf2f5754a.jpg",
        //       relaBizId: "464065340389937153",
        //       remark: "0.08M",
        //       storePath:
        //         "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-04/1720072196805_13a22dcc-12c2-460d-a215-fdbcf2f5754a.jpg",
        //       storeType: "1",
        //       tenantId: "10000",
        //       uploadTime: "2024-07-04 13:49:57",
        //       uploader: "10054",
        //       uploaderName: "燕燕",
        //     },
        //     {
        //       createTime: "2024-07-02 18:49:39",
        //       delFlag: "0",
        //       docId: "463415972729126912",
        //       docName: "00_基础要求_必须执行.xlsx",
        //       docType: "3",
        //       previewPath:
        //         "https://shangfu-charge.bangdao-tech.com/charging-maintenance-test/10000/DOC/2024-07-02/1719917377672_bbccf9de-2f36-4a66-970e-a6c0fd69c350.xlsx",
        //       relaBizId: "463415980157239297",
        //       remark: "0.02M",
        //       storePath:
        //         "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-02/1719917377672_bbccf9de-2f36-4a66-970e-a6c0fd69c350.xlsx",
        //       storeType: "1",
        //       tenantId: "10000",
        //       uploadTime: "2024-07-02 18:49:37",
        //       uploader: "10000",
        //       uploaderName: "系统管理员",
        //     },
        //     {
        //       docId: "470602918509391872",
        //       docName:
        //         "20230505目的地充电-施工作业指导书 -适用于标准化建站（验收）QBLXNY-CDZ-2023-1.doc",
        //       docType: "2",
        //       remark: "16.98M",
        //       storePath:
        //         "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-22/1721630878651_20230505目的地充电-施工作业指导书 -适用于标准化建站（验收）QBLXNY-CDZ-2023-1.doc",
        //       storeType: "1",
        //       tenantId: "10000",
        //       uploadTime: "2024-07-22 14:47:59",
        //       uploader: "10000",
        //     },
        //     {
        //       docId: "470602736896028672",
        //       docName:
        //         "1721466034946_无锡南丰御园（物业门口）_LSNY24J0986_充电桩、配电箱安装验收 (1).pdf",
        //       docType: "4",
        //       remark: "0.07M",
        //       storePath:
        //         "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-22/1721630835727_1721466034946_无锡南丰御园（物业门口）_LSNY24J0986_充电桩、配电箱安装验收 (1).pdf",
        //       storeType: "1",
        //       tenantId: "10000",
        //       uploadTime: "2024-07-22 14:47:15",
        //       uploader: "10000",
        //     },
        //   ],
        //   fileList: [
        //     {
        //       createTime: "2024-07-04 13:49:59",
        //       delFlag: "0",
        //       docId: "464065332122963968",
        //       docName: "1.jpg",
        //       docType: "1",
        //       previewPath:
        //         "https://shangfu-charge.bangdao-tech.com/charging-maintenance-test/10000/DOC/2024-07-04/1720072196805_13a22dcc-12c2-460d-a215-fdbcf2f5754a.jpg",
        //       relaBizId: "464065340389937153",
        //       remark: "0.08M",
        //       storePath:
        //         "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-07-04/1720072196805_13a22dcc-12c2-460d-a215-fdbcf2f5754a.jpg",
        //       storeType: "1",
        //       tenantId: "10000",
        //       uploadTime: "2024-07-04 13:49:57",
        //       uploader: "10054",
        //       uploaderName: "燕燕",
        //     },
        //   ],
        // },
      ],
      productColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "竞品品牌名称", field: "竞品品牌名称" },
        { title: "竞品枪数", field: "竞品枪数" },
        { title: "竞品电费", field: "竞品电费" },
        { title: "竞品服务费", field: "竞品服务费" },
      ],
      productTableData: [],
      powerPriceColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "尖电价（元）", field: "尖电价/元" },
        { title: "峰电价（元）", field: "峰电价/元" },
        { title: "平电价（元）", field: "平电价/元" },
        { title: "谷电价（元）", field: "谷电价/元" },
      ],
      powerPriceTableData: [],
      firstExamineColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "直流枪数", field: "installType" },
        { title: "交流枪数", field: "installType" },
        { title: "车桩比", field: "installType" },
        { title: "场地属性", field: "installType" },
      ],
      firstExamineTableData: [],
      finalExamineColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "单桩总成本", field: "installType" },
        { title: "单桩设备成本", field: "installType" },
        { title: "单桩施工成本", field: "installType" },
        { title: "单桩商务成本", field: "installType" },
        { title: "场地属性", field: "installType" },
        { title: "是否指定施工商", field: "installType" },
      ],
      finalExamineTableData: [],
      baseList: [],
      middleBusinessList: [],
      placeList: [],
      configList: [],
      businessList: [],
      userInfoList: [],
      businessConditionList: [],
      powerPriceList: [],
      installEnvList: [],
      packageList: [],
      engineerList: [],
      firstExamineList: [],
      finalExamineList: [],
      supplyList: [],
      checkList: [],
      remarkList: [],
      isEnergyStorage: false,
      //分段计费
      segmentedBillingList: [],
      //配置信息
      configInfoList: [],
      configInfoColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "变压器类型", field: "transformerType" },
        { title: "变压器台数", field: "transformerNum" },
        { title: "变压器容量kVA", field: "transformerCapacity" },
      ],
      configInfoTableData: [],
      //负荷信息
      loadInfoList: [],
      energyUseColumns: [],
      energyUseTableData: [],
      //容量确认
      volumeConfirmList: [],
      //采购成本
      purchaseCostList: [],
      //投资确认
      investList: [],
      //时间信息
      timeInfoList: [],
    };
  },
  computed: {
    tabList() {
      return this.isEnergyStorage
        ? [
            { label: "基本信息", name: "base" },
            { label: "分段计费", name: "billing" },
            { label: "配置信息", name: "config" },
            { label: "负荷信息", name: "load" },
            { label: "容量确认", name: "volume" },
            { label: "采购成本", name: "cost" },
            { label: "投资确认", name: "invest" },
            { label: "时间信息", name: "time" },
          ]
        : [
            { label: "基本信息", name: "base" },
            { label: "商务信息", name: "business" },
            { label: "工程信息", name: "engineer" },
            { label: "经营初审", name: "examine" },
            { label: "投建审批和终审", name: "check" },
            { label: "补充信息", name: "more" },
          ];
    },
  },
  created() {
    this.energyUseColumns.push({ title: "年月", field: "busiMonth", width: 80 });
    for (var i = 0; i < 24; i++) {
      this.energyUseColumns.push({ title: i + "时", field: "hr" + i, width: 110 });
    }
  },
  methods: {
    anchorFn() {
      document.querySelector(`#${this.activeName}`)?.scrollIntoView({
        behavior: "smooth",
        // 定义动画过渡效果， "auto"或 "smooth" 之一。默认为 "auto"
        block: "start",
        // 定义垂直方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "start"
        inline: "nearest",
        // 定义水平方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "nearest"
      });
    },
    open(row) {
      this.visible = true;
      this.isEnergyStorage = row.isEnergyStorage;
      const method = row.isEnergyStorage
        ? "getEnergyStorageDetail"
        : "getExploreDetail";
      api[method]({ businessId: row.applyNo }).then((res) => {
        const {
          agentInfo,
          baseInfo,
          constructFinalAudit,
          manageAudit,
          pileConfigInfo,
          projectInfo,
          siteInfo,
          stationInfo,
          supplementaryInfo,
          transformers,
          sync24HrEnergyUses,
          ...rest
        } = res.data;
        this.baseList = this.isEnergyStorage
          ? [
              {
                title: "场站名称",
                value: rest?.stationName,
              },
              {
                title: "项目名称",
                value: rest?.projectName,
              },
              {
                title: "场站编码",
                value: rest?.stationCode,
              },
              {
                title: "项目编码",
                value: rest?.projectCode,
              },
              {
                title: "踏勘编码",
                value: rest?.businessId,
              },
              {
                title: "踏勘审批通过时间",
                value: rest?.surveyPassTime,
              },
              {
                title: "储能类型",
                value: rest?.storageType,
              },
              {
                title: "投建模式",
                value: rest?.investMode,
              },
              {
                title: "投建法人实体(资产归属法人实体)",
                value: rest?.investLegalEntity,
              },
              {
                title: "业绩归属公司",
                value: rest?.businessOwnCompany,
              },
              {
                title: "业务负责人",
                value: rest?.businessOwner,
              },
              {
                title: "企业所得税率",
                value: rest?.corporateIncomeTax,
              },
              {
                title: "运营商名称",
                value: rest?.operatorName,
              },
              {
                title: "平台方名称",
                value: rest?.platformName,
              },
              {
                title: "是否与新电途互联互通",
                value: rest?.isXdtConnect,
              },
              {
                title: "详细地址",
                value: rest?.stationAddress,
              },
              {
                title: "站点定位",
                value: rest?.stationPosition,
              },
              {
                title: "合同年限",
                value: rest?.contractYear,
              },
              {
                title: "场地租赁剩余年限",
                value: rest?.remainingLeaseVenue,
              },
              {
                title: "场地方名称",
                value: rest?.siteCompanyName,
              },
              {
                title: "场地方分成税率",
                value: rest?.siteTaxRate,
              },
              {
                title: "场地方分成比例",
                value: rest?.customerSharing,
              },
              {
                title: "居间方开票税率",
                value: rest?.agentTaxRate,
              },
              {
                title: "居间商名称",
                value: rest?.agentCompanyName,
              },
              {
                title: "一次性商务费(元)",
                value: rest?.businessExpense,
              },
              {
                title: "居间商分成比例",
                value: rest?.agentShareRatio,
              },
              {
                title: "人员激励费用",
                value: rest?.encourageAward,
              },
              {
                title: "站点联系人",
                value: rest?.stationContacts,
              },
              {
                title: "联系方式",
                value: rest?.stationPhone,
              },
              {
                title: "商务人员",
                value: rest?.businessManager,
              },
              {
                title: "所属部门",
                value: rest?.businessDept,
              },
              {
                title: "",
                value: "",
              },
            ]
          : [
              {
                title: "踏勘编码",
                value: baseInfo?.businessId,
              },
              {
                title: "绑定其他工单",
                value: baseInfo?.stationName1,
              },
              {
                title: "投资法人实体（资产归属法人实体）",
                value: baseInfo?.investmentAndConstructionLegalEntity,
              },
              {
                title: "桩资产绑定工单",
                value: baseInfo?.stationName1,
              },
              {
                title: "投建类型",
                value: baseInfo?.constuctType,
              },
              {
                title: "绑定的工单号",
                value: baseInfo?.bangdingZichanBusinessId,
              },
              {
                title: "站类型",
                value: baseInfo?.stationJiaozhiType,
              },
              {
                title: "分批投建批次",
                value: baseInfo?.yunguanPici,
              },
              {
                title: "是否报电",
                value: baseInfo?.needCall,
              },
              {
                title: "经纬度",
                value: baseInfo?.sureLonLat,
              },
              {
                title: "归属大区",
                value: baseInfo?.belongingToTheRegion,
              },
              {
                title: "详细地址",
                value: baseInfo?.address,
              },
              {
                title: "业绩归属部门",
                value: baseInfo?.performanceAttributableDepartment,
              },
              {
                title: "是否展开工程踏勘填报",
                value: baseInfo?.stationName1,
              },
              {
                title: "场地名称",
                value: baseInfo?.stationName,
              },
              {
                title: "是否展开最终审批",
                value: baseInfo?.stationName1,
              },
              {
                title: "小区名称",
                value: baseInfo?.communityName,
              },
              {
                title: "价格分类",
                value: baseInfo?.priceClassification,
              },
            ];
        if (this.isEnergyStorage) {
          //储能相关字段
          //分段计费
          this.segmentedBillingList = [
            {
              title: "电价执行标准",
              value: rest?.electricityPriceExecutionStandards,
            },
            // {
            //   title: "电价信息数据选择",
            //   value: rest?.elecPriceData,
            // },
            {
              title: "用电类别",
              value: rest?.elecTypeCompany,
            },
            {
              title: "用电分类",
              value: rest?.elecTypeOneTwo,
            },
            {
              title: "业务负责人",
              value: rest?.businessOwner,
            },
            {
              title: "电压等级",
              value: rest?.voltageLevel,
            },
            {
              title: "是否区分冬夏尖峰月",
              value: rest?.isTopMonthWinter,
            },
            {
              title: "尖峰月份",
              value: rest?.topMonth,
            },
            {
              title: "普通月峰谷价差",
              value: rest?.pricePeakValleyCommon,
            },
            {
              title: "尖峰月尖谷价差",
              value: rest?.priceTopValleyTopmonth,
            },
            {
              title: "谷电价_尖峰月",
              value: rest?.valleyPriceTopmonth,
            },
            {
              title: "尖时段_尖峰月",
              value: rest?.topHoursTopmonth,
            },
            {
              title: "平电价_尖峰月",
              value: rest?.flatPriceTopmonth,
            },
            {
              title: "峰时段_尖峰月",
              value: rest?.peakHoursTopmonth,
            },
            {
              title: "峰时段_普通月",
              value: rest?.peakHoursCommon,
            },
            {
              title: "平时段_尖峰月",
              value: rest?.flatHoursTopmonth,
            },
            {
              title: "平时段_普通月",
              value: rest?.flatHoursCommon,
            },
            {
              title: "谷时段_尖峰月",
              value: rest?.valleyHoursTopmonth,
            },
            {
              title: "谷时段_普通月",
              value: rest?.valleyHoursCommon,
            },
            {
              title: "尖电价_尖峰月",
              value: rest?.topPriceTopmonth,
            },
            {
              title: "峰电价_普通月",
              value: rest?.peakPriceCommon,
            },
            {
              title: "峰电价_尖峰月",
              value: rest?.peakPriceTopmonth,
            },
            {
              title: "平电价_普通月",
              value: rest?.flatPriceCommon,
            },
            {
              title: "谷电价_普通月",
              value: rest?.valleyPriceCommon,
            },
          ];
          //配置信息
          this.configInfoList = [
            {
              title: "主要用电设备负荷总量(kWh)",
              value: rest?.totalLoadElectricalEquipment,
            },
            {
              title: "是否有光伏",
              value: rest?.ifPhotovoltaic,
            },
          ];
          this.configInfoTableData = transformers;
          //负荷信息
          this.loadInfoList = [
            {
              title: "拟投建储能容量(kWh)",
              value: rest?.businessStorageCapacity,
            },
            {
              title: "拟投建储能功率(kW)",
              value: rest?.businessStoragePower,
            },
            // {
            //   title: "防逆流限制功率(kW)",
            //   value: rest?.stationName,
            // },
            {
              title: "DOD%",
              value: rest?.dod,
            },
            {
              title: "充电倍率上限",
              value: rest?.chargeRateLimit,
            },
            {
              title: "放电倍率上限",
              value: rest?.dischargeRateLimit,
            },
            {
              title: "容量推荐最小峰段小时数",
              value: rest?.thrRecHNum,
            },
            {
              title: "循环效率%",
              value: rest?.recycleEfficiency,
            },
            {
              title: "年放电天数",
              value: rest?.annualDischargeDays,
            },
            {
              title: "普通月扣减天数",
              value: rest?.normalMonthDeductionDays,
            },
            {
              title: "尖峰月扣减天数",
              value: rest?.peakMonthDeductionDays,
            },
            {
              title: "尖峰月兜底系数",
              value: rest?.peakMonthUndercoverFactor,
            },
            {
              title: "负荷修正比例",
              value: rest?.adjPowerRate,
            },
            {
              title: "峰段首小时修正比例",
              value: rest?.adjPowerRatePeak,
            },
            {
              title: "用电情况计量单位",
              value: rest?.electricityFlag,
            },
            {
              title: "原始订单是否区分尖峰平谷电量",
              value: rest?.orderElecFlag,
            },
            // {
            //   title: "储能柜厂商",
            //   value: rest?.stationName,
            // },
            {
              title: "是否录入24小时用电情况",
              value: rest?.if24hourPq,
            },
            {
              title: "电表户号",
              value: rest?.elecMeterAccountNumber,
            },
            {
              title: "4个月每小时用电量单",
              value: rest?.fourMonthPq ? JSON.parse(rest?.fourMonthPq) : [],
              slotName: "links",
            },
            {
              title: "近三个月电费单",
              value: rest?.threeMonthElectricBill ? JSON.parse(rest?.threeMonthElectricBill) : [],
              slotName: "links",
            },
          ];
          this.energyUseTableData = sync24HrEnergyUses;
          //容量确认
          this.volumeConfirmList = [
            {
              title: "是否模型测算",
              value: rest?.ifCapacityEstimate,
            },
            {
              title: "提示",
              value: rest?.tips,
            },
            {
              title: "最小峰储能消纳量(kWh)",
              value: rest?.confirmCapacity,
            },
            {
              title: "确认储能容量(kWh)",
              value: rest?.lastConfirmCapacity,
            },
          ];
          //采购成本
          this.purchaseCostList = [
            {
              title: "是否展开采购成本填写",
              value: rest?.whetherStartCostWriting,
            },
            {
              title: "储能设备单价（元/wh）",
              value: rest?.unitStorageDevicesCost,
            },
            {
              title: "网关台数",
              value: rest?.gatewayNum,
            },
            {
              title: "工程单价（元/wh）",
              value: rest?.unitConstuctCost,
            },
            {
              title: "网关等配件单价（元/台）",
              value: rest?.unitGatewayCost,
            },
            {
              title: "拟投资含税总金额（元）",
              value: rest?.investedIncludingTax,
            },
            {
              title: "网关等配件成本（元）",
              value: rest?.gatewayCost,
            },
            {
              title: "储能设备成本（元）",
              value: rest?.storageDevicesCost,
            },
            {
              title: "工程成本（元）",
              value: rest?.constuctCost,
            },
            {
              title: "",
              value: "",
            },
          ];
          //投资确认
          this.investList = [
            {
              title: "投资测算查询",
              value: rest?.ifInvestEstimate,
            },
            {
              title: "投资测算意见",
              value: rest?.suggestInvestEstimate,
            },
            // {
            //   title: "是否展开业务负责人意见填报",
            //   value: rest?.stationName,
            // },
            // {
            //   title: "是否展开经管意见填报",
            //   value: rest?.stationName,
            // },
            // {
            //   title: "是否展开投资初审意见填报",
            //   value: rest?.stationName,
            // },
            {
              title: "是否展开投建终审意见填报",
              value: rest?.ifFillingInvestSuggestion,
            },
            {
              title: "投建终审结果",
              value: rest?.theFinalCheckResult,
            },
            {
              title: "是否高于保底金额",
              value: rest?.isHigherThanGuaranteedAmount,
            },
            {
              title: "差值",
              value: rest?.difference,
            },
            {
              title: "投资回报期（年）",
              value: rest?.paybackPeriod,
            },
            {
              title: "IRR(%)",
              value: rest?.irr,
            },
            {
              title: "每天总放电次数_尖峰月",
              value: rest?.dischargeTimesTopmonth,
            },
            {
              title: "度电价差",
              value: rest?.unitDiffPrice,
            },
            {
              title: "每天总放电次数_普通月",
              value: rest?.dischargeTimesCommon,
            },
            {
              title: "度电收益",
              value: rest?.unitDiffIncome,
            },
            {
              title: "每天总放电次数_尖峰月_冬",
              value: rest?.dischargeTimesTopmonthWinter,
            },
            {
              title: "投资方月均收入含税",
              value: rest?.monthIncome,
            },
            {
              title: "投资方首年月均收入含税",
              value: rest?.monthincomeFirstYear,
            },
            {
              title: "预计分成后投资方总收入含税",
              value: rest?.incomeAllYear,
            },
            {
              title: "首年分成后日均收入含税",
              value: rest?.dayincomeFirstYear,
            },
            {
              title: "净利润（合同年限内的业务利润，不含人工成本等）",
              value: rest?.sumAfterTaxProfit,
            },
            {
              title: "年尖时放电次数_尖峰月",
              value: rest?.topDischargeTimesTopmonth,
            },

            {
              title: "年尖时放电次数_尖峰月_冬",
              value: rest?.topDischargeTimesTopmonthWinter,
            },
            {
              title: "年峰时放电次数_尖峰月",
              value: rest?.peakDischargeTimesTopmonth,
            },
            {
              title: "年峰时放电次数_尖峰月_冬",
              value: rest?.peakDischargeTimesTopmonthWinter,
            },
            {
              title: "年平时放电次数_尖峰月",
              value: rest?.flatDischargeTimesTopmonth,
            },
            {
              title: "年平时放电次数_尖峰月_冬",
              value: rest?.flatDischargeTimesTopmonthWinter,
            },
            {
              title: "年平时充电次数_尖峰月",
              value: rest?.flatChargeTimesTopmonth,
            },
            {
              title: "年平时充电次数_尖峰月_冬",
              value: rest?.flatChargeTimesTopmonthWinter,
            },
            {
              title: "年谷时充电次数_尖峰月",
              value: rest?.valleyChargeTimesTopmonth,
            },
            {
              title: "年谷时充电次数_尖峰月_冬",
              value: rest?.valleyChargeTimesTopmonthWinter,
            },
            {
              title: "年峰时放电次数_普通月",
              value: rest?.peakDischargeTimesCommon,
            },
            {
              title: "年平时放电次数_普通月",
              value: rest?.flatDischargeTimesCommon,
            },
            {
              title: "年平时充电次数_普通月",
              value: rest?.flatChargeTimesCommon,
            },
            {
              title: "年谷时充电次数_普通月",
              value: rest?.valleyChargeTimesCommon,
            },
            {
              title: "",
              value: "",
            },
          ];
          //时间信息
          this.timeInfoList = [
            {
              title: "提交人",
              value: rest?.creator,
            },
            {
              title: "提交时间",
              value: rest?.createTime,
            },
            {
              title: "流程状态",
              value: rest?.status,
            },
            {
              title: "更新时间",
              value: rest?.updateTime,
            },
          ];
        } else {
          this.middleBusinessList = [
            {
              title: "居间商公司名称",
              value: agentInfo?.agentName,
            },
            {
              title: "居间商分成比例%",
              value: agentInfo?.agentShareRatio,
            },
            {
              title: "居间商开票税率",
              value: agentInfo?.agentTaxRate,
            },
            {
              title: "居间商居间费-单桩",
              value: agentInfo?.agentPileJujianFee,
            },
          ];
          this.placeList = [
            {
              title: "场地方公司名称",
              value: siteInfo?.changdiCompanyName,
            },
            {
              title: "场地方分成比例",
              value: siteInfo?.changdiShareRatio,
            },
            {
              title: "场地方开票税率",
              value: siteInfo?.changdiTaxRate,
            },
            {
              title: "场地进场费-单桩",
              value: siteInfo?.changdiPileJujianFee,
            },
          ];
          this.configList = [
            {
              title: "直流总枪数",
              value: pileConfigInfo?.dcTotalEquipNum,
            },
            {
              title: "直流总桩数",
              value: pileConfigInfo?.dcTotalPileNum,
            },
            {
              title: "交流总枪数",
              value: pileConfigInfo?.acTotalEquipNum,
            },
            {
              title: "交流总桩数",
              value: pileConfigInfo?.acTotalPileNum,
            },
            {
              title: "安装总枪数",
              value: pileConfigInfo?.anzhuangTotalEquipNum,
            },
            {
              title: "安装总桩数",
              value: pileConfigInfo?.anzhuangTotalPileNum,
            },
            {
              title: "部门总监",
              value: pileConfigInfo?.departmentLeader,
            },
            {
              title: "商务人员",
              value: pileConfigInfo?.commercialPersonnel,
            },
            {
              title: "城市总监",
              value: pileConfigInfo?.cityLeader,
            },
            {
              title: "区域工程",
              value: pileConfigInfo?.engineeringPersonnel,
            },
          ];
          this.configTableData = pileConfigInfo?.pileInfoList;
          //商务信息
          this.businessList = [
            {
              title: "场地属性",
              value: stationInfo?.stationType,
            },
            {
              title: "场地细分属性",
              value: stationInfo?.stationTypeDetail,
            },
          ];
          //场地属性
          this.placeAttrTableData = stationInfo?.stationPropList;
          //场地信息
          this.placeInfoTableData = stationInfo?.stationList;
          //共存的竞品信息
          this.productTableData = stationInfo?.completionList;
          //用户信息
          this.userInfoList = [
            {
              title: "站点车位数",
              value: stationInfo?.stationSpacesNumber,
            },
            {
              title: "站点车辆数",
              value: stationInfo?.carNum,
            },
            {
              title: "日均电动汽车数",
              value: stationInfo?.electricVehiclesNumber,
            },
            {
              title: "私桩数量",
              value: stationInfo?.personalPileNum,
            },
          ];
          //商方条件
          this.businessConditionList = [
            {
              title: "预计度电服务费",
              value: stationInfo?.elecServiceFee,
            },
            {
              title: "电费开票税率",
              value: stationInfo?.taxRateElectricityBillsTaken,
            },
            {
              title: "场地合同年限",
              value: stationInfo?.siteContractLife,
            },
            {
              title: "场地签约生效日期",
              value: stationInfo?.signEffectiveDate,
            },
            {
              title: "场地方收益模式",
              value: stationInfo?.providerRevenueModel,
            },
            {
              title: "场地签约失效日期",
              value: stationInfo?.signExpirationDate,
            },
            {
              title: "投建协议文件",
              value: JSON.parse(stationInfo?.constructionAggrementText),
              slotName: "links",
            },
          ];
          //项目电价
          this.powerPriceList = [
            {
              title: "基础电价（元/度）",
              value: stationInfo?.electricityPrice,
            },
            {
              title: "基础电价是否上浮",
              value: stationInfo?.priceFluctuation,
            },
            {
              title: "电价是否调整",
              value: stationInfo?.ifChangePriceFluctuation,
            },
            {
              title: "电价调整周期",
              value: stationInfo?.changePeriod,
            },
          ];
          //尖峰平谷电价
          this.powerPriceTableData = stationInfo?.elecPriceList;
          //安装环境
          this.installEnvList = [
            {
              title: "安装区域",
              value: stationInfo?.installationArea,
            },
            {
              title: "安装环境原始照片",
              value: JSON.parse(stationInfo?.anzhuangPhoto),
              slotName: "links",
            },
            {
              title: "场站剩余容量（KVA）",
              value: stationInfo?.remainingCapacityOfTheStationVerified,
            },
            {
              title: "场站通信信号",
              value: stationInfo?.communicationSignal,
            },
            {
              title: "是否换桩项目",
              value: stationInfo?.ifHuanzhuang,
            },
            {
              title: "周围危险物识别",
              value: stationInfo?.dangerousIdentify,
            },
          ];
          //施工发包模式
          this.packageList = [
            {
              title: "施工发包模式",
              value: stationInfo?.constuctFabaoType,
            },
            {
              title: "施工队名称",
              value: stationInfo?.constuctZhidingCompanyName,
            },
          ];
          //工程信息
          this.engineerList = [
            {
              title: "单桩施工成本",
              value: projectInfo?.constuctPileCost,
            },
            {
              title: "施工成本总价",
              value: projectInfo?.constuctAllCost,
            },
            {
              title: "单桩设备成本",
              value: projectInfo?.equipCostPile,
            },
            {
              title: "电源点照片（非报电项目）",
              value: projectInfo?.elecPointPhotoList?.map((x) => {
                return { url: x, name: getNameFromUrl(x) };
              }),
              slotName: "links",
            },
            {
              title: "主缆一材质",
              value: projectInfo?.mainCableMaterial1,
            },
            {
              title: "主缆二材质",
              value: projectInfo?.mainCableMaterial2,
              hidden: projectInfo?.mainCableLength2 == 0,
            },
            {
              title: "主缆一规格",
              value: projectInfo?.mainCableGuige1,
            },
            {
              title: "主缆二规格",
              value: projectInfo?.mainCableGuige2,
              hidden: projectInfo?.mainCableLength2 == 0,
            },
            {
              title: "主缆一长度（米）",
              value: projectInfo?.mainCableLength1,
            },
            {
              title: "主缆二长度（米）",
              value: projectInfo?.mainCableLength2,
              hidden: projectInfo?.mainCableLength2 == 0,
            },
            {
              title: "主缆一型号",
              value: projectInfo?.mainCableModle1,
            },
            {
              title: "主缆二型号",
              value: projectInfo?.mainCableModle2,
              hidden: projectInfo?.mainCableLength2 == 0,
            },
            {
              title: "主缆三材质",
              value: projectInfo?.mainCableMaterial3,
              hidden: projectInfo?.mainCableLength3 == 0,
            },
            {
              title: "主缆三长度（米）",
              value: projectInfo?.mainCableLength3,
              hidden: projectInfo?.mainCableLength3 == 0,
            },
            {
              title: "主缆三规格",
              value: projectInfo?.mainCableGuige3,
              hidden: projectInfo?.mainCableLength3 == 0,
            },
            {
              title: "主缆三型号",
              value: projectInfo?.mainCableModle3,
              hidden: projectInfo?.mainCableLength3 == 0,
            },
          ];
          //经营初审
          this.firstExamineList = [
            {
              title: "经营初审查询",
              value: manageAudit?.queryJingyingChushen,
            },
            {
              title: "交流等级",
              value: manageAudit?.surroundingAssetsExchangeReview,
            },
            {
              title: "服务费评审",
              value: manageAudit?.serviceFeeReview,
            },
            {
              title: "非交流等级",
              value: manageAudit?.surroundingAssetsOtherReview,
            },
            {
              title: "经营评审-周边情况描述",
              value: manageAudit?.surroundingSituationReview,
            },
            {
              title: "",
              value: "",
            },
          ];
          //投建审批和终审
          this.finalExamineList = [
            {
              title: "投建审批查询",
              value: constructFinalAudit?.queryToujianShenpi,
            },
            {
              title: "IRR（%）",
              value: constructFinalAudit?.irrPercent,
            },
            {
              title: "投建审批-描述",
              value: constructFinalAudit?.toujianSurroundingSituationReview,
            },
            {
              title: "经营投建建议",
              value: constructFinalAudit?.investmentBusinessSuggestions,
            },
            {
              title: "指定施工审批结果",
              value: constructFinalAudit?.stationName1,
            },
            {
              title: "最终审批意见",
              value: constructFinalAudit?.finalApprovalComment,
            },
            {
              title: "最终审批结果",
              value: constructFinalAudit?.finalApprovalResult,
            },
            {
              title: "拟投资含税金额（元）",
              value: constructFinalAudit?.investedIncludingTax,
            },
            {
              title: "总设备成本",
              value: constructFinalAudit?.equipTotalCost,
            },
            {
              title: "总施工成本",
              value: constructFinalAudit?.constuctTotalCost,
            },
            {
              title: "总商务成本",
              value: constructFinalAudit?.businessTotalCost,
            },
            {
              title: "指定施工审批流水号",
              value: constructFinalAudit?.stationName1,
            },
          ];
          //补充信息
          this.supplyList = [
            {
              title: "补充说明类型",
              value: supplementaryInfo?.supplyReviewType,
            },
            {
              title: "补充说明详细内容",
              value: supplementaryInfo?.supplyReviewDetail,
            },
            {
              title: "预计单枪日充电量",
              value: supplementaryInfo?.preChargePqPileDay,
            },
          ];
          //审批信息
          this.checkList = [
            {
              title: "踏勘申请发起时间",
              value: baseInfo?.surveySubmitTime,
            },
            {
              title: "申请人",
              value: res.data?.stationName,
            },
            {
              title: "流程结束时间",
              value: baseInfo?.surveyPassTime,
            },
          ];
          //评论信息
          const obj = [
            // { remarkTime: "2023-10-02", remark: "内容1", remarkUser: "章三" },
            // { remarkTime: "2023-10-02", remark: "内容2", remarkUser: "里斯" },
          ];
          this.remarkList = obj.flatMap((x) => {
            return [
              {
                title: "评论时间",
                value: x.remarkTime,
              },
              {
                title: "评论内容",
                value: x.remark,
              },
              {
                title: "评论人",
                value: x.remarkUser,
              },
            ];
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
/deep/ .el-drawer__body {
  scroll-padding-top: 40px;
}
.anchor {
  position: fixed;
  top: 40px;
  background: #fff;
  width: 100%;
  z-index: 10;
  /deep/ .el-tabs__header {
    margin: 0;
  }
}
</style>
