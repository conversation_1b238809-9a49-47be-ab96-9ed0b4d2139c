//详情-采购单详情抽屉
<template>
  <el-drawer title="采购下单详情" :visible.sync="visible" size="60%">
    <div class="drawer-content">
      <el-card>
        <CommonTitle class="mb10" title="基本信息" />
        <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="施工队信息" />
        <BaseDescriptions :list="teamList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="固定资产采购明细" />
        <!-- <BaseDescriptions :list="detailList" :column="2"> </BaseDescriptions> -->
        <vxe-grid
          :columns="tableColumns"
          :data="tableData"
          resizable
          align="center"
        ></vxe-grid>
      </el-card>
    </div>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import CommonTitle from "@/components/commonTitle";
export default {
  components: { CommonTitle, BaseDescriptions },
  data() {
    return {
      visible: false,
      baseList: [],
      teamList: [],
      detailList: [],
      tableColumns: [
        { title: "固定资产采购计划单号", field: "assetApplyNo", width: 200 },
        { title: "项目下固定资产采购", field: "isProjAssetName", width: 80 },
        { title: "项目编码", field: "projNo", width: 150 },
        { title: "固定资产类别", field: "categoryName", width: 100 },
        { title: "资产明细", field: "assetDetailsName", width: 150 },
        { title: "资产名称", field: "assetName", width: 150 },
        { title: "品牌", field: "brand", width: 150 },
        { title: "型号", field: "partNumber", width: 150 },
        { title: "配置", field: "configuration", width: 150 },
        { title: "采购数量", field: "quantityCg", width: 100 },
        { title: "采购合计金额", field: "applyMoney", width: 100 },
        { title: "采购单价", field: "unitpriceCg", width: 100 },
        { title: "税率", field: "taxRate", width: 100 },
        { title: "发票类型", field: "invoiceTypeName", width: 120 },
        { title: "收货人", field: "consigneeName", width: 150 },
        { title: "收货地址", field: "receiverAddress", width: 150 },
        { title: "联系方式", field: "contactWay", width: 150 },
      ],
      tableData: [],
    };
  },
  methods: {
    open(row) {
      this.visible = true;
      api.getPurchaseAsset(row).then((res) => {
        this.tableData = res.data;
        // [
        //   {
        //     title: "固定资产采购计划单号",
        //     value: res.data?.assetApplyNo,
        //   },
        //   {
        //     title: "项目下固定资产采购",
        //     value: res.data?.isProjAssetName,
        //   },
        //   {
        //     title: "项目编码",
        //     value: res.data?.projNo,
        //   },
        //   {
        //     title: "固定资产类别",
        //     value: res.data?.categoryName,
        //   },
        //   {
        //     title: "资产明细",
        //     value: res.data?.assetDetailsName,
        //   },
        //   {
        //     title: "资产名称",
        //     value: res.data?.assetName,
        //   },
        //   {
        //     title: "品牌",
        //     value: res.data?.brand,
        //   },
        //   {
        //     title: "型号",
        //     value: res.data?.partNumber,
        //   },
        //   {
        //     title: "配置",
        //     value: res.data?.configuration,
        //   },
        //   {
        //     title: "采购数量",
        //     value: res.data?.quantityCg,
        //   },
        //   {
        //     title: "采购合计金额",
        //     value: res.data?.applyMoney,
        //   },
        //   {
        //     title: "采购单价",
        //     value: res.data?.unitpriceCg,
        //   },
        //   {
        //     title: "税率",
        //     value: res.data?.taxRate,
        //   },
        //   {
        //     title: "发票类型",
        //     value: res.data?.invoiceTypeName,
        //   },
        //   {
        //     title: "收货人",
        //     value: res.data?.consigneeName,
        //   },
        //   {
        //     title: "收货地址",
        //     value: res.data?.receiverAddress,
        //   },
        //   {
        //     title: "联系方式",
        //     value: res.data?.contactWay,
        //   },
        //   {
        //     title: "",
        //     value: "",
        //   },
        // ];
      });
      api.getPurchaseOrderDetail(row).then((res) => {
        this.baseList = [
          {
            title: "申请单号",
            value: res.data?.applyNo,
          },
          {
            title: "固定资产所属公司",
            value: res.data?.companyName,
          },
          {
            title: "固定资产合同编码",
            value: res.data?.assetConNo,
          },
          {
            title: "固定资产合同名称",
            value: res.data?.assetConName,
          },
          {
            title: "项目编码",
            value: res.data?.projNo,
          },
          {
            title: "是否消耗预备货",
            value: res.data?.stationName1,
          },
          {
            title: "申请状态",
            value: res.data?.applyStatusName,
          },
          {
            title: "业务类型",
            value: res.data?.stationName1,
          },
          {
            title: "合同金额",
            value: res.data?.applyMoneySum,
          },
          {
            title: "交货日期",
            value: res.data?.stationName1,
          },
        ];
        this.teamList = [
          {
            title: "供应商名称",
            value: res.data?.constructName,
          },
          {
            title: "供应商编码",
            value: res.data?.constructCode,
          },
          {
            title: "供应商联系人",
            value: res.data?.contactName,
          },
          {
            title: "供应商联系人电话",
            value: res.data?.contactPhone,
          },
          {
            title: "邮寄联系人",
            value: res.data?.mailContact,
          },
          {
            title: "邮寄联系人电话",
            value: res.data?.mailPhone,
          },
          {
            title: "外包合同邮寄地址",
            value: res.data?.mailAddr,
          },
          {
            title: "备注",
            value: res.data?.stationName1,
          },
        ];
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
</style>
