//详情-采购单框架协议详情抽屉
<template>
  <el-drawer title="采购框架协议详情" :visible.sync="visible" size="60%">
    <div class="drawer-content">
      <el-card>
        <CommonTitle class="mb10" title="基本信息" />
        <BaseDescriptions :list="baseList" :column="2"> </BaseDescriptions>
      </el-card>
      <el-card>
        <CommonTitle class="mb10" title="采购框架协议明细" />
        <vxe-grid
          :columns="tableColumns"
          :data="tableData"
          resizable
          align="center"
        ></vxe-grid>
      </el-card>
    </div>
  </el-drawer>
</template>

<script>
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import CommonTitle from "@/components/commonTitle";
export default {
  components: { CommonTitle, BaseDescriptions },
  data() {
    return {
      visible: false,
      baseList: [],
      tableColumns: [
        { title: "服务项目/硬件名称", field: "installType" },
        { title: "服务内容/硬件类型", field: "installType" },
        { title: "含税单价", field: "installType" },
        { title: "单位", field: "installType" },
        { title: "税率", field: "installType" },
        { title: "备注", field: "installType" },
      ],
      tableData: [],
    };
  },
  methods: {
    open(row) {
      this.visible = true;
      api.getPurchaseAgreementDetail({ applyNo: row.applyNo }).then((res) => {
        this.baseList = [
          {
            title: "申请单号",
            value: res.data?.applyNo,
          },
          {
            title: "协议名称",
            value: res.data?.procotolName,
          },
          {
            title: "协议编码",
            value: res.data?.procotolNo,
          },
          {
            title: "协议类型",
            value: res.data?.procotolTypeName,
          },
          {
            title: "申请状态",
            value: res.data?.applyStatus,
          },
          {
            title: "协议所属公司",
            value: res.data?.companyName,
          },
          {
            title: "协议签订时间",
            value: res.data?.signTime,
          },
          {
            title: "协议开始时间",
            value: res.data?.startTime,
          },
          {
            title: "协议截止时间",
            value: res.data?.endTime,
          },
          {
            title: "发票类型",
            value: res.data?.invoiceTypeName,
          },
          {
            title: "供应商名称",
            value: res.data?.supplierName,
          },
        ];
      });
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-content {
  padding: 0 20px;
}
</style>
