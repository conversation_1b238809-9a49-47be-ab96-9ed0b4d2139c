// 整改通知单
<template>
  <div class="app-container">
    <div style="display: flex;justify-content: space-between;">
      <h3>整改通知单</h3>
      <div>
        <el-button type="text" @click="handleRecordDrawer">整改记录</el-button>
        <el-button @click="handleDownload" :loading="downloadLoading"
          >下载</el-button
        >
      </div>
    </div>
    <el-card>
      <CommonTitle class="mb10" title="项目信息" />
      <DynamicForm
        :config="projectForm"
        :params="projectParams"
        ref="projectForm"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="200px"
        :preview="true"
      ></DynamicForm>
    </el-card>
    <el-card>
      <CommonTitle class="mb20" title="工程整改通知单" />
      <DynamicForm
        :config="noticeForm"
        :params="noticeParams"
        ref="noticeForm"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      >
        <template #fileUpload>
          <file-upload
            ref="upload"
            v-model="noticeParams.fileUrlList"
            :limit="10"
            accept=".jpg, .jpeg, .png"
            :fileMaxSize="50"
            textTip="支持批量上传，上传格式为jpg、jpeg、png文件，单个文件50M以内"
          />
        </template>
      </DynamicForm>
    </el-card>
    <div class="dialog-footer">
      <el-button size="medium" :loading="loading" @click="handleCancel"
        >取 消</el-button
      >
      <el-button
        @click="handleSubmit"
        type="primary"
        size="medium"
        :loading="loading"
        >提 交</el-button
      >
    </div>
    <el-drawer title="工程整改记录" :visible.sync="drawerVisible" size="60%">
      <Timeline
        :list="recordList"
        operateTypeTitle="title"
        operatorNameTitle="createByName"
        operateDetailTitle="rectificationDesc"
      ></Timeline>
    </el-drawer>
  </div>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import FileUpload from "@/components/Upload/fileUpload4.vue";
import { initParams } from "@/utils/buse.js";
import Timeline from "@/components/Timeline/index.vue";
import { fileDownLoad } from "@/utils/downLoad.js";
import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  name: "rectificationNotice",
  components: { CommonTitle, FileUpload, Timeline },
  data() {
    return {
      loading: false,
      projectParams: {},
      noticeParams: {},
      projectBatchId: "",
      allProjectStatusOptions: [],
      drawerVisible: false,
      recordList: [],
      downloadLoading: false,
    };
  },
  computed: {
    noticeForm() {
      return [
        {
          field: "rectificationDesc",
          title: "整改内容：",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 10,
            maxlength: 2000,
            showWordLimit: true,
            placeholder: "请输入整改内容",
          },
          rules: [
            { required: true, message: "请填写整改内容", trigger: "change" },
          ],
        },
        {
          field: "fileUrlList",
          element: "slot",
          slotName: "fileUpload",
          title: "附件图片：",
        },
        {
          field: "buildCompany",
          title: "建设单位：",
          attrs: {
            placeholder: "请输入建设单位",
          },
        },
        {
          field: "time",
          element: "el-date-picker",
          title: "日期：",
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择日期",
          },
        },
      ];
    },
    projectForm() {
      return [
        {
          field: "stationCode",
          title: "场站编码：",
        },
        {
          field: "stationName",
          title: "场站名称：",
        },
        {
          field: "projectStatus",
          title: "项目状态：",
          previewFormatter: (cellValue) => {
            return this.allProjectStatusOptions?.find(
              (x) => x.dictValue == cellValue
            )?.dictLabel;
          },
        },
      ];
    },
  },
  created() {
    this.projectBatchId = this.$route.query.projectBatchId;
    this.projectParams = initParams(this.projectForm);
    this.noticeParams = initParams(this.noticeForm);
    this.getDicts("project_status").then((response) => {
      this.allProjectStatusOptions = response.data;
    });
    this.getDetail();
  },
  methods: {
    handleDownload() {
      this.downloadLoading = true;
      api
        .exportRectification({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.downloadLoading = false;
          if (res) {
            fileDownLoad(res);
          }
        })
        .catch(() => {
          this.downloadLoading = false;
        });
    },
    handleRecordDrawer() {
      this.drawerVisible = true;
      api
        .queryRectificationRecord({ projectBatchId: this.projectBatchId })
        .then((res) => {
          if (res?.code === "10000") {
            this.recordList = res.data;
          }
        });
    },
    getDetail() {
      api.getNoticeInfo({ projectBatchId: this.projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          Object.keys(this.projectParams).forEach((key) => {
            this.projectParams[key] = res.data[key];
          });
          this.noticeParams = { ...this.noticeParams, ...res.data };
        }
      });
    },
    handleCancel() {
      this.$router.go(-1);
    },
    handleSubmit() {
      this.$refs.noticeForm.validate((valid) => {
        if (!valid) return;
        this.$confirm("是否确认提交？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            const params = {
              ...this.noticeParams,
              projectBatchId: this.projectBatchId,
            };
            api.saveRectification(params).then((res) => {
              if (res?.code === "10000") {
                this.$message.success("提交成功");
                this.$store.dispatch("tagsView/delView", this.$route);
                this.handleCancel();
              }
            });
          })
          .catch(() => {});
      });
    },
  },
};
</script>

<style></style>
