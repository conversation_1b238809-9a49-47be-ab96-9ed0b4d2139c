//竣工验收
<template>
  <div class="app-container">
    <div class="page-title">
      <h3>竣工报告验收</h3>
      <div>
        <el-button type="text" icon="el-icon-arrow-left" @click="goBack"
          >返回</el-button
        >
        <!-- <el-button type="primary" @click="saveForm" :loading="loading"
          >保存</el-button
        > -->
      </div>
    </div>
    <el-card>
      <CommonTitle class="mb10" title="项目信息" />
      <div class="info-box">
        <div class="info-box-left">
          <div>项目编码：{{ baseInfo.projectCode }}</div>
          <div>项目名称：{{ baseInfo.projectName }}</div>
          <div>项目状态：{{ baseInfo.projectStatusName }}</div>
        </div>
        <div>
          <el-button type="info" @click="handleSubmit(false)">不通过</el-button>
          <el-button type="primary" @click="handleSubmit(true)">通过</el-button>
        </div>
      </div>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="竣工报告查阅" />
      <FilePreview :fileList="uploadList"></FilePreview>
    </el-card>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
    >
    </BaseFormModal>
  </div>
</template>

<script>
import api from "@/api/engineerProject/investBatchList/index.js";
import { initParams } from "@/utils/buse.js";
import FilePreview from "@/components/Upload/filePreview.vue";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import CommonTitle from "@/components/commonTitle";
export default {
  components: { CommonTitle, FilePreview, BaseFormModal },
  data() {
    return {
      baseInfo: {},
      uploadList: [],
    };
  },
  computed: {
    modalConfig() {
      return {
        modalTitle: "提示信息",
        formConfig: [
          {
            field: "completionAuditReason",
            element: "el-input",
            title: "审核不通过原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
            rules: [{ required: true, message: "请输入审核不通过原因" }],
          },
        ],
      };
    },
  },
  created() {
    this.baseInfo = this.$route.query;
    this.getUploadReport();
  },
  methods: {
    //获取已盖章版报告
    getUploadReport() {
      api
        .uploadReport({
          projectBatchId: this.baseInfo.projectBatchId,
          businessType: "竣工报告",
        })
        .then((res) => {
          this.uploadList = res.data?.cmDocList;
        });
    },
    goBack() {
      this.$router.go(-1);
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    modalConfirmHandler(row) {
      api
        .completeAcceptance({
          ...row,
          projectBatchId: this.baseInfo.projectBatchId,
          completionReportUploadTime: this.baseInfo.completionReportUploadTime,
          // completionReportPassTime: this.baseInfo.completionReportPassTime,
          projectStatus: "12",
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.$message.success(`提交成功`);
            this.goBack();
          }
        });
    },
    handleSubmit(flag) {
      if (flag) {
        // const text = flag ? "通过" : "不通过";
        this.$confirm(`确定验收通过吗？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          api
            .completeAcceptance({
              projectBatchId: this.baseInfo.projectBatchId,
              completionReportUploadTime: this.baseInfo
                .completionReportUploadTime,
              // completionReportPassTime: this.baseInfo.completionReportPassTime,
              projectStatus: "13",
            })
            .then((res) => {
              if (res?.code === "10000") {
                this.$message.success(`提交成功`);
                this.goBack();
              }
            });
        });
      } else {
        this.$refs.formModal.open({
          ...initParams(this.modalConfig.formConfig),
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 20px;
}
.info-box {
  display: flex;
  justify-content: space-between;
  &-left {
    margin-left: 50px;
    > div {
      padding: 10px;
    }
  }
  /deep/ .el-button--small {
    padding: 18px 30px;
    font-size: 16px;
  }
}
</style>
