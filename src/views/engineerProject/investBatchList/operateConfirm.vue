<!-- 项目投运确认单 -->
<template>
  <div class="app-container">
    <div style="display: flex;justify-content: space-between;">
      <h3>项目投运确认单</h3>
      <div>
        <el-button type="primary" @click="handleSubmit" :loading="loading"
          >保存</el-button
        >
        <el-button @click="handleDownload" :loading="downloadLoading"
          >下载</el-button
        >
        <el-button type="text" icon="el-icon-arrow-left" @click="handleCancel"
          >返回</el-button
        >
      </div>
    </div>
    <el-card>
      <CommonTitle class="mb10" title="项目信息" />
      <DynamicForm
        :config="projectForm"
        :params="projectParams"
        ref="projectForm"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="150px"
      >
        <template #deviceList>
          <EditTable
            ref="editTable"
            v-model="projectParams.deviceList"
            :customColumns="deviceColumns"
            :customRules="deviceRules"
          ></EditTable>
        </template>
        <template #fileUpload="{item,params}">
          <file-upload
            ref="upload"
            v-model="params[item.field]"
            :limit="10"
            accept=".jpg, .jpeg, .png"
            :fileMaxSize="50"
            textTip="支持批量上传，上传格式为jpg、jpeg、png文件，单个文件50M以内"
          />
        </template>
      </DynamicForm>
    </el-card>
  </div>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import EditTable from "./components/EditTable.vue";
import FileUpload from "@/components/Upload/fileUpload4.vue";
import { initParams } from "@/utils/buse.js";
import { fileDownLoad } from "@/utils/downLoad.js";
import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  name: "operateConfirm",
  components: { CommonTitle, FileUpload, EditTable },
  data() {
    return {
      projectParams: {},
      loading: false,
      downloadLoading: false,
      projectBatchId: "",
      deviceColumns: [
        { type: "checkbox", width: 60 },
        { type: "seq", title: "序号", width: 60 },
        { field: "materialName", title: "设备名称" },
        { field: "materialModel", title: "规格型号" },
        {
          field: "unit",
          title: "单位",
          formatter: ({ cellValue }) => {
            return this.unitOptions?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
        },
        {
          field: "num",
          title: "数量",
          editRender: { placeholder: "请输入", autofocus: ".el-input__inner" },
          slots: { edit: "edit_number" },
        },
        {
          field: "materialFactory",
          title: "生产厂家",
        },
        {
          field: "remark",
          title: "备注",
          editRender: { placeholder: "请输入", autofocus: ".el-input__inner" },
          slots: { edit: "edit_textarea" },
        },
        { field: "operation", title: "操作", slots: { default: "operation" } },
      ],
      deviceRules: { num: [{ required: true, message: "请输入" }] },
      unitOptions: [],
    };
  },
  computed: {
    projectForm() {
      return [
        {
          field: "projectName",
          title: "项目名称",
          preview: true,
        },
        {
          field: "customer",
          title: "客户名称",
          attrs: {
            maxlength: 200,
          },
          rules: [{ required: true, message: "请输入", trigger: "change" }],
        },
        {
          field: "deviceName",
          title: "设备名称",
          attrs: {
            maxlength: 200,
          },
          rules: [{ required: true, message: "请输入", trigger: "change" }],
        },
        {
          field: "company",
          title: "使用单位",
          attrs: {
            maxlength: 200,
          },
          rules: [{ required: true, message: "请输入", trigger: "change" }],
        },
        {
          field: "installAddr",
          title: "安装地点",
          attrs: {
            maxlength: 200,
          },
          rules: [{ required: true, message: "请输入", trigger: "change" }],
        },
        {
          field: "operateTime",
          element: "el-date-picker",
          title: "投运日期",
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择投运日期",
          },
          rules: [{ required: true, message: "请选择", trigger: "change" }],
        },
        {
          field: "deviceList",
          element: "slot",
          slotName: "deviceList",
          title: "设备清单",
          defaultValue: [],
        },
        {
          field: "checkResult",
          title: "安装项目调试人员检验结果",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "500个字符以内",
          },
        },
        {
          field: "checkPerson",
          title: "调试人员",
        },
        {
          field: "checkTime",
          element: "el-date-picker",
          title: "调试日期",
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择调试日期",
          },
        },
        {
          field: "acceptanceResult",
          title: "项目负责人验收意见",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "500个字符以内",
          },
        },
        {
          field: "projectHeader",
          title: "项目负责人",
        },
        {
          field: "acceptanceTime",
          element: "el-date-picker",
          title: "验收日期",
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择验收日期",
          },
        },
        {
          field: "localInstallPics",
          element: "slot",
          slotName: "fileUpload",
          title: "现场安装照片：",
        },
        {
          field: "miniProgramPics",
          element: "slot",
          slotName: "fileUpload",
          title: "投运小程序截图：",
        },
        {
          field: "installAddrPics",
          element: "slot",
          slotName: "fileUpload",
          title: "安装地址截图：",
        },
      ];
    },
  },
  async created() {
    await Promise.all([
      this.getDicts("material_unit").then((response) => {
        this.unitOptions = response?.data;
      }),
    ]);
    this.projectBatchId = this.$route.query.projectBatchId;
    this.projectParams = {
      ...initParams(this.projectForm),
      projectName: this.$route.query.projectName,
    };
    this.getDetail();
  },
  methods: {
    handleDownload() {
      this.downloadLoading = true;
      api
        .exportOperateConfirm({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.downloadLoading = false;
          if (res) {
            console.log(res, "resssssssssss");
            fileDownLoad(res);
          }
        })
        .catch(() => {
          this.downloadLoading = false;
        });
    },
    getDetail() {
      api
        .getOperateConfirmInfo({ projectBatchId: this.projectBatchId })
        .then((res) => {
          if (res?.code === "10000") {
            this.projectParams = {
              ...this.projectParams,
              ...res.data,
            };
          }
        });
    },
    async handleSubmit() {
      const errMap = await this.$refs.editTable.validTable();
      if (errMap) {
        this.$message.warning("请填写表格必填项！");
        return;
      }
      this.$refs.projectForm.validate((valid) => {
        if (!valid) return;
        this.$confirm("是否确认提交？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            const params = {
              ...this.projectParams,
              projectBatchId: this.projectBatchId,
            };
            api.saveOperateConfirm(params).then((res) => {
              if (res?.code === "10000") {
                this.$message.success("提交成功");
                this.$store.dispatch("tagsView/delView", this.$route);
                this.handleCancel();
              }
            });
          })
          .catch(() => {});
      });
    },
    handleCancel() {
      this.$router.go(-1);
    },
  },
};
</script>

<style></style>
