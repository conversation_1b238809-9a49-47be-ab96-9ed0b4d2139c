<template>
  <div>
    <Timeline
      :list="recordList"
      operateTypeTitle="operateTypeName"
      operateDetailTitle="remark"
      createTimeTitle="operateTime"
    ></Timeline>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";

import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  components: {
    Timeline,
  },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      recordList: [],
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      api.recordInfo({ projectBatchId: this.projectBatchId }).then((res) => {
        this.recordList = res.data;
      });
    },
  },
};
</script>

<style></style>
