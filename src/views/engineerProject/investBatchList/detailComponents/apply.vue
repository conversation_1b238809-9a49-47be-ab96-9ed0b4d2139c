//详情-开工申请
<template>
  <div>
    <el-card>
      <div class="mb10">
        <el-button
          type="primary"
          @click.stop="item.clickFn(baseInfo)"
          v-for="(item, index) in operationBtnList"
          :key="index"
          v-show="item.show(baseInfo)"
          v-has-permi="[item.permission]"
        >
          {{ item.title }}
        </el-button>
      </div>
      <!-- <CommonTitle class="mb10" title="工程信息汇总" /> -->
      <BaseDescriptions :list="infoList" :column="2">
        <template #links="{itemVal,row}">
          <div>
            <div v-for="(item, index) in itemVal" :key="index">
              <el-button
                type="text"
                @click="handleDialog(row, item)"
                style="margin-left:0"
                >{{ item }}
              </el-button>
            </div>
          </div>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="施工队信息和进度计划" />
      <BaseDescriptions :list="teamList" :column="2">
        <template #view="{itemVal}">
          <el-button type="text" @click="handleFile(itemVal)">查看</el-button>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="审核信息" />
      <BaseDescriptions :list="checkList" :column="2"> </BaseDescriptions>
      <div v-if="isEnergyStorage">
        <h4>储能项目需求清单</h4>
        <vxe-grid
          resizable
          :columns="energyColumns"
          :data="checkTableData"
          align="center"
        ></vxe-grid>
      </div>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="确认信息" />
      <BaseDescriptions :list="confirmList" :column="2"> </BaseDescriptions>
      <div v-if="isEnergyStorage">
        <h4>储能项目需求清单</h4>
        <vxe-grid
          resizable
          :columns="energyColumns"
          :data="confirmTableData"
          align="center"
        ></vxe-grid>
      </div>
    </el-card>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      modalWidth="70%"
      labelWidth="160px"
    >
      <template #formHeader>
        <div class="checkFlex" v-if="operationType === 'check'">
          <div>计划开工时间：{{ scheduledStartTime }}</div>
          <div>计划竣工时间：{{ scheduledEndTime }}</div>
          <div>设备需求到场时间：{{ equipmentArrivalTime }}</div>
        </div>
      </template>
      <template #modalFooter="{ row }">
        <div v-if="operationType === 'check'">
          <el-button @click="submitCheck(row, false)" :loading="checkLoading"
            >审核不通过</el-button
          >
          <el-button
            @click="submitCheck(row, true)"
            type="primary"
            :loading="checkLoading"
            >审核通过</el-button
          >
        </div>
      </template>
      <template #deviceDemandList="{ item, params }">
        <EditTable ref="editTable" v-model="params[item.field]"></EditTable>
      </template>
    </BaseFormModal>
    <PurchaseOrder ref="purchaseOrder"></PurchaseOrder>
    <ContractFileDrawer ref="contractFileDrawer"></ContractFileDrawer>
  </div>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import api from "@/api/engineerProject/investBatchList/index.js";
import PurchaseOrder from "../components/purchaseOrder.vue";
import { initParams } from "@/utils/buse.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import EditTable from "../components/EditTable.vue";
import ContractFileDrawer from "../components/contractFileDrawer.vue";
export default {
  components: {
    CommonTitle,
    BaseDescriptions,
    BaseFormModal,
    PurchaseOrder,
    ContractFileDrawer,
    EditTable,
  },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
    projectCode: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      confirmTableData: [],
      checkTableData: [],
      energyColumns: [
        { type: "seq", width: 60, title: "序号" },
        { title: "设备名称", field: "materialName" },
        { title: "规格型号", field: "materialModel" },
        {
          title: "单位",
          field: "unit",
          formatter: ({ cellValue }) => {
            return this.unitOptions?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
        },
        { title: "数量", field: "num" },
        { title: "总容量（kWh）", field: "totalCapacity" },
        { title: "并网柜数量（台）", field: "meshCabinetsNum" },
      ],
      checkLoading: false,
      scheduledEndTime: "",
      scheduledStartTime: "",
      equipmentArrivalTime: "",
      baseInfo: {},
      operationType: "check",
      infoList: [],
      teamList: [],
      checkList: [],
      confirmList: [],
      operationBtnList: [
        {
          title: "开工申请",
          permission: "engineerProject:investBatchList:apply",
          show: (row) => {
            //待申请
            const condition1 = row.workApplyStatus == "1";
            return condition1;
          },
          clickFn: (row) => {
            this.handleApply(row);
          },
        },
        {
          title: "编辑",
          permission: "engineerProject:investBatchList:edit",
          show: (row) => {
            //审核不通过
            const condition1 = ["5"].includes(row.workApplyStatus);
            return condition1;
          },
          clickFn: (row) => {
            this.handleApply(row, "edit");
          },
        },
        {
          title: "审核",
          permission: "engineerProject:investBatchList:check",
          show: (row) => {
            //待审核
            const condition1 = row.workApplyStatus == "2";
            return condition1;
          },
          clickFn: (row) => {
            this.handleCheck(row);
          },
        },
        {
          title: "确认",
          permission: "engineerProject:investBatchList:confirm",
          show: (row) => {
            //审核通过
            const condition1 = row.workApplyStatus == "3";
            return condition1;
          },
          clickFn: (row) => {
            this.handleConfirm(row);
          },
        },
      ],
      workTypeOptions: [],
      deviceSourceTypeList: [],
      unitOptions: [],
      workApplyStatusOptions: [],
      projectAttributeOptions: [],
    };
  },
  created() {
    Promise.all([
      this.getDicts("project_attribute").then((response) => {
        this.projectAttributeOptions = response.data;
      }),
      this.getDicts("work_apply_status").then((response) => {
        this.workApplyStatusOptions = response.data;
      }),
      this.getDicts("device_source_type").then((response) => {
        this.deviceSourceTypeList = response.data;
      }),
      this.getDicts("material_unit").then((response) => {
        this.unitOptions = response?.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getDetail();
        });
      }, 500);
    });
  },
  methods: {
    // 根据项目批次id  查询 开工申请时填写的 计划开工时间  计划竣工时间  设备需求当到场时间
    getWorkTime(projectBatchId) {
      api.workTime({ projectBatchId: projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          this.scheduledStartTime = res.data.scheduledStartTime;
          this.scheduledEndTime = res.data.scheduledEndTime;
          this.equipmentArrivalTime = res.data.equipmentArrivalTime;
          console.log(
            "计划时间",
            this.scheduledStartTime,
            this.scheduledEndTime,
            this.equipmentArrivalTime
          );
        }
      });
    },
    //查看附件
    handleFile(val) {
      this.$refs.contractFileDrawer.open(val);
    },
    handleDialog(row, val) {
      this.$refs[row.refName]?.open({ ...row.params, applyNo: val });
    },
    modalConfirmHandler(row) {
      if (this.operationType === "confirm") {
        api.confirm(row).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("提交成功");
            this.getDetail();
          }
        });
      }
      console.log(row, this.operationType);
    },
    //确认
    handleConfirm(row) {
      this.operationType = "confirm";
      api.pileCount({ projectBatchId: this.projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          this.$refs.formModal.open({
            ...initParams(this.modalConfig.formConfig),
            projectBatchId: this.projectBatchId,
            deviceTotal: res.data,
          });
        }
      });
    },
    //审核
    handleCheck(row) {
      this.operationType = "check";
      //储能传3
      const type = this.isEnergyStorage
        ? "3"
        : row.projectType === "新建"
        ? "1"
        : "2";
      this.getWorkType(type);
      this.getWorkTime(row.projectBatchId);
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        projectBatchId: row.projectBatchId,
        type: type,
      });
    },
    //根据工序项类型查询工序模版名称
    getWorkType(type) {
      api.workType({ type: type }).then((res) => {
        if (res?.code === "10000") {
          this.workTypeOptions = res.data;
        }
      });
    },
    async submitCheck(row, flag) {
      //储能项目 审核不通过时不校验表单和表格
      if (flag && this.isEnergyStorage) {
        const errMap = await this.$refs.editTable.validTable();
        if (errMap) {
          this.$message.warning("请填写表格必填项！");
          return;
        }
      }
      this.$refs.formModal?.$refs.baseForm.validate((valid) => {
        //什么情况需要校验表单？非储能项目+储能项目审核通过时
        //不需要校验？储能项目且审核不通过
        if (!valid && (!this.isEnergyStorage || flag)) return;
        if (!flag && !row.auditReason) {
          this.$message.warning("请填写审核不通过原因！");
          return;
        }
        const params = {
          ...row,
          auditResult: flag ? "1" : "2",
          templateList: row.templateList.map((x) => {
            return this.workTypeOptions?.find((y) => y.workTemplateId === x);
          }),
        };
        this.checkLoading = true;
        api
          .check(params)
          .then((res) => {
            this.checkLoading = false;
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              this.$refs.formModal.closeVisible();
              this.getDetail();
            }
          })
          .catch(() => {
            this.checkLoading = false;
          });
      });
    },
    //开工申请
    handleApply(row, type = "add") {
      this.$router.push({
        path: "/engineerProject/investBatchList/apply",
        query: {
          type: type,
          projectBatchId: this.projectBatchId,
          projectCode: this.projectCode,
        },
      });
    },
    getDetail() {
      api
        .materialList({ projectBatchId: this.projectBatchId, type: "1" })
        .then((res) => {
          this.checkTableData = res.data;
        });
      api
        .materialList({ projectBatchId: this.projectBatchId, type: "2" })
        .then((res) => {
          this.confirmTableData = res.data;
        });
      api
        .baseInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.baseInfo = res.data;
        });
      api
        .applyInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.infoList = [
            {
              title: "投建协议盖章确认时间",
              value: res.data?.consAgreementStampTime,
            },
            {
              title: "投建协议盖章确认人",
              value: res.data?.consAgreementConfirmUser,
            },
            {
              title: "投建协议BD盖章确认时间",
              value: res.data?.agreementStampWriteTime,
            },
            {
              title: this.isEnergyStorage
                ? "施工供应商发单通知-储能柜总数"
                : "施工供应商发单通知-设备总数",
              value: res.data?.supplierOrderPileCount,
            },
            {
              title: "施工供应商发单通知时间",
              value: res.data?.supplierOrderNotifyTime,
            },
            {
              title: "施工供应商发单通知-利旧数量",
              value: res.data?.supplierOrderPileOldCount,
            },
            {
              title: "施工供应商发单通知对象",
              value: res.data?.supOrderNoticeTargetUser,
            },
            {
              title: "施工供应商发单通知-备注",
              value: res.data?.supOrderNoticeTargetUser,
            },
            {
              title: "施工供应商发单通知人",
              value: res.data?.supOrderNoticeSourceUser,
            },

            {
              title: "施工供应商发单通知方式",
              value: res.data?.supOrderNoticeMethod,
            },
            {
              title: "供应商采购下单时间",
              value: res.data?.supplierOrderPurchaseTime,
            },
            {
              title: "供应商采购下单人",
              value: res.data?.supplierPurchaseOrderHolder,
            },
            {
              title: "供应商采购下单申请单号",
              value: res.data?.supplierPurchaseApplyNo?.split(",") || [],
              slotName: "links",
              params: { assetConType: "01" },
              refName: "purchaseOrder",
            },
            {
              title: "供应商采购下单申请状态",
              value: res.data?.supplierPurchaseApplyStatus,
            },
            {
              title: "设备采购下单时间",
              value: res.data?.devicePurchaseTime,
            },
            {
              title: "施工队名称",
              value: res.data?.consTeamName,
            },
            {
              title: "设备采购下单申请单号",
              value: res.data?.devicePurchaseApplyNo?.split(",") || [],
              slotName: "links",
              refName: "purchaseOrder",
              params: { assetConType: "02" },
            },
            {
              title: "施工队负责人",
              value: res.data?.consTeamLeaderName,
            },
            {
              title: "设备采购下单申请状态",
              value: res.data?.devicePurchaseApplyStatus,
            },
            {
              title: "设备采购下单人",
              value: res.data?.devicePurchaseOrderHolder,
            },
          ];
        });
      api.applyInfoTeam({ projectBatchId: this.projectBatchId }).then((res) => {
        this.teamList = [
          {
            title: "施工队项目经理",
            value: res.data?.constructionTeamManagerName,
          },
          {
            title: "施工队项目经理联系方式",
            value: res.data?.constructionTeamManagerTel,
          },

          {
            title: "施工合同（附件）",
            value: res.data?.constructionFile
              ? JSON.parse(res.data?.constructionFile)
              : [],
            slotName: "view",
          },
          // {
          //   title: "合同质保期",
          //   value: res.data?.contractWarrantyPeriod,
          // },
          {
            title: "计划开工时间",
            value: res.data?.scheduledStartTime,
          },
          {
            title: "计划竣工时间",
            value: res.data?.scheduledEndTime,
          },
          {
            title: "设备需求到场时间",
            value: res.data?.equipmentArrivalTime,
          },
          {
            title: "设备收货地址",
            value: res.data?.equipmentDeliveryAddress,
          },
          {
            title: "收货人姓名",
            value: res.data?.consigneeName,
          },
          {
            title: "收货人电话",
            value: res.data?.consigneeTel,
          },
          {
            title: "备注",
            value: res.data?.remark,
          },
          {
            title: "开工申请人",
            value: res.data?.createBy,
          },
          {
            title: "开工申请时间",
            value: res.data?.applyTime,
          },
          {
            title: "开工申请状态",
            value: this.workApplyStatusOptions?.find((x) => {
              return x.dictValue == res.data?.workApplyStatus;
            })?.dictLabel,
          },
        ];
      });
      api
        .applyInfoCheck({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.checkList = [
            {
              title: "开工申请审核时间",
              value: res.data?.auditTime,
            },
            {
              title: "审核人",
              value: res.data?.createBy,
            },
            {
              title: "审核结果",
              value:
                res.data?.auditResult == "1"
                  ? "通过"
                  : res.data?.auditResult == "2"
                  ? "不通过"
                  : "",
            },
            {
              title: "不通过原因",
              value: res.data?.auditReason,
            },
            {
              title: "工序项类别",
              value:
                res.data?.type == "1"
                  ? "12项"
                  : res.data?.type == "2"
                  ? "2项"
                  : "",
              hidden: this.isEnergyStorage,
            },
            {
              title: "项目属性",
              value: res.data?.projectAttributeName,
              hidden: !this.isEnergyStorage,
            },
            {
              title: "工序项明细",
              value: res.data?.operationItemName,
            },
          ];
        });
      api
        .applyInfoConfirm({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.confirmList = [
            {
              title: "采购确认时间",
              value: res.data?.confirmTime,
            },
            {
              title: "确认人",
              value: res.data?.createBy,
            },
            {
              title: "产品规格",
              value: res.data?.productSpecification,
            },
            {
              title: "生产厂家",
              value: res.data?.manufacturer,
            },
            {
              title: "设备数量",
              value: res.data?.deviceTotal,
            },
            {
              title: "立柱数量",
              value: res.data?.uprightTotal,
            },
            {
              title: "设备来源",
              value: this.deviceSourceTypeList?.find((x) => {
                return x.dictValue == res.data?.deviceSource;
              })?.dictLabel,
            },
            {
              title: "设备来源说明",
              value: res.data?.deviceSourceInstruction,
            },
            {
              title: "计划到货时间",
              value: res.data?.plannedArrivalTime,
            },
            {
              title: "",
              value: "",
            },
          ];
        });
    },
  },
  computed: {
    modalConfig() {
      const form = {
        //审核
        check: {
          modalTitle: "工程审核",
          formConfig: [
            {
              field: "type",
              element: "el-radio-group",
              title: "确定工序项",
              props: {
                options: [
                  { label: "新建项目（12项工序）", value: "1" },
                  { label: "换桩项目（2项工序）", value: "2" },
                ],
              },
              rules: [{ required: !this.isEnergyStorage, message: "请选择" }],
              on: {
                change: (val) => {
                  this.$refs.formModal.setFormFields({ templateList: [] });
                  this.getWorkType(val);
                },
              },
              show: !this.isEnergyStorage,
            },
            {
              field: "templateList",
              element: "el-select",
              title: "选择工序项",
              props: {
                options: this.workTypeOptions,
                optionLabel: "workTemplateName",
                optionValue: "workTemplateId",
                multiple: true,
              },
              defaultValue: [],
              rules: [{ required: true, message: "请选择" }],
            },
            {
              field: "deviceDemandList",
              element: "slot",
              slotName: "deviceDemandList",
              title: "储能设备需求清单",
              defaultValue: [],
              show: this.isEnergyStorage,
              rules: [{ required: this.isEnergyStorage, message: "请选择" }],
            },
            {
              field: "projectAttribute",
              element: "el-select",
              title: "项目属性",
              props: {
                options: this.projectAttributeOptions,
                optionValue: "dictValue",
                optionLabel: "dictLabel",
              },
              show: this.isEnergyStorage,
              defaultValue: "1",
            },
            {
              field: "whetherNeedRoadItem",
              element: "el-radio-group",
              title: "是否需要路书",
              props: {
                options: [
                  { label: "需要", value: "0" },
                  { label: "不需要", value: "1" },
                ],
              },
              defaultValue: "1",
              show: !this.isEnergyStorage,
            },
            {
              field: "auditReason",
              element: "el-input",
              title: "审核不通过原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "500个字符以内",
              },
            },
          ],
        },
        //确认
        confirm: {
          modalTitle: "采购确认",
          formConfig: [
            {
              field: "productSpecification",
              title: "产品规格",
              rules: [
                { required: !this.isEnergyStorage, message: "请输入产品规格" },
              ],
              show: !this.isEnergyStorage,
            },
            {
              field: "manufacturer",
              title: "生产厂家",
              rules: [
                { required: !this.isEnergyStorage, message: "请输入生产厂家" },
              ],
              show: !this.isEnergyStorage,
            },
            {
              field: "deviceTotal",
              title: "设备数量",
              element: "el-input-number",
              defaultValue: 9,
              rules: [
                { required: !this.isEnergyStorage, message: "请输入设备数量" },
              ],
              props: {
                controlsPosition: "right",
                disabled: true,
              },
              show: !this.isEnergyStorage,
            },
            {
              field: "uprightTotal",
              title: "立柱数量",
              element: "el-input-number",
              props: {
                controlsPosition: "right",
              },
              rules: [
                { required: !this.isEnergyStorage, message: "请输入立柱数量" },
                {
                  pattern: /^(0|[1-9]\d{0,5}|999999)$/,
                  message: "请输入正确的数字",
                },
              ],
              show: !this.isEnergyStorage,
            },
            {
              field: "deviceSource",
              title: "设备来源",
              element: "el-select",
              props: {
                options: this.deviceSourceTypeList,
                optionLabel: "dictLabel",
                optionValue: "dictValue",
              },
              rules: [{ required: true, message: "请选择设备来源" }],
            },
            {
              field: "deviceSourceInstruction",
              title: "设备来源说明",
              element: "el-input",
              props: {
                type: "textarea",
              },
              rules: [
                {
                  required: ["2", "3"].includes(
                    this.$refs.formModal?.getFormFields()?.deviceSource
                  ),
                  message: "请输入设备来源说明",
                },
              ],
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "500个字符以内",
              },
            },
            {
              field: "plannedArrivalTime",
              title: "计划到货时间",
              element: "el-date-picker",
              props: { valueFormat: "yyyy-MM-dd" },
              rules: [{ required: true, message: "请选择计划到货时间" }],
            },
            {
              field: "deviceDemandList",
              element: "slot",
              slotName: "deviceDemandList",
              title: "设备清单",
              defaultValue: [],
              show: this.isEnergyStorage,
              rules: [{ required: this.isEnergyStorage, message: "请选择" }],
            },
          ],
        },
      };
      console.log(form, "-----");
      return form[this.operationType];
    },
  },
};
</script>

<style>
.checkFlex {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  border: 1px dotted green;
  margin: 0px 20px 20px 20px;
}
</style>
