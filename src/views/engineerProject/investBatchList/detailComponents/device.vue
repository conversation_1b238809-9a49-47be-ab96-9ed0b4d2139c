//详情-设备信息
<template>
  <div>
    <el-card v-if="!this.isEnergyStorage">
      <CommonTitle class="mb10" title="设备信息汇总" />
      <BaseDescriptions :list="infoList" :column="2"> </BaseDescriptions>
    </el-card>
    <el-card v-else>
      <CommonTitle class="mb10" title="到货签收信息" />
      <GridTable
        :columns="signColumns"
        :tableData="signTableData"
        :loading="signLoading"
        tableId="investBatchDetailDeviceList"
        row-id="orderId"
      >
        <template #viewPicture="{row,column}">
          <FileIcons :list="row[column.property]" :iconWidth="40"></FileIcons>
        </template>
      </GridTable>
    </el-card>

    <el-card>
      <CommonTitle class="mb10" title="已安装设备" />
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :loading="loading"
        tableId="investBatchDetailDeviceList"
        row-id="orderId"
      >
        <template #viewPicture="{row}">
          <div
            style="display: flex;flex-wrap: wrap;justify-content:center"
            v-if="row.photo"
          >
            <el-image
              v-for="(item, index) in row.photo.split(',')"
              :key="index"
              :src="item"
              :preview-src-list="row.photo.split(',')"
              style="width: 50px; height: 50px;margin:10px"
            ></el-image>
          </div>
        </template>
      </GridTable>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="未安装设备" />
      <GridTable
        :columns="unInstallColumns"
        :tableData="unInstallTableData"
        :loading="unInstallLoading"
        tableId="investBatchDetailUnInstallDeviceList"
        row-id="orderId"
      >
        <template #viewPicture="{row}">
          <div
            style="display: flex;flex-wrap: wrap;justify-content:center"
            v-if="row.photo"
          >
            <el-image
              v-for="(item, index) in row.photo.split(',')"
              :key="index"
              :src="item"
              :preview-src-list="row.photo.split(',')"
              style="width: 50px; height: 50px;margin:10px"
            ></el-image>
          </div>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import FileIcons from "@/components/FileIcons/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  components: { CommonTitle, BaseDescriptions, GridTable, FileIcons },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      signLoading: false,
      signTableData: [],
      signColumns: [
        {
          field: "sortNo",
          title: "序号",
        },
        {
          field: "deviceName",
          title: "设备名称",
        },
        {
          field: "deviceNo",
          title: "设备编码",
        },
        {
          field: "model",
          title: "规格型号",
        },
        {
          field: "unit",
          title: "单位",
        },
        {
          field: "num",
          title: "数量",
        },
        {
          field: "factory",
          title: "生产厂家",
        },
        {
          field: "certificateList",
          title: "合格证",
          slots: { default: "viewPicture" },
          showOverflow: false,
          minWidth: 120,
          customWidth: 200,
        },
        {
          field: "approachDate",
          title: "进场日期",
        },
        {
          field: "quality",
          title: "外观质量",
        },
        {
          field: "agreeUsed",
          title: "是否同意使用",
        },
      ],
      loading: false,
      tableData: [],
      //储能-已/未安装设备列
      energyColumns: [
        {
          field: "installNum",
          title: "序号",
        },
        {
          field: "deviceName",
          title: "设备名称",
        },
        {
          field: "deviceCode",
          title: "设备编码",
        },
        {
          field: "materialModel",
          title: "规格型号",
        },
        {
          field: "unitName",
          title: "单位",
        },
        {
          field: "deviceCount",
          title: "数量",
        },
        {
          field: "factory",
          title: "生产厂家",
        },
      ],
      //充电-已安装设备列
      installedChargeColumns: [
        {
          field: "installNum",
          title: "安装序号",
        },
        {
          field: "deviceCode",
          title: "设备编码",
        },
        {
          field: "photo",
          title: "设备图片",
          slots: { default: "viewPicture" },
          showOverflow: false,
          minWidth: 120,
          customWidth: 200,
        },
        {
          field: "deviceType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return this.subTypeOptions?.find((x) => x.dictValue == cellValue)
              ?.dictLabel;
          },
        },
        {
          field: "ratePower",
          title: "功率（kW）",
        },
        {
          field: "installMode",
          title: "安装方式",
          formatter: ({ cellValue }) => {
            return this.installModeOptions?.find(
              (x) => x.dictValue == cellValue
            )?.dictLabel;
          },
        },
        {
          field: "onlineTime",
          title: "上线时间（简道云）",
        },
        // {
        //   field: "operateStatus",
        //   title: "设备运营状态（充电平台）",
        // },
        // {
        //   field: "stationName",
        //   title: "当前绑定站点名称（充电平台）",
        // },
        // {
        //   field: "stationCode",
        //   title: "当前绑定站点编码（充电平台）",
        // },
      ],
      infoList: [],
      subTypeOptions: [],
      installModeOptions: [],

      unInstallLoading: false,
      unInstallTableData: [],
      //充电-未安装设备列
      unInstallChargeColumns: [
        {
          field: "installNum",
          title: "序号",
        },
        {
          field: "deviceCode",
          title: "设备编码",
        },
        {
          field: "photo",
          title: "设备图片",
          slots: { default: "viewPicture" },
          showOverflow: false,
          minWidth: 120,
          customWidth: 200,
        },
        {
          field: "deviceType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return this.subTypeOptions?.find((x) => x.dictValue == cellValue)
              ?.dictLabel;
          },
        },
        {
          field: "ratePower",
          title: "功率（kW）",
        },
      ],
    };
  },
  computed: {
    columns() {
      return this.isEnergyStorage
        ? this.energyColumns
        : this.installedChargeColumns;
    },
    unInstallColumns() {
      return this.isEnergyStorage
        ? this.energyColumns
        : this.unInstallChargeColumns;
    },
  },
  created() {
    Promise.all([
      this.getDicts("cm_sub_type").then((response) => {
        this.subTypeOptions = response.data;
      }),
      this.getDicts("install_mode_type").then((response) => {
        this.installModeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.getTableData();
          this.getDetail();
        });
      }, 500);
    });
  },
  methods: {
    getSignTableData() {
      this.signLoading = true;
      api
        .signDeviceList({ projectBatchId: this.projectBatchId,pageNum: 1,pageSize:999 })
        .then((res) => {
          this.signLoading = false;
          if (res?.code === "10000") {
            this.signTableData = res.data.cmProjectAcceptances;
            console.log("signTableData:----",this.signTableData);
          }
        })
        .catch(() => {
          this.signLoading = false;
        });
    },
    getEnergyTableData() {
      this.loading = true;
      this.unInstallLoading = true;
      api
        .energyDeviceList({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.loading = false;
          this.unInstallLoading = false;
          if (res?.code === "10000") {
            this.tableData = res.data?.filter((x) => x.installedFlag == "0");
            this.unInstallTableData = res.data?.filter(
              (x) => x.installedFlag == "1"
            );
          }
        })
        .catch(() => {
          this.loading = false;
          this.unInstallLoading = false;
        });
    },
    getTableData() {
      this.loading = true;
      api
        .deviceList({ projectBatchId: this.projectBatchId, installedFlag: "0" })
        .then((res) => {
          this.loading = false;
          this.tableData = res.data;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getUnInstallTableData() {
      this.unInstallLoading = true;
      api
        .deviceList({ projectBatchId: this.projectBatchId, installedFlag: "1" })
        .then((res) => {
          this.unInstallLoading = false;
          this.unInstallTableData = res.data;
        })
        .catch(() => {
          this.unInstallLoading = false;
        });
    },
    getDetail() {
      if (this.isEnergyStorage) {
        this.getSignTableData();
        this.getEnergyTableData();
      } else {
        this.getTableData();
        this.getUnInstallTableData();
        api
          .baseInfoDetail({ projectBatchId: this.projectBatchId })
          .then((res) => {
            this.infoList = [
              {
                title: "交流桩个数（踏勘）",
                value: res.data?.alternatingPileCount,
              },
              {
                title: "实际安装交流桩个数（维保通）",
                value: res.data?.realAlternatingPileCount,
              },
              {
                title: "交流枪个数（踏勘）",
                value: res.data?.alternatingGunCount,
              },
              {
                title: "实际安装交流枪个数（维保通）",
                value: res.data?.realAlternatingGunCount,
              },
              {
                title: "直流桩个数（踏勘）",
                value: res.data?.directPileCount,
              },
              {
                title: "实际安装直流桩个数（维保通）",
                value: res.data?.realDirectPileCount,
              },
              {
                title: "直流枪个数（踏勘）",
                value: res.data?.directGunCount,
              },
              {
                title: "实际安装直流枪个数（维保通）",
                value: res.data?.realDirectGunCount,
              },
              {
                title: "交流桩功率（KW）（踏勘）",
                value: res.data?.alternatingPilePower,
              },
              {
                title: "实际安装交流桩功率（KW）（维保通）",
                value: res.data?.realAlternatingPilePower,
              },
              {
                title: "交流桩总功率（KW）（踏勘）",
                value: res.data?.alternatingPileTotalPower,
              },
              {
                title: "实际安装交流桩总功率（KW）（维保通）",
                value: res.data?.realAlternatingPileTotalPower,
              },
              {
                title: "直流桩功率（KW）（踏勘）",
                value: res.data?.directPilePower,
              },
              {
                title: "实际安装直流桩功率（KW）（维保通）",
                value: res.data?.realDirectPilePower,
              },
              {
                title: "直流桩总功率（KW）（踏勘）",
                value: res.data?.directPileTotalPower,
              },
              {
                title: "实际安装直流桩总功率（KW）（维保通）",
                value: res.data?.realDirectPileTotalPower,
              },
              {
                title: "充电桩总功率（KW）（踏勘）",
                value: res.data?.totalPower,
              },
              {
                title: "实际安装充电桩总功率（KW）（维保通）",
                value: res.data?.realTotalPower,
              },
              {
                title: "预计安装充电桩数（个）（踏勘）",
                value: res.data?.totalPileCount,
              },
              {
                title: "实际安装充电桩数（个）（维保通）",
                value: res.data?.realPileCount,
              },
              {
                title: "施工供应商下单通知利旧桩数（个）（维保通）",
                value: res.data?.supplierOrderPileOldCount,
              },
              {
                title: "施工供应商下单通知设备总数（个）（维保通）",
                value: res.data?.supplierOrderPileCount,
              },
            ];
          });
      }
    },
  },
};
</script>

<style></style>
