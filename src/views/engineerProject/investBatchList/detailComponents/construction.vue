// 详情-施工信息
<template>
  <div>
    <el-card>
      <CommonTitle class="mb10" title="施工数据概览" />
      <div class="mb10" style="display: flex;justify-content: flex-end;">
        <el-button
          :type="item.type || 'primary'"
          @click.stop="item.clickFn(baseInfo)"
          v-for="(item, index) in operationBtnList"
          :key="index"
          v-show="item.show(baseInfo)"
          v-has-permi="[item.permission]"
          :icon="item.icon || ''"
          :loading="item.loading || null"
        >
          {{ item.title }}
        </el-button>
      </div>
      <BaseDescriptions :list="infoList" :column="2" />
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="施工工序完成情况" />
      <div class="work-check-title">
        <span class="work-check-total"
          >总计
          <span class="text-navy work-check-total-count">{{
            countObj.allWorkNum
          }}</span>
          项工序</span
        >
        <span
          ><svg-icon
            class-name="text-muted mr4"
            icon-class="stop"
          />未处理：<span class="text-navy">{{ countObj.pendingWorkNum }}</span>
          项</span
        >
        <span
          ><i class="el-icon-warning-outline text-warning mr4" />待审核：<span
            class="text-navy"
            >{{ countObj.reviewedWorkNum }}</span
          >
          项</span
        >
        <span
          ><i class="el-icon-check text-navy mr4" />审核通过：<span
            class="text-navy"
            >{{ countObj.approveWorkNum }}</span
          >
          项</span
        >
        <span
          ><i class="el-icon-close text-danger mr4" />审核不通过：<span
            class="text-navy"
            >{{ countObj.failAuditNum }}</span
          >
          项</span
        >
      </div>
      <el-divider></el-divider>
      <div class="card-content" v-if="btnList && btnList.length > 0">
        <el-button
          v-for="(item, index) in btnList"
          :key="index"
          @click="handleDetail(item)"
        >
          <!-- 审核通过 -->
          <i
            class="el-icon-check card-icon-green"
            v-if="item.auditResult == '1'"
          />
          <!-- 审核不通过：审核不通过状态且app侧未处理 -->
          <i
            class="el-icon-close card-icon-red"
            v-if="item.auditResult == '2' && item.flag != '0'"
          />
          <!-- 待处理：没有审核结果且状态为待处理 -->
          <svg-icon
            class-name="svg-icon-none"
            icon-class="stop"
            v-if="!item.auditResult && item.workStatus == '1'"
          />
          <!-- 已处理（待审核）：没有审核结果且状态为待审核 / 审核不通过后app侧已处理 -->
          <i
            class="el-icon-warning-outline card-icon-orange"
            v-if="
              (!item.auditResult && item.workStatus == '2') ||
                (item.auditResult == '2' && item.flag == '0')
            "
          />
          <span class="card-title">{{ item.workName }}</span>
        </el-button>
      </div>
      <el-empty v-else></el-empty>
    </el-card>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      @modalConfirm="modalConfirmHandler"
      :showForm="false"
      :autoClose="false"
      modalWidth="60%"
    >
      <template #modalDefault="{ row }">
        <MultiFileUpload
          ref="attachments"
          @uploadSuccess="
            (attachments) => {
              return updateAttachments(attachments, row);
            }
          "
          accept=".pdf"
          :maxSize="20480"
          :limit="50"
        >
          <template #customTip>
            支持批量上传，上传格式支持pdf文件，单个文件20G以内
          </template>
        </MultiFileUpload>
      </template>
    </BaseFormModal>
    <CompleteReport ref="completeReport"></CompleteReport>

    <!-- 工序详情 -->
    <el-drawer
      title=""
      :visible.sync="workCheckVis"
      :show-close="false"
      size="80%"
      append-to-body
      :wrapperClosable="true"
    >
      <WorkCheck
        :projectId="projectBatchId"
        :workId="workId"
        :edit="false"
        :isEnergyStorage="isEnergyStorage"
      />
    </el-drawer>
  </div>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import { initParams } from "@/utils/buse.js";
import MultiFileUpload from "@/components/MultipleFileUpload";
import CompleteReport from "../components/completeReport.vue";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import WorkCheck from "../../components/WorkCheck";
import { fileDownLoad, downloadUrl } from "@/utils/downLoad.js";
import { queryItemCount } from "@/api/engineerProject/investBatchList/workCheck";
import moment from "moment";
export default {
  components: {
    CommonTitle,
    BaseDescriptions,
    BaseFormModal,
    MultiFileUpload,
    CompleteReport,
    WorkCheck,
  },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      downloadLoading: false,
      projectStatusOptions: [],
      workCheckVis: false,
      workId: "",
      baseInfo: {},
      infoList: [],
      btnList: [],

      workItemList: [],
      commonItemDict: [
        {
          workName: "路书",
          workId: "路书",
          formType: "5",
          condition: (val) => {
            return val.whetherNeedRoadItem == "0";
          },
        },
        {
          workName: "竣工图",
          workId: "竣工图",
          formType: "4",
          workStatus: "1",
          condition: (val) => {
            return true;
          },
        },
        {
          workName: "资产验收单",
          workId: "资产验收单",
          formType: "1",
          workStatus: "1",
          condition: (val) => {
            return true;
          },
        },
        {
          workName: "决算单",
          workId: "决算单",
          formType: "3",
          workStatus: "1",
          condition: (val) => {
            return true;
          },
        },
        {
          workName: "变更项",
          workId: "变更项",
          formType: "2",
          condition: (val) => {
            return val.whetherChangeRequest == "0";
          },
        },
      ],
      countObj: {},
    };
  },
  created() {
    Promise.all([
      this.getDicts("project_status").then((response) => {
        this.projectStatusOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getDetail();
        });
      }, 500);
    });

    // mock
    // this.handleDetail();
  },
  methods: {
    //获取工序项统计数据
    async getItemCount() {
      const res = await queryItemCount({ projectBatchId: this.projectBatchId });
      if (res?.code == "10000") {
        this.countObj = res.data || {};
      }
    },
    //打开工序项详情
    handleDetail(item = {}) {
      this.workCheckVis = true;
      this.workId = item.workId;
    },
    //下载压缩包
    async downloadAllZip() {
      this.downloadLoading = true;
      const method = "exportAllOriginal";
      const res = await api[method]({
        projectBatchId: this.projectBatchId,
      }).catch(() => {
        this.downloadLoading = false;
      });
      if (res) {
        this.downloadLoading = false;
        api.recordExport({
          projectBatchId: this.projectBatchId,
          operateTypeName: "下载竣工报告",
        });
        // fileDownLoad(res);
        const fileName = res.data?.replace(/^.*\/([^\/]+)$/, "\$1");
        downloadUrl(res.data, fileName);
      }
    },
    //下载电子报告
    async handleDownload() {
      api.recordExport({
        projectBatchId: this.projectBatchId,
        operateTypeName: "下载竣工报告",
      });
      const tabDict = [
        { label: "竣工报告首页", name: "home", apiFn: "exportHomePage" },
        { label: "目录", name: "catalogue", apiFn: "exportCatalogue" },
        { label: "施工合同", name: "contract", apiFn: "applyDetail" },
        ...this.workItemList,
        { label: "安全交底", name: "safety", apiFn: "exportSafety" },
        { label: "竣工图", name: "pic", apiFn: "exportProjectComplete" },
        { label: "资产验收单", name: "check", apiFn: "exportFixedAssets" },
        { label: "决算单", name: "calculate", apiFn: "exportBalanceReport" },
        { label: "变更项", name: "change", apiFn: "exportChangeReport" },
      ];
      for (const x of tabDict) {
        let params = { projectBatchId: this.projectBatchId };
        if (x.apiFn == "exportItem") {
          params["workId"] = x.workId;
        }
        const res = await api[x.apiFn](params);
        //施工合同 取开工申请详情中的附件
        if (x.name === "contract") {
          const list = res.data?.constructionFile
            ? JSON.parse(res.data?.constructionFile)
            : [];
          // list.map((x) => {
          //   downloadUrl(x.storePath, x.docName);
          // });
          for (const x of list) {
            await downloadUrl(x.storePath, x.docName);
          }
        } else {
          await fileDownLoad(res);
        }

        //处理附表
        if (x.attachType == "1") {
          api.exportWorkResistance(params).then(async (res) => {
            await fileDownLoad(res);
          });
        } else if (x.attachType == "2") {
          api.exportWorkCable(params).then(async (res) => {
            await fileDownLoad(res);
          });
        }
      }
      // tabDict.map((x) => {

      // });
    },
    //查看竣工报告
    handleReport(row) {
      this.$refs.completeReport.open(row);
    },
    //上传竣工报告
    handleUpload(row) {
      console.log(row, this.$refs, "上传");
      this.$refs.formModal.open({
        docList: [],
      });
      this.$refs.attachments?.setAttachments([]);
    },
    updateAttachments(attachments, row) {
      row.docList = attachments;
    },
    modalConfirmHandler(formParams) {
      if (!formParams.docList || formParams.docList.length == 0) {
        this.$message.warning("请上传至少一个文件！");
        return false;
      }
      let params = formParams.docList.map((x) => {
        return {
          ...x,
          relaBizId: this.projectBatchId,
          businessType: "竣工报告",
        };
      });
      api.upload(params).then((res) => {
        if (res?.code === "10000") {
          api
            .completeAcceptance({
              projectBatchId: this.projectBatchId,
              projectStatus: "11",
              completionReportUploadTime: moment().format("YYYY-MM-DD"),
            })
            .then((res1) => {
              if (res1?.code === "10000") {
                this.$message.success("提交成功");
                this.$refs.formModal.closeVisible();
                this.getDetail();
              }
            });
        }
      });
    },
    //施工验收
    handleConstructAcceptance(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/constructAcceptance",
        query: {
          projectId: this.projectBatchId,
          isEnergyStorage: JSON.stringify(row.businessType == "2"),
        },
      });
    },
    //竣工验收
    handleCompleteAcceptance(row) {
      this.$router.push({
        path: "/engineerProject/investBatchList/completeAcceptance",
        query: { ...row },
      });
    },
    getDetail() {
      this.getItemCount();
      api
        .baseInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.baseInfo = res.data;
        });
      api
        .constructionWorkDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          api
            .commonWorkDetail({ projectBatchId: this.projectBatchId })
            .then((res1) => {
              let commonItems = [];
              const { formData = [] } = res1.data;

              this.commonItemDict.map((x) => {
                if (x.condition?.(res1.data)) {
                  const obj =
                    formData.find((y) => y.formType == x.formType) || {};
                  commonItems.push({ ...x, ...obj });
                }
              });
              // const commonItems = res1.data?.formData?.map((x) => {
              //   const obj = this.commonItemDict.find(
              //     (i) => i.formType == x.formType
              //   );
              //   return { ...x, ...obj };
              // });
              this.btnList = [...res.data, ...commonItems];
            });

          this.workItemList = res.data?.map((x) => {
            return { ...x, apiFn: "exportItem" };
          });
        });
      api
        .constructionInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.infoList = [
            {
              title: "开工申请提交时间",
              value: res.data?.workApplyTime,
            },
            {
              title: "提交人",
              value: res.data?.applyUser,
            },
            {
              title: "开工申请工程审批通过时间",
              value: res.data?.workApplyPassTime,
            },
            {
              title: "工程审批人",
              value: res.data?.auditUser,
            },
            {
              title: "开工申请采购确认时间",
              value: res.data?.workApplyConfirmTime,
            },
            {
              title: "采购确认人",
              value: res.data?.purchaseUser,
            },
            {
              title: "施工队名称",
              value: res.data?.consTeamName,
            },
            {
              title: "施工队负责人",
              value: res.data?.consTeamLeaderName,
            },
            {
              title: "施工队负责人联系电话",
              value: res.data?.consTeamLeaderTel,
            },
            {
              title: "施工队项目经理",
              value: res.data?.consTeamManagerName,
            },
            {
              title: this.isEnergyStorage
                ? "线缆实际长度（米）"
                : "主缆实际长度（米）",
              value: res.data?.mainCableRealLength,
            },
            {
              title: "施工实际总价（元）",
              value: res.data?.consRealTotalPrice,
            },
            {
              title: "单桩施工实际成本（元）",
              value: res.data?.pileRealCost,
              hidden: this.isEnergyStorage,
            },
            {
              title: "资产验收设备数（台）",
              value: res.data?.deviceCount,
              hidden: this.isEnergyStorage,
            },
            {
              title: "项目状态",
              value: this.projectStatusOptions?.find(
                (x) => x.dictValue == res.data?.projectStatus
              )?.dictLabel,
            },
            {
              title: this.isEnergyStorage
                ? "变更后实际设备数（台）"
                : "变更后实际桩数（台）",
              value: res.data?.afterChangePileCount,
            },
            {
              title: "完工时间",
              value: res.data?.consCompleteTime,
            },
            {
              title: "验收时间",
              value: res.data?.consCheckPassTime,
            },
            {
              title: "竣工报告上传时间",
              value: res.data?.completionReportUploadTime,
            },
            {
              title: "竣工报告审核通过时间",
              value: res.data?.completionReportPassTime,
            },
            {
              title: "评价时间",
              value: res.data?.stationName1,
            },
            {
              title: "评分",
              value: res.data?.stationName1,
            },
          ];
        });
    },
  },
  computed: {
    modalConfig() {
      return {
        modalTitle: "上传竣工报告",
        formConfig: [],
      };
    },
    operationBtnList() {
      return [
        {
          title: "施工验收",
          permission: "engineerProject:investBatchList:constructAccept",
          show: (row) => {
            //待验收/施工中
            const condition1 = ["7", "8", "9"].includes(row.projectStatus);
            return condition1;
          },
          clickFn: (row) => {
            this.handleConstructAcceptance(row);
          },
        },
        {
          title: "竣工验收",
          permission: "engineerProject:investBatchList:completeAccept",
          show: (row) => {
            //竣工验收
            const condition1 = row.projectStatus == "11";
            return condition1;
          },
          clickFn: (row) => {
            this.handleCompleteAcceptance(row);
          },
        },
        {
          title: "上传竣工报告",
          permission: "engineerProject:investBatchList:upload",
          show: (row) => {
            //待上线/竣工验收/竣工验收不通过
            const condition1 = ["10", "11", "12"].includes(row.projectStatus);
            return condition1;
          },
          clickFn: (row) => {
            this.handleUpload(row);
          },
        },
        {
          title: "竣工报告",
          icon: "el-icon-view",
          type: "text",
          permission: "engineerProject:investBatchList:report",
          show: (row) => {
            //待上线/竣工验收/竣工验收不通过/待评价/已评价
            const condition1 = ["10", "11", "12", "13", "14"].includes(
              row.projectStatus
            );
            return condition1;
          },
          clickFn: (row) => {
            this.handleReport(row);
          },
        },
        {
          title: "下载电子版报告",
          icon: "el-icon-download",
          type: "text",
          permission: "engineerProject:investBatchList:download",
          show: (row) => {
            //待上线/竣工验收/竣工验收不通过/待评价/已评价
            const condition1 = ["10", "11", "12", "13", "14"].includes(
              row.projectStatus
            );
            return condition1;
          },
          loading: this.downloadLoading,
          clickFn: (row) => {
            this.downloadAllZip();
          },
        },
      ];
    },
  },
};
</script>

<style lang="less" scoped>
.card-content {
  display: flex;
  flex-wrap: wrap;
  .el-button--small {
    position: relative;

    color: #479b81;
    margin: 20px 40px;
    width: 120px;
    padding: 24px 0;
    font-size: 14px;
    border: 1px solid #479b81;
    border-radius: 9px;
    box-shadow: 0 2px 4px rgba(71, 155, 128, 0.5),
      0 0 6px rgba(71, 155, 128, 0.1);
    .el-icon-check,
    .el-icon-close,
    .el-icon-warning-outline,
    .svg-icon-none {
      position: absolute;
      top: 1px;
      right: 1px;
      color: #479b81;
      color: #fff;
      border-radius: 0 2px 0;
      font-size: 18px;
    }
    .card-icon-green {
      color: #479b81;
    }
    .card-icon-red {
      color: #e83425;
    }
    .card-icon-orange {
      color: #f8ac59;
    }
    .card-title {
      width: 100%;
      display: inline-block;
      text-wrap: wrap;
      word-wrap: break-word;
      padding: 0 10px;
      margin-left: 0;
      white-space: pre-wrap;
      line-height: 20px;
    }
  }
}
.work-check-title {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-right: 30px;
  margin-top: 20px;
  font-size: 14px;
  .work-check-total {
    font-size: 16px;
    &-count {
      font-size: 20px;
      font-weight: 700;
    }
  }
}
.el-divider--horizontal {
  margin: 20px 0 0 0;
  background-color: #9dc9bb;
}
</style>
