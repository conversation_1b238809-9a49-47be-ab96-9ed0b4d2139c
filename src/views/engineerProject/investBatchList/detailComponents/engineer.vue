//详情-工程信息
<template>
  <div>
    <el-card>
      <CommonTitle class="mb10" title="工程信息汇总" />
      <BaseDescriptions :list="infoList" :column="2">
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card v-if="!isEnergyStorage">
      <CommonTitle class="mb10" title="踏勘单安装信息" />
      <BaseDescriptions :list="installList" :column="2">
        <!--        <template #pic="{itemVal}">-->
        <!--          <el-image-->
        <!--            v-for="(item, index) in itemVal"-->
        <!--            :key="index"-->
        <!--            :src="item.url"-->
        <!--            style="width: 50px; height: 50px;margin:10px"-->
        <!--            :preview-src-list="itemVal.map((item) => item.url)"-->
        <!--          ></el-image>-->
        <!--          &lt;!&ndash; @click="handlePreview(index, itemVal)" &ndash;&gt;-->
        <!--        </template>-->
        <template #links="{itemVal}">
          <FileIcons :list="itemVal"></FileIcons>
        </template>
      </BaseDescriptions>
    </el-card>
    <!-- <PicPreview ref="picPreview"></PicPreview> -->
  </div>
</template>

<script>
// import PicPreview from "@/components/Upload/picPreview.vue";
import FileIcons from "@/components/FileIcons/index.vue";
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  components: {
    // PicPreview,
    CommonTitle,
    BaseDescriptions,
    FileIcons,
  },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      infoList: [],
      installList: [],
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    handlePreview(index, list) {
      this.$refs.picPreview.open(index, list);
    },
    getDetail() {
      api
        .baseInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.infoList = this.isEnergyStorage
            ? [
                {
                  title: "储能设备单价（元/wh）",
                  value: res.data?.unitStorageDevicesCost,
                },
                {
                  title: "工程实际成本（元）",
                  value: res.data?.consRealTotalPrice,
                },
                {
                  title: "工程单价（元/wh）",
                  value: res.data?.unitConstuctCost,
                },
                {
                  title: "线缆实际长度（米）",
                  value: res.data?.mainCableRealLength,
                },
                {
                  title: "网关台数",
                  value: res.data?.gatewayNum,
                },
                {
                  title: "网关等配件单价（元/台）",
                  value: res.data?.unitGatewayCost,
                },
                {
                  title: "网关等配件成本（元）",
                  value: res.data?.gatewayCost,
                },
                {
                  title: "拟投资含税总金额（元）",
                  value: res.data?.investmentAmount,
                },
                {
                  title: "储能设备成本（元）",
                  value: res.data?.storageDevicesCost,
                },
                {
                  title: "工程预计成本（元）",
                  value: res.data?.constuctCost,
                },
              ]
            : [
                {
                  title: "单桩预计施工成本（元）",
                  value: res.data?.pileExpectCost,
                },
                {
                  title: "单桩实际施工成本（元）",
                  value: res.data?.pileRealCost,
                },
                {
                  title: "施工成本预计总价（元）",
                  value: res.data?.consExpectTotalPrice,
                },
                {
                  title: "施工成本实际总价（元）",
                  value: res.data?.consRealTotalPrice,
                },
                {
                  title: "单瓦预计施工成本（元）",
                  value: res.data?.tileExpectCost,
                },
                {
                  title: "单瓦实际施工成本（元）",
                  value: res.data?.tileRealCost,
                },
                {
                  title: "主缆预计长度（米）",
                  value: res.data?.mainCableExpectLength,
                },
                {
                  title: "主缆实际长度（米）",
                  value: res.data?.mainCableRealLength,
                },
                {
                  title: "单桩商务成本（元）",
                  value: res.data?.pileBusinessCost,
                },
                {
                  title: "签收设备数（台）",
                  value: res.data?.signedPileCount,
                },
                {
                  title: "预计总商务成本（元）",
                  value: res.data?.totalBusinessCost,
                },
                {
                  title: "实际总商务成本（元）",
                  value: res.data?.totalBusinessCostReal,
                },
                {
                  title: "实际安装设备数（台）",
                  value: res.data?.realPileCount,
                },
                {
                  title: "单桩设备成本（元）",
                  value: res.data?.singlePileDeviceCost,
                },
                {
                  title: "预计总设备成本（元）",
                  value: res.data?.deviceTotalCost,
                },
                {
                  title: "实际总设备成本（元）",
                  value: res.data?.deviceTotalCostReal,
                },
                {
                  title: "未安装设备数（台）",
                  value: res.data?.unInstallPileCount,
                },
                {
                  title: "电缆类型（种）",
                  value: res.data?.cableType,
                },
                {
                  title: "拟投资含税金额（元）",
                  value: res.data?.investmentAmount,
                },
                {
                  title: "主缆材质",
                  value: res.data?.mainCableMaterial,
                },
                {
                  title: "单桩总成本（元）",
                  value: res.data?.pileTotalCost,
                },
                {
                  title: "主缆规格",
                  value: res.data?.mainCableSpec,
                },
                {
                  title: "主缆型号",
                  value: res.data?.mainCableModel,
                },
                {
                  title: "电源点照片（非报电项目）",
                  value: res.data?.powerPointPhoto
                    ? JSON.parse(res.data?.powerPointPhoto)
                    : [],
                  slotName: "links",
                },
                {
                  title: "朗新能投工程勘测报告",
                  value: res.data?.lsConsSurveyReport
                    ? JSON.parse(res.data?.lsConsSurveyReport)
                    : [],
                  slotName: "links",
                },
                {
                  title: "朗新能投工程施工图",
                  value: res.data?.lsConsDrawing
                    ? JSON.parse(res.data?.lsConsDrawing)
                    : [],
                  slotName: "links",
                },
                {
                  title: "朗新能投工程全费用清单",
                  value: res.data?.lsConsCostList
                    ? JSON.parse(res.data?.lsConsCostList)
                    : [],
                  slotName: "links",
                },
                // {
                //   title: "施工发包模式",
                //   value: res.data?.constructionContractMode,
                // },
                {
                  title: "",
                  value: "",
                },
              ];
          this.installList = [
            {
              title: "安装区域",
              value: res.data?.installArea,
            },
            {
              title: "安装环境原始照片",
              value: res.data?.installLocationPicture
                ? JSON.parse(res.data?.installLocationPicture)
                : [],
              slotName: "links",
            },
            {
              title: "场站剩余容量(KVA)",
              value: res.data?.stationRemainCapacity,
            },
            {
              title: "场站通信信号",
              value: res.data?.stationCommunicateSignal,
            },
            {
              title: "是否换桩项目",
              value: res.data?.isReplacePileProject,
            },
            {
              title: "周围危险物识别",
              value: res.data?.aroundDangerIdentify,
            },
          ];
        });
    },
  },
};
</script>

<style></style>
