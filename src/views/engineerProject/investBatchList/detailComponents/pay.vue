<template>
  <div>
    <el-card>
      <CommonTitle class="mb10" title="施工付款信息" />
      <BaseDescriptions :list="infoList" :column="2"> </BaseDescriptions>
      <div v-for="(item, index) in payList" :key="index">
        <div class="mb10 mt20">{{ index + 1 + "次付款信息" }}</div>
        <BaseDescriptions :list="item" :column="2"> </BaseDescriptions>
      </div>
    </el-card>
  </div>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
export default {
  components: { CommonTitle, BaseDescriptions },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
    projectCode: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      infoList: [],
      payList: [],
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      api
        .baseInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.infoList = this.isEnergyStorage
            ? [
                {
                  title: "施工预计总价（元）",
                  value: res.data?.consExpectTotalPrice,
                },
                {
                  title: "设备总成本（元）",
                  value: res.data?.deviceTotalCost,
                },
                {
                  title: "施工实际总价（元）",
                  value: res.data?.consRealTotalPrice,
                },
                {
                  title: "已付金额（元）",
                  value: res.data?.paidAmount,
                },
                {
                  title: "未付金额（元）",
                  value: res.data?.unpaidAmount,
                },
                {
                  title: "已付比例",
                  value: res.data?.paidRatio,
                },
              ]
            : [
                {
                  title: "施工预计总价（元）",
                  value: res.data?.consExpectTotalPrice,
                },
                {
                  title: "主缆预计长度（米）",
                  value: res.data?.mainCableExpectLength,
                },
                {
                  title: "施工实际总价（元）",
                  value: res.data?.consRealTotalPrice,
                },
                {
                  title: "主缆实际长度（米）",
                  value: res.data?.mainCableRealLength,
                },
                {
                  title: "单桩施工预计成本（元）",
                  value: res.data?.pileExpectCost,
                },
                {
                  title: "主缆材质",
                  value: res.data?.mainCableMaterial,
                },
                {
                  title: "单桩施工实际成本（元）",
                  value: res.data?.pileRealCost,
                },
                {
                  title: "设备总成本（元）",
                  value: res.data?.deviceTotalCost,
                },
                {
                  title: "单瓦施工预计成本（元）",
                  value: res.data?.tileExpectCost,
                },
                {
                  title: "实际施工安装桩数（台）",
                  value: res.data?.realPileCount,
                },
                {
                  title: "单瓦施工实际成本（元）",
                  value: res.data?.tileRealCost,
                },
                {
                  title: "已付金额（元）",
                  value: res.data?.paidAmount,
                },
                {
                  title: "施工总金额（元）",
                  value: res.data?.totalAmount,
                },
                {
                  title: "未付金额（元）",
                  value: res.data?.unpaidAmount,
                },
                {
                  title: "已付比例",
                  value: res.data?.paidRatio,
                },
                {
                  title: "",
                  value: "",
                },
              ];
        });
      api.getPayDescription({ projNo: this.projectCode }).then((res) => {
        this.payList = res.data?.map((x) => {
          return [
            {
              title: "付款申请单号",
              value: x.applyNo,
            },
            {
              title: "付款申请单状态",
              value: x.status,
            },
            {
              title: "付款金额（元）",
              value: x.payMoney,
            },
            {
              title: "付款时间",
              value: x.payTime,
            },
          ];
        });
      });
    },
  },
};
</script>

<style></style>
