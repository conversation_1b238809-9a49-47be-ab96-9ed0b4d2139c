//详情-基本信息
<template>
  <div>
    <el-card class="step-card">
      <div
        slot="header"
        class="card-title-wrap"
        style="justify-content: space-between"
      >
        <div style="display: flex;align-items: center;">
          <div class="card-title-line"></div>
          <span>项目进度</span>
          <el-tag
            style="margin-left: 10px;font-size: 14px"
            :type="getTagType(baseInfo.projectType)"
            effect="plain"
            size="medium"
            >{{ baseInfo.projectType }}</el-tag
          >
        </div>
        <div>
          <el-button
            type="primary"
            @click.stop="item.clickFn(baseInfo)"
            v-for="(item, index) in operationBtnList"
            :key="index"
            v-show="item.show(baseInfo)"
            v-has-permi="[item.permission]"
          >
            {{ item.title }}
          </el-button>
        </div>
      </div>
      <el-steps :space="200" align-center>
        <el-step
          v-for="(item, index) in stepsList"
          :key="index"
          :title="item.name"
          :status="item.status"
          :description="item.description"
        />
      </el-steps>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="基本信息" />
      <BaseDescriptions :list="infoList" :column="2">
        <template #links="{itemVal,row}">
          <div>
            <div v-for="(item, index) in itemVal" :key="index">
              <el-button
                type="text"
                @click="handleDialog(row, item)"
                style="margin-left:0"
                >{{ item }}
              </el-button>
            </div>
          </div>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="关联单号" />
      <BaseDescriptions :list="relateList" :column="2">
        <template #links="{itemVal,row}">
          <div>
            <div v-for="(item, index) in itemVal" :key="index">
              <el-button
                type="text"
                @click="handleDialog(row, item)"
                style="margin-left:0"
                >{{ item }}
              </el-button>
            </div>
          </div>
        </template>
      </BaseDescriptions>
    </el-card>
    <el-card>
      <CommonTitle class="mb10" title="时间节点" />
      <BaseDescriptions :list="timeList" :column="2"> </BaseDescriptions>
    </el-card>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      modalWidth="70%"
      labelWidth="160px"
    >
      <template #formHeader>
        <div class="checkFlex" v-if="operationType === 'check'">
          <div>计划开工时间：{{ scheduledStartTime }}</div>
          <div>计划竣工时间：{{ scheduledEndTime }}</div>
          <div>设备需求到场时间：{{ equipmentArrivalTime }}</div>
        </div>
      </template>
      <template #modalFooter="{ row }">
        <div v-if="operationType === 'check'">
          <el-button @click="submitCheck(row, false)" :loading="checkLoading"
            >审核不通过</el-button
          >
          <el-button
            @click="submitCheck(row, true)"
            type="primary"
            :loading="checkLoading"
            >审核通过</el-button
          >
        </div>
      </template>
      <template #deviceDemandList="{ item, params }">
        <EditTable ref="editTable" v-model="params[item.field]"></EditTable>
      </template>
    </BaseFormModal>
    <ExploreInfo ref="exploreInfo"></ExploreInfo>
    <AgreementInfo ref="agreementInfo"></AgreementInfo>
    <PurchaseOrder ref="purchaseOrder"></PurchaseOrder>
    <PurchaseAgreement ref="purchaseAgreement"></PurchaseAgreement>
    <OnlineOrder ref="onlineOrder"></OnlineOrder>
    <PayOrder ref="payOrder"></PayOrder>
  </div>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import BaseDescriptions from "@/components/BaseDescriptions/index.vue";
import api from "@/api/engineerProject/investBatchList/index.js";
import ExploreInfo from "../components/exploreInfo.vue";
import AgreementInfo from "../components/agreementInfo.vue";
import PurchaseOrder from "../components/purchaseOrder.vue";
import PurchaseAgreement from "../components/purchaseAgreement.vue";
import OnlineOrder from "../components/onlineOrder.vue";
import PayOrder from "../components/payOrder.vue";
import { initParams } from "@/utils/buse.js";
import EditTable from "../components/EditTable.vue";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
export default {
  components: {
    CommonTitle,
    BaseDescriptions,
    ExploreInfo,
    BaseFormModal,
    AgreementInfo,
    PurchaseOrder,
    PurchaseAgreement,
    PayOrder,
    OnlineOrder,
    EditTable,
  },
  props: {
    projectBatchId: {
      type: String,
      default: "",
    },
    projectCode: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      checkLoading: false,
      baseInfo: {},
      scheduledEndTime: "",
      scheduledStartTime: "",
      equipmentArrivalTime: "",
      stepsList: [
        { name: "商签中", status: "success", description: "2023-1-111" },
        { name: "施工发包中", status: "success", description: "2023-1-111" },
        { name: "未转固", status: "wait", description: "2023-1-111" },
      ],
      operationBtnList: [
        {
          title: "开工申请",
          permission: "engineerProject:investBatchList:apply",
          show: (row) => {
            //待申请
            const condition1 = row.workApplyStatus == "1";
            return condition1;
          },
          clickFn: (row) => {
            this.handleApply(row);
          },
        },
        {
          title: "审核",
          permission: "engineerProject:investBatchList:check",
          show: (row) => {
            //待审核
            const condition1 = row.workApplyStatus == "2";

            return condition1;
          },
          clickFn: (row) => {
            this.handleCheck(row);
          },
        },
      ],
      infoList: [],
      relateList: [],
      timeList: [],
      workTypeOptions: [],
      operationType: "check",
      projectAttributeOptions: [],
    };
  },
  created() {
    this.getDicts("project_attribute").then((response) => {
      this.projectAttributeOptions = response.data;
    });
    this.getDetail();
  },
  methods: {
    // 根据项目批次id  查询 开工申请时填写的 计划开工时间  计划竣工时间  设备需求当到场时间
    getWorkTime(projectBatchId) {
      api.workTime({ projectBatchId: projectBatchId }).then((res) => {
        if (res?.code === "10000") {
          this.scheduledStartTime = res.data.scheduledStartTime;
          this.scheduledEndTime = res.data.scheduledEndTime;
          this.equipmentArrivalTime = res.data.equipmentArrivalTime;
          console.log(
            "计划时间",
            this.scheduledStartTime,
            this.scheduledEndTime,
            this.equipmentArrivalTime
          );
        }
      });
    },
    handleDialog(row, val) {
      if (row.isCustomClick) {
        row.customClick(row, val);
      } else {
        this.$refs[row.refName]?.open({
          ...row.params,
          applyNo: val,
          isEnergyStorage: this.isEnergyStorage,
        });
      }
    },
    modalConfirmHandler(row) {
      console.log(row, this.operationType);
    },
    //审核
    handleCheck(row) {
      this.operationType = "check";
      //储能传3
      const type = this.isEnergyStorage
        ? "3"
        : row.projectType === "新建"
        ? "1"
        : "2";
      this.getWorkType(type);
      this.getWorkTime(row.projectBatchId);
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
        projectBatchId: row.projectBatchId,
        type: type,
      });
    },
    //根据工序项类型查询工序模版名称
    getWorkType(type) {
      api.workType({ type: type }).then((res) => {
        if (res?.code === "10000") {
          this.workTypeOptions = res.data;
        }
      });
    },
    async submitCheck(row, flag) {
      //储能项目 审核不通过时不校验表单和表格
      if (flag && this.isEnergyStorage) {
        const errMap = await this.$refs.editTable.validTable();
        if (errMap) {
          this.$message.warning("请填写表格必填项！");
          return;
        }
      }
      this.$refs.formModal?.$refs.baseForm.validate((valid) => {
        //什么情况需要校验表单？非储能项目+储能项目审核通过时
        //不需要校验？储能项目且审核不通过
        if (!valid && (!this.isEnergyStorage || flag)) return;
        if (!flag && !row.auditReason) {
          this.$message.warning("请填写审核不通过原因！");
          return;
        }
        const params = {
          ...row,
          auditResult: flag ? "1" : "2",
          templateList: row.templateList.map((x) => {
            return this.workTypeOptions?.find((y) => y.workTemplateId === x);
          }),
        };
        this.checkLoading = true;
        api
          .check(params)
          .then((res) => {
            this.checkLoading = false;
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              this.$refs.formModal.closeVisible();
              this.getDetail();
            }
          })
          .catch(() => {
            this.checkLoading = false;
          });
      });
    },
    //开工申请
    handleApply(row, type = "add") {
      this.$router.push({
        path: "/engineerProject/investBatchList/apply",
        query: {
          type: type,
          projectBatchId: this.projectBatchId,
          projectCode: this.projectCode,
        },
      });
    },
    getDetail() {
      api
        .baseInfoDetail({ projectBatchId: this.projectBatchId })
        .then((res) => {
          this.baseInfo = res.data;
          this.stepsList = res.data?.progressList.map((x) => {
            return {
              name: x.name,
              status:
                x.color === "green"
                  ? "success"
                  : x.color === "red"
                  ? "error"
                  : "",
              description: x.time,
            };
          });
          this.infoList = this.isEnergyStorage
            ? [
                {
                  title: "场站名称",
                  value: res.data?.stationName,
                },
                {
                  title: "项目名称",
                  value: res.data?.projectName,
                },
                {
                  title: "场站编码",
                  value: res.data?.stationCode,
                },
                {
                  title: "项目编码",
                  value: res.data?.projectCode,
                },
                {
                  title: "踏勘编码",
                  value:
                    res.data?.surveyCodeSub?.split(",") ||
                    res.data?.surveyCodeMain?.split(",") ||
                    [],
                  slotName: "links",
                  refName: "exploreInfo",
                },
                {
                  title: "踏勘审批通过时间",
                  value: res.data?.surveyPassTime,
                },
                {
                  title: "储能类型",
                  value: res.data?.storageType,
                },
                {
                  title: "投建模式",
                  value: res.data?.constructType,
                },
                {
                  title: "投资法人实体（资产归属法人实体）",
                  value: res.data?.investLegalEntity,
                },
                {
                  title: "业绩归属公司",
                  value: res.data?.businessOwnCompany,
                },
                {
                  title: "业务负责人",
                  value: res.data?.businessOwner,
                },
                {
                  title: "企业所得税率",
                  value: res.data?.corporateIncomeTax,
                },
                {
                  title: "运营商名称",
                  value: res.data?.operatorName,
                },
                {
                  title: "平台方名称",
                  value: res.data?.platformName,
                },
                {
                  title: "是否与新电途互联互通",
                  value: res.data?.isXdtConnect,
                },
                {
                  title: "详细地址",
                  value: res.data?.stationLocation,
                },
                {
                  title: "站点定位",
                  value: res.data?.stationPosition,
                },
                {
                  title: "合同年限",
                  value: res.data?.contractYear,
                },
                {
                  title: "场地租赁剩余年限",
                  value: res.data?.remainingLeaseVenue,
                },
                {
                  title: "场地方名称",
                  value: res.data?.siteCompanyName,
                },
                {
                  title: "场地方分成税率",
                  value: res.data?.siteTaxRate,
                },
                {
                  title: "场地方分成比例",
                  value: res.data?.customerSharing,
                },
                {
                  title: "居间方开票税率",
                  value: res.data?.agentTaxRate,
                },
                {
                  title: "居间商名称",
                  value: res.data?.middleBusiness,
                },
                {
                  title: "一次性商务费（元）",
                  value: res.data?.businessExpense,
                },
                {
                  title: "居间商分成比例",
                  value: res.data?.agentShareRatio,
                },
                {
                  title: "人员激励费用",
                  value: res.data?.encourageAward,
                },
                {
                  title: "站点联系人",
                  value: res.data?.stationContacts,
                },
                {
                  title: "联系方式",
                  value: res.data?.stationPhone,
                },
                {
                  title: "商务人员",
                  value: res.data?.businessDevelopmentName,
                },
                {
                  title: "所属部门",
                  value: res.data?.orgName,
                },
              ]
            : [
                {
                  title: "场站名称",
                  value: res.data?.stationName,
                },
                {
                  title: "场站编码",
                  value: res.data?.stationCode,
                },
                {
                  title: "项目名称",
                  value: res.data?.projectName,
                },
                {
                  title: "项目编码",
                  value: res.data?.projectCode,
                },
                {
                  title: "踏勘编码",
                  value:
                    res.data?.surveyCodeSub?.split(",") ||
                    res.data?.surveyCodeMain?.split(",") ||
                    [],
                  slotName: "links",
                  refName: "exploreInfo",
                },
                {
                  title: "踏勘审批通过时间",
                  value: res.data?.surveyPassTime,
                },
                {
                  title: "投建类型",
                  value: res.data?.constructType,
                },
                {
                  title: "业务类型",
                  value: res.data?.businessTypeName,
                },
                {
                  title: "场站类型",
                  value: res.data?.stationType,
                },
                {
                  title: "小区名称",
                  value: res.data?.areaName,
                },
                {
                  title: "投资法人实体（资产归属法人实体）",
                  value: res.data?.investLegalEntity,
                },
                {
                  title: "场站地址",
                  value: res.data?.stationLocation,
                },
                {
                  title: "投建批次",
                  value: res.data?.constructionBatch,
                },
                //   {
                //     title: "投资策略",
                //     value: res.data?.stationNo,
                //   },
                {
                  title: "居间商",
                  value: res.data?.middleBusiness,
                },
                {
                  title: "归属大区",
                  value: res.data?.orgName,
                },
                {
                  title: "业绩归属部门",
                  value: res.data?.performanceDepartment,
                },
                {
                  title: "部门总监",
                  value: res.data?.departmentDirector,
                },
                {
                  title: "城市总监",
                  value: res.data?.cityDirector,
                },
                // {
                //   title: "工程总监",
                //   value: res.data?.engineeringManagerName,
                // },
                {
                  title: "战区工程",
                  value: res.data?.areaDirector,
                },
                {
                  title: "商务人员",
                  value: res.data?.businessDevelopmentName,
                },
                {
                  title: "是否报电",
                  value: res.data?.isReportPower,
                },
                {
                  title: "场地方公司名称",
                  value: res.data?.siteCompanyName,
                },
                {
                  title: "场地负责人",
                  value: res.data?.siteLeader,
                },
                {
                  title: "负责人联系方式",
                  value: res.data?.siteLeaderTel,
                },
                // {
                //   title: "工程踏勘人员",
                //   value: res.data?.engineeringSurveyor,
                // },
                {
                  title: "主缆预计长度（米）",
                  value: res.data?.mainCableExpectLength,
                },
                {
                  title: "场地属性",
                  value: res.data?.siteAttribute,
                },
                {
                  title: "主缆实际长度（米）",
                  value: res.data?.mainCableRealLength,
                },
                {
                  title: "主缆材质",
                  value: res.data?.mainCableMaterial,
                },
                {
                  title: "施工预计总价（元）",
                  value: res.data?.consExpectTotalPrice,
                },
                {
                  title: "预计安装充电桩数（个）",
                  value: res.data?.totalPileCount,
                },
                {
                  title: "施工实际总价（元）",
                  value: res.data?.consRealTotalPrice,
                },
                {
                  title: "实际安装充电桩数（个）",
                  value: res.data?.realPileCount,
                },
                {
                  title: "单桩施工预计成本（元）",
                  value: res.data?.pileExpectCost,
                },
                {
                  title: "总功率（kW）",
                  value: res.data?.totalPower,
                },
                {
                  title: "单桩施工实际成本（元）",
                  value: res.data?.pileRealCost,
                },
                {
                  title: "实际总功率（kW）",
                  value: res.data?.realTotalPower,
                },
                {
                  title: "总商务成本",
                  value: res.data?.totalBusinessCost,
                },
                {
                  title: "施工发包模式",
                  value: res.data?.constructionContractMode,
                },
                {
                  title: "单桩商务成本",
                  value: res.data?.pileBusinessCost,
                },
                {
                  title: "施工单位名称",
                  value: res.data?.consTeamName,
                },
                {
                  title: "设备总成本",
                  value: res.data?.deviceTotalCost,
                },
                {
                  title: "施工单位供应商编码",
                  value: res.data?.consTeamCode,
                },
                {
                  title: "交流桩个数（个）",
                  value: res.data?.alternatingPileCount,
                },
                {
                  title: "直流桩个数（个）",
                  value: res.data?.directPileCount,
                },
                {
                  title: "交流枪个数（个）",
                  value: res.data?.alternatingGunCount,
                },
                {
                  title: "直流枪个数（个）",
                  value: res.data?.directGunCount,
                },
                {
                  title: "踏勘单是否作废",
                  value: res.data?.surveyFormIsCancel,
                },
                {
                  title: "预计度电服务费（元）",
                  value: res.data?.expectElectricityFee,
                },
                {
                  title: "质保到期时间",
                  value: res.data?.warrantyExpiresTime,
                },
                {
                  title: "是否大客户项目",
                  value: res.data?.ifVipProject,
                },
                {
                  title: "",
                  value: "",
                },
              ];
          this.relateList = this.isEnergyStorage
            ? [
                {
                  title: "该项目踏勘单号",
                  value:
                    res.data?.surveyCodeSub?.split(",") ||
                    res.data?.surveyCodeMain?.split(",") ||
                    [],
                  slotName: "links",
                  refName: "exploreInfo",
                },
                {
                  title: "踏勘单提交时间",
                  value: res.data?.surveySubmitTime,
                },
                {
                  title: "投建协议申请单号",
                  value: res.data?.consAgreementApplyNo?.split(",") || [],
                  params: {
                    businessId:
                      res.data?.surveyCodeSub || res.data?.surveyCodeMain,
                  },
                  // method: () => {
                  //   this.$refs.agreementInfo.open();
                  // },
                  slotName: "links",
                  refName: "agreementInfo",
                },
                {
                  title: "投建协议申请时间",
                  value: res.data?.consAgreementApplyTime,
                },
                {
                  title: "采购申请单号",
                  value: res.data?.deviceApplyNo?.split(",") || [],
                  slotName: "links",
                  refName: "purchaseOrder",
                  params: { assetConType: "02" },
                },
                {
                  title: "采购发包申请通过时间",
                  value: res.data?.deviceOrderPassTime,
                },
                {
                  title: "采购框架协议申请单号",
                  value: res.data?.purchaseAgreementApplyNo?.split(",") || [],
                  slotName: "links",
                  refName: "purchaseAgreement",
                },
                {
                  title: "采购框架协议申请通过时间",
                  value: res.data?.purchaseAgreementApplyPassTime,
                },
                {
                  title: "上线单号",
                  value: res.data?.onlineNo,
                  // method: () => {
                  //   this.$refs.onlineOrder.open();
                  // },
                },
                {
                  title: "上线通过时间",
                  value: res.data?.onlineTime,
                },
                {
                  title: "付款单号",
                  value: res.data?.paymentNo?.split(",") || [],
                  // method: () => {
                  //   this.$refs.payOrder.open();
                  // },
                  slotName: "links",
                  refName: "payOrder",
                },
                {
                  title: "付款单审批通过时间",
                  value: res.data?.paymentApprovePassTime,
                },
              ]
            : [
                {
                  title: "该项目踏勘单号",
                  value:
                    res.data?.surveyCodeSub?.split(",") ||
                    res.data?.surveyCodeMain?.split(",") ||
                    [],
                  slotName: "links",
                  refName: "exploreInfo",
                },
                // {
                //   title: "踏勘子编码",
                //   value: res.data?.surveyCodeSub,
                //   // slotName: "links",
                //   // refName: "exploreInfo",
                //   // method: () => {
                //   //   this.$refs.exploreInfo.open();
                //   // },
                // },
                {
                  title: "踏勘单提交时间",
                  value: res.data?.surveySubmitTime,
                },
                {
                  title: "该项目关联的踏勘主编码",
                  value: res.data?.surveyCodeMain?.split(",") || [],
                  slotName: "links",
                  refName: "exploreInfo",
                  isCustomClick: true,
                  customClick: (row, val) => {
                    if (val.startsWith("GCTS-")) {
                      this.$refs.exploreInfo?.open({
                        ...row.params,
                        applyNo: val,
                        isEnergyStorage: this.isEnergyStorage,
                      });
                    } else {
                      //跳转到历史项目
                      this.$router.push({
                        name: "projectManage",
                        params: {
                          stationCode: res.data?.stationCode,
                          stationName: res.data?.stationName,
                        },
                      });
                    }
                  },
                },
                {
                  title: "主踏勘单提交时间",
                  value: res.data?.mainSurveySubmitTime,
                },
                {
                  title: "投建协议申请单号",
                  value: res.data?.consAgreementApplyNo?.split(",") || [],
                  params: {
                    businessId:
                      res.data?.surveyCodeSub || res.data?.surveyCodeMain,
                  },
                  // method: () => {
                  //   this.$refs.agreementInfo.open();
                  // },
                  slotName: "links",
                  refName: "agreementInfo",
                },
                {
                  title: "投建协议申请时间",
                  value: res.data?.consAgreementApplyTime,
                },
                {
                  title: "供应商采购申请单号",
                  value: res.data?.purchaseApplyNo?.split(",") || [],
                  slotName: "links",
                  refName: "purchaseOrder",
                  params: { assetConType: "01" },
                },
                {
                  title: "供应商采购发包申请通过时间",
                  value: res.data?.purchaseApplyPassTime,
                },
                {
                  title: "设备采购申请单号",
                  value: res.data?.deviceApplyNo?.split(",") || [],
                  slotName: "links",
                  refName: "purchaseOrder",
                  params: { assetConType: "02" },
                },
                {
                  title: "设备采购发包申请通过时间",
                  value: res.data?.deviceOrderPassTime,
                },
                {
                  title: "采购框架协议申请单号",
                  value: res.data?.purchaseAgreementApplyNo?.split(",") || [],
                  slotName: "links",
                  refName: "purchaseAgreement",
                },
                {
                  title: "采购框架协议申请通过时间",
                  value: res.data?.purchaseAgreementApplyPassTime,
                },
                {
                  title: "上线单号",
                  value: res.data?.onlineNo,
                  // method: () => {
                  //   this.$refs.onlineOrder.open();
                  // },
                },
                {
                  title: "上线通过时间",
                  value: res.data?.onlineTime,
                },
                {
                  title: "付款单号",
                  value: res.data?.paymentNo?.split(",") || [],
                  // method: () => {
                  //   this.$refs.payOrder.open();
                  // },
                  slotName: "links",
                  refName: "payOrder",
                },
                {
                  title: "付款单审批通过时间",
                  value: res.data?.paymentApprovePassTime,
                },
              ];
          this.timeList = [
            {
              title: "踏勘审批发起时间",
              value: res.data?.surveySubmitTime,
            },
            {
              title: "踏勘审批通过时间",
              value: res.data?.surveyPassTime,
            },
            {
              title: "投建协议审批发起时间",
              value: res.data?.consAgreementApplyTime,
            },
            {
              title: "投建协议审批通过时间",
              value: res.data?.consAgreementPassTime,
            },
            {
              title: "投建协议已盖章时间",
              value: res.data?.consAgreementStampTime,
            },
            {
              title: "供应商下单通知",
              value: res.data?.supplierOrderNotifyTime,
            },
            {
              title: "供应商采购下单审批通过",
              value: res.data?.supplierOrderPassTime,
            },
            {
              title: "指定施工队时间",
              value: res.data?.designateConsTeamTime,
            },
            {
              title: "开工申请时间",
              value: res.data?.workApplyTime,
            },
            {
              title: "采购下单审批通过时间",
              value: res.data?.purchaseOrderPassTime,
            },
            {
              title: "开工申请确认时间",
              value: res.data?.workApplyConfirmTime,
            },
            {
              title: "开工申请通过时间",
              value: res.data?.workApplyPassTime,
            },
            {
              title: "施工验收通过时间",
              value: res.data?.consCheckPassTime,
            },
            {
              title: "施工完成时间",
              value: res.data?.consCompleteTime,
            },
            {
              title: "竣工报告审核通过时间",
              value: res.data?.completionReportPassTime,
            },
            {
              title: "上传竣工报告时间",
              value: res.data?.completionReportUploadTime,
            },
            {
              title: "质保到期时间",
              value: res.data?.warrantyExpiresTime,
              hidden: !this.isEnergyStorage,
            },
            {
              title: "上线时间",
              value: res.data?.onlineTime,
            },
            {
              title: "",
              value: "",
              hidden: this.isEnergyStorage,
            },
          ];
        });
    },
    getTagType(projectType) {
      const arr = [
        { type: "success", status: "新建" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
        { type: "warning", status: "增柜" },
        { type: "danger", status: "撤柜" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
  },
  computed: {
    modalConfig() {
      const form = {
        check: {
          modalTitle: "工程审核结果",
          formConfig: [
            {
              field: "type",
              element: "el-radio-group",
              title: "确定工序项",
              props: {
                options: [
                  { label: "新建项目（12项工序）", value: "1" },
                  { label: "换桩项目（2项工序）", value: "2" },
                ],
              },
              rules: [{ required: !this.isEnergyStorage, message: "请选择" }],
              on: {
                change: (val) => {
                  this.$refs.formModal.setFormFields({ templateList: [] });
                  this.getWorkType(val);
                },
              },
              show: !this.isEnergyStorage,
            },
            {
              field: "templateList",
              element: "el-select",
              title: "选择工序项",
              props: {
                options: this.workTypeOptions,
                optionLabel: "workTemplateName",
                optionValue: "workTemplateId",
                multiple: true,
              },
              defaultValue: [],
              rules: [{ required: true, message: "请选择" }],
            },
            {
              field: "deviceDemandList",
              element: "slot",
              slotName: "deviceDemandList",
              title: "储能设备需求清单",
              defaultValue: [],
              show: this.isEnergyStorage,
              rules: [{ required: this.isEnergyStorage, message: "请选择" }],
            },
            {
              field: "projectAttribute",
              element: "el-select",
              title: "项目属性",
              props: {
                options: this.projectAttributeOptions,
                optionValue: "dictValue",
                optionLabel: "dictLabel",
              },
              show: this.isEnergyStorage,
              defaultValue: "1",
            },
            {
              field: "whetherNeedRoadItem",
              element: "el-radio-group",
              title: "是否需要路书",
              props: {
                options: [
                  { label: "需要", value: "0" },
                  { label: "不需要", value: "1" },
                ],
              },
              defaultValue: "1",
              show: !this.isEnergyStorage,
            },
            {
              field: "auditReason",
              element: "el-input",
              title: "审核不通过原因",
              props: {
                type: "textarea",
              },
              attrs: {
                rows: 5,
                maxlength: 500,
                showWordLimit: true,
                placeholder: "500个字符以内",
              },
            },
          ],
        },
      };
      return form[this.operationType];
    },
  },
};
</script>

<style scoped lang="less">
.step-card /deep/ .el-steps {
  justify-content: center;
}

.checkFlex {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  border: 1px dotted green;
  margin: 0px 20px 20px 20px;
}
</style>
