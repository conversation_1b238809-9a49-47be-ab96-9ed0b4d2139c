//项目详情
<template>
  <div class="app-container">
    <h3>{{ stationName }}</h3>
    <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="item in filterTabList"
        :key="item.name"
      >
        <component
          :ref="item.name"
          :is="item.component"
          :projectBatchId="projectBatchId"
          :projectCode="projectCode"
          :isEnergyStorage="isEnergyStorage"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission";
import BaseInfo from "./detailComponents/base";
import ConstructionInfo from "./detailComponents/construction.vue";
import EngineerInfo from "./detailComponents/engineer.vue";
import DeviceInfo from "./detailComponents/device.vue";
import ApplyInfo from "./detailComponents/apply.vue";
import PayInfo from "./detailComponents/pay.vue";
import RecordInfo from "./detailComponents/record.vue";
export default {
  components: {
    BaseInfo,
    ConstructionInfo,
    EngineerInfo,
    DeviceInfo,
    ApplyInfo,
    PayInfo,
    RecordInfo,
  },
  data() {
    return {
      tabList: [
        {
          label: "基本信息",
          name: "base",
          component: "BaseInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:base"]);
          },
        },
        {
          label: "工程信息",
          name: "engineer",
          component: "EngineerInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:engineer"]);
          },
        },
        {
          label: "设备信息",
          name: "device",
          component: "DeviceInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:device"]);
          },
        },
        {
          label: "开工申请",
          name: "apply",
          component: "ApplyInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:apply"]);
          },
        },
        {
          label: "施工信息",
          name: "construction",
          component: "ConstructionInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:construction"]);
          },
        },
        {
          label: "付款信息",
          name: "pay",
          component: "PayInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:pay"]);
          },
        },
        {
          label: "操作记录",
          name: "record",
          component: "RecordInfo",
          show: () => {
            return checkPermission(["investBatchList:tab:record"]);
          },
        },
      ],
      projectCode: "",
      projectBatchId: "",
      activeTab: "",
      stationName: "",
      isEnergyStorage: false,
    };
  },
  computed: {
    filterTabList() {
      return this.tabList.filter((x) => x.show());
    },
  },
  created() {
    this.projectBatchId = this.$route.query.projectBatchId;
    this.projectCode = this.$route.query.projectCode;
    this.stationName = this.$route.query.stationName;
    this.isEnergyStorage = JSON.parse(this.$route.query.isEnergyStorage);
    this.activeTab = this.filterTabList[0]?.name;
  },
  methods: {
    handleTabClick() {
      this.$refs[this.activeTab][0]?.getDetail();
    },
  },
};
</script>

<style></style>
