<!-- 验收结果 -->
<template>
  <div class="checkRes">
    <LineTitle title="验收结果" fontSize="18" />

    <el-form
      ref="form"
      :model="form"
      label-position="left"
      label-width="auto"
      :rules="rules"
    >
      <el-form-item label="审核结果" prop="auditResult">
        <el-radio-group v-model="form.auditResult">
          <el-radio :label="'1'">通过</el-radio>
          <el-radio :label="'2'">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="不通过原因"
        prop="auditReason"
        :rules="[
          {
            required: form.auditResult == '2',
            message: '请输入不通过原因',
            trigger: 'blur',
          },
        ]"
      >
        <el-input
          type="textarea"
          v-model="form.auditReason"
          placeholder="请输入"
          rows="6"
          :maxlength="1000"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="建设单位工程经理" prop="constructionManager">
        <el-input
          v-model="form.constructionManager"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="建设单位检查结论"
        prop="constructionCheckContent"
        :rules="[
          {
            required: form.auditResult == '1',
            message: '请输入建设单位检查结论',
            trigger: ['blur', 'change'],
          },
        ]"
      >
        <el-input
          type="textarea"
          v-model="form.constructionCheckContent"
          placeholder="请输入"
          rows="6"
          :maxlength="1000"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import { mapGetters } from "vuex";
export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    data: {
      immediate: true,
      handler(v) {
        this.setFormValue({
          ...v,
          constructionManager: v.constructionManager || this.nickName,
        });
      },
    },
  },
  components: {
    LineTitle,
  },
  computed: {
    ...mapGetters(["userId", "nickName"]),
  },
  data() {
    return {
      form: {
        constructionManager: null,
        constructionCheckContent: null,
        auditReason: null,
        auditResult: null,
      },
      rules: {
        auditResult: [
          {
            required: true,
            message: "请选择审核结果",
            trigger: "blur",
          },
        ],
        constructionManager: [
          {
            required: true,
            message: "请输入建设单位工程经理",
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    this.form.constructionManager = this.nickName;
  },
  methods: {
    async setFormValue(data) {
      Object.assign(this.form, data);
    },
    // @returns [boolean, formValue]
    async getFormValue() {
      let valid = await this.$refs.form.validate();
      return [valid, this.form];
    },
    resetForm() {
      this.$nextTick(() => {
        this.$refs.form?.resetFields();
        this.form.constructionManager = this.nickName;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.checkRes {
  width: 60%;
}
</style>
