// 施工验收详情配置

import Description from "@/components/DataDisplay/Description.vue";
import Table from "@/components/DataDisplay/Table.vue";
import ImgList from "@/components/DataDisplay/ImgList.vue";
import ImgListGroup from "@/components/DataDisplay/ImgListGroup.vue";
import { constructCheck, buildCheck, pileType, checkOptions } from "./constant";
import { cloneDeep } from "lodash-es";
import { Message } from "element-ui";

// 施工验收项-结果、原因
const editItems = [
  {
    label: "验收结果",
    value: "buildCheck",
    html: true,
    format: (v) => {
      const check = buildCheck.find((i) => i.value == v);
      const label = check?.label || "";
      const color = check?.color;
      return `<div style="color:${color}">${label}</div>`;
    },
  },
  {
    label: "验收不通过原因",
    value: "buildReason",
    html: true,
    props: {
      width: "300px",
    },
    format: (v, _k, _d, form) => {
      const check = buildCheck.find((i) => i.value == form.buildCheck);
      const color = check?.color;
      return `<div style="color:${color};white-space:pre-line">${v ||
        ""}</div>`;
    },
    // props: {
    //   style: {
    //     display: "-webkit-box",
    //     "-webkit-line-clamp": 2,
    //     "line-height": 1.2,
    //     "max-height": "calc(1.2em * 2)",
    //     overflow: "hidden",
    //     "text-overflow": "ellipsis",
    //     "-webkit-box-orient": "vertical",
    //   },
    // },
  },
];
// 检查结论
const conclusionConfig = {
  component: Description,
  title: "检查结论",
  attrs: {
    column: 1,
  },
  config: [
    { label: "施工单位检查结论", value: "constructionAcceptConclusion" },
    { label: "施工单位项目经理", value: "constructionProjectManager" },
    { label: "建设单位验收结论", value: "constructionCheckContent" },
    { label: "建设单位项目经理", value: "constructionManager" },
  ],
};
// 审核结果
const resultConfig = {
  component: Description,
  title: "审核结果",
  attrs: {
    column: 1,
  },
  config: [
    {
      label: "审核结果",
      value: "auditResult",
      html: true,
      format: (val) => {
        return checkOptions[val];
      },
    },
    { label: "不通过原因", value: "auditReason" },
  ],
};
// 施工验收项-公共配置
const workCheckConfigs = [
  {
    ...conclusionConfig,
    dataName: "projectWork",
  },
  {
    ...resultConfig,
    dataName: "projectWork",
  },
  {
    component: Table,
    title: "检查项明细",
    desc: true,
    dataName: "projectWorkChecks",
    config: [
      { label: "检查项目", value: "checkName", width: 100 },
      { label: "规范要求", value: "standard", width: 220 },
      {
        label: "施工方检查结果",
        value: "constructCheck",
        format: (val) => constructCheck[val],
        width: 120,
      },
      {
        label: "施工方上传图片",
        value: "pictureUrl",
        element: "el-image",
        width: 180,
      },
      ...editItems,
    ],
    // @returns [true] 表示校验通过，[false, {message, index, field}] 表示校验不通过
    validate: async (tableData, formValue) => {
      for (let k = 0; k < tableData.length; k++) {
        if (formValue?.buildCheck?.[k] == 2 && !formValue?.buildReason?.[k]) {
          const errMsg = "请填写验收不通过原因";
          Message.error(errMsg);
          return [
            false,
            {
              message: errMsg,
              index: k,
              field: "buildReason",
            },
          ];
        }
        // else if (!formValue?.buildCheck?.[k]) {
        //   const errMsg = "请选择验收结果";
        //   Message.error(errMsg);
        //   return [
        //     false,
        //     {
        //       message: errMsg,
        //       index: k,
        //       field: "buildCheck",
        //     },
        //   ];
        // }
      }
      return [true];
    },
  },
];
// 电缆施工验收
const workCheckConfigs2 = [
  {
    ...conclusionConfig,
    dataName: "projectWork",
  },
  {
    ...resultConfig,
    dataName: "projectWork",
  },
  {
    component: Table,
    title: "检查项明细",
    desc: true,
    dataName: "projectWorkChecks",
    config: [
      { label: "类别", value: "checkParentName", width: 100 },
      { label: "检查项目", value: "checkName", width: 100 },
      { label: "规范要求", value: "standard", width: 220 },
      {
        label: "施工方检查结果",
        value: "constructCheck",
        format: (val) => constructCheck[val],
        width: 120,
      },
      {
        label: "施工方上传图片",
        value: "pictureUrl",
        element: "el-image",
        width: 180,
      },
      ...editItems,
    ],
    // @returns [true] 表示校验通过，[false, {message, index, field}] 表示校验不通过
    validate: async (tableData, formValue) => {
      for (let k = 0; k < tableData.length; k++) {
        if (formValue?.buildCheck?.[k] == 2 && !formValue?.buildReason?.[k]) {
          const errMsg = "请填写验收不通过原因";
          Message.error(errMsg);
          return [
            false,
            {
              message: errMsg,
              index: k,
              field: "buildReason",
            },
          ];
        }
        // else if (!formValue?.buildCheck?.[k]) {
        //   const errMsg = "请选择验收结果";
        //   Message.error(errMsg);
        //   return [
        //     false,
        //     {
        //       message: errMsg,
        //       index: k,
        //       field: "buildCheck",
        //     },
        //   ];
        // }
      }
      return [true];
    },
  },
];
// 接地施工验收附表-接地电阻测试
const workCheckConfig2 = [
  {
    component: Description,
    title: "附表检查结论",
    dataName: "projectWorkResistances",
    attrs: {
      column: 1,
    },
    config: [
      { label: "施工单位检查结论", value: "constructionAcceptConclusion" },
      { label: "施工单位项目经理", value: "constructionProjectManager" },
      { label: "仪表型号", value: "meterType" },
      { label: "计量单位", value: "meterUnit" },
      { label: "测试日期", value: "testTime" },
      { label: "天气情况", value: "weather" },
      { label: "气温", value: "temp" },
    ],
  },
  {
    component: Table,
    title: "检查项明细",
    dataName: "projectWorkResistances",
    config: [
      { label: "测试位置", value: "testPosition" },
      { label: "接地阻值", value: "resistanceValue" },
      { label: "备注", value: "pictureUrl", element: "el-image" },
    ],
  },
];
// 电缆头制作、接线及送电调试验收附表-电缆绝缘测试记录表
const workCheckConfig3 = [
  {
    component: Description,
    title: "附表检查结论",
    dataName: "projectWorkCables",
    attrs: {
      column: 1,
    },
    config: [
      { label: "施工单位检查结论", value: "constructionAcceptConclusion" },
      { label: "施工单位项目经理", value: "constructionProjectManager" },
      { label: "仪表型号", value: "meterType" },
      { label: "计量单位", value: "meterUnit" },
      { label: "测试日期", value: "testTime" },
      { label: "天气情况", value: "weather" },
      { label: "气温", value: "temp" },
      { label: "电压", value: "voltage" },
    ],
  },
  {
    component: Table,
    title: "检查项明细",
    dataName: "projectWorkCables",
    config: [
      {
        label: "实验内容（层数、路别、名称、编号）",
        value: "branch",
        format: (v) => `支路${v}`,
      },
      {
        label: "相间MΩ",
        value: "xjMΩ",
        children: [
          {
            label: "L1-L2",
            value: "l1L2",
          },
          {
            label: "L2-L3",
            value: "l2L3",
          },
          {
            label: "L3-L1",
            value: "l3L1",
          },
        ],
      },
      {
        label: "相对零MΩ",
        value: "xdlMΩ",
        children: [
          {
            label: "L1-N",
            value: "l1N",
          },
          {
            label: "L2-N",
            value: "l2N",
          },
          {
            label: "L3-N",
            value: "l3N",
          },
        ],
      },
      {
        label: "相对地MΩ",
        value: "xddMΩ",
        children: [
          {
            label: "L1-PE",
            value: "l1Pe",
          },
          {
            label: "L2-PE",
            value: "l2Pe",
          },
          {
            label: "L3-PE",
            value: "l3Pe",
          },
        ],
      },
      {
        label: "零对地MΩ",
        value: "lddMΩ",
        children: [
          {
            label: "N-PE",
            value: "nPe",
          },
        ],
      },
    ],
  },
];

// 竣工图
const workCheckConfig4 = [
  {
    ...conclusionConfig,
    dataName: "",
  },
  {
    ...resultConfig,
    dataName: "",
  },
  {
    component: ImgList,
    dataName: "completedPicture",
    title: "竣工图",
    attrs: {},
  },
];

//资产验收单
const workCheckConfig5 = [
  {
    ...conclusionConfig,
    dataName: "fixedFormAudit",
  },
  {
    ...resultConfig,
    dataName: "fixedFormAudit",
  },
  {
    component: Description,
    title: "资产验收单",
    dataName: "",
    attrs: {
      column: 1,
    },
    config: [
      { label: "场站整体图片", value: "stationFiles", element: "el-image" },
    ],
  },
  {
    component: Table,
    title: "",
    desc: true,
    dataName: "tabTableData",
    showPagination: true,
    config0: [
      { label: "序号", value: "sortNo", width: 60 },
      { label: "设备编码", value: "pileNo" },
      {
        label: "设备类型",
        value: "subType",
        format: (val) => pileType[val],
      },
      { label: "功率（kW)", value: "ratePower" },
      { label: "数量", value: "total" },
      {
        label: "安装方式",
        value: "installMode",
        format: (v, k, dicts) => {
          return dicts.installModeList.find((i) => i.dictValue === v)
            ?.dictLabel;
        },
      },
      { label: "立桩数量", value: "pilesErectedCount" },
      { label: "图片", value: "attachments", element: "el-image", width: 180 },
    ],
    config1: [
      { label: "序号", value: "sortNo", width: 60 },
      { label: "设备编码", value: "pileNo" },
      {
        label: "设备类型",
        value: "subType",
        format: (val) => pileType[val],
      },
      { label: "功率（kW)", value: "ratePower" },
      { label: "图片", value: "attachments", element: "el-image", width: 180 },
    ],
  },
];
//资产验收单-储能
const workCheckConfig5_2 = [
  {
    ...conclusionConfig,
    dataName: "fixedFormAudit",
  },
  {
    ...resultConfig,
    dataName: "fixedFormAudit",
  },
  {
    component: Description,
    title: "资产验收单",
    dataName: "",
    attrs: {
      column: 1,
    },
    config: [
      { label: "场站整体图片", value: "stationFiles", element: "el-image" },
    ],
  },
  {
    component: Table,
    title: "",
    desc: true,
    dataName: "tabTableData",
    showPagination: true,
    config: [
      { label: "序号", value: "sortNo", width: 60 },
      {
        label: "设备名称",
        value: "deviceName",
      },
      {
        label: "设备编码",
        value: "pileNo",
      },
      {
        label: "规格型号",
        value: "materialModel",
      },
      {
        label: "单位",
        value: "unitName",
      },
      {
        label: "数量",
        value: "total",
      },
      {
        label: "生产厂家",
        value: "factory",
      },
      {
        label: "图片",
        value: "attachments",
        element: "el-image",
        width: 180,
      },
    ],
  },
];

// 决算
const workCheckConfig6 = [
  {
    ...conclusionConfig,
    dataName: "projectFixedFormAudit",
  },
  {
    ...resultConfig,
    dataName: "projectFixedFormAudit",
  },
  {
    component: Description,
    title: "检查结论",
    dataName: "",
    attrs: {
      column: 1,
    },
    config: [
      { label: "主缆实际长度（米）", value: "mainCableRealLength" },
      { label: "单桩施工实际成本（元）", value: "pileRealCost" },
      { label: "施工实际总价（元）", value: "consRealTotalPrice" },
    ],
  },
];
// 决算-储能
const workCheckConfig6_2 = [
  {
    ...conclusionConfig,
    dataName: "projectFixedFormAudit",
  },
  {
    ...resultConfig,
    dataName: "projectFixedFormAudit",
  },
  {
    component: Description,
    title: "检查结论",
    dataName: "",
    attrs: {
      column: 1,
    },
    config: [
      { label: "主缆实际长度（米）", value: "mainCableRealLength" },
      { label: "施工实际总价（元）", value: "consRealTotalPrice" },
    ],
  },
];
// 变更申请
const workCheckConfig7 = [
  {
    ...conclusionConfig,
    dataName: "fixedFormAudit",
  },
  {
    ...resultConfig,
    dataName: "fixedFormAudit",
  },
  {
    component: Table,
    title: "检查结论",
    desc: false,
    dataName: "changeRequestList",
    config: [
      { label: "序号", value: "sortNo", width: 60 },
      {
        label: "变更类型",
        value: "changeType",
        format: (v, k, dicts) => {
          return (
            dicts.changeTypeList.find((i) => i.dictValue === v)?.dictLabel || v
          );
        },
        width: 150,
      },
      {
        label: "变更前",
        value: "beforeChange",
      },
      { label: "变更后", value: "afterChange" },
    ],
  },
];
//路书
const workCheckConfig8 = [
  {
    ...conclusionConfig,
    dataName: "",
  },
  {
    ...resultConfig,
    dataName: "",
  },
  {
    component: ImgList,
    dataName: "doorSiteGuide",
    title: "路口指引",
    attrs: {},
  },
  {
    component: ImgListGroup,
    dataName: "",
    title: "站点图片",
    attrs: {},
    config: [
      { title: "横屏侧面照片", value: "landScapeProfileSide" },
      { title: "横屏正面照片", value: "landScapeFront" },
    ],
  },
  {
    component: ImgList,
    dataName: "parkingChargesStandard",
    title: "停车收费标准照片",
    attrs: {},
  },
  {
    component: Description,
    dataName: "",
    title: "备注",
    config: [{ label: "备注信息", value: "remark" }],
    attrs: {
      column: 1,
    },
  },
];
//电缆、并网箱进场验收单
const workCheckConfigs9 = [
  {
    ...conclusionConfig,
    dataName: "fixedFormAudit",
  },
  {
    ...resultConfig,
    dataName: "fixedFormAudit",
  },
  {
    component: Table,
    title: "到货签收信息",
    desc: true,
    dataName: "cmProjectAcceptances",
    showPagination: true,
    config: [
      { label: "序号", value: "sortNo", width: 100 },
      {
        label: "设备名称",
        value: "deviceName",
        width: 200,
      },
      {
        label: "设备编码",
        value: "deviceNo",
      },
      {
        label: "规格型号",
        value: "model",
        width: 200,
      },
      {
        label: "单位",
        value: "unit",
      },
      {
        label: "数量",
        value: "num",
      },
      {
        label: "生产厂家",
        value: "factory",
        width: 200,
      },
      {
        label: "合格证",
        value: "certificateList",
        element: "el-image",
        width: 200,
      },
      {
        label: "进场日期",
        value: "approachDate",
        width: 120,
      },
      {
        label: "外观质量",
        value: "quality",
      },
      {
        label: "是否同意使用",
        value: "agreeUsed",
      },
    ],
  },
];

// 电气接地电阻测试记录
const workCheckConfig10 = [
  {
    component: Description,
    title: "检查结论",
    dataName: "projectWorkResistances",
    attrs: {
      column: 1,
    },
    config: [
      { label: "施工单位检查结论", value: "constructionAcceptConclusion" },
      { label: "施工单位项目经理", value: "constructionProjectManager" },
      { label: "仪表型号", value: "meterType" },
      { label: "计量单位", value: "meterUnit" },
      { label: "测试日期", value: "testTime" },
      { label: "天气情况", value: "weather" },
      { label: "气温", value: "temp" },
    ],
  },
  {
    ...resultConfig,
    dataName: "fixedFormAudit",
  },
  {
    component: Table,
    title: "检查项明细",
    dataName: "projectWorkResistances",
    config: [
      { label: "测试位置", value: "testPosition" },
      { label: "接地阻值", value: "resistanceValue" },
      { label: "备注", value: "pictureUrl", element: "el-image" },
    ],
  },
];
// 电气绝缘电阻测试记录
const workCheckConfig11 = [
  {
    component: Description,
    title: "检查结论",
    dataName: "projectWorkCables",
    attrs: {
      column: 1,
    },
    config: [
      { label: "施工单位检查结论", value: "constructionAcceptConclusion" },
      { label: "施工单位项目经理", value: "constructionProjectManager" },
      { label: "仪表型号", value: "meterType" },
      { label: "计量单位", value: "meterUnit" },
      { label: "测试日期", value: "testTime" },
      { label: "天气情况", value: "weather" },
      { label: "气温", value: "temp" },
      { label: "电压", value: "voltage" },
    ],
  },
  {
    ...resultConfig,
    dataName: "fixedFormAudit",
  },
  {
    component: Table,
    title: "检查项明细",
    dataName: "projectWorkCables",
    config: [
      {
        label: "试验内容",
        value: "testContent",
      },
      {
        label: "回路",
        value: "branch",
        format: (v) => `回路${v}`,
      },
      // {
      //   label: "实验内容（层数、路别、名称、编号）",
      //   value: "synr",
      //   children: [
      //     {
      //       label: "",
      //       value: "testContent",
      //     },
      //     {
      //       label: "",
      //       value: "branch",
      //       format: (v) => `回路${v}`,
      //     },
      //   ],
      // },
      {
        label: "相间MΩ",
        value: "xjMΩ",
        children: [
          {
            label: "L1-L2",
            value: "l1L2",
          },
          {
            label: "L2-L3",
            value: "l2L3",
          },
          {
            label: "L3-L1",
            value: "l3L1",
          },
        ],
      },
      {
        label: "相对零MΩ",
        value: "xdlMΩ",
        children: [
          {
            label: "L1-N",
            value: "l1N",
          },
          {
            label: "L2-N",
            value: "l2N",
          },
          {
            label: "L3-N",
            value: "l3N",
          },
        ],
      },
      {
        label: "相对地MΩ",
        value: "xddMΩ",
        children: [
          {
            label: "L1-PE",
            value: "l1Pe",
          },
          {
            label: "L2-PE",
            value: "l2Pe",
          },
          {
            label: "L3-PE",
            value: "l3Pe",
          },
        ],
      },
      {
        label: "零对地MΩ",
        value: "lddMΩ",
        children: [
          {
            label: "N-PE",
            value: "nPe",
          },
        ],
      },
    ],
  },
];
// 施工验收tab对应配置
export const detailWorkCheckMap = function() {
  const _self = this;
  return {
    甲供设备进场验收: cloneDeep(workCheckConfigs),
    乙供材料验收: cloneDeep(workCheckConfigs),
    桥架施工验收: cloneDeep(workCheckConfigs),
    管沟开挖施工验收: cloneDeep(workCheckConfigs),
    基坑开挖施工验收: cloneDeep(workCheckConfigs),
    电缆施工验收: cloneDeep(workCheckConfigs2),
    接地施工验收: cloneDeep([...workCheckConfigs, ...workCheckConfig2]),
    "电缆头制作、接线及送电调试验收": cloneDeep([
      ...workCheckConfigs,
      ...workCheckConfig3,
    ]),
    工程防火封堵验收: cloneDeep(workCheckConfigs),
    基础浇筑施工验收: cloneDeep(workCheckConfigs),
    "充电桩、配电箱安装验收": cloneDeep(workCheckConfigs),
    电源点配电工程安装验收: cloneDeep(workCheckConfigs),

    // 固定
    竣工图: cloneDeep(workCheckConfig4),
    资产验收单: _self.isEnergyStorage
      ? cloneDeep(workCheckConfig5_2)
      : cloneDeep(workCheckConfig5),
    决算单: _self.isEnergyStorage
      ? cloneDeep(workCheckConfig6_2)
      : cloneDeep(workCheckConfig6),
    变更项: cloneDeep(workCheckConfig7),
    路书: cloneDeep(workCheckConfig8),

    //储能新增
    "电缆、并网箱进场验收单": cloneDeep(workCheckConfigs9),
    隐蔽工程验收记录表: cloneDeep(workCheckConfigs),
    电气接地电阻测试记录: cloneDeep([...workCheckConfig10]),
    电气绝缘电阻测试记录: cloneDeep([...workCheckConfig11]),
    分项工程验收表: cloneDeep(workCheckConfigs),
  };
};
