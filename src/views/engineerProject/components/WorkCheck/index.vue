<!-- 施工验收 -->
<template>
  <div class="workCheckContainer">
    <div class="work-check-title">
      <h3 v-if="edit">审核施工工序项</h3>
      <span class="work-check-total"
        >总计
        <span class="text-navy work-check-total-count">{{
          countObj.allWorkNum
        }}</span>
        项工序</span
      >
      <span
        ><svg-icon class-name="text-muted mr4" icon-class="stop" />未处理：<span
          class="text-navy"
          >{{ countObj.pendingWorkNum }}</span
        >
        项</span
      >
      <span
        ><i class="el-icon-warning-outline text-warning mr4" />待审核：<span
          class="text-navy"
          >{{ countObj.reviewedWorkNum }}</span
        >
        项</span
      >
      <span
        ><i class="el-icon-check text-navy mr4" />审核通过：<span
          class="text-navy"
          >{{ countObj.approveWorkNum }}</span
        >
        项</span
      >
      <span
        ><i class="el-icon-close text-danger mr4" />审核不通过：<span
          class="text-navy"
          >{{ countObj.failAuditNum }}</span
        >
        项</span
      >
      <el-button
        size="large"
        type="primary"
        @click="submit"
        :disabled="loading"
        v-if="edit"
        >全部终审</el-button
      >
      <el-button
        size="large"
        type="primary"
        @click="handleConstructAcceptance"
        :disabled="loading"
        v-else-if="showConsCheck"
        >施工验收</el-button
      >
    </div>

    <el-tabs v-model="currentWorkId" @tab-click="loadWorkCheckInfo" type="card">
      <el-tab-pane :name="i.workId" v-for="i in worklist" :key="i.workId">
        <span slot="label">
          {{ i.workName }}
          <!-- 审核通过 -->
          <i class="el-icon-check text-navy" v-if="i.auditResult == '1'" />
          <!-- 审核不通过：审核不通过状态且app侧未处理 -->
          <i
            class="el-icon-close text-danger"
            v-if="i.auditResult == '2' && i.flag != '0'"
          />
          <!-- 待处理：没有审核结果且状态为待处理 -->
          <svg-icon
            class-name="text-muted"
            icon-class="stop"
            v-if="!i.auditResult && i.workStatus == '1'"
          />
          <!-- 已处理（待审核）：没有审核结果且状态为待审核 / 审核不通过后app侧已处理 -->
          <i
            class="el-icon-warning-outline text-warning"
            v-if="
              (!i.auditResult && i.workStatus == '2') ||
                (i.auditResult == '2' && i.flag == '0')
            "
          />
        </span>
      </el-tab-pane>
    </el-tabs>

    <div
      v-for="item in dynamicConfig"
      :key="item.workName"
      class="workCheck"
      v-loading="loading"
    >
      <LineTitle
        :title="item.title"
        fontSize="18"
        v-if="item.title"
      ></LineTitle>

      <!-- 描述 -->
      <template v-if="item.desc">
        <!-- 固定验收\进场验收单 -->
        <div
          v-if="
            currentWorkId === '资产验收单' ||
              currentWorkCheck.workName === '电缆、并网箱进场验收单'
          "
        >
          <el-tabs
            v-model="activeName"
            @tab-click="handleQuery"
            v-if="currentWorkId === '资产验收单'"
          >
            <el-tab-pane label="本次安装设备" name="0"></el-tab-pane>
            <el-tab-pane label="未安装设备" name="1"></el-tab-pane>
          </el-tabs>
          <div
            style="float: right;margin-bottom: 10px;"
            v-if="item.showOperation"
          >
            <!-- 设备共计{{ tablePage.total }}个 -->
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="deleteAllFn"
              :disabled="!showOperation"
              >全部清空</el-button
            >
            <el-button
              type="text"
              icon="el-icon-document-copy"
              @click="handleImportAcceptance"
              :disabled="!showOperation"
              v-if="currentWorkId === '资产验收单' && isEnergyStorage"
              >导入进场验收单</el-button
            >
            <el-button
              type="text"
              icon="el-icon-upload"
              @click="handleImport"
              :disabled="!showOperation"
              >批量导入</el-button
            >
          </div>
        </div>
        <!-- 动态验收 -->
        <p v-else-if="!currentWorkCheck.formType">
          检查项目共{{ workCheckLength }}项
          {{ workCheckDesc }}
        </p>
      </template>

      <!-- 动态组件 -->
      <component
        :is="item.component"
        v-bind="{
          ...(item.attrs || {}),
          config: item.config,
          validate: item.validate,
          dicts: {
            changeTypeList,
            installModeList,
          },
        }"
        :data="item.dataName ? data[item.dataName] : data"
        ref="checkForm"
        @change="checkFormValChange"
        @edited="handleEdit"
        :isRowEdit="isRowEdit"
        :editRowId="commonOperateObj && commonOperateObj.id"
      >
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            @click="handleRowEdit(row)"
            :disabled="!showOperation"
            v-if="showEditBtn(row)"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            @click="handleRowSave(row)"
            v-if="!showEditBtn(row)"
          >
            保存
          </el-button>
          <el-button
            type="text"
            @click="handleCancelEdit(row)"
            v-if="!showEditBtn(row)"
          >
            取消
          </el-button>
          <el-button
            type="text"
            @click="deleteDeviceItem(row)"
            :disabled="!showOperation"
          >
            删除
          </el-button>
        </template>
      </component>
      <pagination
        v-show="item.showPagination"
        :total="tablePage.total"
        :page.sync="tablePage.currentPage"
        :limit.sync="tablePage.pageSize"
        :autoScroll="false"
        @pagination="loadDeviceData"
      />
    </div>

    <template v-if="edit">
      <el-divider></el-divider>
      <Result ref="resultForm" :data="checkResult" />

      <el-row type="flex" justify="center" :gutter="48">
        <el-button v-if="key === 0" size="large" type="ghost" @click="back"
          >取消</el-button
        >
        <el-button v-else size="large" type="primary" @click="back"
          >返回上一步</el-button
        >
        <el-button
          v-if="key !== worklist.length - 1"
          size="large"
          type="primary"
          @click="save(true)"
          v-loading="saveLoading"
          >审核并下一步</el-button
        >
        <template v-else>
          <el-button
            size="large"
            type="primary"
            @click="save(false)"
            v-loading="saveLoading"
            >审核</el-button
          >
          <!-- <el-button
            size="large"
            type="primary"
            @click="submit"
            :disabled="loading"
            >提交审核结果</el-button
          > -->
        </template>
      </el-row>
    </template>

    <WorkCheckDialog ref="dialogRef" :projectId="projectId" />
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入设备"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
    >
      <div slot="tip">
        <div>上传格式支持xlxs、xls文件，2G以内。</div>
        <div style="color:red">
          提示：如需导入图片，请使用 WPS 进行编辑上传！
        </div>
      </div>
    </BatchUpload>
  </div>
</template>

<script>
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import Result from "./Result.vue";
import {
  queryWorkCheck,
  queryAcceptanceDetail,
  queryProjectWorkResistance,
  queryProjectWorkCable,
  queryWorkCheckList,
  saveWorkCheck,
  queryAssetDetail,
  queryChangeDetail,
  queryCompleteDetail,
  queryBalanceDetail,
  saveFixedFormAuditInfo,
  queryItemCount,
  queryFixedItems,
  queryRoadDetail,
  queryAssetDeviceList,
  deleteDeviceItem,
  deleteAllDevice,
} from "@/api/engineerProject/investBatchList/workCheck";
import * as api from "@/api/engineerProject/investBatchList/workCheck";
import checkPermission from "@/utils/permission.js";
import { workCheckMap } from "./config";
import { detailWorkCheckMap } from "./detail.config";
import WorkCheckDialog from "./WorkCheckDialog.vue";
import BatchUpload from "@/components/BatchUpload/index.vue";
import deviceApi from "@/api/operationMaintenanceManage/materiel/index.js";
export default {
  components: { LineTitle, Result, WorkCheckDialog, BatchUpload },
  props: {
    projectId: {
      type: String,
      required: true,
      default: "",
    },
    edit: {
      // 区分详情和编辑
      type: Boolean,
      default: true,
    },
    workId: {
      type: String,
      default: "",
    },
    projectStatus: {
      type: String,
      default: "",
    },
    isEnergyStorage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      activeName: "0",
      loading: false,
      worklist: [],
      fixedDict: [
        {
          workName: "路书",
          workId: "路书",
          detailApi: queryRoadDetail,
          formType: "5",
          condition: (val) => {
            return val.whetherNeedRoadItem == "0";
          },
        },
        {
          workName: "竣工图",
          workId: "竣工图",
          detailApi: queryCompleteDetail,
          formType: "4",
          workStatus: "1",
          condition: (val) => {
            return true;
          },
        },
        {
          workName: "资产验收单",
          workId: "资产验收单",
          detailApi: queryAssetDetail,
          formType: "1",
          workStatus: "1",
          condition: (val) => {
            return true;
          },
        },
        {
          workName: "决算单",
          workId: "决算单",
          detailApi: queryBalanceDetail,
          formType: "3",
          workStatus: "1",
          condition: (val) => {
            return true;
          },
        },
        {
          workName: "变更项",
          workId: "变更项",
          detailApi: queryChangeDetail,
          formType: "2",
          condition: (val) => {
            return val.whetherChangeRequest == "0";
          },
        },
      ],
      currentWorkId: "",
      data: {},

      saveLoading: false,

      changeTypeList: [],
      installModeList: [],
      countObj: {},
      showOperation: true,
      deviceOptions: [],
      isRowEdit: [],
      editObj: {},
    };
  },
  computed: {
    currentConfig() {
      return this.edit
        ? workCheckMap.call(this)
        : detailWorkCheckMap.call(this);
    },
    currentWorkCheck() {
      // 当前验收
      return this.worklist.find((i) => i.workId === this.currentWorkId) || {};
    },
    key() {
      return this.worklist.findIndex((i) => i.workId === this.currentWorkId);
    },
    prevKey() {
      return this.key === 0 ? 0 : this.key - 1;
    },
    nextKey() {
      return this.key === this.worklist.length ? this.key : this.key + 1;
    },
    workCheckLength() {
      return this.data?.projectWorkChecks?.length || 0;
    },
    workCheckDesc() {
      let checks = this.data?.projectWorkChecks || [];
      let success = 0;
      let fail = 0;
      checks.forEach((i) => {
        if (i.buildCheck == "1") {
          success++;
        } else if (i.buildCheck == "2") {
          fail++;
        }
      });
      return `，合格${success}项，不合格${fail}项`;
    },
    isCommonWorkCheck() {
      // 判断当前是否是固定验收
      return (
        !!this.worklist.find((i) => i.workId === this.currentWorkId)
          ?.formType ||
        [
          "电缆、并网箱进场验收单",
          "电气接地电阻测试记录",
          "电气绝缘电阻测试记录",
        ].includes(this.currentWorkCheck.workName)
      );
    },
    checkResult() {
      // 审核结果
      switch (this.currentWorkId) {
        case "竣工图":
          return {
            ...this.data,
            fixedAuditId: this.data.completeId,
          };
        case "资产验收单":
          return {
            ...this.data.fixedFormAudit,
          };
        case "决算单":
          return {
            ...this.data.projectFixedFormAudit,
          };
        case "变更项":
          return {
            ...this.data.fixedFormAudit,
          };
        case "路书":
          return {
            ...this.data,
            fixedAuditId: this.data.roadItemId,
          };
        default:
          return this.data.projectWork || this.data.fixedFormAudit;
      }
    },
    dynamicConfig() {
      if (
        !this.isEnergyStorage &&
        this.currentWorkCheck.workName === "资产验收单"
      ) {
        return this.currentConfig["资产验收单"].map((item) => {
          if (item.dataName === "tabTableData") {
            item["config"] =
              this.activeName == "0" ? item.config0 : item.config1;
          }
          return item;
        });
      }
      return this.currentConfig[this.currentWorkCheck.workName];
    },
    showConsCheck() {
      //待验收/施工中
      const condition1 = ["7", "8"].includes(this.projectStatus);
      return (
        condition1 &&
        checkPermission(["engineerProject:investBatchList:constructAccept"])
      );
    },
    commonOperateObj() {
      const arr = [
        {
          deleteAllApi: "deleteAllDevice",
          deleteApi: "deleteDeviceItem",
          editApi: "updateDevice",
          condition: () => {
            return !this.isEnergyStorage && this.currentWorkId === "资产验收单";
          },
          uploadObj: {
            api: "/export/report/importFixedAssets",
            url:
              "/charging-maintenance-ui/static/充电业务资产验收单上传模板.xlsx",
            extraData: {
              projectBatchId: this.projectId,
              installFlag: this.activeName,
            },
          },
          id: "fixedAssetsId",
        },
        {
          //储能的资产验收
          deleteAllApi: "deleteAllDevice",
          deleteApi: "deleteDeviceItem",
          editApi: "updateDevice",
          condition: () => {
            return this.isEnergyStorage && this.currentWorkId === "资产验收单";
          },
          uploadObj: {
            api: "/export/report/importEnergyFixedAssets",
            url:
              "/charging-maintenance-ui/static/储能批量导入固定资产模板.xlsx",
            extraData: {
              projectBatchId: this.projectId,
              installFlag: this.activeName,
            },
          },
          id: "fixedAssetsId",
        },
        {
          // 进场验收单
          deleteAllApi: "deleteAllAcceptance",
          deleteApi: "deleteAcceptanceItem",
          editApi: "updateAcceptance",
          condition: () => {
            return this.currentWorkCheck.workName === "电缆、并网箱进场验收单";
          },
          uploadObj: {
            api: "/export/report/importAcceptance",
            url: "/charging-maintenance-ui/static/进场验收单导入模板.xlsx",
            extraData: {
              projectBatchId: this.projectId,
            },
          },
          id: "acceptanceId",
        },
      ];
      return arr.find((x) => !!x.condition());
    },
    uploadObj() {
      return this.commonOperateObj?.uploadObj || {};
    },
  },
  watch: {
    workId: {
      // immediate: true,
      handler() {
        this.loadWorkCheck();
      },
    },
    projectId: {
      // immediate: true,
      handler() {
        this.getItemCount();
        this.loadWorkCheck();
      },
    },
    showOperation(val) {
      console.log(val, "showoperation!!!");
    },
  },
  async created() {
    await this.getDeviceOptions();
  },
  mounted() {
    //文件类型字典
    this.getDicts("project_change_type").then((response) => {
      this.changeTypeList = response?.data;
    });
    this.getDicts("install_mode_type").then((response) => {
      this.installModeList = response?.data?.map((x) => {
        return { ...x, value: x.dictValue, label: x.dictLabel };
      });
    });
    this.getItemCount();
    this.loadWorkCheck();
  },
  methods: {
    showEditBtn(row) {
      return !this.isRowEdit.includes(row[this.commonOperateObj.id]);
    },
    handleCancelEdit(row) {
      this.isRowEdit = this.isRowEdit?.filter(
        (x) => x !== row[this.commonOperateObj.id]
      );
      this.loadDeviceData();
    },
    handleRowSave(row) {
      const params = this.editObj[row[this.commonOperateObj.id]];
      api[this.commonOperateObj.editApi]({
        ...params,
        certificate: params.certificateList?.map((x) => x.storePath)?.join(","),
        attachments: params.attachmentsList?.map((x) => x.storePath)?.join(","),
      }).then((res) => {
        if (res?.code === "10000") {
          this.loadDeviceData();
          this.isRowEdit = this.isRowEdit?.filter(
            (x) => x !== row[this.commonOperateObj.id]
          );
        }
      });
    },
    handleRowEdit(row) {
      this.editObj[row[this.commonOperateObj.id]] = { ...row };
      this.isRowEdit.push(row[this.commonOperateObj.id]);
      // console.log(this.isRowEdit, "点了编辑");
    },
    async getDeviceOptions() {
      const res = await deviceApi.getTableData({
        pageSize: 9999,
        pageNum: 1,
        status: 0,
      });
      if (res?.code === "10000") {
        this.deviceOptions = res.data?.map((x) => {
          return {
            value: x.materialName,
            label: x.materialName + "(" + x.materialNo + ")",
          };
        });
      }
    },
    handleEdit(row) {
      // console.log(row, "=====编辑了");
      this.editObj[row[this.commonOperateObj?.id]] = { ...row };
    },
    //资产验收单-导入进场验收单
    handleImportAcceptance() {
      this.$confirm(
        "确定一键导入该项目进场验收单里的所有设备数据吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        api
          .importAcceptanceToAsset({
            projectBatchId: this.projectId,
            installFlag: this.activeName,
          })
          .then((res) => {
            if (res?.code === "10000") {
              this.$message.success("导入成功");
              this.handleQuery();
            }
          });
      });
    },
    //全部清空操作
    deleteAllFn() {
      this.$confirm("是否确认全部清空？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        api[this.commonOperateObj?.deleteAllApi]({
          projectBatchId: this.projectId,
        }).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("清空成功");
            this.handleQuery();
          }
        });
      });
    },
    handleImport() {
      this.$refs.batchUpload.open();
    },
    deleteDeviceItem(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const { deleteApi, id } = this.commonOperateObj;
        api[deleteApi]({ [id]: row[id] }).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadDeviceData();
          }
        });
      });
    },
    getAssetDeviceList() {
      this.loading = true;
      queryAssetDeviceList({
        projectBatchId: this.projectId,
        pageSize: this.tablePage.pageSize,
        pageNum: this.tablePage.currentPage,
        installFlag: this.activeName,
      })
        .then((res) => {
          this.loading = false;
          if (res?.code === "10000") {
            this.data["tabTableData"] = res.data?.map((x) => {
              return {
                ...x,
                attachmentsList:
                  x.attachments?.length > 0
                    ? x.attachments.split(",")?.map((y) => {
                        return { storePath: y, docName: y.split("/")?.pop() };
                      })
                    : [],
              };
            });
            console.log(this.data, "dataaaaaaa");
            this.tablePage.total = res.total;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    loadDeviceData() {
      this.editObj = {};
      this.isRowEdit = [];
      if (this.currentWorkId === "资产验收单") {
        this.getAssetDeviceList();
      } else {
        //进场验收单
        this.getAcceptanceDetail();
      }
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadDeviceData();
    },
    //施工验收
    handleConstructAcceptance() {
      this.$router.push({
        path: "/engineerProject/investBatchList/constructAcceptance",
        query: {
          projectId: this.projectId,
          isEnergyStorage: JSON.stringify(this.isEnergyStorage),
        },
      });
      this.$emit("closeDrawer");
    },
    //获取固定验收项信息
    async getFixedItems() {
      const res = await queryFixedItems({ projectBatchId: this.projectId });
      if (res.code === "10000") {
        // this.worklist = res.data.formData || [];
        let arr = [];
        //todo:固定展示3项（路书、变更项从接口字段判断是否展示）
        const { formData = [] } = res.data;
        this.fixedDict.map((x) => {
          if (x.condition?.(res.data)) {
            const obj = formData.find((y) => y.formType == x.formType) || {};
            arr.push({ ...x, ...obj });
          }
        });
        this.worklist = arr;
        console.log(this.worklist, "-------");
        // this.worklist = this.worklist?.map((i) => {
        //   const obj = this.fixedDict.find((x) => x.formType == i.formType);
        //   return { ...i, ...obj };
        // });
      }
    },
    //获取工序项统计数据
    async getItemCount() {
      const res = await queryItemCount({ projectBatchId: this.projectId });
      if (res?.code == "10000") {
        this.countObj = res.data || {};
      }
    },
    async loadWorkCheck() {
      await this.getFixedItems();
      queryWorkCheckList({
        projectBatchId: this.projectId,
      }).then((res) => {
        if (res.code === "10000") {
          this.worklist.unshift(...res.data);

          this.currentWorkId = this.workId || this.worklist[0].workId;
          this.loadWorkCheckInfo();
        }
      });
    },

    reset() {
      this.$nextTick(() => {
        window.scrollTo({ top: 0, behavior: "smooth" });
        this.$refs.resultForm?.resetForm();
        this.$refs.checkForm?.forEach((i) => {
          i.resetFormValue && i.resetFormValue();
        });
      });
    },
    getAcceptanceDetail() {
      this.loading = true;
      api
        .queryAcceptanceDetail({
          projectBatchId: this.projectId,
          pageSize: this.tablePage.pageSize,
          pageNum: this.tablePage.currentPage,
        })
        .then((res) => {
          this.loading = false;
          if (res?.code === "10000") {
            this.data = res.data;
            this.data.cmProjectAcceptances.forEach((x) => {
              x.certificateList =
                x.certificate?.length > 0
                  ? x.certificate.split(",")?.map((y) => {
                      return { storePath: y, docName: y.split("/")?.pop() };
                    })
                  : [];
            });
            this.tablePage.total = res.data.total;

            this.showOperation = this.data?.fixedFormAudit?.auditResult != "1";
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    async loadWorkCheckInfo() {
      this.reset();

      const detailApi = this.currentWorkCheck.detailApi;

      if (detailApi) {
        // 固定验收详情
        this.loading = true;
        const res = await detailApi({ projectBatchId: this.projectId }).catch(
          () => {
            this.loading = false;
          }
        );
        this.loading = false;
        if (res.code === "10000") {
          this.data = res.data;
          if (this.currentWorkId === "资产验收单") {
            this.activeName = "0";

            this.showOperation = this.data?.fixedFormAudit?.auditResult != "1";

            this.handleQuery();
          }
        }
      } else {
        //电缆、并网箱进场验收单 要做分页
        if (this.currentWorkCheck.workName === "电缆、并网箱进场验收单") {
          this.getAcceptanceDetail();
        } else {
          const arr = [
            // {
            //   name: "电缆、并网箱进场验收单",
            //   api: "queryAcceptanceDetail",
            //   dataName: "acceptanceDetail",
            // },
            {
              name: "电气接地电阻测试记录",
              api: "queryProjectWorkResistance",
              dataName: "projectWorkResistances",
            },
            {
              name: "电气绝缘电阻测试记录",
              api: "queryProjectWorkCable",
              dataName: "projectWorkCables",
            },
          ];
          const method =
            arr.find((x) => x.name === this.currentWorkCheck.workName)?.api ||
            "queryWorkCheck";
          // 动态验收详情
          this.loading = true;
          const res = await api[method]({
            workId: this.currentWorkId,
            projectBatchId: this.projectId,
          }).catch(() => {
            this.loading = false;
          });
          if (res.code === "10000") {
            this.data = res.data;
          }
          this.loading = false;
        }
      }
    },
    async checkFormValue(result) {
      const data = {};
      for (let k = 0; k < this.$refs.checkForm.length; k++) {
        let i = this.$refs.checkForm[k];
        if (typeof i.getFormValue === "function") {
          //新增逻辑：当审核通过时，不校验不合格原因是否填写
          const [valid, formValue] = await i.getFormValue(result != "1");
          if (valid) {
            Object.assign(data, formValue);
          }
        }
      }

      return data;
    },
    async getAllFormValue() {
      const [, resultFormValue] = await this.$refs.resultForm.getFormValue();
      let checkFormValue = await this.checkFormValue(
        resultFormValue.auditResult
      );
      const projectWork = {
        ...this.data.projectWork,
        ...resultFormValue,
      };

      const projectWorkChecks = this.data.projectWorkChecks.map((i, k) => {
        return {
          ...i,
          buildCheck: checkFormValue.buildCheck[k],
          buildReason: checkFormValue.buildReason[k],
        };
      });
      return {
        projectWork,
        projectWorkChecks,
      };
    },

    // 检查项不通过原因，合并到验收结果的不通过原因
    checkFormValChange(v) {
      this.$refs.resultForm.setFormValue({
        auditReason: v?.buildReason?.filter((i) => i)?.join(","),
      });
    },
    cancel() {},
    back() {
      this.currentWorkId = this.worklist[this.prevKey].workId;
      this.loadWorkCheckInfo();
    },
    //审核后刷新工序项统计状态和每个tab后的图标状态
    async refreshWorkStatus() {
      this.getItemCount();
      await this.getFixedItems();
      queryWorkCheckList({
        projectBatchId: this.projectId,
      }).then((res) => {
        if (res.code === "10000") {
          this.worklist.unshift(...res.data);
        }
      });
    },
    async save(next = false) {
      // 如果该工序项的状态为【未处理】时（变更项除外），不允许对工序单审，点击【审核并下一步】时，toast提示【该工序项APP端未处理，无法审核！】
      //工序项展示状态为【审核不通过】也不允许审核
      if (
        (!this.currentWorkCheck.auditResult &&
          this.currentWorkCheck.workStatus == "1" &&
          this.currentWorkId !== "变更项") ||
        (this.currentWorkCheck.auditResult == "2" &&
          this.currentWorkCheck.flag != "0")
      ) {
        this.$message.error("该工序项APP端未处理，无法审核！");
        return;
      }
      let saveSuccess = false;
      if (this.isCommonWorkCheck) {
        // 固定验收
        const arr = this.fixedDict.map((x) => x.workId);
        const [, params] = await this.$refs.resultForm.getFormValue();
        this.saveLoading = true;
        const res = await saveFixedFormAuditInfo({
          projectBatchId: this.projectId,
          formType: this.currentWorkCheck.formType,
          constructionCheckContent: params.constructionCheckContent,
          constructionManager: params.constructionManager,
          auditResult: params.auditResult,
          auditReason: params.auditReason,
          fixedAuditId: params.fixedAuditId,
          //固定验收项的workId是中文，不要传；这里传workId是针对储能的固定项里的动态表单
          workId: arr.includes(this.currentWorkId) ? null : this.currentWorkId,
        }).catch(() => {
          this.saveLoading = false;
        });
        if (res.code === "10000") {
          this.$message.success(
            `${this.currentWorkCheck.workName}工序审核成功！`
          );
          saveSuccess = true;
          // if (this.currentWorkId === "变更项") {
          //   this.loadWorkCheckInfo();
          //   this.refreshWorkStatus();
          // }
        }
        this.saveLoading = false;
      } else {
        let params = await this.getAllFormValue();
        const { projectWork, projectWorkChecks } = params;
        console.log(projectWork, projectWorkChecks, "===");
        const len = projectWorkChecks.filter((x) => x.buildCheck !== "1")
          ?.length;
        //不知道哪来的formType 先手动删掉
        delete params.projectWork.formType;
        //如果审核通过，则弹窗提示是否将所有规范要求更改为合格
        if (projectWork.auditResult == "1" && len > 0) {
          this.$confirm(
            "确定要将所有规范要求全部更改为合格吗?",
            `存在${len}个为空/不合格的规范要求！`,
            {
              confirmButtonText: "确定审核通过",
              cancelButtonText: "取消",
              type: "warning",
              center: true,
            }
          ).then(async () => {
            params.projectWorkChecks = projectWorkChecks.map((x) => {
              return {
                ...x,
                buildCheck: "1",
                buildReason: "",
              };
            });
            this.saveLoading = true;

            // 动态验收
            const res = await saveWorkCheck(params).catch(() => {
              this.saveLoading = false;
            });
            if (res.code === "10000") {
              this.$message.success(
                `${this.currentWorkCheck.workName}工序审核成功！`
              );
              saveSuccess = true;
              if (saveSuccess) {
                next &&
                  (this.currentWorkId = this.worklist[this.nextKey].workId);
                this.loadWorkCheckInfo();
                this.refreshWorkStatus();
              }
            }
            this.saveLoading = false;
          });
        } else {
          this.saveLoading = true;
          // 动态验收
          const res = await saveWorkCheck(params).catch(() => {
            this.saveLoading = false;
          });
          if (res.code === "10000") {
            this.$message.success(
              `${this.currentWorkCheck.workName}工序审核成功！`
            );
            saveSuccess = true;
          }
          this.saveLoading = false;
        }
      }
      if (saveSuccess) {
        next && (this.currentWorkId = this.worklist[this.nextKey].workId);
        this.loadWorkCheckInfo();
        this.refreshWorkStatus();
      }
    },
    async submit() {
      console.log(this.data, "====");
      // if (
      //   this.currentWorkId === "变更项" &&
      //   !this.checkResult?.auditResult &&
      //   this.data.changeRequestList?.length > 0
      // ) {
      //   this.$message.error("请先保存");
      //   return;
      // }
      this.$refs.dialogRef.open();
    },
  },
};
</script>

<style scoped lang="less">
.workCheckContainer {
  padding: 0 24px 40px;
  /deep/ .el-tabs__nav-scroll {
    overflow-x: auto;
  }
  /deep/ .el-tabs__nav-prev,
  /deep/ .el-tabs__nav-next {
    // display: none;
  }
  /deep/ .el-tabs__nav-wrap.is-scrollable {
    // padding: 0 0;
  }
  .work-check-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    line-height: 36px;
    .work-check-total {
      font-size: 16px;
      &-count {
        font-size: 20px;
        font-weight: 700;
      }
    }
  }
}
.workCheck {
  margin-top: 20px;
}
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item .el-icon-close {
  position: inherit;
  margin: 0;
  width: 14px;
  height: 14px;
  font-size: 14px;
  font-family: element-icons !important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 16px;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  &:before {
    transform: none;
  }
  &:hover {
    background-color: transparent;
    color: #ed5565;
  }
}
</style>
