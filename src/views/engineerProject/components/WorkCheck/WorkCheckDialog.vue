<template>
  <el-dialog title="提交审核结果" :visible.sync="dialogVisible" width="50%">
    <el-form
      :model="formData"
      label-width="110px"
      :rules="formRules"
      ref="baseForm"
    >
      <el-form-item prop="warrantyPeriod" label="项目质保期">
        <el-input
          placeholder="请输入项目施工质保期，仅支持数字，长度不超过999999"
          v-model="formData.warrantyPeriod"
        >
          <template slot="append">月</template>
        </el-input>
      </el-form-item>
      <el-form-item prop="auditReason" label="不通过原因">
        <el-input
          type="textarea"
          rows="5"
          placeholder="请输入审核不通过原因，字符5000字以内"
          :maxlength="1000"
          v-model="formData.auditReason"
        />
      </el-form-item>
    </el-form>
    <el-row>
      <el-link type="danger" v-if="errMsg">{{ errMsg }}</el-link>
    </el-row>
    <span slot="footer" class="dialog-footer">
      <el-button @click="submit(2)" :disabled="loading" :loading="loading"
        >审核不通过</el-button
      >
      <el-button
        type="primary"
        @click="submit(1)"
        :disabled="loading"
        :loading="loading"
        >审核通过</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {
  submitPcWorkByProject,
  queryPcWorkByProject,
} from "@/api/engineerProject/investBatchList/workCheck";
export default {
  props: {
    projectId: {
      type: String,
      required: true,
      default: "",
    },
  },
  data() {
    return {
      formData: {
        warrantyPeriod: "",
        auditReason: "",
      },
      dialogVisible: false,
      errMsg: "",
      loading: false,
      auditReason: "",
      formRules: {
        warrantyPeriod: [
          { required: true, message: "请输入项目质保期", trigger: "change" },
          {
            pattern: /^(0|[1-9]\d{0,5}|999999)$/,
            message: "请输入正确的数字",
          },
        ],
      },
    };
  },
  methods: {
    close() {
      this.dialogVisible = false;
      this.errMsg = "";
      this.auditReason = "";
      this.$router.go(-1);
      this.$store.dispatch("tagsView/delView", this.$route);
    },
    open() {
      this.formData = {
        warrantyPeriod: "",
        auditReason: "",
      };
      this.dialogVisible = true;
      this.load();
    },
    async load() {
      const res = await queryPcWorkByProject({
        projectBatchId: this.projectId,
      });
      if (res.code === "10000") {
        this.formData.auditReason = res.data?.auditReason;
        // this.formData.warrantyPeriod = res.data?.warrantyPeriod;
      }
    },
    async submit(auditResult) {
      console.log(this.formData, "tj");
      this.$refs.baseForm.validate((valid) => {
        //审核不通过时质保非必填
        if (auditResult == 1 && !valid) return;

        this.$confirm("是否确认提交", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          this.loading = true;
          const res = await submitPcWorkByProject({
            auditResult,
            projectBatchId: this.projectId,
            ...this.formData,
          }).catch((err) => {
            this.errMsg = err;
            this.loading = false;
            console.log(err, "123123");
          });
          this.loading = false;
          if (res.code === "10000") {
            this.$message.success("提交成功");
            this.close();
          }
        });
      });
    },
  },
};
</script>
