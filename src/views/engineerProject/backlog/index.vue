<!-- 待办 -->
<template>
  <div class="card-container">
    <el-row :gutter="24">
      <el-col :xs="6" :sm="6" :md="6" :lg="5" :xl="4">
        <OrderStatusMenu @select="handleSelect" :data="orderStatusMap" />
      </el-col>
      <el-col :xs="18" :sm="18" :md="18" :lg="19" :xl="20">
        <el-radio-group
          v-model="tabActiveTab"
          style="margin-bottom: 20px;"
          size="medium"
          :disabled="loading"
        >
          <el-radio-button
            :label="item.value"
            v-for="item in topTabList"
            :key="item.value"
            >{{ item.label }}
          </el-radio-button>
        </el-radio-group>
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :pagerProps="pagerProps"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :tableProps="tableProps"
          :modalConfig="modalConfig"
          @modalConfirm="modalConfirmHandler"
          @loadData="loadData"
          tabType="card"
          v-if="tabActiveTab"
        >
          <template #toolbar_buttons> </template>
          <template slot="filterCustomBtn">
            <div class="btn-wrap">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click.stop="handleQuery"
                >查询
              </el-button>
              <el-button icon="el-icon-refresh" @click.stop="handleReset"
                >重置
              </el-button>
            </div>
          </template>
          <template slot="fold_header" slot-scope="{ row, column }">
            <div
              @click="handleCollapse(column.property)"
              style="cursor:pointer"
            >
              <span>{{ column.title }}</span>
              <i
                :class="
                  foldObj[column.property]
                    ? 'el-icon-caret-right'
                    : 'el-icon-caret-left'
                "
              ></i>
            </div>
          </template>
          <template #itemDetail="{row,column}">
            <el-link type="primary" @click="handleItemDetailDialog(row)">
              {{ row[column.property] }}
            </el-link>
          </template>
        </BuseCrud>
        <el-empty v-else></el-empty>
        <!-- 工序详情 -->
        <el-drawer
          title=""
          :visible.sync="workCheckVis"
          :show-close="false"
          size="80%"
          append-to-body
          :wrapperClosable="true"
        >
          <WorkCheck
            :projectId="projectBatchId"
            :projectStatus="projectStatus"
            :edit="false"
            :isEnergyStorage="isEnergyStorage"
            @closeDrawer="workCheckVis = false"
          />
        </el-drawer>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import WorkCheck from "../components/WorkCheck";
import checkPermission from "@/utils/permission.js";
import api from "@/api/engineerProject/investBatchList/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";
import dictMixin from "../dictMixin";
import OrderStatusMenu from "./components/OrderStatusMenu.vue";
export default {
  components: { OrderStatusMenu, WorkCheck },
  name: "backlogList",
  mixins: [exportMixin, dictMixin],
  data() {
    return {
      tabActiveTab: "charge",
      topTabList: [],
      topTabDict: [
        {
          value: "charge",
          label: "充电业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:chargeTab",
            ]);
          },
        },
        {
          value: "storage",
          label: "储能业务",
          show: () => {
            return this.checkPermission([
              "engineerProject:investBatchList:storageTab",
            ]);
          },
        },
      ],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "groupId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [{ id: 1, stationName: "1" }],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      foldObj: {
        baseInfo: false,
        timeInfo: false,
        costInfo: false,
      },

      todoStatistics: "",

      orderStatusMap: {},
      tabParams: {},
      projectBatchId: "",
      workCheckVis: false,
      projectStatus: "",
      isEnergyStorage: false,
    };
  },
  watch: {
    tabActiveTab: {
      handler(val) {
        if (val) {
          this.handleQuery();
        }
      },
    },
  },
  created() {
    this.topTabList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.topTabList[0]?.value || "";
    this.params = initParams(this.filterOptions.config);
    this.loadLeftData();
  },
  activated() {
    this.$nextTick(() => {
      this.loadData();
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //打开工序项详情
    handleItemDetailDialog(row) {
      this.projectBatchId = row.projectBatchId;
      this.isEnergyStorage = row.businessType == "2";
      this.workCheckVis = true;
      this.projectStatus = row.projectStatus;
    },
    handleCollapse(type) {
      this.foldObj[type] = !this.foldObj[type];
    },

    checkPermission,
    handleSelect({ index, paramsMap }) {
      this.todoStatistics = index;
      this.tabParams = { ...paramsMap[index] };
      console.log(this.params, paramsMap, index, "+++++++++++");
      this.loadData();
    },
    async loadLeftData() {
      const res = await api.queryTodoStatistics();
      if (res.code === "10000") {
        this.orderStatusMap = res.data;
      }
    },
    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.tabParams,
        todoTag: true,
        businessType: this.tabActiveTab === "charge" ? "1" : "2",
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "surveyPassTime",
          title: "踏勘通过时间",
          startFieldName: "surveyPassStartTime",
          endFieldName: "surveyPassEndTime",
        },
        {
          field: "consCheckPassTime",
          title: "施工验收通过时间",
          startFieldName: "consCheckPassStartTime",
          endFieldName: "consCheckPassEndTime",
        },
        {
          field: "onlineTime",
          title: "上线时间",
          startFieldName: "onlineStartTime",
          endFieldName: "onlineEndTime",
        },
        {
          field: "warrantyExpiresTime",
          title: "质保到期时间",
          startFieldName: "warrantyExpiresStartTime",
          endFieldName: "warrantyExpiresEndTime",
        },
        {
          field: "consCompleteTime",
          title: "施工完成时间",
          startFieldName: "consCompleteStartTime",
          endFieldName: "consCompleteEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },

    //弹窗确认按钮事件
    async modalConfirmHandler() {},
    jumpToStation(row) {
      this.$router.push({
        name: "investBatchList",
        params: {
          surveyCodeMain: row.surveyCodeMain,
          surveyCodeSub: row.surveyCodeSub,
          businessType: row.businessType,
          tabActiveTab: row.businessType == "2" ? "storage" : "charge",
        },
      });
    },
    getTagType(projectType) {
      const arr = [
        { type: "success", status: "新建" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
  },
  computed: {
    tableColumn() {
      return this.tabActiveTab === "charge"
        ? this.chargeColumn
        : this.storageColumn;
    },
    chargeColumn() {
      return [
        {
          field: "baseInfo",
          title: "基本信息",
          minWidth: 100,
          children: this.foldObj.baseInfo
            ? []
            : [
                {
                  field: "businessType",
                  title: "业务类型",
                  width: 100,
                  formatter: ({ cellValue }) => {
                    return this.format("project_business_type", cellValue);
                  },
                },
                {
                  field: "stationName",
                  title: "场站名称",
                  width: 150,
                },
                { field: "stationLocation", title: "场站位置", width: 150 },
                {
                  field: "surveyCodeMain",
                  title: "踏勘主编码",
                  width: 220,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-link
                          type="primary"
                          on={{
                            click: () => this.jumpToStation(row),
                          }}
                        >
                          {row.surveyCodeMain}
                        </el-link>,
                      ];
                    },
                  },
                },
                {
                  field: "surveyCodeSub",
                  title: "踏勘子编码",
                  width: 220,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-link
                          type="primary"
                          on={{
                            click: () => this.jumpToStation(row),
                          }}
                        >
                          {row.surveyCodeSub}
                        </el-link>,
                      ];
                    },
                  },
                },
                {
                  field: "projectType",
                  title: "项目类型",
                  width: 100,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-tag
                          type={this.getTagType(row.projectType)}
                          size="medium"
                          effect="plain"
                          style={"font-size: 16px;"}
                        >
                          {row.projectType}
                        </el-tag>,
                      ];
                    },
                  },
                },
                {
                  field: "constructType",
                  title: "投建类型",
                  width: 100,
                },
                {
                  field: "surveyFormIsCancel",
                  title: "踏勘单是否已作废",
                  width: 100,
                },
                { field: "stationCode", title: "场站编码", width: 150 },
                { field: "projectCode", title: "项目编码", width: 150 },
                {
                  field: "projectProcessNodeName",
                  title: "项目流程节点",
                  width: 150,
                },
                {
                  field: "projectStatusName",
                  title: "项目状态",
                  width: 150,
                },
                {
                  field: "workApplyStatus",
                  title: "开工申请状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.format("work_apply_status", cellValue);
                  },
                },
                {
                  field: "consTeamAuditStatusName",
                  title: "施工供应商审批状态",
                  width: 150,
                },
                {
                  field: "deviceOrderStatus",
                  title: "设备下单状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.format("device_order_status", cellValue);
                  },
                },
                {
                  field: "allWorkNum",
                  title: "工序项总数",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "pendingWorkNum",
                  title: "待处理工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "reviewedWorkNum",
                  title: "待审核工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "approveWorkNum",
                  title: "审核通过工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                { field: "totalPileCount", title: "设备数", width: 150 },
                { field: "totalPower", title: "总功率(kW)", width: 150 },
                { field: "orgName", title: "归属大区", width: 150 },
                {
                  field: "engineeringManagerName",
                  title: "工程经理",
                  width: 150,
                },
                {
                  field: "businessDevelopmentName",
                  title: "商务BD",
                  width: 150,
                },
                { field: "isReportPower", title: "是否报电", width: 150 },
                { field: "consTeamName", title: "施工队", width: 150 },
                {
                  field: "consTeamLeaderName",
                  title: "施工队负责人",
                  width: 150,
                },
                {
                  field: "consTeamManagerName",
                  title: "施工队项目经理",
                  width: 150,
                },
                {
                  field: "constructionContractMode",
                  title: "施工发包模式",
                  width: 150,
                },
              ],
          slots: {
            header: "fold_header",
          },
        },
        {
          field: "timeInfo",
          title: "时间节点",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.timeInfo
            ? []
            : [
                { field: "surveyPassTime", title: "踏勘通过时间", width: 150 },
                { field: "surveyDuration", title: "踏勘耗时", width: 150 },
                {
                  field: "consAgreementPassTime",
                  title: "投建协议审批通过时间",
                  width: 150,
                },
                {
                  field: "consAgreementApprovalDuration",
                  title: "投建协议审批耗时",
                  width: 150,
                },
                {
                  field: "supplierNotifyTime",
                  title: "施工供应商通知时间",
                  width: 150,
                },
                {
                  field: "supplierOrderPassTime",
                  title: "施工供应商下单审批通过时间",
                  width: 150,
                },
                {
                  field: "supplierOrderApprovalDuration",
                  title: "施工供应商下单审批耗时",
                  width: 150,
                },
                {
                  field: "workApplyTime",
                  title: "开工申请发起时间",
                  width: 150,
                },
                {
                  field: "workApplyPassTime",
                  title: "开工申请通过时间",
                  width: 150,
                },
                {
                  field: "workApplyDuration",
                  title: "开工申请耗时",
                  width: 150,
                },
                {
                  field: "deviceOrderPassTime",
                  title: "设备下单审批通过时间",
                  width: 150,
                },
                {
                  field: "deviceOrderApprovalDuration",
                  title: "设备下单审批耗时",
                  width: 150,
                },
                {
                  field: "consCompleteTime",
                  title: "施工完成时间",
                  width: 150,
                },
                {
                  field: "constructionDuration",
                  title: "施工耗时",
                  width: 150,
                },
                {
                  field: "consCheckPassTime",
                  title: "施工验收通过时间",
                  width: 150,
                },
                {
                  field: "constructionCheckDuration",
                  title: "施工验收耗时",
                  width: 150,
                },
                {
                  field: "completionReportUploadTime",
                  title: "竣工报告上传时间",
                  width: 150,
                },
                {
                  field: "completionReportPassTime",
                  title: "竣工报告审核通过时间",
                  width: 150,
                },
                { field: "onlineTime", title: "上线时间", width: 150 },
                {
                  field: "warrantyExpiresTime",
                  title: "质保到期时间",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <span
                          style={{
                            color:
                              row.warrantyExpiresTimeFlag == "1" ? "red" : "",
                          }}
                        >
                          {row.warrantyExpiresTime}
                        </span>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "costInfo",
          title: "成本信息",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.costInfo
            ? []
            : [
                {
                  field: "constructionTotalCost",
                  title: "施工总成本(元)",
                  width: 150,
                },
                {
                  field: "deviceTotalCost",
                  title: "设备总成本(元)",
                  width: 150,
                },
                { field: "paidAmount", title: "已付金额(元)", width: 150 },
                { field: "unpaidAmount", title: "未付金额(元)", width: 150 },
              ],
        },
      ];
    },
    storageColumn() {
      return [
        {
          field: "energyBaseInfo",
          title: "基本信息",
          minWidth: 120,
          children: this.foldObj.energyBaseInfo
            ? []
            : [
                { field: "stationName", title: "场站名称", width: 150 },
                { field: "stationLocation", title: "场站位置", width: 150 },
                {
                  field: "surveyCodeMain",
                  title: "踏勘主编码",
                  width: 220,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-link
                          type="primary"
                          on={{
                            click: () => this.jumpToStation(row),
                          }}
                        >
                          {row.surveyCodeMain}
                        </el-link>,
                      ];
                    },
                  },
                },
                {
                  field: "surveyCodeSub",
                  title: "踏勘子编码",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-link
                          type="primary"
                          on={{
                            click: () => this.jumpToStation(row),
                          }}
                        >
                          {row.surveyCodeSub}
                        </el-link>,
                      ];
                    },
                  },
                },
                {
                  field: "projectType",
                  title: "项目类型",
                  width: 100,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <el-tag
                          type={this.getTagType(row.projectType)}
                          size="medium"
                          effect="plain"
                          style={"font-size: 16px;"}
                        >
                          {row.projectType}
                        </el-tag>,
                      ];
                    },
                  },
                },
                { field: "constructType", title: "投建类型", width: 100 },
                {
                  field: "surveyFormIsCancel",
                  title: "踏勘单是否已作废",
                  width: 100,
                },
                { field: "stationCode", title: "场站编码", width: 150 },
                { field: "projectCode", title: "项目编码", width: 150 },
                {
                  field: "projectProcessNodeName",
                  title: "项目流程节点",
                  width: 150,
                },
                {
                  field: "projectStatus",
                  title: "项目状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.allProjectStatusOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                {
                  field: "workApplyStatus",
                  title: "开工申请状态",
                  width: 150,
                  formatter: ({ cellValue }) => {
                    return this.workApplyStatusOptions?.find(
                      (x) => x.dictValue == cellValue
                    )?.dictLabel;
                  },
                },
                {
                  field: "consTeamAuditStatusName",
                  title: "施工供应商审批状态",
                  width: 150,
                },
                {
                  field: "deviceOrderStatusName",
                  title: "设备下单状态",
                  width: 150,
                },
                {
                  field: "allWorkNum",
                  title: "工序项总数",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "pendingWorkNum",
                  title: "待处理工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "reviewedWorkNum",
                  title: "待审核工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "approveWorkNum",
                  title: "审核通过工序项",
                  width: 100,
                  slots: { default: "itemDetail" },
                },
                {
                  field: "transformerCapacityTotal",
                  title: "变压器总容量(kVA)",
                  width: 100,
                },
                {
                  field: "storageDevicesCount",
                  title: "储能设备数(台)",
                  width: 100,
                },
                { field: "totalCapacity", title: "总容量(kWh)", width: 100 },
                {
                  field: "meshCabinetsCount",
                  title: "并网柜数量(台)",
                  width: 100,
                },
                { field: "platformName", title: "平台方名称", width: 100 },
                {
                  field: "isXdtConnect",
                  title: "是否与新电途互联互通",
                  width: 100,
                },
                { field: "storageType", title: "储能类型", width: 100 },
                {
                  field: "businessDevelopmentName",
                  title: "商务BD",
                  width: 150,
                },
                { field: "consTeamName", title: "施工队", width: 150 },
                {
                  field: "consTeamLeaderName",
                  title: "施工队负责人",
                  width: 150,
                },
                {
                  field: "consTeamManagerName",
                  title: "施工队项目经理",
                  width: 150,
                },
              ],
          slots: {
            header: "fold_header",
          },
        },
        {
          field: "energyTimeNode",
          title: "时间节点",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.energyTimeNode
            ? []
            : [
                { field: "surveyPassTime", title: "踏勘通过时间", width: 150 },
                { field: "surveyDuration", title: "踏勘耗时", width: 150 },
                {
                  field: "consAgreementPassTime",
                  title: "投建协议审批通过时间",
                  width: 150,
                },
                {
                  field: "consAgreementApprovalDuration",
                  title: "投建协议审批耗时",
                  width: 150,
                },
                {
                  field: "supplierOrderNotifyTime",
                  title: "施工供应商通知时间",
                  width: 150,
                },
                {
                  field: "supplierOrderPassTime",
                  title: "施工供应商下单审批通过时间",
                  width: 150,
                },
                {
                  field: "supplierOrderApprovalDuration",
                  title: "施工供应商下单审批耗时",
                  width: 150,
                },
                {
                  field: "workApplyTime",
                  title: "开工申请发起时间",
                  width: 150,
                },
                {
                  field: "workApplyPassTime",
                  title: "开工申请通过时间",
                  width: 150,
                },
                {
                  field: "workApplyDuration",
                  title: "开工申请耗时",
                  width: 150,
                },
                {
                  field: "deviceOrderPassTime",
                  title: "设备下单审批通过时间",
                  width: 150,
                },
                {
                  field: "deviceOrderApprovalDuration",
                  title: "设备下单审批耗时",
                  width: 150,
                },
                {
                  field: "consCompleteTime",
                  title: "施工完成时间",
                  width: 150,
                },
                {
                  field: "constructionDuration",
                  title: "施工耗时",
                  width: 150,
                },
                {
                  field: "consCheckPassTime",
                  title: "施工验收通过时间",
                  width: 150,
                },
                {
                  field: "constructionCheckDuration",
                  title: "施工验收耗时",
                  width: 150,
                },
                {
                  field: "completionReportUploadTime",
                  title: "竣工报告上传时间",
                  width: 150,
                },
                {
                  field: "completionReportPassTime",
                  title: "竣工报告审核通过时间",
                  width: 150,
                },
                { field: "onlineTime", title: "上线时间", width: 150 },
                {
                  field: "warrantyExpiresTime",
                  title: "质保到期时间",
                  width: 150,
                  slots: {
                    default: ({ row }) => {
                      return [
                        <span
                          style={{
                            color:
                              row.warrantyExpiresTimeFlag == "1" ? "red" : "",
                          }}
                        >
                          {row.warrantyExpiresTime}
                        </span>,
                      ];
                    },
                  },
                },
              ],
        },
        {
          field: "energyCostInfo",
          title: "成本信息",
          minWidth: 100,
          slots: {
            header: "fold_header",
          },
          children: this.foldObj.energyCostInfo
            ? []
            : [
                {
                  field: "constructionTotalCost",
                  title: "施工总成本(元)",
                  width: 150,
                },
                {
                  field: "deviceTotalCost",
                  title: "设备总成本(元)",
                  width: 150,
                },
                { field: "paidAmount", title: "已付金额(元)", width: 150 },
                { field: "unpaidAmount", title: "未付金额(元)", width: 150 },
              ],
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "场站编码",
          },
          {
            field: "stationName",
            element: "el-input",
            title: "场站名称",
          },
          {
            field: "region",
            title: "所在区域",
            element: "custom-cascader",
            attrs: {
              options: regionData,
              filterable: true,
              collapseTags: true,
              clearable: true,
              props: {
                checkStrictly: true,
                multiple: true,
              },
            },
          },
          {
            field: "projectProcessNode",
            title: "项目节点",
            element: "el-select",
            props: {
              options: this.allOptions.project_process_node,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "projectStatusList",
            title: "项目状态",
            element: "el-select",
            props: {
              options: this.allOptions.project_status,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              multiple: true,
            },
          },
          {
            field: "surveyPassTime",
            title: "踏勘通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },

          {
            field: "workApplyStatus",
            title: "开工申请状态",
            element: "el-select",
            props: {
              options: this.allOptions.work_apply_status,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },

          {
            field: "deviceOrderStatus",
            title: "设备下单状态",
            element: "el-select",
            props: {
              options: this.allOptions.device_order_status,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },

          {
            field: "consCompleteTime",
            title: "施工完成时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },

          {
            field: "surveyCodeMain",
            element: "el-input",
            title: "踏勘编码",
          },
          {
            field: "surveyCodeSub",
            element: "el-input",
            title: "踏勘子编码",
          },

          {
            field: "consCheckPassTime",
            title: "验收通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },

          {
            field: "projectCode",
            element: "el-input",
            title: "项目编码",
          },
          {
            field: "orgNo",
            element: "el-select",
            title: "归属大区",
            props: {
              options: this.allOptions.deptAllOptionList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
            },
          },
          {
            field: "onlineTime",
            title: "上线时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "engineeringManagerName",
            title: "工程经理",
            element: "el-select",
            props: {
              options: this.allOptions.managerList,
              filterable: true,
            },
          },
          {
            field: "consTeamName",
            element: "el-input",
            title: "施工队",
          },

          {
            field: "warrantyExpiresTime",
            title: "质保到期时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        menu: false,
        addBtn: false,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
</style>
