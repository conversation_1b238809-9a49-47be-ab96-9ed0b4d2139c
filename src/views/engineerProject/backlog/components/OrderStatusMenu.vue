<template>
  <el-menu :default-openeds="openeds" @select="handleSelect">
    <Component
      :is="i.children && i.children.length ? 'el-submenu' : 'el-menu-item'"
      :index="i.value"
      v-for="i in orderStatusList"
      :key="i.value"
    >
      <template slot="title">
        <span>{{ i.label }}</span>
      </template>
      <el-menu-item v-for="c in i.children" :key="c.value" :index="c.value">{{
        c.label
      }}</el-menu-item>
    </Component>
  </el-menu>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      orderStatusList: [
        {
          label: "投建协议待确认",
          value: "consAgreeConfirmCount",
          params: {
            projectStatusList: ["3"], //表示项目状态为【场地双章审批完成】
            consAgreeWaitConfirmTag: true
          },
        },

        {
          label: "供应商下单",
          value: "1",
          children: [
            {
              label: "供应商下单通知",
              value: "waitSupplierOrderNoticeCount",
              params: {
                projectStatusList: ["3"], // 表示项目状态为【场地双章审批完成】
                waitSupplierOrderNoticeTag: true
              },
            },
            // {
            //   label: "待供应商下单",
            //   value: "waitSupplierOrderCount",
            //   params: {
            //     projectStatusList: ["4"], //表示项目状态变成【施工供应商下单已通知】
            //   },
            // },
            {
              label: "待指定施工队",
              value: "designateConsTeamCount",
              params: {
                projectStatusList: ["4"], //表示项目状态变成【施工供应商下单已通知】
              },
            },
          ],
        },

        {
          label: "开工申请",
          value: "2",
          children: [
            {
              label: "待开工申请",
              value: "waitWorkApplyCount",
              params: {
                workApplyStatus: "1", //表示项目的开工申请状态为【待申请】
              },
            },
            {
              label: "开工申请待审核",
              value: "waitWorkApplyAuditCount",
              params: {
                workApplyStatus: "2", //表示开工申请状态变为【待审核】
              },
            },
            {
              label: "开工申请审核不通过",
              value: "workApplyAuditNotPassCount",
              params: {
                workApplyStatus: "5", //表示开工申请状态变为【审核不通过】
              },
            },
            {
              label: "开工申请待确认",
              value: "waitWorkApplyConfirmCount",
              params: {
                workApplyStatus: "3", //表示开工申请状态变为【审核通过】
              },
            },
          ],
        },

        {
          label: "待设备下单",
          value: "waitDeviceOrderCount",
          params: {
            deviceOrderStatus: "1", //表示设备下单状态为【待设备下单】
          },
        },

        {
          label: "待处理项目",
          value: "4",
          children: [
            {
              label: "施工中项目",
              value: "inProgressProjectCount",
              params: {
                projectStatusList: ["7"], //表示项目状态为【施工中】
              },
            },
            {
              label: "待验收项目",
              value: "waitCheckProjectCount",
              params: {
                projectStatusList: ["8"], //表示项目状态为【待验收】
              },
            },
            {
              label: "验收不通过项目",
              value: "checkNotPassProjectCount",
              params: {
                projectStatusList: ["9"], //表示项目状态为【验收不通过】
              },
            },
            {
              label: "竣工报告待上传",
              value: "waitReportUploadCount",
              params: {
                projectStatusList: ["10", "12"], //表示项目状态为【待上线或竣工报告验收不通过】
              },
            },
            {
              label: "竣工报告待验收",
              value: "waitReportCheckCount",
              params: {
                projectStatusList: ["11"], //表示项目状态为【竣工验收】
              },
            },
            {
              label: "待评价项目",
              value: "waitEvaluateProjectCount",
              params: {
                projectStatusList: ["13"], //表示项目状态为【竣工验收】
              },
            },
          ],
        },
        {
          label: "质保到期",
          value: "warrantyExpiredCount",
          params: {
            warrantyIsExpired: "Y", //质保是否已到期，Y：已到期
          },
        },
        {
          label: "施工资质到期",
          value: "consQualifyExpiredCount",
          params: {
            certLifeAgingFlag: "Y", //施工资质已到期，Y：已到期
          },
        },
      ],
    };
  },
  computed: {
    openeds() {
      return this.orderStatusList.reduce((p, i) => {
        p.push(i.value, ...(i.children || []).map((c) => c.value));
        return p;
      }, []);
    },
  },
  methods: {
    handleSelect(index) {
      const paramsMap = this.paramsMap(this.orderStatusList);
      this.$emit("select", { index, paramsMap });
    },
    paramsMap(list, maps = {}) {
      list.forEach((i) => {
        maps[i.value] = i.params;
        if (i.children?.length) {
          this.paramsMap(i.children, maps);
        }
      });
      return maps;
    },
    genStatusNum(list, obj) {
      return list.map((i) => {
        if (i.children?.length) {
          this.genStatusNum(i.children, obj);
        }
        if (obj[i.value] !== undefined) {
          i.label = `${i.label}（${obj[i.value]}）`;
        }
        return i;
      });
    },
  },
  watch: {
    data: {
      immediate: true,
      handler(v) {
        this.orderStatusList = this.genStatusNum(this.orderStatusList, v);
      },
    },
  },
};
</script>
