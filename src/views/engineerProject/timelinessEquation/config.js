// 场站项目-详情配置
import router from "@/router";
import Description from "@/components/DataDisplay/Description.vue";
import Table from "@/components/DataDisplay/Table.vue";

window.stationEvents = {
  investBatchList: function(projectBatchId, projectCode, stationName) {
    router.push({
      path: "/engineerProject/investBatchList/detail",
      query: {
        projectBatchId: projectBatchId,
        projectCode: projectCode,
        stationName: stationName,
      },
    });
  },
};

// 基本信息
export const baseConfig = {
  component: Description,
  config: [
    { label: "场站名称", value: "stationName" },
    { label: "场站编码", value: "stationCode" },
    { label: "踏勘编码", value: "surveyCodeMain" },
    {
      label: "投建批次",
      value: "projectBatchCount",
      element: "el-link",
      props: {
        type: "primary",
      },
      on: {
        click: (e, { data }) => {
          router.push(
            `/engineerProject/investBatchList?stationCode=${data.stationCode}&stationName=${data.stationName}`
          );
        },
      },
    },
    {
      label: "项目编码",
      value: "projectCodeList",
      html: true,
      format: (v, _k, _dicts, data) => {
        return v
          ?.map(
            (i) =>
              `<a style="color: #029c7c" 
                onclick="
                window.stationEvents.investBatchList('${i.projectBatchId}', '${i.projectCode}', '${data.stationName}')
                ">${i.projectCode}
              </a>`
          )
          .join(",");
      },
    },
    {
      label: "进行中的踏勘编码",
      value: "processingSurveyCodeList",
      html: true,
      format: (v, _k, _dicts, data) => {
        return v
          ?.map(
            (i) =>
              `<a style="color: #029c7c" 
                onclick="
                window.stationEvents.investBatchList('${i.projectBatchId}', '${i.projectCode}', '${data.stationName}')
                ">${i.surveyCode}
              </a>`
          )
          .join(",");
      },
    },
    {
      label: "已结束的踏勘编码",
      value: "finishedSurveyCodeList",
      html: true,
      format: (v, _k, _dicts, data) => {
        return v
          ?.map(
            (i) =>
              `<a style="color: #029c7c" 
                onclick="
                window.stationEvents.investBatchList('${i.projectBatchId}', '${i.projectCode}', '${data.stationName}')
                ">${i.surveyCode}
              </a>`
          )
          .join(",");
      },
    },
    { label: "小区名称", value: "areaName" },
    { label: "场站地址", value: "stationLocation" },
    { label: "是否报电", value: "isReportPower" },
    { label: "预计度电服务费", value: "expectElectricityFee" },
    { label: "投资法人实体（投资归属法人实体）", value: "investLegalEntity" },
    { label: "居间商", value: "middleBusiness" },
    // { label: "投资策略", value: "stationName" },
    { label: "业绩归属部门", value: "performanceDepartment" },
    { label: "归属大区", value: "orgName" },
    { label: "城市总监", value: "cityDirector" },
    { label: "部门总监", value: "departmentDirector" },
    { label: "商务人员", value: "businessDevelopmentName" },
    { label: "区域总监", value: "" },
    { label: "站类型", value: "stationType" },
    { label: "工程踏勘人员", value: "engineeringSurveyor" },
    { label: "场地方公司名称", value: "siteCompanyName" },
    { label: "投建模式", value: "constructType" },
    { label: "场地方收益模式", value: "" },
    { label: "安装区域", value: "installArea" },
    { label: "施工发包模式", value: "constructionContractMode" },
  ],
};

// 投建信息
export const constructionConfig = {
  component: Description,

  config: [
    { label: "项目状态", value: "projectStatusName" },
    { label: "项目名称", value: "projectName" },
    { label: "开工申请状态", value: "workApplyStatusName" },
    { label: "投建类型", value: "constructType" },
    { label: "设备下单状态", value: "deviceOrderStatusName" },
    {
      label: "踏勘编码",
      value: "surveyCodeMain",
      element: "el-link",
      props: {
        type: "primary",
      },
      on: {
        click: (e, { data }) => {
          router.push({
            path: "/engineerProject/investBatchList/detail",
            query: {
              projectBatchId: data.projectBatchId,
              projectCode: data.projectCode,
              stationName: data.stationName,
            },
          });
        },
      },
    },
    { label: "设备数", value: "totalPileCount" },
    {
      label: "踏勘子编码",
      value: "surveyCodeSub",
      element: "el-link",
      props: {
        type: "primary",
      },
      on: {
        click: (e, { data }) => {
          router.push({
            path: "/engineerProject/investBatchList/detail",
            query: {
              projectBatchId: data.projectBatchId,
              projectCode: data.projectCode,
            },
          });
        },
      },
    },
    { label: "总功率", value: "totalPower" },
    { label: "踏勘通过时间", value: "surveyPassTime" },
    { label: "开工申请通过时间", value: "workApplyPassTime" },
    { label: "验收时间", value: "consCompleteTime" },
    { label: "竣工报告通过时间", value: "completionReportPassTime" },
    { label: "质保期到期时间", value: "warrantyExpiresTime" },
    { label: "上线时间", value: "onlineTime" },
    { label: "施工队名称", value: "consTeamName" },
    { label: "施工队负责人", value: "consTeamLeaderName" },
    { label: "施工队负责人联系方式", value: "consTeamLeaderTel" },
    { label: "项目经理", value: "consTeamManagerName" },
  ],
};

// 设备信息-汇总信息、设备明细
export const deviceProjectInfoConfig = {
  component: Description,
  config: [
    { label: "交流桩个数（踏勘）", value: "alternatingPileCount" },
    {
      label: "实际安装交流桩个数（维保通）",
      value: "realAlternatingPileCount",
    },
    { label: "交流枪个数（踏勘）", value: "alternatingGunCount" },
    { label: "实际安装交流枪个数（维保通）", value: "realAlternatingGunCount" },
    { label: "直流桩个数（踏勘）", value: "directPileCount" },
    { label: "实际安装直流桩个数（维保通）", value: "realDirectPileCount" },

    { label: "直流枪个数（踏勘）", value: "directGunCount" },
    { label: "实际安装直流枪个数（维保通）", value: "realDirectGunCount" },
    { label: "交流桩功率（KW）（踏勘）", value: "alternatingPilePower" },
    {
      label: "实际安装交流桩功率（KW）（维保通）",
      value: "realAlternatingPilePower",
    },
    { label: "交流桩总功率（KW）（踏勘）", value: "alternatingPileTotalPower" },
    {
      label: "实际安装交流桩总功率（KW）（维保通）",
      value: "realAlternatingPileTotalPower",
    },
    { label: "直流桩功率（KW）（踏勘）", value: "directPilePower" },
    {
      label: "实际安装直流桩功率（KW）（维保通）",
      value: "realDirectPilePower",
    },

    { label: "直流桩总功率（KW）（踏勘）", value: "directPileTotalPower" },
    {
      label: "实际安装直流桩总功率（KW）（维保通）",
      value: "realDirectPileTotalPower",
    },
    { label: "充电桩总功率（KW）（踏勘）", value: "totalPower" },
    { label: "实际安装充电桩总功率（KW）（维保通）", value: "realTotalPower" },
    { label: "总计安装充电桩数（个）（踏勘）", value: "totalPileCount" },
    { label: "实际安装充电桩数（个）（维保通）", value: "realPileCount" },
    {
      label: "施工供应商下单通知利旧桩数（个）（维保通）",
      value: "supplierOrderPileOldCount",
    },
    {
      label: "施工供应商下单通知设备总数（个）（维保通）",
      value: "supplierOrderPileCount",
    },
  ],
};

// 设备信息-明细
export const deviceDetailConfig = {
  component: Table,
  attrs: {
    "span-method": () => [1, 1],
  },
  config: [
    { label: "安装序号", value: "installNum" },
    { label: "设备编码", value: "deviceCode" },
    {
      label: "设备图片",
      value: "photo",
      element: "el-image",
      props: { width: "150px" },
    },
    {
      label: "设备类型",
      value: "deviceType",
      props: { width: "150px" },
      format: (v, _k, dicts) => {
        return dicts?.cm_sub_type?.find((i) => i.dictValue == v)?.dictLabel;
      },
    },
    {
      label: "上线时间（简道云）",
      value: "onlineTime",
      props: { width: "200px" },
    },
    { label: "踏勘单号", value: "surveyCode", props: { width: "150px" } },
    { label: "项目编号", value: "projectCode", props: { width: "150px" } },
    { label: "项目名称", value: "projectName", props: { width: "150px" } },
  ],
};

// 成本汇总
export const costConfig = {
  component: Description,
  config: [
    { label: "施工预计总价（元）", value: "consExpectTotalPrice" },
    { label: "主缆预计长度（米）", value: "mainCableExpectLength" },
    { label: "施工实际总价（元）", value: "consRealTotalPrice" },
    { label: "主缆实际长度（米）", value: "mainCableRealLength" },
    { label: "单桩施工预计成本(元)", value: "pileExpectCost" },
    { label: "主缆材质", value: "mainCableMaterial" },
    { label: "单桩施工实际成本(元)", value: "pileRealCost" },
    { label: "设备总成本（元）", value: "deviceTotalCost" },
    { label: "单瓦预计施工成本（元）", value: "tileExpectCost" },
    { label: "实际施工安装桩数（台）", value: "realPileCount" },
    { label: "单瓦实际施工成本（元）", value: "tileRealCost" },
    { label: "已付金额（元）", value: "paidAmount" },
    { label: "施工总金额（元）", value: "totalAmount" },
    { label: "未付金额（元）", value: "unpaidAmount" },
    { label: "已付比例", value: "paidRatio" },
  ],
};

// 成本明细
export const costlistConfig = {
  component: Description,
  config: [
    { label: "施工预计总价（元）", value: "consExpectTotalPrice" },
    { label: "主缆预计长度（米）", value: "mainCableExpectLength" },
    { label: "施工实际总价（元）", value: "consRealTotalPrice" },
    { label: "主缆实际长度（米）", value: "mainCableRealLength" },
    { label: "单桩施工预计成本(元)", value: "pileExpectCost" },
    { label: "主缆材质", value: "mainCableMaterial" },
    { label: "单桩施工实际成本(元)", value: "pileRealCost" },
    { label: "设备总成本（元）", value: "deviceTotalCost" },
    { label: "单瓦预计施工成本（元）", value: "tileExpectCost" },
    { label: "实际施工安装桩数（台）", value: "realPileCount" },
    { label: "单瓦实际施工成本（元）", value: "tileRealCost" },
    { label: "已付金额（元）", value: "paidAmount" },
    { label: "施工总金额（元）", value: "totalAmount" },
    { label: "未付金额（元）", value: "unpaidAmount" },
    { label: "已付比例", value: "paidRatio" },
  ],
};
