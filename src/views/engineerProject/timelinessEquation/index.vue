<!-- //项目时效统计 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
      :tabRadioList="tabRadioList"
      @tabRadioChange="tabRadioChange"
      tabType="card"
    >
      <template #toolbar_buttons> </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['engineerProject:timelinessEquation:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="fold_header" slot-scope="{ row, column }">
        <div @click="handleCollapse(column.property)" style="cursor:pointer">
          <span>{{ column.title }}</span>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/engineerProject/timelinessEquation.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { regionData } from "element-china-area-data";
import { listAllUser } from "@/api/common.js";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
import moment from "moment";
import dictMixin from "../dictMixin";
export default {
  name: "timelinessEquation",
  mixins: [exportMixin, dictMixin],
  data() {
    return {
      activeTab: "全部",
      //buse参数-s

      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        // toolbarConfig: {
        //   custom: true,
        //   slots: {
        //     buttons: "toolbar_buttons",
        //   },
        // },
        rowConfig: {
          keyField: "groupId",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "orderNotification",
      //buse参数-e
      deptOptionList: [],
      projectStatusOptions: [],
      allProjectStatusOptions: [],
      workApplyStatusOptions: [],
      deviceOrderStatusOptions: [],
      processNodeOptions: [],
      constructTypeOptions: [],
      deviceSourceTypeList: [],
      engineeringManagerOptions: [],
      consTeamManagerOptions: [],
      consTeamLeaderOptions: [],
      workTypeOptions: [],
      managerOptions: [],
      applyFormTypeOptions: [],
      omApplyNoOptions: [],
      selectedData: [],
      foldObj: {
        baseInfo: false,
        timeInfo: false,
        costInfo: false,
        statusInfo: false,
      },
    };
  },

  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    this.getDeptList();
    this.getDicts("device_source_type").then((response) => {
      this.deviceSourceTypeList = response.data;
    });
    this.getDicts("apply_form_type").then((response) => {
      this.applyFormTypeOptions = response.data;
    });
    this.getManagerOptions();
    this.listAllUser();
    Promise.all([
      this.getDicts("project_business_type").then((response) => {
        this.businessTypeOptions = response.data;
      }),
      this.getDicts("project_status").then((response) => {
        this.allProjectStatusOptions = response.data;
        this.projectStatusOptions = this.allProjectStatusOptions;
      }),
      this.getDicts("work_apply_status").then((response) => {
        this.workApplyStatusOptions = response.data;
      }),
      this.getDicts("device_order_status").then((response) => {
        this.deviceOrderStatusOptions = response.data;
      }),

      this.getDicts("project_process_node").then((response) => {
        this.processNodeOptions = response.data;
      }),
      this.getDicts("construct_type").then((response) => {
        this.constructTypeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //获取经理角色下拉选项
    getManagerOptions() {
      api.listRoleAccount({ roleKey: "projectManage" }).then((res) => {
        this.engineeringManagerOptions = res.data?.map((x) => {
          return {
            value: x.nickName,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
      api.listRoleAccount({ roleKey: "consTeamProjectManager" }).then((res) => {
        this.consTeamManagerOptions = res.data?.map((x) => {
          return {
            value: x.nickName,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
      api.listRoleAccount({ roleKey: "consTeamLeader" }).then((res) => {
        this.consTeamLeaderOptions = res.data?.map((x) => {
          return {
            value: x.nickName,
            label: x.userName + "-" + x.nickName,
          };
        });
      });
    },
    getTagType(projectType) {
      const arr = [
        { type: "success", status: "新建" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    toDetail(row) {
      this.$router.push({
        name: "investBatchList",
        params: {
          surveyCodeMain: row.surveyCodeMain,
          surveyCodeSub: row.surveyCodeSub,
        },
      });
    },
    tabRadioChange(val) {
      this.activeTab = val;
      if (val == "1") {
        this.projectStatusOptions = this.allProjectStatusOptions?.filter(
          (x) => x.dictLabel !== "待评价" && x.dictLabel !== "已评价"
        );
      } else {
        this.projectStatusOptions = this.allProjectStatusOptions;
      }
      this.handleQuery();
    },
    handleCollapse(type) {
      this.foldObj[type] = !this.foldObj[type];
    },
    checkPermission,
    handleExport() {
      const params = {
        ...this.params,
        projectStatusList: this.projectStatusOptions?.map((x) => x.dictValue),
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.handleCommonExport(api.exportDurationStatistics, params);
    },

    async loadData() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        projectStatusList: this.projectStatusOptions?.map((x) => x.dictValue),
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.queryDurationStatistics(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "surveyPassTime",
          title: "踏勘通过时间",
          startFieldName: "surveyPassStartTime",
          endFieldName: "surveyPassEndTime",
        },
        {
          field: "consCheckPassTime",
          title: "施工验收通过时间",
          startFieldName: "consCheckPassStartTime",
          endFieldName: "consCheckPassEndTime",
        },
        {
          field: "onlineTime",
          title: "上线时间",
          startFieldName: "onlineStartTime",
          endFieldName: "onlineEndTime",
        },
        {
          field: "warrantyExpiresTime",
          title: "质保到期时间",
          startFieldName: "warrantyExpiresStartTime",
          endFieldName: "warrantyExpiresEndTime",
        },
        {
          field: "consCompleteTime",
          title: "施工完成时间",
          startFieldName: "consCompleteStartTime",
          endFieldName: "consCompleteEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    handleReset() {
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
  },
  computed: {
    tabRadioList() {
      return [
        { value: "0", id: "0", label: "全部" },
        { value: "1", id: "1", label: "进行中" },
      ];
    },
    tableColumn() {
      return [
        {
          field: "businessTypeName",
          title: "业务类型",
          width: 150,
        },
        {
          field: "stationName",
          title: "场站名称",
          width: 180,
        },
        { field: "stationLocation", title: "场站位置", width: 150 },
        {
          field: "surveyCodeMain",
          title: "踏勘主编码",
          width: 220,
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toDetail(row),
                  }}
                >
                  {row.surveyCodeMain}
                </el-link>,
              ];
            },
          },
        },
        {
          field: "surveyCodeSub",
          title: "踏勘子编码",
          width: 180,
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toDetail(row),
                  }}
                >
                  {row.surveyCodeSub}
                </el-link>,
              ];
            },
          },
        },
        {
          field: "projectType",
          title: "项目类型",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-tag
                  type={this.getTagType(row.projectType)}
                  size="medium"
                  effect="plain"
                  style={"font-size: 16px;"}
                >
                  {row.projectType}
                </el-tag>,
              ];
            },
          },
        },
        { field: "constructType", title: "投建类型", width: 100 },
        { field: "stationCode", title: "场站编码", width: 180 },
        { field: "projectCode", title: "项目编码", width: 150 },
        {
          field: "projectProcessNodeName",
          title: "项目流程节点",
          width: 150,
        },
        {
          field: "projectStatusName",
          title: "项目状态",
          width: 150,
        },
        {
          field: "workApplyStatusName",
          title: "开工申请状态",
          width: 150,
        },
        {
          field: "deviceOrderStatusName",
          title: "设备下单状态",
          width: 150,
        },
        { field: "totalPileCount", title: "设备数", width: 100 },
        { field: "orgName", title: "归属大区", width: 150 },
        {
          field: "engineeringManagerName",
          title: "工程经理",
          width: 150,
        },
        {
          field: "businessDevelopmentName",
          title: "商务BD",
          width: 150,
        },
        { field: "consTeamName", title: "施工队", width: 150 },
        {
          field: "consTeamLeaderName",
          title: "施工队负责人",
          width: 150,
        },
        {
          field: "consTeamManagerName",
          title: "施工队项目经理",
          width: 150,
        },
        { field: "surveyPassTime", title: "踏勘通过时间", width: 170 },
        {
          field: "surveyDuration",
          title: "踏勘审批耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
              简道云踏勘工单的提交时间→踏勘工单的审批结束时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "surveyTimeoutDuration",
          title: "踏勘审批超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
             超过维保通【踏勘审批时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>{row.surveyTimeoutDuration}</div>,
              ];
            },
          },
        },

        {
          field: "consSubmitDuration",
          title: "投建协议提交耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
             踏勘工单的审批结束时间→维保通需求池的工单受理时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "consSubmitTimeoutDuration",
          title: "投建协议提交超时时长",
          width: 180,
          titlePrefix: {
            message: `来源说明：
            超过【投建协议提交时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.consSubmitTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "sfApprovalDuration",
          title: "商服审核投建协议耗时",
          width: 180,
          titlePrefix: {
            message: `来源说明：
            维保通需求池的工单受理时间→项目批次关联的投建协议申请单的提交时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "sfApprovalTimeoutDuration",
          title: "商服审核投建协议超时时长",
          width: 170,
          titlePrefix: {
            message: `来源说明：
            超过【商服审核投建协议时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.sfApprovalTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "consAgreementApprovalDuration",
          title: "投建协议审批耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            运管投建协议申请单的提交时间→运管投建协议申请单的审批完结时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "consApprovalTimeoutDuration",
          title: "投建协议审批超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【投建协议运管审批时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.consApprovalTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "stampDuration",
          title: "对方盖章耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            运管投建协议申请单的审批完结时间→维保通商服点击投建协议已盖章时填写的协议双章时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "stampTimeoutDuration",
          title: "对方盖章超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【对方盖章时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>{row.stampTimeoutDuration}</div>,
              ];
            },
          },
        },
        {
          field: "sfCheckDuration",
          title: "商服复核耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            维保通商服点击投建协议已盖章时填写的协议双章时间→维保通商服点击投建协议已盖章的确认时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "sfCheckTimeoutDuration",
          title: "商服复核超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【商服复合时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.sfCheckTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "consOrderApplyDuration",
          title: "工程施工下单申请耗时",
          width: 180,
          titlePrefix: {
            message: `来源说明：
            维保通商服点击投建协议已盖章的确认时间→工程在维保通点击供应商下单通知的提交时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "consOrderApplyTimeoutDuration",
          title: "工程施工下单申请超时时长",
          width: 170,
          titlePrefix: {
            message: `来源说明：
            超过【工程施工下单申请时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.consOrderApplyTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "purchaseDuration",
          title: "采购发包耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            工程在维保通点击供应商下单通知的提交时间→采购在运管提交固定资产采购申请单（施工下单）的提交时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "purchaseTimeoutDuration",
          title: "采购发包超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【采购发包时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.purchaseTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "supplierOrderApprovalDuration",
          title: "单项目施工合同下单时间",
          width: 170,
          titlePrefix: {
            message: `来源说明：
            采购在运管提交固定资产采购申请单（施工下单）的提交时间→固定资产采购申请单（施工下单）运管审批结束时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "supplierOrderTimeoutDuration",
          title: "单项目施工合同下单超时时长",
          width: 170,
          titlePrefix: {
            message: `来源说明：
            超过【单项目施工合同下单时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.supplierOrderTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "consPrepareDuration",
          title: "施工准备耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            采购在运管提交固定资产采购申请单（施工下单）的提交时间→施工队在维保通提交开工申请的时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "consPrepareTimeoutDuration",
          title: "施工准备超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【施工准备时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.consPrepareTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "workApplyDuration",
          title: "开工申请耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            施工队在维保通提交开工时间的时间→开工申请审核通过时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "workApplyTimeoutDuration",
          title: "开工申请超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
           超过【开工申请时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.workApplyTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "constructionDuration",
          title: "施工耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            开工申请审核通过时间→所有工序项处理提交的时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "constructionTimeoutDuration",
          title: "施工超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【施工时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.constructionTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "constructionCheckDuration",
          title: "施工验收耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            所有工序项处理提交的时间→施工验收审核通过时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "consCheckTimeoutDuration",
          title: "施工验收超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【施工验收时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.consCheckTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "completedCheckDuration",
          title: "竣工验收耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            施工验收审核通过时间→竣工报告审核通过时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "completedCheckTimeoutDuration",
          title: "竣工验收超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【竣工验收耗时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>
                  {row.completedCheckTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "onlineDuration",
          title: "上线耗时",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            竣工报告验收通过时间→简道云上线工单审批结束时间。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "onlineTimeoutDuration",
          title: "上线超时时长",
          width: 160,
          titlePrefix: {
            message: `来源说明：
            超过【上线耗时时效】的标准时效后，开始计算超时时长。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
          slots: {
            default: ({ row }) => {
              return [
                <div style={{ color: "red" }}>{row.onlineTimeoutDuration}</div>,
              ];
            },
          },
        },
        {
          field: "warrantyExpiresTime",
          title: "质保到期时间",
          width: 170,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "场站编码",
          },
          {
            field: "stationName",
            element: "el-input",
            title: "场站名称",
          },
          {
            field: "region",
            title: "所在区域",
            element: "custom-cascader",
            attrs: {
              props: {
                checkStrictly: true,
                multiple: true,
                collapseTags: true,
              },
              clearable: true,
              options: regionData,
            },
          },
          {
            field: "projectProcessNode",
            title: "项目节点",
            element: "el-select",
            props: {
              options: this.processNodeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "projectStatus",
            title: "项目状态",
            element: "el-select",
            props: {
              options: this.projectStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "surveyPassTime",
            title: "踏勘通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "workApplyStatus",
            title: "开工申请状态",
            element: "el-select",
            props: {
              options: this.workApplyStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "constructType",
            title: "投建类型",
            element: "el-select",
            props: {
              options: this.constructTypeOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "consCompleteTime",
            title: "施工完成时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "surveyCodeMain",
            element: "el-input",
            title: "踏勘编码",
          },
          {
            field: "surveyCodeSub",
            element: "el-input",
            title: "踏勘子编码",
          },
          {
            field: "consCheckPassTime",
            title: "验收通过时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "projectCode",
            element: "el-input",
            title: "项目编码",
          },
          {
            field: "orgNo",
            title: "归属大区",
            element: "el-select",
            props: {
              options: this.deptOptionList,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
          },
          {
            field: "onlineTime",
            title: "上线时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "engineeringManagerName",
            title: "工程经理",
            element: "el-select",
            props: {
              options: this.engineeringManagerOptions,
              filterable: true,
              clearable: true,
            },
          },
          {
            field: "consTeamName",
            element: "el-input",
            title: "施工队",
          },
          {
            field: "warrantyExpiresTime",
            title: "质保到期时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "consTeamManagerName",
            title: "施工队项目经理",
            element: "el-select",
            props: {
              options: this.consTeamManagerOptions,
              filterable: true,
              clearable: true,
            },
          },
          {
            field: "consTeamLeaderName",
            title: "施工队负责人",
            element: "el-select",
            props: {
              options: this.consTeamLeaderOptions,
              filterable: true,
              clearable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        menu: false,
        addBtn: false,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
</style>
