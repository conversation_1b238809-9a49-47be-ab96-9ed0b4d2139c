<template>
  <div class="app-container">
    <el-dialog
      title="编辑账号数据权限"
      width="50%"
      :visible.sync="visible"
      @close="cancel"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :center="false"
    >
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane
          :label="i.label"
          :name="i.value"
          v-for="i in tabList"
          :key="i.value"
        ></el-tab-pane>
      </el-tabs>

      <div class="center-content">
        <SingleTree
          title="配置省市区"
          :list="data"
          :defaultProps="{
            children: 'children',
            label: 'areaName',
          }"
          v-model="checkedData"
          node-key="areaCode"
          class="tab-content-left"
          :loading="rightLoading"
          :defaultExpandAll="false"
          v-if="activeName == 'city'"
        >
        </SingleTree>
        <!-- <TransferTreeMul
          :cascadeData="data"
          ref="transferTreeMul"
          :titles="titles"
          :rightLoading="rightLoading"
          :leftLoading="leftLoading"
          :defaultExpandAll="true"
          v-model="checkedData"
          v-if="activeName == 'city'"
        ></TransferTreeMul> -->
        <TransferTree
          :cascadeData="data"
          ref="transferTree"
          :titles="titles"
          :rightLoading="rightLoading"
          :leftLoading="leftLoading"
          :defaultExpandAll="true"
          :leftCheckedKey="leftCheckedKey"
          v-else
        ></TransferTree>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  loadCityList,
  loadBusinessList,
  loadOrgList,
  saveCityPerms,
  saveOrgPerms,
  saveBusinessTypePerms,
  loadUnbindCityList,
} from "@/api/engineerProject/setting";
import SingleTree from "@/components/TransferTree/singleTree.vue";

import { deptAllTreeSelect } from "@/api/system/dept";
import TransferTree from "@/components/TransferTree/index.vue";
import TransferTreeMul from "@/components/TransferTree/index2.vue";
import { regionTreeList } from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
export default {
  components: { TransferTree, SingleTree },
  data() {
    return {
      checkedData: [],
      visible: false,
      activeName: "city",
      tabList: [
        {
          label: "城市",
          value: "city",
          title: "省市区",
        },
        {
          label: "组织",
          value: "org",
          title: "组织",
        },
        {
          label: "业务类型",
          value: "businessType",
          title: "业务类型",
        },
      ],

      value: [],
      allOrg: [],
      allBusiness: [],
      allCity: [],

      rightLoading: false,
      leftLoading: false,

      leftCheckedKey: [],
      userId: "",
    };
  },
  computed: {
    titles() {
      const tab = this.tabList.find((i) => i.value === this.activeName);
      return [`未配置${tab.title}`, `已配置${tab.title}`];
    },
    data() {
      if ("org" === this.activeName) {
        return this.allOrg;
      } else if ("businessType" === this.activeName) {
        return this.allBusiness;
      } else if ("city" === this.activeName) {
        return this.allCity;
      }
      return [];
    },
  },
  async mounted() {
    this.leftLoading = true;
    const [allOrg, allBusiness, allCity] = await Promise.all([
      deptAllTreeSelect(),
      this.getDicts("project_business_type"),
      regionTreeList(),
    ]);
    this.allOrg = this.treelistFormat(allOrg.data, 0, "", "deptId", "deptName");
    this.allBusiness = allBusiness.data.map((i) => ({
      id: i.dictValue,
      label: i.dictLabel,
      pid: "",
      children: [],
    }));
    this.allCity = allCity.data;
    this.traverseArr(this.allCity);
    // this.allCity = this.treelistFormat(regionData);
    // this.allCity = this.treelistFormat(
    //   allCity.data,
    //   0,
    //   "",
    //   "areaCode",
    //   "areaName"
    // );
    this.leftLoading = false;
  },
  methods: {
    traverseArr(arr, pid) {
      arr?.forEach((obj) => {
        // 添加id和label
        obj.id = obj.areaCode;
        obj.label = obj.areaName;
        if (pid) {
          obj.pid = pid;
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children, obj.areaCode);
        } else {
          delete obj.children;
        }
      });
    },
    treelistFormat(list, level = 0, pid = "", id = "value", name = "label") {
      const formatlist = list.map((i) => {
        let newitem = {
          id: i[id],
          label: i[name],
          pid,
          level,
          children: i?.children || [],
        };
        if (newitem?.children?.length) {
          newitem.children = this.treelistFormat(
            i.children,
            level + 1,
            i[id],
            id,
            name
          );
        }
        return newitem;
      });
      return formatlist;
    },
    tree2Arr(list, id = "value") {
      const arr =
        list?.map((i) => {
          if (!i?.children?.length) {
            return i[id];
          } else {
            return this.tree2Arr(i.children, id);
          }
        }) || [];
      return arr.flat(Infinity);
    },
    async loadValue() {
      this.rightLoading = true;
      if ("org" === this.activeName) {
        let res = await loadOrgList({ userId: this.userId });
        if (res.code === "10000") {
          this.leftCheckedKey = this.tree2Arr(res.data, "deptId");
        }
      } else if ("city" === this.activeName) {
        let res = await loadCityList({ userId: this.userId });
        if (res.code === "10000") {
          // let res1 = await loadUnbindCityList({ userId: this.userId });
          // let leftData = res1?.data || [];
          // this.traverseArr(leftData);
          // this.leftCheckedKey = this.tree2Arr(res.data, "areaCode");
          this.checkedData = res.data?.cityBindList || [];
          // this.traverseArr(this.checkedData);
          // this.$nextTick(() => {
          //   this.$refs.transferTreeMul.getDefaultLeftData(leftData);
          // });
        }
      } else if ("businessType" === this.activeName) {
        let res = await loadBusinessList({ userId: this.userId });
        if (res.code === "10000") {
          this.leftCheckedKey = this.tree2Arr(res.data, "businessType");
        }
      }
      this.rightLoading = false;
    },
    async handleClick() {
      if (this.activeName == "city") {
        this.loadValue();
        return;
      }
      this.$nextTick(() => {
        this.$refs.transferTree.getDefaultLeftData();
        this.$refs.transferTree.rightData = [];
      });
      await this.loadValue();
      this.$refs.transferTree.setRightData();
    },
    async open(userId) {
      this.visible = true;
      this.userId = userId;
      await this.handleClick();
    },
    cityValueFormat(list, p = {}) {
      let arr = list?.map((i) => {
        let key =
          i.id.length == 2 ? "province" : i.id.length == 4 ? "city" : "country";
        let pre = i.id.length == 2 ? {} : p;
        if (!i?.children?.length) {
          const provinceObj = this.allCity.find((c) => c.id === pre.province);
          const cityObj = provinceObj.children.find((c) => c.id === pre.city);
          const countryObj = cityObj.children.find((c) => c.id === i.id);
          return {
            userId: this.userId,
            [key]: i.id,
            ...pre,
            provinceName: provinceObj?.label,
            cityName: cityObj?.label,
            countryName: countryObj?.label,
          };
        } else {
          return this.cityValueFormat(i.children, {
            ...pre,
            [key]: i.id,
          });
        }
      });
      return arr.flat(Infinity);
    },
    orgValueFormat(list, p = {}) {
      let arr = list?.map((i) => {
        let key =
          i.level == 0 ? "orgNoOne" : i.level == 1 ? "orgNoTwo" : "orgNoThree";
        let pre = i.level == 0 ? {} : p;
        if (!i?.children?.length) {
          const orgNoOneObj = this.allOrg.find((c) => c.id === pre.orgNoOne);
          const orgNoTwoObj = (orgNoOneObj?.children || []).find(
            (c) => c.id === pre.orgNoTwo || c.id === i.id
          );
          const orgNoThreeObj = (orgNoTwoObj?.children || []).find(
            (c) => c.id === i.id
          );
          return {
            userId: this.userId,
            [key]: i.id,
            ...pre,
            orgNoOneName: orgNoOneObj?.label || "",
            orgNoTwoName: orgNoTwoObj?.label || "",
            ...(orgNoThreeObj?.label
              ? { orgNoThreeName: orgNoThreeObj?.label }
              : {}),
          };
        } else {
          return this.orgValueFormat(i.children, {
            ...pre,
            [key]: i.id,
          });
        }
      });
      return arr.flat(Infinity);
    },
    businessValueFormat(list) {
      return list?.map((i) => ({
        businessType: i.id,
        businessName: i.label,
        userId: this.userId,
      }));
    },
    async submit() {
      let params = [];
      let res = {};
      if ("city" === this.activeName) {
        params = { countyList: this.checkedData, userId: this.userId };
        res = await saveCityPerms(params);
      } else if ("org" === this.activeName) {
        const data = this.$refs.transferTree.rightData;
        params = this.orgValueFormat(data);
        if (!params.length) {
          params = [{ userId: this.userId }];
        }
        res = await saveOrgPerms(params);
      } else if ("businessType" === this.activeName) {
        const data = this.$refs.transferTree.rightData;
        params = this.businessValueFormat(data);
        if (!params.length) {
          params = [{ userId: this.userId }];
        }
        res = await saveBusinessTypePerms(params);
      }
      if (res.code === "10000") {
        this.$message.success("保存成功");
        "businessType" === this.activeName && this.cancel();
      }
    },
    cancel() {
      this.visible = false;
      this.activeName = "city";
      this.leftCheckedKey = [];
    },
  },
};
</script>

<style lang="less" scoped>
.center-content {
  display: flex;
  align-items: center;
  justify-content: center;
  /deep/ .el-transfer-panel {
    width: 100%;
  }
  /deep/ .el-transfer-panel__body {
    height: 60vh;
  }
}
</style>
