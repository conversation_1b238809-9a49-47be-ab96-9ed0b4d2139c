// 时效配置
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['engineerProject:timeConfig:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template #statusChange="{ row }"
        ><el-switch
          v-model="row.status"
          active-value="0"
          inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['engineerProject:timeConfig:status'])"
        >
        </el-switch
      ></template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/engineerProject/timeConfig.js";
import { queryBindOrderTypeList } from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
export default {
  name: "engineerTimeConfig",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "projectBusinessName",
          title: "业务类型",
          width: 120,
        },
        {
          field: "agingName",
          title: "时效名称",
          width: 150,
        },
        {
          field: "standardAging",
          title: "标准时效(h)",
          width: 120,
        },
        {
          field: "calcMethod",
          title: "计算方法",
          width: 120,
          formatter: ({ cellValue }) => {
            return cellValue == "0" ? "自然日" : "工作日";
          },
        },
        {
          field: "createByName",
          title: "创建人",
          width: 150,
          //   width: 250,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateByName",
          title: "修改人",
          width: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      agingTypeOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "projectBusinessType",
            title: "业务类型",
            element: "el-select",
            props: {
              options: this.businessTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "agingType",
            title: "时效名称",
            element: "el-select",
            props: {
              options: this.agingTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "停用", value: 1 },
                { label: "启用", value: 0 },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增时效规则",
        editBtn: checkPermission(["engineerProject:timeConfig:edit"]),
        editTitle: "编辑时效规则",
        delBtn: checkPermission(["engineerProject:timeConfig:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "projectBusinessType",
            title: "业务类型",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.businessTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择业务类型",
              },
            ],
          },
          {
            field: "agingType",
            title: "时效名称",
            element: "el-select",
            props: {
              options: this.agingTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择时效名称",
              },
            ],
          },
          {
            field: "standardAging",
            title: "时效标准",
            slots: {
              append: "h",
            },
            rules: [
              {
                required: true,
                message: "时效标准不允许为空！",
              },
              {
                pattern: /^(0|[1-9]\d{0,5}|999999)$/,
                message: "请输入正确的数字",
              },
            ],
            attrs: {
              placeholder: "请输入整数，允许为0",
            },
          },
          {
            field: "calcMethod",
            title: "计算方法",
            element: "el-radio-group",
            props: {
              options: [
                { value: "0", label: "自然日" },
                { value: "1", label: "仅工作日" },
              ],
            },
            defaultValue: "0",
            rules: [
              {
                required: true,
                message: "计算方法不允许为空！",
              },
            ],
          },
        ],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    Promise.all([
      this.getDicts("project_business_type").then((response) => {
        this.businessTypeOptions = response.data;
      }),
      this.getDicts("project_construct_aging_type").then((response) => {
        this.agingTypeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    //状态切换
    handleStatusChange(row) {
      // if (row.status == "0") {
      //   // this.$message.warning("自动派单的站点不能为空，请配置站点！");
      //   row.status = row.status == 1 ? 0 : 1;
      //   return;
      // }
      if (row.status == "1") {
        this.$confirm("是否确认停用？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then((res) => {
            this.updateStatus(row);
          })
          .catch(() => {
            row.status = row.status == "1" ? "0" : "1";
          });
      } else {
        this.updateStatus(row);
      }
    },
    updateStatus(row) {
      const text = row.status == "0" ? "启用" : "停用";
      const { updateTime, createTime, ...params } = row;
      api
        .update({ ...params, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.loadData();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const { updateTime, createTime, ...params } = formParams;
      const res = await api.update(params);
      if (res.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", { ...row });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
