<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="showAdd"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showUpdate(row)">
            编辑
          </el-button>
          <el-button type="text" size="large" @click="modifyStatus(row)">
            {{ row.isEnabled === "0" ? "启用" : "禁用" }}
          </el-button>
        </template>
        <template slot="preview" slot-scope="{ row }">
          <el-button type="text" @click="preview(row)">预览</el-button>
        </template>
      </GridTable>
    </el-card>

    <add
      v-if="visible"
      :visible.sync="visible"
      :title="title"
      :type="type"
      :template-id="templateId"
      @update-list="getList"
    />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import add from "./components/add.vue";
import GridTable from "@/components/GridTable/index.vue";
import { changeStatus, list } from "@/api/acceptanceTemplate/index";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "acceptanceTemplate",
  components: { AdvancedForm, GridTable, add },
  data() {
    return {
      config: [],
      businessTypeOption: [
        { dictLabel: "其他路测工单", dictValue: "otherRoadTest" },
        { dictLabel: "充电桩路测工单", dictValue: "pileRoadTest" },
        { dictLabel: "施工工单", dictValue: "construction" },
      ], //业务类型
      formStatusOptions: [
        {
          dictValue: "1",
          dictLabel: "启用",
        },
        {
          dictValue: "0",
          dictLabel: "禁用",
        },
      ],
      columns: [
        {
          field: "templateName",
          title: "模版名称",
          treeNode: true,
          width: 180,
        },
        {
          field: "businessType",
          title: "业务类型",
          width: 180,
          formatter: ({ cellValue, row, column }) => {
            return (
              this.businessTypeOption.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "storePath",
          title: "模版详情",
          width: 180,
          slots: { default: "preview" },
        },
        {
          field: "isEnabled",
          title: "状态",
          width: 180,
          formatter: ({ cellValue, row, column }) => {
            return (
              this.formStatusOptions.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "updateBy",
          title: "操作人",
          width: 180,
        },
        {
          field: "updateTime",
          title: "操作时间",
          width: 180,
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "templateId",
      visible: false,
      title: "新增",
      type: "add",
      templateId: undefined,
    };
  },
  async created() {
    this.initConfig();
    this.getList();
  },
  mounted() {
    Promise.all([
      // this.getFlowBusinessType(),
    ]);
  },
  methods: {
    preview(row) {
      window.open(row.storePath, "_blank");
    },
    modifyStatus(row) {
      const param = { ...row, isEnabled: row.isEnabled === "1" ? "0" : "1" };
      const text = param.isEnabled === "1" ? "启用" : "禁用";
      changeStatus(param)
        .then((res) => {
          if (res.code === "10000") {
            this.$message.success(text + "成功");
            this.getList();
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    async getFlowBusinessType() {
      //业务类型字典
      return await getDicts("flow_business_type").then((response) => {
        this.businessTypeOption = response?.data;
      });
    },
    showAdd() {
      this.title = "新增";
      this.type = "add";
      this.visible = true;
    },
    showUpdate(row) {
      this.title = "编辑";
      this.type = "update";
      this.templateId = row.templateId;
      this.visible = true;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      list(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "templateName",
          title: "模版名称",
          type: "input",
          placeholder: "请填写模版名称",
        },
        {
          key: "isEnabled",
          title: "状态",
          type: "select",
          options: this.formStatusOptions,
          placeholder: "请选择状态",
        },
      ];
    },
  },
};
</script>

<style scoped></style>
