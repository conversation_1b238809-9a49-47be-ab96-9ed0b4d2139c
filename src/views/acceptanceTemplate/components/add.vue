<!-- 新增施工队 -->
<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="50%"
  >
      <el-form :model="form" :rules="rules" ref="form" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="选择业务类型" prop="businessType">
              <el-select
                v-model="form.businessType"
                filterable
                clearable
                size="mini"
                placeholder="业务类型"
              >
                <el-option
                  v-for="item in businessTypeOption"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模版名称" prop="templateName">
              <el-input v-model="form.templateName" size="mini" :disabled="type === 'detail'" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
        <el-col :span="12">
            <el-form-item label="上传文件" prop="fileList">
              <el-upload
                class="upload-demo"
                ref="upload"
                :action="upload.url"
                :headers="upload.headers"
                :data="uploadParam"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                :before-upload="beforeAvatarUpload"
                :file-list="fileList"
                :accept="accept"
                multiple
                :limit="1"
                :auto-upload="false">
                <el-button :disabled="fileList.length == 1" slot="trigger" size="small" type="primary">选取文件</el-button>
                <el-button :disabled="fileList.length == 1" style="margin-left: 10px;" size="small" type="success" @click="submitUpload">开始上传</el-button>
                <div slot="tip" class="el-upload__tip">支持格式：.pdf ，单个文件不能超过30MB</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="submitInfo">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { add, detail, update } from "@/api/acceptanceTemplate/index";
import { getDicts } from "@/api/system/dict/data";
import { getToken } from '@/utils/auth';
export default {
  components: {  },
  props: ["visible", "type", "title","templateId"],
  data() {
    return {
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/doc/upload",
        // url: "http://charging-maintenance-server-alitest.leo.bangdao-tech.com/charging-test" + "/doc/upload",
      },
      uploadParam: {},
      fileList: [],
      accept: '.pdf',
      form: {
        templateName: undefined,
        businessType: undefined,
        fileList: []
      },
      rules: {
        templateName: [
          { required: true, message: "请填写模版名称", trigger: "blur" }
        ],
        businessType: [{ required: true, message: "请选择业务类型", trigger: "blur" }],
        fileList: [{ required: true, message: "请上传文件", trigger: "blur" }]
      },
      businessTypeOption: [
        {dictLabel: '其他路测工单', dictValue: 'otherRoadTest'},
        {dictLabel: '充电桩路测工单', dictValue: 'pileRoadTest'},
        {dictLabel: '施工工单', dictValue: 'construction'}
      ], //业务类型
    };
  },
  computed: {},
  watch: {},
  async created() {
    if (this.type !== "add") {
      this.getDetail();
    }
  },
  mounted() {
    Promise.all([
      // this.getFlowBusinessType(),
    ])
  },
  methods: {
    beforeAvatarUpload(file) {
      if (file.size / 1024 / 1024 > 30) {
        this.$message.error('文件大小不超过30M,请重新上传')
        this.fileList = []
        return false
      }
      this.uploadParam.fileList = file;
      return true
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    //文件上传成功
    handleSuccess(res, file, fileList) {
      if(res.code == 10000) {
        this.form.fileList.push(...res.data)
      }
    },
    handleRemove() {
      this.fileList = []
      this.form.fileList = []
    },
    async getFlowBusinessType() {
      //业务类型字典
      return await getDicts("flow_business_type").then((response) => {
        this.businessTypeOption = response?.data;
      });
    },
    getDetail() {
      let params = {
        templateId: this.templateId
      };
      detail(params).then(res => {
        if (res?.success) {
          this.form = res.data
          if (res.data.docList.length > 0) {
            this.form.fileList = res.data.docList.map(item => {return {...item, name: item.docName, url: item.storePath, uid: item.docId}})
            this.fileList = this.form.fileList
            console.log(this.form.fileList);
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    closeDialog() {
      this.$emit("update:visible", false);
    },
    submitInfo() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = {
            ...this.form
          };
          if (this.type === "add") {
            add(params).then(res => {
              if (res?.success) {
                this.closeDialog();
                this.$emit("update-list");
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          } else if (this.type === "update") {
            update(params).then(res => {
              if (res?.success) {
                this.closeDialog();
                this.$emit("update-list");
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
