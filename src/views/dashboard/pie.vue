<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
// require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "300px",
    },
    data: {
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      consTypeList: [],
      chart: null,
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.setOption(this.data);
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
    },
    //合并百分比小于0.01的为 “其他”
    getsData(_rowData) {
      var rowData = JSON.parse(JSON.stringify(_rowData));
      var sum = rowData.reduce(function (o, v, i) {
        o += parseFloat(v.value);
        return o;
      }, 0);
      if (isNaN(sum) || sum == 0) {
        return [];
      }
      rowData
        .sort(function (a, b) {
          return a.value - b.value;
        })
        .reverse();
      var val = 0; //其他的相加
      for (let i = 0; i < rowData.length; i++) {
        var _row = rowData[i],
          sub;
        sub = _row.value / sum;
        if (sub < 0.01) {
          rowData.length = i; //去掉最后一个
          val += parseFloat(_row.value);
        }
      }
      // //在后面追加一个其他的项
      if (val > 0) {
        let obj = {
          name: "其他",
          value: val,
        };
        rowData.push(obj);
      }
      return rowData;
    },

    setOption(data) {
      if (!data || (data.length && data.length == 0)) {
        this.chart.showLoading({
          text: "暂无数据",
          color: "#ffffff",
          textColor: "#8a8e91",
          maskColor: "rgba(255, 255, 255, 0.8)",
        });
      } else {
        this.chart.hideLoading();
      }

      let totle = 0;
      data.forEach((element) => {
        totle += Number(element.value);
      });

      this.chart.setOption({
        title: {
          text: "用户用能性质分布分析",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
        },

        series: [
          {
            label: {
              normal: {
                show: true,
                // formatter: "{c}({d}%)" //自定义显示格式(b:name, c:value, d:百分比)
                formatter: function (a) {
                  let vb = (a.value / totle) * 100;
                  return a.name + "(" + vb.toFixed(2) + "%" + ")";
                },
              },
            },
            type: "pie",
            radius: "50%",
            data: this.getsData(data),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      });
    },
  },
};
</script>
