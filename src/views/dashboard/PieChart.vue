<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts';
require('echarts/theme/macarons'); // echarts theme
import resize from './mixins/resize';

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    pieChartData: {
      default: () => {
        return []
      }
    },
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    pieChartData: {
      deep: true,
      handler(newVal) {
        this.setOptions(newVal);
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons');
      this.setOptions(this.pieChartData)
    },
    setOptions(val) {
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle'
        },
        series: [
          {
            // name: 'WEEKLY WRITE ARTICLES',
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['75%', '50%'],
            data: this.pieChartData,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold',
                formatter: "{b}\n" + "{c} " + "({d}%)"
              },
            },
          },
        ],
      });
    },
  }
};
</script>
