<template>
  <div class="dashboard-editor-container">
    <el-row :gutter="10">
      <el-col :xs="12" :sm="12" :lg="6">
        <el-card>
          <div class="top-wrap">
            <img :src="imgSrc1" class="top-img"/>
            <div class="right-wrap">
              <div>用户总数(户)</div>
              <div>
                <count-to
                  :start-val="0"
                  :end-val="Number(revenueData.totalUserNum)"
                  :duration="2600"
                  class="card-panel-num"
                  style="color: #4fb6ff"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6">
        <el-card>
          <div class="top-wrap">
            <img :src="imgSrc2" class="top-img"/>
            <div class="right-wrap">
              <div>今日营收(元)</div>
              <div>
                <count-to
                  :start-val="0"
                  :end-val="Number(revenueData.todayFund)"
                  :duration="2600"
                  class="card-panel-num"
                  style="color: #4fb6ff"
                  prefix="￥"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6">
        <el-card>
          <div class="top-wrap">
            <img :src="imgSrc3" class="top-img"/>
            <div class="right-wrap">
              <div>收费笔数(笔)</div>
              <div>
                <count-to
                  :start-val="0"
                  :end-val="Number(revenueData.chargeNum)"
                  :duration="2600"
                  class="card-panel-num"
                  style="color: #02a950"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6">
        <el-card>
          <div class="top-wrap">
            <img :src="imgSrc4" class="top-img"/>
            <div class="right-wrap">
              <div>当期水量(m³)</div>
              <div>
                <count-to
                  :start-val="0"
                  :end-val="Number(revenueData.currentYmWater)"
                  :duration="2600"
                  class="card-panel-num"
                  style="color: #f2867c"
                />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10" style="margin-top: 20px">
      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <div slot="header" class="card-title">
            <span class="colorspan"></span>
            <span>当前抄表情况（当月）</span>
          </div>
          <div class="flex-wrap">
            <!-- <gauge :optionData="gaugeData" /> -->
            <!-- <el-progress type="circle" :percentage="'25'" width="300" stroke-width="40"></el-progress> -->
            <PieChart :pieChartData="pieChartData1"/>
            <div class="right-wrap">
              <div class="pwrap">
                <div>
                  应抄用户(户)<br />
                  <span class="number" style="color:#ff9400;font-weight: bold;">{{ meterReadingData.shouldMeterReading }}</span>
                </div>
              </div>
              <div class="pwrap">
                <div>
                  已抄用户(户)<br />
                  <span class="number" style="color:#1bbe6b;font-weight: bold;">{{ meterReadingData.alreadyMeterReading }}</span>
                </div>
              </div>
              <div class="pwrap">
                <div>
                  未抄用户(户)<br />
                  <span class="number" style="color:#f4222d;font-weight: bold;">{{ meterReadingData.notMeterReading }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <div slot="header" class="card-title">
            <span class="colorspan"></span>
            <span>收费方式占比（当月）</span>
          </div>
          <div class="pie-wrap">
            <pie-chart :pieChartData="chargeWayData" width="50%" />
            <div class="pie-right">
              <p v-for="(item,index) in chargeWayData" :key="index" >
                <span class="item-legend" :style="{background: item.itemStyle.color}"></span>
                <span style="width:80px;"> {{ item.name }} </span>
                <span> {{ NP.round(NP.divide(item.value, pieCount) * 100, 2) }} %</span>
                <span> ￥{{ item.value }} </span>
              </p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row style="margin-top: 20px">
      <el-card class="box-card">
        <div slot="header" class="card-title">
          <span class="colorspan"></span>
          <span>当月营收统计</span>
        </div>
        <BarChart ref="BarChart" :xAxisData="xAxisData"  :seriesData="revenueStatistics" />
      </el-card>
    </el-row>
  </div>
</template>

<script>
import PanelGroup from "../dashboard/PanelGroup";
import LineChart from "../dashboard/LineChart";
import RaddarChart from "../dashboard/RaddarChart";
import PieChart from "../dashboard/PieChart";
import BarChart from "../dashboard/BarChart";
import gauge from "../dashboard/gauge";
import CountTo from "vue-count-to";
import {
  getRevenueData,
  getMeterReadingData,
  getChargeWayData,
  getRevenueStatistics
} from "@/api/system/index/data";

export default {
  name: "Index",
  components: {
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart,
    gauge,
    CountTo,
  },
  data() {
    return {
      imgSrc1: require("@/assets/icons/user.png"),
      imgSrc2: require("@/assets/icons/money.png"),
      imgSrc3: require("@/assets/icons/line.png"),
      imgSrc4: require("@/assets/icons/list.png"),
      pieChartData1: [
        // {value: 0, name: "已抄",itemStyle: {color: "#fe798c"}},
        // {value: 0, name: "未抄",itemStyle: {color: "#e9e9e9"}}
      ],
      pieChartData: [
        // { value: 320.33, name: "坐收", itemStyle: {color: "#2ac9b4"} },
        // { value: 240.22, name: "走收", itemStyle: {color: "#90c934"} },
        // { value: 149, name: "线上缴费", itemStyle: {color: "#ecf000"} },
        // { value: 100, name: "圈存机", itemStyle: {color: "#6ff29b"} },
        // { value: 59, name: "代扣", itemStyle: {color: "#f28755"} },
        // { value: 18, name: "其他", itemStyle: {color: "#c90011"} },
      ],
      pieCount: 0,
      // 营收数据
      revenueData: {
        totalUserNum: 0,
        todayFund: 0,
        chargeNum: 0,
        arrearageSum: 0,
      },
      // 抄表情况
      meterReadingData: {},
      // 收费方式占比
      chargeWayData: [],
      // 营收统计
      revenueStatistics: [],
      // 收费方式字典
      transTypeOptions: [],
      gaugeData: [
        {
          name: "抄表完成率",
          // value: meterReadingData.meterReadingRate,
          value: 1,
          gradient: ["#fe798c"],
        },
      ],
      xAxisData: [], //x轴坐标
      seriesData: [], //柱状图表数据
      
    };
  },
  methods: {
    mGetDate() {
      //获取当月天数--x轴坐标
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var d = new Date(year, month, 0);
      this.xAxisData = [];
      for (let i = 1; i <= d.getDate(); i++) {
        this.xAxisData.push(i + "日");
      }
    },
    getpieCount() {
      let count = 0
      this.chargeWayData.forEach(element => {
        count = this.NP.plus(count,element.value)
      });
      console.log('[ count ] >', count)
      this.pieCount = count
    },

    //营收数据
    async getRevenueData() {
      await getRevenueData().then(res => {
        if (res.code !== '10000') {
          this.msgError(res.message);
          return;
        }
        this.revenueData = res.data
      });
    },

    //营收统计
    async getRevenueStatistics() {
      await getRevenueStatistics().then(res => {
        if (res.code !== '10000') {
          this.msgError(res.message);
          return;
        }
        console.log("res.data==", res.data);
        res.data.forEach((item) => {
          const revenue = {}
          revenue.name = item.deptName
          revenue.type = 'bar'
          revenue.data = item.rcvAmts
          this.$nextTick(() => {
            this.revenueStatistics.push(revenue)
          })
        });
      });
    },
    //当前抄表情况
    async getMeterReadingData() {
      await getMeterReadingData().then(res =>{
        if (res.code !== '10000') {
          this.msgError(res.message);
          return;
        }
        if(res.data.shouldMeterReading === '0' && res.data.shouldMeterReading === '0'){
          res.data.meterReadingRate = 0;
        }
        this.meterReadingData = res.data
        if (Number(this.meterReadingData.alreadyMeterReading) > 0) {
          this.pieChartData1 = [
            {value: this.meterReadingData.alreadyMeterReading, name: "已抄",itemStyle: {color: "#fe798c"}},
            {value: this.meterReadingData.notMeterReading, name: "未抄",itemStyle: {color: "#e9e9e9"}},
          ]
        } else {
          this.pieChartData1 = [
            // {value: this.meterReadingData.alreadyMeterReading, name: "已抄",itemStyle: {color: "#fe798c"}},
            {value: this.meterReadingData.notMeterReading, name: "未抄",itemStyle: {color: "#e9e9e9"}},
          ]
        }
      });
    },

    //收费方式占比
    async getChargeWayData() {
      await getChargeWayData().then(res => {
        if (res.code !== '10000') {
          this.msgError(res.message);
          return;
        }
        res.data.forEach((item) => {
          const chargeWay = {}
          chargeWay.value = item.rcvAmt
          chargeWay.name = this.selectDictLabel(this.transTypeOptions, item.transType);
          if(item.transType === '01'){
            chargeWay.itemStyle = {color: "#2ac9b4"}
          }else if(item.transType === '02'){
            chargeWay.itemStyle = {color: "#90c934"}
          }else if(item.transType === '03'){
            chargeWay.itemStyle = {color: "#f28755"}
          }else if(item.transType === '04'){
            chargeWay.itemStyle = {color: "#ecf000"}
          }else if(item.transType === '05'){
            chargeWay.itemStyle = {color: "#c90011"}
          }else if(item.transType === '06'){
            chargeWay.itemStyle = {color: "#6ff29b"}
          }else if(item.transType === '07'){
            chargeWay.itemStyle = {color: "#ecf000"}
          }
          this.chargeWayData.push(chargeWay);
        });
      });
    },
    
  },
  async created() {
    await this.getDicts('ch_trans_type').then(response => {
      this.transTypeOptions = response.data;
    });
    this.mGetDate();
  },
  async mounted() {
    let that = this
    this.getConfigKey("sw_support_org").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setSwSupportOrg", "N");
      }else {
        that.$store.dispatch("app/setSwSupportOrg", response.data.configValue);
      }
    });
    this.getConfigKey("is_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setIsSupportOrgNo", "N");
      }else {
        that.$store.dispatch("app/setIsSupportOrgNo", response.data.configValue);
      }
    });
    this.getConfigKey("district_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setDistrictSupport", "N");
      }else {
        that.$store.dispatch("app/setDistrictSupport", response.data.configValue);
      }
    });
    this.getConfigKey("area_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setAreaSupport", "N");
      }else {
        that.$store.dispatch("app/setAreaSupport", response.data.configValue);
      }
    });
    this.getConfigKey("sect_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setSectSupport", "N");
      }else {
        that.$store.dispatch("app/setSectSupport", response.data.configValue);
      }
    });
    this.getConfigKey("wm_para_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setParaSupport", "N");
      }else {
        that.$store.dispatch("app/setParaSupport", response.data.configValue);
      }
    });
    this.getConfigKey("prc_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setPrcSupport", "N");
      }else {
        that.$store.dispatch("app/setPrcSupport", response.data.configValue);
      }
    });
    this.getConfigKey("late_fee_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setLateFeeSupport", "N");
      }else {
        that.$store.dispatch("app/setLateFeeSupport", response.data.configValue);
      }
    });
    this.getConfigKey("mtr_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setMtrSupport", "N");
      }else {
        that.$store.dispatch("app/setMtrSupport", response.data.configValue);
      }
    });
    this.getConfigKey("report_support_org_no").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setReportSupport", "N");
      }else {
        that.$store.dispatch("app/setReportSupport", response.data.configValue);
      }
    });
    this.getConfigKey("start_business_flow").then((response) => {
      if (response.data === null || response.data === undefined){
        that.$store.dispatch("app/setStartBusinessFlow", "N");
      }else {
        that.$store.dispatch("app/setStartBusinessFlow", response.data.configValue);
      }
    });
    await Promise.all([
      this.getRevenueData(),
      this.getRevenueStatistics(),
      this.getMeterReadingData(),
      this.getChargeWayData(),
    ])
    this.getpieCount()
  },
};
</script>

<style lang="less" scoped>
.dashboard-editor-container {
  padding: 20px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.right-write-wrap {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin: 20px 0;
  font-size: 12px;
}

.card-title {
  display: flex;
  align-items: center;
  .colorspan {
    width: 8px;
    height: 15px;
    display: inline-block;
    background: #409eff;
    margin-right: 10px;
  }
}
.flex-wrap {
  display: flex;
  justify-content: space-around;
  .right-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .pwrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;
      .number {
        color: #e6a23c;
        font-size: 23px;
      }
    }
  }
}
.top-wrap {
  display: flex;
  justify-content: space-around;
  .top-img {
    width: 25%;
  }
  .right-wrap {
    width: 60%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.card-panel-num {
  font-size: 30px;
  margin: 10px 0;
}
.mini-font,
.card-panel-num-mini {
  font-size: 13px;
}

.pie-wrap {
  display: flex;
  align-items: center;
  .item-legend {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 5px;
  }
}
.pie-right {
  display: inline-block;
  p{
    margin: 6px;
    padding: 0;
    span{
      margin-right: 5px;
      display: inline-block;
      width: 100px;
    }
  }
}

</style>
