<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from 'echarts';
// require('echarts/theme/macarons'); // echarts theme
import resize from './mixins/resize';

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    isMonth: {
      type: Boolean,
      default: false
    },
    xData: {
      type: Array,
      default: () => []
    },
    yMax:{
      type: Number,
      default: 500
    },
    interval: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      }
    }
  },
  created: function () {
    // if(this.isMonth) {
    //     this.xData = ['1月', '2月', '3月'];
    //     console.log(1);
    // }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons');
      this.setOptions(this.chartData);
    },
    isEmptyArr(arr) {
      return arr != null && arr.length === 0;
    },
    setOptions({ expectedData, actualData, xData } = {}) {
      // 实际和预期都没有数据
      const noData = this.isEmptyArr(expectedData) && this.isEmptyArr(actualData);
      const title = {
        text: this.title,
        padding: [15, 10, 5, 10]
      };
      let title2;
      if (noData) {
        title2 = {
          text: "暂无数据",
          x: "center",
          y: "center",
          show: noData,
          textStyle: {
            color: "#65ABE7",
            fontWeight: "normal",
            fontSize: 15
          }
        };
      }
      this.chart.setOption({
        xAxis: {
          data: xData,
          boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        title: noData ? [title, title2] : title,
        grid: {
          left: 10,
          right: 10,
          bottom: noData ? 40 : 20,
          top: 70,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [15, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          },
            min: 0,
            max: this.yMax,
            interval:this.interval,
            axisLabel: {
                formatter: '{value} K'
            }
        },
        legend: {
          data: ['实收金额', '应收金额']
        },
        series: [
          {
            name: '实收金额',
            itemStyle: {
              normal: {
                color: '#5BD8A6',
                lineStyle: {
                  color: '#5BD8A6',
                  width: 2
                }
              }
            },
            smooth: false,
            type: 'line',
            data: actualData,
            animationDuration: 2800,
            animationEasing: 'cubicInOut'
          },
          {
            name: '应收金额',
            smooth: true,
            type: 'line',
            itemStyle: {
              normal: {
                color: '#3888fa',
                lineStyle: {
                  color: '#3888fa',
                  width: 2
                },
                areaStyle: {
                  color: '#f3f8ff'
                }
              }
            },
            data: expectedData,
            animationDuration: 2800,
            animationEasing: 'quadraticOut'
          }
        ]
      });
    }
  }
};
</script>
