<!-- 环形chart -->
<template>
  <div class="">
    <dv-charts :option="option" :style="{ height: height, width: width }"/>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    width: {
      type: String,
      default: '300px'
    },
    height: {
      type: String,
      default: '300px'
    },
    optionData: {
      default: () => {
        return []
      }
    },
  },
  data() {
    //这里存放数据
    return {
      option: {
        title: {
          text: "剩余油量表",
          style: {
            fill: "#fff",
          },
        },
        series: [
          {
            type: "gauge",
            startAngle: -Math.PI / 2,
            endAngle: Math.PI * 1.5,
            arcLineWidth: 25,
            data: this.optionData,
            // data: [
            //   {
            //     name: "供热率",
            //     value: 65,
            //     gradient: ["#03c2fd", "#1ed3e5", "#2fded6"],
            //   },
            // ],
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            pointer: {
              show: false,
            },
            dataItemStyle: {
              lineCap: "round",
            },
            details: {
              show: true,
              formatter: "{name}\n" + "{value}%",
              style: {
                fill: "#1ed3e5",
                fontSize: 20,
              },
            },
          },
        ],
      },
    };
  },
  computed: {},
  watch: {
    optionData: {
      deep: true,
      handler(newVal) {
        this.option.series[0].data = newVal
      }
    }
  },
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>