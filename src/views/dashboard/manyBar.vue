<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import echarts from "echarts";
// require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";
import {echartsOptionToContentfunction} from '@/utils/index.js'

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "300px"
    },
    type:{
      type:String,
      default:'bar'
    },
    data: {
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.setOptions(this.data);
      });
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
    },
    setOptions(data) {
      if (!data || (data && data.length == 0)) {
        this.chart.showLoading({
          text: "暂无数据",
          color: "#ffffff",
          textColor: "#8a8e91",
          maskColor: "rgba(255, 255, 255, 0.8)"
        });
      } else {
        this.chart.hideLoading();
      }
      const labelValueMap = data.map(item => {
        return {
          label: item.name,
          value: {
            name: item.name,
            type: this.type?this.type:'bar',
            data: item.dataList.map(item => {
              return Number(item.value);
            })
          }
        };
      });
      const xData =
        data &&
        data.length &&
        data.length > 0 &&
        data
          .map((item, index) => {
            if (index === 0) {
              return item.dataList.map(item => {
                return item.period;
              });
            }
          })
          .filter(item => item);
      const value = labelValueMap.map(item => item.value);

      console.log("value??", value);
      this.chart.setOption({
        title: {
          text: this.title,
          left: "center"
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        toolbox: {
          //可视化的工具箱
          show: true,
          feature: {
            dataView: {
              //数据视图
              show: true,
              optionToContent:echartsOptionToContentfunction
            }
          }
        },
        legend: {
          data: labelValueMap.map(item => {
            return item.label;
          }),
          orient: "vertical",
          x: "right", //可设定图例在左、右、居中
          y: "center"
        },
        xAxis: [
          {
            type: "category",
            data: xData[0]
          }
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false
            }
          }
        ],
        series: value
      },true);
    }
  }
};
</script>
