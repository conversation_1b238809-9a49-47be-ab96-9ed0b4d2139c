<template>
  <div :class="className" :style="{ height: height, width: width }"/>
</template>

<script>
import echarts from "echarts";
// require('echarts/theme/macarons'); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "350px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    series: {
      type: Array,
      default: () => [],
    },
    dataZoom: {
      type: Array,
      default: () => [],
    },
    chartData : {
      type: Object,
      default: () => {
        return {}
      },
    },
    title: {
      type: String,
      default: "",
    },
    isMonth: {
      type: Boolean,
      default: false,
    },
    xData: {
      type: Array,
      default: () => [],
    },
    yMax: {
      type: Number,
      default: 500,
    },
    interval: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      chart: null,
      curSeries:[]
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if(val){
          console.log('-----1')
          this.series = val.series
          if(val.series.length > 0){
            this.initChartEnergyOne(val);
          }else {
            this.isEmptyChar();
          }
        }
      }
    }
  },
  created: function () {
    console.log('--------')
    // if(this.isMonth) {
    //     this.xData = ['1月', '2月', '3月'];
    //     console.log(1);
    // }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons", "dark");
      // this.setOptions();
      if (!this.series || (this.series && this.series.length == 0)) {
        this.isEmptyChar();
      } else {
        this.initChartEnergyOne(this.chartData)
      }
    },
    isEmptyChar(){
      this.chart.clear()
      this.chart.setOption( {
        title: {
          text: '暂无数据',
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: 25,
            fontWeight: 'normal',
            color:"#fff"
          }
        }
      })
    },
    isEmptyArr(arr) {
      return arr != null && arr.length === 0;
    },
    setOptions() {
      this.chart.setOption({
        // color: colors,
        textStyle: {
          color: '#9E9E9E'
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          right: "20%",
        },
        toolbox: {
          feature: {
            dataView: {show: true, readOnly: true},
          },
        },
        legend: {
          data: ["Evaporation", "Precipitation", "Temperature"],
        },
        xAxis: [
          {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            // prettier-ignore
            data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "设备名称1",
            position: "left",
            alignTicks: false,
            offset: 0,
            axisLine: {
              show: true,
              lineStyle: {
                // color: colors[0]
              },
            },
            axisLabel: {
              formatter: "{value} ml",
            },
            splitLine: {//修改折线图背景横线 颜色
              lineStyle: {
                // 设置背景横线
                color: "rgba(0, 0, 0, 0)",
              },
            }
          },
          {
            type: "value",
            name: "设备名称2",
            position: "left",
            alignTicks: false,
            offset: 30,
            axisLine: {
              show: true,
              lineStyle: {
                // color: colors[1]
              },
            },
            axisLabel: {
              formatter: "{value} ml",
            },
            splitLine: {
              lineStyle: {
                // 设置背景横线
                color: "rgba(0, 0, 0, 0)",
              },
            }
          },
          {
            type: "value",
            name: "设备名称3",
            position: "left",
            alignTicks: false,
            offset: 60,
            axisLine: {
              show: true,
              lineStyle: {
                // color: colors[2]
              },
            },
            axisLabel: {
              formatter: "{value} °C",
            },
            splitLine: {
              lineStyle: {
                // 设置背景横线
                color: "rgba(0, 0, 0, 0)",
              },
            }
          },
        ],
        series: this.series,
        dataZoom: this.dataZoom
      });
    },
    initChartEnergyOne(val) {
      this.chart.clear()
      this.chart.setOption(val);
    },
  },
};
</script>
