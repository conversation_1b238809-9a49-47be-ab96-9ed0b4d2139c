<!-- 小规格卡片-年度项目类型占比 -->
<template>
  <el-card class="card-wrap">
    <div class="card-left">
      <div>
        年度项目类型占比
      </div>
      <div style="margin:20px 0;">
        <count-to
          :start-val="0"
          :end-val="saleWaterSum || 0"
          :duration="2600"
          :decimals="0"
          class="card-panel-num"
          style="color: white"
        />
      </div>
    </div>
    <div class="card-right">
      <pie-chart :pieChartData="meterRateData" width="100%" height="100%" />
    </div>
  </el-card>
</template>

<script>
import CountTo from "vue-count-to";
import {projectTypeStatistics} from "@/api/homePage/index.js";
import PieChart from "@/views/dashboard/PieChart";

export default {
  components: {
    CountTo,
    PieChart
  },
  data() {
    return {
      colorList: ["#319033", "#ec6606", "#1799de", "#f6d705", "#9cffc0", "#f7ff0f"],
      saleWaterSum: 0,
      meterRateData: []
    };
  },
  computed: {},
  watch: {},
  methods: {
    format(val) {
      return `
        回收率${val}%
       `;
    },
    //本月售水
    projectTypeStatistics() {
      let params = {
        // orgNoList: this.$store.state.user.orgNoListId
      };
      projectTypeStatistics(params).then(res => {
        this.$nextTick(() => {
          let data = res.data;
          let number = 0
          if(data && data.length > 0) {
            data.forEach(element => {
              number += element.number 
            });
          }
          this.meterRateData = []
          this.saleWaterSum = number
          data.forEach((element,index) => {
            let obj = {
              value: (typeof element.number) == "string" ? Number(element.number) : element.number,
              name: element.projectType,
              itemStyle: { color: this.colorList[index] }
            }
            this.meterRateData.push(obj)
          });

          // this.meterRateData = [
          //   {
          //     value: res.data.generalSaleRate,
          //     // value: 25,
          //     name: "机械表",
          //     itemStyle: { color: "#319033" }
          //   },
          //   {
          //     value: res.data.centerCalcRete,
          //     //   value: 25,
          //     name: "中心计费",
          //     itemStyle: { color: "#ec6606" }
          //   },
          //   {
          //     value: res.data.externalCalcRate,
          //     //   value: 25,
          //     name: "表端计费",
          //     itemStyle: { color: "#1799de" }
          //   },
          //   {
          //     value: res.data.cardSaleRate,
          //     //   value: 25,
          //     name: "卡表",
          //     itemStyle: { color: "#f6d705" }
          //   }
          // ];
        });
      });
    }
  },
  created() {
    this.projectTypeStatistics();
  },
  mounted() {}
};
</script>
<style lang="less" scoped>
.card-wrap {
  opacity: 1;
  border-radius: 2px;
  background: linear-gradient(243deg, #D9A2FF -1%, #915FF6 100%);
  color: white;
  border-radius: 10px;
  /deep/.el-card__body {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-left {
    width: 40%;
  }
}
.bottom-wrap {
  display: flex;
}
.perwrap {
  width: 50%;
  display: flex;
}
.card-panel-num {
  font-size: 30px;
  margin: 10px 0;
}
.card-right {
  width: 280px;
  height: 145px;
}
.rightimg {
  width: 100%;
  height: 100%;
}
</style>
