<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
// import echarts from 'echarts';
// require('echarts/theme/macarons'); // echarts theme
import resize from '../mixins/resize';

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    series: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    isMonth: {
      type: Boolean,
      default: false
    },
    xAxis: {
      type: Object,
      default: () => {}
    },
    yMax:{
      type: Number,
      default: undefined
    },
    interval: {
      type: Number,
      default: undefined
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    series: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      }
    }
  },
  created: function () {
    // if(this.isMonth) {
    //     this.xData = ['1月', '2月', '3月'];
    //     console.log(1);
    // }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el, 'macarons');
      this.setOptions(this.series);
    },
    isEmptyArr(arr) {
      return arr != null && arr.length === 0;
    },
    setOptions() {
      this.chart.setOption({
        xAxis: this.xAxis,
        // title: noData ? [title, title2] : title,
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 40,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [15, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          },
            min: 0,
            max: this.yMax,
            interval:this.interval,
            axisLabel: {
                formatter: '{value} '
            }
        },
        legend: {
          data: ['水费回收率', '抄表率']
        },
        series: this.series,
        // series: [
          // {
          //   name: '实收金额',
          //   itemStyle: {
          //     normal: {
          //       color: '#5BD8A6',
          //       lineStyle: {
          //         color: '#5BD8A6',
          //         width: 2
          //       }
          //     }
          //   },
          //   smooth: false,
          //   type: 'line',
          //   data: actualData,
          //   animationDuration: 2800,
          //   animationEasing: 'cubicInOut'
          // },
          // {
          //   name: '应收金额',
          //   smooth: true,
          //   type: 'line',
          //   itemStyle: {
          //     normal: {
          //       color: '#3888fa',
          //       lineStyle: {
          //         color: '#3888fa',
          //         width: 2
          //       },
          //       areaStyle: {
          //         color: '#f3f8ff'
          //       }
          //     }
          //   },
          //   data: expectedData,
          //   animationDuration: 2800,
          //   animationEasing: 'quadraticOut'
          // }
        // ]
      });
    }
  }
};
</script>
