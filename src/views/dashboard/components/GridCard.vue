<!-- 快速导航 -->
<template>
  <div class="">
    <el-card class="top-middle-card">
      <div slot="header" class="clearfix">
        <span>快速导航</span>
        <el-button
            @click="openDialog"
            class="el-icon-set"
            icon="el-icon-setting"
        ></el-button>
      </div>
      <div v-if="pathList && pathList.length > 0" class="grid-wrap">
        <div
            class="grid-item"
            v-for="(item, index) in pathList"
            :key="index"
            @click.stop="goTo(item)"
            
        >
          <!--          <img class="grid-img" :src="img" alt=""/>-->
          <svg-icon
          :style="{background: gridItemBg[index]}"
              slot="prefix"
              :icon-class="item.icon"
              class="grid-img"
          />
          <span>{{ item.menuName }}</span>
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无数据" :image-size="120"></el-empty>
      </div>
    </el-card>

    <el-dialog
        title="快速导航"
        :visible.sync="visible"
        :close-on-click-modal="false"
        @close="closeCisible"
        append-to-body
        width="40%"
    >
      <el-tree
          :data="menuOptions"
          show-checkbox
          ref="menu"
          node-key="menuId"
          empty-text="加载中，请稍后"
          :props="defaultProps"
          :check-strictly="true"
      ></el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="closeCisible">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {bindMenu, getMenuList, listMenu as menuTreeselect} from "@/api/system/menu";

export default {
  data() {
    return {
      menuIdList: [], //已选中的菜单id数组
      pathList: [],
      visible: false,
      orgNo: undefined,
      deptOptions: [],
      // 菜单列表
      menuOptions: [],
      defaultProps: {
        children: "children",
        label: "menuName",
      },
      gridItemBg: [
        '#1890FF',
        '#6EDFA3',
        '#FF8095',
        '#1890FF',
        '#D19EFE',
        '#1890FF',
        '#A270F8',
        '#1890FF',
        '#FFB373',
      ]
    };
  },
  computed: {},
  watch: {},
  methods: {
    goTo(item) {
      this.$router.push({path: item.path})
    },
    openDialog() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.menu.setCheckedKeys(this.menuIdList);
      })
    },
    closeCisible() {
      this.visible = false;
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
        this.filterMenuOptions(this.menuOptions)
      });
    },
    //过滤tree
    filterMenuOptions(arr) {
      if (arr && arr.length > 0) {
        return arr.forEach((el) => {
          if (el.menuType !== "C") {
            this.$set(el, "disabled", true);
          }
          if (el.children && el.children.length > 0) {
            this.filterMenuOptions(el.children);
          }
        });
      }
    },
    submitForm() {
      let selectedNodes = this.$refs.menu.getCheckedNodes()
      this.menuIdList = []
      selectedNodes.forEach((item) => {
        this.menuIdList.push(item.menuId)
      })
      if (this.menuIdList.length > 9) {
        this.$message.info("最多绑定9个菜单")
        return
      }
      let param = {
        menuList: this.menuIdList
      }
      bindMenu(param).then((res) => {
        if (res.code == '10000') {
          this.closeCisible()
          this.loadMenu()
        }
      })

    },
    async loadMenu() {
      await getMenuList().then((res) => {
        this.pathList = res.data
        this.menuIdList = []
        this.pathList.forEach((menu) => {
          menu.path = "/" + menu.path
          this.menuIdList.push(menu.menuId)
        })
      })
    }
  },
  async created() {
    await this.loadMenu();
    this.getMenuTreeselect();
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.top-middle-card {
  border-radius: 10px;

  /deep/ .el-card__header {
    min-height: 54px;
    display: flex;
    align-items: center;
  }

  /deep/ .el-card__body {
    overflow-y: auto;
    height: 35vh;
  }
}

.clearfix {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.el-icon-set {
  border: none;
  position: absolute;
  right: 0;
  font-size: 20px;
}

.grid-wrap {
  display: flex;
  flex-wrap: wrap;

  overflow-y: auto;

  .grid-item {
    // width: 33.333333%;
    // // border: 1px solid red;
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    // justify-content: space-between;
    // padding-top: 30px;
    cursor: pointer;
    font-size: 14px;

    margin: 0px 5px 2px 5px;
    width: calc(100% / 3 - 10px);
    padding-top: calc((100% / 6 - 30px)/2);
    padding-bottom: calc((100% / 6 - 30px)/2);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;

    .grid-img {
      width: 40px;
      height: 40px;
      padding: 10px;
      margin-bottom: 5px;
      border-radius: 5px;
      background-color: #a3b0ffb5;
      color: white;
    }
  }
}

.el-tree {
  max-height: 350px;
  overflow: auto;
}
</style>
