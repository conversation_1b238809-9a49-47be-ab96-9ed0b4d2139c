<!-- 小规格卡片-年度项目完工情况 -->
<template>
  <el-card class="card-wrap">
    <div class="card-left">
      <div>年度项目完工情况
      </div>
      <div style="margin:20px 0;">
        <count-to
          :start-val="0"
          :end-val="Number(obj.a) || 0"
          :duration="2600"
          class="card-panel-num"
          style="color: white"
        />
        <span style="font-size: 28px;">/</span>
        <count-to
          :start-val="0"
          :end-val="Number(obj.b) || 0"
          :duration="2600"
          class="card-panel-num"
          style="color: white"
        />
      </div>
    </div>
    <div class="card-right">
      <el-progress class="rightimg" type="circle" :format="format" :color="colors" :percentage="obj.percentage" :stroke-width="12"></el-progress>
    </div>
  </el-card>
</template>

<script>
import CountTo from "vue-count-to";
import { projectCompleteStatistics } from "@/api/homePage/index.js";

export default {
  components: {
    CountTo,
  },
  data() {
    return {
      obj: {},
      colors: [
        {color: '#f56c6c', percentage: 20},
        {color: '#e6a23c', percentage: 40},
        {color: '#5cb87a', percentage: 60},
        {color: '#1989fa', percentage: 80},
        {color: '#6f7ad3', percentage: 100}
      ]
    };
  },
  computed: {},
  watch: {},
  methods: {
    format(val) {
      let info = `
        完工率${val}%
       `
      return info
    },
    //本月抄表
    projectCompleteStatistics() {
      let params = {
        // orgNoList: this.$store.state.user.orgNoListId
      }
      projectCompleteStatistics(params).then(res => {
          let percentage = this.NP.divide(res.data.completeNumber, res.data.totalNumber)
          percentage = Number(percentage) * 100
          percentage = Number(percentage.toFixed(2))
          this.obj = {
            a: res.data.completeNumber,
            b: res.data.totalNumber,
            percentage: percentage,
          }
      })
    },
  },
  created() {
    this.projectCompleteStatistics()
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.card-wrap {
  opacity: 1;
  border-radius: 2px;
  background: linear-gradient(243deg, #77E29C -1%, #23CBB7 100%);
  color: white;
  border-radius: 10px;
  /deep/.el-card__body {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-left {
    width: 60%;
  }
}
.bottom-wrap {
  display: flex;
}
.perwrap {
  width: 50%;
  display: flex;
}
.card-panel-num {
  font-size: 30px;
  margin: 10px 0;
}
.card-right {
  width: 125px;
  height: 145px;
}
.rightimg {
  width: 100%;
  height: 100%;
}
</style>
