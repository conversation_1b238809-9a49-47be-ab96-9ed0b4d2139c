<!-- 小规格卡片-本月水费回收 -->
<template>
  <el-card class="card-wrap">
    <div class="card-left">
      <div>本月水费回收<span style="font-size: 10px;"> (元)</span>
      </div>
      <div style="margin:20px 0;">
        <count-to
          :start-val="0"
          :end-val="Number(obj.a) || 0"
          :duration="2600"
          class="card-panel-num"
          style="color: white"
          prefix="￥"
        />
      </div>
    </div>
    <div class="card-right">
      <el-progress class="rightimg" v-if="obj.percentage" type="circle" :format="format" :color="colors" :percentage="obj.percentage" :stroke-width="12"></el-progress>
      <img v-else :src="obj.imgUrl" class="rightimg" alt="">
    </div>
  </el-card>
</template>

<script>
import CountTo from "vue-count-to";
import {getMonthFeeRecycle} from "@/api/homePage/index.js";

export default {
  components: {
    CountTo,
  },
  data() {
    return {
      obj: {},
      colors: [
          {color: '#f56c6c', percentage: 20},
          {color: '#e6a23c', percentage: 40},
          {color: '#5cb87a', percentage: 60},
          {color: '#1989fa', percentage: 80},
          {color: '#6f7ad3', percentage: 100}
        ]
    };
  },
  computed: {},
  watch: {},
  methods: {
    format(val) {
      return `回收率${val}%`;
    },
    //本月水费回收
    getMonthFeeRecycle() {
      let params = {
        orgNoList: this.$store.state.user.orgNoListId
      };
      getMonthFeeRecycle(params).then(res => {
        this.$nextTick(() => {
          this.obj = {
            a: res.data.rcvedAmt,
            percentage: res.data.recycleRate
          };
          console.info("本月水费回收", this.obj)
        });
      });
    }
  },
  created() {
    this.getMonthFeeRecycle()
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.card-wrap {
  opacity: 1;
  border-radius: 2px;
  background: linear-gradient(243deg, #D9A2FF -1%, #915FF6 100%);
  color: white;
  border-radius: 10px;
  /deep/.el-card__body {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-left {
    width: 60%;
  }
}
.bottom-wrap {
  display: flex;
}
.perwrap {
  width: 50%;
  display: flex;
}
.card-panel-num {
  font-size: 30px;
  margin: 10px 0;
}
.card-right {
  width: 125px;
  height: 145px;
}
.rightimg {
  width: 100%;
  height: 100%;
}
</style>
