<!-- 小规格卡片-今日营收 -->
<template>
  <el-card class="card-wrap">
    <div class="card-left">
      <div>本月工单
          <span style="font-size: 10px;">(个)</span>
      </div>
      <div style="margin:20px 0;">
        <count-to
          :start-val="0"
          :end-val="obj.a || 0"
          :duration="2600"
          class="card-panel-num"
          style="color: white"
        />
        <span style="font-size: 30px;">/</span>
        <count-to
          :start-val="0"
          :end-val="obj.b || 0"
          :duration="2600"
          class="card-panel-num"
          style="color: white"
        />
      </div>
      <div class="bottom-wrap">
        <i :class="[obj.c >= 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom']" :style="{color: obj.c >= 0 ? 'red' : 'green'}"/>
        <div class="perwrap">
          <span style="white-space: nowrap;">环比</span>
          <span>
            {{ obj.c }}%
          </span>
        </div>
      </div>
    </div>
    <div class="card-right">
      <el-progress class="rightimg" v-if="obj.percentage" type="circle" :format="format" :color="colors" :percentage="obj.percentage" :stroke-width="12"></el-progress>
      <img v-else :src="obj.imgUrl" class="rightimg" alt="">
    </div>
  </el-card>
</template>

<script>
import CountTo from "vue-count-to";
import { getMonthWorkFlow } from "@/api/homePage/index.js";

export default {
  components: {
    CountTo,
  },
  data() {
    return {
      obj: {},
      colors: [
        {color: '#f56c6c', percentage: 20},
        {color: '#e6a23c', percentage: 40},
        {color: '#5cb87a', percentage: 60},
        {color: '#1989fa', percentage: 80},
        {color: '#6f7ad3', percentage: 100}
      ]
    };
  },
  computed: {},
  watch: {},
  methods: {
    format(val) {
      let info = `
        超时率${val}%
       `
      return info
    },
    //获取今日营收
    getMonthWorkFlow() {
      let params = {
        orgNoList: this.$store.state.user.orgNoListId
      }
      getMonthWorkFlow(params).then(res => {
        this.$nextTick(() => {
          this.obj = {
            a: res.data.processWorkNum,
            b: res.data.totalWorkNum,
            c: res.data.qoQ,
            percentage: res.data.timeoutRate
          }
        })
      })
    },
  },
  created() {
    this.getMonthWorkFlow()
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.card-wrap {
  opacity: 1;
  border-radius: 2px;
  background: linear-gradient(242deg, #FFB36D 4%, #FF6FA4 95%);
  color: white;
  border-radius: 10px;
  /deep/.el-card__body {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-left {
    width: 60%;
  }
}
.bottom-wrap {
  display: flex;
}
.perwrap {
  width: 50%;
  display: flex;
}
.card-panel-num {
  font-size: 30px;
  margin: 10px 0;
}
.card-right {
  width: 125px;
  height: 145px;
}
.rightimg {
  width: 100%;
  height: 100%;
}
</style>
