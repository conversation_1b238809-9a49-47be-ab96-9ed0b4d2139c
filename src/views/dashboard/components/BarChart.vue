<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import resize from '../mixins/resize';

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    xAxisData: {
      type: Array,
      default: () => {
        return []
      }
    },
    seriesData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    seriesData: {
      deep: true,
      handler(newVal) {
        this.setOptions(newVal);
      },
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el, 'macarons');
      this.setOptions(this.seriesData)
    },
    setOptions(val) {
      this.chart.setOption({
        legend: {
          orient: "horizontal",
          right: '0%',
          // top: "middle",
          type: "scroll",
          x: "center",
          y: "top",
          itemStyle: {
            color: 'inherit'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          top: 40,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.xAxisData,
            axisTick: {
              alignWithLabel: true
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            }
          }
        ],
        // dataZoom: [
        //   {
        //     type: 'slider', // 内置在坐标系中
        //     // start:0, // 数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
        //     // end:20, // 数据窗口范围的结束百分比。范围是：0 ~ 100。
        //     startValue: 0, // 数据窗口范围的起始数值。如果设置了 dataZoom-inside.start 则 startValue 失效。
        //     endValue: 7, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效。
        //     zoomLock: false, // 是否锁定选择区域（或叫做数据窗口）的大小。
        //   }
        // ],
        series: this.seriesData
        // series: [
        //   {
        //     name: 'pageA',
        //     type: 'bar',
        //     // stack: 'vistors', //stack相同类目堆叠显示
        //     // barWidth: '60%',
        //     data: [79, 52, 200, 334, 390, 330, 220],
        //     // animationDuration
        //   },
        //   {
        //     name: 'pageB',
        //     type: 'bar',
        //     // stack: 'vistors',
        //     // barWidth: '60%',
        //     data: [80, 52, 200, 334, 390, 330, 220],
        //     // animationDuration
        //   }
        // ]
      });
    },
  }
};
</script>
