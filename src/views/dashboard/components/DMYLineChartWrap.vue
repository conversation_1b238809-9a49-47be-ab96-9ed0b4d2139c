<!-- 客服接待量 -->
<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <span>{{name}}</span>
      <span style="margin-left: 40px;font-size: 12px;color: #ccc;">单位：次</span>

      <el-radio-group v-model="radio1" @input="changeRadio1" class="middle-tab" size="mini">
        <el-radio-button :label="1">热线客服</el-radio-button>
        <el-radio-button :label="2">在线客服</el-radio-button>
      </el-radio-group>
      <el-radio-group v-model="radio2" @input="changeRadio2" class="el-icon-set" size="mini">
        <el-radio-button :label="1">今天</el-radio-button>
        <el-radio-button :label="2">本月</el-radio-button>
        <el-radio-button :label="3">今年</el-radio-button>
      </el-radio-group>
    </div>
    <LineChart :name="name" ref="LineChart" :xAxis="xAxis" :series="series" height="270px" />
  </el-card>
</template>

<script>
import LineChart from "./LineChart.vue";
export default {
  components: { LineChart },
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      radio1: 1,
      radio2: 1,
      xAxis: {
        data: ["1月", "2月", "3月", "4月"],
        boundaryGap: true,
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: "热线客服",
          itemStyle: {
            normal: {
              color: "rgba(68,90,235,0.5)",
              lineStyle: {
                color: "rgba(68,90,235,0.5)",
                width: 2,
              },
            },
          },
          smooth: true,
          type: "line",
          data: [12, 23 ,12, 234, 234, 234],
          animationDuration: 2800,
          animationEasing: "cubicInOut",
          areaStyle: {            // 折现下是否填充
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 1, color: 'rgba(68,90,235,0.1)' // 0% 处的颜色
              }, {
                offset: 0, color: 'rgba(68,90,235,0.5)' // 100% 处的颜色
              }],
              global: false
            }
          },
        },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
    changeRadio1(val) {
      if (val == 1) {
        this.xAxis.data = [

        ]
      }
    },
    changeRadio2(val) {
      if (val == 1) {
        this.xAxis.data = ["0点","1点","2点","3点","4点","5点","6点","7点","8点","9点","10点","11点","12点","13点","14点","15点","16点","17点","18点","19点","20点","21点","22点","23点"]
      } else if (val == 2) {
        //查询本月天数
        let date = new Date();
        date.setMonth(date.getMonth() + 1); // 先设置为下个月
        date.setDate(0); // 再置0，变成当前月最后一天
        let dateTotal = date.getDate() // 当前月最后一天即当前月拥有的天数
        let dateArr = []
        for (let i = 0; i < dateTotal; i++) {
          dateArr.push((i + 1) + "号")
        }
        this.xAxis.data = dateArr
      } else {
        //本年
        this.xAxis.data = ["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月",]
      }
      this.$refs.LineChart.setOptions()
    },
  },
  created() {},
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.clearfix {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.el-icon-set {
  border: none;
  position: absolute;
  right: 0;
  font-size: 20px;
  display: flex;
}
.middle-tab {
  position: absolute;
  left: 50%;
  transform: translate(-25%);
}
</style>
