<!-- 我的关注 -->
<template>
  <div class="">
    <el-card class="top-right-card">
      <div slot="header" class="clearfix">
        <span>我的关注</span>
        <el-button
          @click="openDialog"
          class="el-icon-set"
          icon="el-icon-plus"
        ></el-button>
      </div>

      <div v-if="list && list.length > 0">
        <div
          v-for="(item, index) in list"
          :key="index"
          @click="handleView(item)"
          class="list-item"
        >
          <div>
            <el-avatar
              :size="50"
              :src="item.circleUrl || require('@/assets/image/profile.jpg')"
            ></el-avatar>
          </div>
          <div class="middle-wrap">
            <div>{{ item.projectName }}</div>
            <div>
              <i class="el-icon-location-information"></i>
              {{ regionFormat(item.region) }}
            </div>
          </div>
          <div class="right-wrap">
            <div>
              <span>负责人:</span>
              <span>{{ item.projectManager }}</span>
            </div>
            <div>
              <i class="el-icon-wind-power"></i>
              {{ periodFormat(item.projectPeriod) }}
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无数据" :image-size="120"></el-empty>
      </div>
    </el-card>

    <el-dialog
      title="我的关注"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeCisible"
      append-to-body
      width="40%"
    >
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="closeCisible">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { focusListPage } from "@/api/homePage/index.js";

export default {
  components: {},
  data() {
    return {
      visible: false,
      list: [],
      regionList: [],
      periodList: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    handleView(row) {
      this.$router.push({
        path: "/projectManage/projectView",
        query: { id: row.projectId, flowRecordId: row.flowRecordId },
      });
    },
    openDialog() {
      // this.visible = true
      this.$router.push({ path: "/workOrderWorkBench/projectList" });
    },
    submitForm() {},
    closeCisible() {
      this.visible = false;
    },
    getfocusListPage() {
      focusListPage().then((res) => {
        this.list = res.data;
      });
    },
    regionFormat(val) {
      return this.selectDictLabel(this.regionList, val);
    },
    periodFormat(val) {
      return this.selectDictLabel(this.periodList, val);
    },
  },
  created() {
    //获取数据字典--区域
    this.getDicts("pm_region").then((response) => {
      this.regionList = response.data;
    });
    //获取数据字典--项目阶段
    this.getDicts("pm_project_period").then((response) => {
      this.periodList = response.data;
    });
  },
  mounted() {
    this.getfocusListPage();
  },
};
</script>
<style lang="less" scoped>
.top-right-card {
  border-radius: 10px;
  font-size: 12px;

  /deep/ .el-card__header {
    min-height: 54px;
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  /deep/ .el-card__body {
    overflow-y: auto;
    height: 35vh;
  }
}
.clearfix {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.el-icon-set {
  border: none;
  position: absolute;
  right: 0;
  font-size: 20px;
}

.list-item {
  display: flex;
  justify-content: space-around;
  padding: 10px;
  background-color: #ecf6ff;
  margin-bottom: 10px;
  border-radius: 5px;
  .middle-wrap,
  .right-wrap {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }
  .middle-wrap {
    width: 50%;
  }
  .right-wrap {
    width: 30%;
  }
}
</style>
