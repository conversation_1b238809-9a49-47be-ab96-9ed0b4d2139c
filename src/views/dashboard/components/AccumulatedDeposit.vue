<!-- 小规格卡片-本月累计预存 -->
<template>
  <el-card class="card-wrap">
    <div class="card-left">
      <div>本月累计预存
          <span style="font-size: 10px;">(元)</span>
      </div>
      <div style="margin:20px 0;">
        <count-to
          :start-val="0"
          :end-val="Number(obj.a) || 0"
          :duration="2600"
          class="card-panel-num"
          style="color: white"
          prefix="￥"
        />
      </div>
      <div class="bottom-wrap">
        <div class="perwrap">
          <i :class="[obj.b >= 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom']" :style="{color: obj.b >= 0 ? 'red' : 'green'}"/>
          <span style="white-space: nowrap;">同比</span>
          <span>
            {{ obj.b?obj.b:0 }}%
          </span>
        </div>
        <div class="perwrap">
          <i :class="[obj.b >= 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom']" :style="{color: obj.b >= 0 ? 'red' : 'green'}"/>
          <span style="white-space: nowrap;">环比</span>
          <span>
            {{ obj.c?obj.c:0 }}%
          </span>
        </div>
      </div>
    </div>
    <div class="card-right">
      <el-progress class="rightimg" v-if="obj.percentage" type="circle" :format="format" color="#a371f8" :percentage="obj.percentage"></el-progress>
      <img v-else :src="require('@/assets/image/img_2.png')" class="rightimg" alt="">
    </div>
  </el-card>
</template>

<script>
import CountTo from "vue-count-to";
import { getMonthPrepaySale } from "@/api/homePage/index.js";

export default {
  components: {
    CountTo,
  },
  data() {
    return {
      obj: {}
    };
  },
  computed: {},
  watch: {},
  methods: {
    format(val) {
      let info = `
        回收率${val}%
       `
      return info
    },
    //本月累计预存
    getMonthPrepaySale() {
      let params = {
        orgNoList: this.$store.state.user.orgNoListId
      };
      getMonthPrepaySale(params).then(res => {
        this.$nextTick(() => {
          this.obj = {
            a: res.data.curPrepay,
            b: res.data.yoY,
            c: res.data.qoQ,
          }
        })
      })
    },
  },
  created() {
    this.getMonthPrepaySale()
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.card-wrap {
  opacity: 1;
  border-radius: 2px;
  background: linear-gradient(117deg, #798BFF 0%, #ACBCFF 100%);
  color: white;
  border-radius: 10px;
  /deep/.el-card__body {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .card-left {
    width: 60%;
  }
}
.bottom-wrap {
  display: flex;
}
.perwrap {
  width: 50%;
  display: flex;
}
.card-panel-num {
  font-size: 30px;
  margin: 10px 0;
}
.card-right {
  width: 125px;
  height: 145px;
}
.rightimg {
  width: 100%;
  height: 100%;
}
</style>
