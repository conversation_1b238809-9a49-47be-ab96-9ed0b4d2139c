<!-- 营收统计 -->
<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <span>{{name}}</span>
      <span style="margin-left: 40px;font-size: 12px;color: #ccc;">单位：元</span>
      <i v-show="radio == 2" @click="refresh" :class="[isloading ? 'el-icon-loading' : 'el-icon-refresh', 'refreshBtn']" ></i>
      <el-radio-group @input="changeRadio" v-model="radio" class="el-icon-set" size="mini">
        <el-radio-button :label="1">当月</el-radio-button>
        <el-radio-button :label="2">今年</el-radio-button>
      </el-radio-group>
    </div>
    <LineChart ref="BarChart" :xAxis="xAxis" :series="series" height="270px" />
  </el-card>
</template>

<script>
import { getRevenueByDate,getRevenueByYm,refreshBoardData } from "@/api/homePage/index.js";
import LineChart from "./LineChart.vue";
export default {
  components: { LineChart },
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      isloading: false,
      radio: 1,
      xAxis: {
        data: [],
        boundaryGap: true,
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          name: "实收金额",
          itemStyle: {
            normal: {
              color: "rgba(68,90,235,0.5)",
              lineStyle: {
                color: "rgba(68,90,235,0.5)",
                width: 2,
              },
            },
          },
          smooth: true,
          type: "line",
          // data: [12, 23 ,12, 234, 234, 234],
          data: [],
          animationDuration: 2800,
          animationEasing: "cubicInOut",
          areaStyle: {            // 折现下是否填充
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 1, color: 'rgba(68,90,235,0.1)' // 0% 处的颜色
              }, {
                offset: 0, color: 'rgba(68,90,235,0.5)' // 100% 处的颜色
              }],
              global: false
            }
          },
        },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
    //刷新数据
    refresh() {
      let params = {
        boardsNo: '1101'
      }
      this.isloading = true
      refreshBoardData(params).then(res => {
        if (res.code == 10000) {
          this.getRevenueByYm()
        }
      }).finally(() => {
        this.isloading = false
      })
    },
    async changeRadio(value) {
      if (value == 1) {
        try {
          await this.getRevenueByDate()
        } catch (error) {
          this.radio = 2
        }
      } else {
        try {
          await this.getRevenueByYm()
        } catch (error) {
          this.radio = 1
        }
      }
    },
    // 获取本月营收统计
    async getRevenueByDate() {
      let params = {
        orgNoList: this.$store.state.user.orgNoListId
      }
      await getRevenueByDate(params).then(res => {
        let resArr = res.data
        let dataArr = []
        let xdataArr = []
        resArr.forEach(element => {
          dataArr.push(element.rcvAmt)
          // xdataArr.push(element.chargeDate + '日')
        });

        const date = new Date()
        const year = date.getFullYear()
        const month = date.getMonth()

        const days = new Date(year,month+1,0).getDate() // 30
        for (let i = 0; i < days; i++) {
          xdataArr.push((i + 1) + '日')
        }

        this.$set(this.series[0], 'data', dataArr)
        this.$set(this.xAxis, 'data', xdataArr)
      })
    },
    //获取本年营收统计-自然年
    async getRevenueByYm() {
      let params = {
        orgNoList: this.$store.state.user.orgNoListId
      }
      await getRevenueByYm(params).then(res => {
        let resArr = res.data
        let dataArr = []
        let xdataArr = []
        resArr.forEach(element => {
          dataArr.push(element.rcvAmt)
          // xdataArr.push(element.chargeYm + '月')
        });
        for (let i = 0; i < 12; i++) {
          xdataArr.push((i + 1) + '月')
        }
        this.$set(this.series[0], 'data', dataArr)
        this.$set(this.xAxis, 'data', xdataArr)
        this.isloading = false
      })
    },
  },
  async created() {
    await this.getRevenueByDate()
  },
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.clearfix {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.el-icon-set {
  border: none;
  position: absolute;
  right: 0;
  font-size: 20px;
  display: flex;
}
.refreshBtn {
  position: absolute;
  right: 130px;
  cursor: pointer;
}
</style>
