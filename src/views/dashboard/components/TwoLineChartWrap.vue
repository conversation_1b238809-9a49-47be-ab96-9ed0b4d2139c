<!-- 本年抄收统计 -->
<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <span>{{name}}</span>
      <span style="margin-left:40px;font-size: 12px;color: #ccc;">单位：百分比</span>
      <i @click="refresh" :class="[isloading ? 'el-icon-loading' : 'el-icon-refresh', 'el-icon-set']" ></i>
    </div>
    <LineChart ref="BarChart" :xAxis="xAxis" :series="series" height="270px" />
  </el-card>
</template>

<script>
import { getYearFeeRecycle, refreshBoardData } from "@/api/homePage/index.js";
import LineChart from "./LineChart.vue";
export default {
  components: { LineChart },
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      isloading: false,
      radio: 1,
      xAxis: {
        data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月","11月", "12月"],
        boundaryGap: true,
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        axisLabel:{
                        formatter: '{value} %'
                    },
      },
      series: [
        {
          name: "水费回收率",
          itemStyle: {
              color: "#1990ff",
          },
          smooth: false,
          type: "line",
          data: [12, 23, 12, 34, 3, 24],
          animationDuration: 2800,
          animationEasing: "cubicInOut",
          // areaStyle: {            // 折现下是否填充
          //   color: {
          //     type: 'linear',
          //     x: 0,
          //     y: 0,
          //     x2: 0,
          //     y2: 1,
          //     colorStops: [{
          //       offset: 1, color: 'rgba(68,90,235,0.1)' // 0% 处的颜色
          //     }, {
          //       offset: 0, color: 'rgba(68,90,235,0.5)' // 100% 处的颜色
          //     }],
          //     global: false
          //   }
          // },
        },
        {
          name: "抄表率",
          itemStyle: {
              color: "#ffa83c",
          },
          smooth: false,
          type: "line",
          data: [13, 29, 96, 74, 24, 94],
          animationDuration: 2800,
          animationEasing: "cubicInOut",
          // areaStyle: {            // 折现下是否填充
          //   color: {
          //     type: 'linear',
          //     x: 0,
          //     y: 0,
          //     x2: 0,
          //     y2: 1,
          //     colorStops: [{
          //       offset: 1, color: 'rgba(68,90,235,0.1)' // 0% 处的颜色
          //     }, {
          //       offset: 0, color: 'rgba(68,90,235,0.5)' // 100% 处的颜色
          //     }],
          //     global: false
          //   }
          // },
        },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
    //刷新数据
    refresh() {
      let params = {
        boardsNo: '1102'
      }
      this.isloading = true
      refreshBoardData(params).then(res => {
        if (res.code == 10000) {
          this.getYearFeeRecycle()
        }
      }).finally(() => {
        this.isloading = false
      })
    },
    async getYearFeeRecycle() {
      let params = {
        orgNoList: this.$store.state.user.orgNoListId
      };
      await getYearFeeRecycle(params).then(res => {
        let resArr = res.data;
        console.info("抄收数据列表", resArr);
        let meterRateArr = [];
        let feeRateArr = [];
        if (resArr && resArr.length > 0) {
          meterRateArr = resArr.map(i => i.meterReadRate);
          feeRateArr = resArr.map(i => i.recycleRate);
          console.info("抄表率", meterRateArr);
          console.info("水费回收率", feeRateArr);
          this.$set(this.series[0], "data", feeRateArr);
          this.$set(this.series[1], "data", meterRateArr);
        }
      });
    }
  },
  created() {
    this.getYearFeeRecycle()
  },
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.clearfix {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.el-icon-set {
  border: none;
  position: absolute;
  right: 0;
  font-size: 20px;
  cursor: pointer;
}
</style>
