<!-- 年度项目完成统计 -->
<template>
  <el-card class="box-card">
    <div slot="header" class="clearfix">
      <span>{{name}}</span>
      <span style="margin-left: 40px;font-size: 12px;color: #ccc;">单位：个</span>
      <!-- <i @click="refresh" :class="[isloading ? 'el-icon-loading' : 'el-icon-refresh', 'refreshBtn']" ></i> -->
    </div>
    <BarChart
      ref="BarChart"
      :xAxisData="xAxisData"
      :seriesData="seriesData"
      height="300px"
    />
  </el-card>
</template>

<script>
import BarChart from "./BarChart.vue";
import { projectStatisticsByMonth, refreshBoardData } from "@/api/homePage/index.js";

export default {
  components: { BarChart },
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      isloading: false,
      radio: 1,
      xAxisData: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
      seriesData: [
        {
          name: "规划中",
          type: "bar",
          // stack: 'vistors', //stack相同类目堆叠显示
          // barWidth: '60%',
          data: [],
          // animationDuration,
          itemStyle: {
            color: '#798bff'
          }
        },
        {
          name: "已完成",
          type: "bar",
          // stack: 'vistors',
          // barWidth: '60%',
          // data: [80, 52, 200, 334, 390, 330, 220],
          data: [],
          // animationDuration
          itemStyle: {
            color: '#4cd991'
          }
        },
        {
          name: "已关闭",
          type: "bar",
          // stack: 'vistors',
          // barWidth: '60%',
          // data: [80, 52, 200, 334, 390, 330, 220],
          data: [],
          // animationDuration
          itemStyle: {
            color: '#ff0004'
          }
        },
      ],
    };
  },
  computed: {},
  watch: {},
  methods: {
    //刷新数据
    refresh() {
      let params = {
        boardsNo: '1103'
      }
      this.isloading = true
      refreshBoardData(params).then(res => {
        if (res.code == 10000) {
          this.projectStatisticsByMonth()
        }
      }).finally(() => {
        this.isloading = false
      })
    },
    async projectStatisticsByMonth() {
      let params = {
        // orgNoList: this.$store.state.user.orgNoListId
      }
      await projectStatisticsByMonth(params).then(res => {
        let resArr = res.data
        let dataArr1 = []
        let dataArr2 = []
        let dataArr3 = []

        for (let i = 0; i < 12; i++) {
          dataArr1.push(0)
          dataArr2.push(0)
          dataArr3.push(0)
        }
        for (let i = 0; i < 12; i++) {
          for (let j = 0; j < resArr.length; j++) {
            if (Number(resArr[j].month) == i + 1) {
              dataArr1[i] = resArr[j].ongoingNumber
              dataArr2[i] = resArr[j].completeNumber
              dataArr3[i] = resArr[j].cancelNumber
            }
          }
        }
          this.$set(this.seriesData[0], 'data', dataArr1)
          this.$set(this.seriesData[1], 'data', dataArr2)
          this.$set(this.seriesData[2], 'data', dataArr3)
          console.log("---", this.seriesData)
      })
    },
  },
  async created() {
    await this.projectStatisticsByMonth()
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.clearfix {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}
.middle-tab {
  position: absolute;
  left: 50%;
  transform: translate(-25%);
}
.refreshBtn {
  position: absolute;
  right: 0px;
  cursor: pointer;
}
</style>
