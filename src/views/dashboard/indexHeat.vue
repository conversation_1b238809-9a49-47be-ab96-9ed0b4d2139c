<template>
  <div class="dashboard-editor-container">
    <panel-group @handleSetLineChartData="handleSetLineChartData" />

    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 32px">
      <line-chart :chart-data="lineChartData" />
    </el-row>

    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper flex-wrap">
          <!-- <raddar-chart /> -->
          <!-- <el-progress type="circle" :stroke-width="20" :percentage="25"></el-progress> -->
          <gauge :data="gaugeData"/>
          <div class="right-wrap">
            <div class="right-write-wrap">
              <span> 建筑面积(m<sup>3</sup>): <br/> <span style="color:#e6a23c;font-size:20px;">200</span></span>
              <span> 停热面积(m<sup>3</sup>): <br/> <span style="color:red;font-size:20px;">290</span> </span>
            </div>
            <div class="right-write-wrap">
              <span> 建筑面积(m<sup>3</sup>): <br/> <span style="color:#e6a23c;font-size:20px;">60</span> </span>
              <span> 未供面积(m<sup>3</sup>): <br/> <span style="color:red;font-size:20px;">10</span> </span>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper">
          <pie-chart :data="data" />
        </div>
      </el-col>
      <!-- <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import PanelGroup from "../dashboard/PanelGroup";
import LineChart from "../dashboard/LineChart";
import RaddarChart from "../dashboard/RaddarChart";
import PieChart from "../dashboard/PieChart";
import BarChart from "../dashboard/BarChart";
import gauge from "../dashboard/gauge";

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145],
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130],
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130],
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130],
  },
};

export default {
  name: "Index",
  components: {
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart,
    gauge,
  },
  data() {
    return {
      lineChartData: lineChartData.newVisitis,
      data: [
        { value: 320, name: "坐收" },
        { value: 240, name: "走收" },
        { value: 149, name: "线上缴费" },
        { value: 100, name: "圈存机" },
        { value: 59, name: "代扣" },
        { value: 18, name: "其他" },
      ],
      gaugeData: [
        {
          name: "供热率",
          value: 65,
          gradient: ["#03c2fd", "#1ed3e5", "#2fded6"],
        },
      ]
    };
  },
  methods: {
    handleSetLineChartData(type) {
      this.lineChartData = lineChartData[type];
    },
  },
};
</script>

<style lang="less" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.flex-wrap {
  display: flex;
  justify-content: space-around;
}
.right-wrap {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.right-write-wrap {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin: 20px 0;
  font-size: 12px;
}
</style>
