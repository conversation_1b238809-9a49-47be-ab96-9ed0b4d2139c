<!-- 门户首页配置 -->
<template>
  <div class="app-container">
    <div class="wrap">
      <div class="left">
          <el-card :class="{ active: radio == item.dictValue }" v-for="(item,index) in bigDashboards" :key="index" >
            <div @click="chooseLeft(item.dictValue)">
              {{item.dictLabel}}
            </div>
          </el-card>
      </div>
      <div class="right">
        <el-card :class="{ active: rightArr.includes(item.dictValue) }"  v-for="(item,index) in smallDashboards" :key="index" >
          <div @click="chooseRight(item.dictValue)">
            {{item.dictLabel}}
          </div>
        </el-card>
      </div>
    </div>

    <p style="padding: 20px;color:red">
      选择组合：1个大看板+2个小看板或者6个小看板
    </p>

    <div class="btn-wrap">
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </div>
</template>

<script>
import { queryUserBindBoards, batchAddBindBoards } from "@/api/homePage/index.js";
export default {
  components: {},
  data() {
    return {
      bigDashboards: [],
      smallDashboards: [],
      radio: undefined,
      radioOld: undefined,
      rightArr: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    async queryUserBindBoards() {
      await queryUserBindBoards().then(res => {
        let arr = res.data
        this.rightArr = []
        this.radio = undefined
          arr.forEach(element => {
            if (element.boardsType == 'hp_small_dashboards') {
              this.rightArr.push(element.boardsNo)
            } else if (element.boardsType == 'hp_big_dashboards') {
              this.radio = element.boardsNo
            }
          });
      })
    },
    batchAddBindBoards() {
      let bigArr = this.bigDashboards.filter(el => el.dictValue == this.radio)
      let smallArr = []
      this.rightArr.forEach(element => {
        this.smallDashboards.forEach(el => {
          if (el.dictValue == element) {
            smallArr.push(el)
          }
        });
      });
      bigArr.map(el => {
        el.boardsName = el.dictLabel
        el.boardsType = el.dictType
        el.boardsNo = el.dictValue
      })
      smallArr.map(el => {
        el.boardsName = el.dictLabel
        el.boardsType = el.dictType
        el.boardsNo = el.dictValue
      })
      let params = [
        ...bigArr,
        ...smallArr
      ]
      console.log('[ params ] >', params)
      batchAddBindBoards(params).then(res => {
        this.$message.success("保存成功！")
      })
    },
    chooseLeft(val) {
      if (this.radioOld == val) {
        if(this.radio) {
          this.$nextTick(() => {
            this.radio = undefined
          })
        } else {
          this.$nextTick(() => {
            this.radio = val;
          })
        }
      }
      this.radio = val;
      this.radioOld = val
    },
    chooseRight(val) {
      let flag = this.rightArr.includes(val);
      if (flag) {
        this.rightArr.splice(this.rightArr.indexOf(val), 1);
      } else {
        this.rightArr.push(val);
      }
    },
    submit() {
      let isOk = true;
      if (this.radio) {
        if (this.rightArr.length != 2) {
          isOk = false;
        }
      } else {
        if (this.rightArr.length != 6) {
          isOk = false;
        }
      }
      if (!isOk) {
        this.$message.error("配置条件不满足，配置失败！");
        return;
      }
      this.batchAddBindBoards()
    },
  },
  async created() {
    //获取数据字典--大看板
    this.getDicts("hp_big_dashboards").then((response) => {
      this.bigDashboards = response.data;
    });
    //获取数据字典--小看板
    this.getDicts("hp_small_dashboards").then((response) => {
      this.smallDashboards = response.data;
    });
    await this.queryUserBindBoards()
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.wrap {
  display: flex;
  .left,
  .right {
    width: 50%;
    padding: 20px;
    /deep/.el-card__body,
    div {
      width: 100%;
      height: 100%;
    }
    .active {
      background-color: rgb(94, 171, 226);
      color: white;
    }
  }
  .left {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #fde3ce;
    border-radius: 30px;
    margin-right: 10px;
    .el-card {
      min-height: 15vh;
      border-radius: 20px;
      margin: 10px 0;
      cursor: pointer;
      div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .right {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    background-color: #bcebfa;
    border-radius: 30px;
    .el-card {
      margin: 10px 0;
      width: 47%;
      height: 15vh;
      border-radius: 20px;
      cursor: pointer;
      div {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
.btn-wrap {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}
</style>
