//故障类别
<template>
  <div class="app-container">
    <div class="page-header">
      <h3>故障类别</h3>
      <el-button
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click.stop="handleAdd(1)"
        v-has-permi="['operation:faultType:add']"
        >新增</el-button
      >
    </div>
    <el-card>
      <el-tree
        :data="data"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :props="defaultProps"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div class="node-label">{{ node.label }}</div>
          <div>
            <el-button
              type="text"
              size="mini"
              @click="handleAdd(data.typeLevel + 1, data)"
              v-if="data.typeLevel <= 2"
              icon="el-icon-plus"
              v-has-permi="['operation:faultType:add']"
            >
              新增
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(data.typeLevel, data)"
              icon="el-icon-edit"
              v-has-permi="['operation:faultType:edit']"
            >
              修改
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleDelete(data.id)"
              icon="el-icon-minus"
              style="color: red"
              v-has-permi="['operation:faultType:delete']"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-tree>
    </el-card>
    <el-dialog
      :title="title"
      :visible.sync="modalVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="modalForm"
        ref="modalForm"
        label-width="110px"
        :rules="rules"
      >
        <el-form-item label="一级类型：" prop="firstType">
          <span v-if="addLevel == 2 || addLevel == 3">{{
            modalForm.firstType
          }}</span>
          <el-input
            v-model="modalForm.firstType"
            placeholder="请输入一级故障类别，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item
          label="二级类型："
          prop="secondType"
          v-if="addLevel == 2 || addLevel == 3"
        >
          <span v-if="addLevel == 3">{{ modalForm.secondType }}</span>
          <el-input
            v-model="modalForm.secondType"
            placeholder="请输入二级故障类别，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item label="三级类型：" prop="thirdType" v-if="addLevel == 3">
          <el-input
            v-model="modalForm.thirdType"
            placeholder="请输入三级故障类别，长度100个字符以内"
            maxlength="100"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="handleSubmit"
          :loading="submitLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/faultType/index.js";
export default {
  components: {},
  data() {
    return {
      modalVisible: false,
      title: "新增故障类别",
      submitLoading: false,
      data: [
        {
          id: 1,
          label: "一级 1",
          parentId: 0,
          children: [
            {
              id: 4,
              label: "二级 1-1",
              parentId: 1,
              parentLabel: "一级 1",
              children: [
                {
                  id: 9,
                  label: "三级 1-1-1",
                  parentId: 4,
                },
                {
                  id: 10,
                  label: "三级 1-1-2",
                  parentId: 4,
                },
              ],
            },
          ],
        },
        {
          id: 2,
          label: "一级 2",
          parentId: 0,
          children: [
            {
              id: 5,
              label: "二级 2-1",
              parentId: 2,
              parentLabel: "一级 2",
              children: [],
            },
            {
              id: 6,
              label: "二级 2-2",
              parentId: 2,
              parentLabel: "一级 2",
              children: [],
            },
          ],
        },
        {
          id: 3,
          label: "一级 3",
          parentId: 0,
          children: [
            {
              id: 7,
              label: "二级 3-1",
              parentId: 3,
              parentLabel: "一级 3",
              children: [],
            },
            {
              id: 8,
              label: "二级 3-2",
              parentId: 3,
              parentLabel: "一级 3",
              children: [],
            },
          ],
        },
      ],
      modalForm: {
        firstType: "",
        secondType: "",
        thirdType: "",
      },
      rules: {
        firstType: [{ required: true, message: "故障类别不能为空" }],
        secondType: [{ required: true, message: "故障类别不能为空" }],
        thirdType: [{ required: true, message: "故障类别不能为空" }],
      },
      addLevel: 1,
      defaultProps: {
        children: "childrenList",
        label: "typeName",
      },
      flattenData: [],
      modalType: "add",
      parentId: undefined,
      editId: undefined,
    };
  },
  mounted() {
    this.getTreeData();
  },
  methods: {
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getTreeData() {
      api.queryTreeList({}).then((res) => {
        this.data = JSON.parse(JSON.stringify(res.data));
        this.flattenData = this.flattenArray(res.data);
        console.log("-----", this.flattenData);
      });
    },
    handleAdd(level, data) {
      console.log(data);
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "新增故障类别";
      this.modalType = "add";
      if (level == 1) {
        this.modalForm.firstType = "";
        this.parentId = undefined;
      } else if (level == 2) {
        this.modalForm.firstType = data.typeName;
        this.modalForm.secondType = "";
        this.parentId = data.id;
      } else if (level == 3) {
        this.modalForm.firstType = data.parentName; //树结构需提供父级的label 或调接口获取
        this.modalForm.secondType = data.typeName;
        this.modalForm.thirdType = "";
        this.parentId = data.id;
      }
    },
    handleEdit(level, data) {
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "修改故障类别";
      this.modalType = "edit";
      this.editId = data.id;
      if (level == 1) {
        this.modalForm.firstType = data.typeName;
      } else if (level == 2) {
        this.modalForm.firstType = data.parentName;
        this.modalForm.secondType = data.typeName;
      } else if (level == 3) {
        this.modalForm.firstType = this.flattenData?.find(
          (x) => x.id == data.parentId
        )?.parentName; //树结构需提供父级的label 或调接口获取
        this.modalForm.secondType = data.parentName;
        this.modalForm.thirdType = data.typeName;
      }
    },
    //提交
    handleSubmit() {
      const arr = [
        { level: 1, value: "firstType" },
        { level: 2, value: "secondType" },
        { level: 3, value: "thirdType" },
      ];
      const method = this.modalType === "add" ? "addType" : "editType";
      const value = arr.find((x) => x.level === this.addLevel)?.value;

      this.$refs.modalForm.validate((valid) => {
        if (valid) {
          const params =
            this.modalType === "add"
              ? {
                  typeName: this.modalForm[value],
                  typeLevel: this.addLevel,
                  parentId: this.parentId,
                }
              : { typeName: this.modalForm[value], id: this.editId };
          console.log("提交", this.modalForm);
          this.submitLoading = true;
          api[method](params).then((res) => {
            if (res?.success) {
              this.$message.success("提交成功");
              this.getTreeData();
              this.closeDialog();
              this.submitLoading = false;
            }
          });
        } else {
          console.log("校验失败");
        }
      });
    },
    closeDialog() {
      console.log("关闭弹窗");
      this.$refs.modalForm.resetFields();
      this.modalVisible = false;
    },
    handleDelete(id) {
      this.$confirm(
        "删除后，已生成的故障类别不受影响！",
        "确定要删除该故障类别吗?",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }
      ).then(() => {
        let data = {
          id: id,
        };
        api.deleteType(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getTreeData();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  align-items: center;
  h3 {
    margin-right: 40px;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-label {
    width: 600px;
    word-wrap: break-word;
    white-space: normal;
  }
}
/deep/ .el-tree-node__content {
  padding-top: 10px;
  padding-bottom: 10px;
  height: auto;
}
/deep/ .el-card__body {
  max-height: 80vh;
  overflow-y: auto;
}
</style>
