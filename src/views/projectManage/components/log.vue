<!-- 项目管理 -->
<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>项目操作记录</span>
      </div>
      <el-timeline :hide-timestamp="true">
        <el-timeline-item
          placement="top"
          v-for="item in logList"
          :key="item.projectRecordId"
        >
          <el-card>
            <el-row>
              <el-col :span="5">
                <span>{{ item.operateTypeName }}</span>
              </el-col>
              <el-col :span="5">
                <span>操作人：{{ item.operatorName }}</span>
              </el-col>
              <el-col :span="5">
                <span>{{ item.operateTime }}</span>
              </el-col>
            </el-row>
            <span
              style="display:block;margin-top: 10px"
              v-if="
                item.operateType === 'update' || item.operateType === 'rewrite'
              "
              >{{ item.remark }}</span
            >
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script>
import { logList } from "@/api/projectManage";

export default {
  name: "projectManageLog",
  components: {},
  data() {
    return {
      logList: [],
      projectId: undefined,
    };
  },
  mounted() {
    this.getLogList();
  },
  methods: {
    getLogList() {
      logList(this.projectId).then((res) => {
        this.logList = res.data;
        if (this.logList.length < 1) {
          this.$message.info("暂无日志记录");
        }
      });
    },
  },
  watch: {
    $route: {
      handler(newVal) {
        console.log("newVal", newVal);
        this.projectId = newVal.query.projectId;
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/.el-select-group__title {
    padding-left: 10px;
  }
  /deep/.el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }
  /deep/.el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
</style>
