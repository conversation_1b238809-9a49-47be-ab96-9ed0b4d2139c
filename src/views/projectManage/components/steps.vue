<!-- 步骤条 -->
<template>
  <div class="">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>{{ title }}</span>
      </div>
      <el-steps direction="vertical" finish-status="success">
        <el-step
          v-for="(item, index) in steps"
          :key="index"
          :title="item.nodeName"
          :status="item.startTime && item.endTime ? 'success' : 'process'"
          :class="item.taskId === curentTaskId ? 'active-step' : ''"
        >
          <template slot="description">
            <div @click.stop="handleClick(item)">
              <div class="step-info" v-if="item.child == undefined">
                <div>执行人:</div>
                {{ item.assignee }}
              </div>
              <div class="step-info" v-if="item.child == undefined">
                <div>开始时间:</div>
                {{ item.startTime }}
              </div>
              <div class="step-info" v-if="item.child == undefined">
                <div>结束时间:</div>
                {{ item.endTime }}
              </div>
              <el-steps
                class="child-wrap"
                v-for="(childitem, childindex) in item.child"
                :key="childindex"
                direction="vertical"
                finish-status="success"
              >
                <el-step
                  v-for="(el, elIndex) in childitem"
                  :key="elIndex"
                  :title="el.nodeName"
                  :status="el.startTime && el.endTime ? 'success' : 'process'"
                >
                  <template slot="description">
                    <div @click.stop="handleClick(el)">
                      <div class="step-info">
                        <div>执行人:</div>
                        {{ el.assignee }}
                      </div>
                      <div class="step-info">
                        <div>开始时间:</div>
                        {{ el.startTime }}
                      </div>
                      <div class="step-info">
                        <div>结束时间:</div>
                        {{ el.endTime }}
                      </div>
                    </div>
                  </template>
                </el-step>
              </el-steps>
            </div>
          </template>
        </el-step>
      </el-steps>
    </el-card>
  </div>
</template>

<script>
import { getNodeHisInfoInfos } from "@/api/orderScheduling/workStation";

export default {
  components: {},
  props: {
    title: {
      type: String,
      default: "",
    },
    businessNo: {
      type: String,
      default: "",
    },
    curentTaskId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      steps: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    getList() {
      let params = {
        businessNo: this.businessNo,
      };

      getNodeHisInfoInfos(params).then((res) => {
        this.steps = res.data;

        //设置默认查看的节点信息：
        //1、未完成：显示处理中那个节点详情；
        //2、已完成：显示第一个节点详情；
        let taskFinished = true; //工作流是否已结束，如果未找到正在执行的节点则任务流程结束
        this.steps.some((item) => {
          if (item.taskId && !item.endTime) {
            //处理中
            //默认显示当前正在执行的节点的详情
            taskFinished = false;
            this.$emit("refreshTaskFrom", item);
            return true;
          }
        });

        if (taskFinished) {
          //已完成|已作废
          //默认显示第一个节点的信息
          this.$emit("refreshTaskFrom", this.steps[0]);
        }
      });
    },
    handleClick(row) {
      console.log("row", row);
      if (!row.child) {
        if (row.taskId == undefined) {
          this.$message.info("尚未走到该节点");
          return;
        }
        // this.$message.info("切换动态表单")
        this.$emit("refreshTaskFrom", row);
      }
    },
  },
  created() {
    this.getList();
  },
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.step-info {
  display: flex;
}

.child-wrap {
  border: 1px dashed #ccc;
  padding: 10px;
  margin: 10px 0;
}
/deep/ .el-step__main {
  padding: 10px;
}
.active-step /deep/ .el-step__main {
  margin: 5px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
