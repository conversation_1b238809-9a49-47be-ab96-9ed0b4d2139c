<!-- 工单工作台 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
      <!-- 业务类型 -->
      <template slot="relaBizType">
        <el-form-item label="" prop="relaBizType">
          <el-select
            v-model="searchForm.relaBizType"
            @change="changeRelaBizType"
            filterable
            clearable
            size="mini"
            placeholder="业务类型"
          >
            <el-option
              v-for="item in businessTypeOption"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </template>
      <!-- 工单类型 -->
      <template slot="flowType">
        <el-form-item label="" prop="flowType">
          <el-select
            v-model="searchForm.flowType"
            filterable
            clearable
            size="mini"
            placeholder="工单类型"
          >
            <el-option
              v-for="item in orderTypeOption"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </template>
    </AdvancedForm>
    <!--    <el-tabs v-model="activeName" @tab-click="handleQuery(searchForm)" type="card">-->
    <!--      <el-tab-pane label="待处理" name="1"></el-tab-pane>-->
    <!--      <el-tab-pane label="已处理" name="2"></el-tab-pane>-->
    <!--      <el-tab-pane label="全部" name="3"></el-tab-pane>-->
    <!--    </el-tabs>-->

    <div>
      <el-card>
        <GridTable
          :columns="columns"
          :tableData="businessTable"
          :checkbox="true"
          :seq="true"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="businessTableTotal"
          @changePage="changePage"
          :loading="loading"
          :tableId="tableId"
        >
          <template slot="operation" slot-scope="{ row, $index }">
            <!--            <el-button v-if="activeName === '1' || (activeName === '3' && row.flowStatus === '1' && userId === row.assignee)" type="text" size="large" @click="businessHandle(row)">-->
            <!--              工单处理-->
            <!--            </el-button>-->
            <el-button @click="businessDetail(row)" type="text" size="large">
              工单详情
            </el-button>
            <!--            <el-button v-if="activeName !== '2' && row.flowStatus === '1'" v-has-permi="['workOrder:workBench:handleOver']" @click="handleOver(row)" type="text"-->
            <!--                       size="large">-->
            <!--              指派-->
            <!--            </el-button>-->
          </template>

          <template slot="deviceTypeImg" slot-scope="{ row, $index }">
            <div v-if="row.flowStatus == '1'">
              <el-progress :percentage="50"></el-progress>
            </div>
            <div v-else>
              <el-progress :percentage="100"></el-progress>
            </div>
          </template>
        </GridTable>
      </el-card>
    </div>
    <el-dialog
      title="指派"
      :visible.sync="handleOverVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="form" ref="form" label-width="110px" :rules="rules">
        <el-form-item label="人员" prop="targetUserId">
          <el-select v-model="form.targetUserId">
            <el-option
              v-for="item in userOption"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit()">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  allBusinessList,
  businessList,
  businessTransfer,
  handleBusinessList,
} from "@/api/business/flow/flow";
import { stationList } from "@/api/workOrderWorkbench/index.js";
import { getDicts } from "@/api/system/dict/data";
import { useFlowList } from "@/api/orderScheduling/workStation.js";
import { projectInfoList } from "@/api/projectManage/index.js";
import { listUser } from "@/api/system/user";
import { mapGetters } from "vuex";

export default {
  name: "flowManage",
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        businessNoSearch: "",
        flowType: "",
        relaBizType: "",
        stationId: "",
        projectId: "",
        flowStatus: "",
        pageNum: 1,
        pageSize: 10,
      },
      activeName: "3", //暂时默认全部
      handleOverVisible: false,
      form: {
        taskId: undefined,
        businessNo: undefined,
        targetUserId: undefined,
      },
      rules: {
        targetUserId: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },
      userOption: [],
      addForm: {},
      options: [],
      finallySearch: null,
      businessTable: [],
      businessTableTotal: 0,
      addOrderVisible: false, //添加工单
      statusDict: [], //工单状态字典
      businessTypeOption: [], //业务类型
      stationOption: [], //站点
      orderTypeOption: [], //工单类型
      config: [],
      visible: false,
      tableId: "workOrderWorkbench", //tableId必须项目唯一，用于缓存展示列
    };
  },
  async created() {},
  computed: {
    ...mapGetters(["userId"]),
    // eslint-disable-next-line vue/return-in-computed-property
    columns() {
      return [
        {
          field: "businessNo",
          title: "工单编号",
        },
        {
          field: "flowTypeName",
          title: "工单类型",
        },
        {
          field: "relaBizType",
          title: "业务类型",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.businessTypeOption.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "projectName",
          title: "所属项目",
        },
        {
          field: "cent",
          title: "完成进度",
          slots: { default: "deviceTypeImg" },
          customWidth: 200,
        },
        {
          field: "flowStatus",
          title: "工单状态",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.statusDict.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "nickName",
          title: "当前处理人",
          visible: this.activeName === "3",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
  },
  mounted() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getFlowStatusDicts(),
      this.getFlowBusinessType(),
      this.getStationList(),
      this.getUseFlowList(),
      this.getProjectInfoList(),
      this.getListUser(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
    //获取工单列表
    this.getFlowList(this.searchForm);
  },
  watch: {
    $route: {
      handler(newVal) {
        console.log("newVal", newVal);
        this.searchForm.projectId = newVal.query.projectId;
        this.activeName = newVal.query.activeName;
        if (newVal.query.relaBizType) {
          this.searchForm.relaBizType = newVal.query.relaBizType;
        }
      },
      immediate: true,
    },
  },
  methods: {
    //获取项目列表
    async getProjectInfoList() {
      const { code, data } = await projectInfoList(this.searchForm);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.projectId;
        element.dictLabel = element.projectName;
      });
      this.projectOption = data;
    },
    //工单类型
    async getUseFlowList(businessType) {
      let params = {
        businessType,
      };
      const { code, data } = await useFlowList(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.flowType;
        element.dictLabel = element.flowTypeName;
      });
      this.orderTypeOption = data;
    },
    //站点
    async getStationList() {
      const { code, data } = await stationList();
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.stationId;
        element.dictLabel = element.stationName;
      });
      this.stationOption = data;
    },
    getFlowList(params) {
      const args = this._.cloneDeep(params ? params : this.searchForm);
      // args.deviceTypeNo = this.searchForm.deviceTypeNo; //设备类型
      this.loading = true;
      this.finallySearch = args;
      if (this.activeName === "1") {
        businessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else if (this.activeName === "2") {
        handleBusinessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else {
        allBusinessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      }
    },
    //切换业务类型
    async changeRelaBizType(val) {
      await this.getUseFlowList(val);
      this.initConfig();
    },
    //初始化
    initConfig() {
      this.config = [
        {
          key: "businessNoSearch",
          title: "工单编号",
          type: "input",
          placeholder: "请输入设备编号",
        },
        {
          key: "flowType",
          title: "工单类型",
          type: "select",
          options: this.orderTypeOption,
          slot: "flowType",
          placeholder: "请选择工单类型",
        },
        {
          key: "stationId",
          title: "站点",
          type: "select",
          options: this.stationOption,
          placeholder: "请选择站点",
        },
        {
          key: "projectId",
          title: "项目",
          type: "select",
          options: this.projectOption,
          placeholder: "请选择项目",
        },
        {
          key: "flowStatus",
          title: "工单状态",
          type: "select",
          options: this.statusDict,
          placeholder: "请选择工单状态",
        },
        {
          key: "relaBizType",
          title: "业务类型",
          type: "slot",
          options: this.businessTypeOption,
          slot: "relaBizType",
          placeholder: "请选择业务类型",
        },
      ];
    },
    async getFlowStatusDicts() {
      //获取工单状态字典
      return await getDicts("cm_flow_status").then((response) => {
        this.statusDict = response?.data;
      });
    },
    async getFlowBusinessType() {
      //业务类型字典
      return await getDicts("flow_business_type").then((response) => {
        this.businessTypeOption = response?.data;
      });
    },
    //工单处理
    businessHandle(row) {
      this.$router.push({
        path: "/orderDeal",
        query: {
          businessNo: row.businessNo,
          flowStatus: row.flowStatus,
          taskId: row.taskId,
          router: "/workOrderWorkBench/workOrderWorkbench",
        },
      });
    },
    //工单详情
    businessDetail(row) {
      this.$router.push({
        path: "/orderDetail",
        query: {
          businessNo: row.businessNo,
          flowStatus: row.flowStatus,
          taskId: row.taskId,
        },
      });
    },
    // 指派
    handleOver(row) {
      this.handleOverVisible = true;
      this.form.businessNo = row.businessNo;
      this.form.taskId = row.taskId;
    },
    //table内容格式化
    statusFormat(val) {
      return this.selectDictLabel(this.statusDict, val.deviceStatus);
    },

    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deviceTypeNo,
        label: node.deviceTypeName,
        children: node.childrenList,
      };
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.getFlowList(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getFlowList();
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.getFlowList(this.finallySearch);
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    closeDialog() {
      this.$refs.form.resetFields();
      this.handleOverVisible = false;
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
          };

          const loading = this.$loading({
            lock: true,
            text: "指派中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          businessTransfer(params)
            .then((res) => {
              loading.close();
              if (res?.success) {
                this.closeDialog();
                this.handleQuery(this.searchForm);
                this.$message.success("指派成功");
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
</style>
