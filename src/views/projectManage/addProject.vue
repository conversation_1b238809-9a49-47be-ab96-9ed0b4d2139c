<!-- 新增项目 -->
<template>
  <div class="">
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeVisible"
      append-to-body
      width="60%"
    >
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
        :before-leave="beforeLeave"
      >
        <el-tab-pane label="基础信息" name="1"></el-tab-pane>
        <el-tab-pane label="站点信息" name="2"></el-tab-pane>
        <el-tab-pane label="人员信息" name="3"></el-tab-pane>
        <el-tab-pane label="项目资料" name="4"></el-tab-pane>
        <el-tab-pane
          label="创建施工工单"
          name="5"
          v-if="this.pageType === 'add' || this.status === '0'"
        ></el-tab-pane>
      </el-tabs>

      <div v-show="activeName == '1'" class="queryParamsWrap">
        <el-form
          :model="form1"
          :rules="rules1"
          ref="form1"
          :inline="true"
          label-width="110px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="项目编码" prop="projectCode">
                <el-input
                  v-model="form1.projectCode"
                  size="mini"
                  :disabled="
                    pageType === 'detail' ||
                      (pageType === 'update' && this.status !== '0')
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目名称" prop="projectName">
                <el-input
                  v-model="form1.projectName"
                  size="mini"
                  :disabled="
                    pageType === 'detail' ||
                      (pageType === 'update' && this.status !== '0')
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否包干" prop="contractFlag">
                <el-select
                  v-model="form1.contractFlag"
                  size="mini"
                  :disabled="pageType === 'detail'"
                  clearable
                >
                  <el-option key="1" label="是" value="是"> </el-option>
                  <el-option key="0" label="否" value="否"> </el-option>
                  <el-option
                    key="2"
                    label="由公司供应商库自选"
                    value="由公司供应商库自选"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="项目备注">
                <el-input
                  v-model="form1.remark"
                  type="textarea"
                  :rows="5"
                  size="mini"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div v-show="activeName == '2'" class="queryParamsWrap">
        <el-form
          :model="form2"
          :rules="rules2"
          ref="form2"
          :inline="true"
          label-width="110px"
        >
          <el-row type="flex" style="flex-wrap: wrap;">
            <el-col :span="12">
              <el-form-item label="站点名称" prop="stationName">
                <el-input
                  v-model="form2.stationName"
                  size="mini"
                  :disabled="
                    pageType === 'detail' ||
                      (pageType === 'update' && this.status !== '0')
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="站点编码" prop="stationCode">
                <el-autocomplete
                  v-model="form2.stationCode"
                  :fetch-suggestions="querySearch"
                  @select="stationCodeChange"
                  size="mini"
                  :disabled="
                    pageType === 'detail' ||
                      (pageType === 'update' && this.status !== '0')
                  "
                >
                </el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运营状态">
                <el-select
                  v-model="form2.status"
                  filterable
                  clearable
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in stationStatusDict"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属大区" prop="orgNo">
                <el-select
                  v-model="form2.orgNo"
                  filterable
                  clearable
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in deptOptionList"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                    :disabled="item.status == 1"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="充电桩数量" prop="pileNum">
                <el-input-number
                  v-model="form2.pileNum"
                  controls-position="right"
                  :min="1"
                  size="mini"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务模式" prop="businessMode">
                <el-select
                  v-model="form2.businessMode"
                  clearable
                  size="mini"
                  placeholder=""
                  multiple
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in businessModeOptions"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="站点类型" prop="">
                <el-select
                  v-model="form2.stationType"
                  filterable
                  clearable
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in stationTypeOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="行政区" prop="provinceCityCounty">
                <CustomCascader
                  v-model="form2.provinceCityCounty"
                  placeholder=""
                  :options="districtOptions"
                  clearable
                  filterable
                  size="mini"
                  :disabled="pageType === 'detail'"
                ></CustomCascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="经纬度"
                prop="longLat"
                class="radius-wrap append-btn"
              >
                <el-input v-model="form2.longLat" size="mini" disabled>
                  <template slot="append">
                    <div @click="openLatLong" v-if="pageType !== 'detail'">
                      获取经纬度
                    </div>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="详细地址" prop="stationAddress">
                <el-input
                  v-model="form2.stationAddress"
                  placeholder="请填写详细地址"
                  size="mini"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签到半径" prop="radius" class="radius-wrap">
                <el-input-number
                  placeholder="请填写半径,单位:米"
                  v-model="form2.radius"
                  size="mini"
                  :disabled="pageType === 'detail'"
                  :min="0"
                >
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车位数量">
                <el-input-number
                  v-model="form2.parkNum"
                  size="mini"
                  :min="0"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上线时间">
                <el-date-picker
                  v-model="form2.onlineDate"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  :disabled="pageType === 'detail'"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="投运时间">
                <el-date-picker
                  v-model="form2.openDate"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  :disabled="pageType === 'detail'"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="建设时间">
                <el-date-picker
                  v-model="form2.buildDate"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  :disabled="pageType === 'detail'"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否对外开放">
                <el-select
                  v-model="form2.openFlag"
                  filterable
                  clearable
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in openFlagDict"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="站点充电分类">
                <el-select
                  v-model="form2.stationChargeType"
                  filterable
                  clearable
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in stationChargeTypeDict"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="建设场所">
                <el-select
                  v-model="form2.construction"
                  filterable
                  clearable
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in constructionDict"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务电话">
                <el-input
                  v-model="form2.serviceTel"
                  size="mini"
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="站点运营商" prop="operatorCode">
                <el-select
                  v-model="form2.operatorCode"
                  placeholder="请选择站点运营商"
                  clearable
                  size="mini"
                  style="width: 100%"
                  :disabled="pageType === 'detail'"
                  filterable
                >
                  <el-option
                    v-for="dict in providerList"
                    :key="dict.providerCode"
                    :label="dict.providerName"
                    :value="dict.providerCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="站点备注">
                <el-input
                  v-model="form2.remark"
                  type="textarea"
                  size="mini"
                  :rows="5"
                  maxlength="500"
                  show-word-limit
                  :disabled="pageType === 'detail'"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="站点图片上传">
                <!-- <el-upload
                  :headers="upload.headers"
                  :action="upload.url"
                  list-type="picture-card"
                  :on-preview="handlePictureCardPreview"
                  :on-remove="handleRemove"
                  :file-list="form2.fileList"
                  :accept="accept1"
                  :on-success="uploadSuccess"
                  :on-error="uploadError"
                  >
                  <i class="el-icon-plus"></i>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible">
                  <img width="100%" :src="dialogImageUrl" alt="">
                </el-dialog> -->
                <FileUpload
                  type="img"
                  :limit="3"
                  ref="FileUpload"
                  @uploadResult="handleImgUpload"
                  :show-file-list="true"
                  :disabled="pageType === 'detail'"
                  :allowDrag="false"
                />
                <span>{{ form2.fileList.length }}/3</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div v-show="activeName == '3'" class="queryParamsWrap">
        <el-form
          :model="form3"
          :rules="rules3"
          ref="form3"
          :inline="true"
          label-width="140px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="工程经理" prop="buildManager">
                <el-select
                  v-model="form3.buildManager"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in userOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工程联系方式" prop="buildPhone">
                <el-input
                  v-model="form3.buildPhone"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="施工队" prop="buildTeam">
                <el-select
                  v-model="form3.buildTeam"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in buildTeamList"
                    :key="item.constructId"
                    :label="item.constructName"
                    :value="item.constructId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="施工方联系电话" prop="constructionPhone">
                <el-input
                  v-model="form3.constructionPhone"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="施工人员" prop="builder">
                <el-select
                  v-model="form3.builder"
                  size="mini"
                  placeholder=""
                  filterable
                  clearable
                  :disabled="pageType === 'detail'"
                >
                  <el-option
                    v-for="item in userOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商务BD" prop="businessManagerName">
                <el-input
                  v-model="form3.businessManagerName"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="场地负责人" prop="siteManagerName">
                <el-input
                  v-model="form3.siteManagerName"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="场地负责人联系方式" prop="siteManagerPhone">
                <el-input
                  v-model="form3.siteManagerPhone"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="居间商" prop="middleBusiness">
                <el-input
                  v-model="form3.middleBusiness"
                  size="mini"
                  placeholder=""
                  :disabled="pageType === 'detail'"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div v-show="activeName == '4'">
        <el-table :data="tableList">
          <el-table-column type="index" width="50" label="序号" />
          <el-table-column prop="docName" label="文件名称" />
          <el-table-column label="文件类型">
            <template slot-scope="scope">
              {{
                docType.find((el) => el.dictValue == scope.row.docType)
                  .dictLabel
              }}
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="文件大小" />
          <el-table-column prop="uploadTime" label="上传时间" />
          <el-table-column prop="name" label="操作">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleDownload(scope.row)"
                >下载</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="handleDel(scope.row, scope.$index)"
                v-if="pageType !== 'detail'"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="upload-btn-wrap">
          <div class="upload-label">图纸资料：</div>
          <el-upload
            class="upload-demo"
            ref="upload"
            :action="upload.url"
            :headers="upload.headers"
            :data="uploadParam"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
            :before-upload="beforeAvatarUpload"
            :file-list="fileList"
            :accept="accept"
            multiple
            :auto-upload="false"
          >
            <el-button
              slot="trigger"
              size="small"
              type="primary"
              v-if="pageType !== 'detail'"
              >选取文件</el-button
            >
            <el-button
              style="margin-left: 10px;"
              size="small"
              type="success"
              @click="submitUpload"
              v-if="pageType !== 'detail'"
              >开始上传</el-button
            >
            <div slot="tip" class="el-upload__tip">
              支持格式：png .jpg . rar .zip .doc .docx .pdf
              ，单个文件不能超过30MB
            </div>
          </el-upload>
        </div>
      </div>

      <div v-show="activeName == '5'" class="queryParamsWrap">
        <el-form :model="form5" ref="form5" :inline="true" label-width="110px">
          <el-row v-for="(item, index) in form5.typeList" :key="index">
            <el-col :span="9">
              <el-form-item label="业务类型">
                <el-select
                  v-model="item.relaBizType"
                  size="mini"
                  disabled
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in businessTypeOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item
                label="工序项
"
                :prop="'typeList.' + index + '.orderType'"
              >
                <el-select
                  v-model="item.orderType"
                  size="mini"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in useFlowList"
                    :key="item.flowType"
                    :label="item.flowTypeName"
                    :value="item.flowType"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button
                size="mini"
                v-if="form5.typeList.length > 1"
                @click="delItem(index)"
                >删除</el-button
              >
              <el-button type="primary" size="mini" @click="addItem"
                >添加</el-button
              >
            </el-col>
          </el-row>
        </el-form>
      </div>

      <el-dialog
        title="获取经纬度"
        :visible.sync="latLongVisible"
        :close-on-click-modal="false"
        @close="closeLatLongVisible"
        append-to-body
        width="40%"
      >
        <Amap @confirm="getLatLong" height="400px" />
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeVisible">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="preStep"
          v-show="activeName !== '1' && pageType === 'add'"
          >上一步</el-button
        >
        <el-button
          type="primary"
          @click.stop="nextStep"
          v-show="activeName !== '5' && pageType === 'add'"
          >下一步</el-button
        >
        <el-button
          type="primary"
          @click.stop="createProject('1')"
          v-show="
            activeName === '5' && (pageType === 'add' || this.status === '0')
          "
          >创建项目</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitForm('0')"
          v-show="
            activeName === '4' &&
              pageType === 'update' &&
              this.status != '11' &&
              this.status != '9'
          "
          >修改项目</el-button
        >
        <el-button
          type="primary"
          v-show="this.status === '0'"
          @click.stop="saveForm('0')"
          >保存草稿</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Amap from "@/components/Amap/amap.vue";
import { regionData } from "element-china-area-data";
import { useFlowList as useFlowListApi } from "@/api/orderScheduling/workStation.js";
import { getToken } from "@/utils/auth";
import FileUpload from "@/components/Upload/fileUpload.vue";
import {
  addProjectInfo,
  queryProjectInfoById,
  updateProjectInfo,
  queryRepeatProject,
  queryStationCode,
  queryStationDetail,
} from "@/api/projectManage/index.js";
import { listUser } from "@/api/common.js";
import { queryBuildTeamByPage } from "@/api/buildTeam/buildTeam";
import { providerList } from "@/api/provider/provider";
import { queryRepeatStation } from "@/api/station/station";
import {
  PROJECT_CLICK_CREATE_PROJECT_DIALOG_CREATE_BTN,
  PROJECT_CLICK_CREATE_PROJECT_DIALOG_SAVE_BTN,
} from "@/utils/track/track-event-constants";

export default {
  components: {
    Amap,
    FileUpload,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    pageType: {
      default: "add",
    },
    status: {
      default: "0",
    },
    projectId: {
      default: undefined,
    },
    title: {
      default: "新增项目",
    },
    deptOptionList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    let baseUrl =
      process.env.VUE_APP_BASE_API == "/" ? "" : process.env.VUE_APP_BASE_API;
    return {
      activeName: "1",
      form1: {
        projectId: undefined,
        projectCode: undefined,
        projectName: undefined,
        contractFlag: undefined,
        remark: undefined,
      },
      form2: {
        stationId: undefined,
        projectId: undefined,
        stationCode: undefined,
        stationName: undefined,
        provinceCityCounty: [],
        stationAddress: undefined,
        radius: 2000,
        longLat: undefined,
        status: undefined,
        buildDate: undefined,
        openDate: undefined,
        onlineDate: undefined,
        openFlag: undefined,
        stationChargeType: undefined,
        construction: undefined,
        serviceTel: undefined,
        operatorCode: undefined,
        parkNum: undefined,
        remark: undefined,
        fileList: [],
        orgNo: undefined,
        businessMode: [],
      },
      form3: {
        buildManager: undefined,
        buildTeam: undefined,
        builder: undefined,
        buildPhone: undefined,
        constructionPhone: undefined,
        businessManagerName: undefined,
        siteManagerName: undefined,
        siteManagerPhone: undefined,
        middleBusiness: undefined,
      },
      form4: {},
      form5: {
        typeList: [
          {
            relaBizType: "construction",
            orderType: "",
          },
        ],
      },
      rules1: {
        // projectCode: [
        //   { required: true, message: "请填写项目编码", trigger: "blur" },
        // ],
        // projectName: [
        //   { required: true, message: "请填写项目名称", trigger: "blur" },
        // ],
      },
      rules2: {
        stationName: [
          { required: true, message: "请填写站点名称", trigger: "blur" },
        ],
        stationCode: [
          { required: true, message: "请填写站点编码", trigger: "blur" },
        ],
        pileNum: [
          { required: true, message: "请填写充电桩数量", trigger: "change" },
        ],
        provinceCityCounty: [
          { required: true, message: "请选择行政区", trigger: "blur" },
        ],
        stationAddress: [
          { required: true, message: "请填写详细地址", trigger: "blur" },
        ],
        radius: [
          { required: true, message: "请填写半径,单位:米", trigger: "change" },
          { validator: this.validateNumber, trigger: "change" },
        ],
        longLat: [
          { required: true, message: "请选择经纬度", trigger: "change" },
        ],
        orgNo: [
          { required: true, message: "请选择所属大区", trigger: "change" },
        ],
      },
      rules3: {
        buildManager: [
          { required: true, message: "请选择工程经理", trigger: "change" },
        ],
        buildTeam: [
          { required: true, message: "请选择施工队", trigger: "change" },
        ],
        builder: [
          { required: true, message: "请选择施工人员", trigger: "change" },
        ],
      },
      rules5: {
        relaBizType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
        orderType: [
          { required: true, message: "请选择工单类型", trigger: "blur" },
        ],
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/doc/upload",
        // url: "http://charging-maintenance-server-alitest.leo.bangdao-tech.com/charging-test" + "/doc/upload",
      },
      uploadParam: {},
      businessTypeOption: [], //业务类型
      useFlowList: [], //工单类型
      userOption: [], //人员列表
      latLongVisible: false,
      districtOptions: regionData, //省市数据
      fileList: [],
      tableList: [],
      accept1: ".jpg, .jpeg, .png",
      accept: ".png, .jpg, .rar, .zip, .doc, .docx, .pdf",
      options: [],
      stationFileList: [],
      fileLimit: 3,
      dialogImageUrl: "",
      dialogVisible: false,
      docType: [], //文件类型
      stationTypeOption: [], //站点类型字典
      stationStatusDict: [],
      openFlagDict: [],
      stationChargeTypeDict: [],
      constructionDict: [],
      buildTeamList: [],
      providerList: [],
      stationCodeOptions: [],
      businessModeOptions: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    stationCodeChange(item) {
      if (item.stationId) {
        queryStationDetail({ stationId: item.stationId }).then((res) => {
          this.form2 = { ...this.form2, ...res.data };
          this.form2.businessMode =
            res.data?.businessMode === ""
              ? []
              : res.data.businessMode?.split(",");
          this.$refs.FileUpload.fileList = res.data.fileList;
        });
      }
    },
    querySearch(queryString, cb) {
      console.log(queryString);
      queryStationCode({ stationCode: queryString }).then((res) => {
        this.stationCodeOptions = res.data.map((x) => {
          return { ...x, value: x.stationCode };
        });
        cb(this.stationCodeOptions);
      });
    },
    validateNumber(rule, value, callback) {
      // 使用正则表达式校验整数位数为6位，小数位数为2位的数字
      const regex = /^([0-9]{1,10}$)|(^[0-9]{1,10}[\\.]{1}[0-9]{1,2}$)/;
      if (!regex.test(value)) {
        callback(new Error("输入有误，最多输入10位整数，两位小数"));
      }
      callback();
    },
    queryProviderList() {
      let params = {
        pageNum: 1,
        pageSize: 99999,
      };
      providerList(params).then((res) => {
        if (res?.success) {
          this.providerList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    queryTeamList() {
      let params = {
        pageNum: 1,
        pageSize: 99999,
      };
      queryBuildTeamByPage(params).then((res) => {
        if (res?.success) {
          this.buildTeamList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    preStep() {
      if (Number(this.activeName) > 1) {
        this.activeName = (Number(this.activeName) - 1).toString();
      }
    },
    async nextStep() {
      let flag = true;
      console.log("this.activeName", this.activeName);
      if (this.activeName == "1") {
        let params = {
          projectCode: this.form1.projectCode,
          projectName: this.form1.projectName,
          projectId: this.form1.projectId,
          operateType: this.pageType,
        };
        await queryRepeatProject(params).then((res) => {
          if (res.code !== "10000") {
            flag = false;
            this.$message.error(res.message);
          }
        });
      }
      if (this.activeName == "2") {
        let params = {
          stationCode: this.form2.stationCode,
          stationName: this.form2.stationName,
          stationId: this.form2.stationId,
          operateType: this.pageType,
        };
        await queryRepeatStation(params).then((res) => {
          if (res.code !== "10000") {
            flag = false;
            this.$message.error(res.message);
          }
        });
      }
      console.log("flag", flag);
      if (flag) {
        console.log("进入该步骤");
        if (this.activeName == "4") {
          this.activeName = (Number(this.activeName) + 1).toString();
        } else {
          let formName = "form" + this.activeName;
          this.$refs[formName].validate((valid) => {
            if (valid) {
              if (Number(this.activeName) < 5) {
                this.activeName = (Number(this.activeName) + 1).toString();
              }
            } else {
              console.log("error submit!!");
              return false;
            }
          });
        }
      }
    },
    handleClick(val) {},
    beforeLeave(activeName, oldActiveName) {},
    submitForm(operationFlag) {
      this.$refs.form3.validate((valid) => {
        if (valid) {
          this.$refs.form2.validate((valid) => {
            if (valid) {
              this.$refs.form1.validate((valid) => {
                if (valid) {
                  //所有校验成功提交方法
                  this.submitFun(operationFlag);
                } else {
                  console.log("tab1 error submit!!");
                  this.activeName = "1";
                  return false;
                }
              });
            } else {
              console.log("tab2 error submit!!");
              this.activeName = "2";
              return false;
            }
          });
        } else {
          console.log("tab3 error submit!!");
          this.activeName = "3";
          return false;
        }
      });
    },
    createProject(operationFlag) {
      //点击事件上报
      this.reportTrackEvent(PROJECT_CLICK_CREATE_PROJECT_DIALOG_CREATE_BTN);

      this.submitForm(operationFlag);
    },
    saveForm(operationFlag) {
      //点击事件上报
      this.reportTrackEvent(PROJECT_CLICK_CREATE_PROJECT_DIALOG_SAVE_BTN);

      this.submitForm(operationFlag);
    },
    async submitFun(operationFlag) {
      const loading = this.$loading({
        lock: true,
        text: "请稍后",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      let businessFlowList = [];
      this.form5.typeList.forEach((item) => {
        if (item.orderType) {
          let obj = {
            relaBizType: item.relaBizType, //业务类型
            flowType: item.orderType, //流程类型
            flowTypeName: this.useFlowList.find(
              (el) => el.flowType == item.orderType
            ).flowTypeName, //流程类型名称
            flowKey: this.useFlowList.find(
              (el) => el.flowType == item.orderType
            ).flowKey, //流程必传
            flowName: this.useFlowList.find(
              (el) => el.flowType == item.orderType
            ).flowName, //流程名称必传
          };
          businessFlowList.push(obj);
        }
      });
      let params = {
        ...this.form1,
        ...this.form3,
        stationInfo: {
          // 场站信息
          ...this.form2,
          longitude: this.form2.longLat
            ? JSON.parse(this.form2.longLat)[0]
            : "",
          latitude: this.form2.longLat ? JSON.parse(this.form2.longLat)[1] : "",
          province: this.form2.provinceCityCounty[0],
          city: this.form2.provinceCityCounty[1],
          county: this.form2.provinceCityCounty[2],
          businessMode: this.form2.businessMode?.join(","),
        },
        docList: this.tableList,
        businessFlowList: businessFlowList, //工单参数
      };
      if (this.pageType === "add") {
        const { code, data } = await addProjectInfo({
          ...params,
          ...{ operationFlag: operationFlag },
        }).finally(() => {
          loading.close();
        });
        if (code !== "10000") return;
        this.$message.success("项目新建成功");
      } else if (this.pageType === "update") {
        const { code, data } = await updateProjectInfo({
          ...params,
          ...{ operationFlag: operationFlag },
        }).finally(() => {
          loading.close();
        });
        if (code !== "10000") return;
        this.$message.success("项目修改成功");
      }
      this.$emit("update:visible", false);
      this.$emit("update-list");
    },
    closeVisible() {
      this.$emit("update:visible", false);
    },
    closeLatLongVisible() {
      this.latLongVisible = false;
    },
    openLatLong() {
      this.latLongVisible = true;
    },
    getLatLong(obj) {
      console.log("获取经纬度信息", obj);
      this.form2.longLat = JSON.stringify(obj.position);
      this.form2.stationAddress = obj.name;
      this.latLongVisible = false;
    },
    handleDownload(row) {
      console.log("图片信息", row);

      const a = document.createElement("a"); // 创建一个HTML 元素
      let url = row.storePath;
      a.setAttribute("download", row.docName); //download属性
      a.setAttribute("href", url); // href链接
      a.click(); // 自执行点击事件

      // // 假设imageURL是后端返回的图片地址
      // const imageURL = row.storePath;
      // // const imageURL = "https://img1.bdstatic.com/static/common/img/baidu_image_logo_2dd9a28.png";
      // if(row.docType == "gif") {
      //   // 发送一个请求获取图片数据
      //   fetch(imageURL)
      //     .then(response => response.arrayBuffer()) // 获取二进制数据
      //     .then(buffer => {
      //       // 创建Blob对象
      //       const blob = new Blob([buffer], { type: 'image/gif' }); // 指定MIME类型
      //       const blobURL = window.URL.createObjectURL(blob);
      //       const a = document.createElement('a');
      //       a.href = blobURL;
      //       a.download = row.docName + '.' + row.docType; // 设置下载的文件名
      //       a.style.display = 'none';
      //       document.body.appendChild(a);
      //       a.click();
      //       window.URL.revokeObjectURL(blobURL);
      //     })
      //     .catch(error => {
      //       console.error('下载图片失败:', error);
      //     });
      // } else {
      //   // 发送一个请求获取图片数据
      //   fetch(imageURL)
      //     .then(response => response.blob())
      //     .then(blob => {
      //       // 创建Blob对象
      //       const blobURL = window.URL.createObjectURL(blob);
      //       const a = document.createElement('a');
      //       a.href = blobURL;
      //       a.download = row.docName + '.' + row.docType; // 设置下载的文件名
      //       a.style.display = 'none';
      //       document.body.appendChild(a);
      //       a.click();
      //       window.URL.revokeObjectURL(blobURL);
      //     })
      //     .catch(error => {
      //       console.error('下载图片失败:', error);
      //     });
      // }
    },
    handleDel(row, index) {
      this.tableList.splice(index, 1);
    },
    beforeAvatarUpload(file) {
      this.uploadParam.fileList = file;
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    //文件上传成功
    handleSuccess(res, file, fileList) {
      if (res.code == 10000) {
        this.tableList.push(...res.data);
      }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    delItem(index) {
      this.form5.typeList.splice(index, 1);
    },
    addItem() {
      this.form5.typeList.push({
        relaBizType: "construction",
        orderType: "",
      });
    },
    //获取工单类型列表
    async getUseFlowList(businessType) {
      let params = {
        businessTypeList: businessType,
      };
      const { code, data } = await useFlowListApi(params);
      if (code != 10000) return;
      this.useFlowList = data;
    },
    //处理上传结果
    handleImgUpload(result, msg, fileUploadUrls) {
      if (result) {
        //上传成功
        console.log("handleImgUpload", JSON.stringify(fileUploadUrls));
        this.form2.fileList = [];
        this.form2.fileList.push(...fileUploadUrls);
      }
    },

    handleExceed() {},
    fileRemove(file, fileList) {},
    fileChange() {},
    handleFileUploadProgress() {},
    handleFileSuccess() {},

    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 图片上传成功回调
    uploadSuccess(response, file, fileList) {
      // if (response.code == 10000) {
      //   this.form2.stationImage = response.data;
      // }
      console.log("form2.fileList", this.form2.fileList);
    },
    // 图片上传失败回调
    uploadError(err, file, fileList) {
      console.log(err);
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    queryProjectInfoById() {
      let params = {
        projectId: this.projectId,
      };
      queryProjectInfoById(params).then((res) => {
        if (res?.success) {
          //form1
          this.form1.projectId = res.data.projectId;
          this.form1.projectCode = res.data.projectCode;
          this.form1.projectName = res.data.projectName;
          this.form1.contractFlag = res.data.contractFlag;
          this.form1.remark = res.data.remark;
          //form2
          this.form2.stationId = res.data.stationInfo.stationId;
          this.form2.projectId = res.data.stationInfo.projectId;
          this.form2.stationCode = res.data.stationInfo.stationCode;
          this.form2.stationName = res.data.stationInfo.stationName;
          this.form2.pileNum = res.data.stationInfo.pileNum;
          this.form2.stationType = res.data.stationInfo.stationType;
          this.form2.provinceCityCounty =
            res.data.stationInfo.provinceCityCounty;
          this.form2.stationAddress = res.data.stationInfo.stationAddress;
          this.form2.radius = res.data.stationInfo.radius;
          this.form2.longLat = res.data.stationInfo.longLat;
          this.form2.status = res.data.stationInfo.status;
          this.form2.buildDate = res.data.stationInfo.buildDate;
          this.form2.openDate = res.data.stationInfo.openDate;
          this.form2.onlineDate = res.data.stationInfo.onlineDate;
          this.form2.openFlag = res.data.stationInfo.openFlag;
          this.form2.stationChargeType = res.data.stationInfo.stationChargeType;
          this.form2.construction = res.data.stationInfo.construction;
          this.form2.serviceTel = res.data.stationInfo.serviceTel;
          this.form2.operatorCode = res.data.stationInfo.operatorCode;
          this.form2.parkNum = res.data.stationInfo.parkNum;
          this.form2.remark = res.data.stationInfo.remark;
          this.form2.fileList = res.data.stationInfo.fileList;
          this.$refs.FileUpload.fileList = res.data.stationInfo.fileList;
          this.form2.businessMode =
            res.data.stationInfo?.businessMode === ""
              ? []
              : res.data.stationInfo.businessMode?.split(",");
          this.form2.orgNo = res.data.stationInfo.orgNo;
          //form3
          this.form3.buildManager = res.data.buildManager;
          this.form3.builder = res.data.builder;
          this.form3.buildTeam = res.data.buildTeam;
          this.form3.buildPhone = res.data.buildPhone;
          this.form3.constructionPhone = res.data.constructionPhone;
          this.form3.businessManagerName = res.data.businessManagerName;
          this.form3.siteManagerName = res.data.siteManagerName;
          this.form3.siteManagerPhone = res.data.siteManagerPhone;
          this.form3.middleBusiness = res.data.middleBusiness;

          //文件
          this.tableList = res.data.docList;
        } else {
          this.$message.error(res.message);
        }
      });
    },
  },
  async created() {
    queryStationCode({ stationCode: "" }).then((res) => {
      this.stationCodeOptions = res.data;
    });
    this.getDicts("business_mode").then((response) => {
      this.businessModeOptions = response?.data;
    });
    //获取业务类型字典
    this.getDicts("flow_business_type").then((response) => {
      this.businessTypeOption = response?.data;
    });
    //文件类型字典
    this.getDicts("cm_doc_type").then((response) => {
      this.docType = response?.data;
    });
    //站点类型类型字典
    this.getDicts("cm_station_type").then((response) => {
      this.stationTypeOption = response?.data;
    });
    this.getDicts("cm_station_status").then((response) => {
      this.stationStatusDict = response?.data;
    });
    this.getDicts("cm_open_flag").then((response) => {
      this.openFlagDict = response?.data;
    });
    this.getDicts("cm_station_charge_type").then((response) => {
      this.stationChargeTypeDict = response?.data;
    });
    this.getDicts("cm_construction").then((response) => {
      this.constructionDict = response?.data;
    });
    await this.getUseFlowList("construction");
    this.queryTeamList();
    this.queryProviderList();
    this.getListUser();
    if (this.pageType !== "add") {
      this.queryProjectInfoById();
    }
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

.radius-wrap {
  /deep/.el-form-item__content > div {
    display: flex !important;
  }
}
.append-btn {
  /deep/.el-input-group__append {
    width: 100px;
    background-color: #029c7c;
    color: #fff;
    cursor: pointer;
  }
}
/deep/.el-input-group__append {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn-wrap {
  display: flex;
  margin-top: 20px;
  .upload-label {
    margin-top: 10px;
  }
}
</style>
