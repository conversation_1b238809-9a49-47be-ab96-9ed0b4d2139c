<!-- 工单处理 -->
<template>
  <div class="app-container" v-loading="loading">
    <el-row :gutter="20">
      <el-col :span="16">
        <div>
          <el-card>
            <div slot="header" class="card-title-wrap">
              <div class="card-title-line"></div>
              <span>工单信息</span>
            </div>
            <div class="contract-info">
              <img
                v-if="flowStatus == '1'"
                :src="statusImg1"
                class="statusImg"
                alt=""
              />
              <img
                v-if="flowStatus == '2'"
                :src="statusImg2"
                class="statusImg"
                alt=""
              />
              <img
                v-if="flowStatus == '3'"
                :src="statusImg3"
                class="statusImg"
                alt=""
              />
              <div class="queryParamsWrap">
                <el-form :inline="true" :model="projectForm">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="工单编号">
                        <el-input
                          v-model="projectForm.businessNo"
                          disabled
                          size="mini"
                        ></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="工单类型">
                        <!--                        <el-input-->
                        <!--                          v-model="projectForm.flowType"-->
                        <!--                          size="mini"-->
                        <!--                          disabled-->
                        <!--                        ></el-input>-->

                        <el-select
                          v-model="projectForm.flowType"
                          size="mini"
                          disabled
                        >
                          <el-option
                            v-for="item in orderTypeOption"
                            :key="item.dictValue"
                            :label="item.dictLabel"
                            :value="item.dictValue"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="业务类型">
                        <el-select
                          v-model="projectForm.relaBizType"
                          size="mini"
                          disabled
                        >
                          <el-option
                            v-for="item in businessTypeOption"
                            :key="item.dictValue"
                            :label="item.dictLabel"
                            :value="item.dictValue"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="场站名称">
                        <el-input
                          v-model="projectForm.stationName"
                          size="mini"
                          disabled
                        ></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="所属项目">
                        <el-input
                          v-model="projectForm.projectName"
                          size="mini"
                          disabled
                        ></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </div>
          </el-card>
        </div>

        <div style="margin-top: 20px;">
          <el-card>
            <div slot="header" class="card-title-wrap">
              <div class="card-title-line"></div>
              <span>工单详情</span>
            </div>
            <div class="form-create-wrap">
              <div class="work-type-form">
                <!--                <el-divider-->
                <!--                  content-position="left"-->
                <!--                  v-if="reviewNodeList.length > 0"-->
                <!--                >-->
                <!--                  以下是查询信息-->
                <!--                </el-divider>-->
                <!--                <div v-for="(item,index) in reviewNodeList" :key="index">-->
                <!--                  <form-create-->
                <!--                    :rule="item.rule"-->
                <!--                    v-model="receiveFormData"-->
                <!--                    :option="item.option"-->
                <!--                  />-->
                <!--                </div>-->

                <el-divider
                  content-position="left"
                  v-if="dealNodeList.length > 0"
                >
                  请填写以下信息
                </el-divider>
                <div v-for="(item, index) in dealNodeList" :key="index">
                  <form-create
                    :rule="item.rule"
                    v-model="dealFormData[index]"
                    :option="item.option"
                  />

                  <div v-if="showfileName">
                    <div style="font-size:14px;margin:10px 0;">
                      已上传的文件名称：
                    </div>
                    <div v-for="(j, i) in item.rule" :key="i">
                      <div v-if="j.fileList && j.fileList.length > 0">
                        <div v-for="(f, fi) in j.fileList" :key="fi">
                          {{ f.name }}
                          <el-button
                            type="text"
                            size="small"
                            @click="handleDownload(f)"
                            >下载</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <el-divider
                  content-position="left"
                  v-if="commentsList.length > 0"
                >
                  以下是提交信息
                </el-divider>
                <div style="margin-left: 35px">
                  <div
                    v-for="item in commentsList"
                    :key="item.operateTime"
                    class="work-type-comment"
                  >
                    <div
                      style="display: flex; flex-direction: row; justify-content: space-between"
                    >
                      <span>提交结果：{{ item.commentResult }}</span>
                      <span>提交时间：{{ parseTime(item.operateTime) }}</span>
                    </div>
                    <div style="margin-top: 10px">
                      <span>提交人：{{ item.operator }}</span>
                    </div>
                    <div style="margin-top: 10px">
                      <span>备注：{{ item.comment }}</span>
                    </div>
                    <el-divider />
                  </div>
                </div> -->
              </div>
            </div>
            <div class="el-dialog__footer" style="padding: 0">
              <span class="dialog-footer" v-if="taskId === curentTaskId">
                <div v-if="formType === '0'">
                  <el-button
                    v-for="item in commitGroups"
                    :type="item.configExtendValue"
                    :key="item.configId"
                    @click="openHandleCommitDialog(item)"
                  >
                    {{ item.configLabel }}
                  </el-button>
                </div>
              </span>
            </div>
          </el-card>
        </div>
        <div style="margin-top: 20px;">
          <el-card>
            <div slot="header" class="card-title-wrap">
              <div class="card-title-line"></div>
              <span>工单操作记录</span>
            </div>
            <el-timeline :hide-timestamp="true" v-if="recordList.length > 0">
              <el-timeline-item
                placement="top"
                v-for="item in recordList"
                :key="item.orderRecordId"
              >
                <el-card>
                  <el-row style="margin-bottom: 16px">
                    <el-col :span="5">
                      <span class="timeline-title">{{
                        item.operatorTypeName
                      }}</span>
                    </el-col>
                    <el-col :span="14">
                      <span>操作人：{{ item.operatorUserName }}</span>
                    </el-col>
                    <el-col :span="5">
                      <span>{{ item.createTime }}</span>
                    </el-col>
                  </el-row>
                  <el-row>
                    <!-- <span
                      style="display:block;margin-top: 10px"
                      v-if="item.operatorTypeName === '完结工单'"
                      >完结备注: {{ item.remark }}</span
                    >
                    <span
                      style="display:block;margin-top: 10px"
                      v-else-if="item.operatorTypeName === '结束工单'"
                      >结束原因: {{ item.remark }}</span
                    >
                    <span
                      style="display:block;margin-top: 10px"
                      v-else-if="item.operatorTypeName === '处理标签'"
                      >{{ item.remark }}</span
                    >
                    <span
                      style="display:block;margin-top: 10px"
                      v-else-if="item.operatorTypeName === '驳回工单'"
                      >驳回原因: {{ item.remark }}</span
                    >
                    <span
                      style="display:block;margin-top: 10px"
                      v-else-if="item.operatorTypeName === '转派工单'"
                      >{{ item.remark }}</span
                    > -->
                    <template v-if="item.operatorTypeName === '处理工单'">
                      <el-row
                        style="flex-wrap:wrap;align-items:center"
                        type="flex"
                      >
                        <div
                          v-for="(o, i) in item.remark.split(/\【|\】/)"
                          :key="i"
                        >
                          <div v-if="o.startsWith('{') && o.endsWith('}')">
                            <el-image
                              style="width: 150px;height:150px;margin-right:10px"
                              :src="o.replace(/[{}]/g, '')"
                              alt="加载失败"
                              class="avatar"
                              v-if="
                                o.toLowerCase().indexOf('.jpg') > 0 ||
                                  o.toLowerCase().indexOf('.jpeg') > 0 ||
                                  o.toLowerCase().indexOf('.png') != -1
                              "
                              :preview-src-list="[o.replace(/[{}]/g, '')]"
                            />
                            <video
                              style="width: 150px;height:150px;margin-right:10px"
                              v-if="o.toLowerCase().indexOf('.mp4') > 0"
                              :src="o.replace(/[{}]/g, '')"
                              controls
                            ></video>
                            <!-- @click.stop="
                                clickImg(index, [o.replace(/[{}]/g, '')])
                              " -->
                          </div>
                          <span v-else>{{ o }}</span>
                        </div>
                      </el-row>
                    </template>
                    <span v-else style="display:block;margin-top: 10px">
                      {{ item.remark }}
                    </span>
                  </el-row>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <!-- <el-timeline :hide-timestamp="true">
              <el-timeline-item
                placement="top"
                v-for="item in logList"
                :key="item.taskDefId"
              >
                <div class="timestamp">
                  <el-row>
                    <el-col :span="5">
                      <span
                        v-if="
                          item.operatorType != 'createWorkOrder' &&
                            item.operatorType != 'processComplete'
                        "
                        >【{{ item.taskDefName }}】</span
                      >
                      <span>{{ item.operatorName }}</span>
                    </el-col>
                    <el-col :span="5">{{ item.executeTime }}</el-col>
                  </el-row>
                </div>
                <el-card>
                  <h4>
                    操作人：{{
                      item.operatorType === "reAssign" ||
                      item.operatorType === "createWorkOrder"
                        ? item.createUserName
                        : item.taskAssigneeName
                    }}
                  </h4>
                  <el-row v-if="item.operatorType === 'reAssign'">
                    <el-col :span="5">
                      <span>原执行人：{{ item.originalAssigneeName }}</span>
                    </el-col>
                    <el-col :span="5">
                      <span>新执行人：{{ item.taskAssigneeName }}</span>
                    </el-col>
                  </el-row>
                  <h4 v-if="item.operatorType === 'fallback'">
                    驳回至节点：{{ item.fallbackTaskName }}
                  </h4>
                  <h4
                    v-if="
                      item.operatorType != 'createWorkOrder' &&
                        item.operatorType != 'approve' &&
                        item.operatorType != 'processComplete'
                    "
                  >
                    操作备注：{{ item.optReason }}
                  </h4>
                </el-card>
              </el-timeline-item>
            </el-timeline> -->
          </el-card>
        </div>
      </el-col>
      <el-col :span="8">
        <Steps
          title="执行进度"
          :businessNo="businessNo"
          :curentTaskId="curentTaskId"
          @refreshTaskFrom="refreshForm"
        />
      </el-col>
    </el-row>

    <!-- 工单处理确认框 -->
    <el-dialog
      title="提交确认"
      :visible.sync="handleCommit.visible"
      @close="closeHandleCommitDialog"
      :close-on-click-modal="false"
      width="600px"
      append-to-body
      :show-close="false"
    >
      <span v-if="handleCommit.isSubmit">请确认是否提交？</span>
      <el-form
        v-else
        :model="handleCommit.optionForm"
        ref="optionForm"
        label-width="80px"
      >
        <el-row>
          <el-form-item :label="handleCommit.title" prop="opinion">
            <el-input
              type="textarea"
              :rows="5"
              v-model="handleCommit.optionForm.opinion"
              placeholder="请输入备注"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button type="primary" @click="handleWorkOrder">确 定 </el-button>
        <el-button @click="closeHandleCommitDialog">取 消</el-button>
      </div>
    </el-dialog>
    <!-- <PicPreview ref="picPreview"></PicPreview> -->
  </div>
</template>

<script>
import Steps from "./components/steps.vue";
// import PicPreview from "@/components/Upload/picPreview.vue";
import {
  complete,
  dealComplete,
  taskConfigs,
  taskForms,
  useFlowList,
  useFlowList as useFlowListApi,
} from "@/api/orderScheduling/workStation.js";
import { getToken } from "@/utils/auth";
import { getBusinessInfo } from "@/api/business/flow/flow";
import { getDicts } from "@/api/system/dict/data";
import { orderRecord } from "@/api/operationWorkOrder";
export default {
  name: "projectOrderDeal",
  components: {
    Steps,
    //  PicPreview
  },
  data() {
    return {
      loading: false,
      bizType: "",
      businessNo: "",
      flowStatus: "",
      currentRow: undefined,
      type: undefined,
      dialogData: {},
      taskId: "",
      businessTypeOption: [], //业务类型
      orderTypeOption: [], //工单类型
      curentTaskId: "",
      currentNode: {},
      router: "",
      projectForm: {},
      securityForm: {},
      qualityForm: {},
      contractForm: {},
      statusImg1: require("@/assets/icons/icon_in_process.png"),
      statusImg2: require("@/assets/icons/icon_finished.png"),
      statusImg3: require("@/assets/icons/icon_closed.png"),
      emergencyLevelOptions: [],
      regionOptions: [],
      receiveFormData: {},
      dealFormData: [],
      //查看
      reviewNodeList: [],
      //处理
      dealNodeList: [],
      //审核信息
      commentsList: [],
      //获取的原始数据
      dealForms: [],
      commitGroups: [],
      formType: "0",
      handleCommit: {
        isSubmit: false,
        title: "",
        visible: false,
        configName: "",
        signType: "",
        optionForm: {
          opinion: "",
        },
        commitType: {},
      },
      showfileName: false,
      recordList: [],
    };
  },
  computed: {},
  watch: {
    $route: {
      handler(newVal) {
        console.log("newVal", newVal);
        this.businessNo = newVal.query.businessNo;
        this.taskId = newVal.query.taskId;
        this.flowStatus = newVal.query.flowStatus;
        this.curentTaskId = newVal.query.taskId;
        this.router = newVal.query.router;
        this.currentRow = newVal.query.currentRow;
      },
      immediate: true,
    },
  },
  methods: {
    clickImg(index, list) {
      // //点击预览图片
      // this.showImgPreview = true;
      // this.previewImgUrl = o;
      this.$refs.picPreview.open(index, list);
    },
    showDocList(item) {
      const arr = item.remark.split("&");
      return arr && arr[1];
    },
    getRecordList() {
      orderRecord({ orderNo: this.businessNo }).then((res) => {
        this.recordList = res.data;
        if (this.recordList.length < 1) {
          this.$message.info("暂无操作记录");
        }
      });
    },
    handleDownload(f) {
      const a = document.createElement("a"); // 创建一个HTML 元素
      a.setAttribute("download", f.name); //download属性
      a.setAttribute("href", f.url); // href链接
      a.click(); // 自执行点击事件
    },
    async getFlowBusinessType() {
      //业务类型字典
      return await getDicts("flow_business_type").then((response) => {
        this.businessTypeOption = response?.data;
      });
    },
    //获取工单类型列表
    async getUseFlowList() {
      const { code, data } = await useFlowList();
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.flowType;
        element.dictLabel = element.flowTypeName;
      });
      this.orderTypeOption = data;
    },
    refreshForm(nodeInfo) {
      //console.log("taskId", nodeInfo?.taskId);
      this.currentNode = nodeInfo;
      this.curentTaskId = nodeInfo?.taskId;

      if (this.projectForm.stationId) {
        this.getTask();
      }
    },
    async handleWorkOrder() {
      //console.log(this.handleCommit.commitType.configName);
      if (
        this.handleCommit.isSubmit ||
        this.handleCommit.commitType.configName === "APPROVE"
      ) {
        // 表单提交以及审核通过需要校验填写表单的内容
        if (!(await this.checkDealForms())) {
          return;
        }
        // if (this.handleCommit.commitType.configName === "APPROVE") {
        // if (!(await this.checkOptionRule())) {
        //   debugger;
        //   return;
        // }
        // }
        this.commitOrder();
      } else {
        // if (await this.checkOptionRule()) {
        this.commitOrder();
        // }
      }
    },
    commitOrder() {
      const loading = this.$loading({
        lock: true,
        text: "提交中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      const param = {
        configId: this.handleCommit.commitType.configId,
        configName: this.handleCommit.commitType.configName,
        taskId: this.curentTaskId,
        businessNo: this.businessNo,
        opinion: this.handleCommit.optionForm.opinion,
        signType: this.handleCommit.signType,
      };
      if (this.dealNodeList.length > 0) {
        param.dealForms = this.dealForms.map((item, index) => {
          return {
            ...item,
            formJson: JSON.stringify(this.dealNodeList[index].rule),
            formConfig: JSON.stringify(this.dealNodeList[index].option),
          };
        });
      }
      complete(param)
        .then(() => {
          this.$message.success("处理成功");
          // loading.close();
          this.loading = false;
          // this.$emit("freshTable");
          this.closeHandleCommitDialog();
          this.$store.dispatch("tagsView/delVisitedView", this.$route);
          this.$router.push({
            path: this.router,
          });
        })
        .catch(() => {
          loading.close();
          this.loading = false;
        })
        .finally(() => {
          loading.close();
        });
    },
    /**
     * 校验审核通过 / 作废 / 驳回的必填理由
     */
    async checkOptionRule() {
      let flag = false;
      await this.$refs.optionForm.validate((valid) => {
        flag = valid;
      });
      return flag;
    },
    async checkDealForms() {
      let flag = true;
      if (this.dealForms.length > 0) {
        flag = false;
        for (let i = 0; i < this.dealFormData.length; i++) {
          // await this.dealFormData[i].submit(() => {
          //   if (i === this.dealFormData.length - 1) {
          //     flag = true;
          //   }
          // });
          await this.dealFormData[i].validate((valid) => {
            if (valid) {
              flag = true;
            } else {
              this.$message.warning("请填写完整表单信息!");
            }
          });
        }
      }
      return flag;
    },

    cancelDialog() {
      this.$emit("cancelDialog");
    },
    async openHandleCommitDialog(commitType) {
      // 表单提交以及审核通过需要校验填写表单的内容
      if (!(await this.checkDealForms())) {
        return;
      }

      this.handleCommit.commitType = commitType;
      this.handleCommit.visible = true;
      this.handleCommit.optionForm.opinion = "";
      this.handleCommit.configName = "";
      this.handleCommit.signType = "";
      if (commitType.configName === "FORM_SUBMIT") {
        this.handleCommit.isSubmit = true;
      } else {
        this.handleCommit.isSubmit = false;
        if (commitType.configName === "APPROVE") {
          this.handleCommit.title = "备注";
        } else if (commitType.configName === "ALLOW_REJECT") {
          this.handleCommit.title = "驳回理由";
        } else if (commitType.configName === "ALLOW_RECALL") {
          this.handleCommit.title = "召回理由";
        } else if (commitType.configName === "ALLOW_INVALID") {
          this.handleCommit.title = "作废理由";
        } else if (commitType.configName === "ALLOW_SIGN_APPROVE") {
          this.handleCommit.title = "审批意见";
          this.handleCommit.configName = "ALLOW_SIGN_APPROVE";
        }
      }
    },
    closeHandleCommitDialog() {
      this.handleCommit.visible = false;
      this.handleCommit.optionForm.opinion = "";
    },
    async getHeaderInfo() {
      //查询 项目信息
      await this.getProjectInfo();
    },

    async getProjectInfo() {
      let param = {
        businessNo: this.businessNo,
      };

      await getBusinessInfo(param).then((res) => {
        this.projectForm = res.data;
      });
    },
    async getTask() {
      let that = this;
      let params = {
        taskId: this.curentTaskId,
        businessNo: this.businessNo,
        needDealFormData: 1,
        plat: "PC",
      };
      this.loading = true;
      this.showfileName = false;
      await taskForms(params)
        .then((response) => {
          this.commentsList = response.data.comments;
          this.reviewNodeList = response.data.reviewForms.map((item) => {
            let options = JSON.parse(item.formConfig);
            let rule = JSON.parse(item.formJson);
            rule.forEach((element) => {
              element.props = {
                ...element.props,
                disabled: true,
              };
            });
            options.submitBtn = false;
            return {
              rule: rule,
              option: options,
            };
          });
          this.dealForms = response.data.dealForms;
          if (this.dealForms.length > 0) {
            this.formType = this.dealForms[0].formType;
          }
          this.dealNodeList = response.data.dealForms.map((element, index) => {
            let options = JSON.parse(element.formConfig);
            if (!!options.submitBtn) {
              this.formDataCreateShow = false;
            }
            let rule = JSON.parse(element.formJson);
            console.log("rule", rule);
            rule.map((item) => this.handleFormRule(item));
            return {
              rule: rule,
              option: options,
            };
          });
          this.loading = false;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleFormRule(item) {
      let that = this;
      console.log(that);
      if (item.type == "upload") {
        console.log("item", item);
        //添加请求头
        item.props.headers = {
          Authorization: "Bearer " + this.$store.state.user.token,
        };
        if (item.props?.uploadType == "image") {
          item.props.listType = "image";
        } else if (item.props?.uploadType == "file") {
          item.props.listType = "text";
          that.showfileName = true;
        } else {
          item.props.uploadType = "image";
          item.props.listType = "image";
        }
        this.$set(item.props, "onSuccess", function(res, file, fileList) {
          file.url = res.data;
          // item.fileList = fileList;
          that.$set(item, "fileList", fileList);
          console.log(this);
          this.$set(item, "previewSrcList", fileList);
        });
        this.$set(item.props, "onRemove", function(file, fileList) {
          that.$set(item, "fileList", fileList);
        });
      }
      //select接口请求添加token
      if (item.type == "select" && item?.effect?.fetch) {
        this.$set(
          item.effect.fetch.headers,
          "Authorization",
          "Bearer " + getToken()
        );

        //过滤桩信息列表接口，修改入参
        let stationListApi = item?.effect?.fetch?.action;
        if (stationListApi && stationListApi.includes("business/pileList")) {
          //如果两个节点配置了同一个表单则会在taskForm接口中携带stationId, 为了防止重复拼接stationId，每次需做下截取
          stationListApi = stationListApi.slice(
            0,
            stationListApi.indexOf("business/pileList") + 17
          );
          stationListApi += `?stationId=${this.projectForm.stationId}`;
        }
        item.effect.fetch.action = stationListApi;
        item.effect.fetch.parse = (res) => {
          if (res?.data?.length > 0) {
            return res.data.map((item) => {
              return {
                label: item.pileName,
                value: item.pileCode,
              };
            });
          } else {
            return [];
          }
        };
      }
      // 递归设置表单规则
      if (
        (item.type == "FcRow" || item.type == "col") &&
        item.children &&
        item.children.length > 0
      ) {
        item.children.map((object) => this.handleFormRule(object));
      }

      //栅格布局:设置子组件的disabled属性
      if (item.type == "FcRow") {
        if (item?.children?.length > 0) {
          item.children.map((item) => {
            if (item?.children?.length > 0) {
              item.children.map((child) => {
                //未开始|已完成 不可操作,详情 不可操作
                let nodeDisabled = !!(
                  !this.currentNode.taskId || this.currentNode.endTime
                );
                if (child.props) {
                  child.props.disabled = nodeDisabled;
                } else {
                  child.props = {
                    disabled: nodeDisabled,
                  };
                }

                return child;
              });
            }

            return item;
          });
        }
      }

      //非处理情况下表单禁用
      let nodeDisabled = !!(
        !this.currentNode.taskId || this.currentNode.endTime
      ); //未开始|已完成 不可操作,详情 不可操作
      if (item.props) {
        item.props.disabled = nodeDisabled;
      } else {
        item.props = { disabled: nodeDisabled };
      }
    },
    //获取动态按钮
    async getBtn() {
      let params = {
        taskId: this.curentTaskId,
        businessNo: this.businessNo,
        plat: "PC",
      };
      await taskConfigs(params).then((response) => {
        this.commitGroups = response.data.configs;
      });
    },
  },
  created() {
    //合同类型
    // this.getDicts("pm_contract_status").then(response => {
    //   this.contractStatusOptions = response.data;
    // });
    // this.getDicts("pm_emergency_level").then(response => {
    //   this.emergencyLevelOptions = response.data;
    // });
    // this.getDicts("pm_region").then(response => {
    //   this.regionOptions = response.data;
    // });
  },
  mounted() {
    this.getRecordList();
    Promise.all([this.getFlowBusinessType(), this.getUseFlowList()]).then(
      () => {
        setTimeout(() => {
          this.$nextTick(async () => {
            await this.getHeaderInfo();

            if (this.curentTaskId != undefined && this.curentTaskId != "") {
              this.getBtn();
              this.getTask();
            }
          });
        }, 500);
      }
    );

    // setTimeout(() => {
    //
    // }, 500);
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
.contract-info {
  position: relative;
  padding-right: 70px;

  .statusImg {
    position: absolute;
    z-index: 4;
    width: 70px;
    right: 0px;
    top: -40px;
  }
}

.el-dialog__footer {
  display: flex;
  justify-content: flex-end;
}
</style>
