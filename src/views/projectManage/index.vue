<!-- 项目管理 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['projectManage:Export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="deviceTable"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="deviceTableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['projectManage:Add']"
            @click.stop="handleCreate"
          >
            新增
          </el-button>
        </template>
        <template slot="projectCode" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="handleProjectList(row)">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="showFlowInfo(row)">
            {{ row.flowCount }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <!-- 仅能对主项目进行复制，除了主项目的项目状态为草稿和已冻结时，其他状态均可复制。复制过的子项目没有复制操作。 -->
          <el-button
            type="text"
            size="large"
            v-has-permi="['projectManage:copy']"
            @click="copy(row)"
            v-if="
              !['0', '11'].includes(row.status) &&
                !row.projectCode.includes('COPY_')
            "
          >
            复制
          </el-button>
          <template v-if="row.status !== '10' && row.status !== '11'">
            <el-button
              type="text"
              size="large"
              v-has-permi="['projectManage:freeze']"
              @click="freeze(row)"
            >
              冻结
            </el-button>
            <el-button
              type="text"
              size="large"
              @click="handleEdit(row)"
              v-has-permi="['projectManage:Edit']"
              v-if="row.status != '11' && row.status != '9'"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="large"
              v-if="row.status != '1' && row.status != '0'"
              v-has-permi="['projectManage:AddOrder']"
              @click="addOrder(row)"
            >
              添加工单
            </el-button>
            <el-button
              v-if="row.status != '9'"
              @click="handleDel(row)"
              type="text"
              v-has-permi="['projectManage:Del']"
              size="large"
            >
              删除
            </el-button>
            <el-button
              type="text"
              size="large"
              @click="viewLog(row)"
              v-has-permi="['projectManage:log']"
            >
              日志
            </el-button>
          </template>
          <template v-else>
            <el-button
              type="text"
              size="large"
              v-has-permi="['projectManage:freeze']"
              @click="unfreeze(row)"
              v-if="row.status === '11'"
            >
              解冻
            </el-button>
          </template>
        </template>
      </GridTable>
    </el-card>

    <AddProject
      v-if="addProjectDialogVisible"
      :visible.sync="addProjectDialogVisible"
      :page-type="pageType"
      :status="status"
      :deptOptionList="deptAllOptionList"
      :project-id="projectId"
      :title="dialogTitle"
      @update-list="queryProjectInfoByPage"
    />
    <el-dialog
      title="添加工单"
      :visible.sync="addOrderVisible"
      :close-on-click-modal="false"
      @close="closeAddOrderVisible"
      append-to-body
      width="50%"
    >
      <div class="queryParamsWrap">
        <el-form :model="addForm" ref="addForm" :inline="true">
          <el-row v-for="(item, index) in addForm.typeList" :key="index">
            <el-col :span="9">
              <el-form-item
                label="业务类型"
                :prop="'typeList.' + index + '.relaBizType'"
                :rules="{
                  required: true,
                  message: '业务单类型不能为空',
                  trigger: 'blur',
                }"
              >
                <el-select
                  v-model="item.relaBizType"
                  @change="changeRelaBizType(item, index)"
                  size="mini"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in businessTypeOption"
                    :key="item.dictValue"
                    :label="item.dictLabel"
                    :value="item.dictValue"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item
                label="工单类型"
                :prop="'typeList.' + index + '.orderType'"
                :rules="{
                  required: true,
                  message: '工单类型不能为空',
                  trigger: 'blur',
                }"
              >
                <el-select
                  v-model="item.orderType"
                  size="mini"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in item.useFlowList"
                    :key="item.flowType"
                    :label="item.flowTypeName"
                    :value="item.flowType"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button
                size="mini"
                v-if="addForm.typeList.length > 1"
                @click="delItem(index)"
                >删除</el-button
              >
              <el-button type="primary" size="mini" @click="addItem"
                >添加</el-button
              >
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeAddOrderVisible">取 消</el-button>
        <el-button type="primary" @click.stop="submitForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import AddProject from "@/views/projectManage/addProject.vue";
import { getDicts } from "@/api/system/dict/data";
import {
  addProjectConstructionFlow,
  deleteProject,
  exportProjectList,
  freezeProject,
  queryProjectInfoByPage,
  unfreezeProject,
  copyProject,
} from "@/api/projectManage/index.js";
import checkPermission from "@/utils/permission.js";
import { getDeptList, getAllDeptList } from "@/api/operationWorkOrder/index.js";
import { useFlowList as useFlowListApi } from "@/api/orderScheduling/workStation.js";
import { regionData } from "element-china-area-data";
import { exportBusinessList } from "@/api/business/flow/flow";
import { saveAs } from "file-saver";
import { listUser } from "@/api/common";
import {
  PROJECT_CLICK_CREATE_PROJECT_BTN,
  PROJECT_CLICK_PROJECT_REPORT,
} from "@/utils/track/track-event-constants";

export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
    AddProject,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        deviceNo: "",
        deviceName: "",
        realDeviceName: "",
        deviceTypeNo: null,
        deviceModelId: "",
        pumpHouseId: "",
        pageNum: 1,
        pageSize: 10,
      },
      addForm: {
        typeList: [
          {
            relaBizType: "",
            orderType: "",
          },
        ],
      },
      businessTypeOption: [], //业务类型
      noUseBusinessType: [
        "otherRoadTest",
        "acceptance",
        "online",
        "pileRoadTest",
        "construction",
      ],
      useFlowList: [], //工单类型
      options: [],
      finallySearch: null,
      deviceTable: [],
      deviceTableTotal: 0,
      addProjectDialogVisible: false, //显示增加设备档案弹框
      addOrderVisible: false, //添加工单
      projectId: undefined, //项目id
      deviceNo: "", //设备编号
      deviceTypeList: [], //设备类型列表
      deviceModelList: [], //设备型号列表
      statusDict: [], //泵房状态字典
      pumpHouseList: [], //泵房列表
      pageType: "add",
      status: "0",
      dialogTitle: "新增项目",
      config: [],
      columns: [
        {
          field: "projectCode",
          title: "项目编码",
          slots: { default: "projectCode" },
        },
        {
          field: "projectName",
          title: "项目名称",
          customWidth: 200,
        },
        {
          field: "remark",
          title: "项目备注",
          customWidth: 170,
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "belongPlace",
          title: "区域",
          // formatter: ({ cellValue, row, column }) => {
          //   const province = this.areaData.find(
          //     (el) => el.value === row.province
          //   )?.label;
          //   const city = this.areaData.find((el) => el.value === row.city)
          //     ?.label;
          //   const county = this.areaData.find((el) => el.value === row.county)
          //     ?.label;
          //   if (province) {
          //     return province + "-" + city + "-" + county;
          //   }
          //   return "";
          // },
          customWidth: 200,
        },
        {
          field: "stationAddress",
          title: "详细地址",
          customWidth: 200,
        },
        {
          field: "buildManager",
          title: "工程经理",
          formatter: ({ cellValue }) => {
            return (
              this.userOption.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "orgNo",
          title: "所属大区",
          formatter: ({ cellValue }) => {
            return (
              this.deptAllOptionList?.find(
                (item) => item.dictValue == cellValue
              )?.dictLabel || cellValue
            );
          },
        },
        {
          field: "contractFlag",
          title: "是否包干",
          formatter: ({ cellValue }) => {
            return cellValue == "1"
              ? "是"
              : cellValue == "0"
              ? "否"
              : cellValue;
          },
        },
        {
          field: "flowCount",
          title: "工单数量",
          slots: { default: "deviceTypeImg" },
        },
        {
          field: "finishCount",
          title: "已完成数量",
        },
        {
          field: "notFinishCount",
          title: "未完成数量",
        },
        {
          field: "finishRate",
          title: "项目完成进度",
        },
        {
          field: "status",
          title: "项目状态",
          formatter: ({ cellValue, row, column }) => {
            return this.statusDict.find((el) => el.dictValue == cellValue)
              ? this.statusDict.find((el) => el.dictValue == cellValue)
                  .dictLabel
              : cellValue;
          },
        },
        {
          field: "createBy",
          title: "项目创建人",
        },
        {
          field: "acctOrgNo",
          title: "账号所属组织",
          formatter: ({ cellValue }) => {
            return (
              this.deptAllOptionList?.find(
                (item) => item.dictValue == cellValue
              )?.dictLabel || cellValue
            );
          },
        },
        {
          field: "createTime",
          title: "项目创建时间",
        },
        {
          field: "totalCost",
          title: "项目总耗时",
        },
        {
          field: "constructCost",
          title: "施工耗时",
        },
        {
          field: "roadTestCost",
          title: "路测耗时",
        },
        {
          field: "onLineCost",
          title: "上线耗时",
        },
        {
          field: "acceptanceCost",
          title: "验收耗时",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "projectManageTable", //tableId必须项目唯一，用于缓存展示列
      areaData: [],
      deptOptionList: [],
      deptAllOptionList: [],
    };
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { pageNum: 1, pageSize: 10, ...this.$route.params };
    }
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getProjectStatusDicts(),
      this.getAreaData(regionData),
      this.getListUser(),
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      }),
      getAllDeptList({}).then((res) => {
        this.deptAllOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      }),
      //获取业务类型字典
      this.getDicts("flow_business_type").then((response) => {
        if (response?.data && response?.data.length > 0) {
          response.data.forEach((item) => {
            if (this.noUseBusinessType.indexOf(item.dictValue) === -1) {
              this.businessTypeOption.push(item);
            }
          });
        }
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
          //获取项目列表
          this.queryProjectInfoByPage();
        });
      }, 500);
    });
    this.getUseFlowList();
  },
  methods: {
    checkPermission,
    copy(row) {
      this.$confirm("确定复制该项目吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          projectId: row.projectId,
        };
        copyProject(data).then((res) => {
          if (res?.success) {
            this.$message.success("复制成功");
            //更新列表
            this.queryProjectInfoByPage();
          } else {
            this.$message.error("复制失败");
          }
        });
      });
    },
    //添加工单
    async submitForm() {
      let businessFlowList = [];
      this.addForm.typeList.forEach((item) => {
        let obj = {
          relaBizType: item.relaBizType, //业务类型
          flowType: item.orderType, //流程类型
          flowTypeName: item.useFlowList.find(
            (el) => el.flowType == item.orderType
          ).flowTypeName, //流程类型名称
          flowKey: item.useFlowList.find((el) => el.flowType == item.orderType)
            .flowKey, //流程必传
          flowName: item.useFlowList.find((el) => el.flowType == item.orderType)
            .flowName, //流程名称必传
        };
        businessFlowList.push(obj);
      });
      let params = {
        projectId: this.projectId,
        businessFlowList,
      };

      const loading = this.$loading({
        lock: true,
        text: "请稍后",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      const { code, data } = await addProjectConstructionFlow(params).finally(
        () => {
          loading.close();
        }
      );
      if (code != 10000) return;
      this.$message.success("新增成功");
      this.closeAddOrderVisible();
      this.queryProjectInfoByPage();
    },
    delItem(index) {
      this.addForm.typeList.splice(index, 1);
    },
    addItem() {
      this.addForm.typeList.push({
        relaBizType: "",
        orderType: "",
      });
    },
    //获取工单类型列表
    async getUseFlowList(businessType) {
      let params = {
        businessTypeList: businessType,
      };
      const { code, data } = await useFlowListApi(params);
      if (code != 10000) return;
      return data;
    },
    //切换业务类型，查询工单类型
    async changeRelaBizType(item, index) {
      this.addForm.typeList[index].relaBizType = item.relaBizType;
      let list = await this.getUseFlowList(item.relaBizType);
      this.$set(this.addForm.typeList[index], "useFlowList", list);
    },
    //初始化
    initConfig() {
      this.config = [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请输入项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请输入项目名称",
        },
        {
          key: "statusList",
          title: "项目状态",
          type: "select",
          options: this.statusDict,
          multiple: true,
          placeholder: "请选择项目状态",
        },
        {
          key: "region",
          title: "省市区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "createBy",
          title: "项目创建人",
          type: "select",
          options: this.userOption,
          placeholder: "请选择项目创建人",
        },
        {
          key: "builder",
          title: "施工人员",
          type: "select",
          options: this.userOption,
          placeholder: "请选择施工人员",
        },
        {
          key: "buildManager",
          title: "工程经理",
          type: "select",
          options: this.userOption,
          placeholder: "请选择工程经理",
        },
        {
          key: "createTime",
          title: "项目创建时间",
          type: "dateRange",
          placeholder: "请选择项目创建时间",
        },
        {
          key: "orgNo",
          title: "所属大区",
          type: "select",
          placeholder: "请选择所属大区",
          options: this.deptAllOptionList,
        },
        {
          key: "contractFlag",
          title: "是否包干",
          type: "select",
          placeholder: "请选择是否包干",
          options: [
            { dictLabel: "是", dictValue: "1" },
            { dictLabel: "否", dictValue: "0" },
          ],
        },
      ];
    },
    viewLog(row) {
      this.$router.push({
        path: "/projectLogList",
        query: { projectId: row.projectId },
      });
    },
    async getProjectStatusDicts() {
      //获取项目状态字典
      return await getDicts("cm_project_status").then((response) => {
        this.statusDict = response?.data;
      });
    },
    //获取设备类型
    getDeviceTypeList() {
      return getDeviceTypeList({}).then((res) => {
        //构建设备类型树
        this.deviceTypeList = res?.data;
      });
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deviceTypeNo,
        label: node.deviceTypeName,
        children: node.childrenList,
      };
    },
    //获取设备型号
    getDeviceModelList() {
      return getDeviceModelList({ modelStatus: "0" }).then((res) => {
        this.deviceModelList = res?.data;
      });
    },
    //获取泵房列表
    getPumpHouseList(pumpHouseName) {
      let data = { pumpHouseName };
      pumpHouseByPage(data).then((res) => {
        this.pumpHouseList = res?.data;
      });
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryProjectInfoByPage(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryProjectInfoByPage();
    },
    //新建
    handleCreate() {
      this.pageType = "add";
      this.dialogTitle = "新增项目";
      this.addProjectDialogVisible = true;

      //点击事件上报
      this.reportTrackEvent(PROJECT_CLICK_CREATE_PROJECT_BTN);
    },
    //导出
    handleExport(params) {
      this.$confirm("是否确认导出所有数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let args = this._.cloneDeep(params ? params : this.searchForm);
        args = { ...args, provinceList: [], cityList: [], countyList: [] };
        if (Array.isArray(args.region)) {
          args.region.forEach((item) => {
            if (item[0]) {
              args["provinceList"].push(item[0]);
            }
            if (item[1]) {
              args["cityList"].push(item[1]);
            }
            if (item[2]) {
              args["countyList"].push(item[2]);
            }
          });
          delete args.region;
        }
        if (Array.isArray(args.createTime)) {
          args.startTime = args.createTime[0] + " 00：00:00";
          args.endTime = args.createTime[1] + " 23:59:59";
          delete args.createTime;
        }
        if (args.createBy) {
          args.createBy = this.userOption?.find(
            (item) => item.dictValue === args.createBy
          ).dictLabel;
        }
        exportProjectList(args);

        //点击事件上报
        this.reportTrackEvent(PROJECT_CLICK_PROJECT_REPORT);

        this.$message.success("导出任务已提交，请稍后到下载中心下载或查看");
      });
    },
    //获取项目列表
    //获取项目列表
    queryProjectInfoByPage(params) {
      let args = this._.cloneDeep(params ? params : this.searchForm);
      args.deviceTypeNo = this.searchForm.deviceTypeNo; //设备类型
      this.loading = true;
      this.finallySearch = args;
      args = { ...args, provinceList: [], cityList: [], countyList: [] };
      if (Array.isArray(args.region)) {
        args.region.forEach((item) => {
          if (item[0]) {
            args["provinceList"].push(item[0]);
          }
          if (item[1]) {
            args["cityList"].push(item[1]);
          }
          if (item[2]) {
            args["countyList"].push(item[2]);
          }
        });
        delete args.region;
      }
      if (Array.isArray(args.createTime)) {
        args.startTime = args.createTime[0] + " 00：00:00";
        args.endTime = args.createTime[1] + " 23:59:59";
        delete args.createTime;
      }
      if (args.createBy) {
        args.createBy = this.userOption?.find(
          (item) => item.dictValue === args.createBy
        ).dictLabel;
      }
      queryProjectInfoByPage(args)
        .then((res) => {
          this.loading = false;
          this.deviceTable = res?.data;
          this.deviceTableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //设备信息修改
    handleEdit(row) {
      this.pageType = "update";
      this.dialogTitle = row.status === "0" ? "草稿" : "修改项目";
      this.projectId = row.projectId;
      this.addProjectDialogVisible = true;
      this.status = row.status;
    },
    freeze(row) {
      this.$confirm("确定要冻结该项目吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: "冻结中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        freezeProject(row)
          .then((res) => {
            if (res?.success) {
              this.$message.success("冻结成功");
              loading.close();
              //更新列表
              this.queryProjectInfoByPage();
            } else {
              loading.close();
              this.$message.error("冻结失败");
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    unfreeze(row) {
      this.$confirm("确定要解冻该项目吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: "解结中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });

        unfreezeProject(row)
          .then((res) => {
            if (res?.success) {
              this.$message.success("解冻成功");
              loading.close();
              //更新列表
              this.queryProjectInfoByPage();
            } else {
              loading.close();
              this.$message.error("解冻失败");
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    showFlowInfo(row) {
      this.$router.push({
        path: "/orderList",
        query: { projectId: row.projectId, activeName: "3" },
      });
    },
    //项目列表
    handleProjectList(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.addProjectDialogVisible = true;
    },
    //添加工单
    addOrder(row) {
      this.projectId = row.projectId;
      this.addOrderVisible = true;
    },
    closeAddOrderVisible() {
      this.$refs.addForm.resetFields();
      this.addOrderVisible = false;
    },
    //删除设备
    handleDel(row) {
      this.$confirm("确定要删除该项目吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          projectId: row.projectId,
        };

        const loading = this.$loading({
          lock: true,
          text: "删除中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        deleteProject(data)
          .then((res) => {
            if (res?.success) {
              this.$message.success("删除成功");
              loading.close();
              //更新列表
              this.queryProjectInfoByPage();
            } else {
              loading.close();
              this.$message.error("删除失败");
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryProjectInfoByPage(this.finallySearch);
    },
    getAreaData(list) {
      list.forEach((item) => {
        this.areaData.push({ label: item.label, value: item.value });
        if (item?.children) {
          this.getAreaData(item.children);
        }
      });
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
</style>
