export const getDeviceList = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      const obj = {
        "code": "10000",
        "data": [
            {
                "deviceId": "341258124944113664",
                "deviceModelId": "341257994346070016",
                "deviceName": "压力计",
                "deviceNo": "S1000000000001",
                "deviceStatus": "3",
                "deviceTypeName": "压力计",
                "deviceTypeNo": "LX10000011",
                "installDate": "2023-07-31",
                "modelNo": "出水压力设备类型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "出水压力设备名称",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "337190348931637248",
                "deviceModelId": "331458658615631872",
                "deviceName": "压力计",
                "deviceNo": "S1000000000308",
                "deviceStatus": "3",
                "deviceTypeName": "压力计",
                "deviceTypeNo": "LX10000011",
                "installDate": "2023-07-20",
                "modelNo": "压力计",
                "pumpHouseName": "yk测试泵房",
                "realDeviceName": "yk压力计",
                "remark": "yk压力计",
                "serviceLife": 1
            },
            {
                "deviceId": "337188684065579008",
                "deviceModelId": "331458658615631872",
                "deviceName": "压力计",
                "deviceNo": "S1000000000307",
                "deviceStatus": "3",
                "deviceTypeName": "压力计",
                "deviceTypeNo": "LX10000011",
                "installDate": "2023-07-20",
                "modelNo": "压力计",
                "pumpHouseName": "yk测试泵房",
                "realDeviceName": "yk压力计",
                "remark": "yk压力机",
                "serviceLife": 1
            },
            {
                "deviceId": "336469427040280576",
                "deviceModelId": "331458658615631872",
                "deviceName": "余氯仪",
                "deviceNo": "S1000000000306",
                "deviceStatus": "3",
                "deviceTypeName": "压力计",
                "deviceTypeNo": "LX10000011",
                "installDate": "2023-07-18",
                "modelNo": "压力计",
                "pumpHouseName": "祖安泵房",
                "realDeviceName": "lxl测试设备2",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "332932378941087744",
                "deviceModelId": "319866369359446016",
                "deviceName": "泵1",
                "deviceNo": "S1000000000305",
                "deviceStatus": "3",
                "deviceTypeName": "泵",
                "deviceTypeNo": "LX10000001",
                "installDate": "2023-07-08",
                "modelNo": "升压泵A型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "泵1",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "332931103226748928",
                "deviceModelId": "319866369359446016",
                "deviceName": "压力计",
                "deviceNo": "S1000000000304",
                "deviceStatus": "3",
                "deviceTypeName": "泵",
                "deviceTypeNo": "LX10000001",
                "installDate": "2023-07-08",
                "modelNo": "升压泵A型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "进水压力",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "332930019066273792",
                "deviceModelId": "319866369359446016",
                "deviceName": "压力计",
                "deviceNo": "S1000000000303",
                "deviceStatus": "3",
                "deviceTypeName": "泵",
                "deviceTypeNo": "LX10000001",
                "installDate": "2023-07-08",
                "modelNo": "升压泵A型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "出水压力表",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "332929948471943168",
                "deviceModelId": "319866369359446016",
                "deviceName": "压力计",
                "deviceNo": "S1000000000302",
                "deviceStatus": "3",
                "deviceTypeName": "泵",
                "deviceTypeNo": "LX10000001",
                "installDate": "2023-07-08",
                "modelNo": "升压泵A型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "进水压力表",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "332552224892915712",
                "deviceModelId": "319866369359446016",
                "deviceName": "流量计",
                "deviceNo": "S1000000000301",
                "deviceStatus": "3",
                "deviceTypeName": "泵",
                "deviceTypeNo": "LX10000001",
                "installDate": "2023-07-07",
                "modelNo": "升压泵A型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "流量计",
                "remark": "",
                "serviceLife": 1
            },
            {
                "deviceId": "332552097289605120",
                "deviceModelId": "319866369359446016",
                "deviceName": "变频器2",
                "deviceNo": "S1000000000300",
                "deviceStatus": "3",
                "deviceTypeName": "泵",
                "deviceTypeNo": "LX10000001",
                "installDate": "2023-07-07",
                "modelNo": "升压泵A型",
                "pumpHouseName": "李小海的测试泵房",
                "realDeviceName": "变频器2",
                "remark": "",
                "serviceLife": 1
            }
        ],
        "message": "SUCCESS",
        "pageNum": 1,
        "pageSize": 10,
        "success": true,
        "total": 302,
        "traceId": "0AE964F216911332233556142"
    }
      resolve(obj)
    }, 1000);
  })
} 