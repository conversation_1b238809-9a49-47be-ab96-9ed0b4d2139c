<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="dataList"
        :checkbox="true"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="total"
        @changePage="changePage"
        @handleSelectionChange="handleSelectionChange"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            v-hasPermi="['system:dict:add']"
            >新增</el-button
          >
          <!-- <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click.stop="handleUpdate"
            v-hasPermi="['system:dict:edit']"
            >修改</el-button
          >
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click.stop="handleDelete"
            v-hasPermi="['system:dict:remove']"
            >删除</el-button
          > -->
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row)"
            v-hasPermi="['system:dict:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(row)"
            v-hasPermi="['system:dict:remove']"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典类型">
          <el-input v-model="form.dictType" :disabled="true" />
        </el-form-item>
        <el-form-item label="数据标签" prop="dictLabel">
          <el-input v-model="form.dictLabel" placeholder="请输入数据标签" />
        </el-form-item>
        <el-form-item label="数据键值" prop="dictValue">
          <el-input v-model="form.dictValue" placeholder="请输入数据键值" />
        </el-form-item>
        <el-form-item label="显示排序" prop="dictSort">
          <el-input-number
            v-model="form.dictSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listData,
  getData,
  delData,
  addData,
  updateData,
  exportData,
} from "@/api/system/dict/data";
import { listType, getType } from "@/api/system/dict/type";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  components: { GridTable, AdvancedForm },

  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 字典表格数据
      dataList: [],
      // 默认字典类型
      defaultDictType: "",
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 类型数据字典
      typeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dictLabel: undefined,
        dictType: undefined,
        status: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dictLabel: [
          { required: true, message: "数据标签不能为空", trigger: "blur" },
        ],
        dictValue: [
          { required: true, message: "数据键值不能为空", trigger: "blur" },
        ],
        dictSort: [
          { required: true, message: "数据顺序不能为空", trigger: "blur" },
        ],
      },
      tableId: "dictTableTable",
      config: [],
      columns: [
        // { type: "checkbox", width: 60 },
        {
          field: "dictCode",
          title: "字典编号",
        },
        {
          field: "dictLabel",
          title: "字典标签",
        },
        {
          field: "dictValue",
          title: "字典键值",
        },
        {
          field: "statusValue",
          title: "状态",
        },
        {
          field: "remark",
          title: "备注",
          showOverflowTooltip: true,
        },
        {
          field: "createTimeValue",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 220,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  created() {
    const dictId = this.$route.params && this.$route.params.dictId;
    Promise.all([this.getTypeList(), this.getNormalStatusDict()]).then(
      (res) => {
        this.getType(dictId);
      }
    );
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "dictType",
          title: "字典名称",
          type: "select",
          placeholder: "请选择字典名称",
          options: this.typeOptions,
          optionValue: "dictType",
          optionLabel: "dictName",
        },
        {
          key: "dictLabel",
          title: "字典标签",
          type: "input",
          placeholder: "请输入字典标签",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "请选择数据状态",
          options: this.statusOptions,
        },
      ];
    },
    btn(row, col, event) {
      row.flag = !row.flag;
      this.$refs.multipleTable.toggleRowSelection(row, row.flag);
    },
    getNormalStatusDict() {
      return this.getDicts("sys_normal_status").then((response) => {
        this.statusOptions = response.data;
      });
    },
    /** 查询字典类型详细 */
    getType(dictId) {
      return getType(dictId).then((response) => {
        this.queryParams.dictType = response.data.dictType;
        this.defaultDictType = response.data.dictType;
        this.initConfig();
        this.getList();
      });
    },
    /** 查询字典类型列表 */
    getTypeList() {
      return listType().then((response) => {
        this.typeOptions = response.data;
      });
    },
    /** 查询字典数据列表 */
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      listData(args).then((response) => {
        this.dataList = response.data;
        this.total = response.total;
        this.loading = false;
        this.dataList.forEach((element) => {
          //状态
          this.$set(element, "statusValue", this.statusFormat(element));
          //创建时间
          this.$set(
            element,
            "createTimeValue",
            this.parseTime(element.createTime)
          );
        });
      });
    },
    // 数据状态字典翻译
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dictCode: undefined,
        dictLabel: undefined,
        dictValue: undefined,
        dictSort: 0,
        status: "0",
        remark: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加字典数据";
      this.form.dictType = this.queryParams.dictType;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.dictCode);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dictCode = row.dictCode || this.ids;
      getData(dictCode).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改字典数据";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.dictCode != undefined) {
            updateData(this.form).then((response) => {
              if (response.code == 10000) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.message);
              }
            });
          } else {
            addData(this.form).then((response) => {
              if (response.code == 10000) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dictCodes = row.dictCode || this.ids;
      this.$confirm(
        '是否确认删除字典编码为"' + dictCodes + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return delData(dictCodes);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        ["catch"](function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function() {
          return exportData(queryParams);
        })
        .then((response) => {
          this.download(response.message);
        })
        ["catch"](function() {});
    },
    //分页切换
    changePage() {
      this.getList();
    },
  },
};
</script>
