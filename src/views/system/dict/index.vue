<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="typeList"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            v-hasPermi="['system:dict:add']"
            >新增</el-button
          >
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            size="large"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row)"
            v-hasPermi="['system:dict:edit']"
            >修改</el-button
          >
          <el-button
            size="large"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(row)"
            v-hasPermi="['system:dict:remove']"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="字典名称" prop="dictName">
          <el-input v-model="form.dictName" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item label="字典类型" prop="dictType">
          <el-input v-model="form.dictType" placeholder="请输入字典类型" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listType,
  getType,
  delType,
  addType,
  updateType,
  exportType,
} from "@/api/system/dict/type";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  components: { GridTable, AdvancedForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 字典表格数据
      typeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dictName: undefined,
        dictType: undefined,
        status: undefined,
        dateRange: [],
      },
      finallySearch: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dictName: [
          { required: true, message: "字典名称不能为空", trigger: "blur" },
        ],
        dictType: [
          { required: true, message: "字典类型不能为空", trigger: "blur" },
        ],
      },
      config: [],
      columns: [
        {
          field: "dictId",
          title: "字典编号",
        },
        {
          field: "dictName",
          title: "字典名称",
          showOverflowTooltip: true,
        },
        {
          field: "dictTypeHtml",
          title: "字典类型",
          showOverflowTooltip: true,
          slots: {
            default: ({ row }) => {
              const href = `/dict/type/data/${row.dictId}`;
              return (
                <router-link to={href} class="link-type">
                  {row.dictType}
                </router-link>
              );
            },
          },
        },
        {
          field: "statusValue",
          title: "状态",
        },
        {
          field: "remark",
          title: "备注",
        },
        {
          field: "createTimeValue",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "dictTable",
    };
  },
  created() {
    this.getDicts("sys_normal_status")
      .then((response) => {
        this.statusOptions = response.data;
        this.initConfig();
        this.getList();
      })
      .catch((err) => {
        console.log("getDicts");
      });
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "dictName",
          title: "字典名称",
          type: "input",
          placeholder: "请输入字典名称",
        },
        {
          key: "dictType",
          title: "字典类型",
          type: "input",
          placeholder: "请输入字典类型",
          minWidth: 260,
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          options: this.statusOptions,
          placeholder: "请选择字典状态",
        },
        {
          key: "dateRange",
          title: "创建时间",
          type: "dateRange",
        },
      ];
    },
    btn(row, col, event) {
      row.flag = !row.flag;
      this.$refs.multipleTable.toggleRowSelection(row, row.flag);
    },
    /** 查询字典类型列表 */
    getList(params) {
      this.loading = true;
      const args = this._.cloneDeep(params ? params : this.queryParams);
      const { dateRange, ...res } = args;
      let query = this.addDateRangeCreate(res, dateRange);
      this.finallySearch = query;
      listType(query).then((response) => {
        this.typeList = response.data;
        this.total = response.total;
        this.typeList.forEach((element) => {
          //状态
          this.$set(element, "statusValue", this.statusFormat(element));
          //创建时间
          this.$set(
            element,
            "createTimeValue",
            this.parseTime(element.createTime)
          );
        });
        this.loading = false;
      });
    },
    // 字典状态字典翻译
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dictId: undefined,
        dictName: undefined,
        dictType: undefined,
        status: "0",
        remark: undefined,
        dateRange: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        params.pageSize = this.queryParams.pageSize;
      }
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加字典类型";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.dictId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dictId = row.dictId || this.ids;
      getType(dictId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改字典类型";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.dictId != undefined) {
            updateType(this.form).then((response) => {
              if (response.code == 10000) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.message);
              }
            });
          } else {
            addType(this.form).then((response) => {
              if (response.code == 10000) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dictIds = row.dictId || this.ids;
      this.$confirm(
        '是否确认删除字典编号为"' + dictIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return delType(dictIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        ["catch"](function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有类型数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function() {
          return exportType(queryParams);
        })
        .then((response) => {
          this.download(response.message);
        })
        ["catch"](function() {});
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
  },
};
</script>
