<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :column-auto-fit="false"
        :tableData="deptList"
        :checkbox="false"
        :seq="true"
        :loading="loading"
        :tableId="tableId"
        :treeConfig="{
          children: 'children',
          expandAll: true,
        }"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd0"
            v-if="$store.getters.deptId == tenantId"
            v-hasPermi="['system:dept:add']"
            >新增
          </el-button>
        </template>
        <!-- 状态 -->
        <!-- <template slot="status" slot-scope="{ row }">
          <span>{{ statusFormat(row) }}</span>
        </template> -->
        <!-- 创建时间 -->
        <!-- <template slot="createTime" slot-scope="{ row }">
          <span>{{ parseTime(row.createTime) }}</span>
        </template> -->
        <!-- 组织类型 -->
        <!-- <template slot="orgType" slot-scope="{ row }">
          <span>{{ orgTypeFormat(row) }}</span>
        </template> -->
        <template slot="operation" slot-scope="{ row }">
          <el-button
            size="large"
            type="text"
            icon="el-icon-edit"
            v-show="row.deptId !== $store.getters.deptId"
            @click.stop="handleUpdate(row)"
            v-hasPermi="['system:dept:edit']"
            >修改
          </el-button>
          <el-button
            size="large"
            type="text"
            icon="el-icon-plus"
            @click.stop="handleAdd(row)"
            v-hasPermi="['system:dept:add']"
            >新增
          </el-button>
          <el-button
            v-if="row.parentId != 0 && row.deptId !== $store.getters.deptId"
            size="large"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(row)"
            v-hasPermi="['system:dept:remove']"
            >删除
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <!--    <pagination-->
    <!--      v-show="total > 0"-->
    <!--      :total="total"-->
    <!--      :page.sync="queryParams.pageNum"-->
    <!--      :limit.sync="queryParams.pageSize"-->
    <!--      @pagination="getList"-->
    <!--    />-->
    <!-- 添加或修改部门对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col
            :span="24"
            v-if="
              form.parentId !== 0 && (form.parentId !== 100 || tenantId === 100)
            "
          >
            <el-form-item
              v-if="form.parentId == this.tenantId"
              label="所属机构"
              prop="parentId"
            >
              <treeselect
                v-model="form.parentId"
                :options="deptOptions"
                placeholder="选择上级"
              />
            </el-form-item>
            <el-form-item v-else label="所属组织" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="deptOptions"
                placeholder="选择上级"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="组织名称" prop="deptName">
              <el-input
                v-model="form.deptName"
                placeholder="请输入部门名称"
                :maxlength="15"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织类型" prop="orgType">
              <el-select
                v-model="form.orgType"
                style="width: 100%"
                placeholder="组织类型"
              >
                <el-option
                  v-for="dict in orgTypeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number
                v-model="form.orderNum"
                style="width: 100%"
                :controls="false"
                :min="0"
                :precision="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="leader">
              <el-input
                v-model="form.leader"
                placeholder="请输入负责人"
                maxlength="20"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入联系电话"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model="form.email"
                placeholder="请输入邮箱"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="组织状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDept,
  getDept,
  treeselect,
  delDept,
  addDept,
  updateDept,
} from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
export default {
  components: { Treeselect, GridTable, AdvancedForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 表格树数据
      deptList: [],
      // 部门部门树选项
      deptOptions: undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 状态数据字典
      statusOptions: [],
      // 组织类型
      orgTypeOptions: [
        {
          value: "company",
          label: "公司",
        },
        {
          value: "department",
          label: "部门",
        },
      ],
      tenantId: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptName: undefined,
        status: undefined,
      },
      finallySearch: null,
      // 总条数
      total: 0,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        parentId: [
          { required: true, message: "上级组织不能为空", trigger: "blur" },
        ],
        deptName: [
          { required: true, message: "组织名称不能为空", trigger: "blur" },
        ],
        orderNum: [
          { required: true, message: "显示排序不能为空", trigger: "blur" },
        ],
        orgType: [
          { required: true, message: "组织类型不能为空", trigger: "blur" },
        ],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
      },
      tableId: "deptTable",
      config: [],
      columns: [
        {
          field: "deptName",
          title: "组织名称",
          width: 200,
          treeNode: true,
        },
        {
          field: "orderNum",
          title: "排序",
          width: 50,
        },
        {
          field: "status",
          title: "状态",
          width: 100,
          formatter: this.statusFormat,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 200,
        },
        {
          field: "orgType",
          title: "组织类型",
          width: 200,
          formatter: this.orgTypeFormat,
        },
        {
          field: "leaderv",
          title: "负责人",
          width: 200,
        },
        {
          title: "操作",
          minWidth: 220,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  created() {
    this.getDicts("sys_normal_status").then((response) => {
      this.statusOptions = response.data;
      this.initConfig();
      this.getList();
    });
    this.tenantId = this.$store.getters.tenantId;
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "deptName",
          title: "组织名称",
          type: "input",
          placeholder: "请输入组织名称",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          options: this.statusOptions,
          placeholder: "请选择状态",
        },
      ];
    },

    /** 查询部门列表 */
    getList(params) {
      this.loading = true;
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.finallySearch = args
      listDept(args).then((response) => {
        this.deptList = response.data;
        this.total = response.total;
        this.loading = false;
        // this.deptList.forEach((element) => {
        //   //状态
        //   this.$set(element, "statusValue", this.statusFormat(element));
        //   //创建时间
        //   this.$set(
        //     element,
        //     "createTimeValue",
        //     this.parseTime(element.createTime)
        //   );
        //   //组织类型
        //   this.$set(element, "orgTypeValue", this.orgTypeFormat(element));
        // });
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 字典状态字典翻译
    statusFormat(data) {
      const row = data.row;
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    orgTypeFormat(data) {
      const row = data.row;
      if (row.orgType == "company") {
        return "公司";
      } else if (row.orgType == "department") {
        return "部门";
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deptId: undefined,
        parentId: 100,
        deptName: undefined,
        orderNum: undefined,
        leader: undefined,
        phone: undefined,
        email: undefined,
        status: "0",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1
      params.pageSize = this.queryParams.pageSize
      this.getList(params);
      /*      this.getDicts('sys_normal_status').then(response => {
              this.statusOptions = response.data;
            });
            this.tenantId = this.$store.getters.tenantId;*/
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
      /*      this.getDicts('sys_normal_status').then(response => {
              this.statusOptions = response.data;
            });
            this.tenantId = this.$store.getters.tenantId;*/
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != undefined) {
        this.form.parentId = row.deptId;
      }
      this.open = true;
      this.title = "添加组织";
    },
    handleAdd0(row) {
      this.reset();
      this.getTreeselect();
      if (row != undefined) {
        this.form.parentId = this.$store.getters.deptId;
      }
      this.open = true;
      this.title = "添加组织";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect();
      getDept(row.deptId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改组织";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.deptId != undefined) {
            updateDept(this.form).then((response) => {
              if (response.code === "10000") {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          } else {
            addDept(this.form).then((response) => {
              if (response.code === "10000") {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(
        '是否确认删除名称为"' + row.deptName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delDept(row.deptId);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        ["catch"](function () {});
    },
    //分页切换
    changePage() {
      if(this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum
        this.finallySearch.pageSize = this.queryParams.pageSize
      }
      this.getList(this.finallySearch);
    },
  },
};
</script>
