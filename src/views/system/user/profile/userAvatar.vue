<template>
  <div>
    <img v-bind:src="user.avatar ? user.avatar : options.img" @click.stop="editCropper()" title="点击上传头像" class="img-circle img-lg" />
    <el-dialog v-if="open" :title="title" :visible.sync="open" width="800px">
      <el-row>
        <el-col :xs="24" :md="12" :style="{ height: '350px' }">
          <vue-cropper
            ref="cropper"
            :img="user.avatar ? user.avatar : options.img"
            :info="true"
            :autoCrop="options.autoCrop"
            :autoCropWidth="options.autoCropWidth"
            :autoCropHeight="options.autoCropHeight"
            :fixedBox="options.fixedBox"
            @realTime="realTime"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{ height: '350px' }">
          <div class="avatar-upload-preview">
            <img :src="previews.url" :style="previews.img" />
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="{ span: 1, offset: 2 }" :md="2">
          <el-button icon="el-icon-plus" size="small" @click.stop="changeScale(1)"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <el-button icon="el-icon-minus" size="small" @click.stop="changeScale(-1)"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <el-button icon="el-icon-refresh-left" size="small" @click.stop="rotateLeft()"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <el-button icon="el-icon-refresh-right" size="small" @click.stop="rotateRight()"></el-button>
        </el-col>
        <el-col :lg="{ span: 1, offset: 1 }" :md="2">
          <!-- <el-button size="small" @click.stop="changeImg">更换图片</el-button> -->
          <el-upload
            class="upload-demo"
            action="aa"
            :http-request="chooseImg"
            :headers="headers"
            :limit="1"
            :show-file-list="false"
            :auto-upload="true"
            :data="{updateSupport:0}"
            accept=".jpg,.jpeg,.png,.gif"
            :file-list="fileList">
            <el-button size="small" type="primary">更换图片</el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{ span: 2, offset: 6 }" :md="2">
          <el-button type="primary" size="small" @click.stop="uploadImg()">上 传</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';
import store from '@/store';
import { VueCropper } from 'vue-cropper';
import { uploadAvatar } from '@/api/system/user';

export default {
  components: { VueCropper },
  props: {
    user: {
      type: Object
    }
  },
  data() {
    return {
      fileList: [],
      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      imgName: "active.jpg",
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      title: '修改头像',
      // action: process.env.VUE_APP_BASE_API + "/system/user/profile/avatar",
      options: {
        img: store.getters.avatar, // 裁剪图片的地址
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200, // 默认生成截图框高度
        fixedBox: true // 固定截图框大小 不允许改变
      },
      previews: {}
    };
  },
  methods: {
    // 编辑头像
    editCropper() {
      this.open = true;
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf('image/') == -1) {
        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。');
      } else {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          this.options.img = reader.result;
        };
      }
    },
    // 上传图片
    uploadImg() {
      this.$refs.cropper.getCropBlob(data => {
        const formData = new FormData();
        // let fileOfBlob = new File([data], this.imgName);
        formData.append('picture', data);
        uploadAvatar(formData).then(response => {
          if (response.code == 10000) {
            this.open = false;
            this.options.img = response.data;
            this.msgSuccess('修改成功');
            this.$emit("emitFun")
            // this.$store.commit('SET_AVATAR', response.date)
            //剪裁的图片返回的blob结尾的地址,无法正常显示，故不在此处更新vuex，从父页面调取接口
          } else {
            this.msgError(response.message);
          }
          this.$refs.cropper.clearCrop();
        });
      });
    },
    //更换图片
    chooseImg(f) {
      let param = new FormData(); //创建form对象
         param.append('picture',f.file);//通过append向form对象添加数据
         uploadAvatar(param)//上传
         .then(response=>{
           console.log("换图", response);
           if(response.code == "10000"){
             this.options.img = response.data;
              this.$message.success("上传成功!");
              this.$emit("emitFun")
              this.open = false
              // this.$store.commit('SET_AVATAR', response.date)
           }               
         })
    },
    // 实时预览
    realTime(data) {
      this.previews = data;
    }
  }
};
</script>
