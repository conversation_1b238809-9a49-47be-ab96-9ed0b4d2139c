<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="roleList"
        :checkbox="true"
        :batchDelete="true"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="total"
        @changePage="changePage"
        @handleSelectionChange="handleSelectionChange"
        :loading="loading"
        :tableId="tableId"
        row-id="roleId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            v-hasPermi="['system:role:add']"
            >新增
          </el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click.stop="handleDelete"
            v-hasPermi="['system:role:remove']"
            >删除
          </el-button>
        </template>
        <!-- 状态 -->
        <!-- <template slot="statusHtml" slot-scope="{ row, $index }">
          <el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
          ></el-switch>
        </template> -->
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            size="large"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row)"
            v-hasPermi="['system:role:edit']"
            >修改
          </el-button>
          <el-button
            size="large"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleSelect(row)"
            v-hasPermi="['system:role:edit']"
            v-if="row.roleId == userRoleId"
            >查看
          </el-button>
          <el-button
            size="large"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(row)"
            v-hasPermi="['system:role:remove']"
            v-if="row.roleId != userRoleId"
            >删除
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="form.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <!--        <el-form-item label="角色顺序" prop="roleSort">
          <el-input-number v-model="form.roleSort" controls-position="right" :min="0"/>
        </el-form-item>-->
        <el-form-item label="权限字符" prop="roleKey">
          <el-select
            filterable
            v-model="form.roleKey"
            size="mini"
            placeholder="请选择"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in roleKeyList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-tree
            :data="menuOptions"
            show-checkbox
            ref="menu"
            node-key="id"
            empty-text="加载中，请稍后"
            :props="defaultProps"
          ></el-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open2" width="500px">
      <el-form ref="form2" :model="form2" :rules="rules" label-width="80px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="form2.roleName"
            placeholder="请输入角色名称"
            disabled="true"
          />
        </el-form-item>
        <!--        <el-form-item label="角色顺序" prop="roleSort">
          <el-input-number v-model="form2.roleSort" controls-position="right" :min="0" disabled="true"/>
        </el-form-item>-->
        <el-form-item label="备注">
          <el-input
            v-model="form2.remark"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
            disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form2.status" disabled="true">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-tree
            :data="menuOptions2"
            show-checkbox
            ref="menu"
            node-key="id"
            empty-text="加载中，请稍后"
            :props="defaultProps"
            :disabled="true"
          ></el-tree>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRole,
  getRole,
  delRole,
  addRole,
  updateRole,
  exportRole,
  getUserRole,
  changeRoleStatus,
} from "@/api/system/role";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";
import {
  treeselect as deptTreeselect,
  roleDeptTreeselect,
} from "@/api/system/dept";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";

export default {
  components: { GridTable, AdvancedForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      open2: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      //当前登录账号的角色
      userRoleId: null,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 数据范围选项
      dataScopeOptions: [
        // {
        //   value: "1",
        //   label: "全部数据权限",
        // },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本组织及以下数据权限",
        },
      ],
      // 菜单列表
      menuOptions: [],
      menuOptions2: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: undefined,
        roleId: undefined,
        status: undefined,
      },
      finallySearch: null,
      // 表单参数
      form: {},
      // 查看时用于回显
      form2: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
        ],
        // roleKey: [
        //   {required: true, message: "权限字符不能为空", trigger: "blur"},
        // ],
        roleSort: [
          { required: true, message: "角色顺序不能为空", trigger: "blur" },
        ],
      },
      //角色权限字符
      roleKeyList: [],
      config: [],
      tableId: "roleTable",
      columns: [
        { type: "checkbox", customWidth: 60 },
        {
          field: "roleId",
          title: "角色编号",
        },
        {
          field: "roleName",
          title: "角色名称",
          showOverflowTooltip: true,
        },
        {
          field: "statusHtml",
          title: "状态",
          customWidth: 150,
          slots: {
            default: ({ row }) => {
              return (
                <el-switch
                  v-model={row.status}
                  active-value="0"
                  inactive-value="1"
                  onChange={() => this.handleStatusChange(row)}
                ></el-switch>
              );
            },
          },
        },
        {
          field: "createTimeValue",
          title: "创建时间",
        },
        {
          field: "remark",
          title: "备注",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_status").then((response) => {
      this.statusOptions = response.data;
      this.initConfig();
    });
    this.getDicts("sys_role_key").then((response) => {
      this.roleKeyList = response.data;
    });
    getUserRole().then((response) => {
      if (response.data && response.data.length > 0) {
        this.userRoleId = response.data[0].roleId;
      }
    });
    this.getMenuTreeselect();
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "roleName",
          title: "角色名称",
          type: "input",
          placeholder: "请输入角色名称",
        },
        {
          key: "roleId",
          title: "角色编号",
          type: "input-number",
          placeholder: "请输入角色编号",
        },
        {
          key: "status",
          title: "角色状态",
          type: "select",
          options: this.statusOptions,
          placeholder: "请选择角色状态",
        },
      ];
    },
    //改变权限时 清除deptId
    changeDataScope() {
      console.log("清除");
      this.form.deptIds = [];
      this.$refs.dept.setCheckedKeys([]);
      console.log(this.form);
    },
    btn(row, col, event) {
      row.flag = !row.flag;
      this.$refs.multipleTable.toggleRowSelection(row, row.flag);
    },
    /** 查询角色列表 */
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      this.finallySearch = args;
      listRole(this.addDateRangeCreate(args, this.dateRange)).then(
        (response) => {
          this.roleList = response.data;
          this.total = response.total;
          this.roleList.forEach((element) => {
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
          this.loading = false;
        }
      );
    },
    //查看时设置下拉树不可选择
    disabledChange(data, disabled) {
      for (let i = 0; i < data.length; i++) {
        const children = data[i].children;
        if (children != undefined) {
          this.disabledChange(children, disabled);
        }
        data[i].disabled = disabled;
      }
    },

    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
        this.menuOptions2 = this._.cloneDeep(response.data);
      });
    },
    /** 查询部门树结构 */
    getDeptTreeselect() {
      deptTreeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      const checkedKeys = this.$refs.menu.getHalfCheckedKeys();
      // 半选中的菜单节点
      const halfCheckedKeys = this.$refs.menu.getCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    // 所有部门节点数据
    getDeptAllCheckedKeys() {
      // 目前被选中的部门节点
      const checkedKeys = this.$refs.dept.getHalfCheckedKeys();
      // 半选中的部门节点
      const halfCheckedKeys = this.$refs.dept.getCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      return checkedKeys;
    },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      roleMenuTreeselect(roleId).then((response) => {
        //this.getMenuTreeselect();
        this.$refs.menu.setCheckedKeys(response.data);
      });
    },

    /** 根据角色ID查询部门树结构 */
    getRoleDeptTreeselect(roleId) {
      roleDeptTreeselect(roleId).then((response) => {
        this.getDeptTreeselect();
        this.$refs.dept.setCheckedKeys(response.data);
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      const text = row.status === "0" ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.roleName + '"角色吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        ["catch"](function() {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form = {
        roleId: undefined,
        roleName: undefined,
        roleSort: 0,
        status: "0",
        menuIds: [],
        deptIds: [],
        remark: undefined,
      };
      this.resetForm("form");
    },
    // 表单重置
    reset2() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      this.form2 = {
        roleId: undefined,
        roleName: undefined,
        roleSort: 0,
        status: "0",
        menuIds: [],
        deptIds: [],
        remark: undefined,
      };
      this.resetForm("form2");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      params.pageSize = this.queryParams.pageSize;
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery(params) {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      //this.getMenuTreeselect();
      this.open = true;
      this.title = "添加角色";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      getRole(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改角色";
        this.$nextTick(() => {
          this.getRoleMenuTreeselect(roleId);
        });
      });
    },

    /** 查看按钮操作 */
    handleSelect(row) {
      this.reset2();
      const roleId = row.roleId || this.ids;
      getRole(roleId).then((response) => {
        this.form2 = response.data;
        this.open2 = true;
        this.title = "查看角色";
        if (row.roleId == this.userRoleId) {
          this.disabledChange(this.menuOptions2, true);
        }
        this.$nextTick(() => {
          this.getRoleMenuTreeselect(roleId);
        });
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then((response) => {
              if (response.code === "10000") {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            this.$set(this.form, "delFlag", "0");
            addRole(this.form).then((response) => {
              if (response.code === "10000") {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let roleIds = [row.roleId] || this.ids;
      this.$confirm(
        '是否确认删除角色编号为"' + roleIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return delRole(roleIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        ["catch"](function() {});
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
    /** 导出按钮操作 */
    /*    handleExport() {
          const queryParams = this.queryParams;
          this.$confirm("是否确认导出所有角色数据项?", "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(function () {
              return exportRole(queryParams);
            })
            .then((response) => {
              this.download(response.msg);
            })
            ["catch"](function () {});
        },*/
  },
};
</script>
