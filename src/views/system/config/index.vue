<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="configList"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            v-hasPermi="['system:config:add']"
            >新增</el-button
          >
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            size="large"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row)"
            v-hasPermi="['system:config:edit']"
            >修改</el-button
          >
          <el-button
            size="large"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(row)"
            v-hasPermi="['system:config:remove']"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="参数名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数键名" prop="configKey">
          <el-input v-model="form.configKey" placeholder="请输入参数键名" />
        </el-form-item>
        <el-form-item label="参数键值" prop="configValue">
          <el-input
            v-model="form.configValue"
            placeholder="请输入参数键值"
            type="textarea"
            :rows="5"
          />
        </el-form-item>
        <el-form-item label="系统内置" prop="configType">
          <el-radio-group v-model="form.configType">
            <el-radio
              v-for="dict in typeOptions"
              :key="dict.dictValue"
              :label="dict.dictValue"
              >{{ dict.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="5"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listConfig,
  getConfig,
  delConfig,
  addConfig,
  updateConfig,
} from "@/api/system/config";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  name: "Config",
  components: { GridTable, AdvancedForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      typeOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configName: undefined,
        configKey: undefined,
        configType: undefined,
        dateRange: [],
      },
      finallySearch: null,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        configName: [
          { required: true, message: "参数名称不能为空", trigger: "blur" },
        ],
        configKey: [
          { required: true, message: "参数键名不能为空", trigger: "blur" },
        ],
        configValue: [
          { required: true, message: "参数键值不能为空", trigger: "blur" },
        ],
      },
      tableId: "configTable",
      config: [],
      columns: [
        {
          field: "configId",
          title: "参数主键",
        },
        {
          field: "configName",
          title: "参数名称",
          showOverflowTooltip: true,
        },
        {
          field: "configKey",
          title: "参数键名",
          showOverflowTooltip: true,
        },
        {
          field: "configValue",
          title: "参数键值",
        },
        {
          field: "configTypeValue",
          title: "系统内置",
        },
        {
          field: "remark",
          title: "备注",
        },
        {
          field: "createTimeValue",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 160,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  created() {
    this.getDicts("sys_yes_no").then((response) => {
      this.typeOptions = response.data;
      this.getList();
      this.initConfig();
    });
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "configName",
          title: "参数名称",
          type: "input",
          placeholder: "请输入参数名称",
        },
        {
          key: "configKey",
          title: "参数键名",
          type: "input",
          placeholder: "请输入参数键名",
        },
        {
          key: "configType",
          title: "系统内置",
          type: "select",
          options: this.typeOptions,
          placeholder: "请选择系统内置",
        },
        {
          key: "dateRange",
          title: "创建时间",
          type: "dateRange",
        },
      ];
    },
    btn(row, col, event) {
      row.flag = !row.flag;
      this.$refs.multipleTable.toggleRowSelection(row, row.flag);
    },
    /** 查询参数列表 */
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      const { dateRange, ...res } = args;
      let query = this.addDateRangeCreate(res, dateRange);
      this.finallySearch = query;
      listConfig(this.addDateRangeCreate(res, dateRange)).then((response) => {
        this.configList = response.data;
        this.total = response.total;
        this.configList.forEach((element) => {
          //系统内置
          this.$set(element, "configTypeValue", this.typeFormat(element));
          //创建时间
          this.$set(
            element,
            "createTimeValue",
            this.parseTime(element.createTime)
          );
        });
        this.loading = false;
      });
    },
    // 参数系统内置字典翻译
    typeFormat(row) {
      return this.selectDictLabel(this.typeOptions, row.configType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        configId: undefined,
        configName: undefined,
        configKey: undefined,
        configValue: undefined,
        configType: "Y",
        remark: undefined,
        dateRange: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      params.pageSize = this.queryParams.pageSize;
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加参数";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.configId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const configId = row.configId || this.ids;
      getConfig(configId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改参数";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.configId != undefined) {
            updateConfig(this.form).then(() => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addConfig(this.form).then(() => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const configIds = row.configId || this.ids;
      this.$confirm(
        '是否确认删除参数编号为"' + configIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return delConfig(configIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
  },
};
</script>
