<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :column-auto-fit="false"
        :tableData="menuList"
        :loading="loading"
        :tableId="tableId"
        :treeConfig="{
          children: 'children',
          reserve: true,
        }"
        rowId="menuId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click.stop="handleAdd"
            v-hasPermi="['system:menu:add']"
            >新增</el-button
          >
        </template>
        <!-- 图标 -->
        <template slot="icon" slot-scope="{ row }">
          <svg-icon :icon-class="row.icon" />
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            size="large"
            type="text"
            icon="el-icon-edit"
            @click.stop="handleUpdate(row)"
            v-hasPermi="['system:menu:edit']"
            >修改</el-button
          >
          <el-button
            size="large"
            type="text"
            icon="el-icon-plus"
            @click.stop="handleAdd(row)"
            v-hasPermi="['system:menu:add']"
            >新增</el-button
          >
          <el-button
            v-show="row.parentId != 0"
            size="large"
            type="text"
            icon="el-icon-delete"
            @click.stop="handleDelete(row)"
            v-hasPermi="['system:menu:remove']"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>
    <!-- 添加或修改菜单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24" v-if="form.parentId != 0">
            <el-form-item label="上级菜单">
              <treeselect
                v-model="form.parentId"
                :options="menuOptions"
                :show-count="true"
                placeholder="选择上级菜单"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单类型" prop="menuType">
              <el-radio-group v-model="form.menuType">
                <el-radio label="M">目录</el-radio>
                <el-radio label="C">菜单</el-radio>
                <el-radio label="F">按钮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="菜单归属平台" prop="menuPlatform">
              <el-radio-group v-model="form.menuPlatform">
                <el-radio label="PC">PC</el-radio>
                <el-radio label="APP">APP</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.menuType != 'F'" label="菜单图标">
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected" />
                <el-input
                  slot="reference"
                  v-model="form.icon"
                  placeholder="点击选择图标"
                >
                  <svg-icon
                    v-if="form.icon"
                    slot="prefix"
                    :icon-class="form.icon"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                  <i
                    v-else
                    slot="prefix"
                    class="el-icon-search el-input__icon"
                  />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="菜单名称" prop="menuName">
              <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number
                v-model="form.orderNum"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.menuType != 'F'" label="是否外链">
              <el-radio-group v-model="form.isFrame">
                <el-radio label="0">是</el-radio>
                <el-radio label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.menuType != 'F'"
              label="路由地址"
              prop="path"
            >
              <el-input v-model="form.path" placeholder="请输入路由地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.menuType == 'C'">
            <el-form-item label="组件路径" prop="component">
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.menuType != 'M'" label="权限标识">
              <el-input
                v-model="form.perms"
                placeholder="请权限标识"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.menuType != 'F'" label="菜单状态">
              <el-radio-group v-model="form.visible">
                <el-radio
                  v-for="dict in visibleOptions"
                  :key="dict.dictValue"
                  :label="dict.dictValue"
                  >{{ dict.dictLabel }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitForm">确 定</el-button>
        <el-button @click.stop="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMenu,
  getMenu,
  treeselect,
  delMenu,
  addMenu,
  updateMenu,
} from "@/api/system/menu";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
export default {
  components: { Treeselect, IconSelect, GridTable, AdvancedForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 菜单表格树数据
      menuList: [],
      // 菜单树选项
      menuOptions: undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 菜单状态数据字典
      visibleOptions: [],
      // 查询参数
      queryParams: {
        menuName: undefined,
        visible: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        menuName: [
          { required: true, message: "菜单名称不能为空", trigger: "blur" },
        ],
        orderNum: [
          { required: true, message: "菜单顺序不能为空", trigger: "blur" },
        ],
      },
      tableId: "menuTable",
      config: [],
      columns: [
        {
          field: "menuName",
          title: "菜单名称",
          showOverflowTooltip: true,
          treeNode: true,
          width: 160,
        },
        {
          field: "icon",
          title: "图标",
          slots: {
            default: ({ row }) => {
              return <svg-icon icon-class={row.icon} />;
            },
          },
        },
        {
          field: "orderNum",
          title: "排序",
          width: 60,
        },
        {
          field: "perms",
          title: "权限标识",
          width: 130,
        },
        {
          field: "component",
          title: "组件路径",
        },
        {
          field: "visible",
          title: "可见",
          width: 80,
          formatter: this.visibleFormat,
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          title: "操作",
          minWidth: 220,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
    };
  },
  created() {
    this.getDicts("sys_show_hide").then((response) => {
      this.visibleOptions = response.data;
      this.initConfig();
      this.getList();
    });
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "menuName",
          title: "菜单名称",
          type: "input",
          placeholder: "请输入菜单名称",
        },
        {
          key: "visible",
          title: "菜单状态",
          type: "select",
          options: this.visibleOptions,
          placeholder: "请选择菜单状态",
        },
      ];
    },
    // 选择图标
    selected(name) {
      this.form.icon = name;
    },
    /** 查询菜单列表 */
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      listMenu(args).then((response) => {
        this.menuList = response.data;
        this.loading = false;
      });
    },
    /** 查询菜单下拉树结构 */
    getTreeselect() {
      treeselect().then((response) => {
        this.menuOptions = response.data;
      });
    },
    // 菜单显示状态字典翻译
    visibleFormat(data) {
      const { row } = data;
      if (row.menuType == "F") {
        return "";
      }
      return this.selectDictLabel(this.visibleOptions, row.visible);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        menuId: undefined,
        parentId: undefined,
        menuName: undefined,
        icon: undefined,
        menuType: "M",
        menuPlatform: "PC", //菜单归属平台：APP|PC　默认为：PC
        orderNum: undefined,
        isFrame: "1",
        visible: "0",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.getList(params);
    },
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.getTreeselect();
      if (row != null) {
        this.form.parentId = row.menuId;
      }
      this.open = true;
      this.title = "添加菜单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      this.getTreeselect();
      getMenu(row.menuId).then((response) => {
        this.form = response.data;
        // this.$set(this.form, "parentId", undefined)
        this.open = true;
        this.title = "修改菜单";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.menuId != undefined) {
            updateMenu(this.form).then((response) => {
              if (response.code == 10000) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.message);
              }
            });
          } else {
            addMenu(this.form).then((response) => {
              if (response.code == 10000) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(
        '是否确认删除名称为"' + row.menuName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function() {
          return delMenu({ menuId: row.menuId });
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        ["catch"](function() {});
    },
  },
};
</script>
