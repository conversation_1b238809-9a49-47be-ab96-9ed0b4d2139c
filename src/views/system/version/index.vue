<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click.stop="handleAdd"
                           v-hasPermi="['system:version:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="success"
                        icon="el-icon-edit"
                        size="mini"
                        :disabled="single"
                        @click.stop="handleUpdate"
                        v-hasPermi="['system:version:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="danger"
                        icon="el-icon-delete"
                        size="mini"
                        :disabled="multiple"
                        @click.stop="handleDelete"
                        v-hasPermi="['system:version:remove']"
                >删除
                </el-button>
            </el-col>
        </el-row>

        <el-table v-loading="loading" @row-click="btn" ref = "multipleTable" :data="configList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="参数主键" width="150" align="center" prop="versionId"/>
            <el-table-column label="版本号" align="center" prop="versionNo" :show-overflow-tooltip="true"/>
            <el-table-column label="版本名称" align="center" prop="versionName" :show-overflow-tooltip="true"/>
            <el-table-column label="版本说明" align="center" prop="remark" :show-overflow-tooltip="true"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click.stop="handleUpdate(scope.row)"
                            v-hasPermi="['system:version:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-delete"
                            @click.stop="handleDelete(scope.row)"
                            v-hasPermi="['system:version:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
        />

        <!-- 添加或修改参数配置对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px" v-loading="uploadloding">
                <el-form-item label="版本号" prop="versionNo">
                    <el-input v-model="form.versionNo" placeholder="请输入版本号"/>
                </el-form-item>
                <el-form-item label="版本名称" prop="versionName">
                    <el-input v-model="form.versionName" placeholder="请输入版本名称"/>
                </el-form-item>
                <el-form-item label="文件" prop="file" >
                    <el-upload
                            ref="upload"
                            class="upload-demo"
                            :auto-upload="true"
                            :action="upload.url"
                            :headers="upload.headers"
                            :disabled="upload.isUploading"
                            :data="{docvo:JSON.stringify(form)}"
                            :on-remove="fileRemove"
                            :on-change="fileChange"
                            :on-progress="handleFileUploadProgress"
                            :on-success="handleFileSuccess"
                            :file-list="fileList"
                            :limit="fileLimit"
                            :on-exceed="handleExceed"
                    >
                        <el-button size="small" type="primary">点击上传</el-button>
                        <el-input v-model="form.file" maxlength="50" style="display:none" />
                    </el-upload>
                </el-form-item>
                <el-form-item label="版本说明" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="5" placeholder="请输入版本说明"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click.stop="submitForm">确 定</el-button>
                <el-button @click.stop="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {
        listConfig,
        delConfig,
        addConfig,
        updateConfig,
        getConfig
    } from '@/api/system/version';
    import { getToken } from "@/utils/auth";

    let baseUrl =
        process.env.VUE_APP_BASE_API == "/" ? "" : process.env.VUE_APP_BASE_API;

    export default {
        name: 'Version',
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                selectMeterData:{},
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 图片是否上传
                isPictureUploaded: false,
                // 总条数
                total: 0,
                // 参数表格数据
                configList: [],
                uploadloding:false,
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 类型数据字典
                typeOptions: [],
                // 日期范围
                dateRange: [],
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    configName: undefined,
                    configKey: undefined,
                    configType: undefined
                },
                fileLimit: 1,
                fileList: [],
                upload: {
                    // 是否显示弹出层
                    importOpen: false,
                    // 弹出层标题
                    title: "",
                    // 是否禁用上传
                    isUploading: false,
                    // 是否更新已经存在的数据
                    updateSupport: 0,
                    // 设置上传的请求头部
                    headers: { Authorization: "Bearer " + getToken() },
                    // 上传的地址
                    url: baseUrl + "/system/doc/uploadRe"
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {
                    versionNo: [{required: true, message: '版本号不能为空', trigger: 'blur'}],
                    versionName: [{required: true, message: '版本名称不能为空', trigger: 'blur'}],
                },
                sysConfig:{}
            };
        },
        created() {
            this.getList();
            this.getDicts('sys_yes_no').then(response => {
                this.typeOptions = response.data;
            });
        },
        computed:{
            isPictureRequired(){
                return this.sysConfig.pictureParamConfig ? this.isRequired(this.sysConfig.pictureParamConfig) && !this.isPictureUploaded : !this.isPictureUploaded
            }
        },
        watch: {
            selectMeterData() {
                this.form.excpTypeCode = this.selectMeterData.excpTypeCode,
                    this.form.thisRead = this.isEditor ? this.selectMeterData.thisRead : '';
            }
        },
        methods: {
            btn(row, col, event) {
                row.flag = !row.flag;
                this.$refs.multipleTable.toggleRowSelection(row, row.flag);
            },
            /** 查询参数列表 */
            getList() {
                this.loading = true;
                listConfig(this.addDateRangeCreate(this.queryParams, this.dateRange)).then(response => {
                    this.configList = response.rows;
                    this.total = response.total;
                    this.loading = false;
                });
            },
            fileRemove(file, fileList){
                this.isPictureUploaded = true
            },
            //上传的相关方法
            fileChange(file, fileList) {
                this.fileList = fileList;
                this.isPictureUploaded = fileList && fileList.length > 0;
                if(file.name.length > 14){
                    let result = file.name.substring(0,10);
                    let splitdata = file.name.split('.');
                    result += '.' + splitdata[splitdata.length -1];
                    file.name = result;
                }
            },
            // 文件上传中处理
            handleFileUploadProgress(event, file, fileList) {
                this.uploadloding = true;
                this.upload.isUploading = true;
                this.selectMeterData.file = file;
                this.fileList = fileList;
            },
            handleFileSuccess(response, file, fileList) {
                this.upload.open = false;
                this.upload.isUploading = false;
                this.uploadloding = false;
                // this.$refs.upload.clearFiles();
                if (response.code === 200) {
                    this.upload.importOpen = false;
                    this.form.downloadUrl=response.data;
                    // let files = {
                    //     name : file.name,
                    //     url : response.data
                    // }
                    // this.fileList.push(files);
                } else {
                    this.isPictureUploaded = false;
                    console.log("上传失败");
                }
            },
            handleExceed() {
                this.$message.error("只能选择一个文件");
            },
            // 参数系统内置字典翻译
            typeFormat(row) {
                return this.selectDictLabel(this.typeOptions, row.configType);
            },
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    versionId: undefined,
                    configName: undefined,
                    configKey: undefined,
                    configValue: undefined,
                    configType: 'Y',
                    remark: undefined
                };
                this.resetForm('form');
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.dateRange = [];
                this.resetForm('queryForm');
                this.handleQuery();
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset();
                this.open = true;
                this.title = '添加参数';
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.versionId );
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset();
                const versionId = row.versionId || this.ids;
                getConfig(versionId).then(response => {
                    this.form = response.data;
                    this.open = true;
                    this.title = '修改参数';
                });
            },
            /** 提交按钮 */
            submitForm: function () {
                this.$refs['form'].validate(valid => {
                    if (valid) {
                        if (this.form.versionId != undefined) {
                            updateConfig(this.form).then(() => {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            });
                        } else {
                            addConfig(this.form).then(() => {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            });
                        }
                    }
                });
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const versionIds = row.versionId || this.ids;
                this.$confirm('是否确认删除参数编号为"' + versionIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(function () {
                        return delConfig(versionIds);
                    })
                    .then(() => {
                        this.getList();
                        this.msgSuccess('删除成功');
                    });
            }

        }
    };
</script>
