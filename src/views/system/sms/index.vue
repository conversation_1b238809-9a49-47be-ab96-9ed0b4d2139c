<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="smsList"
        :checkbox="true"
        :batchDelete="true"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="smsStatus" slot-scope="{ row, $index }">
          <span v-if="row.smsStatus === '2'" style="color: green">发送成功</span>
          <span v-if="row.smsStatus === '1'" style="color: orange">提交成功</span>
          <span v-if="row.smsStatus === '3'" style="color: red">发送失败</span>
        </template>
      </GridTable>
    </el-card>
    <!-- 添加或修改角色配置对话框 -->

    <!-- 查看改角色配置对话框 -->
  </div>
</template>

<script>
import {
  list
} from "@/api/system/sms/index";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";

export default {
  components: { GridTable, AdvancedForm },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 角色表格数据
      smsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      open2: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      //当前登录账号的角色
      userRoleId: null,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      smsTypeOptions: [],
      // 数据范围选项
      dataScopeOptions: [
        // {
        //   value: "1",
        //   label: "全部数据权限",
        // },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本组织及以下数据权限",
        },
      ],
      // 菜单列表
      menuOptions: [],
      menuOptions2: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      finallySearch: null,
      // 表单参数
      form: {},
      // 查看时用于回显
      form2: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      config: [],
      tableId: "id",
      columns: [
        {
          field: "phone",
          title: "手机号"
        },
        {
          field: "smsTypeName",
          title: "通知类型",
          showOverflowTooltip: true
        },
        {
          field: "createTimeValue",
          title: "发送时间"
        },
        {
          field: "smsStatus",
          title: "状态",
          customWidth: 150,
          slots: { default: "smsStatus" }
        },
        {
          field: "remark",
          title: "状态描述"
        }
      ]
    };
  },
  created() {
    this.getDicts("cm_sms_status").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("cm_sms_type").then((response) => {
      this.smsTypeOptions = response.data;
    });
  },
  mounted() {
    Promise.all([
      this.getList()
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "phone",
          title: "手机号",
          type: "input",
          placeholder: "请输入手机号"
        },
        {
          key: "smsType",
          title: "通知类型",
          type: "select",
          options: this.smsTypeOptions,
          placeholder: "请选择通知类型"
        },
        {
          key: "smsStatus",
          title: "状态",
          type: "select",
          options: this.statusOptions,
          placeholder: "请选择状态"
        },
        {
          key: "createTime",
          title: "发送时间",
          type: "dateRange",
          placeholder: "请选择发送时间"
        }
      ];
    },
    /** 查询列表 */
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      this.finallySearch = args;
      if (Array.isArray(args.createTime)) {
        args.startTime = args.createTime[0] + " 00:00:00";
        args.endTime = args.createTime[1] + " 23:59:59";
        delete args.createTime;
      }
      list(args).then(
        (response) => {
          this.smsList = response.data;
          this.total = response.total;
          this.smsList.forEach((element) => {
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      params.pageSize = this.queryParams.pageSize;
      this.getList(params);
    },
    /** 重置按钮操作 */
    resetQuery(params) {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    }
  }
};
</script>
