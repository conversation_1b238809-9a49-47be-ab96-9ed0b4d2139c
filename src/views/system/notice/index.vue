<template>
  <div class="app-container">
    <div class="queryParamsWrap">
      <el-form
        :model="queryParams"
        ref="queryForm"
        label-width="100px"
        :inline="true"
      >
        <el-row :gutter="12">
          <el-col :span="6">
            <el-form-item label="公告标题" prop="noticeTitle">
              <el-input
                v-model="queryParams.noticeTitle"
                placeholder="请输入关键词"
                clearable
                size="mini"
                @keyup.enter.native="handleQuery"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分类">
              <el-select v-model="queryParams.noticeType" clearable placeholder="请选择分类" size="mini">
                <el-option v-for="dict in typeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                           :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="queryParams.noticeStatus" clearable placeholder="请选择状态" size="mini">
                <el-option v-for="dict in stateOptions" :key="dict.dictValue" :label="dict.dictLabel"
                           :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 40px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click.stop="initGetList">查询</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click.stop="resetQuery">重置</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" style="padding-left: 35px;padding-bottom: 20px">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click.stop="addNotice">新增公告</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table v-loading="loading" ref="multipleTable" :data="tableData" style="width: 100%">
      <el-table-column prop="noticeTitle" min-width="80" align="center" label="公告标题">
        <template slot-scope="scope">
          {{ scope.row.noticeTitle }}
        </template>
      </el-table-column>
      <el-table-column prop="noticeType" min-width="80" align="center" label="分类">
        <template slot-scope="scope">
          {{ typeOptions | typeFilter(scope.row.noticeType) }}
        </template>
      </el-table-column>
      <el-table-column prop="noticeStatus" min-width="80" align="center" label="状态">
        <template slot-scope="scope">
          {{ stateOptions | statusFilter(scope.row.noticeStatus) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" min-width="80" align="center" label="创建时间">
        <template slot-scope="scope">
          {{ scope.row.createTime }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="80">
        <template slot-scope="scope">
          <el-button @click="editNotice(scope.row)" type="text" size="small">修改</el-button>
          <el-divider direction="vertical"></el-divider>
          <el-button @click="handleDelete(scope.row)" type="text" size="small">删除</el-button>
          <el-divider direction="vertical"></el-divider>
          <el-button v-if="scope.row.noticeStatus=='1'" @click="changeStatus(scope.row,'2')" type="text" size="small">
            下线
          </el-button>
          <el-button v-if="scope.row.noticeStatus=='2'" @click="changeStatus(scope.row,'1')" type="text" size="small">
            上线
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :autoScroll="false"
      v-show="total > 0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog title="公告管理" :visible.sync="noticeVisible" :close-on-click-modal="false" @close="addCancle"
               width="780px"
               append-to-body>
      <el-form ref="noticeForm" :model="noticeForm" :rules="noticeRules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="公告标题" prop="noticeTitle">
              <el-input v-model="noticeForm.noticeTitle" placeholder="请输入公告标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属分类" prop="noticeType">
              <el-select v-model="noticeForm.noticeType" clearable placeholder="请选择分类" size="mini">
                <el-option v-for="dict in typeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                           :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容" prop="noticeContent">
              <editor v-model="noticeForm.noticeContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="padding-top: 20px">
        <el-button v-if="addVisible" type="primary" @click.stop="addConfirm">确 定</el-button>
        <el-button v-if="editVisible" type="primary" @click.stop="editConfirm">更 新</el-button>
        <el-button @click.stop="addCancle">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import mixin from "@/mixin/commonPage";
import Editor from "@/components/Editor";
import { queryNotice, saveNotice, updateNotice, deleteNotice } from "@/api/system/notice";


export default {
  mixins: [mixin],
  components: {
    Editor
  },
  filters: {
    statusFilter(stateOptions, status) {
      for (var i = 0; i < stateOptions.length; i++) {
        if (stateOptions[i].dictValue === status) {
          return stateOptions[i].dictLabel;
        }
      }
    },
    typeFilter(typeOptions, type) {
      for (var i = 0; i < typeOptions.length; i++) {
        if (typeOptions[i].dictValue === type) {
          return typeOptions[i].dictLabel;
        }
      }
    },
    formatDate(value) {
      if (typeof (value) == "undefined") {
        return "";
      } else {
        let date = new Date(parseInt(value));
        let y = date.getFullYear();
        let MM = date.getMonth() + 1;
        MM = MM < 10 ? ("0" + MM) : MM;
        let d = date.getDate();
        d = d < 10 ? ("0" + d) : d;
        let h = date.getHours();
        h = h < 10 ? ("0" + h) : h;
        let m = date.getMinutes();
        m = m < 10 ? ("0" + m) : m;
        let s = date.getSeconds();
        s = s < 10 ? ("0" + s) : s;
        return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
      }
    }
  },
  data() {
    const nameValidator = (rule, value, callback) => {
      if(value.length > 14) {
        callback(new Error('标题长度不能超过40位'));
      } else {
        callback();
      }
    };
    return {
      addVisible: false,// 弹窗保存按钮
      editVisible: false,// 弹窗更新按钮
      noticeVisible: false,//新增公告弹窗
      noticeForm: {},//公告列表
      queryParams: {},
      pageInitQuery: false,
      total: 0,
      pageNum: 1,
      pageSize: 10,
      queryApi: "",
      EBS_MrPlanType: [],
      userSearchVisible: false,
      typeOptions: [],// 公告类型
      stateOptions: [],
      noticeRules: {
        noticeTitle: [
          { required: true, message: "请输入公告标题", trigger: "blur" },
          {validator: nameValidator, trigger: 'blur'}
        ],
        noticeType: [
          { required: true, message: "请选择分类", trigger: "change" }
        ],
        noticeContent: [
          { required: true, message: "请输入公告内容", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getDicts("sys_notice_type").then((response) => {
      this.typeOptions = response.data;
    });
    this.getDicts("sys_notice_status").then((response) => {
      this.stateOptions = response.data;
    });
    this.getList();
  },
  methods: {

    //查询网厅公告列表
    initGetList() {
      this.pageNum = 1;
      this.getList();
    },

    //查询网厅公告列表
    getList() {
      this.queryParams.pageSize = this.pageSize,
        this.queryParams.pageNum = this.pageNum,
        queryNotice(this.queryParams).then(res => {
          console.info("数据信息===》》》", res.data);
          this.tableData = Object.assign([], res.data);
          this.total = res.total;
        });
    },

    //新增服务
    addNotice() {
      this.noticeVisible = true;
      this.addVisible = true;
    },

    //关闭服务弹窗
    addCancle() {
      this.addVisible = false;
      this.editVisible = false;
      this.noticeVisible = false;
      this.noticeForm = {};
      this.$refs.noticeForm.resetFields();
    },
    // 保存公告确认
    addConfirm() {
      this.$refs["noticeForm"].validate(valid => {
        if (valid) {
          saveNotice(this.noticeForm).then(res => {
            if (res.code == 10000) {
              this.msgSuccess("添加成功");
              this.noticeVisible = false;
              this.addVisible = false;
              this.getList();
            } else {
              this.msgError("添加失败");
            }
          });
        }
      });
    },
    // 打开编辑公告弹窗
    editNotice(row) {
      this.noticeVisible = true;
      this.editVisible = true;
      let rowData = this._.cloneDeep(row);
      this.noticeForm = rowData;
    },
    //编辑公告
    editConfirm() {
      this.$refs["noticeForm"].validate(valid => {
        if (valid) {
          this.noticeForm.createTime = "";
          updateNotice(this.noticeForm).then(res => {
            if (res.code == 10000) {
              this.msgSuccess("修改成功");
              this.noticeVisible = false;
              this.editVisible = false;
              this.getList();
            } else {
              this.msgError("修改失败");
            }
          });
        }
      });
    },
    // 删除公告
    handleDelete(row) {
      this.$confirm("确定删除该数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let list = { noticeId: row.noticeId };
        deleteNotice(list).then(res => {
          if (res.code == 10000) {
            this.msgSuccess("删除成功");
            this.getList();
          } else {
            this.msgError("删除失败");
          }
        })
          .catch(() => {

          });
      });
    },
    // 上下线功能
    changeStatus(row, status) {
      let msg = "";
      let notice = "";
      if (status == "1") {
        msg = "确定上线吗?";
        notice = "上线成功";
      } else if (status == "2") {
        msg = "确定下线吗?";
        notice = "下线成功";
      }
      this.$confirm(msg, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.noticeForm = row;
        this.noticeForm.noticeStatus = status;
        updateNotice(this.noticeForm).then(res => {
          if (res.code == 10000) {
            this.msgSuccess(notice);
            this.getList();
          } else {
            this.msgError("失败");
          }
        })
          .catch(() => {

          });
      });
    }


  }
};
</script>
