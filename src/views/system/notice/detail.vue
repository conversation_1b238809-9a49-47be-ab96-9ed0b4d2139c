<!-- 公告详情 -->
<template>
  <div class="app-container">
    <el-row type="flex" justify="center">
      <div style="font-size: 24px;">{{ infoObj.noticeTitle }}</div>
    </el-row>
    <el-row type="flex" justify="end">
      <div style="font-size: 12px;margin: 20px;">发布时间：{{ infoObj.createTime }}</div>
    </el-row>
    <div v-html="infoObj.noticeContent"></div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      infoObj: {}
    };
  },
  computed: {},
  watch: {
    // 获取路由参数
    $route: {
      handler(val) {
        this.infoObj = JSON.parse(val.query.infoObj);
      },
      immediate: true,
    },
  },
  methods: {},
  created() {},
  mounted() {},
};
</script>
<style lang="less" scoped></style>
