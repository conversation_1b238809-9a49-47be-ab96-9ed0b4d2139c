<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click.stop="handleAdd"
                           v-hasPermi="['system:match:add']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="success"
                        icon="el-icon-edit"
                        size="mini"
                        :disabled="single"
                        @click.stop="handleUpdate"
                        v-hasPermi="['system:match:edit']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="danger"
                        icon="el-icon-delete"
                        size="mini"
                        :disabled="multiple"
                        @click.stop="handleDelete"
                        v-hasPermi="['system:match:remove']"
                >删除
                </el-button>
            </el-col>
        </el-row>

        <el-table v-loading="loading" @row-click="btn" ref="multipleTable" :data="configList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"/>
            <el-table-column label="参数主键" width="150" align="center" prop="matchId"/>
            <el-table-column label="银行编码" align="center" prop="matchCode" :show-overflow-tooltip="true"/>
            <el-table-column label="机构号" align="center" prop="matchType" :show-overflow-tooltip="true"/>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click.stop="handleUpdate(scope.row)"
                            v-hasPermi="['system:match:edit']"
                    >修改
                    </el-button
                    >
                    <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-delete"
                            @click.stop="handleDelete(scope.row)"
                            v-hasPermi="['system:match:remove']"
                    >删除
                    </el-button
                    >
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
        />

        <!-- 添加或修改参数配置对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="机构号" prop="matchCode">
                    <el-input v-model="form.matchCode" placeholder="请输入银行对应的机构编码"/>
                </el-form-item>
                <el-form-item label="银行编码" prop="matchType">
                    <el-input v-model="form.matchType" placeholder="请输入银行类型"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click.stop="submitForm">确 定</el-button>
                <el-button @click.stop="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import {
        listConfig,
        delConfig,
        addConfig,
        updateConfig,
        getConfig
    } from '@/api/system/match';

    export default {
        name: 'match',
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                // 参数表格数据
                configList: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                // 类型数据字典
                typeOptions: [],
                // 日期范围
                dateRange: [],
                // 查询参数
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    configName: undefined,
                    configKey: undefined,
                    configType: undefined
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {
                    matchCode: [{required: true, message: '参数名称不能为空', trigger: 'blur'}],
                    matchType: [{required: true, message: '类型不能为空', trigger: 'blur'}]
                }
            };
        },
        created() {
            this.getList();
            this.getDicts('sys_yes_no').then(response => {
                this.typeOptions = response.data;
            });
        },
        methods: {
            btn(row, col, event) {
                row.flag = !row.flag;
                this.$refs.multipleTable.toggleRowSelection(row, row.flag);
            },
            /** 查询参数列表 */
            getList() {
                this.loading = true;
                listConfig(this.addDateRangeCreate(this.queryParams, this.dateRange)).then(response => {
                    this.configList = response.rows;
                    this.total = response.total;
                    this.loading = false;

                });
            },
            // 参数系统内置字典翻译
            typeFormat(row) {
                return this.selectDictLabel(this.typeOptions, row.configType);
            },
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                    matchId: undefined,
                    configName: undefined,
                    configKey: undefined,
                    configValue: undefined,
                    configType: 'Y',
                    remark: undefined
                };
                this.resetForm('form');
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.dateRange = [];
                this.resetForm('queryForm');
                this.handleQuery();
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset();
                this.open = true;
                this.title = '添加参数';
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.matchId);
                this.single = selection.length != 1;
                this.multiple = !selection.length;
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset();
                const matchId = row.matchId || this.ids;
                getConfig(matchId).then(response => {
                    this.form = response.data;
                    this.open = true;
                    this.title = '修改参数';
                });
            },
            /** 提交按钮 */
            submitForm: function () {
                this.$refs['form'].validate(valid => {
                    if (valid) {
                        if (this.form.matchId != undefined) {
                            updateConfig(this.form).then(() => {
                                this.msgSuccess('修改成功');
                                this.open = false;
                                this.getList();
                            });
                        } else {
                            debugger;
                            addConfig(this.form).then(() => {
                                this.msgSuccess('新增成功');
                                this.open = false;
                                this.getList();
                            });
                        }
                    }
                });
            },
            /** 删除按钮操作 */
            handleDelete(row) {
                const matchIds = row.matchId || this.ids;
                this.$confirm('是否确认删除参数编号为"' + matchIds + '"的数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                })
                    .then(function () {
                        return delConfig(matchIds);
                    })
                    .then(() => {
                        this.getList();
                        this.msgSuccess('删除成功');
                    });
            }

        }
    };
</script>
