<!-- 新增施工队 -->
<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="60%"
  >
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="基础信息" name="1"></el-tab-pane>
      <el-tab-pane label="资质证书" name="2"></el-tab-pane>
      <el-tab-pane label="服务范围" name="3"></el-tab-pane>
    </el-tabs>

    <div v-show="activeName === '1'">
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="baseParams"
        :defaultColSpan="12"
        labelPosition="top"
        :preview="type === 'detail'"
        labelWidth="130px"
      ></DynamicForm>
    </div>

    <div v-show="activeName === '2'">
      <div>功能暂未开放,敬请期待</div>
      <!--      <el-form :model="form2" :rules="rules2" ref="form2" :inline="true" label-width="110px">-->
      <!--        <el-row :gutter="20">-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="资质" prop="builder">-->
      <!--              <el-input v-model="form3.builder" size="mini" />-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </el-form>-->
    </div>

    <div v-show="activeName === '3'">
      <div>功能暂未开放,敬请期待</div>
      <!--      <el-form :model="form3" :rules="rules3" ref="form3" :inline="true" label-width="110px">-->
      <!--        <el-row :gutter="20">-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="服务范围" prop="builder">-->
      <!--              <el-input v-model="form3.builder" size="mini" />-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </el-form>-->
    </div>

    <div slot="footer" class="dialog-footer" v-if="type !== 'detail'">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="submitInfo">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api/constructionTeam/index.js";
import { initParams } from "@/utils/buse.js";
export default {
  components: {},
  props: ["visible", "constructId", "type", "title", "applyNo"],
  data() {
    return {
      baseParams: {},
      activeName: "1",

      form2: {
        fileList: [],
      },
      form3: {},

      rules2: {
        stationName: [
          { required: true, message: "请填写站点名称", trigger: "blur" },
        ],
      },
      rules3: {
        builder1: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
      },
      applyNoOptions: [],
    };
  },
  computed: {
    baseConfig() {
      return [
        {
          element: "el-input",
          field: "constructCode",
          title: "施工队编码：",
          props: {
            disabled: this.type === "update",
          },
          rules: [
            {
              required: this.type === "add",
              message: "请填写施工队编码",
              trigger: "blur",
            },
          ],
          attrs: {
            placeholder: "请输入施工队编码",
          },
        },
        {
          element: "el-input",
          field: "constructName",
          title: "施工队名称：",
          rules: [
            { required: true, message: "请填写施工队名称", trigger: "blur" },
          ],
          attrs: {
            placeholder: "请输入施工队名称",
          },
        },
        {
          element: "el-input",
          field: "contactName",
          title: "施工队联系人：",
          rules: [
            { required: true, message: "请填写施工队联系人", trigger: "blur" },
          ],
          attrs: {
            placeholder: "请输入施工队联系人",
          },
        },
        {
          element: "el-input",
          field: "contactPhone",
          title: "联系方式：",
          rules: [
            { required: true, message: "请填写联系方式", trigger: "blur" },
          ],
          attrs: {
            placeholder: "请输入联系方式",
          },
        },
        {
          element: "el-autocomplete",
          field: "applyNo",
          title: "运管采购框架协议申请单号：",
          props: {
            placeholder: "请选择或输入运管采购框架协议申请单号",
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryApplyNoList");
            },
          },
          rules: [{ required: false, message: "请选择", trigger: "blur" }],
        },
        {
          field: "procotolTypeName",
          title: "施工队类别：",
          element: "el-select",
          props: {
            disabled: true,
            options: [{ label: "建筑安装", value: "建筑安装" }],
            placeholder: "请选择施工队类别",
          },
          defaultValue: "建筑安装",
          rules: [{ required: true, message: "请选择", trigger: "blur" }],
        },
        {
          element: "el-input",
          field: "mailContact",
          title: "邮寄联系人：",
          attrs: {
            placeholder: "请输入邮寄联系人",
          },
        },
        {
          element: "el-input",
          field: "mailAddr",
          title: "邮寄地址：",
          attrs: {
            placeholder: "请输入邮寄地址",
          },
        },
        {
          element: "el-date-picker",
          field: "cooperationLife",
          title: "合作有效期至：",
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择合作有效期至",
          },
        },
        {
          element: "el-input",
          field: "mailPhone",
          title: "邮寄电话：",
          attrs: {
            placeholder: "请输入邮寄电话",
          },
        },
        {
          element: "el-date-picker",
          field: "certLife",
          title: "施工资质有效期至：",
          props: {
            valueFormat: "yyyy-MM-dd",
            placeholder: "请选择施工资质有效期至",
          },
        },
      ];
    },
  },
  watch: {},
  async created() {
    this.queryApplyNoList();
    this.baseParams = initParams(this.baseConfig);
    this.type !== "add" && this.queryBuildTeamById();
  },
  mounted() {},
  methods: {
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        applyNo: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    queryApplyNoList() {
      api.queryApplyNoList({ applyNo: this.applyNo || "" }).then((res) => {
        this.applyNoOptions = res.data?.map((x) => {
          return { value: x, label: x };
        });
      });
    },
    queryBuildTeamById() {
      let params = {
        constructId: this.constructId,
      };
      api.queryBuildTeamById(params).then((res) => {
        if (res?.success) {
          const { constructInfo = {} } = res.data;
          this.baseParams = { ...this.baseParams, ...constructInfo };
        } else {
          this.$message.error(res.message);
        }
      });
    },
    closeDialog() {
      this.$emit("update:constructId", null);
      this.$emit("update:applyNo", null);
      this.$emit("update:visible", false);
    },
    handleClick() {},
    submitInfo() {
      this.$refs.baseForm.validate((val) => {
        if (!val) {
          //返回false，阻止弹窗关闭
          return false;
        }
        const { createBy, ...rest } = this.baseParams;
        let params = {
          ...rest,
        };
        api.updateBuildTeam(params).then((res) => {
          if (res?.success) {
            this.closeDialog();
            this.$emit("update-list");
            this.$message.success("保存成功");
          }
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
