<!-- 项目管理 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="providerTable"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="providerTableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleCreate"
            v-has-permi="['provider:manage:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="orderTotal" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="handleEdit(row)">
            {{ row.orderTotal }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <!--          <el-button-->
          <!--            type="text"-->
          <!--            size="large"-->
          <!--            @click="handleEdit(row)"-->
          <!--          >-->
          <!--            编辑-->
          <!--          </el-button>-->
          <el-button
            type="text"
            size="large"
            @click="handleDetail(row)"
            v-has-permi="['provider:manage:detail']"
          >
            详情
          </el-button>
          <el-button
            @click="handleDel(row)"
            type="text"
            size="large"
            v-has-permi="['provider:manage:delete']"
          >
            删除
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <AddProvider
      v-if="addProjectDialogVisible"
      @closeDialog="closeDialog"
      :visible.sync="addProjectDialogVisible"
    />

    <DetailProvider
      v-if="detailOrderVisible"
      :providerId="providerId"
      @closeDialog="closeDialog"
      :visible.sync="detailOrderVisible"
    />
  </div>
</template>

<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import AddProvider from "@/views/provider/addProvider.vue";
import DetailProvider from "@/views/provider/detailProvider.vue";
import { providerList, handleDel } from "@/api/provider/provider";

export default {
  name: "providerManage",
  components: {
    Treeselect,
    AdvancedForm,
    GridTable,
    AddProvider,
    DetailProvider,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        providerNo: "",
        providerName: "",
        realproviderName: "",
        providerTypeNo: null,
        providerModelId: "",
        pumpHouseId: "",
        providerStatus: "",
        pageNum: 1,
        pageSize: 10,
      },
      providerId: "",
      addForm: {},
      options: [],
      finallySearch: null,
      providerTable: [],
      providerTableTotal: 0,
      addProjectDialogVisible: false, //显示增加设备档案弹框
      detailOrderVisible: false, //显示增加设备档案弹框
      isAddProvider: true, //是否为新增设备,设备新增和修改共用一个页面
      config: [],
      columns: [
        {
          field: "providerName",
          title: "服务商名称",
        },
        {
          field: "contactPerson",
          title: "联系人",
        },
        {
          field: "contactMobile",
          title: "联系方式",
        },
        {
          field: "providerCode",
          title: "服务商Code",
        },
        {
          field: "manufacturers",
          title: "是否硬件厂家",
        },
        {
          field: "brandName",
          title: "品牌名称",
        },
        {
          field: "serviceScope",
          title: "服务范围",
        },

        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "projectManageTable", //tableId必须项目唯一，用于缓存展示列
    };
  },
  mounted() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      // this.getProviderStatusDicts()
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
    //获取设备列表
    this.getproviderList();
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "providerName",
          title: "服务商名称",
          type: "input",
          placeholder: "请输入服务商名称",
        },
        {
          key: "providerCode",
          title: "服务商code",
          type: "input",
          placeholder: "请输入服务商Code",
        },
        {
          key: "brandName",
          title: "品牌名称",
          type: "input",
          placeholder: "请输入品牌名称",
        },
      ];
    },
    getProviderStatusDicts() {},
    submitForm() {
      this.$refs.providerDialog.submitForm();
    },
    closeDialog() {
      console.log("closeDialog", this.$refs);
      this.$refs.providerDialog.resetForm();
      this.addProjectDialogVisible = false;
    },
    //table内容格式化
    statusFormat(val) {
      return this.selectDictLabel(this.statusDict, val.providerStatus);
    },

    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.providerTypeNo,
        label: node.providerTypeName,
        children: node.childrenList,
      };
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.getproviderList(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getproviderList();
    },
    //新建
    handleCreate() {
      this.addProjectDialogVisible = true;
      this.isAddProvider = true;
    },
    //获取设备列表
    getproviderList(params) {
      const args = this._.cloneDeep(params ? params : this.searchForm);
      // args.providerTypeNo = this.searchForm.providerTypeNo; //设备类型
      this.loading = true;
      this.finallySearch = args;
      providerList(args)
        .then((res) => {
          this.loading = false;
          this.providerTable = res?.data;
          this.providerTableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //设备信息修改
    handleEdit(row) {
      findBindRelation({ providerId: row.providerId }).then((res) => {
        console.info("绑定关系", res);
        if (res.data) {
          return this.msgError("请先解绑该设备绑定的设备组！！！");
        } else {
          this.addProjectDialogVisible = true;
          this.isAddProvider = false;
          this.providerNo = row.providerNo;
        }
      });
    },

    closeAddOrderVisible() {
      this.addOrderVisible = false;
      this.detailOrderVisible = false;
    },
    handleDetail(row) {
      this.providerId = row.providerId;
      this.detailOrderVisible = true;
    },
    //删除设备
    handleDel(row) {
      this.$confirm("确定删除该服务商吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          providerId: row.providerId,
        };
        handleDel(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");

            //更新列表
            this.getproviderList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.getproviderList(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
</style>
