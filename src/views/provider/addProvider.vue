<!-- 新增项目 -->
<template>
  <div class="">
    <el-dialog
      title="新建服务商"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeVisible"
      append-to-body
      width="60%"
    >
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
        :before-leave="beforeLeave"
      >
        <el-tab-pane label="基础信息" name="1"></el-tab-pane>
        <el-tab-pane label="资质证书" name="2"></el-tab-pane>
        <el-tab-pane label="服务范围" name="3"></el-tab-pane>
      </el-tabs>

      <div v-show="activeName == '1'" class="queryParamsWrap">
        <el-form :model="form1" :rules="rules1" ref="form1" :inline="true">
          <el-row>
            <el-col :span="12">
              <el-form-item label="服务商Code" prop="providerCode">
                <el-input v-model="form1.providerCode" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务商名称" prop="providerName">
                <el-input v-model="form1.providerName" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人" prop="contactPerson">
                <el-input v-model="form1.contactPerson" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="contactMobile">
                <el-input v-model="form1.contactMobile" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="硬件厂家" prop="manufacturers">
                <el-input v-model="form1.manufacturers" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="品牌名称" prop="brandName">
                <el-input v-model="form1.brandName" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否可用" prop="yesNo">
                <el-input v-model="form1.yesNo" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通讯地址" prop="commAddr">
                <el-input v-model="form1.commAddr" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务范围" prop="serviceScope">
                <el-input v-model="form1.serviceScope" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="驻扎地点" prop="station">
                <el-input v-model="form1.station" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="考核资质">
                <el-input v-model="form1.aptitude" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="仓储能力" prop="storage">
                <el-input v-model="form1.storage" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同有效期">
                <el-input v-model="form1.duration" size="mini" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上年度评价">
                <el-input v-model="form1.performance" size="mini" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div v-show="activeName == '2'" class="queryParamsWrap">
        <el-form :model="form2" :rules="rules2" ref="form2" :inline="true">
          <el-row>
            <el-col :span="12">
              <el-form-item label="资质" prop="builder">
                <el-input v-model="form3.builder" size="mini" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div v-show="activeName == '3'" class="queryParamsWrap">
        <el-form :model="form3" :rules="rules3" ref="form3" :inline="true">
          <el-row>
            <el-col :span="12">
              <el-form-item label="服务范围" prop="builder">
                <el-input v-model="form3.builder" size="mini" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <el-dialog
        title="获取经纬度"
        :visible.sync="latLongVisible"
        :close-on-click-modal="false"
        @close="closeLatLongVisible"
        append-to-body
        width="40%"
      >
        <Amap @confirm="getLatLong" height="400px" />
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeVisible">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="preStep"
          v-show="activeName != '1'"
          >上一步</el-button
        >
        <el-button
          type="primary"
          @click.stop="nextStep"
          v-show="activeName != '3'"
          >下一步</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitForm"
          v-show="activeName == '3'"
          >创建服务商</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Amap from "@/components/Amap/amap.vue";
import { regionData } from "element-china-area-data";
import { getToken } from "@/utils/auth";
import { saveProvider } from "@/api/provider/provider";

export default {
  components: {
    Amap,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let baseUrl =
      process.env.VUE_APP_BASE_API == "/" ? "" : process.env.VUE_APP_BASE_API;
    return {
      activeName: "1",
      form1: {},
      form2: {
        fileList: [],
      },
      form3: {},
      form4: {},
      form5: {
        typeList: [
          {
            relaBizType: "construction",
            orderType: "",
          },
        ],
      },
      rules1: {
        projectCode: [
          { required: true, message: "请填写站点名称", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "请填写站点编码", trigger: "blur" },
        ],
      },
      rules2: {
        stationName: [
          { required: true, message: "请填写站点名称", trigger: "blur" },
        ],
      },
      rules3: {
        builder1: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_UPLOAD_URL,
      },
      businessTypeOption: [], //业务类型
      useFlowList: [], //工单类型
      latLongVisible: false,
      districtOptions: regionData, //省市数据
      fileList: [],
      accept1: ".jpg, .jpeg, .png",
      accept: ".png, .jpg, .rar, .zip, .doc, .docx, .pdf",
      options: [],

      stationFileList: [],
      fileLimit: 3,

      dialogImageUrl: "",
      dialogVisible: false,
    };
  },
  computed: {},
  watch: {},
  methods: {
    preStep() {
      if (Number(this.activeName) > 1) {
        this.activeName = (Number(this.activeName) - 1).toString();
      }
    },
    nextStep() {
      if (this.activeName == "4") {
        this.activeName = (Number(this.activeName) + 1).toString();
      } else {
        let formName = "form" + this.activeName;
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if (Number(this.activeName) < 5) {
              this.activeName = (Number(this.activeName) + 1).toString();
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    handleClick(val) {},
    beforeLeave(activeName, oldActiveName) {},
    submitForm() {
      this.$refs.form3.validate((valid) => {
        if (valid) {
          this.$refs.form2.validate((valid) => {
            if (valid) {
              this.$refs.form1.validate((valid) => {
                if (valid) {
                  let param = {
                    cmProvider: this.form1,
                  };

                  //所有校验成功提交方法
                  saveProvider(param).then((res) => {
                    this.$message.success("保存成功");
                    this.$emit("closeDialog");
                  });
                } else {
                  console.log("tab1 error submit!!");
                  this.activeName = "1";
                  return false;
                }
              });
            } else {
              console.log("tab2 error submit!!");
              this.activeName = "2";
              return false;
            }
          });
        } else {
          console.log("tab3 error submit!!");
          this.activeName = "3";
          return false;
        }
      });
    },
    submitFun() {
      let params = {
        stationInfo: {
          // 场站信息
          ...this.form1,
          ...this.form2,
          ...this.form3,
        },
        docList: [
          // 文件集合
        ],
        businessFlowList: [
          // 工单参数
        ],
      };
    },
    closeVisible() {
      this.$emit("update:visible", false);
    },
    closeLatLongVisible() {
      this.latLongVisible = false;
    },
    openLatLong() {
      this.latLongVisible = true;
    },
    getLatLong(obj) {
      console.log("获取经纬度信息", obj);
      this.form2.longitude = obj.position.toString();
      this.form2.stationAddress = obj.name;
      this.latLongVisible = false;
    },
    handleUpload(row) {},
    handleDel(row) {},
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    delItem(index) {
      this.form5.typeList.splice(index, 1);
    },
    addItem() {
      this.form5.typeList.push({
        relaBizType: "construction",
        orderType: "",
      });
    },
    //获取工单类型列表
    async getUseFlowList(businessType) {
      let params = {
        businessTypeList: businessType,
      };
      const { code, data } = await useFlowListApi(params);
      if (code != 10000) return;
      this.useFlowList = data;
    },

    handleExceed() {},
    fileRemove(file, fileList) {},
    fileChange() {},
    handleFileUploadProgress() {},
    handleFileSuccess() {},

    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 图片上传成功回调
    uploadSuccess(response, file, fileList) {
      // if (response.code == 10000) {
      //   this.form2.stationImage = response.data;
      // }
      console.log("form2.fileList", this.form2.fileList);
    },
    // 图片上传失败回调
    uploadError(err, file, fileList) {
      console.log(err);
    },
  },
  async created() {
    await this.getUseFlowList("construction");
    //获取业务类型字典
    this.getDicts("flow_business_type").then((response) => {
      this.businessTypeOption = response?.data;
    });
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}

.radius-wrap {
  /deep/ .el-form-item__content > div {
    display: flex !important;
  }
}

.append-btn {
  /deep/ .el-input-group__append {
    width: 100px;
    background-color: #029c7c;
    color: #fff;
    cursor: pointer;
  }
}

/deep/ .el-input-group__append {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn-wrap {
  display: flex;
  margin-top: 20px;

  .upload-label {
    margin-top: 10px;
  }
}
</style>
