<template>
  <div class="app-container">
    <div class="queryParamsWrap">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="80px"
        :rules="rules"
      >
        <el-row>
          <el-col v-if="false" :span="6">
            <el-form-item label="公司" prop="deptIds" label-width="80px">
              <treeselect v-model="deptIds" :limit="1" :multiple="true" :options="deptOptions"
                          :flat="true"
                          placeholder="请选择公司" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建日期" prop="daterange" label-width="80px">
              <el-date-picker
                v-model="queryParams.daterange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="流程类型" prop="flowTypes" label-width="80px">
              <el-select
                filterable
                v-model="queryParams.flowTypes"
                placeholder="请选择流程类型"
                clearable
              >
                <el-option
                  v-for="item in orderTypeOptions"
                  :key="item.typeId"
                  :label="item.typeLabel"
                  :value="item.typeCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="(tableKey==='all' || tableKey==='owe') && queryParams.flowStatus ==='1'">
            <el-form-item label="处理人" prop="operatorId" label-width="80px">
              <el-select
                filterable
                v-model="queryParams.operatorId"
                placeholder="请选择处理人"
                clearable
              >
                <el-option
                  v-for="item in handleUserOptions"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="紧急程度" prop="emergencyLevel" label-width="80px">
              <el-select
                filterable
                v-model="queryParams.emergencyLevel"
                placeholder="请选择紧急程度"
                clearable
              >
                <el-option
                  v-for="item in urgentLevelOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="tableKey==='all' || tableKey==='owe'">
            <el-form-item label="工单状态" prop="flowStatus" label-width="80px">
              <el-select
                filterable
                v-model="queryParams.flowStatus"
                placeholder="请选择工单状态"
                @change="flowStatusChange"
              >
                <el-option
                  v-for="item in orderStatusOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="工单编号"
              prop="businessNo"
              label-width="80px"
            >
              <el-input
                size="small"
                v-model="queryParams.businessNo"
                placeholder="请输入工单编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="报装人"
              prop="consName"
              label-width="80px"
            >
              <el-input
                size="small"
                v-model="queryParams.consName"
                placeholder="请输入报装人"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item
              label="报装地址"
              prop="addr"
              label-width="80px"
            >
              <el-input
                size="small"
                v-model="queryParams.addr"
                placeholder="请输入报装地址"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item
              label="联系方式"
              prop="mobile"
              label-width="80px"
            >
              <el-input
                size="small"
                v-model="queryParams.mobile"
                placeholder="请输入联系方式"
                clearable
              />
            </el-form-item>
          </el-col>


<!--          <el-col :span="6">-->
<!--            <el-form-item label="用水性质">-->
<!--              <el-select-->
<!--                v-model="queryParams.waterType"-->
<!--                placeholder="请选择用水性质"-->
<!--                clearable-->
<!--              >-->
<!--                <el-option v-for="item in waterTypeList" :key="item.dictValue"-->
<!--                           :label="item.dictLabel" :value="item.dictValue"/>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->

            <el-col>
              <div style="margin-left: 1em;float: right">
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click.stop="freshTable"
                >查询
                </el-button>
                <el-button
                  icon="el-icon-refresh"
                  @click.stop="resetQuery"
                  size="mini"
                >重置
                </el-button>
              </div>
            </el-col>
        </el-row>
        <el-row>
          <div style="margin-left: 1em">
            <el-button
              type="primary"
              size="mini"
              @click.stop="createOrder"
            >创建工单
            </el-button>
          </div>
        </el-row>
      </el-form>

      <div class="div_bottom">
        <el-tabs v-model="activeName" @tab-click="handleTabClick">
          <el-tab-pane label="全部" name="first">
            <AllTable ref="allTable" v-if="tableKey==='all'" :queryForm="queryParams" />
          </el-tab-pane>
          <el-tab-pane label="我发起" name="second">
            <OweTable ref="oweTable" v-if="tableKey==='owe'" :queryForm="queryParams" />
          </el-tab-pane>
          <el-tab-pane label="待处理" name="third">
            <ToDealTable ref="toDealTable" v-if="tableKey==='toDeal'" :queryForm="queryParams" />
          </el-tab-pane>
          <el-tab-pane label="待接收" name="forth">
            <HoldTable ref="holdTable" v-if="tableKey==='hold'" :queryForm="queryParams" />
          </el-tab-pane>
          <!-- <el-tab-pane label="已完成" name="fifth">
            <FinishTable ref="finishTable" v-if="tableKey==='finish'" :queryForm="queryParams" />
          </el-tab-pane> -->
        </el-tabs>
      </div>
      <!-- 创建工单对话框 -->
      <el-dialog
        title="创建工单"
        :visible.sync="dialogVisible"
        @close="closeCreateOrderDialog"
        :close-on-click-modal="false"
        width="1000px"
        top="8vh"
      >
        <CreateOrder
          @cancelDialog="closeCreateOrderDialog"
          @freshTable="freshTable"
          v-if="dialogVisible"
        />
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import { treeselect } from "@/api/system/dept";
import ToDealTable from "./components/toDealTable.vue";
import AllTable from "./components/allTable.vue";
import OweTable from "./components/oweTable.vue";
import HoldTable from "./components/holdTable.vue";
import CreateOrder from "./components/createOrder.vue";
import FinishTable from "./components/finishTable.vue";
import { getUserAvailUsers } from "@/api/process/lfDesign";
import {
  workOrderTypeListByPack
} from "@/api/orderScheduling/workStation";

export default {
  components: {
    AllTable,
    OweTable,
    ToDealTable,
    HoldTable,
    CreateOrder,
    Treeselect,
    IconSelect,
    FinishTable
  },
  data() {
    return {
      queryParams: {
        daterange: [],
        flowTypes: [],
        operatorId: "",
        urgentLevel: "",
        flowStatus: "1"
      },
      deptOptions: [], //tree列表
      deptIds: [this.$store.getters.deptId],  //选中的tree
      // 遮罩层
      loading: false,
      activeName: "third",
      orderTypeOptions: [],
      handleUserOptions: [],
      urgentLevelOptions: [],
      orderStatusOptions: [],
      waterTypeList: [],
      tableKey: "toDeal",
      dialogVisible: false,
      pageInitQuery: false
    };
  },
  async created() {
    // await this.getTreeselect();
    this.urgentLevelOptions = [{ id: "0", label: "不紧急" }, { id: "1", label: "正常" }, { id: "2", label: "紧急" }, {
      id: "3",
      label: "非常紧急"
    }];
    getUserAvailUsers({
      pageNum: 1,
      pageSize: 9999
    }).then(response => {
      this.handleUserOptions = response.data;
    });

    // this.getDicts("crm_cons_water_type").then(response => {
    //   this.waterTypeList = response.data;
    // });

    workOrderTypeListByPack({"typeStatus": "1"}).then(response => {
      this.orderTypeOptions = response.data;
    });
    this.orderStatusOptions = [{ id: "1", label: "进行中" }, { id: "2", label: "已完结" }, { id: "3", label: "已作废" }];
  },
  methods: {
    flowStatusChange(value) {
      this.freshTable();
    },
    /** 查询部门下拉树结构 */
    async getTreeselect() {
      await treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    handleTabClick(tab) {
      if (tab.name === "first") {
        this.tableKey = "all";
      } else if (tab.name === "second") {
        this.tableKey = "owe";
      } else if (tab.name === "third") {
        this.tableKey = "toDeal";
      } else if (tab.name === "forth") {
        this.tableKey = "hold";
      } else if (tab.name === "fifth") {
        this.tableKey = "finish";
      }
    },
    createOrder() {
      this.dialogVisible = true;
    },
    freshTable() {
      if (this.queryParams.flowStatus !== '1') {
        this.queryParams.operatorId = ''
      }
      if (this.activeName === "first") {
        this.$nextTick(() => {
          this.$refs.allTable.handleQuery();
        })
      } else if (this.activeName === "second") {
        this.$nextTick(() => {
          this.$refs.oweTable.handleQuery();
        })
      } else if (this.activeName === "third") {
        this.$nextTick(() => {
          this.$refs.toDealTable.handleQuery();
        })
      } else if (this.activeName === "forth") {
        this.$nextTick(() => {
          this.$refs.holdTable.handleQuery();
        })
      } else if (this.activeName === "fifth") {
        this.$nextTick(() => {
          this.$refs.finishTable.handleQuery();
        })
      }
    },
    closeCreateOrderDialog() {
      this.dialogVisible = false;
      this.freshTable();
    },
    resetQuery() {
      this.queryParams = {
        daterange: [],
        flowTypes: [],
        handleUser: "",
        urgentLevel: "",
        flowStatus: "1"
      }
      this.freshTable()
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-form-item--small .el-form-item__content, .el-form-item--small .el-form-item__label {
  line-height: 12px;
}

.app-container {
  /deep/ .el-input-group--append {
    display: flex !important;
  }

  /deep/ .el-input-group__append {
    padding: 0;
    width: 50px;
    display: flex;
    justify-content: center;
  }

  .div_bottom {
    padding: 1em;
  }
}

</style>

