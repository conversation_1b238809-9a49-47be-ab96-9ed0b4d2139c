<!-- 已完成 -->
<template>
  <div class="">
    <el-table v-loading="loading" ref="multipleTable" :data="tableData">
      <el-table-column label="工单编号" prop="businessNo" />
      <el-table-column label="工单类型" prop="workOrderCate" />
      <el-table-column label="报装人">
        <template slot-scope="scope">
          <span v-if = "scope.row.varsMap">{{scope.row.varsMap.consName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报装地址">
        <template slot-scope="scope">
          <span v-if = "scope.row.varsMap">{{scope.row.varsMap.addr }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报装人电话">
        <template slot-scope="scope">
          <span v-if = "scope.row.varsMap">{{scope.row.varsMap.mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="queryForm.flowStatus == '1'"
        label="处理进度"
        prop="taskName"
      />
      <el-table-column
        v-if="queryForm.flowStatus == '2'"
        label="执行结果"
        prop="flowResult"
      />
      <el-table-column
        label="工单说明"
        prop="flowRemark"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建人"
        prop="creator"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="开始时间"
        prop="createTime"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="签入时间"
        prop="claimTime"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.claimTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="完成时间"
        prop="finishedTime"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.finishedTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="耗时"
        prop="lastTimeStr"
        :show-overflow-tooltip="true" />
      <el-table-column
        label="操作"
        align="lastOperator"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button @click="toView(scope.row)" size="mini" type="text"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="pageTotal > 0"
      :total="pageTotal"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--工单查看-->
    <OrderInfo v-if="drawerVisible" :drawer="drawerVisible" :orderItem="drawerOrder" @closeDrawer="closeDrawer" />
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import { listInstallIFinished } from "@/api/orderScheduling/workStation";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {OrderInfo},
  props: ["queryForm"],
  data() {
    //这里存放数据
    return {
      loading: false,
      tableData: [],
      pageTotal: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      drawerVisible: false,
      drawerOrder: {}
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      this.queryParams = {
        ...parentQueryForm,
        pageNum: 1,
        pageSize: 10
      };
      this.getList();
    },
    getList() {
      this.loading = true;
      listInstallIFinished(this.queryParams).then(response => {
        this.tableData = response.data;
        this.pageTotal = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    let parentQueryForm = this._.cloneDeep(this.queryForm);
    this.queryParams = {
      ...parentQueryForm,
      pageNum: 1,
      pageSize: 10
    };
    this.handleQuery();
  },
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>