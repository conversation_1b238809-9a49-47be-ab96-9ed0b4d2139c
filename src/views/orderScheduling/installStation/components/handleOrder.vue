<template>
  <div class="app-container">
    <el-form
      ref="form"
      label-width="88px"
      style="margin-top: 0px; width: 1000px"
    >
      <div
        class="wrap-top"
        style="max-height: 600px;overflow: hidden;overflow-y: auto"
      >
        <div class="work-type-form">
          <el-divider content-position="left" v-if="reviewNodeList.length > 0">
            以下是查询信息
          </el-divider>
          <div v-for="item in reviewNodeList" :key="item">
            <form-create
              :rule="item.rule"
              v-model="receiveFormData"
              :option="item.option"
            />
          </div>
          <el-divider content-position="left" v-if="dealNodeList.length > 0">
            请填写以下信息
          </el-divider>
          <div v-for="(item, index) in dealNodeList" :key="item">
            <form-create
              :rule="item.rule"
              v-model="dealFormData[index]"
              :option="item.option"
            />
          </div>
          <el-divider content-position="left" v-if="commentsList.length > 0">
            以下是提交信息
          </el-divider>
          <div style="margin-left: 35px">
            <div
              v-for="item in commentsList"
              :key="item.operateTime"
              class="work-type-comment"
            >
              <div
                style="display: flex; flex-direction: row; justify-content: space-between"
              >
                <span>提交结果：{{ item.commentResult }}</span>
                <span>提交时间：{{ parseTime(item.operateTime) }}</span>
              </div>
              <div style="margin-top: 10px">
                <span>提交人：{{ item.operator }}</span>
              </div>
              <div style="margin-top: 10px">
                <span>备注：{{ item.comment }}</span>
              </div>
              <el-divider />
            </div>
          </div>
        </div>
        <div style="margin-top: 20px; margin-right: 40px;">
          <work-flow
            title="工单流程"
            :customHeight="400"
            :workFlowList="workFlowList"
          />
        </div>
      </div>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: right;margin-top: 20px;"
    >
      <el-button
        v-for="item in commitGroups"
        :type="item.configExtendValue"
        :key="item.configId"
        @click="openHandleCommitDialog(item)"
      >
        {{ item.configLabel }}
      </el-button>
      <el-button @click="cancelDialog">取 消</el-button>
    </div>

    <!-- 工单处理确认框 -->
    <el-dialog
      title="提交确认"
      :visible.sync="handleCommit.visible"
      @close="closeHandleCommitDialog"
      :close-on-click-modal="false"
      width="600px"
      append-to-body
      :show-close="false"
    >
      <span v-if="handleCommit.isSubmit">请确认是否提交？</span>
      <el-form
        v-else
        :model="handleCommit.optionForm"
        ref="optionForm"
        label-width="80px"
        :rules="handleCommit.optionFormRules"
      >
        <el-row>
          <el-form-item :label="handleCommit.title" prop="opinion">
            <el-input
              type="textarea"
              :rows="5"
              v-model="handleCommit.optionForm.opinion"
              placeholder="请输入审批意见"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button type="primary" @click="handleWorkOrder">确 定</el-button>
        <el-button @click="closeHandleCommitDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import WorkFlow from "./workFlow";
import {
  dealComplete,
  getNodeHisInfoInfos,
  taskConfigs,
  taskForms,
} from "@/api/orderScheduling/workStation";

export default {
  props: ["rowData"],
  components: { WorkFlow },
  async created() {
    const loading = this.$loading({
      lock: true,
      text: "加载中",
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)",
    });
    this.queryParams = this._.cloneDeep(this.rowData);
    const param = {
      flowId: this.queryParams.flowId,
      taskId: this.queryParams.flowableTaskId,
      processInstId: this.queryParams.processInstanceId,
      plat: "PC",
    };
    await taskConfigs(param).then((response) => {
      this.commitGroups = response.data.configs;
    });
    getNodeHisInfoInfos(param).then((response) => {
      this.workFlowList = response.data;
    });
    await taskForms(param)
      .then((response) => {
        this.commentsList = response.data.comments;
        this.reviewNodeList = response.data.reviewForms.map((item) => {
          let options = JSON.parse(item.formConfig);
          let rule = JSON.parse(item.formJson);
          rule.forEach((element) => {
            element.props = {
              ...element.props,
              disabled: true,
            };
          });
          options.submitBtn = false;
          return {
            rule: rule,
            option: options,
          };
        });
        this.dealForms = response.data.dealForms;
        this.dealNodeList = response.data.dealForms.map((item, index) => {
          // 初始化填写表单form-create绑定模型
          this.dealFormData[index] = {};
          let options = JSON.parse(item.formConfig);
          if (!!options.submitBtn) {
            this.formDataCreateShow = false;
          }
          let rule = JSON.parse(item.formJson);
          rule.map((item) => {
            if (item.type == "upload") {
              if (
                item.props?.uploadType == "file" ||
                item.props?.uploadType == "image"
              ) {
              } else {
                this.$set(item.props, "uploadType", "image");
              }
              this.$set(item.props, "onSuccess", function(res, file, fileList) {
                file.url = res.data;
              });
            }
          });
          return {
            rule: rule,
            option: options,
          };
        });
        loading.close();
      })
      .catch((err) => {
        loading.close();
      })
      .finally(() => {
        loading.close();
      });
  },
  data() {
    return {
      formDataCreateShow: true,
      queryParams: {},
      receiveFormData: {},
      dealFormData: [],
      //查看
      reviewNodeList: [],
      //处理
      dealNodeList: [],
      //审核信息
      commentsList: [],
      //获取的原始数据
      dealForms: [],
      commitGroups: [],
      //工单流程
      workFlowList: [],
      handleCommit: {
        isSubmit: false,
        title: "",
        visible: false,
        optionForm: {
          opinion: "",
        },
        optionFormRules: {
          opinion: [
            { required: true, message: "理由不能为空", trigger: "blur" },
          ],
        },
        commitType: {},
      },
    };
  },
  methods: {
    /**
     * 提交创建工单
     */
    async handleWorkOrder() {
      if (
        this.handleCommit.isSubmit ||
        this.handleCommit.commitType.configName === "APPROVE"
      ) {
        // 表单提交以及审核通过需要校验填写表单的内容
        if (!(await this.checkDealForms())) {
          return;
        }
        if (this.handleCommit.commitType.configName === "APPROVE") {
          if (!(await this.checkOptionRule())) {
            return;
          }
        }
        this.commitOrder();
      } else {
        if (await this.checkOptionRule()) {
          this.commitOrder();
        }
      }
    },
    commitOrder() {
      const param = {
        configId: this.handleCommit.commitType.configId,
        // dealForms: this.dealForms,
        flowId: this.queryParams.flowId,
        processInstanceId: this.queryParams.processInstanceId,
        taskId: this.queryParams.flowableTaskId,
        opinion: this.handleCommit.optionForm.opinion,
      };
      if (this.dealNodeList.length > 0) {
        param.dealForms = this.dealForms.map((item, index) => {
          return {
            ...item,
            formJson: JSON.stringify(this.dealNodeList[index].rule),
            formConfig: JSON.stringify(this.dealNodeList[index].option),
          };
        });
      }
      const loading = this.$loading({
        lock: true,
        text: "提交中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      dealComplete(param)
        .then(() => {
          this.$message.success("处理成功");
          loading.close();
          this.$emit("freshTable");
          this.cancelDialog();
        })
        .catch(() => {
          loading.close();
        });
    },

    /**
     * 校验审核通过 / 作废 / 驳回的必填理由
     */
    async checkOptionRule() {
      let flag = false;
      await this.$refs.optionForm.validate((valid) => {
        flag = valid;
      });
      return flag;
    },
    async checkDealForms() {
      let flag = true;
      if (this.dealForms.length > 0) {
        flag = false;
        for (let i = 0; i < this.dealFormData.length; i++) {
          await this.dealFormData[i].submit(() => {
            if (i === this.dealFormData.length - 1) {
              flag = true;
            }
          });
        }
      }
      return flag;
    },

    cancelDialog() {
      this.$emit("cancelDialog");
    },
    openHandleCommitDialog(commitType) {
      this.handleCommit.commitType = commitType;
      this.handleCommit.visible = true;
      this.handleCommit.optionForm.opinion = "";
      if (commitType.configName === "FORM_SUBMIT") {
        this.handleCommit.isSubmit = true;
      } else {
        this.handleCommit.isSubmit = false;
        if (commitType.configName === "APPROVE") {
          this.handleCommit.title = "备注";
        } else if (commitType.configName === "ALLOW_REJECT") {
          this.handleCommit.title = "驳回理由";
        } else if (commitType.configName === "ALLOW_RECALL") {
          this.handleCommit.title = "召回理由";
        } else if (commitType.configName === "ALLOW_INVALID") {
          this.handleCommit.title = "作废理由";
        }
      }
    },
    closeHandleCommitDialog() {
      this.handleCommit.visible = false;
      this.handleCommit.optionForm.opinion = "";
    },
  },
};
</script>
<style scoped>
.app-container {
  overflow: hidden;
  overflow-y: auto;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.wrap-top {
  display: flex;
}

.work-type-form {
  margin-right: 20px;
}

/*el-form-item label和输入框垂直居中*/
.queryParamsWrap .el-form-item /deep/ .el-form-item__label {
  line-height: 32px;
}

/*去掉折叠组件边框*/
/deep/ .el-collapse {
  border: 0;
  padding: 5px 0;
}

/deep/ .el-collapse-item__header {
  height: 40px;
  border: 0;
  padding: 0 10px;
}

/deep/ .el-collapse-item__header .el-collapse-item__arrow {
  position: absolute;
  left: auto;
  right: 10px;
}

/*el-form-label 必填项红色星星显示在左侧*/
/deep/
  .el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label:before {
  content: "";
  display: none;
}

/deep/
  .el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label:after {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
  font-size: large;
  width: 5px;
}
</style>
