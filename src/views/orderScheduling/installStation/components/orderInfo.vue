<!-- 查看 -->
<template>
  <div class="app-container">
    <el-drawer :title="title" :visible.sync="drawerVisible" :before-close="beforeCloseDrawer" size="40%">
      <div v-loading="hisInfoLoading"
           element-loading-text="流程信息加载中"
           style="height: 100%; width: 100%; overflow: hidden; overflow-y: auto; padding: 0 25px 0 25px;">
        <el-steps :space="200" :active="currentNode" finish-status="success">
          <el-step :title="item.nodeName" v-for="item in orderNodeList" :key="item.taskId"></el-step>
        </el-steps>

        <div style="padding: 15px 10px 10px 10px" v-for="item in orderNodeList" :key="item.endTime">
          <div class="title-line-wrap">
            <div class="left-wrap">
              <span class="line"></span>
              <span style="color: #3f9eff">{{ item.nodeName }}</span>
            </div>
            <el-link type="primary" :underline="false" @click="toggle(item)">{{
                item.isShow ? "收起" : "展开"
              }}
            </el-link>
          </div>

          <div
              class="el-step is-vertical"
              style="flex-basis: 50%"
              v-show="item.isShow"
          >
            <div class="el-step__main">
              <div class="el-step__description">
                <div class="stepcontent-wrap">
                  <div v-if="item.assignee">
                    指定{{ item.assignee.startsWith("$") ? "值" : "人" }}： {{
                      item.assignee
                    }}
                  </div>
                  <div v-if="item.candidateUsers">
                    候选人：{{ item.candidateUsers.map(element => element.nickName).join(",") }}
                  </div>
                  <div v-if="item.candidateGroups">
                    候选组：{{ item.candidateGroups.map(element => element.groupName).join(",") }}
                  </div>
                  <div v-if="item.startTime">开始时间: {{ item.startTime }}</div>
                  <div v-if="item.endTime">结束时间: {{ item.endTime }}</div>
                  <div v-if="item.durationInMillis" style="display: flex;justify-content: space-between">
                    <span style="width: 120px">耗时: {{ item.durationInMillis }}</span>
                    <span v-show="item.comment" style="margin-left: 20px">备注: {{ item.comment }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </el-drawer>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

import {getNodeHisInfoInfos} from "@/api/orderScheduling/workStation";

export default {
  //import引入的组件需要注入到对象中才能使用
  props: {
    title: {
      type: String,
      default: "流程总览"
    },
    drawer: {
      type: Boolean,
      default: false
    },
    orderItem: {
      type: Object
    }
  },
  components: {},
  created() {
    this.queryParams = this._.cloneDeep(this.orderItem);
    this.drawerVisible = this.drawer;
    let param = {
      processInstId: this.queryParams.processInstanceId
    };
    this.hisInfoLoading = true;
    getNodeHisInfoInfos(param).then(response => {
      this.orderNodeList = response.data;
      this.orderNodeList.forEach((element) => {
        this.$set(element, "isShow", true);
      });
      let currentNode;
      if(this.queryParams.flowableTaskId == null) {
        currentNode = this.orderNodeList.length;
      } else {
        currentNode= this.orderNodeList.map(item => item.taskId).indexOf(this.queryParams.flowableTaskId);
      }
      this.currentNode = currentNode;

      this.hisInfoLoading = false;
    }).catch(() => {
      this.hisInfoLoading = false;
    });
  },
  data() {
    //这里存放数据
    return {
      queryParams: {},
      drawerVisible: undefined,
      orderNodeList: [],
      // 当前所在节点
      currentNode: 0,
      hisInfoLoading: false
    };
  },
  computed: {},
  watch: {},
  //方法集合
  methods: {
    beforeCloseDrawer(done) {
      this.$emit("closeDrawer");
      this.drawerVisible = false;
      done();
    },
    toggle(item) {
      this.orderNodeList.forEach((element) => {
        if (element.taskId == item.taskId) {
          this.$set(element, "isShow", !item.isShow);
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ .el-drawer__body {
  padding: 10px;
}

.app-container {
  padding: 100px;
}

/deep/ .el-drawer {
  padding: 0;
  overflow-y: auto;
}

.title-line-wrap {
  margin: 10px -10px;
  display: flex;
  justify-content: space-between;

  .left-wrap {
    display: flex;
    align-items: center;

    .line {
      width: 5px;
      height: 20px;
      background-color: #3f9eff;
      display: inline-block;
      margin-right: 5px;
    }
  }
}

.stepcontent-wrap div {
  padding-top: 0.5em;
}

/deep/ .el-step__description {
  padding-right: 10% !important;
}
</style>
