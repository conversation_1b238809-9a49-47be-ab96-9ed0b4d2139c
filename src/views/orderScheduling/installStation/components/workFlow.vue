<template>
  <div class="content" :style="{ height: customHeight + 'px' }">
    <div class="title">{{ title }}</div>

    <el-collapse v-model="activeName" style="flex:1;overflow: auto">
      <el-collapse-item
          v-for="(item, index) in workFlowList"
          :key="index"
          :name="index"
      >
        <template slot="title">
          <div class="work-flow-title">
            <span class="work-flow-index">{{ index + 1 }}</span>
            <span class="work-flow-title-text">{{ item.nodeName }}</span>
          </div>
        </template>

        <div class="work-flow-content">
          <div v-if="item.assignee">
            指定{{ item.assignee.startsWith("$") ? "值" : "人" }}： {{
              item.assignee
            }}
          </div>
          <div v-if="item.users">
            处理人：{{ item.users.map(user => user.nickName).join("、") }}
          </div>
          <div v-if="item.groups">
            处理部门：{{ item.groups.map(user => user.groupName).join("、") }}
          </div>
          <div v-if="item.startTime">开始时间：{{ item.startTime }}</div>
          <div v-if="item.endTime">结束时间：{{ item.endTime }}</div>
          <div v-if="item.durationInMillis" style="">
            <span>耗时：{{ item.durationInMillis }}</span>
            <span style="margin-left: 30px;" v-show="item.comment">备注：{{ item.comment }}</span>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
export default {
  name: "workFlow",
  props: {
    title: {
      type: String
    },
    customHeight: {
      type: Number
    },
    workFlowList: {
      type: Array
    }
  },
  data() {
    return {
      activeName: "" //当前展开的工作流，索引值
    };
  }
};
</script>

<style scoped>
.content {
  width: 440px;
  height: 240px;
  border: 1px solid #dcdfe6;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow-y: auto;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.title {
  border-bottom: 1px solid #dcdfe6;
  height: 33px;
  line-height: 33px;
  padding-left: 10px;
}

.work-flow-title {
  background: #f2f2f2;
  width: 100%;
  height: 30px;
  line-height: 30px;
}

.work-flow-index {
  display: inline-block;
  width: 30px;
  color: white;
  background: #4295e2;
  text-align: center;
}

.work-flow-title-text {
  font-size: 12px;
  color: #4295e2;
  padding-left: 20px;
}

.work-flow-content {
  padding: 0 10px 10px 25px;
}

.work-flow-content div {
  padding-top: 3px;
  margin-left: 1em;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0;
}
</style>
