<template>
  <div>
    <el-table v-loading="loading" ref="multipleTable" :data="tableData">
      <el-table-column label="工单编号" prop="businessNo" />
      <el-table-column label="工单类型" prop="workOrderCate" />
      <el-table-column label="报装人">
        <template slot-scope="scope">
          <span v-if = "scope.row.varsMap">{{scope.row.varsMap.consName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报装地址">
        <template slot-scope="scope">
          <span v-if = "scope.row.varsMap">{{scope.row.varsMap.addr }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报装人电话">
        <template slot-scope="scope">
          <span v-if = "scope.row.varsMap">{{scope.row.varsMap.mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="queryForm.flowStatus=='1'" label="处理进度" prop="taskName" />
      <el-table-column v-if="queryForm.flowStatus=='1'" label="当前处理人" prop="taskAssignee" />
      <el-table-column v-if="queryForm.flowStatus=='1'" label="当前状态" prop="taskStatus" :formatter="taskStatusFormat" :show-overflow-tooltip="true" />
      <el-table-column label="工单状态" prop="flowStatus" :formatter="flowStatusFormat" :show-overflow-tooltip="true" />
      <el-table-column label="紧急程度" prop="emergencyLevel" :show-overflow-tooltip="true" />
      <el-table-column v-if="queryForm.flowStatus=='2'" label="执行结果" prop="flowResult" />
      <el-table-column label="工单说明" prop="flowRemark" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" prop="createTime" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="creator" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="lastOperator" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button @click="toView(scope.row)" size="mini" type="text">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="pageTotal > 0" :total="pageTotal" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList" />

    <!--工单查看-->
    <OrderInfo v-if="drawerVisible" :drawer="drawerVisible" :orderItem="drawerOrder" @closeDrawer="closeDrawer" />

  </div>
</template>
<script>
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import { listInstallAll } from "@/api/orderScheduling/workStation";
import moment from "moment";

export default {
  props: ["queryForm"],
  components: { OrderInfo },
  watch: {},
  mounted() {
    let parentQueryForm = this._.cloneDeep(this.queryForm);
    this.queryParams = {
      ...parentQueryForm,
      // 开始时间
      beginTime:
        parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") + " 00:00:00" : null,
      // 结束时间
      endTime:
        parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") + " 23:59:59" : null,
      pageNum: 1,
      pageSize: 10

    };
    this.handleQuery();
  },
  data() {
    return {
      loading: true,
      pageInitQuery: false,
      queryParams: {},
      pageTotal: 0,
      tableData: [],
      drawerVisible: false,
      drawerOrder: {},
      taskStatusOptions: []
    };
  },
  methods: {
    taskStatusFormat(row) {
      if (row.taskStatus == "1") {
        return "待办";
      } else if (row.taskStatus == "2") {
        return "待处理";
      }
    },

    flowStatusFormat(row) {
      if (row.flowStatus == "1") {
        return "流转中";
      } else if (row.flowStatus == "2") {
        return "已完成";
      } else if (row.flowStatus == "3") {
        return "已作废";
      }
    },
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      this.queryParams = {
        ...parentQueryForm,
        // 开始时间
        beginTime:
          parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") + " 00:00:00" : null,
        // 结束时间
        endTime:
          parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") + " 23:59:59" : null,
        pageNum: 1,
        pageSize: 10
      };
      this.getList();
    },
    getList() {
      console.log(this.queryParams);
      listInstallAll(this.queryParams).then(response => {
        this.tableData = response.data;
        this.pageTotal = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    }
  }
}
;
</script>
<style lang="less" scoped>

</style>
