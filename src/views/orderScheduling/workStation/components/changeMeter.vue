<!-- 工单-处理--换表 -->
<template>
  <div class="">
      <!-- <el-row type="flex" justify="end">
        <el-button @click="$emit('closeDialog')">取 消</el-button>
        <el-button type="primary" @click="changeMeter" v-show="this.startBusinessFlow === 'N'">换 表</el-button>
        <el-button type="primary" @click="changeMeter" v-show="this.startBusinessFlow === 'Y'">提 交</el-button>
      </el-row> -->

      <el-form
        :model="form"
        ref="form"
        label-width="85px"
        :inline="true"
        size="mini"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="用户编号" prop="consNo">
              <!-- <el-input
                suffix-icon="x"
                v-model="form.consNo"
                disabled
              ></el-input> -->
              <span>{{ userInfo.consNo }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户名称" prop="consName">
              <!-- <el-input
                suffix-icon="x"
                v-model="form.consName"
                disabled
              ></el-input> -->
              <span>{{ userInfo.consName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地址" prop="addr">
              <!-- <el-input suffix-icon="x" v-model="form.addr" disabled ></el-input> -->
              <span>{{ userInfo.addr }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row style="margin-top: 10px; margin-bottom: 10px">
        <LineTitle title="旧表信息" fontSize="20" />
      </el-row>

      <el-table :data="tableData1">
        <el-table-column label="水表编号" align="center" prop="madeNo">
        </el-table-column>
        <el-table-column label="最近抄表日期" prop="lastMrTime" align="center" />
        <el-table-column label="最近抄表读数" prop="lastMrNum" align="center" />
        <el-table-column label="拆除读数" align="center">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.downMeterRead"
              :precision="0"
              :controls="false"
              :min="0"
              style="width: 100%"
              disabled
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="水价" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-select
              filterable
              v-model="scope.row.prcCode"
              placeholder="请选择"
              clearable
              disabled
              @change="changePrc(scope.row.prcCode, scope.row)"
            >
              <el-option
                v-for="(item, index) in prcList"
                :key="index"
                :label="item.catPrcName"
                :value="item.prcCode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="水表型号" align="center">
          <template slot-scope="scope">
            <el-select
              filterable
              v-model="scope.row.mtrTypeCd"
              placeholder="请选择"
              clearable
              disabled
              @change="changeMtrTypeCd(scope.row.mtrTypeCd, scope.row)"
            >
              <el-option
                v-for="(item, index) in mtrTypeCdList"
                :key="index"
                :label="item.mtrTypeName"
                :value="item.mtrTypeCd"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="水表厂家" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_Mfgcd(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="水表类型" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_MeterType(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="总表编号" align="center">
          <template slot-scope="scope">
            <el-input
              suffix-icon="x"
              v-model="scope.row.mainMeterNo"
              disabled
            ></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-link type="primary" @click="downMeter(scope.row)"
            v-if="!scope.row.downFlag"
              >拆除</el-link
            >
            <el-link type="primary" @click="cancelDownMeter(scope.row)" v-if="scope.row.downFlag"
              >取消拆除</el-link
            >
          </template>
        </el-table-column> -->
      </el-table>

      <el-row style="margin-top: 10px; margin-bottom: 10px">
        <LineTitle title="新表信息" fontSize="20" />
      </el-row>

      <el-table :data="tableData2">
        <el-table-column label="水表编号" align="center">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.madeNo"
              :precision="0"
              :controls="false"
              :min="0"
              style="width: 100%"
              disabled
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="安装读数" align="center">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.madeRead"
              :precision="0"
              :controls="false"
              :min="0"
              style="width: 100%"
              disabled
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="水价" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-select
              filterable
              v-model="scope.row.prcCode"
              placeholder="请选择"
              clearable
              disabled
              @change="changePrc(scope.row.prcCode, scope.row)"
            >
              <el-option
                v-for="(item, index) in prcList"
                :key="index"
                :label="item.catPrcName"
                :value="item.prcCode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="水表型号" align="center">
          <template slot-scope="scope">
            <el-select
              filterable
              v-model="scope.row.mtrTypeCd"
              placeholder="请选择"
              clearable
              disabled
              @change="changeMtrTypeCd(scope.row.mtrTypeCd, scope.row)"
            >
              <el-option
                v-for="(item, index) in mtrTypeCdList"
                :key="index"
                :label="item.mtrTypeName"
                :value="item.mtrTypeCd"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="水表厂家" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_Mfgcd(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="水表类型" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_MeterType(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="总表编号" align="center">
          <template slot-scope="scope">
            <el-input
              suffix-icon="x"
              v-model="scope.row.mainMeterNo"
              disabled
            ></el-input>
          </template>
        </el-table-column>
      </el-table>

      <el-row style="margin-top: 10px; margin-bottom: 10px">
        <LineTitle title="抄表册" fontSize="20" />
      </el-row>

      <el-form
        :model="form2"
        ref="form2"
        label-width="85px"
        :inline="true"
        size="mini"
      >
        <el-row :gutter="10">
          <el-col>
            <el-form-item label="抄表册" :prop="mrSectNo">
              <el-select
                v-model="form2.mrSectNo"
                placeholder="请选择"
                filterable
                :disabled="true"
              >
                <el-option
                  v-for="(item, index) in sectionList"
                  :key="index"
                  :label="item.name"
                  :value="item.mrSectNo"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import { prcList,premiseDetail, mtrTypeCdList } from '@/api/userManage/userManage';
import { list as sectionList } from "@/api/business/section/section.js";
import { businessFlowParaQueryByProcessId } from "@/api/orderScheduling/workStation";

export default {
  name: "changeMeter",
  //import引入的组件需要注入到对象中才能使用
  components: {
    LineTitle,
  },
  props: {
    visable: {
      type: Boolean,
      default: false,
    },
    rowInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    //这里存放数据
    return {
      userInfo: {},
      form: {
        consNo: "",
        consName: "",
        addr: "",
      },
      form2: {
        mrSectNo: "",
      },
      prcListParams:{
        pageNo: 1,
        pageSize: 999,
        releaseFlag: 1,
        orgNoList:
          this.$store.state.app.prcSupport === "Y" ? this.rowInfo.orgNo : null
      },
      tableData1: [],
      tableData2: [],
      prcList: [],
      mtrTypeCdList: [], //水表型号
      meterTypeList: [], //水表类型
      mfgCdList: [], //生产厂家
      sectionList: [], // 抄表册
      startBusinessFlow: this.$store.state.app.startBusinessFlow,
      sectNo: undefined
    };
  },
  computed: {},
  watch: {
    "rowInfo": {
      handler(newVal) {
        console.log("行内数据rowInfo", newVal);
        if (newVal.processInstanceId) {
          //获取用户档案编辑、详情
          this.businessFlowParaQueryByProcessId()
        }
      },
      deep: true,
      immediate: true
    }
  },
  //方法集合
  methods: {
    //获取抄表册列表
    async getSectionList() {
      let params = {
        pageNum: 1,
        pageSize: 999,
        // orgNoList: [this.rowInfo.orgNo]
      };
      sectionList(params).then((res) => {
        this.sectionList = res.data;
        this.form2.mrSectNo = this.sectNo;
      });
    },
    //生产厂家
    CRM_Mfgcd(row) {
      return this.selectDictLabel(this.mfgCdList, row.manufacturer);
    },
    //水表类型
    CRM_MeterType(row) {
      return this.selectDictLabel(this.meterTypeList, row.meterType);
    },
    changePrc(val,row){
      this.prcList.forEach((item) => {
        if (item.prcCode == val) {
          console.log(item);
          row.prcName = item.catPrcName;
        }
      })
    },
    //更改表具型号
    changeMtrTypeCd(val, row) {
      this.mtrTypeCdList.forEach((item) => {
        if (item.mtrTypeCd == val) {
          console.log(item);
          row.manufacturer = item.manufacturer;
          row.meterType = item.meterType;
        }
      })
    },
    handleClose() {
      this.$emit("closeDialog", false);
      this.$emit("refresh");
    },

    //获取用户档案详情
    async getPremiseDetail(val) {
      let params = {
        consId: val
      }
      await premiseDetail(params).then(res => {
        this.userInfo = res.data.premise;
      })
    },
    async businessFlowParaQueryByProcessId() {
      let params = {
        processInstId: this.rowInfo.processInstanceId
      }
      await businessFlowParaQueryByProcessId(params).then(res => {
        let data = JSON.parse(res.data.paramDetail);
        this.tableData1 = data.downMtrList;
        this.tableData2 = data.upMtrList;
        this.sectNo = data.mrSectNo;
        this.getPremiseDetail(data.consId);
      })
    },
    //查询水价
    getPrcList(){
      prcList(this.prcListParams).then((response) => {
        this.prcList = response.data;
      })
    },
    //获取水表类型
    getMtrTypeCdList(){
      let params = {
        // selectOrgNo: this.queryParams.orgNo,
        pageNum: 1,
        pageSize: 999
      }
      mtrTypeCdList(params).then((response) => {
        this.mtrTypeCdList = response.data;
      })
    },

    userAddMeter(rowIndex) {
      this.tableData2.push({
        meterId: "",
        thisRead: "",
        inflowDirection: "",
        priceValue: {},
        mainRunMeterId: "",
      });
    },
    userDelMeter(rowIndex) {
      if (this.tableData2.length == 1) {
        return;
      }
      let array = this.tableData2.filter((data, index) => index != rowIndex);
      this.tableData2 = array;
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  async created() {
    await this.getSectionList();
    //生产厂家
    this.getDicts("crm_mfgcd").then((response) => {
      this.mfgCdList = response.data;
    });
    //表具类型
    this.getDicts("crm_meter_type").then((response) => {
      this.meterTypeList = response.data;
    });

    // //获取用户档案编辑、详情
    // await this.getPremiseDetail()
    //获取水价
    this.getPrcList();
    //获取表具型号
    this.getMtrTypeCdList();

    //用户表具列表table
    this.tableData2.push({
      meterId: "",
      thisRead: "",
      inflowDirection: "",
      priceValue: {},
      mainRunMeterId: "",
    });
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
</style>