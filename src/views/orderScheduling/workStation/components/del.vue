<template>
  <div class="app-container">
      <el-row style="margin-top: 10px; margin-bottom: 10px">
        <LineTitle title="受理信息" fontSize="20"/>
      </el-row>

      <el-form
          :model="form1"
          ref="queryForm"
          label-width="85px"
          :inline="true"
          :rules="rule1"
          size="mini">
        <el-row :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="业务类型">
              <el-input
                  suffix-icon="x"
                  disabled
                  value="销户"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="受理人员">
              <el-input
                  suffix-icon="x"
                  disabled
                  v-model="form1.userName"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="受理时间">
              <el-input
                  suffix-icon="x"
                  disabled
                  v-model="form1.createTime"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="受理渠道">
              <el-input
                  suffix-icon="x"
                  disabled
                  value="柜台"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="受理说明" prop="remark">
              <el-input
                  suffix-icon="x"
                  v-model="form1.remark"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row style="margin-top: 10px; margin-bottom: 10px">
        <LineTitle title="用户信息" fontSize="20"/>
      </el-row>

      <el-form
          :model="form2"
          ref="queryForm"
          label-width="85px"
          :inline="true"
          :rules="rule2"
          size="mini">
        <el-row  :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="用户编号" prop="consNo">
              <el-input
                  suffix-icon="x"
                  v-model="form2.consNo"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="用户名称" prop="consName">
              <el-input
                  suffix-icon="x"
                  v-model="form2.consName"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="手机号码" prop="mobile">
              <el-input
                  suffix-icon="x"
                  v-model="form2.mobile"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row  :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="营业所" prop="orgName">
              <el-input
                  suffix-icon="x"
                  v-model="form2.orgName"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="地址" prop="addr">
              <el-input
                  suffix-icon="x"
                  v-model="form2.addr"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="证件" prop="certificate">
              <el-input
                  suffix-icon="x"
                  v-model="form2.certificate"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="用户标签" prop="consTag">
              <el-select
                  v-model="form2.consTag"
                  placeholder="请选择"
                  filterable
                  clearable
                  disabled
              >
                <el-option
                    v-for="dict in consTagDict"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="账户余额" prop="balance">
              <el-input
                  suffix-icon="x"
                  v-model="form2.balance"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="账户欠费" prop="oweAmt">
              <el-input
                  suffix-icon="x"
                  v-model="form2.oweAmt"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="最近出账日期" prop="billDate">
              <el-input
                  suffix-icon="x"
                  v-model="form2.billDate"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="用户备注" prop="remark">
              <el-input
                  suffix-icon="x"
                  v-model="form2.remark"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
  </div>
</template>

<script>
import {queryDelPremiseInfo} from "@/api/userManage/userManage";
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import {businessFlowParaQueryByProcessId} from "@/api/orderScheduling/workStation";

export default {
  name: "del",
  components: { LineTitle },
  props: {
    rowInfo: {
      type: Object,
      default: () => {}
    },
    businessCodeObj: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      businessCode: "delUser",
      consTagDict: [],
      form1: {},
      form2: {},
      rule1: {
        remark: [
          {required: true, message: "请输入受理说明", trigger: "blur"}
        ],
      },
      rule2: {
        consNo: [
          {required: true, message: "请输入户号", trigger: "blur"}
        ],
      }
    }
  },
  computed: {},
  watch: {
    "rowInfo": {
      handler(newVal) {
        console.log("行内数据rowInfo", this.rowInfo);
        console.log("行内数据tag", newVal.processInstanceId != undefined);
        if (newVal.processInstanceId) {
          console.log("调用接口");
          this.getBusinessFlowParaQueryByProcessId();
        }
      },
      deep:true,
      immediate:true
    },
  },
  methods: {
    //查询销户信息
    async queryDelPremiseInfo(val) {
      queryDelPremiseInfo({
        consNo: val
          }
      ).then((response) => {
        this.form2 = response.data;
      });
    },
    async getBusinessFlowParaQueryByProcessId() {
      let params = {
        processInstId: this.rowInfo.processInstanceId
      }
      console.log("接口入参", params);
      businessFlowParaQueryByProcessId(params).then(res => {
        this.queryParams = JSON.parse(res.data.paramDetail)
        console.log('[ this.queryParams ] >', this.queryParams)
        this.form1.userName = this.queryParams.operatorName;
        this.form1.createTime = res.data.createTime;
        this.form1.remark = res.data.remark;
        this.queryDelPremiseInfo(res.data.consNo);
      })
    },
  },
  created() {
    //用户标签
    this.getDicts("crm_constag").then(response => {
      this.consTagDict = response.data;
    });
  },

}
</script>

<style lang="less" scoped>
//@import url(); 引入公共css类
</style>