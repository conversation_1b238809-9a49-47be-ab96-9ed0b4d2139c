<template>
  <div>
    <GridTable
      :columns="columns"
      :tableData="tableData"
      :currentPage.sync="queryParams.pageNum"
      :pageSize.sync="queryParams.pageSize"
      :total.sync="pageTotal"
      @changePage="changePage"
      :loading="loading"
      :tableId="tableId"
    >
      <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
        <el-button type="primary" size="mini" @click.stop="createOrder"
          >创建工单
        </el-button>
      </template>
      <template slot="operation" slot-scope="{ row }">
        <el-button @click="toView(row)" size="mini" type="text">查看</el-button>
        <el-button @click="assignAgain(row)" size="mini" type="text"
          >改派</el-button
        >
        <el-button @click="handleOrder(row)" size="mini" type="text"
          >处理</el-button
        >
      </template>
    </GridTable>

    <!--工单查看-->
    <OrderInfo
      v-if="drawerVisible"
      :drawer="drawerVisible"
      :orderItem="drawerOrder"
      @closeDrawer="closeDrawer"
    />
  </div>
</template>
<script>
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import { listToDeal, transferOrder } from "@/api/orderScheduling/workStation";
import moment from "moment";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  props: ["queryForm"],
  components: { OrderInfo, GridTable, AdvancedForm },
  mounted() {
    this.handleQuery();
  },
  data() {
    return {
      loading: true,
      pageInitQuery: false,
      queryParams: {},
      pageTotal: 0,
      tableData: [],
      drawerVisible: false,
      drawerOrder: {},
      tableId: "holdTable",
      columns: Object.freeze([
        {
          field: "businessNo",
          title: "工单编号",
        },
        {
          field: "flowTypeLabel",
          title: "工单类型",
        },
        {
          field: "taskName",
          title: "处理进度",
        },
        {
          field: "taskAssignee",
          title: "当前处理人",
          showOverflowTooltip: true,
        },
        {
          field: "emergencyLevel",
          title: "紧急程度",
          showOverflowTooltip: true,
        },
        {
          field: "flowRemark",
          title: "工单说明",
          showOverflowTooltip: true,
        },
        {
          field: "createTimeValue",
          title: "创建时间",
          showOverflowTooltip: true,
        },
        {
          field: "creator",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ]),
    };
  },
  methods: {
    initParams() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      this.queryParams = {
        flowSource:
          location.href.indexOf("zaixian") == -1
            ? location.href.indexOf("order/workStation") == -1
              ? "hotLine"
              : ""
            : "onLine",
        ...parentQueryForm,
        // 开始时间
        beginTime:
          parentQueryForm.daterange.length > 0
            ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") +
              " 00:00:00"
            : null,
        // 结束时间
        endTime:
          parentQueryForm.daterange.length > 0
            ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") +
              " 23:59:59"
            : null,
        pageNum: 1,
        pageSize: 10,
      };
    },
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      this.initParams();
      this.getList();
    },
    getList() {
      const { daterange, flowStatus, operatorId, ...args } = this.queryParams;

      listToDeal(args)
        .then((response) => {
          this.tableData = response.data;
          this.pageTotal = response.total;
          this.loading = false;
          this.tableData.forEach((element) => {
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    orderCheckIn(row) {
      this.$confirm("请确认是否签入?", "签入", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: "提交中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        let param = {
          flowId: row.flowId,
          processInstanceId: row.processInstanceId,
          taskId: row.taskId,
          transferType: "1",
        };
        transferOrder(param)
          .then(() => {
            loading.close();
            this.$message.success("签入成功");
            this.handleQuery();
          })
          .catch(() => {
            loading.close();
          }).finally(()=>{
          loading.close();
        });
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    },
    createOrder() {
      this.$emit("createOrder");
    },
    //分页切换
    changePage() {
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
</style>
