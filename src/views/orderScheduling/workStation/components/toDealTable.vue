<template>
  <div>
    <GridTable
      :columns="columns"
      :tableData="tableData"
      :currentPage.sync="queryParams.pageNum"
      :pageSize.sync="queryParams.pageSize"
      :total.sync="pageTotal"
      @changePage="changePage"
      :loading="loading"
      :tableId="tableId"
    >
      <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
        <el-button type="primary" size="mini" @click.stop="createOrder"
          >创建工单
        </el-button>
      </template>
      <template slot="operation" slot-scope="{ row }">
        <el-button @click="toView(row)" size="mini" type="text">查看</el-button>
        <el-button @click="assignAgain(row)" size="mini" type="text"
          >改派</el-button
        >
        <el-button @click="handleOrder(row)" size="mini" type="text"
          >处理</el-button
        >
      </template>
    </GridTable>

    <!--工单查看-->
    <OrderInfo
      v-if="drawerVisible"
      :drawer="drawerVisible"
      :orderItem="drawerOrder"
      @closeDrawer="closeDrawer"
    />

    <!-- 工单处理对话框 -->
    <el-dialog
      title="工单处理"
      :visible.sync="showHandleOrderDialog"
      @close="closeHandleOrderDialog"
      :close-on-click-modal="false"
      width="1000px"
      :show-close="false"
    >
      <HandleOrder
        v-if="showHandleOrderDialog"
        :rowData="rowData"
        :isApproval="isApproval"
        @cancelDialog="closeHandleOrderDialog"
        @freshTable="handleQuery"
      />
    </el-dialog>
    <!-- 工单处理对话框 -->
    <el-dialog
      title="工单处理"
      :visible.sync="showHandleOrderNewDialog"
      @close="closeHandleOrderDialog"
      :close-on-click-modal="false"
      width="1000px"
      :show-close="false"
    >
      <HandleOrderNew
        v-if="showHandleOrderNewDialog"
        :rowData="rowData"
        :isApproval="isApproval"
        @cancelDialog="closeHandleOrderDialog"
        @freshTable="handleQuery"
      />
    </el-dialog>

    <!-- 改派确认框 -->
    <el-dialog
      title="改派确认"
      :visible.sync="assignCommit.visible"
      @close="closeAssignCommitDialog"
      :close-on-click-modal="false"
      width="500px"
      append-to-body
    >
      <el-form
        :model="assignCommit.assignForm"
        ref="assignForm"
        :inline="true"
        label-width="80px"
        :rules="assignCommit.assignFormRules"
      >
        <el-row>
          <el-col>
            <el-form-item label="处理人" prop="assignUser">
              <el-select
                filterable
                v-model="assignCommit.assignForm.assignUser"
                placeholder="请选择处理人"
              >
                <el-option
                  v-for="item in assignCommit.assignUserList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button type="primary" @click="handleAssignUser">确 定</el-button>
        <el-button @click="closeAssignCommitDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import {
  listTodo,
  queryByProcessInstId,
  transferOrder,
} from "@/api/orderScheduling/workStation";
import HandleOrder from "./handleOrder";
import HandleOrderNew from "./handleOrderNew";
import { getUserAvailUsers } from "@/api/process/lfDesign";
import moment from "moment";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";

export default {
  props: ["queryForm"],
  components: {
    OrderInfo,
    HandleOrder,
    HandleOrderNew,
    GridTable,
    AdvancedForm,
  },
  mounted() {
    this.handleQuery();
    getUserAvailUsers({
      pageNum: 1,
      pageSize: 9999,
    }).then((response) => {
      this.assignCommit.assignUserList = response.data;
    });
  },
  data() {
    return {
      loading: true,
      pageInitQuery: false,
      queryParams: {},
      pageTotal: 0,
      tableData: [],
      drawerVisible: false,
      showHandleOrderNewDialog: false, //显示工单处理dialog
      showHandleOrderDialog: false, //显示工单处理dialog
      drawerOrder: {},
      rowData: {},
      assignRowData: {},
      assignCommit: {
        visible: false,
        assignUserList: [],
        assignForm: {
          assignUser: "",
        },
        assignFormRules: {
          assignUser: [
            { required: true, message: "请选择处理人", trigger: "change" },
          ],
        },
      },
      businessCodeObj: {},
      isApproval: true,
      tableId: "toDealTable",
      columns: Object.freeze([
        {
          field: "businessNo",
          title: "工单编号",
        },
        {
          field: "flowTypeLabel",
          title: "工单类型",
        },
        {
          field: "taskName",
          title: "处理进度",
        },
        {
          field: "taskAssignee",
          title: "当前处理人",
          showOverflowTooltip: true,
        },
        {
          field: "emergencyLevel",
          title: "紧急程度",
          showOverflowTooltip: true,
        },
        {
          field: "flowRemark",
          title: "工单说明",
          showOverflowTooltip: true,
        },
        {
          field: "createTimeValue",
          title: "创建时间",
          showOverflowTooltip: true,
        },
        {
          field: "creator",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ]),
    };
  },
  methods: {
    initParams() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      this.queryParams = {
        flowSource:
          location.href.indexOf("zaixian") == -1
            ? location.href.indexOf("order/workStation") == -1
              ? "hotLine"
              : ""
            : "onLine",
        ...parentQueryForm,
        flowStatus: "1",
        // 开始时间
        beginTime:
          parentQueryForm.daterange.length > 0
            ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") +
              " 00:00:00"
            : null,
        // 结束时间
        endTime:
          parentQueryForm.daterange.length > 0
            ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") +
              " 23:59:59"
            : null,
        pageNum: 1,
        pageSize: 10,
      };
    },
    async queryByProcessInstId(row) {
      let params = {
        processInstId: row.processInstanceId,
      };
      await queryByProcessInstId(params).then((res) => {
        this.businessCodeObj = res.data;
        console.log("[ businessCodeObj 11111] >", this.businessCodeObj);
        // addUser	开户
        // cgMtr	换表
        // delUser	销户
        // cgPrc	水价变更
        // cgBottom	表底变更
        // cgBill	账单调整
        // refund	线上缴费退费
        // reMtrFee	退表退费
      });
    },
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      this.initParams();
      this.getList();
    },
    getList() {
      const { daterange, flowStatus, operatorId, ...args } = this.queryParams;
      listTodo(args)
        .then((response) => {
          this.tableData = response.data;
          this.pageTotal = response.total;
          this.loading = false;
          this.tableData.forEach((element) => {
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    assignAgain(row) {
      this.assignRowData = row;
      this.assignCommit.visible = true;
    },
    async handleOrder(row) {
      this.showHandleOrderDialog = true;
      this.rowData = row;
    },
    closeHandleOrderDialog() {
      this.showHandleOrderNewDialog = false;
      this.showHandleOrderDialog = false;
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    },
    handleAssignUser() {
      this.$refs.assignForm.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "提交中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let param = {
            flowId: this.assignRowData.flowId,
            processInstanceId: this.assignRowData.processInstanceId,
            taskId: this.assignRowData.taskId,
            targetUser: this.assignCommit.assignForm.assignUser,
            transferType: "2",
          };
          transferOrder(param)
            .then(() => {
              loading.close();
              this.$message.success("改派成功");
              this.closeAssignCommitDialog();
              this.handleQuery();
            })
            .catch(() => {
              loading.close();
            }).finally(()=>{
            loading.close();
          });
        }
      });
    },
    closeAssignCommitDialog() {
      this.assignCommit.visible = false;
      this.assignRowData = {};
    },
    createOrder() {
      this.$emit("createOrder");
    },
    //分页切换
    changePage() {
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
</style>
