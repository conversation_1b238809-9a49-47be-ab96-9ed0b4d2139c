<template>
  <div class="app-container">
    <div class="flex-box" v-if="adjustForm.meteringType === '1'">
      <div class="line"></div>
      <span class="font">调整抄表费用</span>
    </div>
    <el-form
      ref="adjustFormRef"
      :model="adjustForm"
      :inline="true"
      label-width="110px"
      :rules="rules"
    >
      <el-table
        :data="adjustForm.meterInfoVOs"
        v-if="adjustForm.meteringType === '1'"
        border
      >
        <el-table-column
          label="表具编号"
          align="center"
          prop="madeNo"
          width="150"
        />
        <el-table-column
          label="原价格"
          align="center"
          prop="oriPrcCode"
          width="150"
          :formatter="formatPrice"
        />
        <el-table-column
          label="调整价格"
          align="center"
          prop="adjPrcCode"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`meterInfoVOs[${scope.$index}].adjPrcCode`"
              :rules="rules.adjPrcCode"
            >
              <el-select
                filterable
                v-model="scope.row.adjPrcCode"
                placeholder="请选择"
                clearable
                disabled
              >
                <el-option
                  v-for="(item, index) in prcList"
                  :key="index"
                  :label="item.catPrcName"
                  :value="item.prcCode"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          label="原上次读数"
          align="center"
          prop="oriLastMrNum"
          width="150"
        />
        <el-table-column
          label="调整上次读数"
          align="center"
          prop="adjLastMrNum"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`meterInfoVOs[${scope.$index}].adjLastMrNum`"
              :rules="rules.adjLastMrNum"
            >
              <el-input-number
                :precision="2"
                :step="0.1"
                :max="99999"
                :min="0"
                v-model="scope.row.adjLastMrNum"
                placeholder="请输入"
                size="mini"
                @blur="checkData(scope.row)"
                style="width: 80px"
                :controls="false"
                disabled
              >
              </el-input-number>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          label="原本次读数"
          align="center"
          prop="oriThisReadNum"
          width="150"
        />
        <el-table-column
          label="调整本次读数"
          align="center"
          prop="adjThisReadNum"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`meterInfoVOs[${scope.$index}].adjThisReadNum`"
              :rules="rules.adjThisReadNum"
            >
              <el-input-number
                :precision="2"
                :step="0.1"
                :max="99999"
                :min="0"
                v-model="scope.row.adjThisReadNum"
                placeholder="请输入"
                size="mini"
                style="width: 80px"
                @blur="checkData(scope.row)"
                :controls="false"
                disabled
              >
              </el-input-number>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          label="原用量"
          align="center"
          prop="oriPq"
          width="150"
        />
        <el-table-column
          label="调整后用量"
          align="center"
          prop="adjPq"
          width="150"
        />
      </el-table>
    </el-form>

    <div class="flex-box" v-if="adjustForm.meteringType !== '1'">
      <div class="line"></div>
      <span class="font">调整用户费用</span>
    </div>
    <el-form
      ref="adjustFormRef"
      :model="adjustForm"
      :inline="true"
      label-width="110px"
      :rules="rules"
    >
      <el-table
        :data="adjustForm.noMeterInfoVOs"
        v-if="adjustForm.meteringType !== '1'"
        border
      >
        <el-table-column
          v-if="adjustForm.meteringType === '3'"
          label="原人口数"
          align="center"
          prop="oriPop"
          width="150"
        />
        <el-table-column
          v-if="adjustForm.meteringType === '3'"
          label="调整后人口数"
          align="center"
          prop="adjPop"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`noMeterInfoVOs[${scope.$index}].adjPop`"
              :rules="rules.adjPop"
            >
              <el-input-number
                :precision="2"
                :step="1"
                :max="99999"
                :min="0"
                v-model="scope.row.adjPop"
                placeholder="请输入"
                size="mini"
                style="width: 80px"
                :controls="false"
                disabled
              >
              </el-input-number>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          v-if="adjustForm.meteringType === '3'"
          label="原每人水量"
          align="center"
          prop="oriCalcPopPq"
          width="150"
        />
        <el-table-column
          v-if="adjustForm.meteringType === '3'"
          label="调整后每人水量"
          align="center"
          prop="adjCalcPopPq"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`noMeterInfoVOs[${scope.$index}].adjCalcPopPq`"
              :rules="rules.adjCalcPopPq"
            >
              <el-input-number
                :precision="2"
                :step="0.1"
                :max="99999"
                :min="0"
                v-model="scope.row.adjCalcPopPq"
                placeholder="请输入"
                size="mini"
                style="width: 80px"
                :controls="false"
                disabled
              >
              </el-input-number>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          label="原价格"
          align="center"
          prop="oriPrcCode"
          width="150"
          :formatter="formatPrice"
        />
        <el-table-column
          label="调整价格"
          align="center"
          prop="adjPrcCode"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`noMeterInfoVOs[${scope.$index}].adjPrcCode`"
              :rules="rules.adjPrcCode"
            >
              <el-select
                filterable
                v-model="scope.row.adjPrcCode"
                placeholder="请选择"
                clearable
                disabled
              >
                <el-option
                  v-for="(item, index) in prcList"
                  :key="index"
                  :label="item.catPrcName"
                  :value="item.prcCode"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          v-if="adjustForm.meteringType === '2'"
          label="原用量"
          align="center"
          prop="oriPq"
          width="150"
        />
        <el-table-column
          v-if="adjustForm.meteringType === '2'"
          label="调整用量"
          align="center"
          prop="adjPq"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="`noMeterInfoVOs[${scope.$index}].adjPq`"
              :rules="rules.adjPq"
            >
              <el-input-number
                :precision="2"
                :step="0.1"
                :max="99999"
                :min="0"
                v-model="scope.row.adjPq"
                placeholder="请输入"
                size="mini"
                style="width: 80px"
                :controls="false"
                disabled
              >
              </el-input-number>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>

    <div class="flex-box">
      <div class="line"></div>
      <span class="font">调整用户费用</span>
    </div>
    <el-form
      ref="adjustFormRef"
      :model="adjustForm"
      :inline="true"
      label-width="110px"
      :rules="rules"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="原账单金额" prop="madeNo">
            {{ adjustForm.oriBillAmt }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="原违约金" prop="madeNo">
            {{ adjustForm.oriLateFee }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="调整前余额" prop="madeNo">
            {{ adjustForm.oriBalance }}
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="调整人" prop="madeNo">
            {{ adjustForm.adjEmp }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="调整后账单金额" prop="madeNo">-->
        <!--            {{ adjustForm.afterBillAmt }}-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="调整后违约金" prop="madeNo">-->
        <!--            {{ adjustForm.afterPenalty }}-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="调整后余额" prop="madeNo">-->
        <!--            {{ adjustForm.afterBalance }}-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="24">
          <el-form-item label="工单发起时间" prop="madeNo">
            {{ adjustForm.adjustTime }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="调整说明" prop="remark">
            <el-input
              v-model="adjustForm.remark"
              placeholder="请输入调整说明"
              type="textarea"
              clearable
              disabled
              @keyup.enter.native="confirmAdjust"
              :rows="5"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { prcList } from "@/api/userManage/userManage";
import { businessFlowParaQueryById } from "@/api/orderScheduling/workStation";

export default {
  async created() {
    await this.getDicts("ch_amt_code").then((response) => {
      this.amtCodeOptions = response.data;
    });
    this.getPrcList();
  },
  data() {
    return {
      flag: false,
      businessCode: "cgBill",
      // 存放调整数据
      adjustForm: {},
      priceType: "",
      billDetailForm: {},
      amtCodeOptions: [],
      settleFlagOptions: [],
      queryParams: {},
      // 价格列表
      prcList: [],
      rules: {
        remark: [
          { required: true, message: "请输入调整说明", trigger: "blur" },
        ],
        adjPrcCode: [
          { required: true, message: "请选择调整价格", trigger: "change" },
        ],
        adjLastMrNum: [
          { required: true, message: "请输入调整上次读数", trigger: "blur" },
        ],
        adjThisReadNum: [
          { required: true, message: "请输入调整本次读数", trigger: "blur" },
        ],
        adjPop: [
          { required: true, message: "请输入调整人口数", trigger: "blur" },
        ],
        adjCalcPopPq: [
          { required: true, message: "请输入调整每人水量", trigger: "blur" },
        ],
        adjPq: [{ required: true, message: "请输入调整用量", trigger: "blur" }],
      },
    };
  },
  props: {
    rowInfo: {
      type: Object,
      default: () => {},
    },
    businessCodeObj: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    "rowInfo.ecBusinessFlowParaId": {
      handler(newVal) {
        if (newVal) {
          this.getbusinessFlowParaQueryById();
        }
      },
      immediate: true,
    },
  },
  methods: {
    async getbusinessFlowParaQueryById() {
      let params = {
        id: this.rowInfo.ecBusinessFlowParaId,
      };
      businessFlowParaQueryById(params).then((res) => {
        this.queryParams = JSON.parse(res.data.paramDetail);
        console.log("[ this.queryParams ] >", this.queryParams);
        this.adjustForm = this.queryParams;
        this.adjustForm.meteringType = this.queryParams.adjMode;
        this.adjustForm.adjustTime = res.data.createTime;
      });
    },
    /** 获取水价信息 */
    getPrcList() {
      let params = {
        releaseFlag: 1, //只查询生效的水价
        pageNo: 1,
        pageSize: 999,
      };
      prcList(params).then((response) => {
        if (this.priceType) {
          this.prcList = response.data.filter(
            (i) => i.priceType === this.priceType
          );
        } else {
          this.prcList = response.data;
        }
      });
    },
    formatPrice(row) {
      if (row.oriPrcCode) {
        return this.prcList
          .filter((i) => i.prcCode === row.oriPrcCode)
          .map((i) => i.catPrcName);
      }
    },
    amtCodeFormat(val) {
      return this.selectDictLabel(this.amtCodeOptions, val);
    },
  },
};
</script>

<style lang="less" scoped>
.app-container {
  /deep/ .el-input-group--append {
    display: flex !important;
  }

  /deep/ .el-input-group__append {
    padding: 0;
    width: 50px;
    display: flex;
    justify-content: center;
  }

  .cateWrap {
    display: flex;
    padding: 0 20px;
    flex-wrap: wrap;
    // justify-content: space-between;
    border-bottom: 1px solid #ccc;
  }

  .category {
    width: 25%;
    font-size: 14px;
    height: 33px;
    margin-bottom: 10px;
    font-weight: normal;
    display: flex;

    .cateLabel {
      width: 110px;
      padding-right: 20px;
      display: flex;
      text-align: right;
      align-items: center;
      justify-content: flex-end;
      position: relative;

      &::after {
        position: absolute;
        content: ":";
        right: 9px;
      }
    }

    .special .el-form-item {
      margin-bottom: 20px;
    }

    .cateNum {
      display: flex;
      align-items: center;
      word-break: break-all;
      overflow: hidden;
      flex: 1;

      & > div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}

.bill /deep/ .el-input--small .el-input__inner {
  width: 170px;
  text-align: left;
}

.bill /deep/ .el-form--inline .el-form-item__label {
  width: 90px;
}

.bill .customWidth /deep/ .el-form-item__label {
  width: 120px;
}

.bill /deep/ .el-input.is-disabled .el-input__inner {
  color: #606266;
}

.bill .customWidth /deep/ .el-input__inner {
  width: 140px;
}

.bill .pqCal /deep/ .el-input__inner {
  width: 120px;
}

.bill .autoWidth /deep/ .el-textarea__inner {
  width: 300px;
}

.line {
  display: inline-block;
  width: 5px;
  height: 20px;
  margin-right: 10px;
  background: rgba(0, 121, 254, 1);
}

.font {
  font-family: "MicrosoftYaHei-Bold", "微软雅黑 Bold", "微软雅黑", sans-serif;
  font-weight: 700;
  font-style: normal;
  font-size: 16px;
  text-align: left;
}

.flex-box {
  display: flex;
  align-items: center;
  height: 20px;
  margin: 20px 0;
}
</style>
