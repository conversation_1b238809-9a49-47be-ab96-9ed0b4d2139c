<template>
  <div class="app-container">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="88px"
      style="max-height: 650px;overflow: hidden;overflow-y: auto;padding-bottom: 2em"
    >
      <el-divider content-position="left">
        工单信息
      </el-divider>

      <div class="wrap-top">
        <div class="work-type-form">
          <el-form-item label="工单类型：" prop="flowType">
            <el-select
              v-model="formData.flowType"
              placeholder="请选择工单类型"
              style="width: 140px;margin-right: 32px;"
              @change="flowTypeChange"
            >
              <el-option
                v-for="item in flowTypeOptions"
                :key="item.typeId"
                :label="item.typeLabel"
                :value="item.typeCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="紧急程度：" prop="emergencyLevel">
            <el-select
              v-model="formData.emergencyLevel"
              placeholder="请选择紧急程度"
              style="width: 140px;"
            >
              <el-option
                v-for="item in urgentLevelOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="计划耗时：">
            <el-input-number
              :controls="false"
              style="width:100px"
              v-model="formExtraData.planTimeInput"
              :min="1"
              :max="365"
            >
            </el-input-number>
            <el-select
              style="width:100px"
              v-model="formExtraData.planTimeOptVal"
            >
              <el-option
                v-for="item in formExtraData.planTimeOpt"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="工单说明：" prop="remark">
            <el-input
              type="textarea"
              v-model="formData.remark"
              placeholder="请输入工单说明"
              :rows="5"
              style="width: 400px;"
            />
          </el-form-item>
        </div>
        <work-flow title="工单流程" :workFlowList="workFlowList" />
      </div>

      <el-divider
        v-if="fillInForm"
        content-position="left"
        style="margin-top: -10px;"
      >
        填写表单
      </el-divider>
      <form-create
        :rule="formCreateRule"
        v-model="formDataCreate"
        :option="formCreateOption"
      />
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: right">
      <el-button
        v-if="formDataCreateShow"
        type="primary"
        @click="createWorkOrder"
        :disabled="this.formData.flowId == null"
        >确 定</el-button
      >
      <el-button @click="cancelDialog">取 消</el-button>
    </div>
  </div>
</template>
<script>
import WorkFlow from "./workFlow";
import {
  getNodeInfos,
  getProcessStartInfo,
  startWithForm,
  taskFormsFirstDeal,
  workOrderTypeList,
} from "@/api/orderScheduling/workStation";

export default {
  components: { WorkFlow },
  props: {
    flowSource: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      formData: {
        flowId: undefined,
        flowKey: undefined,
        businessKey: undefined,
        flowType: "", //工单类型
        emergencyLevel: "", //紧急程度
        remark: "", //工单说明
      },
      formExtraData: {
        planTimeInput: undefined,
        planTimeOptVal: 1,
        planTimeOpt: [
          { label: "分钟", value: 1 },
          { label: "小时", value: 2 },
          { label: "天", value: 3 },
        ],
      },
      formDataCreateShow: true,
      formDataCreate: {},
      rules: {
        flowType: [
          { required: true, message: "请选择工单类型", trigger: "change" },
        ],
        emergencyLevel: [
          { required: true, message: "请选择紧急程度", trigger: "change" },
        ],
      },
      formCreateRule: [],
      formCreateOption: {},
      flowTypeOptions: [],
      urgentLevelOptions: [],
      workFlowList: [],
      formResponse: {},
      fillInForm: false,
    };
  },
  mounted() {
    this.urgentLevelOptions = [
      { id: "0", label: "不紧急" },
      { id: "1", label: "正常" },
      { id: "2", label: "紧急" },
      {
        id: "3",
        label: "非常紧急",
      },
    ];
    workOrderTypeList({ typeStatus: "1" }).then((response) => {
      this.flowTypeOptions = response.data;
    });
  },
  methods: {
    //工单类型选中事件
    async flowTypeChange(value) {
      await getProcessStartInfo({ typeCode: value }).then((response) => {
        if (response.data && response.data.flowKey) {
          const param = {
            flowId: response.data.flowId,
            flowKey: response.data.flowKey,
            deployId: response.data.deployId,
          };
          this.formData.flowKey = param.flowKey;
          this.formData.flowId = param.flowId;
          getNodeInfos(param).then((response) => {
            if (!!response.data) {
              if (response.data.length > 0) {
                this.workFlowList = response.data;
              }
            }
          });
          const loading = this.$loading({
            lock: true,
            text: "加载中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          //工单的表单信息
          taskFormsFirstDeal(param)
            .then((response) => {
              if (!!response.data) {
                this.fillInForm = true;
                this.formResponse = response.data;
                let options = JSON.parse(response.data.formConfig);
                options.onSubmit = () => {
                  this.createWorkOrder();
                };
                if (!!options.submitBtn) {
                  this.formDataCreateShow = false;
                }
                this.formCreateOption = options;
                this.formCreateRule = JSON.parse(response.data.formJson);
                this.formCreateRule.map((item) => {
                  if (item.type == "upload") {
                    if (
                      item.props?.uploadType == "file" ||
                      item.props?.uploadType == "image"
                    ) {
                    } else {
                      this.$set(item.props, "uploadType", "image");
                    }
                    this.$set(item.props, "onSuccess", function(
                      res,
                      file,
                      fileList
                    ) {
                      file.url = res.data;
                      console.log("file", file);
                    });
                  }
                });
              } else {
                this.formCreateRule = [];
                this.formCreateOption = {};
                this.fillInForm = false;
                this.formResponse = undefined;
                this.formDataCreateShow = true;
                this.formDataCreate = {};
              }
              loading.close();
            })
            .catch(() => {
              loading.close();
            });
        } else {
          this.workFlowList = [];
          this.formCreateRule = [];
          this.formCreateOption = {};
          this.fillInForm = false;
          this.formResponse = undefined;
          this.formDataCreateShow = true;
          this.formDataCreate = {};
          this.$message.warning("未配置工单对应的流程或流程未发布");
        }
      });
    },
    /**
     * 提交创建工单
     */
    createWorkOrder() {
      //form参数赋值
      if (this.formResponse && !!this.formResponse.formJson) {
        this.formResponse.formJson = this.formCreateRule;
      }
      //表单参数校验
      if (this.formDataCreate && !!this.formDataCreate.submit) {
        this.formDataCreate.submit(() => {
          this.commitData();
        });
      } else {
        this.commitData();
      }
    },
    commitData() {
      // 计划时间处理
      this.calculatePlanTimeSIfNecessary();
      this.$refs.form.validate((valid) => {
        if (valid) {
          const param = {
            ...this.formData,
            preDealForms: this.formResponse,
            flowSource: this.flowSource,
          };
          const loading = this.$loading({
            lock: true,
            text: "提交中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          startWithForm(param)
            .then(() => {
              this.$message.success("创建成功");
              loading.close();
              this.cancelDialog();
            })
            .catch(() => {
              loading.close();
            });
        } else {
          return false;
        }
      });
    },
    cancelDialog() {
      this.$emit("cancelDialog");
    },
    calculatePlanTimeSIfNecessary() {
      if (!this.formExtraData.planTimeInput) {
        if (this.formData.instExceptExeTime) {
          delete this.formData.instExceptExeTime;
        }
        return;
      }
      const val = this.formExtraData.planTimeInput;
      const opt = this.formExtraData.planTimeOptVal;
      let planSecs;
      if (opt === 1) {
        planSecs = val * 60;
      } else if (opt === 2) {
        planSecs = val * 60 * 60;
      } else if (opt === 3) {
        planSecs = val * 60 * 60 * 24;
      }
      this.formData.instExceptExeTime = planSecs;
    },
  },
};
</script>
<style scoped>
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.wrap-top {
  display: flex;
}

.work-type-form {
  flex: 1;
  margin-right: 20px;
  display: flex;
  flex-wrap: wrap;
}

/*el-form-item label和输入框垂直居中*/
.queryParamsWrap .el-form-item /deep/ .el-form-item__label {
  line-height: 32px;
}

/*去掉折叠组件边框*/
/deep/ .el-collapse {
  border: 0;
  padding: 5px 0;
}

/deep/ .el-collapse-item__header {
  height: 40px;
  border: 0;
  padding: 0 10px;
}

/deep/ .el-collapse-item__header .el-collapse-item__arrow {
  position: absolute;
  left: auto;
  right: 10px;
}

/*el-form-label 必填项红色星星显示在左侧*/
/deep/
  .el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label:before {
  content: "";
  display: none;
}

/deep/
  .el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label:after {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
  font-size: large;
  width: 5px;
}
</style>
