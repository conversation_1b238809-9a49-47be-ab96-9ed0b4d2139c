<template>
  <div>
    <el-table v-loading="loading" ref="multipleTable" :data="tableData">
      <el-table-column label="工单编号" prop="businessNo" />
      <el-table-column label="工单类型" prop="flowTypeLabel" />
      <el-table-column label="处理进度" prop="taskName" />
      <el-table-column label="当前处理人" prop="taskAssignee" :show-overflow-tooltip="true" />
      <el-table-column label="紧急程度" prop="emergencyLevel" :show-overflow-tooltip="true" />
      <el-table-column label="工单说明" prop="flowRemark" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" prop="createTime" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="creator" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="lastOperator" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button @click="toView(scope.row)" size="mini" type="text">查看</el-button>
          <el-button @click="assignAgain(scope.row)" size="mini" type="text">改派</el-button>
          <el-button @click="handleOrder(scope.row)" size="mini" type="text">处理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="pageTotal > 0" :total="pageTotal" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList" />

    <!--工单查看-->
    <OrderInfo v-if="drawerVisible" :drawer="drawerVisible" :orderItem="drawerOrder" @closeDrawer="closeDrawer" />

    <!-- 工单处理对话框 -->
    <el-dialog
      title="工单处理"
      :visible.sync="showHandleOrderDialog"
      @close="closeHandleOrderDialog"
      :close-on-click-modal="false"
      width="1000px"
      :show-close="false"
    >
      <HandleOrder v-if="showHandleOrderDialog" :rowData="rowData" :isApproval="isApproval"
                    @cancelDialog="closeHandleOrderDialog"
                    @freshTable="getList"
      />
    </el-dialog>
    <!-- 工单处理对话框 -->
    <el-dialog
      title="工单处理"
      :visible.sync="showHandleOrderNewDialog"
      @close="closeHandleOrderDialog"
      :close-on-click-modal="false"
      width="1000px"
      :show-close="false"
    >
      <HandleOrderNew v-if="showHandleOrderNewDialog" :rowData="rowData" :isApproval="isApproval"
                    @cancelDialog="closeHandleOrderDialog"
                    @freshTable="getList"
      />
    </el-dialog>


    <!-- 改派确认框 -->
    <el-dialog
      title="改派确认"
      :visible.sync="assignCommit.visible"
      @close="closeAssignCommitDialog"
      :close-on-click-modal="false"
      width="500px"
      append-to-body
    >
      <el-form
        :model="assignCommit.assignForm"
        ref="assignForm"
        :inline="true"
        label-width="80px"
        :rules="assignCommit.assignFormRules"
      >
        <el-row>
          <el-col>
            <el-form-item label="处理人" prop="assignUser">
              <el-select
                filterable
                v-model="assignCommit.assignForm.assignUser"
                placeholder="请选择处理人"
              >
                <el-option
                  v-for="item in assignCommit.assignUserList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button type="primary" @click="handleAssignUser">确 定</el-button>
        <el-button @click="closeAssignCommitDialog">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>
<script>
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import {listTodo, queryByProcessInstId, transferOrder} from "@/api/orderScheduling/workStation";
import HandleOrder from "./handleOrder";
import HandleOrderNew from "./handleOrderNew";
import { getUserAvailUsers } from "@/api/process/lfDesign";
import moment from "moment";

export default {
  props: ["queryForm"],
  components: { OrderInfo, HandleOrder, HandleOrderNew },
  mounted() {
    let parentQueryForm = this._.cloneDeep(this.queryForm);
    this.queryParams = {
      flowSource: (location.href.indexOf("zaixian") == -1) ? ((location.href.indexOf("order/workStation") == -1) ? "hotLine" : "") : "onLine",
      ...parentQueryForm,
      flowStatus: "1",
      // 开始时间
      beginTime:
        parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") + " 00:00:00" : null,
      // 结束时间
      endTime:
        parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") + " 23:59:59" : null,
      pageNum: 1,
      pageSize: 10
    };
    this.handleQuery();
    getUserAvailUsers({
      pageNum: 1,
      pageSize: 9999
    }).then(response => {
      this.assignCommit.assignUserList = response.data;
    });
  },
  data() {
    return {
      loading: true,
      pageInitQuery: false,
      queryParams: {},
      pageTotal: 0,
      tableData: [],
      drawerVisible: false,
      showHandleOrderNewDialog: false, //显示工单处理dialog
      showHandleOrderDialog: false, //显示工单处理dialog
      drawerOrder: {},
      rowData: {},
      assignRowData: {},
      assignCommit: {
        visible: false,
        assignUserList: [],
        assignForm: {
          assignUser: ""
        },
        assignFormRules: {
          assignUser: [{ required: true, message: "请选择处理人", trigger: "change" }]
        }
      },
      businessCodeObj: {},
      isApproval: true,
    };
  },
  methods: {
    async queryByProcessInstId(row) {
      let params = {
        processInstId: row.processInstanceId,
      };
      await queryByProcessInstId(params).then(res => {
        this.businessCodeObj = res.data
        console.log('[ businessCodeObj 11111] >', this.businessCodeObj)
        // addUser	开户
        // cgMtr	换表
        // delUser	销户
        // cgPrc	水价变更
        // cgBottom	表底变更
        // cgBill	账单调整
        // refund	线上缴费退费
        // reMtrFee	退表退费
      })
    },
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      this.queryParams = {
      flowSource: (location.href.indexOf("zaixian") == -1) ? ((location.href.indexOf("order/workStation") == -1) ? "hotLine" : "") : "onLine",
        ...parentQueryForm,
        flowStatus: "1",
        // 开始时间
        beginTime:
          parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") + " 00:00:00" : null,
        // 结束时间
        endTime:
          parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") + " 23:59:59" : null,
        pageNum: 1,
        pageSize: 10
      };
      this.getList();
    },
    getList() {
      listTodo(this.queryParams).then(response => {
        this.tableData = response.data;
        this.pageTotal = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    assignAgain(row) {
      this.assignRowData = row;
      this.assignCommit.visible = true;
    },
    async handleOrder(row) {
      this.showHandleOrderDialog = true;
      this.rowData = row;
    },
    closeHandleOrderDialog() {
      this.showHandleOrderNewDialog = false;
      this.showHandleOrderDialog = false;
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    },
    handleAssignUser() {
      this.$refs.assignForm.validate(valid => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "提交中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });
          let param = {
            flowId: this.assignRowData.flowId,
            processInstanceId: this.assignRowData.processInstanceId,
            taskId: this.assignRowData.taskId,
            targetUser: this.assignCommit.assignForm.assignUser,
            transferType: "2"
          };
          transferOrder(param).then(() => {
            loading.close();
            this.$message.success("改派成功");
            this.closeAssignCommitDialog();
            this.handleQuery();
          }).catch(() => {
            loading.close();
          }).finally(()=>{
            loading.close();
          });
        }
      });
    },
    closeAssignCommitDialog() {
      this.assignCommit.visible = false;
      this.assignRowData = {};
    }
  }
}
;
</script>
<style lang="less" scoped>

</style>
