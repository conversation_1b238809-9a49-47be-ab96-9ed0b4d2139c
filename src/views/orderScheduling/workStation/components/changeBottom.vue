<template>
  <div class="app-container">
    <el-row style="margin-top: 10px; margin-bottom: 10px">
      <LineTitle title="受理信息" fontSize="20"/>
    </el-row>

    <el-form
        :model="form1"
        ref="queryForm"
        label-width="85px"
        :inline="true"
        :rules="rule1"
        size="mini">
      <el-row :gutter="10">
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="业务类型">
            <el-input
                suffix-icon="x"
                disabled
                value="修改表底"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="受理人员">
            <el-input
                suffix-icon="x"
                disabled
                v-model="form1.userName"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="受理时间">
            <el-input
                suffix-icon="x"
                disabled
                v-model="form1.businessStartTime"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="受理渠道">
            <el-input
                suffix-icon="x"
                disabled
                value="柜台"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="受理说明" prop="remark">
            <el-input
                suffix-icon="x"
                disabled
                v-model="form1.remark"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row style="margin-top: 10px; margin-bottom: 10px">
      <LineTitle title="用户信息" fontSize="20"/>
    </el-row>

    <el-form
        :model="form2"
        ref="queryForm2"
        label-width="85px"
        :inline="true"
        :rules="rule2"
        size="mini">
      <el-row :gutter="10">
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="用户编号" prop="consName">
            <el-input
                suffix-icon="x"
                v-model="form2.consNo"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="用户名称" prop="consName">
            <el-input
                suffix-icon="x"
                v-model="form2.consName"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="手机号码" prop="mobile">
            <el-input
                suffix-icon="x"
                v-model="form2.mobile"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="营业所" prop="orgName">
            <el-input
                suffix-icon="x"
                v-model="form2.orgName"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="地址" prop="addr">
            <el-input
                suffix-icon="x"
                v-model="form2.addr"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="证件" prop="certificate">
            <el-input
                suffix-icon="x"
                v-model="form2.certificate"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="用户标签" prop="consTag">
            <el-select
                v-model="form2.consTag"
                placeholder="请选择"
                filterable
                clearable
                disabled
            >
              <el-option
                  v-for="dict in consTagDict"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="账户余额" prop="balance">
            <el-input
                suffix-icon="x"
                v-model="form2.balance"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="账户欠费" prop="oweAmt">
            <el-input
                suffix-icon="x"
                v-model="form2.oweAmt"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="最近出账日期" prop="billDate">
            <el-input
                suffix-icon="x"
                v-model="form2.billDate"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" style="margin-bottom: -5px">
          <el-form-item label="用户备注" prop="remark">
            <el-input
                suffix-icon="x"
                v-model="form2.remark"
                disabled
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row style="margin-top: 10px; margin-bottom: 10px">
      <LineTitle title="表具信息" fontSize="20"/>
    </el-row>

    <el-form>
      <el-table :data="tableData" ref="multipleTable" style="width: 100%">
        <el-table-column
            label="表具编号"
            min-width="100"
            align="center"
            prop="madeNo"
        />
        <el-table-column
            label="厂家型号"
            min-width="100"
            align="center"
            prop="mtrTypeCd"
        />
        <el-table-column
            label="上次抄表日期"
            min-width="150"
            align="center"
            prop="lastMrTime"
        />
        <el-table-column
            label="当前读数"
            min-width="100"
            align="center"
            prop="lastMrNum"
        />
        <el-table-column label="本次" align="center">
          <template slot-scope="scope">
            <el-input-number
                v-model="scope.row.thisMeterRead"
                :precision="0"
                :controls="false"
                :min="0"
                disabled
                style="width: 100%"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column
            label="价格"
            min-width="100"
            align="center"
            prop="prcName"
        />
      </el-table>
    </el-form>
  </div>
</template>

<script>
import {queryDelPremiseInfo} from "@/api/userManage/userManage";
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import {businessFlowParaQueryById} from "@/api/orderScheduling/workStation";

export default {
  name: "changBottom",
  components: {LineTitle},
  props: {
    rowInfo: {
      type: Object,
      default: () => {}
    },
    businessCodeObj: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      type: "business",
      tableData: [],
      consNo: '',
      checkMtrRead: true,
      businessCode: "cgBottom",
      consTagDict: [],
      form1: {
        userName: '',
        businessStartTime: '',
        remark: '',
      },
      form2: {},
      rule1: {
        remark: [
          {required: false, message: "请输入受理说明", trigger: "blur"}
        ],
      },
      rule2: {
        consNo: [
          {required: true, message: "请输入户号", trigger: "blur"}
        ],
      }
    }
  },
  computed: {},
  watch: {
    "rowInfo.ecBusinessFlowParaId": {
      handler(newVal) {
        if (newVal) {
          this.getbusinessFlowParaQueryById()
        }
      },
      immediate:true
    },
    "rowInfo.consNo": {
      handler(newVal) {
        if (newVal) {
          this.queryDelPremiseInfo()
        }
      },
      immediate:true
    },
  },
  methods: {
    //查询销户信息
    async queryDelPremiseInfo() {
      queryDelPremiseInfo({
            consNo: this.rowInfo.consNo
          }
      ).then((response) => {
        this.form2 = response.data;
      });
    },
    async getbusinessFlowParaQueryById() {
      let params = {
        id: this.rowInfo.ecBusinessFlowParaId
      }
      businessFlowParaQueryById(params).then(res => {
        this.queryParams = JSON.parse(res.data.paramDetail)
        console.log('[ this.queryParams ] >', this.queryParams)
        this.form1.userName = this.queryParams.operatorName;
        this.form1.businessStartTime = res.data.createTime;
        this.form1.remark = res.data.remark;
        this.tableData = this.queryParams.updatePremiseMeterDTOList
      })
    },
  },
  created() {
    //用户标签
    this.getDicts("crm_constag").then(response => {
      this.consTagDict = response.data;
    });
  },

}
</script>

<style lang="less" scoped>
//@import url(); 引入公共css类
</style>
