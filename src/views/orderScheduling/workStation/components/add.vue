<!-- 工单处理-用户档案信息 -->
<template>
  <div class="">
      <el-row type="flex" justify="end" v-if="title != '详情'">
        <el-button @click="$emit('closeDialog')">取 消</el-button>
        <el-button
            type="primary"
            v-if="activeName == 'first' && type != 'edit'"
            @click="addPremise"
            v-show="this.startBusinessFlow === 'N'"
        >建 档
        </el-button
        >
        <el-button
            type="primary"
            v-if="activeName == 'first' && type != 'edit'"
            @click="commitAddPremiseFlow"
            v-show="this.startBusinessFlow === 'Y'"
        >提 交
        </el-button
        >
        <el-button
            type="primary"
            v-if="activeName == 'second' && type != 'edit'"
            @click="commitPremise"
            v-show="this.startBusinessFlow === 'N'"
        >建 档
        </el-button
        >
        <el-button
            type="primary"
            v-if="activeName == 'second' && type != 'edit'"
            @click="commitPremise"
            v-show="this.startBusinessFlow === 'Y'"
        >提 交
        </el-button
        >
        <el-button
            type="primary"
            v-if="activeName == 'first' && type == 'edit'"
            @click="updatePremise"
        >修 改
        </el-button
        >
        <el-button
            type="primary"
            v-if="
            (!stagingFlag && activeName == 'second') ||
              (activeName == 'first' && type == 'add')
          "
            @click="temporarySave"
        >暂 存
        </el-button
        >
        <el-button
            type="primary"
            v-if="activeName == 'second' && stagingFlag"
            @click="updateEsAppInfo"
        >修 改
        </el-button
        >
      </el-row>
      <el-row style="margin-top: 10px; margin-bottom: 10px">
        <LineTitle title="用户基础信息" fontSize="20"/>
      </el-row>

      <el-form
          :model="queryParams"
          ref="queryForm"
          label-width="85px"
          :inline="true"
          :rules="rules"
          size="mini"
      >
        <el-row :gutter="10">
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="用户编号" prop="consNo">
              <el-input
                  suffix-icon="x"
                  v-model="queryParams.consNo"
                  disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="用户名称" prop="consName">
              <el-input
                  suffix-icon="x"
                  v-model="queryParams.consName"
                  :disabled="type == 'detail'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
                label="营业所"
                prop="orgNo"
                :rules="[
                {
                  required: true,
                  message: '请选择公司',
                  trigger: 'change'
                }
              ]"
            >
              <el-input
                  suffix-icon="x"
                  v-model="orgInfo.deptName"
                  :disabled="type === 'detail'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="所属区域" prop="districtId">
              <el-select
                  v-model="queryParams.districtId"
                  placeholder="请选择"
                  clearable
                  filterable
                  @change="getCommunityList"
                  :disabled="type == 'detail'"
              >
                <el-option
                    v-for="dict in regionList"
                    :key="dict.districtId"
                    :label="dict.districtName"
                    :value="dict.districtId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="所属小区" prop="commId">
              <el-select
                  v-model="queryParams.commId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type == 'detail'"
              >
                <el-option
                    v-for="dict in communityList"
                    :key="dict.areaId"
                    :label="dict.areaName"
                    :value="dict.areaId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="地址" prop="addr">
              <el-input
                  suffix-icon="x"
                  v-model="queryParams.addr"
                  :disabled="type === 'detail'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="mobile">
              <el-input
                  suffix-icon="x"
                  v-model="queryParams.mobile"
                  placeholder="请输入号码"
                  :disabled="type === 'detail'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
              :span="8"
              style="margin-bottom: -5px"
          >
            <el-form-item label="用水性质" prop="waterType">
              <el-select
                  v-model="queryParams.waterType"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail'"
              >
                <el-option
                    v-for="dict in waterTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="抄表册" prop="mrSectNo">
              <el-select
                  v-model="queryParams.mrSectNo"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail' || hasPlan"
              >
                <el-option
                    v-for="(item, index) in sectionList"
                    :key="index"
                    :label="item.name"
                    :value="item.mrSectNo"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="证件类型" prop="certType">
              <el-select
                  v-model="queryParams.certType"
                  placeholder="证件类型"
                  filterable
                  clearable
                  :disabled="type == 'detail'"
              >
                <el-option
                    v-for="dict in certTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="证件号码" prop="certNo">
              <el-input
                  suffix-icon="x"
                  v-model="queryParams.certNo"
                  placeholder="请输入号码"
                  :disabled="type == 'detail'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户标签" prop="consTag">
              <el-select
                  v-model="queryParams.consTag"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type == 'detail' || hasPlan"
              >
                <el-option
                    v-for="dict in consTagList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
              :span="8"
              style="margin-bottom: -5px"
          >
            <el-form-item label="计量方式" prop="meteringType">
              <el-select
                  v-model="queryParams.meteringType"
                  @change="changeMeterType"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail' || hasPlan"
              >
                <el-option
                    v-for="dict in meteringTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="margin-bottom: -5px">
            <el-form-item label="阶梯人口数" prop="stepPop">
              <el-input-number
                  v-model="queryParams.stepPop"
                  :precision="0"
                  :controls="false"
                  :min="0"
                  style="width: 180px"
                  placeholder="请输入阶梯人口数"
                  :disabled="type == 'detail' || hasPlan"
              />
            </el-form-item>
          </el-col>
          <el-col
              :span="8"
              style="margin-bottom: -5px"
          >
            <el-form-item label="用户类型" prop="consType">
              <el-select
                  v-model="queryParams.consType"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail'"
              >
                <el-option
                    v-for="dict in consTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
                label="水表类型"
                :prop="queryParams.meteringType == '1' ? 'meterType' : ''"
            >
              <el-select
                  v-model="queryParams.meterType"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail' || hasPlan"
              >
                <el-option
                    v-for="dict in meterTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="多表计价" prop="multiMeterPrice">
              <el-select
                  v-model="queryParams.multiMeterPrice"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail' || hasPlan"
              >
                <el-option
                    v-for="dict in multiMeterPriceList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否低保" prop="lowIncome">
              <el-select
                  v-model="queryParams.lowIncome"
                  placeholder="请选择"
                  filterable
                  clearable
                  @change="changeLowIncome"
                  :disabled="type === 'detail' || hasPlan"
              >
                <el-option
                    v-for="dict in yesOrNo"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="违约金" prop="lateFeeId">
              <el-select
                  v-model="queryParams.lateFeeId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :disabled="type === 'detail' || hasPlan"
              >
                <el-option
                    v-for="dict in deditOptions"
                    :key="dict.id"
                    :label="dict.ruleName"
                    :value="dict.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户备注" prop="remark">
              <el-input
                  suffix-icon="x"
                  v-model="queryParams.remark"
                  placeholder="请输入用户备注"
                  :disabled="type === 'detail'"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row
          style="margin-top: 10px; margin-bottom: 10px"
          v-if="queryParams.meteringType != '3'"
      >
        <LineTitle title="水表水价信息" fontSize="20"/>
      </el-row>

      <el-table :data="tableData2" v-if="queryParams.meteringType != '3'">
        <el-table-column label="水表编号" align="center">
          <template slot-scope="scope">
            <span v-if="title == '详情'">{{ scope.row.madeNo }}</span>
            <el-select
                v-else
                filterable
                v-model="scope.row.madeNo"
                placeholder="请选择"
                :disabled="title == '详情' || queryParams.meteringType == '2' || hasPlan"
                remote
                :remote-method="remoteSearchMadeNo"
                @change="changeMadeNo(scope.row.madeNo, scope.$index)"
            >
              <el-option
                  v-for="(item, index) in eamMtrList"
                  :key="index"
                  :label="item.madeNo"
                  :value="item.madeNo"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="安装读数" align="center">
          <template slot-scope="scope">
            <el-input-number
                v-model="scope.row.madeRead"
                :disabled="title == '详情' || queryParams.meteringType == '2' || hasPlan"
                :precision="0"
                :controls="false"
                :min="0"
                style="width: 100%"
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="水价" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-select
                filterable
                v-model="scope.row.prcCode"
                placeholder="请选择"
                clearable
                :disabled="title == '详情' || hasPlan"
                @change="changePrc(scope.row.prcCode, scope.row)"
            >
              <el-option
                  v-for="(item, index) in prcList"
                  :key="index"
                  :label="item.catPrcName"
                  :value="item.prcCode"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="水表型号" align="center">
          <template slot-scope="scope">
            <el-select
                filterable
                v-model="scope.row.mtrTypeCd"
                placeholder="请选择"
                clearable
                :disabled="title == '详情' || queryParams.meteringType == '2' || meterLibrary || hasPlan"
                @change="changeMtrTypeCd(scope.row.mtrTypeCd, scope.row)"
            >
              <el-option
                  v-for="(item, index) in mtrTypeCdList"
                  :key="index"
                  :label="item.mtrTypeName"
                  :value="item.mtrTypeCd"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="水表厂家" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_Mfgcd(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="水表类型" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_MeterType(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            label="总表编号"
            align="center"
        >
          <template slot-scope="scope">
            <el-input
                suffix-icon="x"
                v-model="scope.row.mainMeterNo"
                :disabled="
                title == '详情' ||queryParams.meteringType == '2' || hasPlan
              "
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column v-if="valveShow == 'Y'" label="欠费是否关阀" align="center">
          <template slot-scope="scope">
            <el-select
                filterable
                v-model="scope.row.closeValveIfArrears"
                placeholder="请选择"
                clearable
                :disabled="
                title == '详情' ||
                  queryParams.meteringType == '2'
              ">
              <el-option label="是" value="Y"/>
              <el-option label="否" value="N"/>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" v-if="type == 'add'">
          <template slot-scope="scope">
            <el-link
                type="primary"
                @click="userAddMeter(scope.$index)"
                :disabled="title == '详情' || title == '编辑'"
            >新增
            </el-link
            >
            <el-divider direction="vertical"></el-divider>
            <el-link
                type="primary"
                @click="userDelMeter(scope.$index)"
                :disabled="title == '详情' || title == '编辑'"
            >删除
            </el-link
            >
          </template>
        </el-table-column>
      </el-table>

      <el-row
          style="margin-top: 10px; margin-bottom: 10px"
          v-if="queryParams.meteringType == '3'"
      >
        <LineTitle title="无表人口信息" fontSize="20"/>
      </el-row>

      <el-form
          :model="form3"
          ref="form3"
          label-width="110px"
          :inline="true"
          :rules="rulesForm3"
          size="mini"
          v-if="queryParams.meteringType == '3'"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="用水人口数" prop="calcPop">
              <el-input-number
                  v-model="form3.calcPop"
                  :disabled="title == '详情'"
                  :precision="0"
                  :controls="false"
                  :min="0"
                  style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每人水量" prop="calcPopPq">
              <el-input-number
                  v-model="form3.calcPopPq"
                  :disabled="title == '详情'"
                  :precision="0"
                  :controls="false"
                  :min="0"
                  style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="水价" prop="calcPopPrc">
              <el-select
                  filterable
                  v-model="form3.calcPopPrc"
                  placeholder="请选择"
                  clearable
                  :disabled="title == '详情'"
              >
                <el-option
                    v-for="(item, index) in prcList"
                    :key="index"
                    :label="item.catPrcName"
                    :value="item.prcCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row
          style="margin-top: 10px; margin-bottom: 10px"
          v-if="queryParams.lowIncome == 'Y'"
      >
        <LineTitle title="低保设置" fontSize="20"/>
      </el-row>

      <el-form
          :model="form2"
          ref="form2"
          label-width="110px"
          :inline="true"
          :rules="rulesForm2"
          size="mini"
          v-if="queryParams.lowIncome == 'Y'"
      >
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item label="低保减免吨数" prop="lowIncomePq">
              <el-input-number
                  v-model="form2.lowIncomePq"
                  :disabled="title == '详情'"
                  :precision="0"
                  :controls="false"
                  :min="0"
                  style="width: 100%"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="水价" prop="lowIncomePrc">
              <el-select
                  filterable
                  v-model="form2.lowIncomePrc"
                  placeholder="请选择"
                  clearable
                  :disabled="title == '详情'"
              >
                <el-option
                    v-for="(item, index) in prcList"
                    :key="index"
                    :label="item.catPrcName"
                    :value="item.prcCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row
          style="margin-top: 10px; margin-bottom: 10px"
          v-if="tableData3.length > 0"
      >
        <LineTitle title="换表记录" fontSize="20"/>
      </el-row>

      <el-table :data="tableData3" v-if="tableData3.length > 0">
        <el-table-column label="水表编号" prop="oldMadeNo" align="center">
        </el-table-column>
        <el-table-column label="当前读数" prop="oldMeterRead" align="center">
        </el-table-column>
        <el-table-column label="水表厂家" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_Mfgcd(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="水表类型" align="center">
          <template slot-scope="scope">
            <span>
              {{ CRM_MeterType(scope.row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
            label="换表时间"
            prop="createTime"
            align="center"
        ></el-table-column>
      </el-table>

    <!--表具新增-->
    <el-dialog
        title="新增表具"
        :visible.sync="addMeterVisible"
        v-if="addMeterVisible"
        append-to-body
        destroy-on-close
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        custom-class="mydialog"
        width="600px"
    >
      <el-form ref="addForm" :model="addMeterForm" :rules="addMeterRules" label-width="120px">
        <el-row>
          <el-col :span="16" :offset="4">
            <el-form-item label="水表编号" prop="madeNo">
              <el-input
                  v-model="addMeterForm.madeNo"
                  placeholder="请输入水表编号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" :offset="4">
            <el-form-item
                label="表具型号"
                prop="mtrTypeCd"
            >
              <el-select
                  v-model="addMeterForm.mtrTypeCd"
                  placeholder="请选择表具型号"
                  @change="mtrTypeCdChange"
              >
                <el-option
                    v-for="dict in mtrTypeCdList"
                    :key="dict.mtrTypeCd"
                    :label="dict.mtrTypeName"
                    :value="dict.mtrTypeCd"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16" :offset="4">
            <el-form-item label="水表厂家">
              <el-select
                  v-model="addMeterForm.mfgCd"
                  placeholder="请选择表具厂家"
                  disabled
              >
                <el-option
                    v-for="dict in mfgCdList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="16" :offset="4">
            <el-form-item label="水表类型">
              <el-select
                  v-model="addMeterForm.meterType"
                  placeholder="请选择水表类型"
                  disabled
              >
                <el-option
                    v-for="dict in meterTypeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click.stop="submitAddMeterForm">确 定</el-button>
        <el-button @click.stop="cancelMeterAddDialog">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import LineTitle from "@/components/ViewComponents/LineTitle.vue";
import {
  mtrTypeCdList,
  prcList,
  queryExistCount,
  queryList,
} from "@/api/userManage/userManage";
import {list} from "@/api/crm/customer/region.js";
import {list as communityList} from "@/api/crm/customer/village.js";
import {list as sectionList} from "@/api/business/section/section.js";
import {list as mtrList} from "@/api/crm/metermanage/meterArchives.js";
import { businessFlowParaQueryByProcessId } from "@/api/orderScheduling/workStation";
import { getDept } from "@/api/system/dept";


export default {
  //import引入的组件需要注入到对象中才能使用
  name: "Add",
  components: {
    LineTitle,
  },
  props: {
    visable: {
      type: Boolean,
      default: false
    },
    activeName: {
      type: String,
      default: "first"
    },
    type: {
      type: String,
      default: ""
    },
    rowInfo: {
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ""
    },
    stagingFlag: {
      //是否是暂存
      type: Boolean,
      default: false
    },
    businessCodeObj: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    let checkIDCard = (rule, value, callback) => {
      const IDCardReg = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$/;
      if (value) {
        if (IDCardReg.test(value)) {
          callback();
        } else {
          callback(new Error("身份证号格式不正确"));
        }
      }
    };
    let checkData = (rule, value, callback) => {
      let _this = this;
      let oldValue = _this.oldPremise[rule.param];
      if (oldValue) {
        if (_this.hasPlan && value !== oldValue) {
          if (rule.param === "stepPop") {
            callback(new Error(_this.warningMsg + "：" + oldValue));
          } else {
            callback(new Error(_this.warningMsg));
          }
        } else {
          callback();
        }
      } else {
        if (_this.hasPlan && value) {
          callback(new Error(_this.warningMsg));
        } else {
          callback();
        }
      }
    };
    let checkData_2 = (rule, value, callback) => {
      let _this = this;
      let oldValue = _this.dataForm2[rule.param];
      if (oldValue) {
        if (_this.hasPlan && value !== oldValue) {
          callback(new Error(_this.warningMsg));
        } else {
          callback();
        }
      } else {
        if (_this.hasPlan && value) {
          callback(new Error(_this.warningMsg));
        } else {
          callback();
        }
      }
    };
    let checkData_3 = (rule, value, callback) => {
      let _this = this;
      let oldValue = _this.dataForm3[rule.param];
      if (oldValue) {
        if (_this.hasPlan && value !== oldValue) {
          callback(new Error(_this.warningMsg));
        } else {
          callback();
        }
      } else {
        if (_this.hasPlan && value) {
          callback(new Error(_this.warningMsg));
        } else {
          callback();
        }
      }
    };
    let checkPhone = (rule, value, callback) => {
      if (value === "" || value === undefined) {
        callback();
      } else {
        let regPone = null;
        let mobile = /^1(3|4|5|6|7|8|9)\d{9}$/; //最新16手机正则
        let tel = /^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/; //座机
        if (value.charAt(0) == 0) {
          // charAt查找第一个字符方法，用来判断输入的是座机还是手机号
          regPone = tel;
        } else {
          regPone = mobile;
        }
        if (!regPone.test(value)) {
          return callback(
              new Error("请填写联系人电话(座机格式'区号-座机号码')")
          );
        }
        callback();
      }
    };
    //这里存放数据
    return {
      checkIDCard,
      queryParams: {
        consName: undefined,
        orgNo: undefined,
        districtId: undefined,
        commId: undefined,
        addr: undefined,
        mobile: undefined,
        waterType: undefined,
        mrSectNo: undefined,
        certType: undefined,
        certNo: undefined,
        consTag: undefined,
        meteringType: undefined,
        stepPop: undefined,
        consType: undefined,
        meterType: undefined,
        multiMeterPrice: undefined,
        lowIncome: undefined,
        lateFeeId: undefined,
        remark: undefined,
        businessCode: "addUser",
      },
      rules: {
        consName: [
          {required: true, message: "请输入用户名称", trigger: "blur"}
        ],
        districtId: [
          {required: true, message: "请选择所属区域", trigger: "change"}
        ],
        commId: [
          {required: true, message: "请选择所属小区", trigger: "change"}
        ],
        addr: [{required: true, message: "请输入地址", trigger: "blur"}],
        waterType: [
          {required: true, message: "请选择用水性质", trigger: "change"}
        ],
        meteringType: [
          {required: true, message: "请选择计量方式", trigger: "change"},
          {validator: checkData, param: "meteringType", trigger: "change"}
        ],
        consType: [
          {required: true, message: "请选择用户类型", trigger: "change"}
        ],
        meterType: [
          {required: true, message: "请选择水表类型", trigger: "change"},
          {validator: checkData, param: "meterType", trigger: "change"}
        ],
        mobile: [{required: false, validator: checkPhone, trigger: "blur"}],
        mrSectNo: [
          {
            required: false,
            validator: checkData,
            param: "mrSectNo",
            trigger: "change"
          }
        ],
        consTag: [
          {
            required: false,
            validator: checkData,
            param: "consTag",
            trigger: "change"
          }
        ],
        stepPop: [
          {
            required: false,
            validator: checkData,
            param: "stepPop",
            trigger: "blur"
          }
        ],
        multiMeterPrice: [
          {
            required: false,
            validator: checkData,
            param: "multiMeterPrice",
            trigger: "change"
          }
        ],
        lowIncome: [
          {
            required: false,
            validator: checkData,
            param: "lowIncome",
            trigger: "change"
          }
        ],
        lateFeeId: [
          {
            required: false,
            validator: checkData,
            param: "lateFeeId",
            trigger: "change"
          }
        ]
      },
      rulesForm2: {
        lowIncomePq: [
          {required: true, message: "请输入减免吨数", trigger: "blur"},
          {validator: checkData_2, param: "lowIncomePq", trigger: "blur"}
        ],
        lowIncomePrc: [
          {required: true, message: "请选择水价", trigger: "change"},
          {validator: checkData_2, param: "lowIncomePrc", trigger: "change"}
        ]
      },
      rulesForm3: {
        calcPop: [
          {required: true, message: "请输入用水人口数", trigger: "blur"},
          {validator: checkData_3, param: "calcPop", trigger: "blur"}
        ],
        calcPopPq: [
          {required: true, message: "请输入每人水量", trigger: "blur"},
          {validator: checkData_3, param: "calcPopPq", trigger: "blur"}
        ],
        calcPopPrc: [
          {required: true, message: "请选择水价", trigger: "change"},
          {validator: checkData_3, param: "calcPopPrc", trigger: "change"}
        ]
      },
      warningMsg: "存在在途计划,请勿修改",
      hasPlan: false,
      oldPremise: {},
      tableData2: [],
      tableData3: [],
      //气表列表
      meterList: [],
      districtIds: [],
      deditOptions: [], //违约金
      valveShow: '',
      dataForm2: {},
      dataForm3: {},
      form2: {},
      form3: {},
      //字典集合
      waterTypeList: [],
      meterTypeList: [],
      consTagList: [],
      consTypeList: [],
      meteringTypeList: [],
      consStatusList: [],
      certTypeList: [],
      multiMeterPriceList: [],
      yesOrNo: [],
      //小区集合
      communityList: [],
      //区域集合
      regionList: [],
      sectionList: [],
      //表具型号
      mtrTypeCdList: [],
      prcList: [],
      mfgCdList: [],
      addPremiseParam: {
        addPremiseMeterDTO: []
      },
      addRowIndex: '',
      addMeterForm: {
        madeNo: '',
        mtrTypeCd: '',
        mfgCd: '',
        meterType: ''
      },
      addMeterRules: {
        madeNo: [
          {required: true, message: "请输入水表编号", trigger: "blur"}
        ],
        mtrTypeCd: [
          {required: true, message: "请选择水表类型", trigger: "blur"}
        ],
      },
      addMeterVisible: false,
      eamMtrList: [{
        madeNo: '新增表具',
        meterId: 0
      }],
      meterLibrary: true,
      startBusinessFlow: this.$store.state.app.startBusinessFlow,
      orgInfo:{}
    };
  },
  computed: {},
  watch: {
    "queryParams.orgNo": {
      handler(newVal) {
        if (!!newVal) {
          this.getRegionList();
          this.getCommunityList();
          this.getQueryList();
          this.getPrcList();
          this.getMtrTypeCdList();
          this.getSectionList();
        }
        if (this.type == "add") {
          this.queryParams.mrSectNo = undefined;
        }
      },
      deep: true,
      immediate: true
    },
    "queryParams.districtId": {
      handler(newVal) {
        if (!!newVal) {
          this.getCommunityList();
        }
      }
    },
    "rowInfo": {
      handler(newVal) {
        if (newVal.processInstanceId) {
          this.getBusinessFlowParaQueryByProcessId();
        }
      },
      deep: true,
      immediate: true
    },
  },
  //方法集合
  methods: {
    //查询部门
    getDept(val){
      getDept(val).then(res => {
        this.orgInfo = res.data;
      })
    },
    getBusinessFlowParaQueryByProcessId() {
      console.log("接口参数this.rowInfo.processInstanceId", this.rowInfo.processInstanceId);
      let params = {
        processInstId: Number(this.rowInfo.processInstanceId)
      }
      businessFlowParaQueryByProcessId(params).then(res => {
        this.queryParams = JSON.parse(res.data.paramDetail)
        console.log('[ this.queryParams ] >', this.queryParams)
        this.tableData2 = this.queryParams.addPremiseMeterDTO
        console.log("查询orgNo",this.queryParams.orgNo);
        this.getDept(Number(this.queryParams.orgNo));
      })
    },
    remoteSearchMadeNo(val) {
      mtrList({
        pageNum: 1,
        pageSize: 10,
        madeNoLike: val
      }).then(response => {
        this.eamMtrList = [{
          madeNo: '新增表具',
          meterId: 0
        }];
        this.eamMtrList.push(...response.data)
      })
    },
    submitAddMeterForm() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          //验证表号是否已经存在
          queryExistCount({madeNo: this.addMeterForm.madeNo}).then(response => {
            if (response.data == 0) {
              let index = this.addRowIndex == '' ? 0 : this.addRowIndex;
              let row = this.tableData2[index]
              this.$set(row, 'madeNo', this.addMeterForm.madeNo)
              this.$set(row, "mtrTypeCd", this.addMeterForm.mtrTypeCd)
              this.$set(row, "manufacturer", this.addMeterForm.mfgCd)
              this.$set(row, "meterType", this.addMeterForm.meterType)
              this.$set(row, "madeRead", '')
              this.cancelMeterAddDialog();
            } else {
              this.$message.error("表具已存在")
            }
          })
        }
      })
    },
    cancelMeterAddDialog() {
      let index = this.addRowIndex == '' ? 0 : this.addRowIndex;
      let row = this.tableData2[index]
      if (row.madeNo == '新增表具') {
        this.$set(row, 'madeNo', '');
      }
      this.addMeterForm = {
        madeNo: '',
        mtrTypeCd: '',
        mfgCd: '',
        meterType: ''
      };
      this.addMeterVisible = false;
    },
    mtrTypeCdChange(mtrTypeCd) {
      let data = this.mtrTypeCdList.find(item => item.mtrTypeCd == mtrTypeCd)
      this.$set(this.addMeterForm, "mfgCd", data.manufacturer);
      this.$set(this.addMeterForm, "meterType", data.meterType);
    },
    changeMadeNo(madeNo, index) {
      console.info(madeNo, index)
      this.addRowIndex = index;
      if (madeNo == '新增表具') {
        this.addMeterVisible = true;
      } else {
        let meter = this.eamMtrList.find(item => item.madeNo == madeNo);
        if (meter) {
          let row = this.tableData2[index]
          this.$set(row, "madeRead", meter.madeRead)
          this.$set(row, "mtrTypeCd", meter.mtrTypeCd)
          this.$set(row, "manufacturer", meter.mfgCd)
          this.$set(row, "meterType", meter.meterType)
        }
      }
    },
    changeMeterType() {
      this.tableData2 = [];
      if (this.queryParams.meteringType == "2") {
        let param = {
          madeNo: "",
          madeRead: 0,
          mtrTypeCd: "NoMeter",
          closeValveIfArrears: "Y"
        };
        this.tableData2.push(param);
      } else {
        let param = {
          madeNo: "",
          closeValveIfArrears: "Y"
        };
        this.tableData2.push(param);
      }
    },
    // 切换证件类型，验证身份证、港澳通行证、台湾通行证、护照
    handleCardChange() {
      if (this.queryParams.certType == 1) {
        //身份证
        this.rules.certNo = [
          {required: false, validator: this.checkIDCard, trigger: "blur"}
        ];
      } else if (this.queryParams.certType == 2) {
        //军官证
        this.rules.certType = [{}];
      }
    },
    /**
     * 字典方法
     */
    //用水性质
    CRM_WaterType(row) {
      return this.selectDictLabel(this.waterTypeList, row.waterType);
    },
    //水表类型
    CRM_MeterType(row) {
      return this.selectDictLabel(this.meterTypeList, row.meterType);
    },
    //用户标签
    CRM_ConsTag(row) {
      return this.selectDictLabel(this.consTagList, row.consTag);
    },
    //用户类型
    CRM_ConsType(row) {
      return this.selectDictLabel(this.consTypeList, row.consType);
    },
    //计量方式
    CRM_MeteringType(row) {
      return this.selectDictLabel(this.meterTypeList, row.meteringType);
    },
    //用户状态
    CRM_ConsStatus(row) {
      return this.selectDictLabel(this.consStatusList, row.status);
    },
    //证件类型
    CRM_CertType(row) {
      return this.selectDictLabel(this.certTypeList, row.certType);
    },
    //多表计价
    CRM_MultiMeterPrice(row) {
      return this.selectDictLabel(
          this.multiMeterPriceList,
          row.multiMeterPrice
      );
    },
    //是否
    SYS_Yes_No(row) {
      return this.selectDictLabel(this.yesOrNo, row.lowIncome);
    },
    //生产厂家
    CRM_Mfgcd(row) {
      return this.selectDictLabel(this.mfgCdList, row.manufacturer);
    },

    //获取区域列表
    getRegionList() {
      console.log("3415" , this.queryParams.orgNo , this.$store.state.app.districtSupport);
      list({
        orgNoList: this.$store.state.app.districtSupport === "Y" ? this.queryParams.orgNo : null
      }).then(res => {
        this.regionList = res.data;
      });
    },
    // 获取小区列表
    getCommunityList() {
      // this.queryParams.commId = undefined;
      let params = {
        districtId: this.queryParams.districtId,
        orgNoList: this.$store.state.app.areaSupport === "Y" ? this.queryParams.orgNo : null
      };
      communityList(params).then(res => {
        this.communityList = res.data;
      });
    },
    //获取抄表册列表
    getSectionList() {
      let params = {
        orgNoList: this.$store.state.app.sectSupport === "Y" ? this.queryParams.orgNo : null,
        pageNum: 1,
        pageSize: 999
      };
      sectionList(params).then(res => {
        this.sectionList = res.data;
        console.log(this.sectionList);
      });
    },
    //获取水表类型
    getMtrTypeCdList() {
      let params = {
        pageNum: 1,
        pageSize: 999,
        orgNoList: this.$store.state.app.paraSupport === "Y" ? this.queryParams.orgNo : null,
      };
      console.log(params)
      console.log("水表类型",this.$store.state.app.paraSupport)
      console.log("this.queryParams.orgNo",this.queryParams.orgNo)
      mtrTypeCdList(params).then(response => {
        this.mtrTypeCdList = response.data;
      });
    },

    //查询水价
    getPrcList() {
      let params = {
        releaseFlag: 1, //只查询生效的水价
        pageNo: 1,
        pageSize: 999,
        orgNoList: this.$store.state.app.prcSupport === "Y" ? this.queryParams.orgNo : null,
      };
      prcList(params).then(response => {
        this.prcList = response.data;
      });
    },
    //获取违约金规则列表
    getQueryList() {
      queryList({orgNoList: this.$store.state.app.lateFeeSupport === "Y" ? this.queryParams.orgNo : null}).then(res => {
        console.log(res);
        this.deditOptions = res.data;
      });
    },

    //更改表具型号
    changeMtrTypeCd(val, row) {
      this.mtrTypeCdList.forEach(item => {
        if (item.mtrTypeCd == val) {
          console.log(item);
          row.manufacturer = item.manufacturer;
          row.meterType = item.meterType;
        }
      });
    },

    //更改水价
    changePrc(val, row) {
      this.prcList.forEach(item => {
        if (item.prcCode == val) {
          console.log(item);
          row.prcName = item.catPrcName;
        }
      });
    },
    //切换低保
    changeLowIncome(val) {
      if (val != "Y") {
        this.form2 = {};
      }
    },
    getPrcNameByCode(prcCode) {
      if (!!prcCode) {
        let priceInfo = this.prcList.find(item => item.prcCode === prcCode);
        if (!!priceInfo) {
          return priceInfo.catPrcName;
        }
      }
      return undefined;
    },
    handleClose() {
      this.$emit("closeDialog", false);
      this.$emit("refresh");
    },
    userAddMeter(rowIndex) {
      this.tableData2.push({
        meterId: "",
        thisRead: "",
        inflowDirection: "",
        priceValue: {},
        mainRunMeterId: "",
        closeValveIfArrears: "Y",
      });
    },
    userDelMeter(rowIndex) {
      if (this.tableData2.length == 1) {
        return;
      }
      let array = this.tableData2.filter((data, index) => index != rowIndex);
      this.tableData2 = array;
    },
    //展示卡表类型
    meterTypeOptionFilter(val) {
      console.log("&&&&&&&&&&&&&&");
      console.log(val);
      let type = "";
      this.mtrTypeCdList.forEach(element => {
        if (element.meterType == val) {
          console.log(element.meterType);
          this.meterTypeList.forEach(item => {
            if (item.value == element.meterType) {
              type = item.label;
              // this.Madetype = type;
              console.log(type);
            }
          });
        }
      });
      console.log("=============");
      console.log(type);
      return type;
    },

    async getMeterList() {
      let temp = {
        madeNo: "新增表具",
        meterId: "add"
      };
      this.meterList = [];
      this.meterList.push(temp);
      await meterList({
        orgNoList: this.baseUserDetail.orgNo,
        asCode: "001"
      }).then(res => {
        res.data.forEach(item => {
          this.meterList.push(item);
        });
      });
    },

    //新增气表
    addMeter(scope, val) {
      let row = scope.row;
      if (row.meterId == "add" && val == 1) {
        this.dialogVisibleAddMeter = true;
        this.addMeterForm.index = scope.$index;
        //选中项置空
        this.tableData2.forEach(element => {
          if (element.meterId == "add") {
            element.meterId = "";
          }
        });
      } else if (row.mainRunMeterId == "add" && val == 2) {
        this.dialogVisibleAddMeter = true;
        //选中项置空-主表
        this.tableData2.forEach(element => {
          if (element.mainRunMeterId == "add") {
            element.mainRunMeterId = "";
          }
        });
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    /**
     * 字典获取
     */
    //用水性质
    this.getDicts("crm_cons_water_type").then(response => {
      this.waterTypeList = response.data;
    });
    //表具类型
    this.getDicts("crm_meter_type").then(response => {
      this.meterTypeList = response.data;
    });
    //用户标签
    this.getDicts("crm_constag").then(response => {
      this.consTagList = response.data;
    });
    //用户类型
    this.getDicts("crm_cons_type").then(response => {
      this.consTypeList = response.data;
    });
    //计量方式
    this.getDicts("crm_cons_metering_type").then(response => {
      this.meteringTypeList = response.data;
    });
    //用户状态
    this.getDicts("crm_cons_status").then(response => {
      this.consStatusList = response.data;
    });
    //证件类型
    this.getDicts("crm_certtype").then(response => {
      this.certTypeList = response.data;
    });
    //多表计价
    this.getDicts("crm_multi_meter_price").then(response => {
      this.multiMeterPriceList = response.data;
    });
    //系统是否
    this.getDicts("sys_yes_no").then(response => {
      this.yesOrNo = response.data;
    });
    //生产厂家
    this.getDicts("crm_mfgcd").then(response => {
      this.mfgCdList = response.data;
    });
    //获取表具型号
    // this.getMtrTypeCdList();
    //获取水价
    // this.getPrcList();
    //获取区域
    // this.getRegionList();
    //获取违约金列表
    // this.getQueryList();
    //用户表具列表table
    this.tableData2.push({
      meterId: "",
      thisRead: "",
      inflowDirection: "",
      priceValue: {},
      mainRunMeterId: "",
      closeValveIfArrears: "Y",
    });
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  async mounted() {
    this.getConfigKey("show_close_valve_config").then((response) => {
      if (response.data === null || response.data === undefined) {
        this.valveShow = "N";
      } else {
        this.valveShow = response.data.configValue;
      }
    });
    // if (this.type == "edit" || this.type == "detail") {
    //   //获取用户档案编辑、详情
    //   await this.getPremiseDetail();
    // } else if (this.type == "add") {
    //   //用户新增
    //   if (this.stagingFlag) {
    //     //暂存用户详情
    //     this.getEsAppDetail();
    //   }
    // }
  },
  beforeCreate() {
  },
  beforeMount() {
  },
  beforeUpdate() {
  },
  updated() {
  },
  beforeDestroy() {
  },
  destroyed() {
  },
  activated() {
  }
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
</style>
