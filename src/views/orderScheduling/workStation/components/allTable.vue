<template>
  <div>
    <GridTable
      :columns="columns"
      :tableData="tableData"
      :currentPage.sync="queryParams.pageNum"
      :pageSize.sync="queryParams.pageSize"
      :total.sync="pageTotal"
      @changePage="changePage"
      :loading="loading"
      :tableId="tableId"
    >
      <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
        <el-button type="primary" size="mini" @click.stop="createOrder"
          >创建工单
        </el-button>
      </template>
      <template slot="operation" slot-scope="{ row }">
        <el-button @click="toView(row)" size="mini" type="text">查看</el-button>
      </template>
    </GridTable>

    <!--工单查看-->
    <OrderInfo
      v-if="drawerVisible"
      :drawer="drawerVisible"
      :orderItem="drawerOrder"
      @closeDrawer="closeDrawer"
    />
  </div>
</template>
<script>
import mixin from "@/mixin/commonPage";
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import { listAll } from "@/api/orderScheduling/workStation";
import moment from "moment";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  // mixins: [mixin],
  props: ["queryForm"],
  components: { OrderInfo, GridTable, AdvancedForm },
  watch: {},
  mounted() {
    this.initColumns();
    this.handleQuery();
  },
  data() {
    return {
      loading: false,
      pageInitQuery: false,
      queryParams: {},
      pageTotal: 0,
      tableData: [],
      drawerVisible: false,
      drawerOrder: {},
      taskStatusOptions: [],
      tableId: "allTable",
      columns: [],
    };
  },
  methods: {
    initColumns() {
      this.columns = [
        {
          field: "businessNo",
          title: "工单编号",
        },
        {
          field: "flowTypeLabel",
          title: "工单类型",
        },
        {
          field: "flowStatusValue",
          title: "工单状态",
          showOverflowTooltip: true,
        },
        {
          field: "emergencyLevel",
          title: "紧急程度",
          showOverflowTooltip: true,
        },
        {
          field: "flowRemark",
          title: "工单说明",
          showOverflowTooltip: true,
        },
        {
          field: "createTimeValue",
          title: "创建时间",
          showOverflowTooltip: true,
        },
        {
          field: "creator",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
      if (this.queryForm.flowStatus == "1") {
        this.columns.splice(2, 0, {
          field: "taskName",
          title: "处理进度",
        });
        this.columns.splice(3, 0, {
          field: "taskAssignee",
          title: "当前处理人",
        });
        this.columns.splice(4, 0, {
          field: "taskStatusValue",
          title: "当前状态",
        });
      } else if (this.queryForm.flowStatus == "2") {
        this.columns.splice(4, 0, {
          field: "flowResult",
          title: "执行结果",
        });
      }
    },
    initParams() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      if (!Object.keys(parentQueryForm).length) {
        return;
      }
      this.queryParams = {
        flowSource:
          location.href.indexOf("zaixian") == -1
            ? location.href.indexOf("order/workStation") == -1
              ? "hotLine"
              : ""
            : "onLine",
        ...parentQueryForm,
        // 开始时间
        beginTime:
          parentQueryForm.daterange.length > 0
            ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") +
              " 00:00:00"
            : null,
        // 结束时间
        endTime:
          parentQueryForm.daterange.length > 0
            ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") +
              " 23:59:59"
            : null,
        pageNum: 1,
        pageSize: 10,
      };
    },
    taskStatusFormat(row) {
      if (row.taskStatus == "1") {
        return "待办";
      } else if (row.taskStatus == "2") {
        return "待处理";
      }
    },

    flowStatusFormat(row) {
      if (row.flowStatus == "1") {
        return "流转中";
      } else if (row.flowStatus == "2") {
        return "已完成";
      } else if (row.flowStatus == "3") {
        return "已作废";
      }
    },
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      this.initParams();
      this.getList();
    },
    getList() {
      const { daterange, ...args } = this.queryParams;
      this.loading = true;
      listAll(args)
        .then((response) => {
          this.tableData = response.data;
          this.pageTotal = response.total;
          this.loading = false;
          this.tableData.forEach((element) => {
            //工单状态
            this.$set(
              element,
              "flowStatusValue",
              this.flowStatusFormat(element)
            );
            //当前状态
            this.$set(
              element,
              "taskStatusValue",
              this.taskStatusFormat(element)
            );
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
          });
        })
        .catch(() => {
          this.loading = false;
        });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    },
    createOrder() {
      this.$emit("createOrder");
    },
    //分页切换
    changePage() {
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
</style>
