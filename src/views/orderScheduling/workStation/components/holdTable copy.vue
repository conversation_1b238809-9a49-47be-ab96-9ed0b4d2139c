<template>
  <div>
    <el-table v-loading="loading" ref="multipleTable" :data="tableData">
      <el-table-column label="工单编号" prop="businessNo" />
      <el-table-column label="工单类型" prop="flowTypeLabel" />
      <el-table-column label="处理进度" prop="taskName" />
      <el-table-column label="当前处理人" prop="taskAssignee" :show-overflow-tooltip="true" />
      <!--      <el-table-column label="当期状态" prop="typeStatus" :show-overflow-tooltip="true" width="90" />-->
      <el-table-column label="紧急程度" prop="emergencyLevel" :show-overflow-tooltip="true" />
      <el-table-column label="工单说明" prop="flowRemark" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" prop="createTime" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="creator" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="lastOperator" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button @click="toView(scope.row)" size="mini" type="text">查看</el-button>
          <el-button @click="orderCheckIn(scope.row)" size="mini" type="text">签入</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="pageTotal > 0" :total="pageTotal" :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList" />

    <!--工单查看-->
    <OrderInfo v-if="drawerVisible" :drawer="drawerVisible" :orderItem="drawerOrder" @closeDrawer="closeDrawer" />

  </div>
</template>
<script>
import OrderInfo from "@/views/orderScheduling/workStation/components/orderInfo.vue";
import { listToDeal, transferOrder } from "@/api/orderScheduling/workStation";
import moment from "moment";

export default {
  props: ["queryForm"],
  components: { OrderInfo },
  mounted() {
    let parentQueryForm = this._.cloneDeep(this.queryForm);
    this.queryParams = {
      flowSource: (location.href.indexOf("zaixian") == -1) ? ((location.href.indexOf("order/workStation") == -1) ? "hotLine" : "") : "onLine",
      ...parentQueryForm,
      // 开始时间
      beginTime:
        parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") + " 00:00:00" : null,
      // 结束时间
      endTime:
        parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") + " 23:59:59" : null,
      pageNum: 1,
      pageSize: 10
    };
    this.handleQuery();
  },
  data() {
    return {
      loading: true,
      pageInitQuery: false,
      queryParams: {},
      pageTotal: 0,
      tableData: [],
      drawerVisible: false,
      drawerOrder: {}
    };
  },
  methods: {
    toView(row) {
      this.drawerVisible = true;
      this.drawerOrder = row;
    },
    handleQuery() {
      let parentQueryForm = this._.cloneDeep(this.queryForm);
      this.queryParams = {
      flowSource: (location.href.indexOf("zaixian") == -1) ? ((location.href.indexOf("order/workStation") == -1) ? "hotLine" : "") : "onLine",
        ...parentQueryForm,
        // 开始时间
        beginTime:
          parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[0]).format("yyyy-MM-DD") + " 00:00:00" : null,
        // 结束时间
        endTime:
          parentQueryForm.daterange.length > 0 ? moment(parentQueryForm.daterange[1]).format("yyyy-MM-DD") + " 23:59:59" : null,
        pageNum: 1,
        pageSize: 10
      };
      this.getList();
    },
    getList() {
      listToDeal(this.queryParams).then(response => {
        this.tableData = response.data;
        this.pageTotal = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    orderCheckIn(row) {
      this.$confirm("请确认是否签入?", "签入", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          text: "提交中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
        let param = {
          flowId: row.flowId,
          processInstanceId: row.processInstanceId,
          taskId: row.taskId,
          transferType: "1"
        };
        transferOrder(param).then(() => {
          loading.close();
          this.$message.success("签入成功");
          this.handleQuery();
        }).catch(() => {
          loading.close();
        }).finally(()=>{
          loading.close();
        });
      });
    },
    closeDrawer() {
      this.drawerVisible = false;
      this.drawerOrder = {};
    }
  }
}
;
</script>
<style lang="less" scoped>

</style>
