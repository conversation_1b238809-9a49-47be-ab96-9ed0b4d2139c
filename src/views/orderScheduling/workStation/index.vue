<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
      <!-- <template slot="deptIds">
        <el-form-item label="公司" prop="deptIds" label-width="80px">
          <treeselect
            v-model="deptIds"
            :limit="1"
            :multiple="true"
            :options="deptOptions"
            :flat="true"
            placeholder="请选择公司"
          />
        </el-form-item>
      </template> -->
    </AdvancedForm>
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="全部" name="first">
        <AllTable
          ref="allTable"
          v-if="tableKey === 'all'"
          :queryForm="tempPropParams"
          @createOrder="createOrder"
        />
      </el-tab-pane>
      <el-tab-pane label="我发起" name="second">
        <OweTable
          ref="oweTable"
          v-if="tableKey === 'owe'"
          :queryForm="tempPropParams"
          @createOrder="createOrder"
        />
      </el-tab-pane>
      <el-tab-pane label="待处理" name="third">
        <ToDealTable
          ref="toDealTable"
          v-if="tableKey === 'toDeal'"
          :queryForm="tempPropParams"
          @createOrder="createOrder"
        />
      </el-tab-pane>
      <el-tab-pane label="待接收" name="forth">
        <HoldTable
          ref="holdTable"
          v-if="tableKey === 'hold'"
          :queryForm="tempPropParams"
          @createOrder="createOrder"
        />
      </el-tab-pane>
      <!-- <el-tab-pane label="已完成" name="fifth">
            <FinishTable ref="finishTable" v-if="tableKey==='finish'" :queryForm="queryParams" />
          </el-tab-pane> -->
    </el-tabs>
    <!-- 创建工单对话框 -->
    <el-dialog
      title="创建工单"
      :visible.sync="dialogVisible"
      @close="closeCreateOrderDialog"
      :close-on-click-modal="false"
      width="1000px"
      top="8vh"
    >
      <CreateOrder
        @cancelDialog="closeCreateOrderDialog"
        @freshTable="freshTable"
        v-if="dialogVisible"
        :flowSource="flowSource"
      />
    </el-dialog>
  </div>
</template>
<script>
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import IconSelect from "@/components/IconSelect";
import { treeselect } from "@/api/system/dept";
import ToDealTable from "./components/toDealTable.vue";
import AllTable from "./components/allTable.vue";
import OweTable from "./components/oweTable.vue";
import HoldTable from "./components/holdTable.vue";
import CreateOrder from "./components/createOrder.vue";
import FinishTable from "./components/finishTable.vue";
import { workOrderTypeList } from "@/api/orderScheduling/workStation";
import { getUserAvailUsers } from "@/api/process/lfDesign";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
export default {
  components: {
    AllTable,
    OweTable,
    ToDealTable,
    HoldTable,
    CreateOrder,
    Treeselect,
    IconSelect,
    FinishTable,
    GridTable,
    AdvancedForm,
  },
  data() {
    return {
      queryParams: {
        daterange: [],
        flowTypes: [],
        handleUser: "",
        urgentLevel: "",
        flowStatus: "1",
      },
      flowSource:
        location.href.indexOf("zaixian") == -1
          ? location.href.indexOf("order/workStation") == -1
            ? "hotLine"
            : ""
          : "onLine",
      deptOptions: [], //tree列表
      deptIds: [this.$store.getters.deptId], //选中的tree
      // 遮罩层
      loading: false,
      activeName: "first",
      tableKey: "all",
      orderTypeOptions: [],
      handleUserOptions: [],
      urgentLevelOptions: [],
      orderStatusOptions: [],
      dialogVisible: false,
      pageInitQuery: false,
      config: [],
      tableId: "roleTable",
      columns: [
        {
          field: "roleId",
          title: "角色编号",
          width: 200,
        },
      ],
      tempPropParams: {}, //组件搜索条件
    };
  },
  async created() {
    // await this.getTreeselect();
    this.urgentLevelOptions = [
      { id: "0", label: "不紧急" },
      { id: "1", label: "正常" },
      { id: "2", label: "紧急" },
      {
        id: "3",
        label: "非常紧急",
      },
    ];

    this.orderStatusOptions = [
      { id: "1", label: "进行中" },
      { id: "2", label: "已完结" },
      { id: "3", label: "已作废" },
    ];

    Promise.all([this.getUserAvailUsers(), this.workOrderTypeList()]).then(
      () => {
        setTimeout(() => {
          this.initConfig();
        }, 500);
        this.freshTable(this.queryParams);
      }
    );
    //
    this.tempPropParams = this._.cloneDeep(this.queryParams);
  },
  methods: {
    //初始化
    initConfig() {
      this.config = [
        {
          key: "daterange",
          title: "创建日期",
          type: "dateRange",
        },
        {
          key: "typeId",
          title: "流程类型",
          type: "select",
          options: this.orderTypeOptions,
          optionLabel: "typeLabel",
          optionValue: "typeCode",
          placeholder: "请选择流程类型",
        },
        {
          key: "emergencyLevel",
          title: "紧急程度",
          type: "select",
          placeholder: "请选择紧急程度",
          options: this.urgentLevelOptions,
          optionLabel: "label",
          optionValue: "id",
        },
        {
          key: "businessNo",
          title: "工单编号",
          type: "input",
          placeholder: "请输入工单编号",
        },
      ];

      if (this.tableKey === "all" || this.tableKey === "owe") {
        const operatorIdConfig = {
          key: "operatorId",
          title: "处理人",
          type: "select",
          placeholder: "请选择处理人",
          options: this.handleUserOptions,
          optionLabel: "nickName",
          optionValue: "userId",
        };
        const flowStatusConfig = {
          key: "flowStatus",
          title: "工单状态",
          type: "select",
          placeholder: "请选择工单状态",
          options: this.orderStatusOptions,
          optionLabel: "label",
          optionValue: "id",
        };
        if (this.queryParams.flowStatus === "1") {
          this.config.splice(2, 0, operatorIdConfig);
          this.config.splice(4, 0, flowStatusConfig);
        } else {
          this.config.splice(3, 0, flowStatusConfig);
        }
      }
    },
    // 处理人
    getUserAvailUsers() {
      return getUserAvailUsers({
        pageNum: 1,
        pageSize: 9999,
      }).then((response) => {
        if (response.data) {
          this.handleUserOptions = response.data;
        }
      });
    },
    //流程类型
    workOrderTypeList() {
      return workOrderTypeList({ typeStatus: "1" }).then((response) => {
        this.orderTypeOptions = response.data;
      });
    },
    flowStatusChange(value) {
      this.freshTable();
    },
    /** 查询部门下拉树结构 */
    async getTreeselect() {
      await treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    handleTabClick(tab) {
      if (tab.name === "first") {
        this.tableKey = "all";
      } else if (tab.name === "second") {
        this.tableKey = "owe";
      } else if (tab.name === "third") {
        this.tableKey = "toDeal";
      } else if (tab.name === "forth") {
        this.tableKey = "hold";
      } else if (tab.name === "fifth") {
        this.tableKey = "finish";
      }
      this.initConfig();
    },
    createOrder() {
      this.dialogVisible = true;
    },
    freshTable() {
      this.tempPropParams.pageNum = 1
      this.tempPropParams.pageSize = this.queryParams.pageSize
      if (this.queryParams.flowStatus !== "1") {
        this.tempPropParams.operatorId = "";
      }
      if (this.activeName === "first") {
        this.$nextTick(() => {
          this.$refs.allTable.handleQuery();
        });
      } else if (this.activeName === "second") {
        this.$nextTick(() => {
          this.$refs.oweTable.handleQuery();
        });
      } else if (this.activeName === "third") {
        this.$nextTick(() => {
          this.$refs.toDealTable.handleQuery();
        });
      } else if (this.activeName === "forth") {
      
        this.$nextTick(() => {
          this.$refs.holdTable.handleQuery();
        });
      } else if (this.activeName === "fifth") {
        this.$nextTick(() => {
          this.$refs.finishTable.handleQuery();
        });
      }
    },
    closeCreateOrderDialog() {
      this.dialogVisible = false;
      this.freshTable();
    },
    handleQuery(params) {
      this.queryParams.pageNum = 1
      if (params) {
        params.pageSize = this.queryParams.pageSize
      }
      this.tempPropParams = params;
      this.freshTable();
    },
    resetQuery(params) {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.$nextTick(() => {
        this.$refs.AdvancedForm.resetParams();
      });
      this.tabPrams = {};
      this.tempPropParams = params;
      this.freshTable(this.tempPropParams);
    },
    //分页切换
    changePage() {
      if(this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum
        this.finallySearch.pageSize = this.queryParams.pageSize
      }
      this.freshTable(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-form-item--small .el-form-item__content,
.el-form-item--small .el-form-item__label {
  line-height: 12px;
}

.app-container {
  /deep/ .el-input-group--append {
    display: flex !important;
  }

  /deep/ .el-input-group__append {
    padding: 0;
    width: 50px;
    display: flex;
    justify-content: center;
  }

  .div_bottom {
    padding: 1em;
  }
}
</style>

