<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="pileCode" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showDetail(row)">
            {{ row.pileCode }}
          </el-button>
        </template>
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleCreate"
            v-has-permi="['chargingPile:info:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['chargingPile:info:detail']"
          >
            详情
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleUpdate(row)"
            v-has-permi="['chargingPile:info:edit']"
          >
            编辑
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <detail
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :row-info="rowInfo"
      :title="title"
      :type="type"
      @update-list="getList"
    >
    </detail>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import Detail from "@/views/chargingPile/components/detail.vue";
import { queryPileInfoByPage } from "@/api/chargingPile/pile";

export default {
  name: "pileList",
  components: { AdvancedForm, GridTable, Detail },
  data() {
    return {
      config: [],
      columns: [
        {
          field: "pileCode",
          title: "桩编号",
          treeNode: true,
          slots: { default: "pileCode" },
        },
        {
          field: "pileName",
          title: "桩名称",
          showOverflowTooltip: true,
        },
        {
          field: "assetCode",
          title: "资产编码",
          showOverflowTooltip: true,
        },
        {
          field: "brandName",
          title: "桩品牌",
          customWidth: 200,
          showOverflowTooltip: true,
        },
        {
          field: "modelName",
          title: "桩型号",
          showOverflowTooltip: true,
        },
        {
          field: "stationCode",
          title: "所属站点",
          showOverflowTooltip: true,
        },
        {
          field: "stationName",
          title: "站点名称",
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "pileList",
      detailVisible: false,
      rowInfo: {},
      title: "详情",
      type: "detail",
      pileBrandDict: [],
    };
  },
  async created() {
    await this.getDicts("cm_pile_brand").then((response) => {
      this.pileBrandDict = response?.data;
    });
    this.initConfig();
    this.getList();
  },
  mounted() {},
  methods: {
    handleCreate() {
      this.title = "新增";
      this.type = "add";
      this.detailVisible = true;
    },
    handleUpdate(row) {
      this.rowInfo = row;
      this.title = "编辑";
      this.type = "update";
      this.detailVisible = true;
    },
    showDetail(row) {
      this.rowInfo = row;
      this.title = "详情";
      this.type = "detail";
      this.detailVisible = true;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      queryPileInfoByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "pileCode",
          title: "桩编号",
          type: "input",
          placeholder: "请填写桩编号",
        },
        {
          key: "pileName",
          title: "桩名称",
          type: "input",
          placeholder: "请填写桩名称",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
      ];
    },
    //桩品牌转换
    pileBrandFormat({ cellValue }) {
      return this.selectDictLabel(this.pileBrandDict, cellValue);
    },
  },
  watch: {
    $route: {
      handler(newVal) {
        if (newVal.query.stationId) {
          this.searchForm.stationId = newVal.query.stationId;
          this.searchForm.roadTestFlag = newVal.query.roadTestFlag;
          this.searchForm.stationName = newVal.query.stationName;
        }
      },
      immediate: true,
    },
  },
};
</script>

<style scoped></style>
