<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    @close="closeDialog"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
  >
    <el-form :model="form" ref="form" label-width="110px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="站点名称" prop="stationId">
            <el-select
              v-model="form.stationId"
              filterable
              placeholder="请选择站点"
              clearable
              size="mini"
              style="width: 100%"
              :disabled="type === 'detail'"
              filterable
            >
              <el-option
                v-for="dict in stationList"
                :key="dict.stationId"
                :label="dict.stationName"
                :value="dict.stationId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩编码" prop="pileCode">
            <el-input
              placeholder="请输入桩编码"
              v-model="form.pileCode"
              size="mini"
              style="width: 100%;"
              :disabled="type === 'detail'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩名称" prop="pileName">
            <el-input
              placeholder="请输入桩名称"
              v-model="form.pileName"
              size="mini"
              style="width: 100%;"
              :disabled="type === 'detail'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产编码" prop="assetCode">
            <el-input
              placeholder="请输入资产编码"
              v-model="form.assetCode"
              size="mini"
              style="width: 100%;"
              :disabled="type === 'detail'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩品牌" prop="pileBrandId">
            <el-select
              v-model="form.pileBrandId"
              placeholder="桩品牌"
              clearable
              size="mini"
              style="width: 100%"
              :disabled="type === 'detail'"
              filterable
              :filter-method="getBrandList"
            >
              <el-option
              v-for="item in pileBrandList"
              :key="item.brandId"
              :label="item.brandName"
              :value="item.brandId"
            />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩型号" prop="modelName">
            <el-select
              v-model="form.pileModelId"
              placeholder="桩型号"
              clearable
              size="mini"
              style="width: 100%"
              :disabled="type === 'detail'"
              filterable
              :filter-method="getModelList"
            >
              <el-option
                v-for="item in pileModelList"
                :key="item.modelId"
                :label="item.modelName"
                :value="item.modelId"
              />
            </el-select>

          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运营状态" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择运营状态"
              clearable
              size="mini"
              style="width: 100%"
              :disabled="type === 'detail'"
            >
              <el-option
                v-for="dict in pileStatusDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投运时间" prop="operDate">
            <el-date-picker
              v-model="form.operDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              size="mini"
              style="width: 100%"
              :disabled="type === 'detail'"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="额定功率" prop="ratePower">
            <el-input
              placeholder="请输入额定功率"
              v-model="form.ratePower"
              size="mini"
              style="width: 100%;"
              :disabled="type === 'detail'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩通讯地址" prop="pilePostalAddress">
            <el-input
              placeholder="请输入桩通讯地址"
              v-model="form.pilePostalAddress"
              size="mini"
              style="width: 100%;"
              :disabled="type === 'detail'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否对外开放" prop="openFlag">
            <el-select
              v-model="form.openFlag"
              placeholder="是否对外开放"
              clearable
              size="mini"
              style="width: 100%"
              :disabled="type === 'detail'"
            >
              <el-option
                v-for="dict in openFlagDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桩备注" prop="remark">
            <el-input
              placeholder="请输入桩备注"
              v-model="form.remark"
              size="mini"
              style="width: 100%;"
              :disabled="type === 'detail'"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click="submitForm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { addPile, brandList, modelList, updatePile } from "@/api/chargingPile/pile";
import { queryStationInfoByPage } from "@/api/station/station";

export default {
  name: "detail",
  props: ["visible", "rowInfo", "type", "title"],
  data() {
    return {
      form: {},
      rules: {
        pileCode: [
          { required: true, message: "请输入充电桩编号", trigger: "blur" }
        ],
        stationId: [
          { required: true, message: "请选择站点信息", trigger: "change" }
        ]
      },
      pileBrandList: [],
      pileModelList: [],
      openFlagDict: [],
      pileStatusDict: [],
      stationList: []
    };
  },
  async created() {
    await this.getBrandList('')
    await this.getModelList('')
    await this.getDicts("cm_open_flag").then(response => {
      this.openFlagDict = response?.data;
    });
    await this.getDicts("cm_pile_status").then(response => {
      this.pileStatusDict = response?.data;
    });
    await this.queryStationList();
    if (this.type !== "add") {
      this.form = this.rowInfo;
    }
  },
  methods: {
    async getBrandList(name) {
      const param =  {brandName: name}
      await brandList(param).then(response => {
        this.pileBrandList = response?.data;
      });
    },
    async getModelList(name) {
      const param =  {modelName: name}
      await modelList(param).then(response => {
        this.pileModelList = response?.data;
      });
    },
    closeDialog() {
      this.$emit("update:rowInfo", {});
      this.$emit("update:visible", false);
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.type === "add") {
            addPile(this.form).then(res => {
              if (res?.success) {
                this.closeDialog();
                this.$emit("update-list");
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          } else if (this.type === "update") {
            updatePile(this.form).then(res => {
              if (res?.success) {
                this.closeDialog();
                this.$emit("update-list");
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        }
      });
    },
    async queryStationList() {
      let params = {
        pageNum: 1,
        pageSize: 99999
      };
      await queryStationInfoByPage(params).then(res => {
        if (res?.success) {
          this.stationList = res.data;
        }
      });
    }
  }
};
</script>

<style scoped>

</style>