<!-- 合伙人账单 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      @rowDel="deleteRowHandler"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['destinationCharge:partnerBill:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['destinationCharge:partnerBill:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['destinationCharge:partnerBill:add']"
          >新增</el-button
        >
        <!-- <el-button
              type="primary"
              @click="handleBatchAdd"
              v-has-permi="['destinationCharge:partnerBill:batchAdd']"
              >批量新增</el-button
            > -->
      </template>

      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{}">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/destinationCharge/partnerBill/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
export default {
  name: "destinationPartnerBill",
  components: { BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/st/stationSettlementAnalysis/import",
        url: "/charging-maintenance-ui/static/合伙人账单导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "partnerBillId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "upload",
      //buse参数-e
      recordList: [],
      isEdit: false,

      // 字典数据
      feeTypeOptions: [], // 费用类型字典
      settlementStatusOptions: [], // 结算状态字典
      serviceInvoiceCategoryOptions: [], // 平台服务费开票类目字典
      serviceInvoiceRateOptions: [], // 平台服务费开票税率字典
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取费用类型字典
    this.getDicts("fee_type").then((response) => {
      this.feeTypeOptions = response.data;
    });

    // 获取结算状态字典
    this.getDicts("settlement_status").then((response) => {
      this.settlementStatusOptions = response.data;
    });

    // 获取平台服务费开票类目字典
    this.getDicts("service_invoice_category").then((response) => {
      this.serviceInvoiceCategoryOptions = response.data;
    });

    // 获取平台服务费开票税率字典
    this.getDicts("service_invoice_rate").then((response) => {
      this.serviceInvoiceRateOptions = response.data;
    });

    // 获取下拉列表数据
    // api.getDropLists().then((res) => {
    //   if (res.success) {
    //     // 处理下拉列表数据
    //     if (res.data.institutionName) {
    //       this.institutionNameOptions = res.data.institutionName.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //     if (res.data.serviceProvider) {
    //       this.serviceProviderOptions = res.data.serviceProvider.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //   }
    // });

    // this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    checkPermission,

    handleClose() {
      this.$refs.crud.switchModalView(false);
    },

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },

    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.exportData, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billingPeriodRange",
          title: "账单周期",
          startFieldName: "startBillingPeriod",
          endFieldName: "endBillingPeriod",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:update
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          partnerBillId: row.partnerBillId,
        };
        api.remove(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },

    handleLog(row) {
      queryLog({ businessId: row.partnerBillId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "salesContractCode",
          title: "销售合同编码",
          width: 180,
        },
        {
          field: "customerName",
          title: "客户名称",
          width: 120,
        },
        {
          field: "billingPeriod",
          title: "账期",
          width: 120,
        },
        {
          field: "totalPeriods",
          title: "总期数",
          width: 120,
        },
        {
          field: "paymentPeriods",
          title: "支付期数",
          width: 120,
        },
        {
          field: "gunCount",
          title: "枪数",
          width: 120,
        },
        {
          field: "unitPrice",
          title: "单价（元/枪）",
          width: 120,
        },
        {
          field: "amount",
          title: "金额",
          width: 120,
        },
        {
          field: "feeTypeName",
          title: "费用类型",
          width: 120,
        },
        {
          field: "settlementStatusName",
          title: "结算状态",
          width: 120,
        },
        {
          field: "serviceInvoiceCategoryName",
          title: "平台服务费开票类目",
          width: 120,
        },
        {
          field: "serviceInvoiceRateName",
          title: "平台服务费开票税率",
          width: 120,
        },
        {
          field: "remarks",
          title: "备注",
          width: 120,
        },
        {
          field: "creatorName",
          title: "创建人",
          width: 120,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "salesContractCode",
            element: "el-input",
            title: "合同编码",
          },
          {
            field: "feeType",
            element: "el-select",
            title: "费用类型",
            props: {
              options: this.feeTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "settlementStatus",
            element: "el-select",
            title: "结算状态",
            props: {
              options: this.settlementStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "billingPeriodRange",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },

          {
            field: "customerName",
            element: "el-input",
            title: "客户名称",
          },
          {
            field: "serviceInvoiceCategory",
            element: "el-select",
            title: "开票类目",
            props: {
              options: this.serviceInvoiceCategoryOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["destinationCharge:partnerBill:edit"]),
        delBtn: checkPermission(["destinationCharge:partnerBill:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "60%",
        formConfig: [
          {
            field: "salesContractCode",
            title: "销售合同编码",
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "customerName",
            title: "客户名称",
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "billingPeriod",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "totalPeriods",
            title: "总期数（期）",
            element: "el-input-number",
            props: {
              precision: 0,
            },
          },
          {
            field: "paymentPeriods",
            title: "支付期数（期）",
            element: "el-input-number",
            props: {
              precision: 0,
            },
          },
          {
            field: "gunCount",
            title: "枪数",
            element: "el-input-number",
            props: {
              precision: 0,
            },
          },
          {
            field: "unitPrice",
            title: "单价（元/枪）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
          },
          {
            field: "amount",
            title: "金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
          },
          {
            field: "feeType",
            title: "费用类型",
            element: "el-select",
            props: {
              options: this.feeTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "settlementStatus",
            title: "结算状态",
            element: "el-select",
            props: {
              options: this.settlementStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "serviceInvoiceCategory",
            title: "平台服务费开票类目",
            element: "el-select",
            props: {
              options: this.serviceInvoiceCategoryOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "serviceInvoiceRate",
            title: "平台服务费开票税率（%）",
            element: "el-select",
            props: {
              options: this.serviceInvoiceRateOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },

          {
            field: "remarks",
            title: "备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: () => {
              return checkPermission(["destinationCharge:partnerBill:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "200px",
        },
      };
    },
  },
};
</script>

<style></style>
