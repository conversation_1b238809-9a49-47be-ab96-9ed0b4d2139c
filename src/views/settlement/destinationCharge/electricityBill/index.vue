<!-- 电费账单 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowEdit="rowEdit"
      @rowDel="deleteRowHandler"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['destinationCharge:electricityBill:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['destinationCharge:electricityBill:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['destinationCharge:electricityBill:add']"
          >新增</el-button
        >
        <!-- <el-button
            type="primary"
            @click="handleBatchAdd"
            v-has-permi="['destinationCharge:electricityBill:batchAdd']"
            >批量新增</el-button
          > -->
      </template>

      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/destinationCharge/settleAnalysis/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryCityTree } from "@/api/common.js";
import { queryLog } from "@/api/common.js";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
export default {
  name: "destinationElectricityBill",
  components: { BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/st/stationSettlementAnalysis/import",
        url: "/charging-maintenance-ui/static/场站结算情况分析导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "upload",
      //buse参数-e
      recordList: [],
      businessTypeOptions: [],
      onlineStatusOptions: [],
      serviceProviderOptions: [],
      institutionNameOptions: [],
      companyNameOptions: [],
      customerNameOptions: [],
      fileTypeOptions: [
        { dictLabel: "收入", dictValue: "income" },
        { dictLabel: "支出", dictValue: "expense" },
      ],

      // 文件预览相关状态
      showFilePreview: false,
      previewFileList: [],
      typeOptions: [],
      isEdit: false,
      regionData: [],

      // 字典数据
      operationStatusOptions: [], // 运营状态字典
      stationChargeTypeOptions: [], // 站点充电类型字典
      settlementCycleOptions: [], // 结算周期字典
      settlementTypeOptions: [], // 结算类型字典
      deptOptionList: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取运营状态字典
    this.getDicts("cm_station_status").then((response) => {
      this.operationStatusOptions = response.data;
    });

    // 获取站点充电类型字典
    this.getDicts("cm_station_charge_type").then((response) => {
      this.stationChargeTypeOptions = response.data;
    });

    // 获取结算周期字典
    this.getDicts("settlement_cycle").then((response) => {
      this.settlementCycleOptions = response.data;
    });

    // 获取结算类型字典
    this.getDicts("settlement_type").then((response) => {
      this.settlementTypeOptions = response.data;
    });
    this.getCityRegionData();
    this.getDeptList();
    // 获取下拉列表数据
    // api.getDropLists().then((res) => {
    //   if (res.success) {
    //     // 处理下拉列表数据
    //     if (res.data.institutionName) {
    //       this.institutionNameOptions = res.data.institutionName.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //     if (res.data.serviceProvider) {
    //       this.serviceProviderOptions = res.data.serviceProvider.map(
    //         (item) => ({
    //           dictLabel: item,
    //           dictValue: item,
    //         })
    //       );
    //     }
    //   }
    // });

    // this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    checkPermission,
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    handleClose() {
      this.$refs.crud.switchModalView(false);
    },

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },

    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.exportData, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "onlineRangeTime",
          title: "上线日期",
          startFieldName: "onlineStartDate",
          endFieldName: "onlineEndDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    async loadData() {
      const { region } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      region?.[0] && (params.province = region[0] + "0000");
      region?.[1] && (params.city = region[1] + "00");
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.queryList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:update
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.remove(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("删除成功");
            this.loadData();
          }
        });
      });
    },

    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },

    // 获取站点数据的方法，用于 PageSelector 组件
    async fetchStationData(params, keyName) {
      try {
        const searchParams = {
          pageNum: params.pageNum,
          pageSize: params.pageSize,
          [keyName]: params.searchText,
        };

        const res = await api.queryStationList(searchParams);
        return {
          data: res.data || [],
          total: res.total || 0,
        };
      } catch (error) {
        console.error("获取站点数据失败:", error);
        return {
          data: [],
          total: 0,
        };
      }
    },

    // 站点编号选择变化处理
    handleStationCodeChange({ value, options }) {
      const selectedStation = options.find((item) => item.stationNo === value);
      if (selectedStation) {
        // 自动填充站点名称和基础信息
        this.$refs.crud.setFormFields({
          stationCode: value,
          stationName: selectedStation.stationName,
          // 填充站点基础信息（根据接口文档字段映射）
          orgNo: selectedStation.orgNo,
          belongPlace: selectedStation.belongPlace,
        });
      }
    },

    // 站点名称选择变化处理
    handleStationNameChange({ value, options }) {
      const selectedStation = options.find(
        (item) => item.stationName === value
      );
      if (selectedStation) {
        // 自动填充站点编号和基础信息
        this.$refs.crud.setFormFields({
          stationName: value,
          stationCode: selectedStation.stationNo, // 接口返回stationNo字段
          // 填充站点基础信息（根据接口文档字段映射）
          orgNo: selectedStation.orgNo,
          belongPlace: selectedStation.belongPlace,
        });
      }
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "stationCode",
          title: "站点编号",
          width: 180,
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 120,
        },
        {
          field: "stationType",
          title: "省份",
          width: 120,
        },
        {
          field: "construction",
          title: "市",
          width: 120,
        },
        {
          field: "stationChargeType",
          title: "所属区域",
          width: 120,
        },
        {
          field: "operationStatus",
          title: "负责人",
          width: 120,
        },
        {
          field: "openDate",
          title: "结算类型（场地方、供电单位）",
          width: 120,
        },
        {
          field: "onlineDate",
          title: "账单周期",
          width: 120,
        },
        {
          field: "pileNum",
          title: "场地方电量（kWh）",
          width: 120,
        },
        {
          field: "settlementCycle",
          title: "场地方电费（元）",
          width: 120,
        },
        {
          field: "orgNo",
          title: "平台充电电量（kWh）",
          width: 120,
        },
        {
          field: "managerName",
          title: "平台充电电费（元）",
          width: 120,
        },
        {
          field: "province",
          title: "电损=（场地方电量-充电电量）/场地方电量",
          width: 120,
        },
        {
          field: "city",
          title: "电费结算金额（元）",
          width: 120,
        },
        {
          field: "settlementType",
          title: "电费偏差",
          width: 120,
        },
        {
          field: "lastMeterTime",
          title: "特殊情况备注",
          width: 120,
        },
        {
          field: "siteElectricity",
          title: "电费结算金额-场地方电费（元）",
          width: 120,
        },
        {
          field: "creatorName",
          title: "创建人",
          width: 120,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "站点编号",
          },
          {
            field: "orgNo",
            element: "el-select",
            title: "所属区域",
            props: {
              options: this.deptOptionList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "region",
            title: "省市",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              props: {
                checkStrictly: false,
                multiple: false,
                value: "areaCode",
                label: "areaName",
              },
              options: this.regionData, //省市数据,
            },
          },
          {
            field: "onlineRangeTime",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "stationName",
            element: "page-autocomplete",
            title: "站点名称",
            props: {
              clearable: true,
              optionValue: "stationName",
              optionLabel: "stationName",
              fetchMethod: (params) => {
                return this.remoteStationOptions(params, "stationName");
              },
            },
          },
          {
            field: "manager",
            element: "el-input",
            title: "负责人",
          },
          {
            field: "operationStatus",
            element: "el-select",
            title: "结算类型",
            props: {
              options: this.operationStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["destinationCharge:electricityBill:edit"]),
        delBtn: checkPermission(["destinationCharge:electricityBill:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "60%",
        formConfig: [
          {
            field: "stationCode",
            title: "站点编号",
            element: "page-selector",
            rules: [{ required: true, message: "请选择站点编号" }],
            props: {
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationNo");
              },
              optionValue: "stationNo", // 接口返回stationNo字段
              optionLabel: "stationNo", // 显示stationNo字段
              filterable: true,
              placeholder: "请选择站点编号",
            },
            on: {
              change: this.handleStationCodeChange,
            },
            preview: this.isEdit,
          },
          {
            field: "stationName",
            title: "站点名称",
            element: "page-selector",
            rules: [{ required: true, message: "请选择站点名称" }],
            props: {
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationName");
              },
              optionValue: "stationName", // 显示的值
              optionLabel: "stationName", // 选中的值
              filterable: true,
              placeholder: "请选择站点名称",
            },
            on: {
              change: this.handleStationNameChange,
            },
            preview: this.isEdit,
          },
          {
            field: "orgNo",
            title: "所属区域",
            preview: true,
          },
          {
            field: "belongPlace",
            title: "省市",
            preview: true,
          },
          {
            field: "settlementType",
            title: "结算类型",
            element: "el-select",
            props: {
              options: this.settlementTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "manager",
            title: "负责人",
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "lastMeterTime",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "siteElectricity",
            title: "场地方电量（kWh）",
            element: "el-input-number",
            props: {
              precision: 3,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "siteFee",
            title: "场地方电费（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "platformCharge",
            title: "平台充电电量（kWh）",
            element: "el-input-number",
            props: {
              precision: 3,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "platformFee",
            title: "平台充电电费（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "electricalLoss",
            title: "电损（%）",
            element: "el-input-number",
            props: {
              precision: 3,
            },
          },
          {
            field: "feeSettle",
            title: "电费结算金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "feeDeviation",
            title: "电费偏差（%）",
            element: "el-input-number",
            props: {
              precision: 3,
            },
          },
          {
            field: "remark",
            title: "特殊情况备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["destinationCharge:electricityBill:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "200px",
        },
      };
    },
  },
};
</script>

<style></style>
