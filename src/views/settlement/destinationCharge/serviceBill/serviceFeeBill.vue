<!-- 服务费账单 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['serviceBill:serviceFeeBill:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <!-- <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select> -->
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['serviceBill:serviceFeeBill:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['serviceBill:serviceFeeBill:add']"
          >新增</el-button
        >
        <!-- <el-button
            type="primary"
            @click="handleBatchAdd"
            v-has-permi="['serviceBill:serviceFeeBill:batchAdd']"
            >批量新增</el-button
          > -->
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入分润进度"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/charge/profitProgress.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import Timeline from "@/components/Timeline/index.vue";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { queryLog } from "@/api/common.js";
export default {
  name: "serviceFeeBill",
  mixins: [exportMixin],
  components: {
    Timeline,
    BatchUpload,
  },
  data() {
    return {
      workLoading: false,
      uploadObj: {
        api: "/st/newcharge/profit/importExcel",
        url: "/charging-maintenance-ui/static/服务费账单导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      isEdit: false,
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
      reconciliationPersonOptions: [],
      recordList: [],
      costTypeOptions: [{ dictLabel: "前端模拟", dictValue: "1" }],
      billReviewOptions: [],
      returnCompleteOptions: [],
      counterpartySealOptions: [],
      ourSealOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取下拉列表数据
    this.loadDropListData();
  },
  methods: {
    checkPermission,

    handleBatchAdd() {},

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },

    // 加载下拉列表数据
    async loadDropListData() {
      try {
        const res = await api.getDropLists();
        if (res.success && res.data) {
          if (res.success) {
            // 处理下拉列表数据
            if (res.data.reconciliationPerson) {
              this.reconciliationPersonOptions = res.data.reconciliationPerson.map(
                (item) => ({
                  dictLabel: item,
                  dictValue: item,
                })
              );
            }
          }
        }
      } catch (error) {
        console.error("获取下拉列表数据失败:", error);
      }
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "onlineRangeTime",
          title: "账单周期",
          startFieldName: "billYearMonthStart",
          endFieldName: "billYearMonthEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:update
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    async remoteStationOptions(params, key) {
      const { searchText, ...rest } = params;
      const res = await api.queryStationList({
        ...rest,
        [key]: searchText,
      });
      return {
        data: res.data,
        total: res.total,
      };
    },
    // 获取站点数据的方法，用于 PageSelector 组件
    async fetchStationData(params, keyName) {
      try {
        const searchParams = {
          pageNum: params.pageNum,
          pageSize: params.pageSize,
          [keyName]: params.searchText,
        };

        const res = await api.queryStationList(searchParams);
        return {
          data: res.data || [],
          total: res.total || 0,
        };
      } catch (error) {
        console.error("获取站点数据失败:", error);
        return {
          data: [],
          total: 0,
        };
      }
    },
    // 站点编号选择变化处理
    handleStationCodeChange({ value, options }) {
      const selectedStation = options.find((item) => item.stationNo === value);
      if (selectedStation) {
        // 自动填充站点名称和基础信息
        this.$refs.crud.setFormFields({
          stationCode: value,
          stationName: selectedStation.stationName,
          // 填充站点基础信息（根据接口文档字段映射）
          orgNo: selectedStation.orgNo,
          belongPlace: selectedStation.belongPlace,
        });
      }
    },

    // 站点名称选择变化处理
    handleStationNameChange({ value, options }) {
      const selectedStation = options.find(
        (item) => item.stationName === value
      );
      if (selectedStation) {
        // 自动填充站点编号和基础信息
        this.$refs.crud.setFormFields({
          stationName: value,
          stationCode: selectedStation.stationNo, // 接口返回stationNo字段
          // 填充站点基础信息（根据接口文档字段映射）
          orgNo: selectedStation.orgNo,
          belongPlace: selectedStation.belongPlace,
        });
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "billYearMonth",
          title: "站点编号",
          width: 120,
        },
        {
          field: "operator",
          title: "站点名称",
          width: 150,
        },
        {
          field: "costType",
          title: "省份",
          width: 120,
        },
        {
          field: "sharedIncome",
          title: "市",
          width: 120,
        },
        {
          field: "returnAmount",
          title: "所属区域",
          width: 120,
        },
        {
          field: "reconciliationPerson",
          title: "负责人",
          width: 100,
        },
        {
          field: "billReview",
          title: "结算类型",
          width: 100,
        },
        {
          field: "counterpartySeal",
          title: "账单周期",
          width: 100,
        },
        {
          field: "ourSeal",
          title: "服务费应结算金额（元）",
          width: 100,
        },
        {
          field: "returnComplete",
          title: "服务费计算标准",
          width: 100,
        },
        {
          field: "remarks",
          title: "服务费实际结算金额（元）",
          width: 150,
        },
        {
          field: "mode",
          title: "结算进度",
          width: 100,
        },
        {
          field: "mode",
          title: "结算备注",
          width: 100,
        },
        {
          field: "updateBy",
          title: "创建人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "创建时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "站点编号",
          },
          {
            field: "orgNo",
            element: "el-select",
            title: "所属区域",
            props: {
              options: this.deptOptionList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "region",
            title: "省市",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              props: {
                checkStrictly: false,
                multiple: false,
                value: "areaCode",
                label: "areaName",
              },
              options: this.regionData, //省市数据,
            },
          },
          {
            field: "onlineRangeTime",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "stationName",
            element: "page-autocomplete",
            title: "场站名称",
            props: {
              clearable: true,
              optionValue: "stationName",
              optionLabel: "stationName",
              fetchMethod: (params) => {
                return this.remoteStationOptions(params, "stationName");
              },
            },
          },
          {
            field: "manager",
            element: "el-input",
            title: "负责人",
          },
          {
            field: "operationStatus",
            element: "el-select",
            title: "结算进度",
            props: {
              options: this.operationStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "operationStatus",
            element: "el-select",
            title: "结算类型",
            props: {
              options: this.operationStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "stationCode",
            title: "站点编号",
            element: "page-selector",
            rules: [{ required: true, message: "请选择站点编号" }],
            props: {
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationNo");
              },
              optionValue: "stationNo", // 接口返回stationNo字段
              optionLabel: "stationNo", // 显示stationNo字段
              filterable: true,
              placeholder: "请选择站点编号",
            },
            on: {
              change: this.handleStationCodeChange,
            },
            preview: this.isEdit,
          },
          {
            field: "stationName",
            title: "站点名称",
            element: "page-selector",
            rules: [{ required: true, message: "请选择站点名称" }],
            props: {
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationName");
              },
              optionValue: "stationName", // 显示的值
              optionLabel: "stationName", // 选中的值
              filterable: true,
              placeholder: "请选择站点名称",
            },
            on: {
              change: this.handleStationNameChange,
            },
            preview: this.isEdit,
          },
          {
            field: "orgNo",
            title: "所属区域",
            preview: true,
          },
          {
            field: "belongPlace",
            title: "省市",
            preview: true,
          },
          {
            field: "settlementCycle",
            title: "结算进度",
            element: "el-select",
            props: {
              options: this.settlementCycleOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "settlementType",
            title: "结算类型",
            element: "el-select",
            props: {
              options: this.settlementTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "lastMeterTime",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "siteFee",
            title: "服务费应结算金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "platformCharge",
            title: "服务费计算标准（%）",
            element: "el-input-number",
            props: {
              precision: 3,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "platformFee",
            title: "服务费实际结算金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "totalFee",
            title: "服务费结算人员",
          },
          {
            field: "remark",
            title: "结算备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
      };
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["serviceBill:serviceFeeBill:edit"]),
        delBtn: checkPermission(["serviceBill:serviceFeeBill:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "60%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["serviceBill:serviceFeeBill:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "220px",
        },
      };
    },
  },
};
</script>

<style></style>
