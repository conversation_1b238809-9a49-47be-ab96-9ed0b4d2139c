<!-- 场站结算进度 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtStorageEnergy:stationProgress:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtStorageEnergy:stationProgress:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['xdtStorageEnergy:stationProgress:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtStorageEnergy:stationProgress:batchAdd']"
          >批量新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchOperation"
          v-has-permi="['xdtStorageEnergy:stationProgress:batchOperation']"
          >批量操作</el-button
        >
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleResetAll"
      ref="batchUpload"
      title="批量导入场站结算进度"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>

    <!-- 批量新增抽屉 -->
    <BatchAddDrawer
      ref="batchAddDrawer"
      title="批量新增场站结算进度"
      :columns="batchAddColumns"
      @submit="handleBatchAddSubmit"
    />
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/storageEnergy/stationProgress.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import Timeline from "@/components/Timeline/index.vue";
import { queryLog } from "@/api/common.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
import BatchAddDrawer from "./components/BatchAddDrawer.vue";

export default {
  name: "stationProgress",
  mixins: [exportMixin],
  components: {
    Timeline,
    BatchUpload,
    BatchAddDrawer,
  },
  data() {
    return {
      selectPage: "1",
      workLoading: false,
      uploadObj: {
        api: "/st/newcharge/station/importExcel",
        url: "/charging-maintenance-ui/static/场站结算进度导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      isEdit: false,
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
      siteNameOptions: [],
      recordList: [],
      costTypeOptions: [],
      billReviewOptions: [],
      selectedData: [],
      reconciliationPersonOptions: [],
      settlementStatusOptions: [],
      settlementMethodOptions: [],
      investmentModeOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };

    this.getDicts("st_new_charge_station_pay_status").then((response) => {
      this.settlementStatusOptions = response.data;
    });
    this.getDicts("st_new_charge_station_pay_method").then((response) => {
      this.settlementMethodOptions = response.data;
    });
    this.getDicts("st_new_charge_station_invest").then((response) => {
      this.investmentModeOptions = response.data;
    });
    // 获取下拉列表数据
    this.loadDropListData();
  },
  methods: {
    checkPermission,
    handleResetAll() {
      this.loadDropListData();
      this.handleQuery();
    },
    handleBatchOperation() {
      if (!this.selectedData.length) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.operationType = "batchOperation";
      this.$refs.crud.switchModalView(true, "batchOperation", {
        ...initParams(this.modalConfig.formConfig),
        idList: this.selectedData?.map((x) => x.id),
        //todo 选择全部页/当前页 新加一个字段
        selectPage: this.selectPage,
      });
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },

    handleBatchAdd() {
      this.$refs.batchAddDrawer.open();
    },

    // 处理批量新增提交
    async handleBatchAddSubmit(formData) {
      try {
        // 调用批量新增API
        await api.batchAdd(formData);
        this.$message.success("批量新增成功");
        this.$refs.batchAddDrawer.handleClose();
        this.loadData();
      } catch (error) {
        console.error("批量新增失败:", error);
        this.$message.error("批量新增失败，请重试");
      }
    },

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },

    // 加载下拉列表数据
    async loadDropListData() {
      try {
        const res = await api.getDropLists();
        if (res.success && res.data) {
          if (res.success) {
            // 处理下拉列表数据
            if (res.data.siteName) {
              this.siteNameOptions = res.data.siteName.map((item) => ({
                dictLabel: item,
                dictValue: item,
              }));
            }
          }
        }
      } catch (error) {
        console.error("获取下拉列表数据失败:", error);
      }
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billYearMonthRange",
          title: "账单年月",
          startFieldName: "billYearMonthStart",
          endFieldName: "billYearMonthEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType: add/update/batchOperation
      try {
        await api[crudOperationType](params);
        this.$message.success("提交成功");
        this.loadData();
        crudOperationType === "batchOperation" &&
          this.$refs.crud.tableDeselectHandler();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
    //
    querySiteName(queryString, cb) {
      const results = queryString
        ? this.siteNameOptions.filter((item) =>
            item.dictLabel.toLowerCase().includes(queryString.toLowerCase())
          )
        : this.siteNameOptions;

      // 转换为 el-autocomplete 需要的格式
      const suggestions = results.map((item) => ({
        value: item.dictValue,
        label: item.dictLabel,
      }));

      cb(suggestions);
    },
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },

    // 批量新增表格列配置
    batchAddColumns() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          title: "账单月份",
          field: "billYearMonth",
          width: 250,
          isEdit: true,
          element: "el-date-picker",
          rules: [{ required: true, message: "请选择账单年月" }],
          props: {
            type: "month",
            valueFormat: "yyyy-MM",
          },
        },
        {
          title: "运营商",
          field: "operator",
          width: 150,
          isEdit: true,
          element: "el-input",
          rules: [{ required: true, message: "请输入运营商" }],
        },
        {
          title: "站点名称",
          field: "siteName",
          width: 150,
          isEdit: true,
          element: "el-autocomplete",
          props: {
            fetchSuggestions: this.querySiteName,
          },
          rules: [{ required: true, message: "请输入场站名称" }],
        },
        {
          title: "投资模式",
          field: "investmentMode",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.investmentModeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "结算方式",
          field: "settlementMethod",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.settlementMethodOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "折扣（%）",
          field: "discount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 3,
            min: 0,
          },
        },
        {
          title: "充电量（kWh）",
          field: "chargingAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 4,
            min: 0,
          },
        },
        {
          title: "实际充电费（元）",
          field: "actualChargingFee",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "放电量（kWh）",
          field: "dischargingAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 4,
            min: 0,
          },
        },
        {
          title: "实际放电费（元）",
          field: "actualDischargingFee",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "电损率（%）",
          field: "powerLossRate",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 3,
            min: 0,
          },
        },
        {
          title: "收益（元）",
          field: "income",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "应付金额（元）",
          field: "payableAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "应回金额（元）",
          field: "receivableAmount",
          width: 150,
          isEdit: true,
          element: "el-input-number",
          props: {
            precision: 2,
            min: 0,
          },
        },
        {
          title: "出账日期",
          field: "billingDate",
          width: 250,
          isEdit: true,
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          title: "回款日期",
          field: "paymentDate",
          width: 250,
          isEdit: true,
          element: "el-date-picker",
          props: {
            type: "date",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          title: "结算进度",
          field: "settlementStatus",
          width: 120,
          isEdit: true,
          element: "el-select",
          props: {
            options: this.settlementStatusOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
            filterable: true,
          },
        },
        {
          title: "对账负责人",
          field: "reconciliationPerson",
          width: 120,
          isEdit: true,
          element: "el-input",
        },
        {
          title: "备注",
          field: "remarks",
          width: 200,
          isEdit: true,
          element: "el-input",
          props: {
            type: "textarea",
            rows: 2,
            maxlength: 500,
            showWordLimit: true,
            placeholder: "500个字符以内",
          },
        },
      ];
    },
    tableColumn() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          field: "billYearMonth",
          title: "账单年月",
          width: 120,
        },
        {
          field: "operator",
          title: "运营商",
          width: 150,
        },
        {
          field: "siteName",
          title: "场站名称",
          width: 150,
        },
        {
          field: "settlementMethod",
          title: "结算方式",
          width: 120,
        },
        {
          field: "settlementStatus",
          title: "结算状态",
          width: 120,
        },
        {
          field: "investmentMode",
          title: "投资模式",
          width: 120,
        },
        {
          field: "reconciliationPerson",
          title: "对账负责人",
          width: 100,
        },
        {
          field: "remarks",
          title: "备注",
          width: 150,
        },
        {
          field: "updateBy",
          title: "操作人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "操作时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "operator",
            element: "el-input",
            title: "运营商",
          },
          {
            field: "settlementMethod",
            element: "el-select",
            title: "结算方式",
            props: {
              options: this.settlementMethodOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },

          {
            field: "settlementStatus",
            element: "el-select",
            title: "结算进度",
            props: {
              options: this.settlementStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "billYearMonthRange",
            title: "账单年月",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
            },
          },
          {
            field: "siteName",
            element: "el-autocomplete",
            title: "站点名称",
            props: {
              fetchSuggestions: this.querySiteName,
            },
          },
          {
            field: "investmentMode",
            element: "el-select",
            title: "投资模式",
            props: {
              options: this.investmentModeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "reconciliationPerson",
            element: "el-select",
            title: "对账人",
            props: {
              options: this.reconciliationPersonOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "billYearMonth",
            title: "账单月份",
            element: "el-date-picker",
            rules: [{ required: true, message: "请选择账单年月" }],
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
            preview: this.isEdit,
          },
          {
            field: "operator",
            title: "运营商",
            element: "el-input",
            rules: [{ required: true, message: "请输入运营商" }],
            preview: this.isEdit,
          },
          {
            field: "siteName",
            title: "站点名称",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: this.querySiteName,
            },
            rules: [{ required: true, message: "请输入场站名称" }],
          },
          {
            field: "investmentMode",
            title: "投资模式",
            element: "el-select",
            props: {
              options: this.investmentModeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "settlementMethod",
            title: "结算方式",
            element: "el-select",
            props: {
              options: this.settlementMethodOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "discount",
            title: "折扣（%）",
            element: "el-input-number",
            props: {
              precision: 3,
              min: 0,
            },
          },
          {
            field: "chargingAmount",
            title: "充电量（kWh）",
            element: "el-input-number",
            props: {
              precision: 4,
              min: 0,
            },
          },
          {
            field: "actualChargingFee",
            title: "实际充电费（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
          },
          {
            field: "dischargingAmount",
            title: "放电量（kWh）",
            element: "el-input-number",
            props: {
              precision: 4,
              min: 0,
            },
          },
          {
            field: "actualDischargingFee",
            title: "实际放电费（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
          },
          {
            field: "powerLossRate",
            title: "电损率（%）",
            element: "el-input-number",
            props: {
              precision: 3,
              min: 0,
            },
          },
          {
            field: "income",
            title: "收益（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
          },
          {
            field: "payableAmount",
            title: "应付金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
          },
          {
            field: "receivableAmount",
            title: "应回金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
            },
          },
          {
            field: "billingDate",
            title: "出账日期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "paymentDate",
            title: "回款日期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "settlementStatus",
            title: "结算进度",
            element: "el-select",
            props: {
              options: this.settlementStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "reconciliationPerson",
            title: "对账负责人",
            element: "el-input",
          },
          {
            field: "remarks",
            title: "备注",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
        batchOperation: [
          {
            field: "settlementMethod",
            title: "结算方式",
            element: "el-select",
            props: {
              options: this.settlementMethodOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "discount",
            title: "折扣（%）",
            element: "el-input-number",
            props: {
              precision: 3,
              min: 0,
            },
          },
          {
            field: "billingDate",
            title: "出账日期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "paymentDate",
            title: "回款日期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "settlementStatus",
            title: "结算进度",
            element: "el-select",
            props: {
              options: this.settlementStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "reconciliationPerson",
            title: "对账负责人",
            element: "el-input",
          },
        ],
      };
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["xdtStorageEnergy:stationProgress:edit"]),
        delBtn: checkPermission(["xdtStorageEnergy:stationProgress:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["xdtStorageEnergy:stationProgress:log"]);
            },
          },
          //非操作列
          {
            title: "批量操作",
            typeName: "batchOperation",
            slotName: "batchOperation",
            condition: (row) => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "180px",
        },
      };
    },
  },
};
</script>

<style></style>
