<!-- 邦道与服务商账单 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['bangdaoAndService:list:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['bangdaoAndService:list:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchStatus"
          v-has-permi="['bangdaoAndService:list:batchStatus']"
          >批量标记结算状态</el-button
        >
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleResetAll"
      ref="batchUpload"
      title="批量导入邦道与服务商账单"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/lifePay/bangdaoAndService/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import moment from "moment";
import BatchUpload from "@/components/BatchUpload/index.vue";

export default {
  name: "bangdaoAndService",
  components: { BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      selectPage: "1",
      uploadObj: {
        api: "/st/lifePay/bdProvider/importExcel",
        url: "/charging-maintenance-ui/static/邦道与服务商账单导入模板.xlsx",
        extraData: {},
      },
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        recordId: "",
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "editStatus",
      //buse参数-e

      settleStatusOptions: [],
      partnerNameOptions: [],
      instituteNameOptions: [],
      midOptions: [],
      selectedData: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取结算状态字典
    this.getDicts("st_bd_bill_status").then((response) => {
      this.settleStatusOptions = response.data;
    });

    this.getDropLists();
    // this.loadData();
  },
  activated() {
    this.loadData();
  },
  methods: {
    checkPermission,
    getDropLists() {
      // 获取下拉列表数据
      api.getDropLists().then((res) => {
        if (res.success) {
          // 处理下拉列表数据
          if (res.data.partnerName) {
            this.partnerNameOptions = res.data.partnerName.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          if (res.data.instituteName) {
            this.instituteNameOptions = res.data.instituteName.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
          if (res.data.mid) {
            this.midOptions = res.data.mid.map((item) => ({
              dictLabel: item,
              dictValue: item,
            }));
          }
        }
      });
    },
    handleResetAll() {
      this.getDropLists();
      this.handleQuery();
    },

    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleBatchStatus() {
      if (this.selectPage == "1" && this.selectedData?.length == 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }

      this.operationType = "editStatus";
      this.$refs.crud.switchModalView(true, "editStatus", {
        ...initParams(this.modalConfig.formConfig),
        idList: this.selectedData?.map((x) => x.id),
        allPageFlag: this.selectPage == "2",
      });
    },
    handleEditStatus(row) {
      this.operationType = "editStatus";
      this.$refs.crud.switchModalView(true, "editStatus", {
        ...initParams(this.modalConfig.formConfig),
        idList: [row.id],
        status: row.status,
      });
    },
    handleDownload(row) {},
    handlePreview(row) {},
    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "settlementDayRange",
          title: "结算周期",
          startFieldName: "settlementDayStart",
          endFieldName: "settlementDayEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType

      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.$refs.crud.tableDeselectHandler();
        this.loadData();
      } else {
        return false;
      }
    },
  },
  computed: {
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    tableColumn() {
      return [
        { type: "checkbox", width: 60, fixed: "left" },
        {
          field: "partnerName",
          title: "合作伙伴名",
          width: 180,
        },
        {
          field: "instituteName",
          title: "机构名称",
          width: 120,
        },
        {
          field: "mid",
          title: "MID",
          width: 120,
        },
        {
          field: "accountDay",
          title: "账户日期",
          width: 120,
        },
        {
          field: "settlementDay",
          title: "结算期",
          width: 120,
        },
        {
          field: "onlineDay",
          title: "上线日期",
          width: 120,
        },
        {
          field: "aliShouldPayAmount",
          title: "支付宝应收",
          width: 120,
        },
        {
          field: "aliActualPayAmount",
          title: "支付宝实收",
          width: 120,
        },
        {
          field: "spShouldPayAmount",
          title: "服务商应收",
          width: 120,
        },
        {
          field: "spActualPayAmount",
          title: "服务商实收",
          width: 120,
        },
        {
          field: "rebate",
          title: "计算的返佣比例",
          width: 120,
        },
        {
          field: "status",
          title: "结算状态",
          width: 120,
        },
        {
          field: "updateBy",
          title: "操作人",
          width: 120,
        },
        {
          field: "updateTime",
          title: "操作时间",
          width: 120,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "partnerName",
            element: "el-input",
            title: "合作伙伴名",
          },
          {
            field: "status",
            title: "结算状态",
            element: "el-select",
            props: {
              options: this.settleStatusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "settlementDayRange",
            title: "结算周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "instituteName",
            title: "机构名称",
            element: "el-select",
            props: {
              options: this.instituteNameOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "mid",
            title: "MID",
            element: "el-select",
            props: {
              options: this.midOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        //修改结算状态
        editStatus: [
          {
            field: "status",
            title: "结算状态",
            element: "el-select",
            props: {
              options: this.settleStatusOptions,
              optionLabel: "dictLabel", //自定义选项名
              optionValue: "dictValue", //自定义选项值
              filterable: true,
            },
            rules: [
              { required: true, message: "请选择结算状态", trigger: "change" },
            ],
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "修改结算状态",
            typeName: "editStatus",
            event: (row) => {
              return this.handleEditStatus(row);
            },
            condition: (row) => {
              return checkPermission(["bangdaoAndService:list:editStatus"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
