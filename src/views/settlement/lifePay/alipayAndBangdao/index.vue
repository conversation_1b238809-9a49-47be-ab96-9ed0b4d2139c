<!-- 支付宝与邦道账单 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in realList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <DetailPage
      v-show="tabActiveTab === 'detail'"
      ref="detail"
      :searchParams="searchParams"
    ></DetailPage>
    <BankPage
      v-show="tabActiveTab === 'bank'"
      ref="bank"
      @jump="handleJump"
    ></BankPage>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";

import DetailPage from "./detail.vue";
import BankPage from "./bank.vue";

export default {
  name: "alipayAndBangdao",
  components: { DetailPage, BankPage },
  data() {
    return {
      tabActiveTab: "detail",
      topTabDict: [
        {
          value: "detail",
          label: "支付宝明细账单",
          show: () => {
            return this.checkPermission(["alipayAndBangdao:detail:list"]);
          },
        },
        {
          value: "bank",
          label: "银行流水",
          show: () => {
            return this.checkPermission(["alipayAndBangdao:bank:list"]);
          },
        },
      ],
      searchParams: {},
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.$refs[val]?.loadData();
      },
    },
  },
  created() {
    this.realList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.realList[0]?.value || "";
  },
  activated() {
    // if (Object.keys(this.$route.params)?.length > 0) {
    //   this.params = { ...this.params, ...this.$route.params };
    // }
    this.$refs[this.tabActiveTab]?.loadData();
  },
  methods: {
    checkPermission,
    handleJump(params) {
      this.searchParams = params;
      this.$nextTick(() => {
        // this.tabActiveTab = "originalData";
      });
    },
  },
};
</script>

<style lang="less" scoped>
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
</style>
