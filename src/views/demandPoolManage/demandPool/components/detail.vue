<!-- 需求详情 -->
<template>
  <div class="app-container">
    <h3>需求单详情</h3>
    <el-card class="step-card">
      <div style="display: flex;justify-content: space-between">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>需求单进度</span>
          <el-tag style="margin-left:10px">{{ demandStatusName }}</el-tag>
        </div>
        <div v-if="basicForm.demandStatus != undefined">
          <el-dropdown
            @command="(command) => handleCommand(command, this.basicForm)"
          >
            <el-button type="text" size="large">
              快捷操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="(item, index) in moreBtnList"
                  :command="item.command"
                  :key="index"
                  v-show="item.show(basicForm)"
                  v-has-permi="[item.permission]"
                >
                  {{ item.title }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <el-steps :space="200" align-center>
        <el-step
          v-for="(item, index) in stepsList"
          :key="index"
          :title="item.name"
          :status="item.status"
        />
      </el-steps>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <el-form :model="basicForm" ref="basicForm" label-width="150px">
        <el-form-item label="业务类型：" prop="businessType">
          <span>{{ basicForm.businessType }}</span>
        </el-form-item>
        <el-form-item label="需求类型：" prop="demandType">
          <span>{{ basicForm.demandType }}</span>
        </el-form-item>
        <el-form-item label="工时：" prop="jobHourStr">
          <span>{{ basicForm.jobHourStr }}</span>
        </el-form-item>
        <el-form-item label="需求提交人：" prop="submitter">
          <span>{{ basicForm.submitter }}</span>
        </el-form-item>
        <el-form-item label="提交人联系方式：" prop="submitter">
          <span>{{ basicForm.submitterContact }}</span>
        </el-form-item>
        <el-form-item
          label="合作商名称："
          prop="partnerName"
          v-if="showStation || basicForm.partnerName"
        >
          <span>{{ basicForm.partnerName }}</span>
        </el-form-item>
        <el-row
          v-if="showStation"
          v-for="(item, index) in basicForm.stationList"
          :key="index"
        >
          <el-form-item label="场站名称：" prop="stationName">
            <span>{{ item.stationName }}</span>
          </el-form-item>
          <el-form-item label="踏勘编号：" prop="surveyCode">
            <span>{{ item.surveyCode }}</span>
          </el-form-item>
          <el-form-item label="场站编码：" prop="stationCode">
            <span>{{ item.stationCode }}</span>
          </el-form-item>
        </el-row>
        <el-form-item label="备注：" prop="remark">
          <span>{{ basicForm.remark }}</span>
        </el-form-item>
        <el-form-item label="附件（上传协议或其他文件）：" prop="docList">
          <div v-for="(item, index) in basicForm.docList" :key="index">
            <el-link @click="handlePreview(index)">{{ item.docName }}</el-link>
          </div>
          <PreviewFiles
            :initial-index="previewIndex"
            v-if="showViewer"
            :on-close="
              () => {
                showViewer = false;
              }
            "
            :url-list="basicForm.docList"
            :fileOptions="{ url: 'storePath', name: 'docName' }"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <FormCreatePreview :formData="formJson"></FormCreatePreview>
      <!-- <form-create
          :rule="formJson"
          v-model="dealFormData"
          :option="formConfig"
      />

      <div v-if="showfileName">
        <div style="font-size:14px;margin:10px 0;">
          已上传的文件名称：
        </div>
        <div v-for="(j, i) in formJson" :key="i">
          <div v-if="j.fileList && j.fileList.length > 0">
            <div v-for="(f, fi) in j.fileList" :key="fi">
              {{ f.name }}
              <el-button type="text" size="small" @click="handleDownload(f)"
              >下载
              </el-button
              >
            </div>
          </div>
        </div>
      </div> -->
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>工单流转记录</span>
      </div>

      <Timeline
        :list="recordList"
        operateTypeTitle="operatorTypeName"
        operatorNameTitle="operatorUserName"
        createTimeTitle="operatorTime"
        operateDetailTitle="remark"
      ></Timeline>
    </el-card>
    <!-- 转派需求单-S -->
    <el-dialog
      title="转派需求单"
      :visible.sync="transferVisible"
      @close="closeTransferDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="transferForm"
        ref="transferForm"
        label-width="110px"
        :rules="transferRules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model="transferForm.deptId"
                :options="deptOptions"
                placeholder="请选择归属部门"
                @select="handleNodeClick"
                :beforeClearAll="beforeClearAll"
                :default-expand-level="1"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="人员" prop="handleUser">
          <el-select v-model="transferForm.handleUser" filterable>
            <el-option
              v-for="item in userOption"
              :key="item.dictValue"
              :label="item.nickName + '-' + item.userName"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeTransferDialog" :loading="transferLoading"
          >取 消
        </el-button>
        <el-button
          type="primary"
          @click.stop="submitTransfer"
          :loading="transferLoading"
          >提交
        </el-button>
      </div>
    </el-dialog>
    <!-- 转派需求单-E -->
    <!-- 审批进度-S -->
    <el-drawer
      title="运营管理系统审批进度"
      :visible.sync="processVisible"
      @close="processVisible = false"
      size="70%"
    >
      <div class="tag-title" slot="title">
        运营管理系统审批进度
        <el-tag class="ml10">{{ omApproveStatusName }} </el-tag>
      </div>
      <div class="info-title" type="flex">
        <div>申请单号：{{ omApproveNo }}</div>
        <!-- <el-button type="text" @click="showApproveInfo"
          >付款单类查看金蝶审批信息</el-button
        > -->
      </div>
      <Timeline
        :list="processList"
        operateTypeTitle="itemCodeName"
        operatorNameTitle="operateEmpName"
        createTimeTitle="operateTime"
        operateDetailTitle="operateRemark"
      ></Timeline>
      <el-dialog
        :visible.sync="approveInfoVisible"
        :modal-append-to-body="false"
        append-to-body
        width="35%"
        :close-on-click-modal="true"
      >
        <el-form
          label-position="right"
          label-width="180px"
          :model="jdApproveForm"
        >
          <el-form-item label="金蝶付款单状态：">
            <span>{{ jdApproveForm.jdApproveStatusName }}</span>
          </el-form-item>
          <el-form-item label="金蝶当前审批人信息：">
            <span>{{ jdApproveForm.jdApproveUser }}</span>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-drawer>
    <!-- 审批进度-E -->
    <!-- 关联申请单号-S -->
    <el-dialog
      title="关联申请单号"
      :visible.sync="associateVisible"
      @close="closeAssociateDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="associateForm"
        ref="associateForm"
        label-width="110px"
        :rules="associateFormRules"
      >
        <el-form-item label="申请单号" prop="omApproveNo">
          <el-input
            v-model="associateForm.omApproveNo"
            placeholder="请输入运营管理系统的申请单号"
            maxlength="20"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click.stop="closeAssociateDialog"
          :loading="associateLoading"
          >取消
        </el-button>
        <el-button
          type="primary"
          @click.stop="submitAssociate"
          :loading="associateLoading"
          >确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 关联申请单号-E -->
    <!-- 邮寄时间-S -->
    <el-dialog
      title="邮寄时间"
      :visible.sync="timeVisible"
      @close="closeTimeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="timeForm" ref="timeForm" label-width="110px">
        <el-form-item label="协议寄出时间" prop="sendDate">
          <el-date-picker
            v-model="timeForm.sendDate"
            type="date"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="协议收回时间" prop="backDate">
          <el-date-picker
            v-model="timeForm.backDate"
            type="date"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeTimeDialog" :loading="timeLoading"
          >取消
        </el-button>
        <el-button
          type="primary"
          @click.stop="submitTime"
          :loading="timeLoading"
          >确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 邮寄时间-E -->
    <!-- 归档-S -->
    <el-dialog
      title="协议归档"
      :visible.sync="documentationVisible"
      @close="closeDocumentationDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="documentationForm"
        ref="documentationForm"
        label-width="110px"
      >
        <el-form-item label="备注" prop="fileRemark">
          <el-input
            type="textarea"
            v-model="documentationForm.fileRemark"
            maxlength="500"
            show-word-limit
            placeholder="500个字符以内"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click.stop="closeDocumentationDialog"
          :loading="documentationLoading"
          >取消
        </el-button>
        <el-button
          type="primary"
          @click.stop="submitDocumentation"
          :loading="documentationLoading"
          >确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 归档-E -->
    <!-- 确认需求单-S -->
    <el-dialog
      title="确认需求单"
      :visible.sync="confirmVisible"
      @close="closeConfirmDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="confirmForm" ref="confirmForm" label-width="110px">
        <el-form-item label="原因" prop="confirmReason">
          <el-input
            type="textarea"
            v-model="confirmForm.confirmReason"
            maxlength="500"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="submitConfirm(false)" :loading="confirmLoading"
          >不通过
        </el-button>
        <el-button
          type="primary"
          @click.stop="submitConfirm(true)"
          :loading="confirmLoading"
          >通过
        </el-button>
      </div>
    </el-dialog>
    <!-- 确认需求单-E -->
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import { getToken } from "@/utils/auth";
import FormCreatePreview from "@/components/formCreatePreview/index.vue";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import {
  queryDemandList,
  exportExcel,
  deleteDemand,
  withdrawDemand,
  acceptDemand,
  restoreDemand,
  transferDemand,
  confirmDemand,
  queryProcess,
  associateDemand,
  submitMailTime,
  submitDocumentation,
  queryDetail,
  queryRecord,
  submitRemark,
  submitComplete,
  submitSupple,
  copyDemand,
  queryJdApproveInfo,
} from "@/api/demandPool/index.js";
import { mapGetters } from "vuex";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listAllUser, listDept } from "@/api/common";

export default {
  name: "demandPoolDetail",
  components: {
    Treeselect,
    Timeline,
    PreviewFiles,
    FormCreatePreview,
  },
  data() {
    return {
      transferForm: {
        deptId: undefined,
        handleUser: undefined,
      },
      transferRules: {
        handleUser: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },
      transferVisible: false,
      transferLoading: false,
      moreBtnList: [
        {
          title: "受理",
          command: "acceptance",
          clickFn: (row) => {
            this.handleAccept(row);
          },
          permission: "demandPool:demand:acceptance",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 待接收
            const demandStatusList = ["1"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "确认",
          command: "confirm",
          clickFn: (row) => {
            this.handleConfirm(row);
          },
          permission: "demandPool:demand:confirm",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 已接收  已驳回
            const demandStatusList = ["3", "5"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "转派",
          command: "transfer",
          clickFn: (row) => {
            console.log("详情内容：", row);
            this.handleTransfer(row);
          },
          permission: "demandPool:demand:transfer",
          show: (row) => {
            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 待接收 已接收 处理中 已驳回 已完结
            const demandStatusList = ["1", "3", "4", "5", "6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            return isSupervisor && demandStatus;
          },
        },

        {
          title: "审批进度",
          command: "process",
          clickFn: (row) => {
            this.handleProcess(row);
          },
          permission: "demandPool:demand:process",
          show: (row) => {
            return row.omApproveNo != undefined && row.omApproveNo != "";
          },
        },
        {
          title: "关联申请编号",
          command: "associate",
          clickFn: (row) => {
            this.handleAssociate(row);
          },
          permission: "demandPool:demand:associate",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            // 处理中
            const demandStatusList = ["4", "6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "邮寄时间",
          command: "mail",
          clickFn: (row) => {
            this.handleMailTime(row);
          },
          permission: "demandPool:demand:mail",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 已完结
            const demandStatusList = ["6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "归档",
          command: "documentation",
          clickFn: (row) => {
            this.handleDocumentation(row);
          },
          permission: "demandPool:demand:documentation",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 已完结
            const demandStatusList = ["6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
      ],
      showViewer: false,
      previewIndex: 0,
      basicForm: {
        demandStatus: undefined,
        groupUserIds: [],
      },
      deptOptions: [],
      userOption: [],
      demandId: "",
      demandStatusOptions: [],
      stepsList: [],
      recordList: [],
      showfileName: false,
      formJson: [],
      formConfig: {},
      dealFormData: {},
      processVisible: false,
      processList: [],
      approveInfoVisible: false,
      jdApproveForm: {
        jdApproveStatusName: "",
        jdApproveUser: "",
      },
      omApproveNo: "",
      omApproveStatusName: "",
      documentationVisible: false,
      documentationLoading: false,
      documentationForm: { fileRemark: undefined },
      timeVisible: false,
      timeLoading: false,
      timeForm: { sendDate: "", backDate: "" },

      timeFormRules: {
        sendDate: [
          { required: true, message: "请选择协议寄出时间", trigger: "change" },
        ],
        backDate: [
          { required: true, message: "请选择协议收回时间", trigger: "change" },
        ],
      },
      associateVisible: false,
      associateLoading: false,
      associateForm: { omApproveNo: undefined },
      associateFormRules: {
        omApproveNo: [
          { required: true, message: "请输入申请单号", trigger: "blur" },
        ],
      },
      confirmVisible: false,
      confirmForm: {
        confirmReason: undefined,
      },
      confirmLoading: false,
    };
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    showStation() {
      return this.basicForm.twoDemandTypeName === "场地协议";
    },
    demandStatusName() {
      return (
        this.demandStatusOptions?.find(
          (item) => item.dictValue === this.basicForm.demandStatus
        )?.dictLabel || ""
      );
    },
  },
  created() {
    this.demandId = this.$route.query.demandId;
    this.getDicts("cm_demand_status").then((response) => {
      this.demandStatusOptions = response.data;
    });
  },
  mounted() {
    this.getTreeselect();
    this.getListUser();
    this.queryDetail();
    this.queryRecord();
  },
  methods: {
    closeConfirmDialog() {
      this.$refs.confirmForm.resetFields();
      this.confirmVisible = false;
    },
    submitConfirm(isPass) {
      if (!isPass && !this.confirmForm.confirmReason) {
        this.$message.warning("审核不通过原因不能为空！");
        return false;
      }
      let params = {
        ...this.confirmForm,
        demandId: this.demandId,
        confirmResult: isPass ? 1 : 0,
      };
      this.confirmLoading = true;
      confirmDemand(params)
        .then((res) => {
          this.confirmLoading = false;
          if (res?.code === "10000") {
            this.closeConfirmDialog();
            this.queryDetail();
            this.queryRecord();
            this.$message.success("确认成功");
          }
        })
        .catch(() => {
          this.confirmLoading = false;
        });
    },
    //确认
    handleConfirm(row) {
      this.confirmVisible = true;
      this.demandId = row.demandId;
    },
    handleAccept(row) {
      this.$confirm("确定接收该需求单吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          demandId: row.demandId,
        };
        acceptDemand(data).then((res) => {
          if (res?.success) {
            this.$message.success("接收成功");
            this.queryDetail();
            this.queryRecord();
          }
        });
      });
    },
    //归档
    handleDocumentation(row) {
      this.documentationVisible = true;
      this.demandId = row.demandId;
    },
    closeDocumentationDialog() {
      this.documentationVisible = false;
      this.$refs.documentationForm.resetForm();
    },
    submitDocumentation() {
      let params = {
        ...this.documentationForm,
        demandId: this.demandId,
      };
      this.documentationLoading = true;
      submitDocumentation(params)
        .then((res) => {
          this.documentationLoading = false;
          if (res?.code === "10000") {
            this.closeDocumentationDialog();
            this.queryDetail();
            this.queryRecord();
            this.$message.success("归档成功");
          }
        })
        .catch(() => {
          this.documentationLoading = false;
        });
    },
    //邮寄时间
    handleMailTime(row) {
      this.demandId = row.demandId;
      this.timeVisible = true;
      this.timeForm = { sendDate: row.sendDate, backDate: row.backDate };
    },
    submitTime() {
      if (!this.timeForm.sendDate && !this.timeForm.backDate) {
        this.$message.warning("请选择邮寄时间");
        return;
      }
      let params = {
        ...this.timeForm,
        demandId: this.demandId,
      };
      this.timeLoading = true;
      submitMailTime(params)
        .then((res) => {
          this.timeLoading = false;
          if (res?.code === "10000") {
            this.closeTimeDialog();
            this.queryDetail();
            this.queryRecord();
            this.$message.success("关联成功");
          }
        })
        .catch(() => {
          this.timeLoading = false;
        });
    },
    closeTimeDialog() {
      this.timeVisible = false;
      this.$refs.timeForm.resetFields();
    },
    //关联申请编号
    handleAssociate(row) {
      this.demandId = row.demandId;
      this.associateVisible = true;
      this.associateForm.omApproveNo = row.omApproveNo;
    },
    submitAssociate() {
      this.$refs.associateForm.validate((valid) => {
        if (!valid) {
          return false;
        }
        let params = {
          ...this.associateForm,
          demandId: this.demandId,
        };
        this.associateLoading = true;
        associateDemand(params)
          .then((res) => {
            this.associateLoading = false;
            if (res?.code === "10000") {
              this.closeAssociateDialog();
              this.queryDetail();
              this.queryRecord();
              this.$message.success("关联成功");
            }
          })
          .catch(() => {
            this.associateLoading = false;
          });
      });
    },
    closeAssociateDialog() {
      this.associateVisible = false;
      this.$refs.associateForm.resetFields();
    },
    //审批进度
    handleProcess(row) {
      this.processVisible = true;
      this.omApproveNo = row.omApproveNo;
      queryProcess({ omApproveNo: row.omApproveNo }).then((res) => {
        if (res?.code == 10000) {
          this.processList = res.data;
        }
      });
      this.omApproveStatusName = row.omApproveStatus;
    },

    handleCommand(command, row) {
      this.moreBtnList?.find((x) => x.command == command)?.clickFn(row);
      console.log(command, row, "command");
    },
    //获取用户列表
    async getListUser(param) {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      if (param) {
        params.orgNo = Number(param);
      }
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    submitTransfer() {
      this.$refs.transferForm.validate(async (valid) => {
        if (valid) {
          let params = {
            handleUser: this.transferForm.handleUser,
            demandId: this.demandId,
          };
          this.transferLoading = true;
          transferDemand(params)
            .then((res) => {
              this.transferLoading = false;
              if (res?.code === "10000") {
                this.closeTransferDialog();
                this.queryDetail();
                this.queryRecord();
                this.$message.success("指派成功");
              }
            })
            .catch(() => {
              this.transferLoading = false;
            });
        }
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.transferForm.handleUser = undefined;
      this.getListUser(data.deptId);
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    beforeClearAll() {
      this.transferForm.handleUser = undefined;
      this.transferForm.deptId = undefined;
      this.getListUser();
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    closeTransferDialog() {
      this.$refs.transferForm.resetFields();
      this.transferVisible = false;
    },
    //转派
    handleTransfer(row) {
      this.transferVisible = true;
      this.demandId = row.demandId;
    },
    //附件预览
    handlePreview(index) {
      this.showViewer = true;
      this.previewIndex = index;
    },
    async queryRecord() {
      const res = await queryRecord({
        demandId: this.demandId,
      });
      if (res?.code === "10000") {
        this.recordList = res.data;
      }
    },
    async queryDetail() {
      const res = await queryDetail({
        demandId: this.demandId,
      });
      if (res?.code === "10000") {
        this.basicForm = { ...res.data };
        console.log("详情信息", this.basicForm);
        this.stepsList = res.data?.progressList?.map((x) => {
          return {
            ...x,
            status:
              x.color === "green"
                ? "success"
                : x.color === "red"
                ? "error"
                : "",
          };
        });
        this.formConfig = JSON.parse(res.data.formConfig);
        this.formJson = JSON.parse(res.data.formJson)?.map((item) => {
          this.handleFormRule(item);
          return item;
        });
        console.log(this.basicForm);
      }
    },
    handleFormRule(item) {
      let that = this;
      // console.log("item", item);
      if (item.type == "upload") {
        // console.log("item", item);
        //添加请求头
        item.props.headers = {
          Authorization: "Bearer " + this.$store.state.user.token,
        };
        if (item.props?.uploadType == "image") {
          item.props.listType = "image";
        } else if (item.props?.uploadType == "file") {
          item.props.listType = "text";
          that.showfileName = true;
        } else {
          item.props.uploadType = "image";
          item.props.listType = "image";
        }
        this.$set(item.props, "onSuccess", function(res, file, fileList) {
          file.url = res.data;
          // item.fileList = fileList;
          that.$set(item, "fileList", fileList);
          // console.log(this);
          this.$set(item, "previewSrcList", fileList);
        });
        this.$set(item.props, "onRemove", function(file, fileList) {
          that.$set(item, "fileList", fileList);
        });
      }
      //select接口请求添加token
      if (item.type == "select" && item?.effect?.fetch) {
        this.$set(
          item.effect.fetch.headers,
          "Authorization",
          "Bearer " + getToken()
        );

        //过滤桩信息列表接口，修改入参
        let stationListApi = item?.effect?.fetch?.action;
        // if (stationListApi && stationListApi.includes("business/pileList")) {
        //   //如果两个节点配置了同一个表单则会在taskForm接口中携带stationId, 为了防止重复拼接stationId，每次需做下截取
        //   stationListApi = stationListApi.slice(
        //     0,
        //     stationListApi.indexOf("business/pileList") + 17
        //   );
        //   stationListApi += `?stationId=${this.projectForm.stationId}`;
        // }
        item.effect.fetch.action = stationListApi;
        item.effect.fetch.parse = (res) => {
          if (res?.data?.length > 0) {
            return res.data.map((item) => {
              return {
                label: item.dictLabel,
                value: item.dictValue,
              };
            });
          } else {
            return [];
          }
        };
      }
      // 递归设置表单规则
      if (
        (item.type == "FcRow" || item.type == "col") &&
        item.children &&
        item.children.length > 0
      ) {
        item.children.map((object) => this.handleFormRule(object));
      }

      //栅格布局:设置子组件的disabled属性
      if (item.type == "FcRow") {
        if (item?.children?.length > 0) {
          item.children.map((item) => {
            if (item?.children?.length > 0) {
              item.children.map((child) => {
                // //未开始|已完成 不可操作,详情 不可操作
                // let nodeDisabled = !!(
                //   !this.currentNode.taskId || this.currentNode.endTime
                // );
                if (child.props) {
                  child.props.disabled = true;
                } else {
                  child.props = {
                    disabled: true,
                  };
                }

                return child;
              });
            }

            return item;
          });
        }
      }
      // console.log(item, "====item");
      //非处理情况下表单禁用
      // let nodeDisabled = !!(
      //   !this.currentNode.taskId || this.currentNode.endTime
      // ); //未开始|已完成 不可操作,详情 不可操作
      if (item.props) {
        item.props.disabled = true;
      } else {
        item.props = { disabled: true };
      }
    },
    handleDownload(f) {
      const a = document.createElement("a"); // 创建一个HTML 元素
      a.setAttribute("download", f.name); //download属性
      a.setAttribute("href", f.url); // href链接
      a.click(); // 自执行点击事件
    },
  },
  watch: {},
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}

.step-card /deep/ .el-steps {
  justify-content: center;
}

/deep/ .el-card {
  margin-bottom: 10px;
}

.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
.tag-title {
  // margin: 10px 40px 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.info-title {
  margin: 10px 40px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
