<template>
  <div class="app-container" v-loading="pageLoading">
    <h3>{{ demandId ? "修改" : "新增" }}需求</h3>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <el-form
        :model="basicForm"
        ref="basicForm"
        label-width="140px"
        :rules="basicRules"
      >
        <el-form-item label="业务类型" prop="twoBusinessType">
          <CustomCascader
            v-model="basicForm.twoBusinessType"
            placeholder="请选择二级业务类型"
            :options="businessTypeOptions"
            filterable
            style="width: 100%"
            @change="handleBusinessChange"
            :props="{
              value: 'id',
              label: 'typeName',
              children: 'childrenList',
              emitPath: false,
            }"
          >
            <template slot-scope="{ node, data }">
              <el-tooltip
                :disabled="data.typeName.length < 11"
                class="item"
                effect="dark"
                :content="data.typeName"
                placement="top-start"
              >
                <div class="cascader-node">{{ data.typeName }}</div>
              </el-tooltip>
            </template>
          </CustomCascader>
          <!-- <el-row>
            <el-col :span="12">
              <el-select
                v-model="basicForm.oneBusinessType"
                placeholder="一级业务类型"
                clearable
                style="width: 100%;"
                @change="handleFirstBusinessChange"
              >
                <el-option
                  v-for="item in businessFirstLevelList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select
                v-model="basicForm.twoBusinessType"
                placeholder="二级业务类型"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="item in businessChildrenList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-col>
          </el-row> -->
        </el-form-item>
        <el-form-item label="需求类型" prop="twoDemandType">
          <CustomCascader
            v-model="basicForm.twoDemandType"
            placeholder="请选择二级需求类型"
            :options="demandTreeOptions"
            filterable
            style="width: 100%"
            :props="{
              value: 'id',
              label: 'typeName',
              children: 'childrenList',
              emitPath: false,
            }"
            @change="getFormJson"
          >
            <template slot-scope="{ node, data }">
              <el-tooltip
                :disabled="data.typeName.length < 11"
                class="item"
                effect="dark"
                :content="data.typeName"
                placement="top-start"
              >
                <div class="cascader-node">{{ data.typeName }}</div>
              </el-tooltip>
            </template>
          </CustomCascader>
          <!-- <el-row>
            <el-col :span="12">
              <el-select
                v-model="basicForm.oneDemandType"
                placeholder="一级需求类型"
                clearable
                style="width: 100%;"
                @change="handleFirstDemandChange"
              >
                <el-option
                  v-for="item in demandFirstLevelList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-select
                v-model="basicForm.twoDemandType"
                placeholder="二级需求类型"
                clearable
                style="width: 100%;"
                @change="getFormJson"
              >
                <el-option
                  v-for="item in demandChildrenList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-col>
          </el-row> -->
        </el-form-item>
        <el-form-item label="工时" prop="jobHour">
          <el-select
            v-model="basicForm.jobHour"
            placeholder="请输入工时编码或工时名称"
            filterable
            remote
            style="width: 100%;margin-bottom:18px"
            @change="workHourCodeChange"
            clearable
            v-el-select-loadmore="getWorkHourList"
            :remote-method="remoteMethod"
          >
            <el-option
              v-for="item in workHourCodeList"
              :key="item.WH_NO"
              :label="'【' + item.WH_NO + '】' + item.WH_NAME"
              :value="item.WH_NO"
            />
          </el-select>
          <el-select
            v-model="basicForm.supportJobHour"
            placeholder="请输入支持工时编码或工时名称"
            filterable
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in childWorkHourCodeList"
              :key="item.WH_NO"
              :label="'【' + item.WH_NO + '】' + item.WH_NAME"
              :value="item.WH_NO"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="需求提交人" prop="submitterId">
          <!-- <el-input
            v-model="basicForm.submitter"
            placeholder="请输入姓名"
            clearable
            maxlength="64"
          >
          </el-input> -->
          <el-select
            v-model="basicForm.submitterId"
            placeholder="请选择"
            filterable
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in userOptions"
              :key="item.userId"
              :label="item.userName + '-' + item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交人联系方式" prop="submitterContact">
          <el-input
            v-model="basicForm.submitterContact"
            placeholder="请输入手机号"
            clearable
            maxlength="20"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="合作商名称" prop="partnerName">
          <el-autocomplete
            v-model="basicForm.partnerName"
            placeholder="可自行填写项目名称、场地名称、合作商名称"
            style="width: 100%"
            clearable
            :fetch-suggestions="querySearch"
            @select="handleSelect"
            value-key="partnerName"
          >
            <!-- <el-option
              v-for="item in partnerOptions"
              :key="item.partnerId"
              :label="item.partnerName"
              :value="item.partnerId"
            /> -->
          </el-autocomplete>
        </el-form-item>
        <el-row
          v-if="showStationForm"
          v-for="(item, index) in basicForm.stationList"
          :key="index"
          class="station-group-item"
        >
          <el-col :span="20">
            <el-form-item
              label="场站名称"
              :prop="'stationList.' + index + '.stationName'"
              :rules="{
                required: true,
                message: '请选择场站名称',
                trigger: 'blur',
              }"
            >
              <el-select
                v-model="item.stationName"
                placeholder="请选择或搜索场站名称"
                filterable
                remote
                style="width: 100%"
                clearable
                @change="
                  (val) => {
                    stationChange(val, index);
                  }
                "
                v-el-select-loadmore="getStationList"
                :remote-method="remoteStationMethod"
                value-key="stationName"
              >
                <el-option
                  v-for="(item, index) in stationOptions"
                  :key="index"
                  :label="item.stationName"
                  :value="item"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="踏勘编号"
              :prop="'stationList.' + index + '.surveyCode'"
              :rules="{
                required: true,
                trigger: 'blur',
                validator: (rule, value, callback) => {
                  validateSurveyCode(rule, value, callback, index);
                },
              }"
            >
              <!-- <el-input v-model="item.surveyCode" style="width: 100%" clearable>
              </el-input> -->
              <el-select
                v-model="item.surveyCode"
                placeholder="请选择或搜索踏勘编号"
                filterable
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in exploreCodeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="场站编码" prop="stationCode">
              <el-select
                v-model="item.stationCode"
                style="width: 100%"
                placeholder="请选择或搜索场站编码"
                filterable
                clearable
              >
                <el-option
                  v-for="item in stationCodeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-button
              type="primary"
              circle
              icon="el-icon-plus"
              @click="addItem"
              v-if="index === 0"
              style="margin-bottom: 18px;"
            />
            <el-button
              type="primary"
              circle
              icon="el-icon-minus"
              style="background:red;border:1px solid red;margin-bottom: 18px;"
              @click="removeItem(index)"
              v-else
            />
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="basicForm.remark"
            type="textarea"
            maxlength="1000"
            show-word-limit
            :rows="5"
            clearable
            placeholder="请说明具体的说明备注，1000个字符以内"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="附件（上传协议或其他文件）" prop="docList">
          <MultiFileUpload
            ref="attachments"
            @uploadSuccess="updateAttachments"
            accept=".jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .pdf"
            :maxSize="500"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>

      <form-create
        :rule="formJson"
        v-model="dealFormData"
        :option="formConfig"
      />

      <div v-if="showfileName">
        <div style="font-size:14px;margin:10px 0;">
          已上传的文件名称：
        </div>
        <div v-for="(j, i) in formJson" :key="i">
          <div v-if="j.fileList && j.fileList.length > 0">
            <div v-for="(f, fi) in j.fileList" :key="fi">
              {{ f.name }}
              <el-button type="text" size="small" @click="handleDownload(f)"
                >下载</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeVisible" size="medium" :loading="loading"
        >取 消</el-button
      >
      <el-button
        @click="handleSubmit(0)"
        type="primary"
        size="medium"
        :loading="loading"
        v-if="demandStatus != '5'"
        >存草稿</el-button
      >
      <el-button
        @click="handleSubmit(1)"
        type="primary"
        size="medium"
        :loading="loading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
// import { stationList } from "@/api/workOrderWorkbench";
import { getToken } from "@/utils/auth";
import { validMobile } from "@/utils/validate";
import { mapGetters } from "vuex";
import Api from "@/api/demandPool/partnerManager.js";
import { listAllUser } from "@/api/common.js";
import {
  queryDetail,
  submitForm,
  queryWorkHourList,
  queryStationInfo,
  queryFormJson,
} from "@/api/demandPool/index.js";
import MultiFileUpload from "@/components/MultipleFileUpload";
import {
  queryBusinessFirstLevel,
  queryBusinessChildrenList,
  queryDemandFirstLevel,
  queryDemandChildrenList,
  queryDemandTree,
} from "@/api/demandPool/dashboard.js";
import { queryBusinessList } from "@/api/demandPool/demandType.js";
import { querySecondDemandType } from "@/api/demandPool/setGroup.js";
import formCreateMixin from "@/mixin/formCreate.js";

export default {
  name: "demandAdd",
  components: { MultiFileUpload },
  mixins: [formCreateMixin],
  data() {
    return {
      demandStatus: "",
      showfileName: false,
      formJson: [],
      formConfig: {},
      dealFormData: {},
      partnerOptions: [],
      demandId: "",
      basicForm: {
        oneBusinessType: undefined,
        twoBusinessType: undefined,
        oneDemandType: undefined,
        twoDemandType: undefined,
        jobHour: undefined,
        supportJobHour: undefined,
        submitterId: undefined,
        submitterContact: undefined,
        partnerName: undefined,
        stationCode: undefined,
        stationName: undefined,
        surveyCode: undefined,
        remark: undefined,
        docList: [],
        stationList: [{ stationName: "", surveyCode: "", stationCode: "" }],
      },
      stationOptions: [],
      exploreCodeOptions: [],
      stationCodeOptions: [],
      loading: false,
      businessFirstLevelList: [],
      businessChildrenList: [],
      demandFirstLevelList: [],
      demandChildrenList: [],
      workHourCodeList: [],
      // childWorkHourCodeList: [],
      isLoading: false,
      isStationLoading: false,
      currentPage: 1,
      currentStationPage: 1,
      pageLoading: false,
      selectedPartner: {},
      workHourText: "",
      workHourTotal: 0,
      stationText: "",
      stationTotal: 0,
      userOptions: [],
      businessTypeOptions: [],
      demandTreeOptions: [],
      demandTypeSecondOptions: [],
      activeNum: 0,
    };
  },
  async mounted() {
    this.basicForm.submitterId = this.userId;
    this.basicForm.submitterContact = this.phonenumber;
    this.demandId = this.$route.query.demandId || "";
    if (this.demandId) {
      await this.getDetail();
    }
  },
  async activated() {
    listAllUser({ status: "0" }).then((res) => {
      this.userOptions = res.data;
    });
    this.getStationList();
    // this.queryBusinessType();
    // this.queryDemandType();
    this.getPartnerOptions();
    this.getWorkHourList();
    this.getBusinessTypeOptions();
    this.getSecondDemandOptions();
    //重新进入页面时，处理需求类型选项
    this.activeNum > 0 && this.handleDemandOptions(this.basicForm);
    this.activeNum++;
  },
  watch: {
    // "basicForm.twoDemandType": {
    //   handler(val) {
    //     console.log(val, "二级");
    //     this.getFormJson(val);
    //   },
    // },
  },
  computed: {
    ...mapGetters(["nickName", "phonenumber", "userId"]),
    childWorkHourCodeList() {
      return this.workHourCodeList?.find(
        (x) => x["WH_NO"] === this.basicForm.jobHour
      )?.children;
    },
    showStationForm() {
      return this.demandTypeSecondOptions
        ?.find((x) => x.id === this.basicForm.twoDemandType)
        ?.typeName.includes("场地协议");
      // return true;
    },
    basicRules() {
      return {
        twoBusinessType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
        twoDemandType: [
          { required: true, message: "请选择需求类型", trigger: "blur" },
        ],
        // jobHour: [{ required: true, message: "请选择工时", trigger: "blur" }],
        partnerName: [
          { required: true, message: "请输入合作商名称", trigger: "change" },
        ],
        submitterId: [
          { required: true, message: "请选择需求提交人", trigger: "blur" },
        ],
        submitterContact: [
          {
            required: true,
            message: "请输入正确的电话号码",
            validator: (rule, value, callback) => {
              if (validMobile(value)) {
                callback();
              } else {
                callback(new Error("请输入正确的电话号码"));
              }
            },
            trigger: "blur",
          },
        ],
        docList: [{ required: true, message: "请上传附件", trigger: "blur" }],
      };
    },
  },
  methods: {
    getSecondDemandOptions() {
      querySecondDemandType().then((res) => {
        if (res?.code === "10000") {
          this.demandTypeSecondOptions = res.data;
        }
      });
    },
    handleBusinessChange(val) {
      this.basicForm.twoDemandType = undefined;
      this.queryDemandOptionsByBusiness(val);
    },
    //需求类型根据已选的业务类型联动
    async queryDemandOptionsByBusiness(val) {
      const res = await queryDemandTree({ twoBusinessType: val });
      if (res?.code === "10000") {
        this.demandTreeOptions = res.data;
      }
    },
    getBusinessTypeOptions() {
      queryBusinessList().then((res) => {
        if (res.code === "10000") {
          this.businessTypeOptions = res.data?.map((x) => {
            return { childrenList: [], ...x };
          });
        }
      });
    },
    validateSurveyCode(rule, value, callback, index) {
      if (!value) {
        const stationName =
          this.basicForm.stationList[index]?.stationName?.stationName ||
          this.basicForm.stationList[index]?.stationName;
        console.log(stationName, "stationName");
        callback(new Error(`${stationName}踏勘编号为空，请修改后提交`));
      } else {
        callback();
      }
    },
    addItem() {
      this.basicForm.stationList.push({
        stationName: "",
        surveyCode: "",
        stationCode: "",
      });
      //   }
    },
    removeItem(index) {
      this.basicForm.stationList.splice(index, 1);
    },
    handleSelect(val) {
      console.log(val, "选中");
      this.selectedPartner = { ...val };
    },
    querySearch(queryString, cb) {
      Api.queryPartnerList({
        pageNum: 1,
        pageSize: 9999,
        status: 0,
        partnerName: queryString,
      }).then((res) => {
        const result = res.data;
        // this.partnerOptions = res.data;
        cb(result);
      });
    },
    getFormJson(val) {
      this.showfileName = false;
      const defId = this.demandTypeSecondOptions?.find((x) => x.id === val)
        ?.defId;
      console.log(defId, "from defid");
      if (defId) {
        queryFormJson({ defId: defId }).then((res) => {
          if (res.data) {
            const { formConfig, formJson } = res.data;
            this.formConfig = JSON.parse(formConfig);
            this.formJson = JSON.parse(formJson)?.map((item) => {
              this.handleFormRule(item);
              return item;
            });
            console.log(JSON.parse(formJson), this.formJson, "formJson");
            console.log(this.formConfig, "formConfig");
          } else {
            this.formConfig = {};
            this.formJson = [];
          }
        });
      } else {
        this.formConfig = {};
        this.formJson = [];
      }
    },
    getPartnerOptions() {
      Api.queryPartnerList({ pageNum: 1, pageSize: 9999, status: 0 }).then(
        (res) => {
          this.partnerOptions = res.data;
        }
      );
    },
    async getWorkHourList() {
      const page =
        this.workHourTotal == 0 ? 1 : Math.ceil(this.workHourTotal / 10);
      if (this.isLoading || this.currentPage > page) {
        return;
      }
      this.isLoading = true;
      const params = {
        pageNum: this.currentPage,
        pageSize: 10,
        WH_NO: this.workHourText,
      };
      const res = await queryWorkHourList(params);
      this.workHourTotal = res.total;
      const newOptions = res?.data?.map((x) => {
        return { ...x };
      });
      if (newOptions.length > 0) {
        this.workHourCodeList = this.workHourCodeList.concat(newOptions);
        this.currentPage++;
      }
      this.isLoading = false;
    },
    workHourCodeChange(val) {
      this.basicForm.supportJobHour = undefined;
      console.log(val, this.workHourCodeList, "选中");
      // this.childWorkHourCodeList = this.workHourCodeList?.find(
      //   (x) => x["WH_NO"] === val
      // )?.children;
      // console.log(this.childWorkHourCodeList, "子集");
    },
    async remoteMethod(val) {
      this.workHourCodeList = [];
      this.currentPage = 1;
      this.workHourText = val;
      await this.getWorkHourList();
    },
    async remoteStationMethod(val) {
      this.stationOptions = [];
      this.currentStationPage = 1;
      this.stationText = val;
      await this.getStationList();
    },
    handleSubmit(isSubmit) {
      this.$refs.basicForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        this.dealFormData.validate(async (valid2) => {
          if (valid2) {
            console.log(this.basicForm, "basicform");
            const params = {
              ...this.basicForm,
              // demandId: this.demandId,
              stationList: this.basicForm.stationList?.map((x) => {
                let obj = {
                  surveyCode: x.surveyCode,
                  stationCode: x.stationCode,
                  stationName: x.stationName?.stationName || x.stationName,
                };
                return obj;
              }),
              demandStatus: isSubmit,
              formJson: JSON.stringify(this.formJson),
              formConfig: JSON.stringify(this.formConfig),
              submitter: this.userOptions?.find(
                (x) => x.userId === this.basicForm.submitterId
              )?.nickName,
              // partnerId:
              //   this.selectedPartner?.partnerName === this.basicForm.partnerName
              //     ? this.selectedPartner?.partnerId
              //     : null,
            };
            console.log(params, "params");
            this.loading = true;
            const res = await submitForm(params);
            this.loading = false;
            if (res?.code === "10000") {
              this.$message.success("提交成功");
              this.$router.push({
                path: "/demandPoolManage/demandPool",
              });
            }
          } else {
            this.$message.warning("请填写完整表单信息!");
          }
        });
      });
    },
    async getStationList() {
      const page =
        this.stationTotal == 0 ? 1 : Math.ceil(this.stationTotal / 10);
      if (this.isStationLoading || this.currentStationPage > page) {
        return;
      }
      this.isStationLoading = true;
      const params = {
        pageNum: this.currentStationPage,
        pageSize: 10,
        stationName: this.stationText,
      };
      const res = await queryStationInfo(params);
      this.stationTotal = res?.total;
      const newOptions = res?.data?.map((x) => {
        return { ...x };
      });
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentStationPage++;
      }
      this.isStationLoading = false;
    },
    async handleDemandOptions(data) {
      console.log("调用了这个方法");
      await this.queryDemandOptionsByBusiness(data?.twoBusinessType);
      //todo:编辑时回显的类型被删除或修改
      const {
        oneDemandType,
        oneDemandTypeName,
        twoDemandType,
        twoDemandTypeName,
      } = data;
      const oneObj = this.demandTreeOptions?.find((x) => x.id == oneDemandType);
      //第一种：一级和二级都没有了
      if (!oneObj || Object.keys(oneObj).length == 0) {
        this.demandTreeOptions.push({
          id: oneDemandType,
          typeName: oneDemandTypeName,
          disabled: true,
          childrenList: [{ id: twoDemandType, typeName: twoDemandTypeName }],
        });
      } else {
        //第二种：一级有，二级被删除或修改
        const twoObj = oneObj.childrenList.find((x) => x.id === twoDemandType);
        if (!twoObj || Object.keys(twoObj).length == 0) {
          this.demandTreeOptions.forEach((x) => {
            if (x.id == oneDemandType) {
              x.childrenList.push({
                id: twoDemandType,
                typeName: twoDemandTypeName,
                disabled: true,
              });
            }
          });
        }
      }
    },
    async getDetail() {
      this.pageLoading = true;
      const res = await queryDetail({ demandId: this.demandId }).catch(() => {
        this.pageLoading = false;
      });
      this.pageLoading = false;
      if (res?.code === "10000") {
        this.demandStatus = res.data?.demandStatus;
        // queryBusinessChildrenList({
        //   ids: [res.data?.oneBusinessType],
        // }).then((res) => {
        //   if (res?.code === "10000") {
        //     this.businessChildrenList = res.data;
        //   }
        // });
        // const response = await queryDemandChildrenList({
        //   ids: [res.data?.oneDemandType],
        // });
        // if (response?.code === "10000") {
        //   this.demandChildrenList = response.data;
        // }
        await this.handleDemandOptions(res.data);

        //todo:编辑时回显的类型被删除或修改
        //第一种：一级和二级都没有了
        // this.demandTreeOptions.push({
        //   id: "481472333169745920",
        //   typeName: "小青一级",
        //   disabled: true,
        //   childrenList: [{ id: "481772371720761344", typeName: "小青二级" }],
        // });
        //第二种：一级有，二级被删除或修改
        // this.demandTreeOptions.forEach((x) => {
        //   if (x.id == "481472333169745920") {
        //     x.childrenList.push({
        //       id: "481772371720761344",
        //       typeName: "小青二级",
        //       disabled: true,
        //     });
        //   }
        // });
        this.basicForm = { ...this.basicForm, ...res.data };
        this.basicForm.stationList?.length === 0 &&
          (this.basicForm.stationList = [
            { stationName: "", surveyCode: "", stationCode: "" },
          ]);
        this.$refs.attachments.setAttachments(res.data?.docList || []);
        this.formConfig = JSON.parse(res.data.formConfig);
        this.formJson = JSON.parse(res.data.formJson)?.map((item) => {
          this.handleFormRule(item);
          return item;
        });
      }
    },
    updateAttachments(attachments) {
      this.basicForm.docList = attachments;
    },
    stationChange(item, index) {
      if (item) {
        this.basicForm.stationList[index].surveyCode = item.businessIdList?.[0];
        this.basicForm.stationList[index].stationCode =
          item.stationCodeList?.[0];
        this.exploreCodeOptions = item.businessIdList?.map((x) => {
          return { dictLabel: x, dictValue: x };
        });
        this.stationCodeOptions = item.stationCodeList?.map((x) => {
          return { dictLabel: x, dictValue: x };
        });
      }
    },
    closeVisible() {
      this.$router.push({
        path: "/demandPoolManage/demandPool",
      });
    },
    async queryBusinessType() {
      const res = await queryBusinessFirstLevel();
      if (res?.code === "10000") {
        this.businessFirstLevelList = res.data;
      }
    },
    async queryDemandType() {
      const res = await queryDemandFirstLevel();
      if (res?.code === "10000") {
        this.demandFirstLevelList = res.data;
      }
    },
    // async handleFirstDemandChange(val) {
    //   this.basicForm.twoDemandType = undefined;
    //   if (!val) {
    //     this.demandChildrenList = [];
    //   } else {
    //     const res = await queryDemandChildrenList({ ids: [val] });
    //     if (res?.code === "10000") {
    //       this.demandChildrenList = res.data;
    //     }
    //   }
    // },
    // async handleFirstBusinessChange(val) {
    //   this.basicForm.twoBusinessType = undefined;
    //   if (!val) {
    //     this.businessChildrenList = [];
    //   } else {
    //     const res = await queryBusinessChildrenList({ ids: [val] });
    //     if (res?.code === "10000") {
    //       this.businessChildrenList = res.data;
    //     }
    //   }
    // },
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 下拉框下拉的框
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        // 增加滚动监听，
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          console.log(this.scrollHeight, this.scrollTop, this.clientHeight);
          const condition =
            this.scrollHeight - this.scrollTop - 2 <= this.clientHeight;
          // 当滚动条滚动到最底下的时候执行接口加载下一页
          if (condition) {
            console.log("进入判断");
            binding.value();
          }
        });
      },
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
.el-card.is-always-shadow {
  margin-bottom: 10px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}
.station-group-item {
  border: 1px dashed #029c7c;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
