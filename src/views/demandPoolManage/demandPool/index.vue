//需求池
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      @handleExport="handleExport"
      @handleParentChange="handleParentChange"
      :showExportButton="checkPermission(['demandPool:list:export'])"
      labelWidth="120px"
    >
      <template slot="businessType">
        <el-row>
          <el-col :span="12">
            <el-select
              v-model="searchForm.oneBusinessType"
              placeholder="一级业务类型"
              clearable
              style="width: 100%;"
              @change="handleFirstBusinessChange"
            >
              <el-option
                v-for="item in businessFirstLevelList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
              v-model="searchForm.twoBusinessType"
              placeholder="二级业务类型"
              clearable
              style="width: 100%;"
            >
              <el-option
                v-for="item in businessChildrenList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
      </template>
      <template slot="demandType">
        <el-row>
          <el-col :span="12">
            <el-select
              v-model="searchForm.oneDemandType"
              placeholder="一级需求类型"
              clearable
              style="width: 100%;"
              @change="handleFirstDemandChange"
            >
              <el-option
                v-for="item in demandFirstLevelList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
              v-model="searchForm.twoDemandType"
              placeholder="二级需求类型"
              clearable
              style="width: 100%;"
            >
              <el-option
                v-for="item in demandChildrenList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
      </template>
      <!-- showExportButton -->
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="item.clickFn()"
            v-for="(item, index) in toolBarBtnList"
            :key="index"
            v-has-permi="[item.permission]"
          >
            {{ item.title }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <div style="float:right">
            <el-button
              type="text"
              size="large"
              @click.stop="item.clickFn(row)"
              v-for="(item, index) in operationBtnList"
              :key="index"
              v-show="item.show(row)"
              v-has-permi="[item.permission]"
            >
              {{ item.title }}
            </el-button>
            <el-dropdown @command="(command) => handleCommand(command, row)">
              <el-button type="text" size="large">
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  :command="item.command"
                  v-for="(item, index) in moreBtnList"
                  :key="index"
                  v-show="item.show(row)"
                  v-has-permi="[item.permission]"
                >
                  <el-button type="text" size="large">
                    {{ item.title }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showDetail(row)">
            {{ row.orderId }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <!-- 转派需求单-S -->
    <el-dialog
      title="转派需求单"
      :visible.sync="transferVisible"
      @close="closeTransferDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="transferForm"
        ref="transferForm"
        label-width="110px"
        :rules="transferRules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model="transferForm.deptId"
                :options="deptOptions"
                placeholder="请选择归属部门"
                @select="handleNodeClick"
                :beforeClearAll="beforeClearAll"
                :default-expand-level="1"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="人员" prop="handleUser">
          <el-select v-model="transferForm.handleUser" filterable>
            <el-option
              v-for="item in userOption"
              :key="item.dictValue"
              :label="item.nickName + '-' + item.userName"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeTransferDialog" :loading="transferLoading"
          >取 消</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitTransfer"
          :loading="transferLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <!-- 转派需求单-E -->
    <!-- 确认需求单-S -->
    <el-dialog
      title="确认需求单"
      :visible.sync="confirmVisible"
      @close="closeConfirmDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="confirmForm" ref="confirmForm" label-width="110px">
        <el-form-item label="原因" prop="confirmReason">
          <el-input
            type="textarea"
            v-model="confirmForm.confirmReason"
            maxlength="500"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="submitConfirm(false)" :loading="confirmLoading"
          >不通过</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitConfirm(true)"
          :loading="confirmLoading"
          >通过</el-button
        >
      </div>
    </el-dialog>
    <!-- 确认需求单-E -->
    <!-- 审批进度-S -->
    <el-drawer
      title="运营管理系统审批进度"
      :visible.sync="processVisible"
      @close="processVisible = false"
      size="70%"
    >
      <div class="tag-title" slot="title">
        运营管理系统审批进度<el-tag class="ml10">{{
          omApproveStatusName
        }}</el-tag>
      </div>
      <div class="info-title" type="flex">
        <div>申请单号：{{ omApproveNo }}</div>
        <!-- <el-button type="text" @click="showApproveInfo"
          >付款单类查看金蝶审批信息</el-button
        > -->
      </div>
      <Timeline
        :list="processList"
        operateTypeTitle="itemCodeName"
        operatorNameTitle="operateEmpName"
        createTimeTitle="operateTime"
        operateDetailTitle="operateRemark"
      ></Timeline>
      <el-dialog
        :visible.sync="approveInfoVisible"
        :modal-append-to-body="false"
        append-to-body
        width="35%"
        :close-on-click-modal="true"
      >
        <el-form
          label-position="right"
          label-width="180px"
          :model="jdApproveForm"
        >
          <el-form-item label="金蝶付款单状态：">
            <span>{{ jdApproveForm.jdApproveStatusName }}</span>
          </el-form-item>
          <el-form-item label="金蝶当前审批人信息：">
            <span>{{ jdApproveForm.jdApproveUser }}</span>
          </el-form-item>
        </el-form>
      </el-dialog>
    </el-drawer>
    <!-- 审批进度-E -->
    <!-- 关联申请单号-S -->
    <el-dialog
      title="关联申请单号"
      :visible.sync="associateVisible"
      @close="closeAssociateDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="associateForm"
        ref="associateForm"
        label-width="110px"
        :rules="associateFormRules"
      >
        <el-form-item label="申请单号" prop="omApproveNo">
          <el-input
            v-model="associateForm.omApproveNo"
            placeholder="请输入运营管理系统的申请单号"
            maxlength="20"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click.stop="closeAssociateDialog"
          :loading="associateLoading"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitAssociate"
          :loading="associateLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 关联申请单号-E -->
    <!-- 邮寄时间-S -->
    <el-dialog
      title="邮寄时间"
      :visible.sync="timeVisible"
      @close="closeTimeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="timeForm" ref="timeForm" label-width="110px">
        <el-form-item label="协议寄出时间" prop="sendDate">
          <el-date-picker
            v-model="timeForm.sendDate"
            type="date"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="协议收回时间" prop="backDate">
          <el-date-picker
            v-model="timeForm.backDate"
            type="date"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeTimeDialog" :loading="timeLoading"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitTime"
          :loading="timeLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 邮寄时间-E -->
    <!-- 归档-S -->
    <el-dialog
      title="协议归档"
      :visible.sync="documentationVisible"
      @close="closeDocumentationDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="documentationForm"
        ref="documentationForm"
        label-width="110px"
      >
        <el-form-item label="备注" prop="fileRemark">
          <el-input
            type="textarea"
            v-model="documentationForm.fileRemark"
            maxlength="500"
            show-word-limit
            placeholder="500个字符以内"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click.stop="closeDocumentationDialog"
          :loading="documentationLoading"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click.stop="submitDocumentation"
          :loading="documentationLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 归档-E -->
    <!-- 附件-S -->
    <el-dialog
      title="查看附件"
      :visible.sync="fileVisible"
      @close="fileVisible = false"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <div class="mb20">
        <el-button
          type="primary"
          @click="handleDownload"
          :loading="downloadLoading"
          v-has-permi="['demandPool:demand:batchDownload']"
          >批量下载</el-button
        >
      </div>
      <vxe-table :data="fileList" align="center" ref="fileTable">
        <vxe-column type="checkbox" width="60"></vxe-column>
        <vxe-column field="docName" title="文件名称">
          <template #default="{ row, $rowIndex }">
            <el-link @click="handlePreview($rowIndex)">{{
              row.docName
            }}</el-link>
          </template>
        </vxe-column>
        <vxe-column field="uploadTime" title="上传时间"></vxe-column>
        <vxe-column field="uploaderName" title="上传人"></vxe-column>
      </vxe-table>
      <PreviewFiles
        :initial-index="previewIndex"
        v-if="showViewer"
        :on-close="
          () => {
            showViewer = false;
          }
        "
        :url-list="fileList"
        :fileOptions="{ url: 'storePath', name: 'docName' }"
      />
      <!-- <div
        v-for="(item, index) in fileList"
        :key="index"
        style="margin-bottom: 10px;"
      >
        <el-link :href="item.storePath" target="_blank">{{
          item.docName
        }}</el-link>
      </div> -->
    </el-dialog>
    <!-- 附件-E -->
    <!-- 备注-S -->
    <el-dialog
      title="备注"
      :visible.sync="remarkVisible"
      @close="closeRemarkDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="remarkForm"
        ref="remarkForm"
        label-width="110px"
        :rules="remarkFormRules"
      >
        <el-form-item label="需求属性" prop="demandAttributes">
          <el-radio-group v-model="remarkForm.demandAttributes">
            <el-radio label="0">标准需求</el-radio>
            <el-radio label="1">非标准需求</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="remarkForm.remark"
            type="textarea"
            :rows="5"
            maxlength="1000"
            show-word-limit
            placeholder="请输入"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="附件" prop="docList">
          <Upload
            ref="uploadFiles"
            :accept="'.jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .pdf'"
            multiple
            v-model="remarkForm.docList"
            :maxSize="500"
          ></Upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeRemarkDialog">取 消</el-button>
        <el-button
          :loading="remarkLoading"
          @click.stop="saveRemark"
          type="primary"
          >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 备注-E -->
    <!-- 完结-S -->
    <el-dialog
      title="确认完结"
      :visible.sync="completeVisible"
      @close="closeCompleteDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="completeForm"
        ref="completeForm"
        label-width="110px"
        :rules="completeFormRules"
      >
        <el-form-item label="完结原因" prop="finishReason">
          <el-input
            v-model="completeForm.finishReason"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="请输入手动完结原因，500字以内"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeCompleteDialog">取 消</el-button>
        <el-button
          :loading="completeLoading"
          @click.stop="saveComplete"
          type="primary"
          >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 完结-E -->
    <!-- 加补材料-S -->
    <el-dialog
      title="加补材料"
      :visible.sync="suppleVisible"
      @close="closeSuppleDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="suppleForm"
        ref="suppleForm"
        label-width="110px"
        :rules="suppleFormRules"
      >
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="suppleForm.remark"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="请输入，500字以内"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeSuppleDialog">取 消</el-button>
        <el-button
          :loading="suppleLoading"
          @click.stop="saveSupple"
          type="primary"
          >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 加补材料-E -->
  </div>
</template>

<script>
import Upload from "@/components/Upload/upload.vue";
import Timeline from "@/components/Timeline/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import { getToken } from "@/utils/auth";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import { downLoadUrl2Blob } from "@/api/common.js";
import { fileDownLoad } from "@/utils/downLoad.js";
import { listAllUser, listDept } from "@/api/common";
import {
  queryDemandList,
  exportExcel,
  deleteDemand,
  withdrawDemand,
  acceptDemand,
  restoreDemand,
  transferDemand,
  confirmDemand,
  queryProcess,
  associateDemand,
  submitMailTime,
  submitDocumentation,
  submitRemark,
  submitComplete,
  submitSupple,
  copyDemand,
  queryJdApproveInfo,
} from "@/api/demandPool/index.js";
import {
  queryBusinessFirstLevel,
  queryBusinessChildrenList,
  queryDemandFirstLevel,
  queryDemandChildrenList,
} from "@/api/demandPool/dashboard.js";
import moment from "moment";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { mapGetters } from "vuex";
export default {
  name: "demandPool",
  components: {
    AdvancedForm,
    GridTable,
    Treeselect,
    Timeline,
    Upload,
    PreviewFiles,
  },
  data() {
    return {
      jdApproveForm: {
        jdApproveStatusName: "",
        jdApproveUser: "",
      },
      approveInfoVisible: false,
      omApproveNo: "",
      downloadLoading: false,
      showViewer: false,
      previewIndex: 0,
      suppleLoading: false,
      suppleVisible: false,
      suppleForm: { remark: "" },
      suppleFormRules: {
        remark: [{ required: true, message: "请输入备注", trigger: "change" }],
      },
      completeVisible: false,
      completeLoading: false,
      completeForm: { finishReason: "" },
      completeFormRules: {
        finishReason: [
          { required: true, message: "请输入完结原因", trigger: "change" },
        ],
      },
      omApproveStatusName: "",
      fileVisible: false,
      fileList: [],
      documentationVisible: false,
      documentationLoading: false,
      documentationForm: { fileRemark: undefined },
      timeVisible: false,
      timeLoading: false,
      timeForm: { sendDate: "", backDate: "" },

      timeFormRules: {
        sendDate: [
          { required: true, message: "请选择协议寄出时间", trigger: "change" },
        ],
        backDate: [
          { required: true, message: "请选择协议收回时间", trigger: "change" },
        ],
      },
      associateVisible: false,
      associateLoading: false,
      associateForm: { omApproveNo: undefined },
      associateFormRules: {
        omApproveNo: [
          { required: true, message: "请输入申请单号", trigger: "blur" },
        ],
      },
      processVisible: false,
      processList: [],
      confirmVisible: false,
      confirmForm: {
        confirmReason: undefined,
      },
      confirmLoading: false,
      transferVisible: false,
      demandId: "",
      transferForm: {
        deptId: undefined,
        handleUser: undefined,
      },
      transferRules: {
        handleUser: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },
      transferLoading: false,
      deptOptions: [],
      userOption: [],
      // config: [],
      columns: [
        // {
        //   type: "checkbox",
        //   customWidth: 60,
        // },
        {
          field: "demandNo",
          title: "需求单号",
        },
        {
          field: "businessType",
          title: "业务类型",
          customWidth: 200,
        },
        {
          field: "demandType",
          title: "需求类型",
          customWidth: 200,
        },
        {
          field: "partnerName",
          title: "合作商名称",
          customWidth: 200,
        },
        {
          field: "submitter",
          title: "提交人",
        },
        {
          field: "acceptRemindTime",
          title: "受理到期时间",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.acceptRemindTag == "Y" ? "color:red" : ""}>
                  {row.acceptRemindTime}
                </span>
              );
            },
          },
          titlePrefix: {
            message:
              "提交需求单后，按受理时效计算受理到期时间；按受理预警提醒时效来提前标红。",
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "handleRemindTime",
          title: "处理到期时间",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.handleRemindTag == "Y" ? "color:red" : ""}>
                  {row.handleRemindTime}
                </span>
              );
            },
          },
          titlePrefix: {
            message:
              "接收需求单后，按处理时效计算处理到期时间；按处理预警提醒时效来提前标红。",
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "submitOmRemindTime",
          title: "提交运管到期时间",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.submitOmRemindTag == "Y" ? "color:red" : ""}>
                  {row.submitOmRemindTime}
                </span>
              );
            },
          },
          titlePrefix: {
            message:
              "确认通过后，按提交运管时效计算到期时间；按提交运管提醒时效来提前标红。",
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "submitTime",
          title: "提交时间",
        },
        {
          field: "demandStatus",
          title: "需求单状态",
          formatter: ({ cellValue }) => {
            return (
              this.demandStatusOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "omApproveStatus",
          title: "运管审批状态",
        },
        {
          field: "omApproveNo",
          title: "运管审批单号",
        },
        {
          field: "receiveTime",
          title: "接收时间",
        },
        {
          field: "receiveTimeoutDuration",
          title: "接收超时时长",
        },
        {
          field: "handleUserName",
          title: "处理人",
        },
        {
          field: "confirmTime",
          title: "确认时间",
        },
        {
          field: "handleTimeoutDuration",
          title: "处理超时时长",
        },
        {
          field: "submitOmTime",
          title: "提交运管时间",
        },
        {
          field: "submitOmTimeoutDuration",
          title: "提交运管超时时长",
        },
        {
          field: "finishTime",
          title: "完结时间",
        },
        {
          field: "isNeedMaterial",
          title: "是否需要加补材料",
          formatter: ({ cellValue }) => {
            return cellValue === "Y"
              ? "是"
              : cellValue === "N"
              ? "否"
              : cellValue;
          },
        },
        {
          field: "isHaveMaterial",
          title: "是否已补材料",
          formatter: ({ cellValue }) => {
            return cellValue === "Y"
              ? "是"
              : cellValue === "N"
              ? "否"
              : cellValue;
          },
        },
        {
          field: "demandAttributes",
          title: "标准属性",
          formatter: ({ cellValue }) => {
            return cellValue == "0"
              ? "标准需求"
              : cellValue == "1"
              ? "非标准需求"
              : cellValue;
          },
        },
        {
          field: "addRemark",
          title: "备注内容",
        },
        {
          title: "操作",
          minWidth: 340,
          // showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        oneBusinessType: "",
        oneDemandType: "",
        twoDemandType: "",
        twoBusinessType: "",
      },
      total: 0,
      loading: false,
      tableId: "demandPoolList",
      recordList: [],
      demandStatusOptions: [],
      orderStatusOptions: [],
      tariffTypeOptions: [],
      chargeStatusOptions: [],
      handRow: {
        stationIds: [],
      },
      businessFirstLevelList: [],
      businessChildrenList: [],
      demandFirstLevelList: [],
      demandChildrenList: [],
      // 工具栏按钮
      toolBarBtnList: [
        {
          title: "新增需求",
          clickFn: () => {
            //编辑页面开启缓存 如果重新打开其他编辑 则将之前的编辑页面先关闭
            const page = this.$store.state.tagsView.visitedViews?.find(
              (x) => x.path === "/demandPoolManage/demandPool/addDemand"
            );
            if (page) {
              this.$store.dispatch("tagsView/delView", page);
            }
            this.$nextTick(() => {
              this.$router.push({
                path: "/demandPoolManage/demandPool/addDemand",
              });
            });
          },
          permission: "demandPool:demand:add",
        },
        {
          title: "业务类型",
          clickFn: () => {
            this.$router.push({ path: "/demandPool/businessType" });
          },
          permission: "demandPool:businessType:list",
        },
        {
          title: "需求类型",
          clickFn: () => {
            this.$router.push({ path: "/demandPool/demandType" });
          },
          permission: "demandPool:demand:type",
        },
        {
          title: "分组管理",
          clickFn: () => {
            this.$router.push({ path: "/demandPool/setProcessGroup" });
          },
          permission: "demandPool:group:manage",
        },
        {
          title: "账号数据权限",
          clickFn: () => {
            this.$router.push({ path: "/demandPool/setDataPermission" });
          },
          permission: "demandPool:user:manage",
        },
      ],
      //操作列按钮
      operationBtnList: [
        {
          title: "复制",
          command: "copy",
          clickFn: (row) => {
            this.handleCopy(row);
          },
          permission: "demandPool:demand:copy",
          show: (row) => {
            return true;
          },
        },
        {
          title: "修改",
          command: "edit",
          clickFn: (row) => {
            //编辑页面开启缓存 如果重新打开其他编辑 则将之前的编辑页面先关闭
            const page = this.$store.state.tagsView.visitedViews?.find(
              (x) => x.path === "/demandPoolManage/demandPool/editDemand"
            );
            if (page) {
              this.$store.dispatch("tagsView/delView", page);
            }
            this.$nextTick(() => {
              this.$router.push({
                path: "/demandPoolManage/demandPool/editDemand",
                query: {
                  demandId: row.demandId,
                },
              });
            });
          },
          permission: "demandPool:demand:edit",
          show: (row) => {
            //BD角色
            const isProjBd = this.roles.includes("projBd");
            // 草稿 待接收  已驳回
            const demandStatusList = ["0", "1", "5"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            return isProjBd && demandStatus && this.userId === row.createBy;
          },
        },
        {
          title: "受理",
          command: "acceptance",
          clickFn: (row) => {
            this.handleAccept(row);
          },
          permission: "demandPool:demand:acceptance",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 待接收
            const demandStatusList = ["1"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "确认",
          command: "confirm",
          clickFn: (row) => {
            this.handleConfirm(row);
          },
          permission: "demandPool:demand:confirm",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 已接收  已驳回
            const demandStatusList = ["3", "5"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "加补材料",
          command: "supplement",
          clickFn: (row) => {
            this.handleSupplement(row);
          },
          permission: "demandPool:demand:supplement",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");
            // 处理中
            const demandStatusList = ["4"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);
            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "完结",
          command: "complete",
          clickFn: (row) => {
            this.handleComplete(row);
          },
          permission: "demandPool:demand:complete",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");
            // 处理中
            const demandStatusList = ["4"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);
            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "转派",
          command: "transfer",
          clickFn: (row) => {
            this.handleTransfer(row);
          },
          permission: "demandPool:demand:transfer",
          show: (row) => {
            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 待接收 已接收 处理中 已驳回 已完结
            const demandStatusList = ["1", "3", "4", "5", "6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            return isSupervisor && demandStatus;
          },
        },
        {
          title: "附件",
          clickFn: (row) => {
            this.handleFile(row);
          },
          permission: "demandPool:demand:file",
          show: (row) => {
            return true;
          },
        },
        {
          title: "详情",
          clickFn: (row) => {
            this.$router.push({
              path: "/demandPoolManage/demandPool/detail",
              query: { demandId: row.demandId },
            });
          },
          permission: "demandPool:demand:detail",
          show: (row) => {
            return true;
          },
        },
      ],
      //操作列 更多下按钮
      moreBtnList: [
        {
          title: "删除",
          command: "delete",
          clickFn: (row) => {
            this.handleDelete(row);
          },
          permission: "demandPool:demand:delete",
          show: (row) => {
            //BD角色
            const isProjBd = this.roles.includes("projBd");
            // 草稿 待接收  已撤销  已驳回
            const demandStatusList = ["0", "1", "2", "5"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            return isProjBd && demandStatus && this.userId === row.createBy;
          },
        },
        {
          title: "撤销",
          command: "withdraw",
          clickFn: (row) => {
            this.handleWithdraw(row);
          },
          permission: "demandPool:demand:withdraw",
          show: (row) => {
            //BD角色
            const isProjBd = this.roles.includes("projBd");
            // 待接收
            const demandStatusList = ["1"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            return isProjBd && demandStatus && this.userId === row.createBy;
          },
        },
        {
          title: "恢复",
          command: "restore",
          clickFn: (row) => {
            this.handleRestore(row);
          },
          permission: "demandPool:demand:restore",
          show: (row) => {
            //BD角色
            const isProjBd = this.roles.includes("projBd");
            // 已撤销
            const demandStatusList = ["2"];
            const demandStatus = demandStatusList.includes(row.demandStatus);
            return isProjBd && demandStatus && this.userId === row.createBy;
          },
        },
        {
          title: "审批进度",
          command: "process",
          clickFn: (row) => {
            this.handleProcess(row);
          },
          permission: "demandPool:demand:process",
          show: (row) => {
            return row.omApproveNo != undefined && row.omApproveNo != "";
          },
        },
        {
          title: "关联申请编号",
          command: "associate",
          clickFn: (row) => {
            this.handleAssociate(row);
          },
          permission: "demandPool:demand:associate",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            // 处理中
            const demandStatusList = ["4", "6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "邮寄时间",
          command: "mail",
          clickFn: (row) => {
            this.handleMailTime(row);
          },
          permission: "demandPool:demand:mail",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 已完结
            const demandStatusList = ["6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "归档",
          command: "documentation",
          clickFn: (row) => {
            this.handleDocumentation(row);
          },
          permission: "demandPool:demand:documentation",
          show: (row) => {
            //专员
            const isCommissioner = this.roles.includes("commissioner");

            //主管
            const isSupervisor = this.roles.includes("supervisor");

            // 已完结
            const demandStatusList = ["6"];
            const demandStatus = demandStatusList.includes(row.demandStatus);

            // 如果角色是专员 还需要根据分组管理判断 专员是否有权限处理
            const isGroup = row.groupUserIds.includes(this.userId);

            return (
              ((isCommissioner && isGroup) || isSupervisor) && demandStatus
            );
          },
        },
        {
          title: "备注",
          command: "remark",
          clickFn: (row) => {
            this.handleRemark(row);
          },
          permission: "demandPool:demand:remark",
          show: (row) => {
            return true;
          },
        },
      ],
      checkStatusOptions: [
        { dictLabel: "审批中", dictValue: "审批中" },
        { dictLabel: "审批通过", dictValue: "审批通过" },
      ],
      token: "",
      remarkVisible: false,
      remarkLoading: false,
      remarkForm: {
        remark: "",
        demandAttributes: "",
        docList: [],
      },
      remarkFormRules: {
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" },
          { max: 1000, message: "1000字符以内", trigger: "blur" },
        ],
      },
    };
  },
  created() {},
  activated() {
    this.token = getToken();
    this.getTreeselect();
    this.getListUser();

    this.getDicts("cm_demand_status").then((response) => {
      this.demandStatusOptions = response.data;
    });
    // this.getDicts("cm_approve_status").then((response) => {
    //   this.checkStatusOptions = response.data;
    // });

    if (this.$route.params) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    Promise.all([this.queryBusinessType(), this.queryDemandType()]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          // this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    handleCopy(row) {
      this.$confirm("是否确认复制此需求申请？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          demandId: row.demandId,
        };
        copyDemand(data).then((res) => {
          if (res?.success) {
            this.$message.success("复制成功");
            //更新列表
            this.getList();
          }
        });
      });
    },
    async handleDownload() {
      const list = this.$refs.fileTable.getCheckboxRecords();
      if (list?.length === 0) {
        this.$message.warning("请勾选要下载的文件");
        return;
      }
      const downloadPromises = list.map(async (x) => {
        try {
          const res = await downLoadUrl2Blob({ fileUrl: x.storePath });
          if (res) {
            await fileDownLoad(res, x.docName);
          }
        } catch (error) {
          // 处理错误，例如显示提示信息
          console.error(`Error downloading file: ${x.docName}`, error);
        }
      });
      this.downloadLoading = true;
      try {
        // 等待所有文件下载完成
        await Promise.all(downloadPromises);
      } finally {
        // 无论成功还是失败，都将 downloadLoading 设为 false
        this.downloadLoading = false;
      }
    },

    //附件预览
    handlePreview(index) {
      this.showViewer = true;
      this.previewIndex = index;
    },
    //加补材料
    handleSupplement(row) {
      this.suppleVisible = true;
      this.demandId = row.demandId;
    },
    closeSuppleDialog() {
      this.suppleVisible = false;
      this.$refs.suppleForm.resetFields();
    },
    async saveSupple() {
      this.$refs.suppleForm.validate(async (valid) => {
        if (!valid) return false;
        this.suppleLoading = true;
        const params = {
          demandId: this.demandId,
          ...this.suppleForm,
        };
        const res = await submitSupple(params);
        this.suppleLoading = false;
        if (res?.code == "10000") {
          this.$message.success("提交成功");
          this.suppleVisible = false;
          this.getList();
        }
      });
    },
    //完结
    handleComplete(row) {
      this.completeVisible = true;
      this.demandId = row.demandId;
    },
    closeCompleteDialog() {
      this.completeVisible = false;
      this.$refs.completeForm.resetFields();
    },
    async saveComplete() {
      this.$refs.completeForm.validate(async (valid) => {
        if (!valid) return false;
        this.completeLoading = true;
        const params = {
          demandId: this.demandId,
          finishReason: this.completeForm.finishReason,
        };
        const res = await submitComplete(params);
        this.completeLoading = false;
        if (res?.code == "10000") {
          this.$message.success("提交成功");
          this.completeVisible = false;
          this.getList();
        }
      });
    },
    async saveRemark() {
      this.$refs.remarkForm.validate(async (valid) => {
        if (!valid) return false;
        this.remarkLoading = true;
        const params = {
          demandId: this.demandId,
          ...this.remarkForm,
        };
        console.log(this.remarkForm, "提交");
        const res = await submitRemark(params);
        this.remarkLoading = false;
        if (res?.code == "10000") {
          this.$message.success("备注提交成功");
          this.remarkVisible = false;
          this.getList();
        }
      });
    },
    closeRemarkDialog() {
      this.remarkVisible = false;
      this.$refs.remarkForm.resetFields();
    },
    handleRemark(row) {
      this.remarkVisible = true;
      this.demandId = row.demandId;
    },
    checkPermission,
    async queryBusinessType() {
      const res = await queryBusinessFirstLevel();
      if (res?.code === "10000") {
        this.businessFirstLevelList = res.data;
      }
    },
    async queryDemandType() {
      const res = await queryDemandFirstLevel();
      if (res?.code === "10000") {
        this.demandFirstLevelList = res.data;
      }
    },
    async handleFirstDemandChange(val) {
      console.log(val, "---val");
      if (!val) {
        this.demandChildrenList = [];
      } else {
        const res = await queryDemandChildrenList({ ids: [val] });
        if (res?.code === "10000") {
          this.demandChildrenList = res.data;
        }
      }
      this.searchForm.twoDemandType = "";
    },
    async handleFirstBusinessChange(val) {
      if (!val) {
        this.businessChildrenList = [];
      } else {
        const res = await queryBusinessChildrenList({ ids: [val] });
        if (res?.code === "10000") {
          this.businessChildrenList = res.data;
        }
      }
      this.searchForm.twoBusinessType = "";
    },

    //查看附件
    handleFile(row) {
      this.$refs.fileTable?.clearCheckboxRow();
      this.fileVisible = true;
      const list = [];
      JSON.parse(row.formJson)?.map((x) => {
        if (x.type === "upload") {
          const arr = x.fileList?.map((x) => {
            return {
              ...x,
              storePath: x.url,
              docName: x.name,
              uploadTime: row.submitTime,
              uploaderName: row.submitter,
            };
          });
          list.push(...arr);
        }
      });
      console.log(list, "list");
      this.fileList = [...(row.docList || []), ...list];
    },
    //归档
    handleDocumentation(row) {
      this.documentationVisible = true;
      this.demandId = row.demandId;
    },
    closeDocumentationDialog() {
      this.documentationVisible = false;
      this.$refs.documentationForm.resetForm();
    },
    submitDocumentation() {
      let params = {
        ...this.documentationForm,
        demandId: this.demandId,
      };
      this.documentationLoading = true;
      submitDocumentation(params)
        .then((res) => {
          this.documentationLoading = false;
          if (res?.code === "10000") {
            this.closeDocumentationDialog();
            this.getList();
            this.$message.success("归档成功");
          }
        })
        .catch(() => {
          this.documentationLoading = false;
        });
    },
    //邮寄时间
    handleMailTime(row) {
      this.demandId = row.demandId;
      this.timeVisible = true;
      this.timeForm = { sendDate: row.sendDate, backDate: row.backDate };
    },
    submitTime() {
      if (!this.timeForm.sendDate && !this.timeForm.backDate) {
        this.$message.warning("请选择邮寄时间");
        return;
      }
      let params = {
        ...this.timeForm,
        demandId: this.demandId,
      };
      this.timeLoading = true;
      submitMailTime(params)
        .then((res) => {
          this.timeLoading = false;
          if (res?.code === "10000") {
            this.closeTimeDialog();
            this.getList();
            this.$message.success("关联成功");
          }
        })
        .catch(() => {
          this.timeLoading = false;
        });
    },
    closeTimeDialog() {
      this.timeVisible = false;
      this.$refs.timeForm.resetFields();
    },
    //关联申请编号
    handleAssociate(row) {
      this.demandId = row.demandId;
      this.associateVisible = true;
      this.associateForm.omApproveNo = row.omApproveNo;
    },
    submitAssociate() {
      this.$refs.associateForm.validate((valid) => {
        if (!valid) {
          return false;
        }
        let params = {
          ...this.associateForm,
          demandId: this.demandId,
        };
        this.associateLoading = true;
        associateDemand(params)
          .then((res) => {
            this.associateLoading = false;
            if (res?.code === "10000") {
              this.closeAssociateDialog();
              this.getList();
              this.$message.success("关联成功");
            }
          })
          .catch(() => {
            this.associateLoading = false;
          });
      });
    },
    closeAssociateDialog() {
      this.associateVisible = false;
      this.$refs.associateForm.resetFields();
    },
    //审批进度
    handleProcess(row) {
      this.processVisible = true;
      this.omApproveNo = row.omApproveNo;
      queryProcess({ omApproveNo: row.omApproveNo }).then((res) => {
        if (res?.code == 10000) {
          this.processList = res.data;
        }
      });
      this.omApproveStatusName = row.omApproveStatus;
    },
    showApproveInfo() {
      this.approveInfoVisible = true;
      queryJdApproveInfo({ omApproveNo: this.omApproveNo }).then((res) => {
        if (res?.code === "10000") {
          this.jdApproveForm = res.data;
        }
      });
    },
    //确认
    handleConfirm(row) {
      this.confirmVisible = true;
      this.demandId = row.demandId;
    },
    closeConfirmDialog() {
      this.$refs.confirmForm.resetFields();
      this.confirmVisible = false;
    },
    submitConfirm(isPass) {
      if (!isPass && !this.confirmForm.confirmReason) {
        this.$message.warning("审核不通过原因不能为空！");
        return false;
      }
      let params = {
        ...this.confirmForm,
        demandId: this.demandId,
        confirmResult: isPass ? 1 : 0,
      };
      this.confirmLoading = true;
      confirmDemand(params)
        .then((res) => {
          this.confirmLoading = false;
          if (res?.code === "10000") {
            this.closeConfirmDialog();
            this.getList();
            this.$message.success("确认成功");
          }
        })
        .catch(() => {
          this.confirmLoading = false;
        });
    },
    //转派
    handleTransfer(row) {
      this.transferVisible = true;
      this.demandId = row.demandId;
    },
    closeTransferDialog() {
      this.$refs.transferForm.resetFields();
      this.transferVisible = false;
    },
    submitTransfer() {
      this.$refs.transferForm.validate(async (valid) => {
        if (valid) {
          let params = {
            handleUser: this.transferForm.handleUser,
            demandId: this.demandId,
          };
          this.transferLoading = true;
          transferDemand(params)
            .then((res) => {
              this.transferLoading = false;
              if (res?.code === "10000") {
                this.closeTransferDialog();
                this.getList();
                this.$message.success("指派成功");
              }
            })
            .catch(() => {
              this.transferLoading = false;
            });
        }
      });
    },
    handleDelete(row) {
      this.$confirm("确定删除该需求单吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          demandId: row.demandId,
        };
        deleteDemand(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getList();
          }
        });
      });
    },
    handleWithdraw(row) {
      this.$confirm("确定撤销该需求单吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          demandId: row.demandId,
        };
        withdrawDemand(data).then((res) => {
          if (res?.success) {
            this.$message.success("撤销成功");
            //更新列表
            this.getList();
          }
        });
      });
    },
    handleAccept(row) {
      this.$confirm("确定接收该需求单吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          demandId: row.demandId,
        };
        acceptDemand(data).then((res) => {
          if (res?.success) {
            this.$message.success("接收成功");
            //更新列表
            this.getList();
          }
        });
      });
    },
    handleRestore(row) {
      this.$confirm("确定恢复该需求单吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        // center: true,
      }).then(() => {
        let data = {
          demandId: row.demandId,
        };
        restoreDemand(data).then((res) => {
          if (res?.success) {
            this.$message.success("恢复成功");
            //更新列表
            this.getList();
          }
        });
      });
    },
    handleCommand(command, row) {
      this.moreBtnList?.find((x) => x.command == command)?.clickFn(row);
      console.log(command, row, "command");
    },
    handleParentChange(obj) {
      console.log("change", obj);
      //   const { val, key } = obj;
      //   if(key===''){}
    },
    showDetail(row) {
      this.$router.push({
        path: "/chargingOrder/detail",
        query: {
          orderId: row.orderId,
        },
      });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "acceptRangeTime",
          title: "接收时间",
          startFieldName: "receiveBeginTime",
          endFieldName: "receiveEndTime",
        },
        {
          field: "submitRangeTime",
          title: "提交时间",
          startFieldName: "submitBeginTime",
          endFieldName: "submitEndTime",
        },
        {
          field: "verifyRangeTime",
          title: "确认时间",
          startFieldName: "confirmBeginTime",
          endFieldName: "confirmEndTime",
        },
        {
          field: "operateRangeTime",
          title: "提交运管时间",
          startFieldName: "submitOmBeginTime",
          endFieldName: "submitOmEndTime",
        },
        {
          field: "endRangeTime",
          title: "完结时间",
          startFieldName: "finishBeginTime",
          endFieldName: "finishEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    handleExport() {
      let text = "是否确认导出所有数据?";

      const params = {
        ...this.searchForm,
      };
      this.handleTimeRange(params);
      console.log("查询", params);
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      this.handleTimeRange(params);
      console.log("查询", params);

      queryDemandList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.transferForm.handleUser = undefined;
      this.getListUser(data.deptId);
    },
    beforeClearAll() {
      this.transferForm.handleUser = undefined;
      this.transferForm.deptId = undefined;
      this.getListUser();
    },
    //获取用户列表
    async getListUser(param) {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      if (param) {
        params.orgNo = Number(param);
      }
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    config() {
      return [
        {
          key: "demandNo",
          title: "需求单号",
          type: "input",
          placeholder: "请输入需求单号",
        },
        {
          key: "submitter",
          title: "提交人",
          type: "input",
          placeholder: "请输入提交人",
        },
        {
          key: "submitRangeTime",
          title: "提交时间",
          type: "dateRange",
          placeholder: "请选择提交时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "demandType",
          title: "需求类型",
          type: "slot",
          colNum: 16,
        },
        {
          key: "demandStatus",
          title: "需求状态",
          type: "select",
          placeholder: "请选择需求状态",
          options: this.demandStatusOptions,
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "slot",
          colNum: 16,
        },
        {
          key: "handleUserName",
          title: "处理人",
          type: "input",
          placeholder: "请输入处理人",
        },
        {
          key: "acceptRangeTime",
          title: "接收时间",
          type: "dateRange",
          placeholder: "请选择接收时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "omApproveStatus",
          title: "运管审批状态",
          type: "select",
          placeholder: "请选择运管审批状态",
          options: this.checkStatusOptions,
        },
        {
          key: "operateRangeTime",
          title: "提交运管时间",
          type: "dateRange",
          placeholder: "请选择提交运管时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "verifyRangeTime",
          title: "确认时间",
          type: "dateRange",
          placeholder: "请选择确认时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "endRangeTime",
          title: "完结时间",
          type: "dateRange",
          placeholder: "请选择完结时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "partnerName",
          title: "合作商名称",
          type: "input",
          placeholder: "请输入合作商名称",
        },
        {
          key: "isNeedMaterial",
          title: "是否需要加补材料",
          type: "select",
          placeholder: "请选择是否需要加补材料",
          options: [
            { dictValue: "Y", dictLabel: "是" },
            { dictValue: "N", dictLabel: "否" },
          ],
        },
        {
          key: "isHaveMaterial",
          title: "是否已补材料",
          type: "select",
          placeholder: "请选择是否已补材料",
          options: [
            { dictValue: "Y", dictLabel: "是" },
            { dictValue: "N", dictLabel: "否" },
          ],
        },
        {
          key: "receiveIsTimeout",
          title: "受理是否超时",
          type: "select",
          placeholder: "请选择受理是否超时",
          options: [
            { dictValue: "Y", dictLabel: "是" },
            { dictValue: "N", dictLabel: "否" },
          ],
        },
        {
          key: "submitOmIsTimeout",
          title: "提交运管是否超时",
          type: "select",
          placeholder: "请选择提交运管是否超时",
          options: [
            { dictValue: "Y", dictLabel: "是" },
            { dictValue: "N", dictLabel: "否" },
          ],
        },
        {
          key: "handleIsTimeout",
          title: "处理是否超时",
          type: "select",
          placeholder: "请选择处理是否超时",
          options: [
            { dictValue: "Y", dictLabel: "是" },
            { dictValue: "N", dictLabel: "否" },
          ],
        },
        {
          key: "demandAttributes",
          title: "需求属性",
          type: "select",
          placeholder: "请选择需求属性",
          options: [
            { dictValue: "0", dictLabel: "标准需求" },
            { dictValue: "1", dictLabel: "非标准需求" },
          ],
        },
      ];
    },
  },
};
</script>

<style lang="less" scoped>
.tag-title {
  // margin: 10px 40px 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.info-title {
  margin: 10px 40px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
