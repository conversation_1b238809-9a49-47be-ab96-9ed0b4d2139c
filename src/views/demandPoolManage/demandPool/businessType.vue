// 业务类型
<template>
  <div class="app-container">
    <div class="page-header">
      <h3>业务类型</h3>
      <el-button
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click.stop="handleAdd(1)"
        v-has-permi="['demandPool:businessType:add']"
        >新增</el-button
      >
    </div>
    <el-card>
      <el-tree
        :data="data"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :props="defaultProps"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div class="node-label">{{ node.label }}</div>
          <div>
            <el-button
              type="text"
              size="mini"
              @click="handleAdd(data.typeLevel + 1, data)"
              v-if="data.typeLevel <= 1"
              icon="el-icon-plus"
              v-has-permi="['demandPool:businessType:add']"
            >
              新增
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(data.typeLevel, data)"
              icon="el-icon-edit"
              v-has-permi="['demandPool:businessType:edit']"
            >
              修改
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleDelete(data.id)"
              icon="el-icon-minus"
              style="color: red"
              v-has-permi="['demandPool:businessType:delete']"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-tree>
    </el-card>
    <el-dialog
      :title="title"
      :visible.sync="modalVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="modalForm"
        ref="modalForm"
        label-width="110px"
        :rules="rules"
      >
        <el-form-item label="一级类型：" prop="firstType">
          <span v-if="addLevel == 2 || addLevel == 3">{{
            modalForm.firstType
          }}</span>
          <el-input
            v-model="modalForm.firstType"
            placeholder="请输入一级业务类型，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
        <el-form-item
          label="二级类型："
          prop="secondType"
          v-if="addLevel == 2 || addLevel == 3"
        >
          <span v-if="addLevel == 3">{{ modalForm.secondType }}</span>
          <el-input
            v-model="modalForm.secondType"
            placeholder="请输入二级业务类型，长度100个字符以内"
            maxlength="100"
            v-else
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="handleSubmit"
          :loading="submitLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/demandPool/businessType.js";
export default {
  components: {},
  data() {
    return {
      modalVisible: false,
      title: "新增业务类型",
      submitLoading: false,
      data: [
        {
          childrenList: [
            {
              createTime: "2024-02-06 14:31:44",
              id: "7",
              orgNo: "10000",
              parentId: "5",
              parentName: "抄表类型",
              tenantId: "10000",
              typeLevel: 2,
              typeName: "二级类型",
              updateTime: "2024-02-22 16:27:05",
            },
            {
              createTime: "2024-02-06 14:32:17",
              id: "8",
              orgNo: "10000",
              parentId: "5",
              parentName: "抄表类型",
              tenantId: "10000",
              typeLevel: 2,
              typeName: "二级类型-2",
              updateTime: "2024-02-22 16:27:05",
            },
          ],
          createTime: "2024-02-06 11:22:07",
          id: "5",
          orgNo: "10000",
          parentId: "0",
          tenantId: "10000",
          typeLevel: 1,
          typeName: "抄表类型",
          updateTime: "2024-02-19 14:26:43",
        },
        {
          childrenList: [
            {
              createTime: "2024-02-19 10:52:58",
              id: "19",
              orgNo: "10000",
              parentId: "18",
              parentName: "故障类",
              tenantId: "10000",
              typeLevel: 2,
              typeName: "离线--断电",
              updateTime: "2024-04-15 14:39:54",
            },
            {
              createTime: "2024-02-19 10:53:44",
              id: "20",
              orgNo: "10000",
              parentId: "18",
              parentName: "故障类",
              tenantId: "10000",
              typeLevel: 2,
              typeName: "离线--断电",
              updateTime: "2024-04-15 14:39:54",
            },
          ],
          createTime: "2024-02-19 10:52:42",
          id: "18",
          orgNo: "10000",
          parentId: "0",
          tenantId: "10000",
          typeLevel: 1,
          typeName: "故障类",
          updateTime: "2024-02-22 14:27:39",
        },
        {
          childrenList: [
            {
              createTime: "2024-02-20 16:28:47",
              id: "49",
              orgNo: "10000",
              parentId: "48",
              parentName: "离线/故障类",
              tenantId: "10000",
              typeLevel: 2,
              typeName: "离线-离线",
              updateTime: "2024-04-15 14:40:45",
            },
          ],
          createTime: "2024-02-20 16:28:36",
          id: "48",
          orgNo: "10000",
          parentId: "0",
          tenantId: "10000",
          typeLevel: 1,
          typeName: "离线/故障类",
          updateTime: "2024-02-22 15:03:29",
        },
      ],
      modalForm: {
        firstType: "",
        secondType: "",
      },
      rules: {
        firstType: [{ required: true, message: "业务类型不能为空" }],
        secondType: [{ required: true, message: "业务类型不能为空" }],
      },
      addLevel: 1,
      defaultProps: {
        children: "childrenList",
        label: "typeName",
      },
      flattenData: [],
      modalType: "add",
      parentId: undefined,
      editId: undefined,
    };
  },
  mounted() {
    this.getTreeData();
  },
  methods: {
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getTreeData() {
      api.queryTreeList({}).then((res) => {
        this.data = JSON.parse(JSON.stringify(res.data));
        this.flattenData = this.flattenArray(res.data);
        console.log("-----", this.flattenData);
      });
    },
    handleAdd(level, data) {
      console.log(data);
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "新增业务类型";
      this.modalType = "add";
      if (level == 1) {
        this.modalForm.firstType = "";
        this.parentId = undefined;
      } else if (level == 2) {
        this.modalForm.firstType = data.typeName;
        this.modalForm.secondType = "";
        this.parentId = data.id;
      }
    },
    handleEdit(level, data) {
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "修改业务类型";
      this.modalType = "edit";
      this.editId = data.id;
      if (level == 1) {
        this.modalForm.firstType = data.typeName;
      } else if (level == 2) {
        this.modalForm.firstType = data.parentName;
        this.modalForm.secondType = data.typeName;
      }
    },
    //提交
    handleSubmit() {
      const arr = [
        { level: 1, value: "firstType" },
        { level: 2, value: "secondType" },
        // { level: 3, value: "thirdType" },
      ];
      const method = this.modalType === "add" ? "addType" : "editType";
      const value = arr.find((x) => x.level === this.addLevel)?.value;

      this.$refs.modalForm.validate((valid) => {
        if (valid) {
          const params =
            this.modalType === "add"
              ? {
                  typeName: this.modalForm[value],
                  typeLevel: this.addLevel,
                  parentId: this.parentId,
                }
              : { typeName: this.modalForm[value], id: this.editId };
          console.log("提交", this.modalForm);
          this.submitLoading = true;
          api[method](params).then((res) => {
            if (res?.success) {
              this.$message.success("提交成功");
              this.getTreeData();
              this.closeDialog();
              this.submitLoading = false;
            }
          });
        } else {
          console.log("校验失败");
        }
      });
    },
    closeDialog() {
      console.log("关闭弹窗");
      this.$refs.modalForm.resetFields();
      this.modalVisible = false;
    },
    handleDelete(id) {
      this.$confirm(
        "删除后，已生成的需求单不受影响！",
        "确定要删除该业务类型吗?",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          center: true,
        }
      ).then(() => {
        let data = {
          id: id,
        };
        api.deleteType(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getTreeData();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  align-items: center;
  h3 {
    margin-right: 40px;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-label {
    width: 600px;
    word-wrap: break-word;
    white-space: normal;
  }
}
/deep/ .el-tree-node__content {
  padding-top: 10px;
  padding-bottom: 10px;
  height: auto;
}
/deep/ .el-card__body {
  max-height: 80vh;
  overflow-y: auto;
}
</style>
