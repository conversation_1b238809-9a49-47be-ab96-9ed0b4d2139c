// 账号数据权限
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        row-id="reportId"
      >
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="handleAdd(row)"
            v-has-permi="['demandPool:setPermission:edit']"
          >
            配置
          </el-button>
        </template>
        <template slot="groupMember" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="handleMemberView(row)"
            v-has-permi="['demandPool:setPermission:detail']"
            >查看</el-button
          >
        </template>
      </GridTable>
    </el-card>
    <UpdateModal ref="updateModal" @submit="queryData"></UpdateModal>
    <MemberModal ref="memberModal"></MemberModal>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { queryPermissionList } from "@/api/demandPool/setPermission.js";

import checkPermission from "@/utils/permission.js";
import UpdateModal from "./components/updateModal.vue";
import MemberModal from "./components/memberModal.vue";
export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
    UpdateModal,
    MemberModal,
  },
  data() {
    return {
      groupStatusOptions: [
        // { label: "全部", value: "all" },
        { label: "停用", value: 1 },
        { label: "启用", value: 0 },
      ],
      stationOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      editForm: { stationName: "", dateRange: [], reason: "" },
      addForm: [{ stationId: "", dateRange: [], reason: "" }],

      options: [],
      finallySearch: null,
      tableData: [],
      tableTotal: 0,
      addVisible: false, //添加站点
      editVisible: false, //忽略

      config: [],
      columns: [
        {
          field: "userName",
          title: "登录账号",
        },
        {
          field: "nickName",
          title: "用户昵称",
        },
        {
          field: "groupMember",
          title: "可查看的需求数据",
          slots: { default: "groupMember" },
        },
        {
          field: "configurationTime",
          title: "配置时间",
        },
        {
          field: "configurationBy",
          title: "配置人",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "dataPermissionList", //tableId必须项目唯一，用于缓存展示列
      recordList: [],
    };
  },
  mounted() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([this.queryData()]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    //初始化
    initConfig() {
      this.config = [
        {
          key: "userName",
          title: "登录账号",
          type: "input",
          placeholder: "请输入登录账号",
        },
        {
          key: "nickName",
          title: "用户昵称",
          type: "input",
          placeholder: "请输入用户昵称",
        },
      ];
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryData(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };

      // this.finallySearch = args;
      queryPermissionList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //新增/修改
    handleAdd(row) {
      this.$refs.updateModal.openDialog(row);
    },

    //查看组内人员
    handleMemberView(row) {
      this.$refs.memberModal.openDialog(row);
    },
    closeEditDialog() {
      this.editVisible = false;
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
