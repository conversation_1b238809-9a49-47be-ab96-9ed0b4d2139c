<template>
  <el-dialog
    title="可查看的需求数据"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="70%"
  >
    <div class="app-container">
      <el-card>
        <GridTable
          ref="gridTable"
          :columns="columns"
          :tableData="tableData"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="tableTotal"
          @changePage="changePage"
          :loading="loading"
          :tableId="tableId"
          row-id="reportId"
        >
        </GridTable>
      </el-card>
    </div>
  </el-dialog>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import { queryDemandList } from "@/api/demandPool/setPermission.js";
export default {
  components: {
    GridTable,
  },
  data() {
    return {
      loading: false,
      visible: false,
      userId: "",
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      tableTotal: 0,
      columns: [
        {
          field: "parentDemandName",
          title: "一级需求",
          customWidth: 200,
        },
        {
          field: "childDemandName",
          title: "二级需求",
          customWidth: 200,
        },
      ],
      tableId: "dataPermissionInnerTable", //tableId必须项目唯一，用于缓存展示列
    };
  },
  mounted() {
    // this.queryData();
  },
  methods: {
    openDialog(row) {
      this.visible = true;
      this.userId = row.userId;
      this.queryData();
    },
    closeDialog() {
      this.visible = false;
      this.resetQuery();
    },
    //获取项目列表
    queryData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        userId: this.userId,
      };
      // this.finallySearch = args;
      queryDemandList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.queryData(params);
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },
  },
};
</script>

<style></style>
