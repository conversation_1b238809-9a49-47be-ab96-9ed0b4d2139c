<template>
  <el-dialog
    title="配置数据权限"
    :visible.sync="addVisible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="70%"
  >
    <div class="queryParamsWrap">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="需求类型" name="demand">
          <TransferTree
            :cascadeData="cascadeData"
            v-model="checkedData"
            ref="transferTree"
            :titles="['未配置', '已配置']"
          ></TransferTree
        ></el-tab-pane>
        <el-tab-pane label="部门" name="dept">
          <TransferTree
            :cascadeData="deptList"
            v-model="deptChecked"
            ref="deptTree"
            :titles="['未配置', '已配置']"
          ></TransferTree>
        </el-tab-pane>
        <el-tab-pane label="角色" name="role">
          <el-transfer
            v-model="roleChecked"
            :data="roleList"
            filterable
            :props="{ key: 'roleId', label: 'roleName' }"
            :titles="['未配置', '已配置']"
          ></el-transfer>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取消</el-button>
      <el-button type="primary" @click.stop="handleSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TransferTree from "@/components/TransferTree/index2.vue";
import {
  queryDemandTree,
  queryConfigDemand,
  submitEdit,
  submitRole,
  submitDept,
  queryDeptTree,
  queryCheckedDept,
  queryRoleTree,
  queryCheckedRole,
} from "@/api/demandPool/setPermission.js";

export default {
  components: { TransferTree },
  data() {
    return {
      activeName: "demand",
      checkedData: [],
      addVisible: false,
      cascadeData: [],
      userId: "",
      roleChecked: [],
      roleList: [],
      deptChecked: [],
      deptList: [],
    };
  },
  async created() {
    await this.getTreeList();
  },
  methods: {
    //获取角色树及已选项
    async getRoleData() {
      const res = await queryRoleTree({ userId: this.userId });
      if (res) {
        this.roleList = res.data;
        queryCheckedRole({ userId: this.userId }).then((res) => {
          if (res?.code === "10000") {
            this.roleChecked = res.data?.map((x) => x.roleId);
          }
        });
      }
    },
    //获取部门树及已选项
    async getDeptData() {
      const res = await queryDeptTree({ userId: this.userId });
      if (res) {
        this.deptList = res.data;
        this.traverseDept(this.deptList);
        // console.log(this.deptList, "dept");
        queryCheckedDept({ userId: this.userId }).then((res) => {
          if (res?.code === "10000") {
            this.deptChecked = res.data;
            this.traverseDept(this.deptChecked);
            this.$nextTick(() => {
              this.$refs.deptTree.getDefaultLeftData();
            });
          }
        });
      }
    },
    //获取所有组织-用户树形结构
    async getTreeList() {
      const res = await queryDemandTree({ userId: this.userId });
      if (res) {
        // this.cascadeData = res.data;
        // this.traverseArr(this.cascadeData);
        this.cascadeData = res.data?.map((x) => {
          return {
            ...x,
            id: x.id,
            label: x.typeName,
            children: x.childrenList?.map((i) => {
              return {
                ...i,
                id: i.id,
                label: i.typeName,
                pid: i.parentId,
                plabel: i.parentName,
              };
            }),
          };
        });
      }
    },
    traverseDept(arr, pid) {
      arr.forEach((obj) => {
        // 添加id和label
        obj.id = obj.deptId;
        obj.label = obj.deptName;
        obj.pid = obj.parentId;
        if (pid) {
          obj.pid = pid;
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseDept(obj.children, obj.deptId);
        } else {
          delete obj.children;
        }
      });
    },
    traverseArr(arr) {
      arr.forEach((obj) => {
        obj.label = obj.typeName;
        obj.pid = obj.parentId;

        //没有子级的一级节点禁用勾选
        if (obj.typeLevel === 1) {
          obj["children"] = obj.childrenList || obj.childList || [];
          if (!obj.children) {
            obj["disabled"] = true;
          }
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children);
        }
      });
    },
    async openDialog(row) {
      this.addVisible = true;
      this.userId = row.userId;
      this.activeName = "demand";
      //获取已绑定的用户
      const res = await queryConfigDemand({ userId: row.userId });
      if (res) {
        this.checkedData = res.data?.map((x) => {
          return {
            ...x,
            id: x.id,
            label: x.typeName,
            children: x.childList?.map((i) => {
              return {
                ...i,
                id: i.id,
                label: i.typeName,
                pid: i.parentId,
                plabel: i.parentName,
              };
            }),
          };
        });
        // this.traverseArr(this.checkedData);
      }
      this.$nextTick(() => {
        this.$refs.transferTree.getDefaultLeftData();
      });
      this.getDeptData();
      this.getRoleData();
    },
    closeDialog() {
      this.addVisible = false;
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    //提交
    async handleSubmit() {
      let params = {};
      let res;
      if (this.activeName === "demand") {
        let arr = this.flattenArray(this.checkedData);
        let demandIds = [];
        arr.map((x) => {
          if (x.typeLevel === 2) {
            demandIds.push(x.id);
          }
        });
        params = { demandIds: demandIds };
        res = await submitEdit({ ...params, userId: this.userId });
      } else if (this.activeName === "dept") {
        params = { deptList: this.deptChecked };
        res = await submitDept({ ...params, userId: this.userId });
      } else {
        params = {
          roleList: this.roleList?.filter((x) =>
            this.roleChecked.includes(x.roleId)
          ),
        };
        res = await submitRole({ ...params, userId: this.userId });
      }
      if (res?.code == 10000) {
        this.$message.success("修改成功");
        if (this.activeName === "role") {
          this.closeDialog();
          this.$emit("submit");
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;
  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}
/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}
</style>
