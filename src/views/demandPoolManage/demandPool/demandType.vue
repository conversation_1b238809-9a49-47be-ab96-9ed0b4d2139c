// 需求类型
<template>
  <div class="app-container">
    <div class="page-header">
      <h3>需求类型</h3>
      <el-button
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click.stop="handleAdd(1)"
        v-has-permi="['demandPool:demandType:add']"
        >新增</el-button
      >
    </div>
    <el-card>
      <el-tree
        :data="data"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :props="defaultProps"
        :draggable="true"
        :allow-drop="allowDrop"
        @node-drop="handleDrop"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <div class="node-label">
            {{ node.label
            }}<el-tooltip
              effect="dark"
              content="该需求类型未绑定任何处理组！"
              placement="top"
              class="ml10"
              v-if="data.bindGroupTag == 0"
            >
              <i class="el-icon-warning" style="color: #ff6a00"></i>
            </el-tooltip>
          </div>
          <div v-if="data.typeLevel == 2" class="node-middle">
            <div>接收时效：{{ showNum(data.acceptValidTime) }}h</div>
            <div>处理时效：{{ showNum(data.handleValidTime) }}h</div>
            <div>提交运管时效：{{ showNum(data.submitValidTime) }}h</div>
            <div>关联表单名称：{{ showNum(data.defName) }}</div>
          </div>
          <div class="node-btn">
            <el-button
              type="text"
              size="mini"
              @click="handleAdd(data.typeLevel + 1, data)"
              v-if="data.typeLevel <= 1"
              icon="el-icon-plus"
              v-has-permi="['demandPool:demandType:add']"
            >
              新增
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(data.typeLevel, data)"
              icon="el-icon-edit"
              v-has-permi="['demandPool:demandType:edit']"
            >
              修改
            </el-button>
            <el-button
              type="text"
              size="mini"
              icon="el-icon-connection"
              @click="handleBind(data)"
              v-if="data.typeLevel > 1"
              v-has-permi="['demandPool:demandType:bind']"
            >
              关联分组
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleDelete(data.id)"
              icon="el-icon-minus"
              style="color: red"
              v-has-permi="['demandPool:demandType:delete']"
            >
              删除
            </el-button>
          </div>
        </div>
      </el-tree>
    </el-card>
    <el-dialog
      :title="title"
      :visible.sync="modalVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      v-if="modalVisible"
    >
      <el-form
        :model="modalForm"
        ref="modalForm"
        label-width="180px"
        :rules="rules"
      >
        <el-form-item label="一级类型：" prop="firstType">
          <span v-if="addLevel == 2">{{ modalForm.firstType }}</span>
          <el-input
            v-model="modalForm.firstType"
            placeholder="请输入一级需求类型，长度100个字符以内"
            maxlength="100"
            clearable
            v-else
          />
        </el-form-item>
        <el-form-item label="二级类型：" prop="secondType" v-if="addLevel == 2">
          <el-input
            v-model="modalForm.secondType"
            placeholder="请输入二级需求类型，长度100个字符以内"
            maxlength="100"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="业务类型："
          prop="twoBusinessTypeList"
          v-if="addLevel == 2"
        >
          <CustomCascader
            v-model="modalForm.twoBusinessTypeList"
            placeholder="请选择二级业务类型"
            :options="businessTypeOptions"
            collapse-tags
            filterable
            clearable
            style="width: 100%"
            :props="{
              multiple: true,
              value: 'id',
              label: 'typeName',
              children: 'childrenList',
              emitPath: false,
            }"
          >
            <template slot-scope="{ node, data }">
              <el-tooltip
                :disabled="data.typeName.length < 11"
                class="item"
                effect="dark"
                :content="data.typeName"
                placement="top-start"
              >
                <div class="cascader-node">{{ data.typeName }}</div>
              </el-tooltip>
            </template>
          </CustomCascader>
        </el-form-item>
        <el-form-item
          label="受理时效："
          prop="acceptValidTime"
          v-if="addLevel == 2"
        >
          <el-input
            v-model="modalForm.acceptValidTime"
            placeholder="请输入受理时效"
            type="number"
            clearable
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="受理预警提醒时效："
          prop="acceptRemindValidTime"
          v-if="addLevel == 2"
        >
          <el-input
            v-model="modalForm.acceptRemindValidTime"
            placeholder="请输入受理超时时效"
            type="number"
            clearable
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="处理时效："
          prop="handleValidTime"
          v-if="addLevel == 2"
        >
          <el-input
            v-model="modalForm.handleValidTime"
            placeholder="请输入处理时效"
            type="number"
            clearable
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="处理预警提醒时效："
          prop="handleRemindValidTime"
          v-if="addLevel == 2"
        >
          <el-input
            v-model="modalForm.handleRemindValidTime"
            placeholder="请输入处理超时时效"
            type="number"
            clearable
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="提交运管时效："
          prop="submitValidTime"
          v-if="addLevel == 2"
        >
          <el-input
            v-model="modalForm.submitValidTime"
            placeholder="请输入提交运管时效"
            type="number"
            clearable
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="提交运管预警提醒时效："
          prop="submitRemindValidTime"
          v-if="addLevel == 2"
        >
          <el-input
            v-model="modalForm.submitRemindValidTime"
            placeholder="请输入提交运管超时时效"
            type="number"
            clearable
          >
            <template slot="append">h</template>
          </el-input>
        </el-form-item>
        <el-form-item label="关联表单名称：" prop="defId" v-if="addLevel == 2">
          <el-select
            v-model="modalForm.defId"
            placeholder="请选择关联表单名称"
            clearable
            style="width: 100%;"
            filterable
          >
            <el-option
              v-for="item in relatedFormOptions"
              :key="item.defId"
              :label="item.formName"
              :value="item.defId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="handleSubmit"
          :loading="submitLoading"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="绑定分组"
      :visible.sync="bindVisible"
      @close="bindVisible = false"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="bindForm" ref="bindForm" label-width="110px">
        <el-form-item label="分组名称：" prop="groupIds">
          <el-select
            v-model="bindForm.groupIds"
            placeholder="请选择分组"
            clearable
            style="width: 100%;"
            filterable
            multiple
          >
            <el-option
              v-for="item in groupOptions"
              :key="item.id"
              :label="item.groupName"
              :value="item.id"
              :disabled="item.enableStatus == 0"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="bindVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click.stop="submitBind"
          :loading="bindLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as api from "@/api/demandPool/demandType.js";
import { list } from "@/api/demandPool/formDesign.js";
import { queryGroupList } from "@/api/demandPool/setGroup.js";
export default {
  components: {},
  data() {
    return {
      bindLoading: false,
      bindVisible: false,
      groupOptions: [],
      bindForm: { groupIds: [] },
      businessTypeOptions: [],
      modalVisible: false,
      title: "新增需求类型",
      submitLoading: false,
      data: [],
      modalForm: {
        firstType: "",
        secondType: "",
        acceptValidTime: "",
        handleValidTime: "",
        submitValidTime: "",
        defId: undefined,
        twoBusinessTypeList: [],
        acceptRemindValidTime: "",
        handleRemindValidTime: "",
        submitRemindValidTime: "",
      },
      rules: {
        submitRemindValidTime: [
          { required: true, message: "请输入提交运管超时时效" },
          {
            pattern: /^(?:\d{1,4}(?:\.\d{1,2})?|10000)$/,
            trigger: "change",
            message: "请输入0-10000之间的数字，保留两位小数",
          },
        ],
        handleRemindValidTime: [
          { required: true, message: "请输入处理超时时效" },
          {
            pattern: /^(?:\d{1,4}(?:\.\d{1,2})?|10000)$/,
            trigger: "change",
            message: "请输入0-10000之间的数字，保留两位小数",
          },
        ],
        acceptRemindValidTime: [
          { required: true, message: "请输入受理超时时效" },
          {
            pattern: /^(?:\d{1,4}(?:\.\d{1,2})?|10000)$/,
            trigger: "change",
            message: "请输入0-10000之间的数字，保留两位小数",
          },
        ],
        twoBusinessTypeList: [
          { required: true, message: "请选择二级业务类型" },
        ],
        firstType: [{ required: true, message: "需求类型不能为空" }],
        secondType: [{ required: true, message: "需求类型不能为空" }],
        acceptValidTime: [
          { required: true, message: "请输入受理时效" },
          {
            pattern: /^(?:\d{1,4}(?:\.\d{1,2})?|10000)$/,
            trigger: "change",
            message: "请输入0-10000之间的数字，保留两位小数",
          },
        ],
        handleValidTime: [
          { required: true, message: "请输入处理时效" },
          {
            pattern: /^(?:\d{1,4}(?:\.\d{1,2})?|10000)$/,
            trigger: "change",
            message: "请输入0-10000之间的数字，保留两位小数",
          },
        ],
        submitValidTime: [
          { required: true, message: "请输入提交运管时效" },
          {
            pattern: /^(?:\d{1,4}(?:\.\d{1,2})?|10000)$/,
            trigger: "change",
            message: "请输入0-10000之间的数字，保留两位小数",
          },
        ],
      },
      addLevel: 1,
      defaultProps: {
        children: "childrenList",
        label: "typeName",
      },
      flattenData: [],
      modalType: "add",
      parentId: undefined,
      editId: undefined,
      relatedFormOptions: [],
    };
  },
  mounted() {
    this.getFormOptions();
    this.getTreeData();
    this.getBusinessTypeOptions();
    this.queryGroupOptions();
  },
  methods: {
    submitBind() {
      this.bindLoading = true;
      api
        .bindGroups(this.bindForm)
        .then((res) => {
          this.bindLoading = false;
          if (res?.code === "10000") {
            this.$message.success("提交成功");
            this.bindVisible = false;
            this.getTreeData();
          }
        })
        .catch(() => {
          this.bindLoading = false;
        });
    },
    queryGroupOptions() {
      queryGroupList({ pageNum: 1, pageSize: 99999 }).then((res) => {
        if (res?.code === "10000") {
          this.groupOptions = res.data;
        }
      });
    },
    getBusinessTypeOptions() {
      api.queryBusinessList().then((res) => {
        if (res.code === "10000") {
          this.businessTypeOptions = res.data?.map((x) => {
            return { childrenList: [], ...x };
          });
        }
      });
    },
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.level === dropNode.level) {
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === "prev" || type === "next";
        }
      } else {
        return false;
      }
    },
    handleDrop(draggingNode, dropNode) {
      // console.log(draggingNode, dropNode);
      let list = [];
      // 获取子级id
      for (let item of dropNode.parent.childNodes) {
        list.push(item.data.id);
      }
      // console.log(list, this.data, "========");
      api.sortTree({ ids: list }).then((res) => {
        if (res.code === "10000") {
          this.$message.success("排序成功");
          this.getTreeData();
        }
      });
    },
    showNum(data) {
      return data ?? " - ";
    },
    getFormOptions() {
      list({ formStatus: 1, pageNum: 1, pageSize: 999 }).then((res) => {
        if (res.code === "10000") {
          this.relatedFormOptions = res.data;
        }
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getTreeData() {
      api.queryTreeList({}).then((res) => {
        this.data = JSON.parse(JSON.stringify(res.data));
        this.flattenData = this.flattenArray(res.data);
        console.log("-----", this.flattenData);
      });
    },
    handleAdd(level, data) {
      console.log(data);
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "新增需求类型";
      this.modalType = "add";
      if (level == 1) {
        this.modalForm = {
          firstType: "",
        };
        this.parentId = undefined;
      } else if (level == 2) {
        this.modalForm = {
          firstType: data.typeName,
          secondType: "",
        };
        this.parentId = data.id;
      }
    },
    handleBind(data) {
      this.bindForm = { groupIds: data.groupIds, id: data.id };
      this.bindVisible = true;
    },
    handleEdit(level, data) {
      this.addLevel = level;
      this.modalVisible = true;
      this.title = "修改需求类型";
      this.modalType = "edit";
      this.editId = data.id;
      if (level == 1) {
        this.modalForm = { ...data, firstType: data.typeName };
      } else if (level == 2) {
        this.modalForm = {
          ...data,
          firstType: data.parentName,
          secondType: data.typeName,
        };
      }
    },
    //提交
    handleSubmit() {
      const arr = [
        { level: 1, value: "firstType" },
        { level: 2, value: "secondType" },
        // { level: 3, value: "thirdType" },
      ];
      const method = this.modalType === "add" ? "addType" : "editType";
      const value = arr.find((x) => x.level === this.addLevel)?.value;

      this.$refs.modalForm.validate((valid) => {
        if (valid) {
          let params =
            this.modalType === "add"
              ? {
                  ...this.modalForm,
                  typeName: this.modalForm[value],
                  typeLevel: this.addLevel,
                  parentId: this.parentId,
                }
              : {
                  ...this.modalForm,
                  typeName: this.modalForm[value],
                  id: this.editId,
                };
          this.submitLoading = true;
          api[method](params).then((res) => {
            if (res?.success) {
              this.$message.success("提交成功");
              this.getTreeData();
              this.closeDialog();
              this.submitLoading = false;
            }
          });
        } else {
          console.log("校验失败");
        }
      });
    },
    closeDialog() {
      console.log("关闭弹窗");
      this.$refs.modalForm.resetFields();
      this.modalVisible = false;
    },
    handleDelete(id) {
      this.$confirm("", "确定要删除该需求类型吗?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true,
      }).then(() => {
        let data = {
          id: id,
        };
        api.deleteType(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getTreeData();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  align-items: center;
  h3 {
    margin-right: 40px;
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .node-label {
    width: 200px;
    word-wrap: break-word;
    white-space: normal;
  }
  .node-btn {
    min-width: 170px;
    display: flex;
    justify-content: flex-end;
  }
  .node-middle {
    display: flex;
    // justify-content: space-around;
    flex-wrap: wrap;
    flex: 1;
    font-size: 12px;
    color: #5a5959;
    div {
      margin-right: 30px;
      padding: 10px;
    }
  }
}
/deep/ .el-tree-node__content {
  padding-top: 10px;
  padding-bottom: 10px;
  height: auto;
}
/deep/ .el-card__body {
  max-height: 80vh;
  overflow-y: auto;
}
</style>
