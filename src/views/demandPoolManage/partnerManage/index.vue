<template>
  <div class="partner-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    />
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="goToAdd"
            v-has-permi="['demandPool:partner:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch
            :value="!row.status"
            @change="(val) => handleStatusChange(val, row)"
            :disabled="!checkPermission(['demandPool:partner:status'])"
          >
          </el-switch>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            size="large"
            type="text"
            @click.stop="editHandler(row)"
            v-has-permi="['demandPool:partner:edit']"
            >编辑</el-button
          >
          <el-button
            size="large"
            type="text"
            @click.stop="goDetail(row)"
            v-has-permi="['demandPool:partner:detail']"
            >详情</el-button
          >
          <el-button
            size="large"
            type="text"
            @click.stop="deleteHandler(row)"
            v-has-permi="['demandPool:partner:delete']"
            >删除</el-button
          >
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import Api from "@/api/demandPool/partnerManager.js";
export default {
  name: "PartnerManager",
  components: {
    AdvancedForm,
    GridTable,
  },

  data() {
    return {
      tableId: "partnerManageList",
      loading: false,
      total: 0,
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        partnerName: "",
        contact: "",
        status: "",
      },
    };
  },
  computed: {
    config() {
      return [
        {
          key: "partnerName",
          title: "合作商名称",
          type: "input",
          placeholder: "请填写合作商名称",
        },
        {
          key: "contact",
          title: "联系人",
          type: "input",
          placeholder: "请填写联系人",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "请选择状态",
          options: [
            {
              dictLabel: "启用",
              dictValue: 0,
            },
            {
              dictLabel: "禁用",
              dictValue: 1,
            },
          ],
        },
      ];
    },
    columns() {
      return [
        {
          field: "partnerName",
          title: "合作商名称",
          customWidth: 200,
        },
        {
          field: "contact",
          title: "联系人",
        },
        {
          field: "contactTel",
          title: "联系方式",
        },
        {
          field: "socialCreditCode",
          title: "统一社会信用代码",
          minWidth: 120,
        },
        {
          field: "priceType",
          title: "电费计价方式",
        },
        {
          field: "mailAddress",
          title: "发票邮寄地址",
        },
        {
          field: "status",
          title: "状态",
          slots: { default: "status" },
        },

        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
  },

  mounted() {
    this.getPartnerList(this.searchForm);
  },

  methods: {
    checkPermission,
    async getPartnerList(params) {
      this.loading = true;
      const res = await Api.queryPartnerList(params);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.total = res.total;
      }
    },
    changePage() {
      this.getPartnerList(this.searchForm);
    },
    goToAdd() {
      this.$router.push({
        name: "partnerManageAdd",
      });
    },
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.getPartnerList(params);
    },
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getPartnerList(this.searchForm);
    },
    async handleStatusChange(val, { partnerId }) {
      this.loading = true;
      const res = await Api.changeStatus({
        partnerId,
        status: val ? 0 : 1,
      });
      this.loading = false;
      if (res.code === "10000") {
        this.getPartnerList(this.searchForm);
      } else {
        this.$message.error(res.message);
      }
    },
    editHandler(row) {
      this.$router.push({
        name: "partnerManageEdit",
        query: {
          partnerId: row.partnerId,
        },
      });
    },
    goDetail(row) {
      this.$router.push({
        name: "partnerManageDetail",
        query: {
          partnerId: row.partnerId,
        },
      });
    },
    deleteHandler(row) {
      if (row.status == 0) {
        this.$message.warning("启用状态下无法删除，请先停用该合作商！");
        return;
      }
      this.$confirm("确定删除该合作商吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await Api.removePartner({ partnerId: row.partnerId });
        if (res.code === "10000") {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getPartnerList(this.searchForm);
        } else {
          this.$message({
            type: "error",
            message: res.message || "删除失败",
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
