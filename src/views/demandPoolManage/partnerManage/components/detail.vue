<template>
  <el-card>
    <el-form ref="form" label-width="140px" class="partner-add-form">
      <CommonTitle class="mb10" title="基本信息" />
      <el-form-item label="合作商名称：" prop="partnerName">
        {{ form.partnerName }}
      </el-form-item>
      <el-form-item label="公司地址：" prop="address">
        {{ form.address }}
      </el-form-item>
      <el-form-item label="详细地址：" prop="detailAddress">
        {{ form.detailAddress }}
      </el-form-item>
      <el-form-item label="联系人：" prop="contact">
        {{ form.contact }}
      </el-form-item>
      <el-form-item label="联系人电话：" prop="contactTel">
        {{ form.contactTel }}
      </el-form-item>
      <el-form-item label="备注：">
        {{ form.remark }}
      </el-form-item>
      <CommonTitle class="mb10" title="营业执照" />
      <el-form-item label="企业名称：">
        {{ form.enterpriseName }}
      </el-form-item>
      <el-form-item label="统一社会信用代码：">
        {{ form.socialCreditCode }}
      </el-form-item>
      <el-form-item label="企业法人：">
        {{ form.legalPerson }}
      </el-form-item>
      <el-form-item label="营业执照："
        ><FileUploadPreview type="img" ref="FileUpload" isPreview />
      </el-form-item>
      <CommonTitle class="mb10" title="开票信息" />
      <el-form-item label="开户行：">
        {{ form.bankName }}
      </el-form-item>
      <el-form-item label="银行机构代码：">
        {{ form.bankCode }}
      </el-form-item>
      <el-form-item label="账号：">
        {{ form.bankAccount }}
      </el-form-item>
      <el-form-item label="发票寄送地址：">
        {{ form.mailAddress }}
      </el-form-item>
      <el-form-item label="传真：">
        {{ form.fax }}
      </el-form-item>
      <el-form-item label="电话：" prop="phoneNumber">
        {{ form.phoneNumber }}
      </el-form-item>
      <el-form-item label="开户行许可证或盖章版开票信息："
        ><FileUploadPreview type="img" ref="ticketUpload" isPreview />
      </el-form-item>
      <CommonTitle class="mb10" title="其他信息" />
      <el-form-item label="电费计价方式：">
        {{ form.priceType }}
      </el-form-item>
      <CommonTitle class="mb10" title="附件上传" />
      <MultiFileUpload ref="attachments" isPreview />
    </el-form>
  </el-card>
</template>

<script>
import Api from "@/api/demandPool/partnerManager.js";
import CommonTitle from "@/components/commonTitle";
import MultiFileUpload from "@/components/MultipleFileUpload";

import FileUploadPreview from "@/components/Upload/ImgUploadPreview.vue";

export default {
  name: "ChargingMaintenanceUiDetail",
  components: {
    CommonTitle,
    MultiFileUpload,
    FileUploadPreview,
  },

  data() {
    return {
      form: {},
    };
  },

  mounted() {
    this.partnerId = this.$route.query.partnerId;
    if (this.partnerId) {
      this.getPartnerDetail();
    } else {
      this.$$message.error("缺少合作商id");
    }
  },

  methods: {
    async getPartnerDetail() {
      const res = await Api.getPartnerDetail({ partnerId: this.partnerId });
      if (res.code === "10000") {
        this.form = res.data;
        const {
          province,
          city,
          county,
          imgList,
          docList,
          licenseList,
        } = res.data;
        this.form.imgList = imgList;
        this.$refs.attachments.setAttachments(docList);
        this.$refs.FileUpload.setFileList(imgList);
        this.$refs.ticketUpload.setFileList(licenseList);
        this.form.companyAddress = [province, city, county];
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
