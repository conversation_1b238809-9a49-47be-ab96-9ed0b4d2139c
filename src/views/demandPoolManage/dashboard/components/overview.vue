<template>
  <div>
    <div class="form-content">
      <!-- 筛选项-start -->
      <el-form
        :model="form"
        ref="form"
        label-width="100px"
        style="padding:0 12px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务类型">
              <el-select
                v-model="form.oneBusinessTypes"
                placeholder="一级业务类型"
                clearable
                multiple
                style="width: 49%;"
                collapse-tags
                @change="handleFirstBusinessChange"
              >
                <el-option
                  v-for="item in businessFirstLevelList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-select
                v-model="form.twoBusinessTypes"
                placeholder="二级业务类型"
                clearable
                multiple
                collapse-tags
                style="width: 49%;"
              >
                <el-option
                  v-for="item in businessChildrenList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求类型">
              <el-select
                v-model="form.oneDemandTypes"
                placeholder="一级需求类型"
                clearable
                style="width: 49%;"
                multiple
                collapse-tags
                @change="handleFirstDemandChange"
              >
                <el-option
                  v-for="item in demandFirstLevelList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-select
                v-model="form.twoDemandTypes"
                placeholder="二级需求类型"
                clearable
                collapse-tags
                multiple
                style="width: 49%;"
              >
                <el-option
                  v-for="item in demandChildrenList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="22">
            <el-form-item label="时间" prop="timeRange">
              <el-date-picker
                v-model="form.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
                value-format="yyyy-MM-dd"
                @change="queryData"
              ></el-date-picker>
              <el-radio-group v-model="form.timeRange" @change="queryData">
                <el-radio-button
                  v-for="(x, i) in timeArr"
                  :label="x.date"
                  :key="i"
                  >{{ x.title }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button
              type="primary"
              @click="queryData"
              icon="el-icon-search"
              style="float: right;margin-right:4px"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <!-- 筛选项-end -->
    </div>
    <!-- 数据概览-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading1">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据概览</span>
      </div>
      <div class="statistics-box">
        <div
          v-for="(item, index) in statisticsList"
          :key="index"
          class="statistics-item"
        >
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
              <el-tooltip effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <!-- 数据概览-end -->
    <!-- 需求单完成趋势-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading2">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>需求单完成趋势</span>
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <p>
              每日完成率：（当日提交给运管的申请单/当日确认通过的申请单）*100%
            </p>
            <p>每日完成：当日提交给运管的申请单</p>
            <p>每日新增：当日新增的需求单数量</p>
          </div>
          <i class="el-icon-question ml5"></i>
        </el-tooltip>
      </div>
      <LineChart
        :axisData="handleTend.time"
        :serieData="handleTend.tendencyArr"
        lineType="line"
        v-if="handleTend.time && handleTend.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 需求单完成趋势-end -->
    <!-- 需求类型-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading3">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>需求类型</span>
      </div>
      <LineChart
        :axisData="orderTypeObj.time"
        :serieData="orderTypeObj.tendencyArr"
        lineType="bar"
        v-if="orderTypeObj.time && orderTypeObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 需求类型-end -->
    <div style="display: flex;">
      <!-- 受理超时情况-start -->
      <el-card
        style="margin-bottom: 10px;flex:1;margin-right: 10px;"
        v-loading="loading4"
      >
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>受理超时情况</span>
        </div>
        <PieChartSolid
          :list="acceptExceedPieList"
          v-if="acceptExceedPieList && acceptExceedPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 受理超时情况-end -->
      <!-- 处理超时情况-start -->
      <el-card
        style="margin-bottom: 10px;flex:1;margin-right: 10px;"
        v-loading="loading5"
      >
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>处理超时情况</span>
        </div>
        <PieChartSolid
          :list="handleExceedPieList"
          :pieRadius="['47%', '70%']"
          v-if="handleExceedPieList && handleExceedPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 处理超时情况-end -->
      <!-- 提交运管超时情况-start -->
      <el-card style="margin-bottom: 10px;flex:1;" v-loading="loading6">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>提交运管超时情况</span>
        </div>
        <PieChartSolid
          :list="submitExceedPieList"
          :pieRadius="['47%', '70%']"
          v-if="submitExceedPieList && submitExceedPieList.length > 0"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 提交运管超时情况-end -->
    </div>
    <!-- 处理人需求接受数排名-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading7">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理人需求接受数排名</span>
      </div>
      <LineChart
        :axisData="acceptNumRateObj.time"
        :serieData="acceptNumRateObj.tendencyArr"
        lineType="bar"
        v-if="acceptNumRateObj.time && acceptNumRateObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 处理人需求接受数排名-end -->
    <!-- 处理人及时率排名-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading8">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理人及时率排名</span>
      </div>
      <LineChart
        :axisData="timeRateObj.time"
        :serieData="timeRateObj.tendencyArr"
        lineType="bar"
        v-if="timeRateObj.time && timeRateObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 处理人及时率排名-end -->
    <!-- 处理人完成率排名-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading9">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理人完成率排名</span>
      </div>
      <LineChart
        :axisData="completeRateObj.time"
        :serieData="completeRateObj.tendencyArr"
        lineType="bar"
        v-if="completeRateObj.time && completeRateObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 处理人完成率排名-end -->
    <!-- 处理人能效排名-start -->
    <el-card style="margin-bottom: 10px;" v-loading="loading10">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>处理人能效排名</span>
      </div>
      <LineChart
        :axisData="efficientRateObj.time"
        :serieData="efficientRateObj.tendencyArr"
        lineType="bar"
        v-if="efficientRateObj.time && efficientRateObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 处理人能效排名-end -->
  </div>
</template>

<script>
import LineChart from "@/components/Echarts/LineChart.vue";
import moment from "moment";
import PieChartSolid from "@/components/Echarts/pieChartSolid.vue";
import {
  queryOrderData,
  queryHandleTend,
  queryOrderType,
  queryAcceptTimeout,
  queryHandleTimeout,
  querySubmitTimeout,
  queryAcceptNumRate,
  queryTimeRate,
  queryCompleteRate,
  queryEfficientRate,
  queryBusinessFirstLevel,
  queryBusinessChildrenList,
  queryDemandFirstLevel,
  queryDemandChildrenList,
} from "@/api/demandPool/dashboard.js";
export default {
  components: { LineChart, PieChartSolid },
  data() {
    return {
      businessFirstLevelList: [],
      businessChildrenList: [],
      demandFirstLevelList: [],
      demandChildrenList: [],
      form: {
        oneBusinessTypes: [],
        twoBusinessTypes: [],
        oneDemandTypes: [],
        twoDemandTypes: [],
        timeRange: [],
      },
      loading1: false,
      loading2: false,
      loading3: false,
      loading4: false,
      loading5: false,
      loading6: false,
      loading7: false,
      loading8: false,
      loading9: false,
      loading10: false,
      handleTend: { time: [], tendencyArr: [] },
      statisticsList: [
        {
          title: "累计需求单",
          unit: "个",
          value: 0,
          tooltip: "统计除了草稿状态的所有需求单总数",
        },
        {
          title: "待接收",
          unit: "个",
          value: 0,
          tooltip: "待接收状态的需求单总数",
        },
        {
          title: "已撤销",
          unit: "个",
          value: 0,
          tooltip: "已撤销状态的需求单总数",
        },
        {
          title: "已接收",
          unit: "个",
          value: 0,
          tooltip: "已接收状态的需求单总数",
        },
        {
          title: "已驳回",
          unit: "个",
          value: 0,
          tooltip: "已驳回状态的需求单总数",
        },
        {
          title: "处理中",
          unit: "个",
          value: 0,
          tooltip: "处理中状态的需求单总数",
        },
        {
          title: "已完结",
          unit: "个",
          value: 0,
          tooltip: "已完结状态的需求单总数",
        },
        {
          title: "已归档",
          unit: "个",
          value: 0,
          tooltip: "已归档状态的需求单总数",
        },
        {
          title: "平均完成率",
          unit: "%",
          value: 0,
          tooltip:
            "已提交运管的需求单数/确认通过的需求单数*100%，小数点后保留一位，四舍五入，单位为【%】",
        },
        {
          title: "平均及时率",
          unit: "%",
          value: 0,
          tooltip: `需求单的接收、确认、提交运管这3个节点如果超时，则算超时次数，
                  单个需求单的及时率=1-节点超时次数/3*100%，
                  多个需求单的及时率=1-所有需求单节点超时次数总和/（3*需求单总数）*100%，
                  及时率小数点后保留一位，四舍五入，单位为【%】`,
        },
        {
          title: "能效",
          unit: "单",
          value: 0,
          tooltip:
            "确认的需求单（处理中+已驳回）总数/筛选天数，按照自然月进行天数计算。能效结果取整数，四舍五入，单位为【单】",
        },
        {
          title: "平均完结率",
          unit: "%",
          value: 0,
          tooltip:
            "已完结+已归档状态的需求单数/处理中+已完结+已归档状态的需求单总数*100%",
        },
      ],
      orderTypeObj: { time: [], tendencyArr: [] },
      acceptExceedPieList: [],
      handleExceedPieList: [],
      submitExceedPieList: [],
      acceptNumRateObj: { time: [], tendencyArr: [] },
      timeRateObj: { time: [], tendencyArr: [] },
      completeRateObj: { time: [], tendencyArr: [] },
      efficientRateObj: { time: [], tendencyArr: [] },
    };
  },
  computed: {
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD 00:00:00"),
            moment().format("YYYY-MM-DD 23:59:59"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
  },
  async created() {
    await this.queryBusinessType();
    await this.queryDemandType();
    this.form.timeRange = [
      moment()
        .subtract(6, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
    this.queryData();
  },
  methods: {
    async queryBusinessType() {
      const res = await queryBusinessFirstLevel();
      if (res?.code === "10000") {
        this.businessFirstLevelList = res.data;
      }
    },
    async queryDemandType() {
      const res = await queryDemandFirstLevel();
      if (res?.code === "10000") {
        this.demandFirstLevelList = res.data;
      }
    },
    async handleFirstDemandChange(val) {
      console.log(val, "---val");
      if (!val || val?.length === 0) {
        this.demandChildrenList = [];
        this.form.twoDemandTypes = [];
      } else {
        const res = await queryDemandChildrenList({ ids: val });
        if (res?.code === "10000") {
          this.demandChildrenList = res.data;
          const arr = this.demandChildrenList?.map((x) => x.id);
          this.form.twoDemandTypes = this.form.twoDemandTypes?.filter(
            (item) => arr.indexOf(item) > -1
          );
        }
      }
    },
    async handleFirstBusinessChange(val) {
      if (!val || val?.length === 0) {
        this.businessChildrenList = [];
        this.form.twoBusinessTypes = [];
      } else {
        const res = await queryBusinessChildrenList({ ids: val });
        if (res?.code === "10000") {
          this.businessChildrenList = res.data;
          const arr = this.businessChildrenList?.map((x) => x.id);
          this.form.twoBusinessTypes = this.form.twoBusinessTypes?.filter(
            (item) => arr.indexOf(item) > -1
          );
        }
      }
    },
    queryData() {
      let params = { ...this.form };
      if (this.form.timeRange?.length > 0) {
        params["startTime"] = moment(this.form.timeRange[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        params["endTime"] = moment(this.form.timeRange[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }
      this.loading1 = true;
      //数据概览
      queryOrderData(params)
        .then((res1) => {
          this.loading1 = false;
          if (res1?.code == "10000") {
            this.statisticsList = [
              {
                title: "累计需求单",
                unit: "个",
                value: res1.data?.totalCount ?? 0,
                tooltip: "统计除了草稿状态的所有需求单总数",
              },
              {
                title: "待接收",
                unit: "个",
                value: res1.data?.notReceiveCount ?? 0,
                tooltip: "待接收状态的需求单总数",
              },
              {
                title: "已撤销",
                unit: "个",
                value: res1.data?.revokedCount ?? 0,
                tooltip: "已撤销状态的需求单总数",
              },
              {
                title: "已接收",
                unit: "个",
                value: res1.data?.receivedCount ?? 0,
                tooltip: "已接收状态的需求单总数",
              },
              {
                title: "已驳回",
                unit: "个",
                value: res1.data?.rejectedCount ?? 0,
                tooltip: "已驳回状态的需求单总数",
              },
              {
                title: "处理中",
                unit: "个",
                value: res1.data?.handlingCount ?? 0,
                tooltip: "处理中状态的需求单总数",
              },
              {
                title: "已完结",
                unit: "个",
                value: res1.data?.finishedCount ?? 0,
                tooltip: "已完结状态的需求单总数",
              },
              {
                title: "已归档",
                unit: "个",
                value: res1.data?.filedCount ?? 0,
                tooltip: "已归档状态的需求单总数",
              },
              {
                title: "平均完成率",
                unit: "%",
                value: res1.data?.completePercent ?? 0,
                tooltip:
                  "已提交运管的需求单数/确认通过的需求单数*100%，小数点后保留一位，四舍五入，单位为【%】",
              },
              {
                title: "平均及时率",
                unit: "%",
                value: res1.data?.avgTimelyPercent ?? 0,
                tooltip: `需求单的接收、确认、提交运管这3个节点如果超时，则算超时次数，
                  单个需求单的及时率=1-节点超时次数/3*100%，
                  多个需求单的及时率=1-所有需求单节点超时次数总和/（3*需求单总数）*100%，
                  及时率小数点后保留一位，四舍五入，单位为【%】`,
              },
              {
                title: "能效",
                unit: "单",
                value: res1.data?.energyEff ?? 0,
                tooltip:
                  "确认的需求单（处理中+已驳回）总数/筛选天数，按照自然月进行天数计算。能效结果取整数，四舍五入，单位为【单】",
              },
              {
                title: "平均完结率",
                unit: "%",
                value: res1.data?.finishPercent ?? 0,
                tooltip:
                  "已完结+已归档状态的需求单数/处理中+已完结+已归档状态的需求单总数*100%",
              },
            ];
          }
        })
        .catch(() => {
          this.loading1 = false;
        });

      //需求单完成趋势
      queryHandleTend(params)
        .then((res2) => {
          this.loading2 = false;
          if (res2?.code !== "10000") return;
          this.handleTend = {
            time: res2.data?.map((item) => item.time),
            tendencyArr: [
              {
                name: "每日新增(个)",
                data: res2.data?.map((item) => item.createCount),
              },
              {
                name: "每日完成(个)",
                data: res2.data?.map((item) => item.completeCount),
              },
              {
                name: "每日完成率(%)",
                data: res2.data?.map((item) => item.completePercent),
              },
            ],
          };
        })
        .catch(() => {
          this.loading2 = false;
        });
      //需求类型
      queryOrderType(params)
        .then((res3) => {
          this.loading3 = false;
          if (res3?.code !== "10000") return;
          this.orderTypeObj = {
            time: res3.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "需求单数",
                data: res3.data?.map((x) => x.count),
              },
            ],
          };
        })
        .catch(() => {
          this.loading3 = false;
        });
      //受理超时情况
      queryAcceptTimeout(params)
        .then((res4) => {
          this.loading4 = false;
          if (res4?.code !== "10000") return;
          this.acceptExceedPieList = res4.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading4 = false;
        });
      //处理超时情况
      queryHandleTimeout(params)
        .then((res5) => {
          this.loading5 = false;
          if (res5?.code !== "10000") return;
          this.handleExceedPieList = res5.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading5 = false;
        });
      //提交超时情况
      querySubmitTimeout(params)
        .then((res6) => {
          this.loading6 = false;
          if (res6?.code !== "10000") return;
          this.submitExceedPieList = res6.data?.map((x) => {
            return { value: x.count, name: x.name };
          });
        })
        .catch(() => {
          this.loading6 = false;
        });
      //处理人需求接受数排名
      queryAcceptNumRate(params)
        .then((res7) => {
          this.loading7 = false;
          if (res7?.code !== "10000") return;
          this.acceptNumRateObj = {
            time: res7.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "接受数（单）",
                data: res7.data?.map((x) => x.count),
              },
            ],
          };
        })
        .catch(() => {
          this.loading7 = false;
        });
      //处理人及时率排名
      queryTimeRate(params)
        .then((res8) => {
          this.loading8 = false;
          if (res8?.code !== "10000") return;
          this.timeRateObj = {
            time: res8.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "及时率（%）",
                data: res8.data?.map((x) => x.percent),
              },
            ],
          };
        })
        .catch(() => {
          this.loading8 = false;
        });
      //处理人完成率排名
      queryCompleteRate(params)
        .then((res9) => {
          this.loading9 = false;
          if (res9?.code !== "10000") return;
          this.completeRateObj = {
            time: res9.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "完成率（%）",
                data: res9.data?.map((x) => x.percent),
              },
            ],
          };
        })
        .catch(() => {
          this.loading9 = false;
        });
      //处理人能效排名
      queryEfficientRate(params)
        .then((res10) => {
          this.loading10 = false;
          if (res10?.code !== "10000") return;
          this.efficientRateObj = {
            time: res10.data?.map((x) => x.name),
            tendencyArr: [
              {
                name: "能效（单）",
                data: res10.data?.map((x) => x.count),
              },
            ],
          };
        })
        .catch(() => {
          this.loading10 = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto;
  grid-row-gap: 32px;
  grid-column-gap: 32px;
  .statistics-item {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
/deep/ .el-statistic {
  margin: 20px 0;
  display: flex;
  flex-direction: column-reverse;
  .head {
    color: #469a7d;
    font-size: 14px;
    margin-bottom: 0;
  }
  .con {
    color: #469a7d;
    margin-bottom: 12px;
    .number {
      font-size: 24px;
      font-weight: 500;
    }
  }
}
</style>
