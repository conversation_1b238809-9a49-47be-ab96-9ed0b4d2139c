// 需求明细
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['demandPool:dashboard:export'])"
      @handleExport="handleExport"
      labelWidth="120px"
    >
      <template slot="businessType">
        <el-row>
          <el-col :span="12">
            <el-select
              v-model="searchForm.oneBusinessTypes"
              placeholder="一级业务类型"
              clearable
              multiple
              style="width: 100%;"
              collapse-tags
              @change="handleFirstBusinessChange"
            >
              <el-option
                v-for="item in businessFirstLevelList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
              v-model="searchForm.twoBusinessTypes"
              placeholder="二级业务类型"
              clearable
              multiple
              collapse-tags
              style="width: 100%;"
            >
              <el-option
                v-for="item in businessChildrenList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
      </template>
      <template slot="demandType">
        <el-row>
          <el-col :span="12">
            <el-select
              v-model="searchForm.oneDemandTypes"
              placeholder="一级需求类型"
              clearable
              style="width: 100%;"
              multiple
              collapse-tags
              @change="handleFirstDemandChange"
            >
              <el-option
                v-for="item in demandFirstLevelList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select
              v-model="searchForm.twoDemandTypes"
              placeholder="二级需求类型"
              clearable
              collapse-tags
              multiple
              style="width: 100%;"
            >
              <el-option
                v-for="item in demandChildrenList"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-col>
        </el-row>
      </template>
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        row-id="reportId"
      >
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import { getToken } from "@/utils/auth";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryDetailList,
  exportExcel,
  queryBusinessFirstLevel,
  queryBusinessChildrenList,
  queryDemandFirstLevel,
  queryDemandChildrenList,
} from "@/api/demandPool/dashboard.js";
import checkPermission from "@/utils/permission.js";

export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      stationOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        oneBusinessTypes: [],
        oneDemandTypes: [],
        twoDemandTypes: [],
        twoBusinessTypes: [],
      },
      editForm: { stationName: "", dateRange: [], reason: "" },
      addForm: [{ stationId: "", dateRange: [], reason: "" }],

      options: [],
      finallySearch: null,
      tableData: [],
      tableTotal: 0,
      addVisible: false, //添加站点
      editVisible: false, //忽略

      config: [],
      columns: [
        {
          field: "demandNo",
          title: "需求单号",
        },
        {
          field: "businessType",
          title: "业务类型",
          customWidth: 200,
        },
        {
          field: "demandType",
          customWidth: 200,
          title: "需求类型",
        },
        {
          field: "jobHourStr",
          customWidth: 200,
          title: "工时",
        },
        {
          field: "submitter",
          title: "提交人",
        },
        {
          field: "submitterContact",
          title: "联系方式",
        },
        {
          field: "submitTime",
          title: "提交时间",
        },
        {
          field: "remark",
          title: "备注",
          customWidth: 150,
        },
        {
          field: "demandStatus",
          title: "需求单状态",
          formatter: ({ cellValue }) => {
            return (
              this.demandStatusOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "omApproveStatus",
          title: "运管审批状态",
        },
        {
          field: "omApproveNo",
          title: "运管审批单号",
        },
        {
          field: "receiveTime",
          title: "接收时间",
        },
        {
          field: "handleUserName",
          title: "处理人",
        },
        {
          field: "confirmTime",
          title: "确认时间",
        },
        {
          field: "submitOmTime",
          title: "运管提交时间",
        },
        {
          field: "finishTime",
          title: "完结时间",
        },
        {
          field: "fileTime",
          title: "归档时间",
        },
        {
          field: "receiveDuration",
          title: "接收时长",
        },
        {
          field: "receiveIsTimeout",
          title: "接收是否超时",
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{ color: row.receiveIsTimeout === "Y" ? "red" : "" }}
                >
                  {row.receiveIsTimeout === "Y"
                    ? "是"
                    : row.receiveIsTimeout === "N"
                    ? "否"
                    : ""}
                </div>,
              ];
            },
          },
        },
        {
          field: "receiveTimeoutDuration",
          title: "接收超时时长",
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{ color: row.receiveIsTimeout === "Y" ? "red" : "" }}
                >
                  {row.receiveTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "handleDuration",
          title: "处理时长",
        },
        {
          field: "rejectCount",
          title: "驳回次数",
        },
        {
          field: "handleIsTimeout",
          title: "处理是否超时",
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{ color: row.handleIsTimeout === "Y" ? "red" : "" }}
                >
                  {row.handleIsTimeout === "Y"
                    ? "是"
                    : row.handleIsTimeout === "N"
                    ? "否"
                    : ""}
                </div>,
              ];
            },
          },
        },
        {
          field: "handleTimeoutDuration",
          title: "处理超时时长",
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{ color: row.handleIsTimeout === "Y" ? "red" : "" }}
                >
                  {row.handleTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "omApproveDuration",
          title: "运管审批时长",
        },
        {
          field: "submitOmIsTimeout",
          title: "提交运管是否超时",
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{ color: row.submitOmIsTimeout === "Y" ? "red" : "" }}
                >
                  {row.submitOmIsTimeout === "Y"
                    ? "是"
                    : row.submitOmIsTimeout === "N"
                    ? "否"
                    : ""}
                </div>,
              ];
            },
          },
        },
        {
          field: "submitOmTimeoutDuration",
          title: "提交运管超时时长",
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{ color: row.submitOmIsTimeout === "Y" ? "red" : "" }}
                >
                  {row.submitOmTimeoutDuration}
                </div>,
              ];
            },
          },
        },
        {
          field: "submitToFIleDuration",
          title: "需求提交到归档耗时",
        },
        {
          field: "totalDuration",
          title: "需求总耗时",
        },
        {
          field: "timelyRate",
          title: "及时率",
        },
      ],
      tableId: "dashboardDetailTable", //tableId必须项目唯一，用于缓存展示列
      recordList: [],
      token: "",
      businessFirstLevelList: [],
      businessChildrenList: [],
      demandFirstLevelList: [],
      demandChildrenList: [],
      demandStatusOptions: [],
    };
  },
  mounted() {
    this.token = getToken();
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.queryBusinessType(),
      this.queryDemandType(),
      this.getDicts("cm_demand_status").then((response) => {
        this.demandStatusOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
          this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    //初始化
    initConfig() {
      this.config = [
        {
          key: "demandNo",
          title: "需求单号",
          type: "input",
          placeholder: "请输入需求单号",
        },
        {
          key: "submitter",
          title: "提交人",
          type: "input",
          placeholder: "请输入提交人",
        },
        {
          key: "submitRangeTime",
          title: "提交时间",
          type: "dateRange",
          placeholder: "请选择提交时间",
        },
        {
          key: "demandType",
          title: "需求类型",
          type: "slot",
          colNum: 16,
        },
        {
          key: "demandStatus",
          title: "需求状态",
          type: "select",
          placeholder: "请选择需求状态",
          options: this.demandStatusOptions,
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "slot",
          colNum: 16,
        },
        {
          key: "handleUserName",
          title: "处理人",
          type: "input",
          placeholder: "请输入处理人",
        },
        {
          key: "acceptRangeTime",
          title: "受理时间",
          type: "dateRange",
          placeholder: "请选择受理时间",
        },
        {
          key: "operateRangeTime",
          title: "提交运管时间",
          type: "dateRange",
          placeholder: "请选择提交运管时间",
        },
        {
          key: "verifyRangeTime",
          title: "确认时间",
          type: "dateRange",
          placeholder: "请选择确认时间",
        },
        {
          key: "receiveIsTimeout",
          title: "受理是否超时",
          type: "select",
          placeholder: "请选择受理是否超时",
          options: [
            { dictLabel: "是", dictValue: "Y" },
            { dictLabel: "否", dictValue: "N" },
          ],
        },
        {
          key: "submitOmIsTimeout",
          title: "提交运管是否超时",
          type: "select",
          placeholder: "请选择提交运管是否超时",
          options: [
            { dictLabel: "是", dictValue: "Y" },
            { dictLabel: "否", dictValue: "N" },
          ],
        },
        {
          key: "handleIsTimeout",
          title: "处理是否超时",
          type: "select",
          placeholder: "请选择处理是否超时",
          options: [
            { dictLabel: "是", dictValue: "Y" },
            { dictLabel: "否", dictValue: "N" },
          ],
        },
      ];
    },
    async queryBusinessType() {
      const res = await queryBusinessFirstLevel();
      if (res?.code === "10000") {
        this.businessFirstLevelList = res.data;
      }
    },
    async queryDemandType() {
      const res = await queryDemandFirstLevel();
      if (res?.code === "10000") {
        this.demandFirstLevelList = res.data;
      }
    },
    async handleFirstDemandChange(val) {
      console.log(val, "---val");
      if (!val || val?.length === 0) {
        this.demandChildrenList = [];
        this.searchForm.twoDemandTypes = [];
      } else {
        const res = await queryDemandChildrenList({ ids: val });
        if (res?.code === "10000") {
          this.demandChildrenList = res.data;
          const arr = this.demandChildrenList?.map((x) => x.id);
          this.searchForm.twoDemandTypes = this.searchForm.twoDemandTypes?.filter(
            (item) => arr.indexOf(item) > -1
          );
        }
      }
    },
    async handleFirstBusinessChange(val) {
      if (!val || val?.length === 0) {
        this.businessChildrenList = [];
        this.searchForm.twoBusinessTypes = [];
      } else {
        const res = await queryBusinessChildrenList({ ids: val });
        if (res?.code === "10000") {
          this.businessChildrenList = res.data;
          const arr = this.businessChildrenList?.map((x) => x.id);
          this.searchForm.twoBusinessTypes = this.searchForm.twoBusinessTypes?.filter(
            (item) => arr.indexOf(item) > -1
          );
        }
      }
    },
    //查询列表
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };
      this.handleTimeRange(params);
      // this.finallySearch = args;
      queryDetailList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "acceptRangeTime",
          title: "接收时间",
          startFieldName: "receiveBeginTime",
          endFieldName: "receiveEndTime",
        },
        {
          field: "submitRangeTime",
          title: "提交时间",
          startFieldName: "submitBeginTime",
          endFieldName: "submitEndTime",
        },
        {
          field: "verifyRangeTime",
          title: "确认时间",
          startFieldName: "confirmBeginTime",
          endFieldName: "confirmEndTime",
        },
        {
          field: "operateRangeTime",
          title: "提交运管时间",
          startFieldName: "submitOmBeginTime",
          endFieldName: "submitOmEndTime",
        },
        // {
        //   field: "endRangeTime",
        //   title: "完结时间",
        //   startFieldName: "finishBeginTime",
        //   endFieldName: "finishEndTime",
        // },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      const params = {
        ...this.searchForm,
      };
      this.handleTimeRange(params);
      console.log("查询", params);
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
