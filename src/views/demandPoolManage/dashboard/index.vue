//数据看板
<template>
  <div>
    <div class="box-menu">
      <el-radio-group v-model="activeName" style="margin: 20px;">
        <el-radio-button
          v-for="(item, index) in realList"
          :key="index"
          :label="item.label"
          >{{ item.title }}</el-radio-button
        >
        <!-- <el-radio-button
          label="detail"
          v-show="checkPermission(['demandPool:dashboard:detail'])"
          >需求明细</el-radio-button
        > -->
      </el-radio-group>
    </div>
    <Overview
      v-if="activeName == 'overview'"
      v-has-permi="['demandPool:dashboard:overview']"
    ></Overview>
    <DetailTable
      v-if="activeName == 'detail'"
      v-has-permi="['demandPool:dashboard:detail']"
    ></DetailTable>
  </div>
</template>

<script>
import Overview from "./components/overview";
import DetailTable from "./components/detailTable";
import checkPermission from "@/utils/permission.js";
export default {
  components: { Overview, DetailTable },
  data() {
    return {
      activeName: "overview",
      groupRadioList: [
        {
          label: "overview",
          title: "需求总览",
          show: () => {
            return this.checkPermission(["demandPool:dashboard:overview"]);
          },
        },
        {
          label: "detail",
          title: "需求明细",
          show: () => {
            return this.checkPermission(["demandPool:dashboard:detail"]);
          },
        },
      ],
    };
  },

  created() {
    this.realList = this.groupRadioList.filter((x) => !!x.show());
    this.activeName = this.realList[0]?.label || "";
    console.log(this.realList, "realList");
  },
  mounted() {},
  methods: {
    checkPermission,
  },
};
</script>

<style lang="less" scoped>
.box-menu {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.form-content {
  padding: 0 20px;
  margin-bottom: 18px;
  /deep/ .el-form {
    display: flex;
  }
  /deep/ .el-radio-button--mini .el-radio-button__inner {
    padding: 7px 10px;
  }
  /deep/ .el-date-editor .el-range-separator {
    width: 7%;
  }
  /deep/ .el-form-item--small.el-form-item {
    margin-bottom: 0;
    // margin-right: 20px;
    // display: flex;
  }
  /deep/ .vue-treeselect__control {
    table-layout: auto;
  }
  /deep/ .el-date-editor--daterange.el-input__inner {
    width: 250px;
  }
}
</style>
