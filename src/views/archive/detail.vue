<!-- 人员详情 -->
<template>
  <div class="app-container" v-loading="pageLoading">
    <div style="display: flex;justify-content: space-between;">
      <h3>人员档案详情</h3>
      <div>
        <el-button
          type="text"
          size="medium"
          v-for="(item, index) in btnArr"
          :key="index"
          @click="item.event(params)"
          v-show="item.condition(params)"
          >{{ item.title }}</el-button
        >
      </div>
    </div>

    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="params"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        preview
      >
      </DynamicForm>
    </el-card>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>联系方式</span>
      </div>
      <DynamicForm
        ref="contactForm"
        :config="contactConfig"
        :params="params"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        preview
      >
      </DynamicForm>
    </el-card>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <DynamicForm
        ref="otherForm"
        :config="otherConfig"
        :params="params"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        preview
      >
      </DynamicForm>
    </el-card>
    <el-drawer title="操作日志" :visible.sync="drawerVisible" size="60%">
      <Timeline
        :list="recordList"
        operateTypeTitle="operatorTypeName"
        operatorNameTitle="operatorUserName"
        createTimeTitle="operatorTime"
        operateDetailTitle="remark"
      ></Timeline>
    </el-drawer>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import { regionData } from "element-china-area-data";
import api from "@/api/archive/index.js";
import checkPermission from "@/utils/permission.js";
import Timeline from "@/components/Timeline/index.vue";
export default {
  name: "archiveDetailPage",
  components: { Timeline },
  mixins: [],
  data() {
    return {
      drawerVisible: false,
      recordList: [],
      id: "",
      btnLoading: false,
      pageLoading: false,
      params: {},
      personPropOptions: [],
    };
  },
  computed: {
    btnArr() {
      return [
        {
          title: "编辑",
          event: (row) => {
            return this.rowEdit(row);
          },
          condition: (row) => {
            return checkPermission(["archive:list:edit"]);
          },
        },
        {
          title: "日志",
          event: (row) => {
            return this.handleLog(row);
          },
          condition: (row) => {
            return checkPermission(["archive:list:log"]);
          },
        },
      ];
    },
    baseConfig() {
      return [
        {
          field: "name",
          title: "姓名：",
          attrs: {
            placeholder: "100个字符以内，必填",
            maxlength: 100,
          },
          rules: [
            {
              required: true,
              message: "请输入姓名",
              trigger: "change",
            },
          ],
        },
        {
          field: "position",
          title: "职位名称：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryList");
            },
          },
        },
        {
          field: "regionStr",
          title: "所在省市：",
          element: "el-cascader",
          props: {
            options: regionData,
            placeholder: "请选择省市区",
            filterable: true,
          },
        },
        {
          field: "detailAddress",
          title: "详细地址：",
          element: "el-input",
          attrs: {
            placeholder: "1000个字符以内，非必填",
            maxlength: 1000,
          },
        },
        {
          field: "companyName",
          title: "公司名称：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryList");
            },
          },
        },
        {
          field: "deptName",
          title: "所属部门：",
          element: "el-input",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
        {
          field: "attributeName",
          title: "人员属性：",
          element: "el-select",
          props: {
            options: this.personPropOptions,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
            placeholder: "请选择人员属性",
          },
          rules: [
            {
              required: true,
              message: "请选择人员属性",
              trigger: "change",
            },
          ],
        },
      ];
    },
    contactConfig() {
      return [
        {
          field: "mobile",
          title: "手机号：",
          attrs: {
            placeholder: "11位手机号，非必填",
            maxlength: 11,
          },
        },
        {
          field: "landline",
          title: "座机：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
        {
          field: "wechat",
          title: "微信号：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
        {
          field: "email",
          title: "邮箱：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
      ];
    },
    otherConfig() {
      return [
        {
          field: "industryType",
          title: "行业类型：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryList");
            },
          },
        },
        {
          field: "remark",
          title: "备注：",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 10000,
            showWordLimit: true,
            placeholder: "10000个字符以内，非必填",
          },
        },
      ];
    },
  },
  created() {
    this.id = this.$route.query.id;
    this.getDicts("staff_attribute").then((response) => {
      this.personPropOptions = response.data;
    });

    this.params = {
      ...initParams(this.baseConfig),
      ...initParams(this.contactConfig),
      ...initParams(this.otherConfig),
    };
    this.getDetail();
  },
  // watch: {
  //   baseParams: {
  //     handler(val) {
  //       console.log(val, "------val");
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    rowEdit(row) {
      this.$router.push({
        path: "/archive/edit",
        query: {
          type: "edit",
          id: row.id,
        },
      });
    },
    handleLog(row) {
      api.queryLog({ id: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.drawerVisible = true;
    },
    querySearch(queryString, cb, api) {
      api[api]({
        name: queryString,
        companyName: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.companyName || x };
        });
        cb(result);
      });
    },
    goBack() {
      this.$router.go(-1);
    },

    async getDetail() {
      this.pageLoading = true;
      const res = await api
        .getDetail({
          id: this.id,
        })
        .catch(() => {
          this.pageLoading = false;
        });
      this.pageLoading = false;
      if (res?.code === "10000") {
        const { province, county, city } = res.data;
        this.params = { ...this.params, ...res.data };
        this.params.region = [province, city, county];
      }
    },
  },
};
</script>

<style lang="less"></style>
