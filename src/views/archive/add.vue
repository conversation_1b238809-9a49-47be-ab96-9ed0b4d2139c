<!-- 新增人员 -->
<template>
  <div class="app-container" v-loading="pageLoading">
    <h3>{{ type === "add" ? "新增" : "编辑" }}人员档案</h3>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <DynamicForm
        ref="baseForm"
        :config="baseConfig"
        :params="params"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      >
      </DynamicForm>
    </el-card>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>联系方式</span>
      </div>
      <DynamicForm
        ref="contactForm"
        :config="contactConfig"
        :params="params"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      >
      </DynamicForm>
    </el-card>
    <el-card id="info">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>其他信息</span>
      </div>
      <DynamicForm
        ref="otherForm"
        :config="otherConfig"
        :params="params"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
      >
      </DynamicForm>
    </el-card>
    <div class="dialog-footer">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading"
        >取 消</el-button
      >
      <el-button
        @click="submit"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import { regionData } from "element-china-area-data";
import api from "@/api/archive/index.js";

export default {
  name: "archiveAddPage",
  components: {},
  mixins: [],
  data() {
    return {
      id: "",
      btnLoading: false,
      pageLoading: false,
      type: "add",
      params: {},
      personPropOptions: [],
    };
  },
  computed: {
    baseConfig() {
      return [
        {
          field: "name",
          title: "姓名：",
          attrs: {
            placeholder: "100个字符以内，必填",
            maxlength: 100,
          },
          rules: [
            {
              required: true,
              message: "请输入姓名",
              trigger: "change",
            },
          ],
        },
        {
          field: "position",
          title: "职位名称：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryPosition");
            },
          },
        },
        {
          field: "region",
          title: "所在省市：",
          element: "custom-cascader",
          attrs: {
            options: regionData,
            placeholder: "请选择省市区",
            filterable: true,
            clearable: true,
            props: {
              checkStrictly: true,
            },
          },
        },
        {
          field: "detailAddress",
          title: "详细地址：",
          element: "el-input",
          attrs: {
            placeholder: "1000个字符以内，非必填",
            maxlength: 1000,
          },
        },
        {
          field: "companyName",
          title: "公司名称：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryCompany");
            },
          },
        },
        {
          field: "deptName",
          title: "所属部门：",
          element: "el-input",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
        {
          field: "attribute",
          title: "人员属性：",
          element: "el-select",
          props: {
            options: this.personPropOptions,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
            placeholder: "请选择人员属性",
          },
          rules: [
            {
              required: true,
              message: "请选择人员属性",
              trigger: "change",
            },
          ],
        },
      ];
    },
    contactConfig() {
      return [
        {
          field: "mobile",
          title: "手机号：",
          attrs: {
            placeholder: "11位手机号，非必填",
            maxlength: 11,
          },
          rules: [
            {
              pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
              message: "请输入正确的手机号码",
              trigger: "change",
            },
          ],
        },
        {
          field: "landline",
          title: "座机：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
        {
          field: "wechat",
          title: "微信号：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
        },
        {
          field: "email",
          title: "邮箱：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          rules: [
            {
              type: "email",
              message: "请输入正确的邮箱地址",
              trigger: "change",
            },
          ],
        },
      ];
    },
    otherConfig() {
      return [
        {
          field: "industryType",
          title: "行业类型：",
          attrs: {
            placeholder: "100个字符以内，非必填",
            maxlength: 100,
          },
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryIndustryType");
            },
          },
        },
        {
          field: "remark",
          title: "备注：",
          props: {
            type: "textarea",
          },
          attrs: {
            rows: 5,
            maxlength: 10000,
            showWordLimit: true,
            placeholder: "10000个字符以内，非必填",
          },
        },
      ];
    },
  },
  created() {
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.getDicts("staff_attribute").then((response) => {
      this.personPropOptions = response.data;
    });

    this.params = {
      ...initParams(this.baseConfig),
      ...initParams(this.contactConfig),
      ...initParams(this.otherConfig),
      attribute: this.$route.query.attribute,
    };
    this.getDetail();
  },
  // watch: {
  //   baseParams: {
  //     handler(val) {
  //       console.log(val, "------val");
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        name: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    goBack() {
      this.$router.go(-1);
    },

    async getDetail() {
      if (!this.id) return;
      this.pageLoading = true;
      const res = await api
        .getDetail({
          id: this.id,
        })
        .catch(() => {
          this.pageLoading = false;
          this.pageLoading = false;
        });
      this.pageLoading = false;
      if (res?.code === "10000") {
        const { province, county, city } = res.data;
        this.params = { ...this.params, ...res.data };
        this.params.region = [];
        province && (this.params.region[0] = province);
        city && (this.params.region[1] = city);
        county && (this.params.region[2] = county);
      }
    },

    async submit() {
      console.log(this.params, "保存");
      this.$refs.baseForm.validate((valid) => {
        if (!valid) {
          return;
        }
        this.$confirm(`是否确认保存？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async (response) => {
          const { region } = { ...this.params };
          let params = {
            ...this.params,
            province: region?.[0],
            city: region?.[1],
            county: region?.[2],
          };
          this.btnLoading = true;
          const res = await api.submitForm(params).catch(() => {
            this.btnLoading = false;
          });
          this.btnLoading = false;
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.goBack();
          }
        });
      });
      // });
    },
  },
};
</script>

<style lang="less"></style>
