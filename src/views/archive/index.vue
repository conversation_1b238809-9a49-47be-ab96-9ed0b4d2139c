<!-- 人员档案 -->
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      @input="handleReset"
      style="margin-bottom: 20px;"
      size="medium"
      :disabled="loading"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in topTabList"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
      v-if="tabActiveTab"
    >
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="rowAdd"
          v-has-permi="['archive:list:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['archive:list:batchAdd']"
          >导入</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['archive:list:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
    <el-drawer title="操作日志" :visible.sync="drawerVisible" size="60%">
      <Timeline
        :list="recordList"
        operateTypeTitle="operatorTypeName"
        operatorNameTitle="operatorUserName"
        createTimeTitle="operatorTime"
        operateDetailTitle="remark"
      ></Timeline>
    </el-drawer>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入人员档案"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/archive/index.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import BatchUpload from "@/components/BatchUpload/index.vue";
export default {
  name: "archivePage",
  components: { Timeline, BatchUpload },
  mixins: [exportMixin],
  data() {
    return {
      uploadObj: {
        api: "/export/report/importStaffInfo",
        url: "/charging-maintenance-ui/static/人员档案批量导入模板.xlsx",
        extraData: {},
      },
      drawerVisible: false,
      recordList: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "name",
          title: "姓名",
          width: 100,
        },
        {
          field: "mobile",
          title: "手机号",
          width: 150,
        },
        {
          field: "wechat",
          title: "微信号",
          width: 150,
        },
        {
          field: "companyName",
          title: "所属公司",
          width: 200,
        },
        {
          field: "deptName",
          title: "所属部门",
          width: 150,
        },
        {
          field: "position",
          title: "职位名称",
          width: 150,
          //   width: 250,
        },
        {
          field: "regionStr",
          title: "所在省市",
          width: 150,
        },
        {
          field: "industryType",
          title: "行业类型",
          width: 150,
        },
        {
          field: "remark",
          title: "备注",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      tabActiveTab: "inner",
      topTabList: [],
      topTabDict: [
        {
          value: "inner",
          label: "内部人员",
          show: () => {
            return this.checkPermission(["archive:list:innerTab"]);
          },
        },
        {
          value: "outer",
          label: "外部人员",
          show: () => {
            return this.checkPermission(["archive:list:outerTab"]);
          },
        },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "name",
            element: "el-input",
            title: "姓名",
          },
          {
            field: "mobile",
            element: "el-input",
            title: "手机号",
          },
          {
            field: "position",
            title: "职位名称",
            element: "el-input",
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增",
        editBtn: checkPermission(["archive:list:edit"]),
        editTitle: "编辑",
        delBtn: checkPermission(["archive:list:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["archive:list:log"]);
            },
          },
          {
            title: "详情",
            typeName: "detail",
            showForm: false,
            event: (row) => {
              return this.handleDetail(row);
            },
            condition: (row) => {
              return checkPermission(["archive:list:view"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    this.topTabList = this.topTabDict.filter((x) => !!x.show());
    this.tabActiveTab = this.topTabList[0]?.value || "";
    Promise.all([
      //   this.getDicts("support_dept").then((response) => {
      //     this.deptOptions = response.data;
      //   }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleBatchAdd() {
      this.$refs.batchUpload.open();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.queryLog({ id: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.drawerVisible = true;
    },
    handleDetail(row) {
      this.$router.push({
        path: "/archive/detail",
        query: { id: row.id },
      });
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        attribute: this.tabActiveTab === "inner" ? "1" : "2",
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        attribute: this.tabActiveTab === "inner" ? "1" : "2",
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const { updateTime, createTime, ...params } = formParams;
      api.update(params).then((res) => {
        if (res.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.$router.push({
        path: "/archive/add",
        query: {
          type: "add",
          attribute:
            this.tabActiveTab === "inner"
              ? "1"
              : this.tabActiveTab === "outer"
              ? "2"
              : "",
        },
      });
    },
    rowEdit(row) {
      this.$router.push({
        path: "/archive/edit",
        query: {
          type: "edit",
          id: row.id,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
