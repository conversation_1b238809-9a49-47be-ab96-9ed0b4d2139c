<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="roadDataTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="goPage(row)" type="text" size="large">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            :disabled="row.status === '11'"
            type="text"
            size="large"
            @click="startTestFn(row)"
            v-has-permi="['roadTest:waitRoadTest:start']"
          >
            开始路测
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <div v-if="startTestVisible">
      <el-dialog
        title="开始路测"
        :visible.sync="startTestVisible"
        :close-on-click-modal="false"
        @close="startTestVisible = false"
        append-to-body
        width="60%"
      >
        <StartTest @flashData="flashData" :rowData="rowData"></StartTest>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { queryProjectInfoByPage } from "@/api/roadTestAcceptance/index.js";
import StartTest from "./components/startTest.vue";
import { regionData } from "element-china-area-data";
export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
    StartTest,
  },
  data() {
    return {
      loading: false,
      startTestVisible: false,
      tableData: [],
      rowData: null,
      roadDataTotal: 0,
      finallySearch: null,
      searchForm: {
        projectCode: "",
        projectName: "",
        stationCode: null,
        stationName: "",
        statusList: ["3"],
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    tableId() {
      return "waitRoadTestTable";
    },
    columns() {
      return [
        {
          title: "项目编码",
          field: "projectCode",
          // slots: { default: "deviceTypeImg" },
        },
        {
          field: "projectName",
          title: "项目名称",
        },
        {
          field: "remark",
          title: "项目备注",
          showOverflow: true,
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
        },
        {
          field: "createTime",
          title: "施工验收完成时间",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
    config() {
      return [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请输入项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请输入项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          slotName: "simpleDeviceTypeNo",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
  },
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    //新建
    startTestFn(row) {
      this.rowData = row;
      this.startTestVisible = true;
    },
    //获取项目列表
    async loadData(params) {
      this.loading = true;
      let args = this._.cloneDeep(params ? params : this.searchForm);
      if (Array.isArray(args.region)) {
        args["provinceList"] = [];
        args["cityList"] = [];
        args["countyList"] = [];
        args.region.forEach((item) => {
          if (item[0]) {
            args["provinceList"].push(item[0]);
          }
          if (item[1]) {
            args["cityList"].push(item[1]);
          }
          if (item[2]) {
            args["countyList"].push(item[2]);
          }
        });
        delete args.region;
      }
      this.finallySearch = args;

      const res = await queryProjectInfoByPage(args).catch(() => {
        this.loading = false;
      });

      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res?.data;
        this.roadDataTotal = res?.total;
      }
    },
    //分页切换
    changePage() {
      this.loadData();
    },
    goPage(row) {
      this.$router.push();
    },
    flashData() {
      this.startTestVisible = false;
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}

.projectcode {
  color: #029c7c;
}
</style>
