<!-- 开始路测 -->
<template>
  <div class="content-wrap">
    <div class="sub_title">执行人信息</div>
    <el-form :model="form" :rules="rules" ref="form" :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="roadTestUser" label="路测负责人">
            <el-select
              v-model="form.roadTestUser"
              filterable
              size="mini"
              placeholder="请选择路测负责人"
            >
              <el-option
                v-for="item in userOption"
                :key="item.value"
                :label="item.nickName"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="sub_title">创建工单</div>
    <el-tabs v-model="activeName" type="card" :before-leave="beforeLeave">
      <el-tab-pane label="充电桩路测工单" name="1"></el-tab-pane>
      <el-tab-pane label="其它路测工单" name="2"></el-tab-pane>
    </el-tabs>
    <div v-show="activeName == '1'" class="queryParamsWrap">
      <el-form :model="form1" :rules="rules1" ref="form1" :inline="true">
        <el-row>
          <el-col :span="12">
            <el-form-item prop="flowType" label="工单类型">
              <el-select
                v-model="form1.flowType"
                filterable
                size="mini"
                placeholder="请选择工单类型"
              >
                <el-option
                  v-for="item in useFlowList1"
                  :key="item.flowType"
                  :label="item.flowTypeName"
                  :value="item.flowType"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <div class="titlebar">
            <el-col :span="8">
              <el-form-item prop="isAsync" label="请选择充电桩">
                <el-button type="primary" :loading="loading" @click="asyncData()">
                  {{ loading ? "数据加载中 ..." : "获取充电桩数据" }}
                </el-button>
              </el-form-item>
            </el-col>
            <span>当前选择充电桩数量：{{ checkRowArr.length }}</span>
          </div>
        </el-row>
      </el-form>

      <div style="margin-top:20px">
        <vxe-table
          border
          class="mytable-scrollbar"
          :data="tableData"
          headerAlign="center"
          ref="xTable"
          align="center"
          @checkbox-change="checkboxChange"
          @checkbox-all="checkboxAll"
        >
          <vxe-table-column
            type="checkbox"
            fixed="left"
            width="60"
          ></vxe-table-column>
          <vxe-table-column field="pileCode" title="充电桩编号">
          </vxe-table-column>
          <vxe-table-column field="pileName" title="充电桩名称">
            <template #default="{ row }">
              <vxe-input v-model="row.pileName" max-length="50" />
            </template>
          </vxe-table-column>
          <vxe-table-column title="操作" width="140px" :show-overflow="false">
            <template #default="{ row, rowIndex }">
              <el-button type="text" @click="handleItemSave(row)">
                保存
              </el-button>
            </template>
          </vxe-table-column>
        </vxe-table>
        <pagination
          :total="pileTableTotal"
          :page.sync="searchForm.pageNum"
          :limit.sync="searchForm.pageSize"
          :autoScroll="false"
          @pagination="handlePageChange"
        />
      </div>
    </div>
    <div v-show="activeName == '2'" class="queryParamsWrap">
      <el-form :model="form2" ref="form2" :inline="true">
        <el-row v-for="(item, index) in form2.typeList" :key="index">
          <el-col :span="12">
            <el-form-item
              label="工单类型"
              :prop="'typeList.' + index + '.flowType'"
              :rules="{
                required: true,
                message: '工单类型不能为空',
                trigger: 'change',
              }"
            >
              <el-select
                v-model="item.flowType"
                size="mini"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in useFlowList2"
                  :key="item.flowType"
                  :label="item.flowTypeName"
                  :value="item.flowType"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button
              size="mini"
              v-if="form2.typeList.length > 1"
              @click="delItem(index)"
            >删除
            </el-button
            >
            <el-button type="primary" size="mini" @click="addItem"
            >添加
            </el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footerbtn">
      <el-button
        type="primary"
        @click.stop="activeName = '1'"
        v-show="activeName != '1'"
      >上一步
      </el-button
      >
      <el-button type="primary" @click.stop="nextFn" v-show="activeName != '2'"
      >下一步
      </el-button
      >
      <el-button
        type="primary"
        @click.stop="submitForm"
        v-show="activeName == '2'"
      >确认
      </el-button
      >
    </div>
    <el-dialog
      title="查询结果"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="visible = false"
      append-to-body
      width="90%"
    >
      <h3>能源维保通的充电桩数据比充电运营平台多<span style="font-size: xx-large;color: #029c7c">{{ count }}</span>条，详情如下，建议双方核实后修改桩关联的站点信息。
      </h3>
      <vxe-table
        border
        class="mytable-scrollbar"
        :data="resultList"
        headerAlign="center"
        ref="xTable"
        align="center"
        style="margin-top: 10px"
      >
        <vxe-table-column field="index" title="序号">
        </vxe-table-column>
        <vxe-table-column field="pileCode" title="充电桩编号">
        </vxe-table-column>
        <vxe-table-column field="pileName" title="充电桩名称">
        </vxe-table-column>
      </vxe-table>
      <div slot="footer" class="footerbtn">
        <el-button type="primary" @click.stop="visible = false">已知晓</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryPileInfoByPage,
  editPileInfo,
  addRoadTest
} from "@/api/roadTestAcceptance/index.js";
import { listByBusinessType } from "@/api/roadTestAcceptance/otherIndex.js";
import { mapState } from "vuex";
import { queryPlatformPileInfo } from "@/api/chargingPile/pile";

export default {
  props: {
    rowData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      tableData: [],
      useFlowList1: [], //工单类型
      useFlowList2: [], //工单类型
      activeName: "1",
      searchForm: {
        stationId: this.rowData.stationId,
        pageNum: 1,
        pageSize: 10
      },
      pileTableTotal: 0,
      rules: {
        roadTestUser: [
          { required: true, message: "请选择路测负责人", trigger: "change" }
        ]
      },
      rules1: {
        flowType: [
          { required: true, message: "请选择工单类型", trigger: "change" }
        ],
      },
      form: {
        roadTestUser: undefined
      },
      form1: {
        flowType: undefined,
        isAsync: undefined
      },
      form2: {
        typeList: [
          {
            flowType: ""
          }
        ]
      },
      checkRowArr: [],
      loading: false,
      visible: false,
      resultList: [],
      count: 0,
      isAsync: false
    };
  },
  methods: {
    asyncData() {
      this.loading = true;
      const param =  { stationCode: this.rowData.stationCode, ...this.searchForm }
      // const param = {
      //   ...this.searchForm, pageNum: 1, pageSize: 10,
      //   stationId: "3821014943983493796",
      //   stationCode: "20230501002"
      // };
      queryPlatformPileInfo(param).then(res => {
        this.loading = false;
        if (res?.success) {
          this.form1.isAsync = true;
          this.tableData = res.data.map((item, i) => {
            return {
              ...item,
              index: i + 1
            };
          });
          this.pileTotal = res.total;
          if (this.tableData.length > 0 && this.tableData[0].relationPileInfoVOList.length > 0) {
            this.visible = true;
            this.resultList = this.tableData[0].relationPileInfoVOList.map((item, i) => {
              return {
                ...item,
                index: i + 1
              };
            });
          } else {
            this.resultList = [];
          }
          this.count = this.tableData[0].relationPileInfoVOList.length;
        }
      });
    },

    // 充电桩列表
    async getTableData() {
      const { code, data, total } = await queryPileInfoByPage(this.searchForm);
      if (code != 10000) return;
      this.tableData = data.map((item, i) => {
        return {
          ...item,
          index: i + 1
        };
      });

      this.pileTableTotal = total;
    },
    //获取工单类型列表
    async getUseFlowList1() {
      const { code, data } = await listByBusinessType({
        businessType: "pileRoadTest",
        app: "SF-CM"
      });
      if (code != 10000) return;
      this.useFlowList1 = data;
    },
    async getUseFlowList2() {
      const { code, data } = await listByBusinessType({
        businessType: "otherRoadTest",
        app: "SF-CM"
      });
      if (code != 10000) return;
      this.useFlowList2 = data;
    },
    //分页切换
    handlePageChange() {
      this.getTableData();
    },
    //勾选触发
    checkboxChange() {
      this.checkRowArr = this.$refs.xTable.getCheckboxRecords();
    },
    //全选框
    checkboxAll({ checked }) {
      this.checkRowArr = checked ? this.$refs.xTable.getCheckboxRecords() : [];
    },
    delItem(index) {
      this.form2.typeList.splice(index, 1);
    },
    addItem() {
      this.form2.typeList.push({
        flowType: ""
      });
    },
    submitForm() {
      this.$refs.form.validate((valid1) => {
        this.$refs.form2.validate((valid2) => {
          if (valid1 && valid2) {
            // let arr = this.form2.typeList.map((item) => {
            //   return item.flowType;
            // });
            // if (arr.length != Array.from(new Set(arr)).length) {
            //   this.$message.error("存在相同的工单类型，请重新选择！");
            //   return;
            // }
            const {
              stationId,
              stationName,
              projectName,
              projectId
            } = this.rowData;
            let data = {
              stationId,
              stationName,
              projectName,
              projectId,
              ...this.form,
              piles: this.checkRowArr.map((item) => {
                return {
                  ...item,
                  relaBizType: "pileRoadTest",
                  ...this.useFlowList1.find((obj) => {
                    return obj.flowType == this.form1.flowType;
                  })
                };
              }),
              others: this.form2.typeList.map((item) => {
                return {
                  ...this.useFlowList2.find((obj) => {
                    return obj.flowType == item.flowType;
                  }),
                  relaBizType: "otherRoadTest"
                };
              })
            };
            this.addRoadTest(data);
          }
        });
      });
    },
    nextFn() {
      this.$refs.form1.validate((valid) => {
        if (valid && this.checkRowArr.length) {
          this.activeName = "2";
        } else {
          this.$message.error("请选择工单类型及充电桩！");
        }
      });
    },
    //tab点击事件
    beforeLeave(activeName) {
      if (activeName == "2" && !this.checkRowArr.length) {
        this.$message.error("请选择充电桩！");
        return false;
      }
    },
    async handleItemSave(row) {
      const { code } = await editPileInfo(row);
      if (code == "10000") {
        this.$message.success("保存成功！");
      }
    },
    async addRoadTest(data) {
      const { code } = await addRoadTest(data);
      if (code == "10000") {
        this.$message.success("保存成功！");
        this.$emit("flashData");
      }
    }
  },
  computed: {
    ...mapState("dataDict", ["userOption"])
  },
  created() {
    this.getUseFlowList1();
    this.getUseFlowList2();
    this.getTableData();
  }
};
</script>
<style lang="less" scoped>
.titlebar {
  display: flex;
  justify-content: space-between;
}

.labelbar {
  font-size: 14px;
  color: #606266;
  font-weight: 700;

  &::before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
}

.content-wrap {
  margin-top: -20px;
}

/* 标题样式 */
.sub_title {
  margin-left: 10px;
  display: list-item;
  line-height: 40px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 20px;
}

.sub_title::marker {
  content: "| ";
  color: #029c7c;
  font-size: 18px;
  font-weight: bold;
}

.footerbtn {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/deep/ .el-dialog__wrapper.result {
  .el-dialog__footer {
    text-align: center !important;
  }
}
</style>
