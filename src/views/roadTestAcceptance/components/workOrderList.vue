<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="searchForm.total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="goPage(row)" type="text" size="large">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="deviceStatus" slot-scope="{ row, $index }">
          <el-progress
            :percentage="
              (NP.divide(row.finishCount, row.flowCount) * 100).toFixed(0) || 0
            "
          ></el-progress>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="businessDetail(row)">
            工单详情
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryProjectInfoByPage,
  businessList,
} from "@/api/projectManage/index.js";
import { mapState } from "vuex";
export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      loading: false,
      startTestVisible: false,
      tableData: [],
      searchForm: {
        deviceNo: "",
        realDeviceName: "",
        deviceTypeNo: null,
        stationName: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  computed: {
    ...mapState("dataDict", [
      "cm_flow_status",
      "flow_business_type",
      "orderTypeOption",
    ]),
    tableId() {
      return "waitRoadTestTable";
    },
    columns() {
      return [
        {
          field: "businessNo",
          title: "工单编号",
        },
        {
          field: "flowType",
          title: "工单类型",
          formatter: ({ cellValue, row, column }) => {
            return this.orderTypeOption.find((el) => el.value == cellValue)
              ?.label;
          },
        },
        {
          field: "relaBizType",
          title: "业务类型",
          formatter: ({ cellValue, row, column }) => {
            return this.flow_business_type.find((el) => el.value == cellValue)
              ?.label;
          },
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "projectName",
          title: "所属项目",
        },
        {
          field: "pileCode",
          title: "充电桩编号",
        },
        {
          field: "pileName",
          title: "充电桩名称",
        },
        {
          field: "cent",
          title: "完成进度",
          slots: { default: "deviceStatus" },
        },
        {
          field: "flowStatus",
          title: "工单状态",
          formatter: ({ cellValue, row, column }) => {
            return this.cm_flow_status.find((el) => el.value == cellValue)
              ?.label;
          },
        },
        {
          title: "操作",
          minWidth: 90,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
    config() {
      return [
        {
          key: "businessNo",
          title: "工单编号",
          type: "input",
          placeholder: "请输入设备编号",
        },
        {
          key: "flowType",
          title: "工单类型",
          type: "select",
          options: this.orderTypeOption,
          optionLabel: "label",
          optionValue: "value",
          placeholder: "请选择工单类型",
        },
        {
          key: "relaBizType",
          title: "业务类型",
          type: "select",
          optionLabel: "label",
          optionValue: "value",
          options: this.flow_business_type,
          placeholder: "请选择业务类型",
        },
        {
          key: "flowStatus",
          title: "工单状态",
          type: "select",
          optionLabel: "label",
          optionValue: "value",
          options: this.cm_flow_status,
          placeholder: "请选择工单状态",
        },
      ];
    },
  },
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    //获取项目列表
    async loadData() {
      this.loading = true;
      const res = await businessList(this.searchForm);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.searchForm.total = res.total;
      }
    },
    //分页切换
    changePage() {
      this.loadData();
    },
    //工单详情
    businessDetail(row) {
      this.$router.push({
        path: "/orderDetail",
        query: {
          businessNo: row.businessNo,
          taskId: row.taskId,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/.el-select-group__title {
    padding-left: 10px;
  }
  /deep/.el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }
  /deep/.el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.projectcode {
  color: #029c7c;
}
</style>
