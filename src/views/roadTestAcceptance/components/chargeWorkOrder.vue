<template>
  <div>
    <el-form :model="form1" :rules="rules1" ref="form1" :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="flowType" label="工单类型">
            <el-select
              v-model="form1.flowType"
              filterable
              size="mini"
              placeholder="请选择工单类型"
            >
              <el-option
                v-for="item in useFlowList1"
                :key="item.flowType"
                :label="item.flowTypeName"
                :value="item.flowType"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="titlebar">
      <span>当前选择充电桩数量：{{ checkRowArr.length }}</span>
    </div>
    <div style="margin-top:20px">
      <vxe-table
        border
        class="mytable-scrollbar"
        :data="tableData"
        headerAlign="center"
        ref="xTable"
        align="center"
        @checkbox-change="checkboxChange"
        @checkbox-all="checkboxAll"
      >
        <vxe-table-column
          type="checkbox"
          fixed="left"
          width="60"
        ></vxe-table-column>
        <vxe-table-column field="pileCode" title="充电桩编号">
        </vxe-table-column>
        <vxe-table-column field="pileName" title="充电桩名称">
          <template #default="{ row }">
            <vxe-input v-model="row.pileName" max-length="50" />
          </template>
        </vxe-table-column>
        <vxe-table-column title="操作" width="140px" :show-overflow="false">
          <template #default="{ row, rowIndex }">
            <el-button type="text" @click="handleItemSave(row)">
              保存
            </el-button>
          </template>
        </vxe-table-column>
      </vxe-table>
      <pagination
        :total="pileTotal"
        :page.sync="searchForm.pageNum"
        :limit.sync="searchForm.pageSize"
        :autoScroll="false"
        @pagination="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
import {
  queryPileInfoByPage,
  editPileInfo
} from "@/api/roadTestAcceptance/index.js";
import { listByBusinessType } from "@/api/roadTestAcceptance/otherIndex.js";

export default {
  data() {
    return {
      rules1: {
        flowType: [
          { required: true, message: "请选择工单类型", trigger: "change" }
        ]
      },
      useFlowList1: [],
      form1: {
        flowType: undefined
      },
      checkRowArr: [],
      tableData: [],
      pileTotal: 0,
      searchForm: {
        pageNum: 1,
        pageSize: 5,
        stationId: this.rowData.stationId
      }
    };
  },
  props: {
    rowData: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  created() {
    this.getUseFlowList1();
    this.getTableData();
  },
  methods: {
    //分页切换
    handlePageChange() {
    },
    //勾选触发
    checkboxChange() {
      this.checkRowArr = this.$refs.xTable.getCheckboxRecords();
    },
    //全选框
    checkboxAll({ checked }) {
      this.checkRowArr = checked ? this.$refs.xTable.getCheckboxRecords() : [];
    },
    //获取工单类型列表
    async getUseFlowList1() {
      const { code, data } = await listByBusinessType({
        businessType: "pileRoadTest",
        app: "SF-CM"
      });
      if (code != 10000) return;
      this.useFlowList1 = data;
    },
    // 充电桩列表
    async getTableData() {
      const { code, data, total } = await queryPileInfoByPage(this.searchForm);
      if (code != 10000) return;
      this.tableData = data.map((item, i) => {
        return {
          ...item,
          index: i + 1
        };
      });
      this.pileTotal = total;
    },
    async handleItemSave(row) {
      const { code } = await editPileInfo(row);
      if (code == "10000") {
        this.$message.success("保存成功！");
      }
    }
  },
  computed: {
    tableId() {
      return "startTestTable";
    },
    columns() {
      return [
        {
          field: "index",
          title: "序号",
          width: "60"
        },
        {
          title: "充电桩编号",
          field: "pileCode"
        },
        {
          title: "充电桩名称",
          field: "stationCode"
        }
      ];
    }
  }
};
</script>

<style></style>
