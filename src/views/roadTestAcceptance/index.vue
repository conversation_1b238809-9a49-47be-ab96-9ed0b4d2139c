<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane
        label="待路测"
        name="1"
        v-if="checkPermission(['roadTest:tab:todo'])"
      >
        <WaitRoadTest v-if="activeName == '1'"></WaitRoadTest>
      </el-tab-pane>
      <el-tab-pane
        label="路测中"
        name="2"
        v-if="checkPermission(['roadTest:tab:doing'])"
      >
        <RoadTestIng v-if="activeName == '2'"></RoadTestIng>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import WaitRoadTest from "./waitRoadTest.vue";
import checkPermission from "@/utils/permission.js";
import RoadTestIng from "./roadTestIng.vue";
import { ROADTEST_CLICK_TAB } from "@/utils/track/track-event-constants";

export default {
  components: {
    WaitRoadTest,
    RoadTestIng,
  },

  data() {
    return {
      activeName: "1",
    };
  },
  created() {
    // 工单类型
    this.$store.dispatch("dataDict/getOrderType");
    // 人员
    this.$store.dispatch("dataDict/getListUser");
    //获取工单状态字典
    this.$store.dispatch("dataDict/getDicts", "cm_flow_status");
    //业务类型字典
    this.$store.dispatch("dataDict/getDicts", "flow_business_type");
  },
  methods: {
    checkPermission,
    handleTabClick() {
      this.reportTrackEvent(ROADTEST_CLICK_TAB, {
        tabname: this.activeName == "1" ? "待路测" : "路测中"
      });
    }
  },
};
</script>

<style></style>
