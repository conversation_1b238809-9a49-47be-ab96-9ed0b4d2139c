<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="roadDataTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceTypeImg" slot-scope="{ row }">
          <el-button @click="showFlowInfo(row)" type="text" size="large">
            {{ row.pileFlowCount }}
          </el-button>
        </template>
        <template slot="deviceStatus" slot-scope="{ row }">
          <el-button @click="showFlowInfo(row)" type="text" size="large">
            {{ row.otherFlowCount }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button :disabled="row.status === '11'" type="text" size="large" @click="addCharge(row)" v-has-permi="['roadTest:roadTestIng:addCharge']">
            添加充电桩路测工单
          </el-button>
          <el-button :disabled="row.status === '11'" type="text" size="large" @click="addOther(row)" v-has-permi="['roadTest:roadTestIng:addOther']">
            添加其它路测工单
          </el-button>
        </template>
        <template slot="pileNum" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showPileInfo(row)">
            {{ row.pileNum }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <div v-if="chargeVisible">
      <el-dialog
        title="添加充电桩路测工单"
        :visible.sync="chargeVisible"
        :close-on-click-modal="false"
        @close="chargeVisible = false"
        append-to-body
        width="60%"
      >
        <ChargeWorkOrder :rowData="rowData" ref="chargeWorkOrder"></ChargeWorkOrder>
        <div slot="footer" class="dialog-footer">
          <el-button @click.stop="closeVisible">取 消</el-button>
          <el-button type="primary" @click="addWorkOrderFn">确认</el-button>
        </div>
      </el-dialog>
    </div>
    <div v-if="otherVisible">
      <el-dialog
        title="添加其它路测工单"
        :visible.sync="otherVisible"
        :close-on-click-modal="false"
        @close="otherVisible = false"
        append-to-body
        width="40%"
      >
        <OtherWorkOrder :rowData="rowData" ref="otherWorkOrder"></OtherWorkOrder>
        <div slot="footer" class="dialog-footer">
          <el-button @click.stop="closeVisible">取 消</el-button>
          <el-button type="primary" @click="addOtherWorkOrder">确认</el-button>
        </div>
      </el-dialog>
    </div>
    <div v-if="workOrderListVisible">
      <el-dialog
        title="工单列表"
        :visible.sync="workOrderListVisible"
        :close-on-click-modal="false"
        @close="workOrderListVisible = false"
        append-to-body
        width="90%"
      >
        <WorkOrderList :rowData="rowData"></WorkOrderList>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { queryRoadTestProjectInfoByPage } from "@/api/projectManage/index.js";
import ChargeWorkOrder from "./components/chargeWorkOrder.vue";
import OtherWorkOrder from "./components/otherWorkOrder.vue";
import WorkOrderList from "./components/workOrderList.vue";
import { regionData } from "element-china-area-data";
import {
  addRoadTest
} from "@/api/roadTestAcceptance/index.js";
import { mapState } from "vuex";

export default {
  name: "projectManage",
  components: {
    AdvancedForm,
    GridTable,
    ChargeWorkOrder,
    OtherWorkOrder,
    WorkOrderList
  },
  data() {
    return {
      loading: false,
      otherVisible: false,
      chargeVisible: false,
      workOrderListVisible: false,
      tableData: [],
      roadDataTotal: 0,
      finallySearch: null,
      searchForm: {
        projectCode: "",
        projectName: "",
        stationCode: null,
        stationName: "",
        statusList: ["4", "5", "6", "7", "8"],
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      rowData: null
    };
  },
  computed: {
    ...mapState("dataDict", [
      "userOption"
    ]),
    tableId() {
      return "waitRoadTestTable";
    },
    columns() {
      return [
        {
          title: "项目编码",
          field: "projectCode"
        },
        {
          field: "projectName",
          title: "项目名称"
        },
        {
          field: "remark",
          title: "项目备注",
          showOverflow: true
        },
        {
          field: "stationCode",
          title: "站点编码"
        },
        {
          field: "stationName",
          title: "站点名称"
        },
        {
          field: "stationAddress",
          title: "地址",
          showOverflow: true
        },
        {
          field: "createTime",
          title: "施工验收完成时间",
          showOverflow: true
        },
        {
          field: "roadTestStartTime",
          title: "路测开始时间",
          showOverflow: true
        },
        {
          field: "roadTestUser",
          title: "路测负责人",
          showOverflow: true,
          formatter: ({ cellValue, row, column }) => {
            return this.userOption?.find(el => el.value == cellValue)?.label;
          }
        },
        {
          field: "pileFlowCount",
          title: "充电桩路测工单",
          showOverflow: true,
          slots: { default: "deviceTypeImg" }
        },
        {
          field: "pileNum",
          title: "充电桩数量",
          showOverflow: true,
          slots: { default: "pileNum" }
        },
        {
          field: "pileFlowFinishCount",
          title: "已完成数量",
          showOverflow: true
        },
        {
          field: "pileFlowNotFinishCount",
          title: "未完成数量",
          showOverflow: true
        },
        {
          field: "otherFlowCount",
          title: "其他路测工单",
          showOverflow: true,
          slots: { default: "deviceStatus" }
        },
        {
          field: "otherFlowFinishCount",
          title: "已完成数量",
          showOverflow: true
        },
        {
          field: "otherFlowNotFinishCount",
          title: "未完成数量",
          showOverflow: true
        },
        {
          title: "操作",
          minWidth: 350,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" }
        }
      ];
    },
    config() {
      return [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请输入项目编码"
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请输入项目名称"
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码"
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称"
        }, 
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    }
  },
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    //新建
    startTestFn(row) {
      this.startTestVisible = true;
    },
    //获取项目列表
    async loadData(params) {

      this.loading = true;
      let args = this._.cloneDeep(params ? params : this.searchForm);
      if (Array.isArray(args.region)) {
        args["provinceList"] = [];
        args["cityList"] = [];
        args["countyList"] = [];
        args.region.forEach((item) => {
          if (item[0]) {
            args["provinceList"].push(item[0]);
          }
          if (item[1]) {
            args["cityList"].push(item[1]);
          }
          if (item[2]) {
            args["countyList"].push(item[2]);
          }
        });
        delete args.region;
      }
      this.finallySearch = args;

      const res = await queryRoadTestProjectInfoByPage(args);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data || [];
        this.roadDataTotal = res.total;
      }
    },
    //分页切换
    changePage() {
      this.loadData();
    },
    goPage(row) {
      this.$router.push();
    },
    addCharge(row) {
      this.rowData = $deepClone(row);
      this.chargeVisible = true;
    },
    addOther(row) {
      this.rowData = $deepClone(row);
      this.otherVisible = true;
    },
    showFlowInfo(row) {
      this.$router.push({
        path: "/workOrderWorkBench/workOrderWorkbench",
        query: { projectId: row.projectId }
      });
    },
    showPileInfo(row) {
      this.$router.push({
        path: "/stationManage/chargingPileManage",
        query: { stationId: row.stationId, roadTestFlag: true, stationName: row.stationName }
      });
    },
    closeVisible() {
      this.chargeVisible = false;
      this.otherVisible = false;
    },
    addWorkOrderFn() {
      this.$refs.chargeWorkOrder.$refs.form1.validate((valid) => {
        const { checkRowArr, form1, useFlowList1 } = this.$refs.chargeWorkOrder;
        if (valid && checkRowArr.length) {
          let flowTypeObj = useFlowList1.find(item => form1.flowType == item.flowType);

          const {
            stationId,
            stationName,
            projectName,
            projectId,
            roadTestUser
          } = this.rowData;

          this.addRoadTest({
            stationId,
            stationName,
            projectName,
            projectId,
            roadTestUser,
            others: [],
            piles: checkRowArr.map((item) => {
              return {
                ...item,
                ...flowTypeObj
              };
            })
          });
        } else {
          this.$message.error("请选择工单类型及充电桩！");
        }
      });
    },
    addOtherWorkOrder() {
      this.$refs.otherWorkOrder.$refs.form.validate((valid) => {
        const { form, useFlowList } = this.$refs.otherWorkOrder;
        if (valid) {
          // let arr = form.typeList.map((item) => {
          //     return item.flowType;
          //   });
          //   if (arr.length != Array.from(new Set(arr)).length) {
          //     this.$message.error("存在相同的工单类型，请重新选择！");
          //     return;
          //   }
          let othersList = form.typeList.map((item) => {
            return {
              ...useFlowList.find((obj) => {
                return obj.flowType == item.flowType;
              })
            };
          });
          const {
            stationId,
            stationName,
            projectName,
            projectId,
            roadTestUser
          } = this.rowData;
          this.addRoadTest({
            stationId,
            stationName,
            projectName,
            projectId,
            roadTestUser,
            others: othersList,
            piles: []
          });
        }
      });
    },
    async addRoadTest(data) {
      const loading = this.$loading({
        lock: true,
        text: "提交中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      try {
        const { code } = await addRoadTest(data).finally(()=>{
          loading.close();
        })
        if (code == "10000") {
          this.$message.success("操作成功！");
          this.closeVisible();
          this.loadData();
        }
      } catch (e) {
        loading.close();
      }
    }
  }
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
