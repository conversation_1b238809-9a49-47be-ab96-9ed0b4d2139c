<!-- 合作商档案列表 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <!-- <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['contractTest:template:add']"
          >新增</el-button
        >
      </template> -->
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['contract:partner:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operateTypeName"
          operatorNameTitle="operatorName"
          createTimeTitle="operateTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>

      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/infoArchive/partner/partner.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { fileDownLoad } from "@/utils/downLoad.js";
import { downLoadUrl2Blob } from "@/api/common.js";
export default {
  name: "agreementPartnerList",
  components: { Timeline },
  mixins: [exportMixin],
  data() {
    return {
      contractPartyOptions: [],
      currentContractPartyPage: 1,
      contractPartyText: "",
      contractPartyTotal: 0,
      isContractPartyLoading: false,
      stationOptions: [],
      currentStationPage: 1,
      stationText: "",
      stationTotal: 0,
      isStationLoading: false,
      applyStatus: "",
      processList: [],
      processVisible: false,
      omApplyNo: "",
      contractId: "",
      operationType: "tag",
      recordList: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "partnerName",
          title: "合同商名称",
          width: 200,
        },
        {
          field: "partnerTypeNames",
          title: "合作商类别",
          width: 150,
          showOverflow: false,
          slots: {
            default: ({ row }) => {
              return row.partnerTypeNames?.split("|")?.map((x) => {
                return <div>{x}</div>;
              });
            },
          },
        },
        {
          field: "partnerTypeNames",
          title: "标签",
          width: 150,
          showOverflow: false,
          slots: {
            default: ({ row }) => {
              return row.partnerTagName?.map((x) => {
                return (
                  <div class={"mb10 mt10"}>
                    <el-tag type="success">{x}</el-tag>
                  </div>
                );
              });
            },
          },
        },
        {
          field: "address",
          title: "联系地址",
          width: 150,
        },
        {
          field: "contacts",
          title: "联系人",
          width: 200,
        },
        {
          field: "partnerBusiness",
          title: "合作业务",
          width: 150,
        },
        {
          field: "protocolCount",
          title: "合同协议签约数量",
          width: 150,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleJump(row);
                    },
                  }}
                >
                  {row.protocolCount}
                </el-link>
              );
            },
          },
        },
        {
          field: "expireProtocolCount",
          title: "到期合同协议数",
          width: 180,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleJump(row, true);
                    },
                  }}
                >
                  {row.expireProtocolCount}
                </el-link>
              );
            },
          },
        },
        {
          field: "partnerCreateTime",
          title: "合作商创建时间",
          width: 150,
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
      agreementAttrsOptions: [],
      businessAttrsOptions: [],
      agreementSideOptions: [],
      checkStatusOptions: [],
      partnerTagOptions: [],
      businessOptions: [],
      partnerTypeOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "partnerName",
            element: "el-input",
            title: "合作商名称",
          },
          {
            field: "partnerType",
            element: "el-select",
            title: "合作商类别",
            props: {
              filterable: true,
              options: this.partnerTypeOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
          {
            field: "partnerBusiness",
            element: "el-select",
            title: "合作业务",
            props: {
              filterable: true,
              options: this.businessOptions,
              //   optionValue: "dictValue",
              //   optionLabel: "dictLabel",
            },
          },
          {
            field: "partnerTag",
            element: "el-select",
            title: "合作商标签",
            props: {
              filterable: true,
              options: this.partnerTagOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      const form = {
        //打标签
        tag: [
          {
            field: "partnerTag",
            element: "el-select",
            title: "选择标签",
            props: {
              multiple: true,
              filterable: true,
              options: this.partnerTagOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
            attrs: {},
            rules: [{ required: true, message: "请选择标签", trigger: "blur" }],
          },
          {
            field: "remark",
            element: "el-input",
            title: "备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 1000,
              showWordLimit: true,
              placeholder: "请输入备注描述，1000个字符以内",
            },
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: false,
        editTitle: "编辑紧急程度",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        customOperationTypes: [
          {
            title: "打标签",
            modalTitle: "配置合作商标签",
            typeName: "tag",
            event: (row) => {
              return this.handleTag(row);
            },
            condition: (row) => {
              return checkPermission(["contract:partner:tag"]);
            },
          },
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["contract:partner:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    this.getDicts("partner_tag").then((response) => {
      this.partnerTagOptions = response.data;
    });
    this.getDicts("archive_partner_type").then((response) => {
      this.partnerTypeOptions = response.data;
    });

    this.getBusinessOptions();
    this.loadData();
  },

  methods: {
    handleJump(row, isExpired = false) {
      if (isExpired) {
        this.$router.push({
          name: "agreementList",
          params: {
            status: "已到期",
            contractParty: row.partnerName,
          },
        });
      } else {
        this.$router.push({
          name: "agreementList",
          params: {
            contractParty: row.partnerName,
          },
        });
      }
    },
    getBusinessOptions() {
      api.businessType({}).then((res) => {
        this.businessOptions = res.data?.map((x) => {
          return { value: x, label: x };
        });
      });
    },
    //审批进度
    handleProcess(row) {
      this.processVisible = true;
      this.omApplyNo = row.omApplyNo;
      this.applyStatus = row.applyStatus;
      api.queryProcess({ applyNo: row.omApplyNo }).then((res) => {
        if (res?.code == 10000) {
          this.processList = res.data;
        }
      });
    },

    //打标签
    handleTag(row) {
      this.operationType = "tag";
      this.$refs.crud.switchModalView(true, "tag", {
        partnerId: row.partnerId,
        partnerTag: row.partnerTag?.split(","),
        remark: "",
      });
    },
    handleLog(row) {
      api.log({ partnerId: row.partnerId }).then((res) => {
        if (res?.code === "10000") {
          this.recordList = res.data;
        }
      });
      this.$refs.crud.switchModalView(true, "log", {
        ...row,
      });
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "submitTime",
          title: "提交时间",
          startFieldName: "submitStartTime",
          endFieldName: "submitEndTime",
        },
        {
          field: "invalidTime",
          title: "失效时间",
          startFieldName: "expireStartTime",
          endFieldName: "expireEndTime",
        },
        {
          field: "validTime",
          title: "生效时间",
          startFieldName: "effectiveStartTime",
          endFieldName: "effectiveEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const partnerTagName = formParams.partnerTag
        .map((x) => {
          return this.partnerTagOptions?.find((i) => i.dictValue == x)
            ?.dictLabel;
        })
        ?.join(",");
      const params = {
        ...formParams,
        partnerTag: formParams.partnerTag?.join(","),
        partnerTagName: partnerTagName,
      };
      // crudOperationType:tag
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {},
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
        businessTypeIds: row.businessType?.split(",") || [],
      });
    },
    handleClose() {
      this.$refs.crud.switchModalView(false);
    },
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 下拉框下拉的框
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        // 增加滚动监听，
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          console.log(this.scrollHeight, this.scrollTop, this.clientHeight);
          const condition =
            this.scrollHeight - this.scrollTop - 2 <= this.clientHeight;
          // 当滚动条滚动到最底下的时候执行接口加载下一页
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
/deep/ .el-drawer__header {
  margin-bottom: 0px !important;
}
.info-title {
  display: flex;
  justify-content: space-between;
}
</style>
