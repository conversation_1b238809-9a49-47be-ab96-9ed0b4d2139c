<!-- 合同检测 -->
<template>
  <div class="card-container">
    <el-radio-group v-model="activeName" style="margin: 20px 0;">
      <el-radio-button
        v-for="(item, index) in realList"
        :key="index"
        :label="item.label"
        >{{ item.title }}</el-radio-button
      >
    </el-radio-group>
    <OnlineTest
      v-show="activeName == 'test'"
      v-has-permi="['contract:test:onlineTest']"
    ></OnlineTest>
    <TemplateManage
      v-show="activeName == 'template'"
      v-has-permi="['contract:test:template']"
    ></TemplateManage>
    <Record
      v-show="activeName == 'record'"
      v-has-permi="['contract:test:record']"
    ></Record>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import OnlineTest from "./components/onlineTest.vue";
import TemplateManage from "./components/template.vue";
import Record from "./components/record.vue";
export default {
  components: { OnlineTest, TemplateManage, Record },
  data() {
    return {
      activeName: "test",
      realList: [],
      groupRadioList: [
        {
          label: "test",
          title: "在线检测",
          show: () => {
            return this.checkPermission(["contract:test:onlineTest"]);
          },
        },
        {
          label: "template",
          title: "模版管理",
          show: () => {
            return this.checkPermission(["contract:test:template"]);
          },
        },
        {
          label: "record",
          title: "检测记录",
          show: () => {
            return this.checkPermission(["contract:test:record"]);
          },
        },
      ],
    };
  },
  created() {
    this.realList = this.groupRadioList.filter((x) => !!x.show());
    this.activeName = this.realList[0]?.label || "";
  },
  methods: {
    checkPermission,
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 24px;
  font-size: 14px;
}
</style>
