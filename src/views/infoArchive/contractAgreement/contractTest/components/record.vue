<!-- 检测记录 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :filterOptions="filterOptions"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <!-- <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['contractTest:template:add']"
          >新增</el-button
        > -->
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
    <el-drawer
      title="合同协议检查结果预览"
      :visible.sync="drawerVisible"
      size="70%"
    >
      <OfficeFiles
        :fileUrl="fileUrl"
        :fileName="fileName"
        custom-style="overflow:auto"
      ></OfficeFiles>
    </el-drawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/infoArchive/contractAgreement/contractTest.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import OfficeFiles from "@/components/PreviewFiles/vueOfficeFiles.vue";
import { fileDownLoad } from "@/utils/downLoad.js";
import { downLoadUrl2Blob } from "@/api/common.js";
export default {
  name: "templateManagePage",
  components: { Timeline, OfficeFiles },
  mixins: [exportMixin],
  data() {
    return {
      fileUrl: "",
      fileName: "",
      drawerVisible: false,
      recordList: [],
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "archiveAttr",
          title: "合同协议属性",
        },
        {
          field: "archiveType",
          title: "合同协议类型",
        },
        {
          field: "fileName",
          title: "合同协议名称",
        },
        // {
        //   field: "checkItemJson",
        //   title: "检查结果",
        //   width: 150,
        // },
        // {
        //   field: "checkItemJson",
        //   title: "问题项",
        //   width: 150,
        // },
        {
          field: "createName",
          title: "检查人",
          //   width: 250,
        },
        {
          field: "createTime",
          title: "检查时间",
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
      typeOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "fileName",
            element: "el-input",
            title: "协议名称",
          },
          {
            field: "archiveType",
            title: "合同协议类型",
            element: "el-select",
            props: {
              options: this.typeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictLabel",
            },
          },
          {
            field: "checkTime",
            title: "检查时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: false,
        editTitle: "编辑紧急程度",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        customOperationTypes: [
          {
            title: "预览",
            typeName: "preview",
            showForm: false,
            event: (row) => {
              return this.handlePreview(row);
            },
            condition: (row) => {
              return checkPermission(["contractTest:record:preview"]);
            },
          },
          {
            title: "下载",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleDownload(row);
            },
            condition: (row) => {
              return checkPermission(["contractTest:record:download"]);
            },
          },
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["contractTest:record:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.getDicts("archive_type").then((res) => {
      this.typeOptions = res.data;
    });
    this.loadData();
  },

  methods: {
    handleDownload(row) {
      downLoadUrl2Blob({ fileUrl: row.fileCheckUrl }).then(async (res) => {
        if (res) {
          await fileDownLoad(res, row.fileName);
          api.saveDownload({ recordId: row.recordId });
        }
      });
    },
    handlePreview(row) {
      this.drawerVisible = true;
      this.fileUrl = row.fileCheckUrl;
      this.fileName = row.fileName;
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.recordLog({ recordId: row.recordId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "checkTime",
          title: "检查时间",
          startFieldName: "startTime",
          endFieldName: "endTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.recordPageList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const { updateTime, createTime, ...params } = formParams;
      api.update(params).then((res) => {
        if (res.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          tempId: row.tempId,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {},
    rowEdit(row) {},
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
