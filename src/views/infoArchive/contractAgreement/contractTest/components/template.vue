<!-- 模版管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
      v-if="!isEditStatus"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['contractTest:template:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          operateDetailTitle="remark"
          createTimeTitle="operatorTime"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
    <TemplateUploadCom
      v-else
      @back="handleBack"
      :rowData="rowData"
    ></TemplateUploadCom>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/infoArchive/contractAgreement/contractTest.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import TemplateUploadCom from "./templateUploadCom.vue";

export default {
  name: "templateManagePage",
  components: { Timeline, TemplateUploadCom },
  mixins: [exportMixin],
  data() {
    return {
      rowData: {},
      isEditStatus: false,
      recordList: [],
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "archiveAttr",
          title: "合同协议属性",
        },
        {
          field: "archiveType",
          title: "合同协议类型",
        },
        {
          field: "checkItemJson",
          title: "检查项",
          formatter: ({ row }) => {
            const params = JSON.parse(row.checkItemJson);
            return Object.keys(params)
              .map((key) => {
                return key + ":" + this.handleConfig(params[key]);
              })
              .join("｜\n");
          },
        },
        {
          field: "createName",
          title: "创建人",
          //   width: 250,
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "updateName",
          title: "修改人",
        },
        {
          field: "updateTime",
          title: "修改时间",
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: checkPermission(["contractTest:template:edit"]),
        editTitle: "编辑紧急程度",
        delBtn: checkPermission(["contractTest:template:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "supportDept",
            title: "支持部门",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.deptOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择支持部门",
              },
            ],
          },
          {
            field: "businessTypeIds",
            title: "业务类型",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
              multiple: true,
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择业务类型",
              },
            ],
          },
          {
            field: "urgencyName",
            title: "紧急程度名称",
            // element: "el-autocomplete",
            // props: {
            //   fetchSuggestions: (queryString, cb) => {
            //     return this.querySearch(queryString, cb);
            //   },
            // },
            rules: [
              {
                required: true,
                message: "紧急程度名称不允许为空！",
              },
            ],
            attrs: {
              placeholder: "自定义紧急程度",
            },
          },
          {
            field: "urgencyDefinition",
            title: "紧急程度定义",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder: "500个字符以内",
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
            },
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["contractTest:template:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.loadData();
  },

  methods: {
    //规则展示处理：根据上传模版时传的检查项json判断
    handleConfig(params) {
      if (params.config1 == 1) {
        const config2Arr = [
          { value: 1, label: "小于等于" },
          { value: 2, label: "等于" },
          { value: 3, label: "大于等于" },
        ];
        return (
          config2Arr.find((x) => x.value == params.config2)?.label +
          params.config3
        );
      } else {
        return "不能为空";
      }
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.templateLog({ tempId: row.tempId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.templateList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const { updateTime, createTime, ...params } = formParams;
      api.update(params).then((res) => {
        if (res.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          tempId: row.tempId,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.rowData = {};
      this.isEditStatus = true;
    },
    rowEdit(row) {
      this.isEditStatus = true;
      this.rowData = row;
    },
    handleBack() {
      this.isEditStatus = false;
      this.loadData();
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
