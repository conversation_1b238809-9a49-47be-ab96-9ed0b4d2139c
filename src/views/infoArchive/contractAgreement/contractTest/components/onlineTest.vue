<!-- 在线检测 -->
<template>
  <div>
    <div style="display: flex;align-items: center;">
      <el-button @click="handleUpload" type="primary">上传合同&协议</el-button>
      <el-button
        @click="handleDownload"
        type="primary"
        v-if="fileCheckUrlOne && fileCheckUrlTwo"
        >下载检测结果</el-button
      >
      <el-button
        @click="handleEmail"
        type="primary"
        v-if="fileCheckUrlOne && fileCheckUrlTwo"
        >检测结果邮件发送</el-button
      >
      <el-button
        @click="handleCheckItemsDrawer"
        type="primary"
        v-if="fileCheckUrlOne"
        >查看检查项配置</el-button
      >
      <el-tooltip  content="黄色 代表文件检测不一致的内容" class="ml10">
        <div style="background-color: #FDBC06;width: 20px;height: 20px;" ></div>
      </el-tooltip>
      <el-tooltip  content="绿色 代表文件中新增的内容" class="ml10">
        <div style="background-color: #A0D4C9;width: 20px;height: 20px;"></div>
      </el-tooltip>
    </div>
    <!-- <Comparison
      :oldUrl="oldUrl"
      :newUrl="newUrl"
      :realWordList="realWordList"
      :templateWordList="templateWordList"
    ></Comparison> -->
    <div class="comparison-list">
      <el-card class="card-box">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>{{
            isTwoCheck ? "合同协议模版及检查规则" : "合同协议模版"
          }}</span>
        </div>
        <OfficeFiles
          :fileUrl="tempCheckUrl"
          custom-style="overflow:auto"
          v-if="tempCheckUrl"
        ></OfficeFiles>
        <el-empty v-else></el-empty>
      </el-card>
      <el-card class="card-box">
        <div
          slot="header"
          style="display: flex;justify-content: space-between;align-items: center;"
        >
          <div class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>
              {{
                isTwoCheck ? "目标文件检查项检查结果" : "目标文件条款检查结果"
              }}
            </span>
            <span style="font-size: 14px;">
              （共{{ differenceCount }}项不一致）
            </span>
          </div>
          <el-button
            @click="switchCheckUrl(true)"
            type="primary"
            v-if="fileCheckUrlTwo && !isTwoCheck"
            >查看检查项检测结果</el-button
          >
          <el-button
            @click="switchCheckUrl(false)"
            type="primary"
            v-if="isTwoCheck"
            >查看条款检测结果</el-button
          >
          <el-button
            @click="handleTwoCheck"
            type="primary"
            v-if="fileCheckUrlOne && !fileCheckUrlTwo"
            :loading="btnLoading"
            >继续检测检查项</el-button
          >
        </div>
        <OfficeFiles
          :fileUrl="fileCheckUrl"
          custom-style="overflow:auto"
          v-if="fileCheckUrl"
        ></OfficeFiles>
        <el-empty v-else></el-empty>
      </el-card>
    </div>
    <!-- 上传合同协议 -->
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
      modalWidth="70%"
    >
      <template #fileUpload="{item,params}">
        <file-upload
          ref="upload"
          v-model="params.fileUrl"
          :limit="1"
          accept=".docx"
          :fileMaxSize="20"
          :showPaste="false"
          textTip="仅支持单一文件上传，上传格式为docx文件，单个文件20M以内"
        />
      </template>
    </BaseFormModal>
    <el-dialog
      title="合同&协议检测结果"
      :visible.sync="resultVisible"
      :close-on-click-modal="false"
      @close="resultVisible = false"
      append-to-body
      width="500px"
    >
      <div class="modal-box">
        <div class="modal-title">
          目标文件与模版条款有<span>{{ differenceCount }}</span
          >处不一致！
        </div>
        <div>
          为保证检查项的顺序不被打乱，建议您先将目标文件的条款与模版修改一致后，再重新检测。
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resultVisible = false">取消</el-button>
        <el-button type="primary" @click="handleTwoCheck" :loading="btnLoading"
          >仍要检测</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="合同&协议检测结果"
      :visible.sync="intervalVisible"
      :close-on-click-modal="false"
      @close="intervalVisible = false"
      append-to-body
      width="500px"
    >
      <div class="modal-content">
        <div>
          目标文件与模版条款完全一致，{{ seconds }}s后自动进行检查项检测！
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleTwoCheck" :loading="btnLoading"
          >跳过</el-button
        >
      </div>
    </el-dialog>
    <!-- 检测结果邮件发送 -->
    <BaseFormModal
      ref="mailModal"
      :modalTitle="mailModalConfig.modalTitle"
      :config="mailModalConfig.formConfig"
      @modalConfirm="mailModalConfirmHandler"
      labelWidth="130px"
      :autoClose="false"
      :loading="mailLoading"
    >
      <template #filePreview="{item,params}">
        <div>
          <el-link
            :href="params.attachments && params.attachments[0].attachmentPath"
            target="_blank"
          >
            {{ params.attachments[0].fileName }}
          </el-link>
        </div>
        <div>
          <el-link
            :href="params.attachments && params.attachments[1].attachmentPath"
            target="_blank"
          >
            {{ params.attachments[1].fileName }}
          </el-link>
        </div>
      </template>
    </BaseFormModal>
    <el-dialog
      title="发送结果"
      :visible.sync="sendVisible"
      :close-on-click-modal="false"
      @close="sendVisible = false"
      append-to-body
      width="500px"
    >
      <div class="send-box">
        <i class="el-icon-success icon-success" v-if="sendSuccess"></i>
        <i class="el-icon-warning icon-warn" v-else></i>

        <div style="max-width: 250px;">
          <div class="send-box-title">
            {{ sendSuccess ? "邮件发送成功！" : "发送失败！" }}
          </div>
          <div v-if="sendSuccess">
            成功发送<span class="send-box-count">{{ successMailCount }}</span
            >个邮箱！
          </div>
          <div v-else>
            {{ failReason }}
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleSkipClose" v-if="sendSuccess"
          >{{ seconds }}s后自动关闭该弹窗</el-button
        >
        <el-button @click="handleResultClose" v-if="!sendSuccess"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="sendVisible = false"
          v-if="!sendSuccess"
          >重试</el-button
        >
      </div>
    </el-dialog>
    <el-drawer title="检查项配置结果" :visible.sync="drawerVisible" size="60%">
      <DynamicForm
        ref="checkForm"
        :config="checkItemConfig"
        :params="checkParams"
        :defaultColSpan="24"
        labelPosition="right"
        labelWidth="120px"
        v-if="checkItemConfig && checkItemConfig.length > 0"
      >
        <template #checkItem="{item,params}">
          <el-row>
            <el-col :span="24">
              <el-select v-model="params[item.field].config1">
                <el-option label="自定义数据" :value="1"></el-option>
                <el-option label="不能为空" :value="2"></el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row v-if="params[item.field].config1 == 1">
            <el-col :span="12">
              <el-select v-model="params[item.field].config2">
                <el-option label="小于等于" :value="1"></el-option>
                <el-option label="等于" :value="2"></el-option>
                <el-option label="大于等于" :value="3"></el-option>
              </el-select>
            </el-col>
            <el-col :span="12">
              <el-input
                v-model="params[item.field].config3"
                placeholder="请输入"
              ></el-input>
            </el-col>
          </el-row>
        </template>
      </DynamicForm>
      <el-empty v-else></el-empty>
    </el-drawer>
  </div>
</template>

<script>
// import Comparison from "@/components/Comparison/comparison.vue";
import { initParams } from "@/utils/buse.js";
import api from "@/api/infoArchive/contractAgreement/contractTest.js";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import FileUpload from "@/components/Upload/fileUpload4.vue";
import OfficeFiles from "@/components/PreviewFiles/vueOfficeFiles.vue";
import { fileDownLoad } from "@/utils/downLoad";
import { downLoadUrl2Blob } from "@/api/common.js";

export default {
  components: { OfficeFiles, BaseFormModal, FileUpload },
  data() {
    return {
      // tempCheckUrl: "",
      // fileCheckUrl: "",
      tempCheckUrlOne: "",
      fileCheckUrlOne: "",
      tempCheckUrlTwo: "",
      fileCheckUrlTwo: "",
      differenceCountOne: 0,
      differenceCountTwo: 0,
      attrsOptions: [],
      typeOptions: [],
      params: {},
      resultVisible: false,
      isTwoCheck: false,
      intervalVisible: false,
      seconds: 5,
      btnLoading: false,
      checkFileName: "",
      sendVisible: false,
      sendSuccess: true,
      failReason: "",
      successMailCount: 0,
      mailLoading: false,
      archiveType: "",
      archiveAttr: "",
      checkParams: {},
      checkItemConfig: [],
      drawerVisible: false,
    };
  },
  computed: {
    tempCheckUrl() {
      return this.isTwoCheck ? this.tempCheckUrlTwo : this.tempCheckUrlOne;
    },
    fileCheckUrl() {
      return this.isTwoCheck ? this.fileCheckUrlTwo : this.fileCheckUrlOne;
    },
    differenceCount() {
      return this.isTwoCheck
        ? this.differenceCountTwo
        : this.differenceCountOne;
    },
    modalConfig() {
      return {
        modalTitle: "上传合同协议",
        formConfig: [
          {
            field: "archiveAttr",
            title: "合同协议属性",
            element: "el-select",
            props: {
              options: this.attrsOptions,
              optionLabel: "dictLabel",
              optionValue: "dictLabel",
            },
            rules: [
              {
                required: true,
                message: "请选择合同协议属性",
                trigger: "change",
              },
            ],
          },
          {
            field: "archiveType",
            title: "合同协议类型",
            element: "el-select",
            props: {
              options: this.typeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictLabel",
            },
            rules: [
              {
                required: true,
                message: "请选择合同协议类型",
                trigger: "change",
              },
            ],
          },
          {
            title: "附件",
            element: "slot",
            slotName: "fileUpload",
            field: "fileUrl",
            rules: [
              {
                required: true,
                message: "请上传附件",
                trigger: "change",
              },
            ],
          },
        ],
      };
    },
    mailModalConfig() {
      return {
        modalTitle: "邮件发送检测结果",
        formConfig: [
          {
            field: "attachments",
            title: "发送文件",
            element: "slot",
            slotName: "filePreview",
            defaultValue: [
              {
                fileName: this.checkFileName + "_条款检测结果.docx",
                attachmentPath: this.fileCheckUrlOne,
              },
              {
                fileName: this.checkFileName + "_检查项检测结果.docx",
                attachmentPath: this.fileCheckUrlTwo,
              },
            ],
          },
          {
            field: "to",
            title: "邮箱地址",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder:
                "请输入邮件地址，多个地址之间用【,】分隔，最多100个地址",
              rows: 5,
            },
            rules: [
              { required: true, message: "请输入邮箱地址" },
              { validator: this.validateEmails },
            ],
          },
          {
            field: "subject",
            title: "邮箱主题",
            attrs: {
              placeholder: "请输入邮箱主题",
              maxlength: 100,
            },
            defaultValue: "风险告知：" + this.checkFileName,
          },
          {
            field: "text",
            title: "邮件正文",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder: "请输入邮件正文内容",
              rows: 5,
              maxlength: 10000,
              showWordLimit: true,
            },
          },
        ],
      };
    },
  },
  created() {
    this.getDicts("archive_attr").then((res) => {
      this.attrsOptions = res.data;
    });
    this.getDicts("archive_type").then((res) => {
      this.typeOptions = res.data;
    });
  },
  methods: {
    handleCheckItemsDrawer() {
      this.drawerVisible = true;
      api
        .queryCheckItems({
          archiveAttr: this.archiveAttr,
          archiveType: this.archiveType,
        })
        .then((res) => {
          this.checkParams = JSON.parse(res.data.checkItemJson || "{}");
          this.checkItemConfig = Object.keys(this.checkParams)?.map((x) => {
            return {
              title: x,
              field: x,
              element: "slot",
              slotName: "checkItem",
            };
          });
        });
    },
    //下载检测结果
    handleDownload() {
      const arr = [
        {
          name: this.checkFileName + "_条款检测结果.docx",
          url: this.fileCheckUrlOne,
        },
        {
          name: this.checkFileName + "_检查项检测结果.docx",
          url: this.fileCheckUrlTwo,
        },
      ];
      arr.forEach((item) => {
        downLoadUrl2Blob({ fileUrl: item.url }).then(async (res) => {
          if (res) {
            await fileDownLoad(res, item.name);
          }
        });
      });
    },
    // 邮箱校验
    validateEmails(rule, value, callback) {
      const emails = value.split(/,\s*/);
      if (emails.length > 100) {
        callback(new Error("最多输入100个邮箱地址"));
        return;
      }
      const invalid = emails.find(
        (e) => !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(e)
      );
      invalid ? callback(new Error(`无效邮箱地址：${invalid}`)) : callback();
    },
    handleEmail() {
      this.$refs.mailModal.open({
        ...initParams(this.mailModalConfig.formConfig),
      });
    },
    mailModalConfirmHandler(row) {
      console.log(row);
      this.mailLoading = true;
      api
        .sendMail(row)
        .then((res) => {
          this.mailLoading = false;
          this.sendVisible = true;
          if (res.success) {
            this.$refs.mailModal.closeVisible();
            this.sendSuccess = true;
            this.successMailCount = res.data?.successCount;
            this.startCountdownMail();
          } else {
            this.sendSuccess = false;
            this.failReason = res.message;
          }
        })
        .catch(() => {
          this.mailLoading = false;
        });
    },

    //发送结果倒计时
    startCountdownMail() {
      this.seconds = 5;
      this.timer = setInterval(() => {
        if (this.seconds > 0) {
          this.seconds--;
        } else {
          this.handleSkipClose();
        }
      }, 1000);
    },
    //发送结果弹窗关闭
    handleSkipClose() {
      clearInterval(this.timer);
      this.sendVisible = false;
    },
    //发送失败取消按钮
    handleResultClose() {
      this.sendVisible = false;
      this.$refs.mailModal.closeVisible();
    },
    switchCheckUrl(isTwo) {
      this.isTwoCheck = isTwo;
    },
    handleUpload() {
      this.$refs.formModal.open({
        ...initParams(this.modalConfig.formConfig),
      });
    },
    //第一次检测
    modalConfirmHandler(row) {
      const params = { ...row, fileUrl: row.fileUrl[0]?.storePath };
      this.params = params;
      api.checkFilesStepOne(params).then((res) => {
        if (res?.code === "10000") {
          const {
            tempCheckUrl,
            fileCheckUrl,
            differenceCount,
            checkFileName,
            archiveAttr,
            archiveType,
          } = res.data;
          this.fileCheckUrlOne = fileCheckUrl;
          this.tempCheckUrlOne = tempCheckUrl;
          this.differenceCountOne = differenceCount;
          this.isTwoCheck = false;
          this.tempCheckUrlTwo = "";
          this.fileCheckUrlTwo = "";
          this.differenceCountTwo = 0;
          this.checkFileName = checkFileName;
          this.archiveType = archiveType;
          this.archiveAttr = archiveAttr;
          this.showOneCheckModal();
        }
      });
    },
    //第二次检测
    handleTwoCheck() {
      this.intervalVisible = false;
      this.resultVisible = false;
      this.btnLoading = true;
      api
        .checkFilesStepTwo(this.params)
        .then((res) => {
          this.btnLoading = false;
          if (res?.code === "10000") {
            this.isTwoCheck = true;
            const {
              tempCheckUrl,
              fileCheckUrl,
              differenceCount,
              checkFileName,
            } = res.data;
            this.fileCheckUrlTwo = fileCheckUrl;
            this.tempCheckUrlTwo = tempCheckUrl;
            this.differenceCountTwo = differenceCount;
            this.checkFileName = checkFileName;
          }
        })
        .catch(() => {
          this.btnLoading = false;
        });
    },
    //第一次检测后显示弹窗
    showOneCheckModal() {
      if (this.differenceCount > 0) {
        this.resultVisible = true;
      } else {
        this.intervalVisible = true;
        this.startCountdown();
      }
    },
    startCountdown() {
      this.seconds = 5;
      this.timer = setInterval(() => {
        if (this.seconds > 0) {
          this.seconds--;
        } else {
          clearInterval(this.timer);
          this.intervalVisible = false;
          // 这里触发自动检测逻辑
          this.handleTwoCheck();
        }
      }, 1000);
    },
    handleSkip() {
      clearInterval(this.timer);
      this.intervalVisible = false;
      // 跳过后的逻辑
      this.handleTwoCheck();
    },
  },
};
</script>

<style lang="less" scoped>
.comparison-list {
  display: flex;
  margin-top: 10px;
  .card-box {
    flex: 1;
  }
}

.modal-content {
  display: flex;
  justify-content: center;
  align-items: center;
}
.modal-box {
  text-align: center;
  .modal-title {
    font-size: 18px;
    margin-bottom: 10px;
    span {
      font-size: 20px;
      color: red;
      font-weight: 700;
    }
  }
}
.send-box {
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-success {
    font-size: 50px;
    color: #62b837;
    margin-right: 20px;
  }
  .icon-warn {
    font-size: 50px;
    color: #e46b64;
    margin-right: 20px;
  }
  .send-box-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #101010;
  }
  .send-box-count {
    color: red;
    padding: 5px;
  }
}
/deep/ .el-card__header {
  padding: 12px 20px;
  .card-title-wrap {
    height: 34px;
  }
}
/deep/ .el-card__body {
  padding: 0;
}
/deep/ .docx-wrapper {
  padding: 0;
}
</style>
