<template>
  <div>
    <el-row>
      <el-col :span="18">
        <DynamicForm
          ref="baseForm"
          :config="baseConfig"
          :params="params"
          :defaultColSpan="12"
          labelPosition="right"
          labelWidth="120px"
        >
        </DynamicForm>
      </el-col>
      <el-col :span="6">
        <el-button @click="handleUpload" type="primary"
          >上传合同&协议</el-button
        >
        <el-button @click="handleSave" type="primary">保存</el-button>
        <el-button type="text" @click="handleBack">返回></el-button>
      </el-col>
    </el-row>
    <el-alert
      title="在模板中用半角符号${}$圈出检查项（即 ${检查项名称}$ ）"
      type="info"
      :closable="false"
    >
    </el-alert>
    <el-row class="mt10">
      <el-col :span="14">
        <el-card class="file-card">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>源文件</span>
          </div>
          <OfficeFiles
            :fileUrl="fileUrl"
            :fileName="fileName"
            custom-style="height:75vh;overflow:auto"
          ></OfficeFiles>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card>
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>检查项配置结果</span>
          </div>
          <DynamicForm
            ref="checkForm"
            :config="checkItemConfig"
            :params="checkParams"
            :defaultColSpan="24"
            labelPosition="right"
            labelWidth="120px"
            v-if="checkItemConfig && checkItemConfig.length > 0"
          >
            <template #checkItem="{item,params}">
              <el-row>
                <el-col :span="24">
                  <el-select v-model="params[item.field].config1">
                    <el-option label="自定义数据" :value="1"></el-option>
                    <el-option label="不能为空" :value="2"></el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row v-if="params[item.field].config1 == 1">
                <el-col :span="12">
                  <el-select v-model="params[item.field].config2">
                    <el-option label="小于等于" :value="1"></el-option>
                    <el-option label="等于" :value="2"></el-option>
                    <el-option label="大于等于" :value="3"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="params[item.field].config3"
                    placeholder="请输入"
                  ></el-input>
                </el-col>
              </el-row>
            </template>
          </DynamicForm>
          <el-empty v-else></el-empty>
        </el-card>
      </el-col>
    </el-row>
    <BaseFormModal
      ref="formModal"
      :modalTitle="modalConfig.modalTitle"
      :config="modalConfig.formConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
      modalWidth="80%"
    >
      <template #fileUpload="{item,params}">
        <file-upload
          ref="upload"
          v-model="params.file"
          :limit="1"
          accept=".pdf, .docx"
          :fileMaxSize="20"
          :showPaste="false"
          textTip="仅支持单一文件上传，上传格式为pdf、docx文件，单个文件20M以内"
        />
      </template>
    </BaseFormModal>
  </div>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import FileUpload from "@/components/Upload/fileUpload4.vue";
import api from "@/api/infoArchive/contractAgreement/contractTest.js";
import OfficeFiles from "@/components/PreviewFiles/vueOfficeFiles.vue";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
export default {
  components: { OfficeFiles, FileUpload, BaseFormModal },
  props: { rowData: { type: Object, default: () => {} } },
  data() {
    return {
      checkParams: {
        // 规则1: { config1: 1, config2: 1, config3: undefined },
        // 规则2: { config1: 1, config2: 1, config3: undefined },
      },
      checkItemConfig: [],
      uploadVisible: false,
      attrsOptions: [],
      typeOptions: [],
      fileName: "",
      fileUrl: "",
      params: {},
    };
  },
  computed: {
    modalConfig() {
      return {
        modalTitle: "上传合同协议",
        formConfig: [
          {
            title: "附件",
            element: "slot",
            slotName: "fileUpload",
            field: "file",
            rules: [
              {
                required: true,
                message: "请上传附件",
                trigger: "change",
              },
            ],
          },
        ],
      };
    },
    baseConfig() {
      return [
        {
          field: "archiveAttr",
          title: "合同协议属性",
          element: "el-select",
          props: {
            options: this.attrsOptions,
            optionLabel: "dictLabel",
            optionValue: "dictLabel",
          },
          rules: [
            {
              required: true,
              message: "请选择合同协议属性",
              trigger: "change",
            },
          ],
        },
        {
          field: "archiveType",
          title: "合同协议类型",
          element: "el-select",
          props: {
            options: this.typeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictLabel",
          },
          rules: [
            {
              required: true,
              message: "请选择合同协议类型",
              trigger: "change",
            },
          ],
        },
      ];
    },
  },
  created() {
    this.params = { ...initParams(this.baseConfig), ...this.rowData };

    if (this.rowData?.tempUrl) {
      this.fileUrl = this.rowData?.tempUrl || "";
      this.checkParams = JSON.parse(this.rowData?.checkItemJson || "{}");
      this.checkItemConfig = Object.keys(this.checkParams)?.map((x) => {
        return {
          title: x,
          field: x,
          element: "slot",
          slotName: "checkItem",
        };
      });
    }
    this.getDicts("archive_attr").then((res) => {
      this.attrsOptions = res.data;
    });
    this.getDicts("archive_type").then((res) => {
      this.typeOptions = res.data;
    });
  },
  methods: {
    handleSave() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) return;
        const params = {
          ...this.rowData,
          ...this.params,
          tempUrl: this.fileUrl,
          checkItemJson: JSON.stringify(this.checkParams),
        };
        const method = this.rowData.tempId ? "updateTemplate" : "saveTemplate";
        api[method](params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.handleBack();
          }
        });
      });
    },
    handleUpload() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) return;
        this.$refs.formModal.open({
          ...initParams(this.modalConfig.formConfig),
        });
      });
    },
    modalConfirmHandler(row) {
      console.log(row);
      const params = { fileUrl: row.file[0]?.storePath };
      api.uploadTemplate(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success(`提交成功`);
          this.fileUrl = row.file[0]?.storePath;
          this.fileName = row.file[0]?.docName;
          this.checkItemConfig = res.data?.map((x) => {
            return {
              title: x,
              field: x,
              element: "slot",
              slotName: "checkItem",
            };
          });
          this.checkParams = res.data?.reduce((acc, cur) => {
            acc[cur] = { config1: 1, config2: 1, config3: undefined };
            return acc;
          }, {});
        }
      });
    },
    handleBack() {
      this.$emit("back");
    },
  },
};
</script>

<style lang="less" scoped>
.file-box {
  border: 1px solid #b8d3cb;
}
.file-card {
  /deep/ .el-card__body {
    padding: 0;
  }
  /deep/ .docx-wrapper {
    padding: 0;
  }
}
</style>
