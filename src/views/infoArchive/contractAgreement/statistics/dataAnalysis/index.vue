<!-- 数据分析 -->
<template>
  <div v-loading="loading">
    <div class="form-content">
      <!-- 筛选项-start -->
      <el-form
        :model="form"
        ref="form"
        label-width="100px"
        style="padding:0 12px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="属性">
              <el-select
                v-model="form.attribute"
                placeholder="属性"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="item in attributeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictLabel"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务类型">
              <el-select
                v-model="form.businessType"
                placeholder="业务类型"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="item in businessTypeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="end">
          <el-col :span="22">
            <el-form-item label="时间" prop="timeRange">
              <el-date-picker
                v-model="form.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="截止日期"
                value-format="yyyy-MM-dd"
                @change="queryData"
              ></el-date-picker>
              <el-radio-group v-model="form.timeRange" @change="queryData">
                <el-radio-button
                  v-for="(x, i) in timeArr"
                  :label="x.date"
                  :key="i"
                  >{{ x.title }}</el-radio-button
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button
              type="primary"
              @click="queryData"
              icon="el-icon-search"
              style="float: right;margin-right:4px"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <!-- 筛选项-end -->
    </div>
    <!-- 数据概览-start -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>数据概览</span>
      </div>
      <div class="statistics-box">
        <div
          v-for="(item, index) in statisticsList"
          :key="index"
          class="statistics-item"
        >
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
              <!-- <el-tooltip effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip> -->
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <!-- 数据概览-end -->
    <!-- 新签合同协议-start -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>新签合同协议</span>
        <!-- <el-tooltip effect="dark" placement="top">
          <div slot="content"></div>
          <i class="el-icon-question ml5"></i>
        </el-tooltip> -->
      </div>
      <LineChart
        :axisData="handleTend.time"
        :serieData="handleTend.tendencyArr"
        lineType="line"
        v-if="handleTend.time && handleTend.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 新签合同协议-end -->

    <div style="display: flex;">
      <!-- 业务类型占比-start -->
      <el-card style="margin-bottom: 10px;flex:1;margin-right: 10px;">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>业务类型占比</span>
        </div>
        <PieChartSolid
          :list="businessPieList"
          v-if="businessPieList && businessPieList.length > 0"
          labelFormatter="{b|{b}} {d|{d}%}"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 业务类型占比-end -->
      <!-- 预警占比-start -->
      <el-card style="margin-bottom: 10px;flex:1;">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>预警占比</span>
        </div>
        <PieChartSolid
          :list="warnPieList"
          :pieRadius="['47%', '70%']"
          v-if="warnPieList && warnPieList.length > 0"
          labelFormatter="{b|{b}} {d|{d}%}"
        ></PieChartSolid>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 预警占比-end -->
    </div>
  </div>
</template>

<script>
import LineChart from "@/components/Echarts/LineChart.vue";
import moment from "moment";
import PieChartSolid from "@/components/Echarts/pieChartSolid.vue";
import api from "@/api/infoArchive/contractAgreement/statistics.js";
import agreeApi from "@/api/infoArchive/contractAgreement/agreement.js";

export default {
  components: { LineChart, PieChartSolid },
  data() {
    return {
      statisticObj: {},
      form: {
        attribute: "",
        businessType: "",
        timeRange: [],
      },
      businessTypeOptions: [],
      attributeOptions: [],
      loading: false,
      handleTend: { time: [], tendencyArr: [] },
      businessPieList: [],
      warnPieList: [],
    };
  },
  computed: {
    timeArr() {
      return [
        {
          title: "今日",
          date: [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD")],
        },
        {
          title: "昨日",
          date: [
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
            moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近7日",
          date: [
            moment()
              .subtract(6, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近30日",
          date: [
            moment()
              .subtract(29, "days")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
        {
          title: "近3个月",
          date: [
            moment()
              .subtract(3, "months")
              .format("YYYY-MM-DD 00:00:00"),
            moment().format("YYYY-MM-DD 23:59:59"),
          ],
        },
        {
          title: "近半年",
          date: [
            moment()
              .subtract(6, "months")
              .format("YYYY-MM-DD"),
            moment().format("YYYY-MM-DD"),
          ],
        },
      ];
    },
    statisticsList() {
      return [
        {
          title: "累计合同协议",
          unit: "个",
          value: this.statisticObj?.agreementCount
            ? +this.statisticObj?.agreementCount
            : 0,
          tooltip: "所有状态的工单总数",
        },
        {
          title: "审批中",
          unit: "个",
          value: this.statisticObj?.approvalCount
            ? +this.statisticObj?.approvalCount
            : 0,
          tooltip: "处理中状态的工单总数",
        },
        {
          title: "生效中",
          unit: "个",
          value: this.statisticObj?.effectiveCount
            ? +this.statisticObj?.effectiveCount
            : 0,
          tooltip: "已完成状态的工单总数",
        },
        {
          title: "无效",
          unit: "个",
          value: this.statisticObj?.invalidCount
            ? +this.statisticObj?.invalidCount
            : 0,
          tooltip: "已完结状态的工单总数",
        },
        {
          title: "即将到期",
          unit: "个",
          value: this.statisticObj?.expiringCount
            ? +this.statisticObj?.expiringCount
            : 0,
          tooltip: "已作废状态的工单总数",
        },
        {
          title: "已到期",
          unit: "个",
          value: this.statisticObj?.expiredCount
            ? +this.statisticObj?.expiredCount
            : 0,
          tooltip: "草稿状态的工单总数",
        },
      ];
    },
  },
  async created() {
    this.getBusinessOptions();

    this.getDicts("contract_attribute").then((response) => {
      this.attributeOptions = response.data;
    });
    this.form.timeRange = [
      moment()
        .subtract(6, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
    this.queryData();
  },
  methods: {
    //获取业务类型
    getBusinessOptions() {
      agreeApi.queryBusinessType({ name: "" }).then((res) => {
        this.businessTypeOptions = res.data?.map((x) => {
          return { dictLabel: x, dictValue: x };
        });
      });
    },
    queryData() {
      let params = {
        ...this.form,
      };
      if (this.form.timeRange?.length > 0) {
        params["startSubmitTime"] = moment(this.form.timeRange[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        params["endSubmitTime"] = moment(this.form.timeRange[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      }
      this.loading = true;
      //数据概览
      api
        .queryStatisticData(params)
        .then((res) => {
          this.loading = false;
          if (res?.code == "10000") {
            this.statisticObj = res.data;
            console.log("statisticObj", this.statisticObj);
            this.handleTend = {
              time: res.data?.dailyAgreementVOList?.map((item) => item.day),
              tendencyArr: [
                {
                  name: "每日新增",
                  data: res.data?.dailyAgreementVOList?.map(
                    (item) => item.count
                  ),
                },
              ],
            };
            this.businessPieList = res.data?.businessTypeVOList?.map((x) => {
              return { value: x.percentage, name: x.businessType };
            });
            this.businessPieList.push({
              value: res.data?.otherTypePercentage,
              name: "其他",
            });
            this.warnPieList = [
              {
                value: res.data?.warningPercentage,
                name: "已预警",
              },
              {
                value: res.data?.unWarningPercentage,
                name: "未预警",
              },
            ];
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.form-content {
  padding: 0 20px;
  margin: 18px 0;
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto;
  grid-row-gap: 32px;
  grid-column-gap: 32px;
  .statistics-item {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
/deep/ .el-statistic {
  margin: 20px 0;
  display: flex;
  flex-direction: column-reverse;
  .head {
    color: #469a7d;
    font-size: 14px;
    margin-bottom: 0;
  }
  .con {
    color: #469a7d;
    margin-bottom: 12px;
    .number {
      font-size: 24px;
      font-weight: 500;
    }
  }
}
</style>
