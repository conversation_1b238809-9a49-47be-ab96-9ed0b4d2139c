<!-- 监控预警 -->
<template>
  <div class="app-container">
    <div class="content" v-loading="loading">
      <!-- 未来5个月即将到期合同协议预警-S -->
      <el-card style="flex:1.5" class="mr10">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>未来5个月即将到期合同协议预警</span>
        </div>
        <LineChart
          :axisData="handleTend.time"
          :serieData="handleTend.tendencyArr"
          lineType="bar"
          v-if="handleTend.time && handleTend.time.length > 0"
          :lineColor="['#459a7e']"
          height="200px"
        ></LineChart>
        <el-empty v-else></el-empty>
      </el-card>
      <!-- 未来5个月即将到期合同协议预警-E -->
      <!-- 预警数据概览-S -->
      <el-card style="flex:1">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>预警数据概览</span>
        </div>
        <div class="statistics-box">
          <div
            v-for="(item, index) in statisticsList"
            :key="index"
            class="statistics-item"
          >
            <el-statistic
              group-separator=","
              :precision="0"
              :value="item.value"
            >
              <template slot="suffix">
                <span style="font-size:12px">{{ item.unit }}</span>
              </template>
              <template slot="title">
                {{ item.title }}
                <!-- <el-tooltip effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip> -->
              </template>
            </el-statistic>
          </div>
        </div>
      </el-card>
      <!-- 预警数据概览-E -->
    </div>
    <!-- 即将到期合同协议-S -->
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>即将到期合同协议</span>
      </div>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData1"
        :currentPage.sync="page1.pageNum"
        :pageSize.sync="page1.pageSize"
        :total.sync="page1.total"
        @changePage="queryTable1"
        :loading="loading1"
        tableId="jjdqhtxy"
        row-id="stationId"
      >
        <template #xToolbarBtn>
          <el-button class="el-icon-download" @click="handleExport(1)"
            >导出</el-button
          >
        </template>
      </GridTable>
    </el-card>
    <!-- 即将到期合同协议-E -->
    <!-- 已到期合同协议-S -->
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>已到期合同协议</span>
      </div>
      <GridTable
        ref="gridTable2"
        :columns="columns"
        :tableData="tableData2"
        :currentPage.sync="page1.pageNum"
        :pageSize.sync="page1.pageSize"
        :total.sync="page1.total"
        @changePage="queryTable2"
        :loading="loading2"
        tableId="ydqhtxy"
        row-id="stationId"
      >
        <template #xToolbarBtn>
          <el-button class="el-icon-download" @click="handleExport(2)"
            >导出</el-button
          >
        </template>
      </GridTable>
    </el-card>
    <!-- 已到期合同协议-E -->
    <!-- 已变更合同协议-S -->
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>已变更合同协议</span>
      </div>
      <GridTable
        ref="gridTable3"
        :columns="columns3"
        :tableData="tableData3"
        :currentPage.sync="page3.pageNum"
        :pageSize.sync="page3.pageSize"
        :total.sync="page3.total"
        @changePage="queryTable3"
        :loading="loading3"
        tableId="ybghtxy"
        row-id="stationId"
      >
        <template #xToolbarBtn>
          <el-button class="el-icon-download" @click="handleExport(3)"
            >导出</el-button
          >
        </template>
      </GridTable>
    </el-card>
    <!-- 已变更合同协议-E -->
  </div>
</template>

<script>
import LineChart from "@/components/Echarts/LineChart.vue";
import GridTable from "@/components/GridTable/index.vue";
import exportMixin from "@/mixin/export.js";

import api from "@/api/infoArchive/contractAgreement/statistics.js";
export default {
  components: { LineChart, GridTable },
  mixins: [exportMixin],
  data() {
    return {
      loading: false,
      handleTend: { time: [], tendencyArr: [] },
      statisticObj: {},
      page1: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      loading1: false,
      tableData1: [],
      columns: [
        {
          field: "contractName",
          title: "合同名称",
          customWidth: 120,
        },
        {
          field: "contractNo",
          title: "合同编码",
          customWidth: 120,
        },
        {
          field: "businessType",
          title: "业务类型",
          customWidth: 120,
        },
        {
          field: "partA",
          title: "甲方",
          customWidth: 120,
        },
        {
          field: "expireTime",
          title: "到期时间",
          customWidth: 120,
        },
        {
          field: "omApplyNo",
          title: "运管申请单号",
          customWidth: 120,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleJump({ omApplyNo: row.omApplyNo });
                    },
                  }}
                >
                  {row.omApplyNo}
                </el-link>
              );
            },
          },
        },
        {
          field: "saleManager",
          title: "销售经理",
          customWidth: 120,
        },
        {
          field: "submitUser",
          title: "提交人",
          customWidth: 120,
        },
      ],
      page2: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      loading2: false,
      tableData2: [],
      columns3: [
        {
          field: "contractName",
          title: "合同名称",
          customWidth: 120,
        },
        {
          field: "contractNo",
          title: "合同编码",
          customWidth: 120,
        },
        {
          field: "changeApplyNo",
          title: "变更申请单号",
          customWidth: 120,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleJump({
                        changeApplyNo: row.changeApplyNo,
                      });
                    },
                  }}
                >
                  {row.changeApplyNo}
                </el-link>
              );
            },
          },
        },
        {
          field: "omApplyNo",
          title: "运管申请单号",
          customWidth: 120,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleJump({ omApplyNo: row.omApplyNo });
                    },
                  }}
                >
                  {row.omApplyNo}
                </el-link>
              );
            },
          },
        },
        {
          field: "businessType",
          title: "合同协议类型",
          customWidth: 120,
        },
        {
          field: "partA",
          title: "甲方",
          customWidth: 120,
        },
        {
          field: "updateTime",
          title: "变更时间",
          customWidth: 120,
        },
        {
          field: "salesManager",
          title: "销售经理",
          customWidth: 120,
        },
        {
          field: "submitUser",
          title: "提交人",
          customWidth: 150,
        },
      ],
      page3: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      loading3: false,
      tableData3: [],
    };
  },
  computed: {
    statisticsList() {
      return [
        {
          title: "即将到期",
          value: this.statisticObj?.expireCount
            ? +this.statisticObj?.expireCount
            : 0,
        },
        {
          title: "已到期",
          value: this.statisticObj?.expiredCount
            ? +this.statisticObj?.expiredCount
            : 0,
        },
        {
          title: "变更",
          value: this.statisticObj?.changeCount
            ? +this.statisticObj?.changeCount
            : 0,
        },
      ];
    },
  },
  created() {
    this.queryData();
    this.queryTable1();
    this.queryTable2();
    this.queryTable3();
  },
  methods: {
    //即将到期
    queryTable1() {
      this.loading1 = true;
      const params = {
        pageNum: this.page1.pageNum,
        pageSize: this.page1.pageSize,
        queryType: 1,
      };
      api.queryWarnTable(params).then((res) => {
        this.loading1 = false;
        this.tableData1 = res?.data;
        this.page1.total = res?.total;
      });
    },
    //已到期
    queryTable2() {
      this.loading2 = true;
      const params = {
        pageNum: this.page2.pageNum,
        pageSize: this.page2.pageSize,
        queryType: 2,
      };
      api.queryWarnTable(params).then((res) => {
        this.loading2 = false;
        this.tableData2 = res?.data;
        this.page2.total = res?.total;
      });
    },
    //已变更
    queryTable3() {
      this.loading3 = true;
      const params = {
        pageNum: this.page3.pageNum,
        pageSize: this.page3.pageSize,
        queryType: 3,
      };
      api.queryChangedTable(params).then((res) => {
        this.loading3 = false;
        this.tableData3 = res?.data;
        this.page3.total = res?.total;
      });
    },
    queryData() {
      this.loading = true;
      //数据概览
      api.queryWarnData({}).then((res) => {
        this.loading = false;
        this.statisticObj = res.data;
        this.handleTend = {
          time: res.data?.recentMonthWarningVOS?.map((x) => x.monthTime),
          tendencyArr: [
            {
              name: "到期个数",
              data: res.data?.recentMonthWarningVOS?.map((x) => x.count),
              barWidth: "50px",
            },
          ],
        };
      });
    },
    handleExport(flag = 1) {
      const params = { queryType: flag };
      this.handleCommonExport(
        flag == 3 ? api.exportChanged : api.export,
        params
      );
    },
    handleJump(params) {
      this.$router.push({
        name: "agreementList",
        params: params,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto;
  align-items: center;
  height: 200px;
  //   grid-row-gap: 32px;
  //   grid-column-gap: 32px;
  .statistics-item {
    // border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 6px;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
}
/deep/ .el-statistic {
  margin: 20px 0;
  display: flex;
  flex-direction: column-reverse;
  .head {
    color: #9fb4ae;
    font-size: 14px;
    margin-bottom: 0;
  }
  .con {
    color: #101010;
    margin-bottom: 16px;
    .number {
      font-size: 24px;
      //   font-weight: 500;
    }
  }
}
</style>
