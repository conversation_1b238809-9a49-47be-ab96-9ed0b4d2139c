<template>
  <div class="card-container">
    <div class="page-header">
      <el-card class="page-header-left">
        <div class="page-header-left-top">
          <h3>{{ baseInfo.contractName }}</h3>
          <div style="display: flex;">
            <span>{{ baseInfo.omApplyNo }}</span
            ><i
              class="el-icon-document-copy pointer-icon"
              @click="copyToClipboard(baseInfo.omApplyNo)"
            ></i>
          </div>
          <el-tag
            style="margin-left: 10px;font-size: 14px"
            :type="getTagType(baseInfo.applyStatus)"
            effect="plain"
            size="medium"
            >{{ baseInfo.status }}</el-tag
          >
        </div>
        <div class="page-header-left-bottom">
          <div style="flex:1" class="mr10">
            <div class="text-title">甲方</div>
            <div class="text-content">{{ baseInfo.partA }}</div>
          </div>
          <div style="flex:1" class="mr10">
            <div class="text-title">乙方</div>
            <div class="text-content">{{ baseInfo.partB }}</div>
          </div>
          <div style="flex:1">
            <div class="text-title">有效期</div>
            <div class="text-content">
              {{ baseInfo.effectiveTime }}~{{ baseInfo.expireTime }}
            </div>
          </div>
        </div>
        <div
          class="page-header-left-bottom"
          v-if="baseInfo.partC || baseInfo.partD"
        >
          <div style="flex:1" class="mr10" v-if="baseInfo.partC">
            <div class="text-title">丙方</div>
            <div class="text-content">{{ baseInfo.partC }}</div>
          </div>
          <div style="flex:1" class="mr10" v-if="baseInfo.partD">
            <div class="text-title">丁方</div>
            <div class="text-content">{{ baseInfo.partD }}</div>
          </div>
          <div style="flex:1" class="mr10" v-if="baseInfo.partE">
            <div class="text-title">戊方</div>
            <div class="text-content">{{ baseInfo.partE }}</div>
          </div>
          <div style="flex:1"></div>
        </div>
      </el-card>
      <el-card class="page-header-right">
        <img src="@/assets/image/img_risk.png" />
        <div>
          <div class="right-text">
            发现<span class="right-text-red">{{ baseInfo.alarmCount }}</span>个风险预警
          </div>
          <div>当前合同存在监控预警，请及时关注！</div>
        </div>
      </el-card>
    </div>
    <el-card class="mt20">
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="基本信息" name="base">
          <BaseInfo :contractId="contractId" ref="base"></BaseInfo>
        </el-tab-pane>
        <el-tab-pane label="预警信息" name="warn">
          <WarnInfo :contractId="contractId" ref="warn"></WarnInfo>
        </el-tab-pane>
        <el-tab-pane label="审批信息" name="check">
          <CheckInfo :contractId="contractId" ref="check"></CheckInfo>
        </el-tab-pane>
        <el-tab-pane label="关联项目" name="project">
          <AssociateProject
            :contractId="contractId"
            ref="project"
          ></AssociateProject>
        </el-tab-pane>
        <el-tab-pane label="关联合同" name="contract">
          <Contract :contractId="contractId" ref="contract"></Contract>
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="log">
          <Log :contractId="contractId" ref="log"></Log>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { copyToClipboard } from "@/utils/index.js";
import BaseInfo from "./components/baseInfo.vue";
import WarnInfo from "./components/warnInfo.vue";
import CheckInfo from "./components/checkInfo.vue";
import AssociateProject from "./components/associateProject.vue";
import Contract from "./components/contract.vue";
import Log from "./components/log.vue";
import api from "@/api/infoArchive/contractAgreement/agreement.js";

export default {
  components: {
    BaseInfo,
    WarnInfo,
    CheckInfo,
    AssociateProject,
    Contract,
    Log,
  },
  data() {
    return {
      activeName: "base",
      contractId: "",
      baseInfo: {},
    };
  },
  created() {
    this.contractId = this.$route.query.contractId;
    this.loadData();
  },
  mounted() {
    this.handleTabClick();
  },
  methods: {
    loadData() {
      api.baseInfoDetail({ contractId: this.contractId }).then((res) => {
        if (res?.code === "10000") {
          this.baseInfo = res.data;
        }
      });
    },
    copyToClipboard,
    getTagType(projectType) {
      const arr = [
        { type: "success", status: "生效中" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
        { type: "warning", status: "增柜" },
        { type: "danger", status: "撤柜" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
    handleTabClick() {
      console.log(this.activeName, this.$refs, this.$refs[this.activeName]);
      this.$refs[this.activeName]?.getDetail();
    },
  },
};
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  &-left {
    flex: 1.5;
    margin-right: 20px;
    &-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-bottom {
      margin-top: 24px;
      display: flex;
      .text-title {
        font-size: 14px;
        color: rgba(154, 180, 174, 1);
      }
      .text-content {
        margin-top: 18px;
        font-size: 16px;
        color: rgba(16, 16, 16, 1);
      }
    }
  }
  &-right {
    flex: 1;
    /deep/ .el-card__body {
      display: flex;
      align-items: center;
      img {
        width: 200px;
        height: 180px;
        margin-right: 20px;
      }
      .right-text {
        margin-bottom: 30px;
      }
      .right-text-red {
        color: red;
        margin: 0 20px;
        font-size: 20px;
      }
    }
  }
}
</style>
