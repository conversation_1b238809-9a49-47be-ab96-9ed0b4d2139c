<!-- 协议档案 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <!-- <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['contractTest:template:add']"
          >新增</el-button
        >
      </template> -->
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['agreement:list:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template slot="stationName">
        <el-select
          v-model="params.stationName"
          placeholder="请选择或搜索站点名称"
          filterable
          remote
          style="width: 100%"
          clearable
          v-el-select-loadmore="getStationList"
          :remote-method="remoteStationMethod"
          @clear="remoteStationMethod('')"
        >
          <el-option
            v-for="(item, index) in stationOptions"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
      <template slot="contractParty">
        <el-select
          v-model="params.contractParty"
          placeholder="请选择或搜索协议方"
          filterable
          remote
          style="width: 100%"
          clearable
          v-el-select-loadmore="getContractPartyList"
          :remote-method="remoteContractPartyMethod"
          @clear="remoteContractPartyMethod('')"
        >
          <el-option
            v-for="(item, index) in contractPartyOptions"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline :list="recordList"></Timeline>
      </template>
      <!-- 下载附件 -->
      <template slot="downloadFile" slot-scope="{ row, operationType }">
        <vxe-grid
          :columns="downloadColumns"
          :data="row.fileList"
          align="center"
          resizable
          ref="downloadTable"
        ></vxe-grid>
      </template>
      <!-- 查看附件 -->
      <template slot="fileList" slot-scope="{ row, operationType }">
        <vxe-table :data="row.fileList" align="center" ref="fileTable">
          <vxe-column field="fileType" title="文件类型"></vxe-column>
          <vxe-column field="fileName" title="文件名">
            <template #default="{ row, $rowIndex }">
              <el-link @click="handlePreview($rowIndex)">{{
                row.fileName
              }}</el-link>
            </template>
          </vxe-column>
          <vxe-column title="操作">
            <template #default="{ row, $rowIndex }">
              <el-link @click="handleDownLoad(row)">下载</el-link>
            </template>
          </vxe-column>
        </vxe-table>
        <PreviewFiles
          :initial-index="previewIndex"
          v-if="showViewer"
          :on-close="
            () => {
              showViewer = false;
            }
          "
          :url-list="row.fileList"
          :fileOptions="{ url: 'storePath', name: 'fileName' }"
        />
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
        <div v-else-if="crudOperationType === 'downloadFile'">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="submitDownloadList" type="primary">下载</el-button>
        </div>
      </template>
    </BuseCrud>
    <el-drawer title="操作日志" :visible.sync="detailDrawerVisible" size="60%">
      <div slot="title" style="display: flex;">
        <h3 class="mr10">合同概要</h3>
        <el-button type="text" @click="handleDetail({ contractId: contractId })"
          >查看更多>
        </el-button>
      </div>
      <BaseInfo :contractId="contractId" ref="baseInfoRef"></BaseInfo>
    </el-drawer>
    <el-drawer
      title="运营管理系统审批进度"
      :visible.sync="processVisible"
      @close="processVisible = false"
      size="70%"
    >
      <div class="info-title">
        <div>申请单号：{{ omApplyNo }}</div>
        <el-tag class="ml10">{{ applyStatus }}</el-tag>
      </div>
      <Timeline
        :list="processList"
        operateTypeTitle="itemCodeName"
        operatorNameTitle="operateEmpName"
        createTimeTitle="operateTime"
        operateDetailTitle="operateRemark"
      ></Timeline>
    </el-drawer>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/infoArchive/contractAgreement/agreement.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import { fileDownLoad } from "@/utils/downLoad.js";
import { downLoadUrl2Blob } from "@/api/common.js";
import BaseInfo from "./components/baseInfo.vue";
export default {
  name: "agreementList",
  components: { Timeline, PreviewFiles, BaseInfo },
  mixins: [exportMixin],
  data() {
    return {
      contractPartyOptions: [],
      currentContractPartyPage: 1,
      contractPartyText: "",
      contractPartyTotal: 0,
      isContractPartyLoading: false,
      stationOptions: [],
      currentStationPage: 1,
      stationText: "",
      stationTotal: 0,
      isStationLoading: false,
      applyStatus: "",
      processList: [],
      processVisible: false,
      omApplyNo: "",
      contractId: "",
      detailDrawerVisible: false,
      showViewer: false,
      previewIndex: 0,
      fileColumns: [
        { title: "文件类型", field: "fileType" },
        { title: "文件名称", field: "fileName" },
        { title: "操作", slot: { default: "operation" } },
      ],
      downloadColumns: [
        { type: "checkbox", width: 60 },
        { title: "请选择文件", field: "fileName" },
      ],
      downloadTableData: [],
      operationType: "remark",
      recordList: [],
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "contractName",
          title: "合同协议名称",
          width: 200,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleDetail(row);
                    },
                  }}
                >
                  {row.contractName}
                </el-link>
              );
            },
          },
        },
        {
          field: "attributeName",
          title: "属性",
          width: 150,
        },
        {
          field: "businessType",
          title: "业务类型",
          width: 150,
        },
        {
          field: "stationName",
          title: "场站名称",
          width: 150,
        },
        {
          field: "contractParty",
          title: "合同协议方",
          width: 200,
        },
        {
          field: "effectiveTime",
          title: "生效时间",
          width: 150,
        },
        {
          field: "expireTime",
          title: "失效时间",
          width: 150,
        },
        {
          field: "omApplyNo",
          title: "运管申请单号",
          width: 180,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.handleProcess(row);
                    },
                  }}
                >
                  {row.omApplyNo}
                </el-link>
              );
            },
          },
        },
        {
          field: "applyStatus",
          title: "审批状态",
          width: 150,
        },
        {
          field: "status",
          title: "合同协议状态",
          width: 150,
          slots: {
            default: ({ row }) => {
              return [
                <el-tag
                  style={{
                    color: this.getTagType(row.status).fontColor,
                    borderColor: this.getTagType(row.status).fontColor,
                  }}
                  type={this.getTagType(row.status).type}
                  color={this.getTagType(row.status).color}
                  size="medium"
                >
                  {row.status}
                </el-tag>,
              ];
            },
          },
        },
        {
          field: "submitUser",
          title: "提交人",
          width: 150,
          //   width: 250,
        },
        {
          field: "submitTime",
          title: "提交时间",
          width: 150,
        },
        {
          field: "remark",
          title: "备注",
          width: 150,
        },
        {
          field: "changeApplyNo",
          title: "变更申请单号",
          width: 180,
          slots: {
            default: ({ row }) => {
              return row.changeApplyNo?.split("，")?.map((x) => {
                return (
                  <div>
                    <el-link
                      on={{
                        click: () => {
                          return this.loadData({ changeApplyNo: x });
                        },
                      }}
                    >
                      {x}
                    </el-link>
                  </div>
                );
              });
            },
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
      agreementAttrsOptions: [],
      businessAttrsOptions: [],
      agreementSideOptions: [],
      checkStatusOptions: [],
      agreementStatusOptions: [
        { dictValue: "审批中", dictLabel: "审批中" },
        { dictValue: "驳回", dictLabel: "驳回" },
        { dictValue: "生效中", dictLabel: "生效中" },
        { dictValue: "无效", dictLabel: "无效" },
        { dictValue: "即将到期", dictLabel: "即将到期" },
        { dictValue: "已到期", dictLabel: "已到期" },
        { dictValue: "草稿", dictLabel: "草稿" },
      ],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "contractName",
            element: "el-input",
            title: "协议名称",
          },
          {
            field: "attribute",
            element: "el-select",
            title: "协议属性",
            props: {
              options: this.agreementAttrsOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
          {
            field: "businessType",
            element: "el-autocomplete",
            title: "业务类型",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryBusinessType");
              },
            },
          },
          {
            field: "stationName",
            element: "slot",
            slotName: "stationName",
            title: "场站名称",
          },
          {
            field: "validTime",
            title: "生效时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "contractParty",
            title: "协议方",
            element: "slot",
            slotName: "contractParty",
          },
          {
            field: "omApplyNo",
            element: "el-input",
            title: "审批单号",
          },
          {
            field: "applyStatus",
            element: "el-autocomplete",
            title: "审批状态",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "queryApplyStatus");
              },
            },
          },
          {
            field: "status",
            element: "el-select",
            title: "协议状态",
            props: {
              options: this.agreementStatusOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
            },
          },
          {
            field: "invalidTime",
            title: "失效时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "changeApplyNo",
            element: "el-input",
            title: "变更单号",
          },
          {
            field: "submitTime",
            title: "提交时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      const form = {
        //备注
        remark: [
          {
            field: "remark",
            element: "el-input",
            title: "备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 1000,
              showWordLimit: true,
              placeholder: "请输入备注描述，1000个字符以内",
            },
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增紧急程度",
        editBtn: false,
        editTitle: "编辑紧急程度",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        customOperationTypes: [
          {
            title: "预览",
            typeName: "preview",
            showForm: false,
            event: (row) => {
              return this.handleDetailPreview(row);
            },
            condition: (row) => {
              return checkPermission(["agreement:list:preview"]);
            },
          },
          // {
          //   title: "附件",
          //   modalTitle: "查看附件",
          //   typeName: "fileList",
          //   slotName: "fileList",
          //   showForm: false,
          //   event: (row) => {
          //     return this.handleFileList(row);
          //   },
          //   condition: (row) => {
          //     return checkPermission(["agreement:list:file"]);
          //   },
          // },
          //  {
          //    title: "下载",
          //    modalTitle: "请选择要下载的附件",
          //    typeName: "downloadFile",
          //    slotName: "downloadFile",
          //    showForm: false,
          //    event: (row) => {
          //      return this.handleDownloadFile(row);
          //    },
          //    condition: (row) => {
          //      return checkPermission(["agreement:list:download"]);
          //    },
          //  },
          {
            title: "备注",
            modalTitle: "添加备注",
            typeName: "remark",
            event: (row) => {
              return this.handleRemark(row);
            },
            condition: (row) => {
              return checkPermission(["agreement:list:remark"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  activated() {
    this.params = initParams(this.filterOptions.config);
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    this.getDicts("contract_attribute").then((response) => {
      this.agreementAttrsOptions = response.data;
    });
    this.getStationList();
    this.getContractPartyList();
    this.loadData();
  },

  methods: {
    getTagType(status) {
      const arr = [
        { type: "", status: "审批中", color: "#ebf3fe", fontColor: "#5292f7" },
        { type: "danger", status: "驳回" },
        { type: "success", status: "生效中" },
        { type: "info", status: "无效" },
        { type: "warning", status: "即将到期" },
        { type: "danger", status: "已到期" },
        { type: "", status: "草稿" },
      ];
      return arr.find((x) => x.status == status) || {};
    },
    async getContractPartyList() {
      const page =
        this.contractPartyTotal == 0
          ? 1
          : Math.ceil(this.contractPartyTotal / 10);
      if (this.isContractPartyLoading || this.currentContractPartyPage > page) {
        return;
      }
      this.isContractPartyLoading = true;
      const params = {
        pageNum: this.currentContractPartyPage,
        pageSize: 10,
        name: this.contractPartyText,
      };
      const res = await api.queryContractParty(params);
      this.contractPartyTotal = res?.total;
      const newOptions = res?.data;
      if (newOptions.length > 0) {
        this.contractPartyOptions = this.contractPartyOptions.concat(
          newOptions
        );
        this.currentContractPartyPage++;
      }
      this.isContractPartyLoading = false;
    },
    async remoteContractPartyMethod(val) {
      this.contractPartyOptions = [];
      this.currentContractPartyPage = 1;
      this.contractPartyText = val;
      await this.getContractPartyList();
    },
    async getStationList() {
      const page =
        this.stationTotal == 0 ? 1 : Math.ceil(this.stationTotal / 10);
      if (this.isStationLoading || this.currentStationPage > page) {
        return;
      }
      this.isStationLoading = true;
      const params = {
        pageNum: this.currentStationPage,
        pageSize: 10,
        name: this.stationText,
      };
      const res = await api.queryStationName(params);
      this.stationTotal = res?.total;
      const newOptions = res?.data;
      if (newOptions.length > 0) {
        this.stationOptions = this.stationOptions.concat(newOptions);
        this.currentStationPage++;
      }
      this.isStationLoading = false;
    },
    async remoteStationMethod(val) {
      this.stationOptions = [];
      this.currentStationPage = 1;
      this.stationText = val;
      await this.getStationList();
    },
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        name: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    //审批进度
    handleProcess(row) {
      this.processVisible = true;
      this.omApplyNo = row.omApplyNo;
      this.applyStatus = row.applyStatus;
      api.queryProcess({ applyNo: row.omApplyNo }).then((res) => {
        if (res?.code == 10000) {
          this.processList = res.data;
        }
      });
    },
    handleDetailPreview(row) {
      this.contractId = row.contractId;
      this.detailDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.baseInfoRef?.getDetail();
      });
    },
    handleDetail(row) {
      this.detailDrawerVisible = false;
      this.$router.push({
        path: "/infoArchive/contractAgreement/agreement/list/detail",
        query: { contractId: row.contractId },
      });
    },
    //附件中文件单个下载
    async handleDownLoad(row) {
      const res = await downLoadUrl2Blob({ fileUrl: row.storePath });
      if (res) {
        await fileDownLoad(res, row.fileName);
      }
    },
    //附件预览
    handlePreview(index) {
      this.showViewer = true;
      this.previewIndex = index;
    },
    //附件
    handleFileList(row) {
      this.$refs.crud.switchModalView(true, "fileList", {
        fileList: row.attachment || [
          {
            fileName: "123",
            id: 1,
            storePath:
              "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-08-27/1724728556797_下载.pdf",
          },
          {
            fileName: "456",
            id: 2,
            storePath:
              "https://shangfu-charge.oss-cn-hangzhou.aliyuncs.com//charging-maintenance-test/10000/DOC/2024-08-27/1724728556797_下载.pdf",
          },
        ],
      });
    },
    submitDownloadList() {
      const selected = this.$refs.downloadTable.getCheckboxRecords(true);
      console.log("[ selected ] >", selected);
    },
    //下载
    handleDownloadFile(row) {
      this.$refs.crud.switchModalView(true, "downloadFile", {
        fileList: row.fileList || [
          { fileName: "123", id: 1 },
          { fileName: "456", id: 2 },
        ],
      });
    },
    handleRemark(row) {
      this.operationType = "remark";
      this.$refs.crud.switchModalView(true, "remark", {
        ...initParams(this.modalConfig.formConfig),
        remark: row.remark,
        contractId: row.contractId,
      });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },

    async loadData(param = {}) {
      this.params = { ...this.params, ...param };
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.remoteStationMethod("");
      this.remoteContractPartyMethod("");
      this.loadData();
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "submitTime",
          title: "提交时间",
          startFieldName: "submitStartTime",
          endFieldName: "submitEndTime",
        },
        {
          field: "invalidTime",
          title: "失效时间",
          startFieldName: "expireStartTime",
          endFieldName: "expireEndTime",
        },
        {
          field: "validTime",
          title: "生效时间",
          startFieldName: "effectiveStartTime",
          endFieldName: "effectiveEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      const params = { ...formParams };
      // crudOperationType:remark
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          ...row,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {},
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...row,
        businessTypeIds: row.businessType?.split(",") || [],
      });
    },
    handleClose() {
      this.$refs.crud.switchModalView(false);
    },
  },
  directives: {
    "el-select-loadmore": {
      bind(el, binding) {
        // 下拉框下拉的框
        const SELECTWRAP_DOM = el.querySelector(
          ".el-select-dropdown .el-select-dropdown__wrap"
        );
        // 增加滚动监听，
        SELECTWRAP_DOM.addEventListener("scroll", function() {
          console.log(this.scrollHeight, this.scrollTop, this.clientHeight);
          const condition =
            this.scrollHeight - this.scrollTop - 2 <= this.clientHeight;
          // 当滚动条滚动到最底下的时候执行接口加载下一页
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
/deep/ .el-drawer__header {
  margin-bottom: 0px !important;
}
.info-title {
  display: flex;
  justify-content: space-between;
}
</style>
