export const OtherInfoDict = {
  采购框架协议申请: [
    {
      title: "所属公司",
      value: "companyName",
    },
    {
      title: "发票类型",
      value: "invoiceTypeName",
    },
    {
      title: "框架协议类型",
      value: "procotolTypeName",
    },
    {
      title: "销售中心",
      value: "",
    },
    {
      title: "利润中心",
      value: "",
    },
    {
      title: "签订时间",
      value: "signTime",
    },
    {
      title: "开始时间",
      value: "startTime",
    },
    {
      title: "结束时间",
      value: "endTime",
    },
    {
      title: "附件类型",
      value: "",
    },
    {
      title: "附件",
      value: "",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "框架协议文本",
      value: "",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "询价比材料",
      value: "",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
  ],
  固定资产采购合同申请: [
    {
      title: "采购申请数据类型",
      value: "",
    },
    {
      title: "固定资产所属公司",
      value: "companyName",
    },
    {
      title: "申请类型",
      value: "",
    },
    {
      title: "项目下固定资产",
      value: "isProjAssetName",
    },
    {
      title: "供应商编码",
      value: "factoryNo",
    },
    {
      title: "供应商名称",
      value: "factoryName",
    },
    {
      title: "供应商联系人",
      value: "",
    },
    {
      title: "供应商联系人电话",
      value: "",
    },
    {
      title: "邮寄联系人",
      value: "",
    },
    {
      title: "邮寄联系人电话",
      value: "",
    },
    {
      title: "外包合同邮寄地址",
      value: "",
    },
    {
      title: "本次合同申请总金额",
      value: "",
    },
    {
      title: "备注",
      value: "",
    },
    {
      title: "采购合同文本",
      value: "",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "采购合同扫描件",
      value: "",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "是否备货合同",
      value: "",
    },
    {
      title: "是否消耗备货设备",
      value: "",
    },
    {
      title: "交货日期",
      value: "",
    },
    {
      title: "合同金额",
      value: "",
    },
    {
      title: "回票金额",
      value: "",
    },
    {
      title: "金蝶已付金额",
      value: "",
    },
  ],
  付款申请记录: [
    {
      title: "部门",
      value: "",
    },
    {
      title: "是否固定资产采购付款",
      value: "",
    },
    {
      title: "申请类型",
      value: "",
    },
    {
      title: "预计付款日期",
      value: "paymentTime",
    },
    {
      title: "采购类型",
      value: "",
    },
    {
      title: "供应商",
      value: "factoryName",
    },
    {
      title: "开户银行",
      value: "",
    },
    {
      title: "银行账号",
      value: "",
    },
    {
      title: "付款类别",
      value: "",
    },
    {
      title: "付款阶段",
      value: "",
    },
    {
      title: "付款方式",
      value: "",
    },
    {
      title: "说明",
      value: "",
    },
    {
      title: "汇出户头",
      value: "",
    },
    {
      title: "支票号码",
      value: "",
    },
    {
      title: "备注",
      value: "",
    },
    {
      title: "是否取得发票",
      value: "",
    },
    {
      title: "货物是否入库",
      value: "",
    },
    {
      title: "本次付款性质",
      value: "",
    },
    {
      title: "本次实际付款金额",
      value: "payAmounts",
    },
    {
      title: "消息接收人",
      value: "stationName",
    },
    {
      title: "在途待支付款",
      value: "stationName",
    },
    {
      title: "已完成付款",
      value: "payAmountTotal",
    },
    {
      title: "付款比例",
      value: "stationName",
    },
    {
      title: "最后收款时间",
      value: "stationName",
    },
    {
      title: "累计收款金额",
      value: "stationName",
    },
    {
      title: "累计回票金额",
      value: "stationName",
    },
    {
      title: "收款比例",
      value: "stationName",
    },
    {
      title: "里程碑报告",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "采购合同扫描件",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "发票",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "其他",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "冲抵应收附件",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "电子回单",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
  ],
  采购框架协议变更申请记录: [
    // {
    //   title: "变更申请单号",
    //   value: "stationName",
    // },
    // {
    //   title: "申请人",
    //   value: "stationName",
    // },
    // {
    //   title: "申请时间",
    //   value: "stationName",
    // },
    // {
    //   title: "原申请单号",
    //   value: "stationName",
    // },
    {
      title: "采购框架协议编码",
      value: "stationName",
    },
    {
      title: "变更内容",
      value: "stationName",
    },
    {
      title: "采购框架协议名称",
      value: "stationName",
    },
    {
      title: "所属公司",
      value: "stationName",
    },
    {
      title: "发票类型",
      value: "stationName",
    },
    {
      title: "框架协议类型",
      value: "stationName",
    },
    {
      title: "销售中心",
      value: "stationName",
    },
    {
      title: "利润中心",
      value: "stationName",
    },
    {
      title: "签订时间",
      value: "stationName",
    },
    {
      title: "开始时间",
      value: "stationName",
    },
    {
      title: "结束时间",
      value: "stationName",
    },
    {
      title: "附件类型",
      value: "stationName",
    },
    {
      title: "附件",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "框架协议文本",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "询价比材料",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "供应商编码",
      value: "stationName",
    },
    {
      title: "供应商名称",
      value: "stationName",
    },
    {
      title: "供应商联系人",
      value: "stationName",
    },
    {
      title: "供应商联系人电话",
      value: "stationName",
    },
    {
      title: "邮寄联系人",
      value: "stationName",
    },
    {
      title: "邮寄联系人电话",
      value: "stationName",
    },
    {
      title: "框架协议邮寄地址",
      value: "stationName",
    },
    {
      title: "备注",
      value: "stationName",
    },
  ],
  "投建协议申请记录-充电": [
    {
      title: "申请部门",
      value: "applyDept",
    },
    {
      title: "内部渠道公司",
      value: "company",
    },
    {
      title: "合作部门",
      value: "cooperationDept",
    },
    {
      title: "产权方/产权代理方公司名称",
      value: "",
    },
    {
      title: "项目名称",
      value: "projName",
    },
    {
      title: "归属公司",
      value: "",
    },
    {
      title: "承担成本中心",
      value: "",
    },
    {
      title: "利润中心",
      value: "",
    },
    {
      title: "商务经理",
      value: "businessManager",
    },
    {
      title: "踏勘审批单号",
      value: "businessId",
    },
    {
      title: "踏勘名称",
      value: "",
    },
    {
      title: "踏勘流程审批方式",
      value: "",
    },
    {
      title: "踏勘审批是否结束",
      value: "",
    },
    {
      title: "渠道信息",
      value: "agentName",
    },
    {
      title: "合同续签方式",
      value: "",
    },
    {
      title: "合同到期时间",
      value: "endTime",
    },
    {
      title: "投建踏勘审批结果",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "产权方产权证明/代理方授权证明",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "投建协议",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "工程报价单",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "投建协议扫描件",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "合同作废申请书",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "场地租赁费/分成比例",
      value: "rentalFee",
    },
  ],
  "投建协议申请记录-储能": [
    {
      title: "申请部门",
      value: "applyDept",
    },
    {
      title: "投建模型",
      value: "cnConstructModelName",
    },
    {
      title: "内部渠道公司",
      value: "companyName",
    },
    {
      title: "产权方/产权代理方公司名称",
      value: "agentCompanyName",
    },
    {
      title: "项目名称",
      value: "projName",
    },
    {
      title: "归属公司",
      value: "belongCompany",
    },
    {
      title: "承担成本中心",
      value: "costCenterName",
    },
    {
      title: "商务经理",
      value: "businessManager",
    },
    {
      title: "投建踏勘审批结果",
      value: "",
    },
    {
      title: "产权方产权证明/代理方授权证明",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "投建清单",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
    {
      title: "工程报价单",
      value: "powerPointPhoto",
      formatter: (val) => {
        return val ? JSON.parse(val) : [];
      },
      slotName: "links",
    },
  ],
  投建变更申请记录: [
    {
      title: "申请部门",
      value: "stationName",
    },
    {
      title: "内部渠道公司",
      value: "stationName",
    },
    {
      title: "合作部门",
      value: "stationName",
    },
    {
      title: "产权方/产权代理方公司名称",
      value: "stationName",
    },
    {
      title: "项目名称",
      value: "stationName",
    },
    {
      title: "归属公司",
      value: "stationName",
    },
    {
      title: "承担成本中心",
      value: "stationName",
    },
    {
      title: "利润中心",
      value: "stationName",
    },
    {
      title: "商务经理",
      value: "stationName",
    },
    {
      title: "踏勘审批单号",
      value: "stationName",
    },
    {
      title: "踏勘名称",
      value: "stationName",
    },
    {
      title: "踏勘流程审批方式",
      value: "stationName",
    },
    {
      title: "踏勘审批是否结束",
      value: "stationName",
    },
    {
      title: "渠道信息",
      value: "stationName",
    },
    {
      title: "合同续签方式",
      value: "stationName",
    },
    {
      title: "合同到期时间",
      value: "stationName",
    },
    {
      title: "是否上传附件",
      value: "stationName",
    },
    {
      title: "是否修改回报率",
      value: "stationName",
    },
  ],
  合同申请记录: [
    // {
    //   title: "合同申请编号",
    //   value: "conNo",
    // },
    {
      title: "pipeline来源",
      value: "",
    },
    {
      title: "pipeline",
      value: "",
    },
    {
      title: "项目经理",
      value: "",
    },
    // {
    //   title: "合同名称",
    //   value: "stationName",
    // },
    // {
    //   title: "合同编码",
    //   value: "stationName",
    // },
    // {
    //   title: "公司/乙方",
    //   value: "stationName",
    // },
    // {
    //   title: "客户/甲方",
    //   value: "stationName",
    // },
    {
      title: "甲方注册地",
      value: "firstPartyRegistration",
    },
    {
      title: "销售经理",
      value: "",
    },
    {
      title: "合同签订日期",
      value: "contractDate",
    },
    {
      title: "合同期限",
      value: "contractPeriod",
    },
    {
      title: "合同性质",
      value: "",
    },
    {
      title: "是否内部转包",
      value: "isIcb",
    },
    {
      title: "是否海外",
      value: "",
    },
    {
      title: "市场类型",
      value: "",
    },
    {
      title: "项目来源（按承接方式）",
      value: "",
    },
    {
      title: "合同条款外包限制",
      value: "",
    },
    {
      title: "客户负责人",
      value: "",
    },
    {
      title: "项目开始时间",
      value: "",
    },
    {
      title: "项目结束时间",
      value: "",
    },
    {
      title: "是否电子签章合同",
      value: "",
    },
    {
      title: "运维开始-结束时间",
      value: "",
    },
    {
      title: "行业分类",
      value: "",
    },
    {
      title: "甲方合同编码",
      value: "",
    },
    {
      title: "机会获取方",
      value: "",
    },
    {
      title: "合同签署方",
      value: "",
    },
    {
      title: "利润归属方",
      value: "",
    },
    {
      title: "业务线",
      value: "businessLine",
    },
    {
      title: "币种",
      value: "",
    },
    {
      title: "是否与PL预算申请并行",
      value: "",
    },
    {
      title: "是否中标合同",
      value: "",
    },
    {
      title: "是否维护储能场站信息",
      value: "",
    },
    {
      title: "是否标准合同",
      value: "",
    },
    {
      title: "客户联系人",
      value: "",
    },
    {
      title: "客户联系方式",
      value: "",
    },
    {
      title: "客户邮箱",
      value: "",
    },
    {
      title: "合作模式",
      value: "",
    },
    {
      title: "余额预警金额（万元）",
      value: "",
    },
    {
      title: "授信额度（万元）",
      value: "",
    },
    {
      title: "数据开放授权",
      value: "",
    },
    {
      title: "分润收费标准",
      value: "",
    },
    {
      title: "折扣力度",
      value: "",
    },
    {
      title: "活动开始时间",
      value: "",
    },
    {
      title: "活动截止时间",
      value: "",
    },
    {
      title: "站点",
      value: "",
    },
    {
      title: "附件",
      value: "",
    },
  ],
  合同变更申请记录: [
    {
      title: "合同申请编号",
      value: "stationName",
    },
    {
      title: "pipeline来源",
      value: "stationName",
    },
    {
      title: "pipeline",
      value: "stationName",
    },
    {
      title: "项目经理",
      value: "stationName",
    },
    {
      title: "合同名称",
      value: "stationName",
    },
    {
      title: "合同编码",
      value: "stationName",
    },
    {
      title: "公司/乙方",
      value: "stationName",
    },
    {
      title: "客户/甲方",
      value: "stationName",
    },
    {
      title: "甲方注册地",
      value: "stationName",
    },
    {
      title: "销售经理",
      value: "stationName",
    },
    {
      title: "合同签订日期",
      value: "stationName",
    },
    {
      title: "合同期限",
      value: "stationName",
    },
    {
      title: "合同性质",
      value: "stationName",
    },
    {
      title: "是否内部转包",
      value: "stationName",
    },
    {
      title: "是否海外",
      value: "stationName",
    },
    {
      title: "市场类型",
      value: "stationName",
    },
    {
      title: "项目来源（按承接方式）",
      value: "stationName",
    },
    {
      title: "合同条款外包限制",
      value: "stationName",
    },
    {
      title: "客户负责人",
      value: "stationName",
    },
    {
      title: "项目开始时间",
      value: "stationName",
    },
    {
      title: "项目结束时间",
      value: "stationName",
    },
    {
      title: "是否电子签章合同",
      value: "stationName",
    },
    {
      title: "运维开始-结束时间",
      value: "stationName",
    },
    {
      title: "行业分类",
      value: "stationName",
    },
    {
      title: "甲方合同编码",
      value: "stationName",
    },
    {
      title: "机会获取方",
      value: "stationName",
    },
    {
      title: "合同签署方",
      value: "stationName",
    },
    {
      title: "利润归属方",
      value: "stationName",
    },
    {
      title: "业务线",
      value: "stationName",
    },
    {
      title: "币种",
      value: "stationName",
    },
    {
      title: "是否与PL预算申请并行",
      value: "stationName",
    },
    {
      title: "是否中标合同",
      value: "stationName",
    },
    {
      title: "是否维护储能场站信息",
      value: "stationName",
    },
    {
      title: "是否标准合同",
      value: "stationName",
    },
    {
      title: "客户联系人",
      value: "stationName",
    },
    {
      title: "客户联系方式",
      value: "stationName",
    },
    {
      title: "客户邮箱",
      value: "stationName",
    },
    {
      title: "合作模式",
      value: "stationName",
    },
    {
      title: "余额预警金额（万元）",
      value: "stationName",
    },
    {
      title: "授信额度（万元）",
      value: "stationName",
    },
    {
      title: "数据开放授权",
      value: "stationName",
    },
    {
      title: "分润收费标准",
      value: "stationName",
    },
    {
      title: "折扣力度",
      value: "stationName",
    },
    {
      title: "活动开始时间",
      value: "stationName",
    },
    {
      title: "活动截止时间",
      value: "stationName",
    },
    {
      title: "站点",
      value: "stationName",
    },
    {
      title: "是否与原附件一致",
      value: "stationName",
    },
    {
      title: "附件",
      value: "stationName",
    },
    {
      title: "变更类型",
      value: "stationName",
    },
    {
      title: "变更原因",
      value: "stationName",
    },
    {
      title: "变更内容（简述）",
      value: "stationName",
    },
  ],
  合同终止申请记录: [
    {
      title: "合同终止申请编号",
      value: "omApplyNo",
    },
    {
      title: "合同申请编号",
      value: "mainContractNo",
    },
    {
      title: "合同编码",
      value: "contractNo",
    },
    {
      title: "合同名称",
      value: "contractName",
    },
    {
      title: "原合同金额",
      value: "totalAmountBefore",
    },
    {
      title: "剩余合同金额",
      value: "totalAmount",
    },
    {
      title: "项目经理",
      value: "projManagerName",
    },
    {
      title: "销售经理",
      value: "salesManager",
    },
    {
      title: "客户名称",
      value: "partA",
    },
    {
      title: "终止类型",
      value: "stopType",
    },
    {
      title: "终止审批状态",
      value: "stopApplyStatus",
    },
    {
      title: "合同签署方",
      value: "signingPartyName",
    },
    {
      title: "归档状态",
      value: "isArchived",
    },
    {
      title: "终止原因",
      value: "",
    },
    // {
    //   title: "合同终止申请编号",
    //   value: "stationName",
    // },
    // {
    //   title: "合同申请编号",
    //   value: "stationName",
    // },
    // {
    //   title: "是否已有pipeline",
    //   value: "stationName",
    // },
    // {
    //   title: "pipeline",
    //   value: "stationName",
    // },
    // {
    //   title: "合同名称",
    //   value: "stationName",
    // },
    // {
    //   title: "合同编码",
    //   value: "stationName",
    // },
    // {
    //   title: "项目经理",
    //   value: "stationName",
    // },
    // {
    //   title: "公司/乙方",
    //   value: "stationName",
    // },
    // {
    //   title: "客户/甲方",
    //   value: "stationName",
    // },
    // {
    //   title: "甲方注册地",
    //   value: "stationName",
    // },
    // {
    //   title: "销售经理",
    //   value: "stationName",
    // },
    // {
    //   title: "合同签订日期",
    //   value: "stationName",
    // },
    // {
    //   title: "合同期限",
    //   value: "stationName",
    // },
    // {
    //   title: "合同性质",
    //   value: "stationName",
    // },
    // {
    //   title: "是否内部转包",
    //   value: "stationName",
    // },
    // {
    //   title: "是否海外",
    //   value: "stationName",
    // },
    // {
    //   title: "市场类型",
    //   value: "stationName",
    // },
    // {
    //   title: "项目来源（按承接方式）",
    //   value: "stationName",
    // },
    // {
    //   title: "合同条款外包限制",
    //   value: "stationName",
    // },
    // {
    //   title: "项目开始时间",
    //   value: "stationName",
    // },
    // {
    //   title: "项目结束时间",
    //   value: "stationName",
    // },
    // {
    //   title: "是否电子签章合同",
    //   value: "stationName",
    // },
    // {
    //   title: "运维开始时间-运维结束时间",
    //   value: "stationName",
    // },
    // {
    //   title: "行业分类",
    //   value: "stationName",
    // },
    // {
    //   title: "甲方合同编码",
    //   value: "stationName",
    // },
    // {
    //   title: "机会获取方",
    //   value: "stationName",
    // },
    // {
    //   title: "合同签署方",
    //   value: "stationName",
    // },
    // {
    //   title: "利润归属方",
    //   value: "stationName",
    // },
    // {
    //   title: "业务线",
    //   value: "stationName",
    // },
    // {
    //   title: "币种",
    //   value: "stationName",
    // },
    // {
    //   title: "汇率",
    //   value: "stationName",
    // },
    // {
    //   title: "是否中标合同",
    //   value: "stationName",
    // },
    // {
    //   title: "中标时间",
    //   value: "stationName",
    // },
    // {
    //   title: "项目类型",
    //   value: "stationName",
    // },
    // {
    //   title: "终止类型",
    //   value: "stationName",
    // },
    // {
    //   title: "附件",
    //   value: "stationName",
    // },
    // {
    //   title: "终止原因",
    //   value: "stationName",
    // },
  ],
  协议申请记录: [
    {
      title: "我方/公司",
      value: "companyName",
    },
    {
      title: "销售经理",
      value: "salesManager",
    },
    {
      title: "操作人员",
      value: "protocolOperatorName",
    },
    {
      title: "利润中心",
      value: "profitCenterName",
    },
    {
      title: "业务线",
      value: "businessLine",
    },
    {
      title: "协议方数",
      value: "xyfCount",
    },
    {
      title: "客户名称",
      value: "customerName",
    },
    {
      title: "注册地址",
      value: "registrationAddress",
    },
    {
      title: "是否是ISV协议",
      value: "",
    },
    //  {
    //    title: "协议单号",
    //    value: "stationName",
    //  },
    //  {
    //    title: "协议编码",
    //    value: "stationName",
    //  },
    {
      title: "子公司协议编码",
      value: "protocolOtherCode",
    },
    {
      title: "协议类型",
      value: "protocolTypeName",
    },
    //  {
    //    title: "协议名称",
    //    value: "stationName",
    //  },
    //  {
    //    title: "主协议编码",
    //    value: "stationName",
    //  },
    //  {
    //    title: "业务类型",
    //    value: "stationName",
    //  },
    {
      title: "协议签订时间",
      value: "signTime",
    },
    //  {
    //    title: "协议开始时间",
    //    value: "stationName",
    //  },
    //  {
    //    title: "协议失效时间",
    //    value: "stationName",
    //  },
    {
      title: "协议签署目的",
      value: "signPurpose",
    },
    {
      title: "附件",
      value: "",
    },
    {
      title: "是否有区域排他性",
      value: "isAreaPt",
    },
    {
      title: "是否有行业排他性",
      value: "isIndustryPt",
    },
    {
      title: "是否涉及知识产权",
      value: "isIntellectProperty",
    },
    {
      title: "是否涉及违约处罚",
      value: "isDefyPunish",
    },
    {
      title: "是否我方资金投入",
      value: "isInvest",
    },
  ],
  协议变更申请记录: [
    {
      title: "主协议单号",
      value: "mainContractNo",
    },
    {
      title: "协议方数",
      value: "xyfCount",
    },
    {
      title: "客户名称",
      value: "customerName",
    },
    {
      title: "客户注册地",
      value: "registrationAddress",
    },
    {
      title: "我方/公司",
      value: "companyName",
    },
    {
      title: "利润中心",
      value: "profitCenterName",
    },
    {
      title: "销售经理",
      value: "salesManager",
    },
    {
      title: "协议签订时间",
      value: "signTime",
    },
    {
      title: "子公司协议编码",
      value: "protocolOtherCode",
    },
    // {
    //   title: "操作人员",
    //   value: "stationName",
    // },
    // {
    //   title: "利润中心",
    //   value: "stationName",
    // },
    // {
    //   title: "业务线",
    //   value: "stationName",
    // },
    // {
    //   title: "协议方数",
    //   value: "stationName",
    // },
    // {
    //   title: "客户名称",
    //   value: "stationName",
    // },
    // {
    //   title: "注册地址",
    //   value: "stationName",
    // },
    // {
    //   title: "是否是ISV协议",
    //   value: "stationName",
    // },
    // {
    //   title: "协议单号",
    //   value: "stationName",
    // },
    // {
    //   title: "协议编码",
    //   value: "stationName",
    // },
    // {
    //   title: "子公司协议编码",
    //   value: "stationName",
    // },
    // {
    //   title: "协议类型",
    //   value: "stationName",
    // },
    // {
    //   title: "协议名称",
    //   value: "stationName",
    // },
    // {
    //   title: "主协议编码",
    //   value: "stationName",
    // },
    // {
    //   title: "业务类型",
    //   value: "stationName",
    // },
    // {
    //   title: "协议签订时间",
    //   value: "stationName",
    // },
    // {
    //   title: "协议开始时间",
    //   value: "stationName",
    // },
    // {
    //   title: "协议失效时间",
    //   value: "stationName",
    // },
    // {
    //   title: "协议签署目的",
    //   value: "stationName",
    // },
    // {
    //   title: "附件",
    //   value: "stationName",
    // },
    // {
    //   title: "是否有区域排他性",
    //   value: "stationName",
    // },
    // {
    //   title: "是否有行业排他性",
    //   value: "stationName",
    // },
    // {
    //   title: "是否涉及知识产权",
    //   value: "stationName",
    // },
    // {
    //   title: "是否涉及违约处罚",
    //   value: "stationName",
    // },
    // {
    //   title: "是否我方资金投入",
    //   value: "stationName",
    // },
    {
      title: "变更原因",
      value: "",
    },
    {
      title: "变更内容（简述）",
      value: "",
    },
  ],
};
