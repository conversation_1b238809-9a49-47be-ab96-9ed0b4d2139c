<!-- 审批信息 -->
<template>
  <div>
    <div class="info-title">
      <div>申请单号：{{ baseInfo.omApplyNo }}</div>
      <el-tag class="ml10">{{ baseInfo.applyStatus }}</el-tag>
    </div>
    <Timeline
      :list="processList"
      operateTypeTitle="itemCodeName"
      operatorNameTitle="operateEmpName"
      createTimeTitle="operateTime"
      operateDetailTitle="operateRemark"
    ></Timeline>
  </div>
</template>

<script>
import Timeline from "@/components/Timeline/index.vue";
import api from "@/api/infoArchive/contractAgreement/agreement.js";

export default {
  components: { Timeline },
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      processVisible: false,
      processList: [],
      baseInfo: {},
    };
  },
  created() {
    // this.getDetail();
  },
  methods: {
    getDetail() {
      api.baseInfoDetail({ contractId: this.contractId }).then((res) => {
        if (res?.code === "10000") {
          this.baseInfo = res.data;
          api.queryProcess({ applyNo: this.baseInfo.omApplyNo }).then((res) => {
            if (res?.code == 10000) {
              this.processList = res.data;
            }
          });
        }
      });
    },
  },
};
</script>

<style>
.info-title {
  display: flex;
  align-items: center;
  margin: 20px 40px;
}
</style>
