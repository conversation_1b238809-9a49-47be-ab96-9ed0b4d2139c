<!-- 预警信息 -->
<template>
  <div>
    <GridTable
      :columns="columns"
      :tableData="tableData"
      :currentPage.sync="searchForm.pageNum"
      :pageSize.sync="searchForm.pageSize"
      :total.sync="total"
      @changePage="getList"
      :loading="loading"
      :tableId="tableId"
      row-id="orderId"
    >
      <template slot="xToolbarBtn">
        <div style="font-size: 16px;font-weight: 500;">
          该合同监控预警状态<el-switch
            v-model="monitorStatus"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange"
            style="margin-left: 5px;"
          ></el-switch>
        </div>
      </template>
    </GridTable>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";

import api from "@/api/infoArchive/contractAgreement/agreement.js";
export default {
  components: {
    GridTable,
  },
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      monitorStatus: "0",
      columns: [
        {
          field: "alarmContent",
          title: "预警内容",
          customWidth: "33%",
        },
        {
          field: "alarmTime",
          title: "预警时间",
          customWidth: "33%",
        },
        {
          field: "statusName",
          title: "当前状态",
          customWidth: "33%",
          slots: {
            default: ({ row }) => {
              return (
                <div
                  style={{ color: row.statusName === "未解除" ? "red" : "" }}
                >
                  {row.statusName}
                </div>
              );
            },
          },
        },
      ],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      total: 0,
      loading: false,
      tableId: "agreementWarnInfoList",
    };
  },
  created() {
    // this.getDetail();
  },
  methods: {
    handleStatusChange() {
      const text = this.monitorStatus == "0" ? "启用" : "停用";
      this.$confirm(`是否确认${text}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          api
            .changeMonitorStatus({
              monitorStatus: this.monitorStatus,
              contractId: this.contractId,
            })
            .then((res) => {
              if (res.code == "10000") {
                this.msgSuccess(text + "成功");
              } else {
                this.monitorStatus = this.monitorStatus == 1 ? "0" : "1";
              }
            })
            .catch(function() {
              this.monitorStatus = this.monitorStatus == 1 ? "0" : "1";
            });
        })
        .catch(() => {
          this.monitorStatus = this.monitorStatus == 1 ? "0" : "1";
        });
    },
    getDetail() {
      api.baseInfoDetail({ contractId: this.contractId }).then((res) => {
        if (res?.code === "10000") {
          this.monitorStatus = res.data?.monitorStatus;
        }
      });
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = { ...this.searchForm, contractId: this.contractId };
      api
        .queryWarnList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style></style>
