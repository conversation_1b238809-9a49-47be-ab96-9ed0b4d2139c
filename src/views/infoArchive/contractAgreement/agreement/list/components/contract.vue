<!-- 关联合同 -->
<template>
  <vxe-grid
    :columns="columns"
    :data="tableData"
    resizable
    align="center"
  ></vxe-grid>
</template>

<script>
import api from "@/api/infoArchive/contractAgreement/agreement.js";

export default {
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      columns: [
        {
          title: "合同名称",
          field: "projectName",
        },
        {
          title: "合同编码",
          field: "projectCode",
        },
        {
          title: "业务类型",
          field: "stationName",
        },
        {
          title: "合同协议有效期",
          field: "stationCode",
        },
        {
          title: "运管申请单号",
          field: "omApproveNo",
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.jumpToList(row),
                  }}
                >
                  {row.omApproveNo}
                </el-link>,
              ];
            },
          },
        },
        {
          title: "销售经理",
          field: "projectName",
        },
        {
          title: "提交人",
          field: "projectName",
        },
        {
          title: "提交时间",
          field: "projectName",
        },
        {
          title: "审批状态",
          field: "projectName",
        },
        {
          title: "合同协议状态",
          field: "projectName",
        },
      ],
      tableData: [],
    };
  },
  created() {
    // this.getDetail();
  },
  methods: {
    getDetail() {
      api.associateContract({ contractId: this.contractId }).then((res) => {
        if (res?.code == 10000) {
          this.tableData = res.data;
        }
      });
    },
    jumpToList(row) {
      this.$router.push({
        name: "agreementList",
        params: {
          omApproveNo: row.surveyCodeMain,
        },
      });
    },
  },
};
</script>

<style></style>
