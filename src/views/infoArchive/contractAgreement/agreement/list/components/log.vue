<!-- 操作日志 -->
<template>
  <el-card>
    <CommonTitle class="mb10" title="操作日志" />
    <Timeline
      :list="processList"
      operateTypeTitle="operatorTypeName"
      operatorNameTitle="operatorUserName"
      createTimeTitle="operatorTime"
      operateDetailTitle="remark"
    ></Timeline>
  </el-card>
</template>

<script>
import CommonTitle from "@/components/commonTitle";

import Timeline from "@/components/Timeline/index.vue";
import api from "@/api/infoArchive/contractAgreement/agreement.js";

export default {
  components: { Timeline, CommonTitle },
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      omApproveNo: "",
      omApproveStatusName: "",
      processList: [],
    };
  },
  created() {
    // this.getDetail();
  },
  methods: {
    getDetail() {
      api.queryLog({ contractId: this.contractId }).then((res) => {
        if (res?.code == 10000) {
          this.processList = res.data;
        }
      });
    },
  },
};
</script>

<style>
.info-title {
  display: flex;
}
</style>
