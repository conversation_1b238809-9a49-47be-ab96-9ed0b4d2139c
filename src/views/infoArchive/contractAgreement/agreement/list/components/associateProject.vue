<!-- 关联项目 -->
<template>
  <vxe-grid
    :columns="columns"
    :data="tableData"
    resizable
    align="center"
  ></vxe-grid>
</template>

<script>
import api from "@/api/infoArchive/contractAgreement/agreement.js";

export default {
  props: {
    contractId: { type: String, default: "" },
  },
  data() {
    return {
      allProjectStatusOptions: [],
      columns: [
        {
          title: "项目名称",
          field: "projectName",
        },
        {
          title: "项目编码",
          field: "projectCode",
        },
        {
          title: "场地名称",
          field: "stationName",
        },
        {
          title: "场地编码",
          field: "stationCode",
        },
        {
          title: "踏勘编码",
          field: "surveyCodeMain",
          slots: {
            default: ({ row }) => {
              return [
                <el-link
                  type="primary"
                  on={{
                    click: () => this.toBatchList(row),
                  }}
                >
                  {row.surveyCodeMain}
                </el-link>,
              ];
            },
          },
        },
        {
          title: "商务BD",
          field: "businessDevelopmentName",
        },
        {
          title: "项目创建时间",
          field: "createTime",
        },
        {
          title: "项目状态",
          field: "projectStatus",
          formatter: ({ cellValue }) => {
            return this.allProjectStatusOptions?.find(
              (x) => x.dictValue == cellValue
            )?.dictLabel;
          },
        },
      ],
      tableData: [],
    };
  },
  created() {
    this.getDicts("project_status").then((response) => {
      this.allProjectStatusOptions = response.data;
    });
    // this.getDetail();
  },
  methods: {
    getDetail() {
      api.baseInfoDetail({ contractId: this.contractId }).then((res) => {
        if (res?.code === "10000") {
          const { omApplyNo, contractTypeName } = res.data;
          api
            .associateProject({
              omApplyNo: omApplyNo,
              contractTypeName: contractTypeName,
            })
            .then((res) => {
              if (res?.code == 10000) {
                this.tableData = res.data;
              }
            });
        }
      });
    },
    toBatchList(row) {
      this.$router.push({
        name: "investBatchList",
        params: {
          surveyCodeMain: row.surveyCodeMain,
          surveyCodeSub: row.surveyCodeSub,
          businessType: row.businessType,
          tabActiveTab: row.businessType == "2" ? "storage" : "charge",
        },
      });
    },
  },
};
</script>

<style></style>
