<!-- 预警管理 -->
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['contract:notice:add']"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>

      <template #statusChange="{ row }"
        ><el-switch
          v-model="row.status"
          active-value="0"
          inactive-value="1"
          @change="handleStatusChange(row)"
          :disabled="!checkPermission(['contract:notice:status'])"
        >
        </el-switch
      ></template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          operateDetailTitle="remark"
          createTimeTitle="operatorTime"
        ></Timeline>
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/infoArchive/contractAgreement/notice.js";
import Timeline from "@/components/Timeline/index.vue";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import agreeApi from "@/api/infoArchive/contractAgreement/agreement.js";
import { listAllUser } from "@/api/common.js";
import { queryGroupList } from "@/api/ledger/setGroup.js";
export default {
  name: "contractNoticePage",
  components: { Timeline },
  mixins: [exportMixin],
  data() {
    return {
      recordList: [],
      businessTypeOptions: [],
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "businessType",
          title: "业务类型",
          width: 150,
        },
        {
          field: "noticeCondition",
          title: "通知触发条件",
          width: 150,
        },
        {
          field: "noticeTargetName",
          title: "通知对象",
          width: 150,
        },
        {
          field: "noticeWay",
          title: "通知方式",
          width: 150,
        },
        {
          field: "email",
          title: "邮箱地址",
          width: 150,
        },
        {
          field: "createByName",
          title: "创建人",
          width: 150,
          //   width: 250,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 150,
        },
        {
          field: "updateByName",
          title: "修改人",
          width: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
          width: 150,
        },
        {
          field: "status",
          title: "状态",
          width: 150,
          slots: {
            default: "statusChange",
          },
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptions: [],
      conditionOptions: [],
      noticeObject: 1,
      userOptions: [],
      groupOptions: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增",
        editBtn: checkPermission(["contract:notice:edit"]),
        editTitle: "编辑",
        delBtn: checkPermission(["contract:notice:delete"]),
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "businessType",
            title: "业务类型",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.businessTypeOptions,
              optionValue: "dictValue",
              optionLabel: "dictLabel",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择业务类型",
              },
            ],
          },
          {
            field: "noticeCondition",
            title: "通知触发条件",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.conditionOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择通知触发条件",
              },
            ],
            on: {
              change: (val) => {
                this.$refs.crud.setFormFields({
                  emailSubject: val,
                });
              },
            },
          },
          {
            field: "noticeObject",
            title: "通知对象",
            element: "el-radio-group",
            props: {
              options: [
                { value: 1, label: "人" },
                { value: 2, label: "组" },
              ],
            },
            rules: [
              {
                required: true,
                message: "请选择通知对象",
              },
            ],
            on: {
              change: (val) => {
                this.noticeObject = val;
              },
            },
            defaultValue: 1,
          },
          {
            field: "noticeUserIds",
            title: "通知人",
            element: "el-select",
            props: {
              options: this.userOptions,
              filterable: true,
              multiple: true,
            },
            rules: [
              {
                required: this.noticeObject == 1,
                message: "请选择通知人",
              },
            ],
            show: this.noticeObject == 1,
            defaultValue: [],
          },
          {
            field: "noticeGroupIds",
            title: "通知组",
            element: "el-select",
            props: {
              options: this.groupOptions,
              filterable: true,
              multiple: true,
            },
            rules: [
              {
                required: this.noticeObject == 2,
                message: "请选择通知组",
              },
            ],
            show: this.noticeObject == 2,
            defaultValue: [],
          },
          {
            field: "noticeWayList",
            element: "el-checkbox-group",
            title: "通知方式",
            props: {
              options: [
                { label: "钉钉", value: "钉钉" },
                { label: "邮件", value: "邮件" },
              ],
            },
            rules: [{ required: true, message: "请选择通知方式" }],
            defaultValue: [],
          },
          {
            field: "email",
            title: "邮箱地址",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder:
                "请输入邮件地址，多个地址之间用【,】分隔，最多100个地址",
              rows: 5,
            },
            rules: [
              {
                required: this.$refs.crud
                  ?.getFormFields()
                  ?.noticeWayList?.includes("邮件"),
                message: "请输入邮箱地址",
              },
              { validator: this.validateEmails },
            ],
          },
          {
            field: "emailSubject",
            title: "邮箱主题",
            attrs: {
              placeholder: "请输入邮箱主题",
              maxlength: 100,
            },
            rules: [
              {
                required: this.$refs.crud
                  ?.getFormFields()
                  ?.noticeWayList?.includes("邮件"),
                message: "请输入邮箱主题",
              },
            ],
          },
          {
            field: "emailBody",
            title: "邮件正文",
            props: {
              type: "textarea",
            },
            attrs: {
              placeholder: "请输入邮件正文内容",
              rows: 5,
              maxlength: 10000,
              showWordLimit: true,
            },
            rules: [
              {
                required: this.$refs.crud
                  ?.getFormFields()
                  ?.noticeWayList?.includes("邮件"),
                message: "请输入邮件正文内容",
              },
            ],
          },
        ],
        customOperationTypes: [
          {
            title: "日志",
            modalTitle: "操作日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["contract:notice:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    // this.loadData();
  },
  activated() {
    this.listAllUser();
    this.queryGroupList();
    this.getBusinessTypeOptions();
    this.getConditionOptions();
    // Promise.all([
    //   //   this.getBusinessTypeOptions(),
    //   //   this.getDicts("support_dept").then((response) => {
    //   //     this.deptOptions = response.data;
    //   //   }),
    // ]).then(() => {
    //   setTimeout(() => {
    //     this.$nextTick(() => {
    this.loadData();
    //     });
    //   }, 500);
    // });
  },
  methods: {
    getConditionOptions() {
      api.queryCondition({}).then((res) => {
        this.conditionOptions = res.data?.map((x) => {
          return { dictValue: x, dictLabel: x };
        });
      });
    },
    // 邮箱校验
    validateEmails(rule, value, callback) {
      const emails = value?.split(/,\s*/) || [];
      if (emails.length > 100) {
        callback(new Error("最多输入100个邮箱地址"));
        return;
      }
      const invalid = emails.find(
        (e) => !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(e)
      );
      invalid ? callback(new Error(`无效邮箱地址：${invalid}`)) : callback();
    },
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    queryGroupList() {
      queryGroupList({ pageNum: 1, pageSize: 9999, status: 0 }).then((res) => {
        this.groupOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.groupId,
            label: x.groupName,
          };
        });
      });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleLog(row) {
      api.queryLog({ alarmId: row.alarmId }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    getBusinessTypeOptions() {
      agreeApi.queryBusinessType({ name: "" }).then((res) => {
        this.businessTypeOptions = res.data?.map((x) => {
          return { dictLabel: x, dictValue: x };
        });
        this.businessTypeOptions.unshift({
          dictLabel: "全部",
          dictValue: "全部",
        });
      });
    },
    querySearch(queryString, cb) {
      api
        .queryUrgencySearch({
          itemAttributeName: queryString || "",
        })
        .then((res) => {
          const result = res.data?.map((x) => {
            return { value: x };
          });
          cb(result);
        });
    },
    //状态切换
    handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";
      this.$confirm(`是否确认${text}？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          this.updateStatus(row);
        })
        .catch(() => {
          row.status = row.status == "1" ? "0" : "1";
        });
    },
    updateStatus(row) {
      const text = row.status == "0" ? "启用" : "停用";
      api
        .changeStatus({ alarmId: row.alarmId, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.loadData();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },

    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      return new Promise(async (resolve) => {
        const {
          noticeObject,
          noticeUserIds,
          noticeGroupIds,
          ...rest
        } = formParams;
        let params = { ...rest };
        if (noticeObject == 1) {
          params["noticeUserIds"] = noticeUserIds;
          params["noticeGroupIds"] = [];
        } else {
          params["noticeGroupIds"] = noticeGroupIds;
          params["noticeUserIds"] = [];
        }
        // crudOperationType:
        const res = await api.update(params).catch(() => {
          resolve(false);
        });
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          alarmId: row.alarmId,
        };
        api.deleteData(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    rowAdd() {
      this.noticeObject = 1;
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.noticeObject = row.noticeGroupId ? 2 : 1;
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        noticeWayList: row.noticeWay?.split(","),
        noticeObject: this.noticeObject,
        noticeGroupIds: row.noticeGroupId?.split(","),
        noticeUserIds: row.noticeUserId?.split(","),
      });
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
