<!-- 工单工作台 -->
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['workOrder:workBench:export'])"
      @handleExport="handleExport"
    >
      <!-- 业务类型 -->
      <template slot="relaBizType">
        <el-select
          v-model="searchForm.relaBizType"
          @change="changeRelaBizType"
          filterable
          clearable
          placeholder="业务类型"
        >
          <el-option
            v-for="item in businessTypeOption"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          >
          </el-option>
        </el-select>
      </template>
      <!-- 工单类型 -->
      <template slot="flowType">
        <el-select
          v-model="searchForm.flowType"
          filterable
          clearable
          placeholder="工单类型"
        >
          <el-option
            v-for="item in orderTypeOption"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          >
          </el-option>
        </el-select>
      </template>
    </AdvancedForm>
    <el-tabs
      v-model="activeName"
      @tab-click="handleQuery(searchForm)"
      type="card"
    >
      <el-tab-pane
        v-if="checkPermission(['workOrder:workBench:all'])"
        label="全部"
        name="3"
      ></el-tab-pane>
      <el-tab-pane
        v-if="checkPermission(['workOrder:workBench:todo'])"
        label="待处理"
        name="1"
      ></el-tab-pane>
      <el-tab-pane
        v-if="checkPermission(['workOrder:workBench:done'])"
        label="已处理"
        name="2"
      ></el-tab-pane>
      <el-tab-pane
        v-if="checkPermission(['workOrder:workBench:create'])"
        label="我创建的"
        name="4"
      ></el-tab-pane>
      <el-tab-pane
        v-if="checkPermission(['workOrder:workBench:transfer'])"
        label="我转派的"
        name="5"
      ></el-tab-pane>
    </el-tabs>

    <div>
      <el-card>
        <GridTable
          :columns="columns"
          :tableData="businessTable"
          :checkbox="true"
          :seq="true"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="businessTableTotal"
          @changePage="changePage"
          :loading="loading"
          :tableId="tableId"
        >
          <template slot="operation" slot-scope="{ row, $index }">
            <el-button
              :disabled="row.status === '11'"
              v-show="
                activeName === '1' ||
                  (activeName === '3' &&
                    row.flowStatus === '1' &&
                    userId === row.assignee)
              "
              type="text"
              size="large"
              @click="businessHandle(row)"
              v-has-permi="['workOrder:workBench:handle']"
            >
              工单处理
            </el-button>
            <el-button
              v-has-permi="['workOrder:workBench:detail']"
              @click="businessDetail(row)"
              type="text"
              size="large"
            >
              工单详情
            </el-button>
            <el-button
              :disabled="row.status === '11'"
              v-show="activeName !== '2' && row.flowStatus === '1'"
              v-has-permi="['workOrder:workBench:handleOver']"
              @click="handleOver(row)"
              type="text"
              size="large"
            >
              指派
            </el-button>
            <el-button
              :disabled="row.status === '11'"
              v-show="activeName !== '2' && row.flowStatus === '1'"
              v-has-permi="['workOrder:workBench:handleInvalid']"
              @click="handleInvalid(row)"
              type="text"
              size="large"
            >
              作废
            </el-button>
          </template>

          <template slot="deviceTypeImg" slot-scope="{ row, $index }">
            <div v-if="row.flowStatus == '1'">
              <el-progress :percentage="50"></el-progress>
            </div>
            <div v-else>
              <el-progress :percentage="100"></el-progress>
            </div>
          </template>
          <template
            slot="stationTag"
            slot-scope="{ row, $index }"
            v-if="row.stationTag"
          >
            <el-tag
              style=" margin-right: 10px;margin-bottom: 5px"
              :key="item"
              v-for="item in row.stationTag"
              >{{ item }}
            </el-tag>
          </template>
        </GridTable>
      </el-card>
    </div>
    <el-dialog
      title="指派"
      :visible.sync="handleOverVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="form" ref="form" label-width="110px" :rules="rules">
        <el-row>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model="form.deptId"
                :options="deptOptions"
                placeholder="请选择归属部门"
                @select="handleNodeClick"
                :beforeClearAll="beforeClearAll"
                :default-expand-level="1"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="人员" prop="targetUserId">
          <el-select v-model="form.targetUserId" filterable>
            <el-option
              v-for="item in userOption"
              :key="item.dictValue"
              :label="item.nickName + '-' + item.userName"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit()">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import { getToken } from "@/utils/auth";
import GridTable from "@/components/GridTable/index.vue";
import {
  allBusinessList,
  createBusinessList,
  transferBusinessList,
  businessList,
  businessTransfer,
  exportBusinessList,
  handleBusinessList,
} from "@/api/business/flow/flow";
import { stationList } from "@/api/workOrderWorkbench/index.js";
import { getDicts } from "@/api/system/dict/data";
import { useFlowList } from "@/api/orderScheduling/workStation.js";
import { projectInfoList } from "@/api/projectManage/index.js";
import { invalidFlow } from "@/api/business/flow/flow.js";
import { listAllUser, listDept } from "@/api/common";
import { mapGetters } from "vuex";
import checkPermission from "@/utils/permission.js";
import Treeselect from "@riophae/vue-treeselect";
import { saveAs } from "file-saver";
import { regionData } from "element-china-area-data";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
import {
  WORKBENCH_CLICK_REPORT,
  WORKBENCH_CLICK_TAB,
} from "@/utils/track/track-event-constants";

export default {
  name: "workOrderWorkbench",
  components: {
    Treeselect,
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        businessNoSearch: "",
        flowType: "",
        relaBizType: "",
        stationId: "",
        projectId: "",
        flowStatus: "",
        assignee: "",
        createTime: undefined,
        region: undefined,
        handleEndTime: undefined,
        createBy: "",
        acctOrgNo: "",
        pageNum: 1,
        pageSize: 10,
      },
      activeName: "3",
      handleOverVisible: false,
      form: {
        taskId: undefined,
        businessNo: undefined,
        targetUserId: undefined,
      },
      rules: {
        targetUserId: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },
      deptOptions: [],
      userOption: [],
      addForm: {},
      options: [],
      finallySearch: null,
      businessTable: [],
      businessTableTotal: 0,
      addOrderVisible: false, //添加工单
      statusDict: [], //工单状态字典
      businessTypeOption: [], //业务类型
      stationOption: [], //站点
      orderTypeOption: [], //工单类型
      // config: [],
      visible: false,
      tableId: "workOrderWorkbench", //tableId必须项目唯一，用于缓存展示列
      userOptionCopy: [],
      deptOptionList: [],
    };
  },
  async created() {
    this.getTreeselect();
  },
  computed: {
    ...mapGetters(["userId"]),
    // eslint-disable-next-line vue/return-in-computed-property
    columns() {
      return [
        {
          field: "businessNo",
          title: "工单编号",
        },
        {
          field: "flowTypeName",
          title: "工单类型",
        },
        {
          field: "relaBizType",
          title: "业务类型",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.businessTypeOption.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationTag",
          title: "站点标签",
          slots: { default: "stationTag" },
          showOverflow: false,
          minWidth: 250,
        },
        {
          field: "projectName",
          title: "所属项目",
        },
        {
          field: "cent",
          title: "完成进度",
          slots: { default: "deviceTypeImg" },
          customWidth: 200,
        },
        {
          field: "flowStatus",
          title: "工单状态",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.statusDict.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "nickName",
          title: "当前处理人",
          // visible: this.activeName === "3",
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "createBy",
          title: "创建人",
          formatter: ({ cellValue }) => {
            return (
              this.userOption?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "acctOrgNo",
          title: "创建人所属组织",
          formatter: ({ cellValue }) => {
            return (
              this.deptOptionList?.find((item) => item.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "transferTime",
          title: "转派时间",
          visible: this.activeName === "5",
        },
        {
          field: "endTime",
          title: "完成时间",
          visible: this.activeName !== "1",
        },
        {
          field: "totalCost",
          title: "工单耗时",
          visible: this.activeName !== "1",
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ];
    },
    config() {
      return [
        {
          key: "businessNoSearch",
          title: "工单编号",
          type: "input",
          placeholder: "请输入工单编号",
        },
        {
          key: "flowType",
          title: "工单类型",
          type: "select",
          options: this.orderTypeOption,
          slot: "flowType",
          placeholder: "请选择工单类型",
        },
        {
          key: "stationId",
          title: "站点",
          type: "select",
          options: this.stationOption,
          placeholder: "请选择站点",
        },
        {
          key: "projectId",
          title: "项目",
          type: "select",
          options: this.projectOption,
          placeholder: "请选择项目",
        },
        {
          key: "flowStatus",
          title: "工单状态",
          type: "select",
          options: this.statusDict,
          placeholder: "请选择工单状态",
        },
        {
          key: "relaBizType",
          title: "业务类型",
          type: "slot",
          options: this.businessTypeOption,
          slot: "relaBizType",
          placeholder: "请选择业务类型",
        },
        {
          key: "assignee",
          title: "处理人",
          type: "select",
          options: this.userOption,
          placeholder: "请选择处理人",
          hidden: ["1", "2"].includes(this.activeName),
        },
        {
          key: "createTime",
          title: "创建时间",
          type: "dateRange",
          placeholder: "请选择创建时间",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
          hidden: ["1", "2"].includes(this.activeName),
        },
        {
          key: "handleEndTime",
          title: "完成时间",
          type: "dateRange",
          placeholder: "请选择完成时间",
          hidden: this.activeName === "1",
        },
        {
          key: "createBy",
          title: "创建人",
          type: "select",
          options: this.userOption,
          placeholder: "请选择创建人",
        },
        {
          key: "acctOrgNo",
          title: "创建人所属组织",
          type: "select",
          options: this.deptOptionList,
          placeholder: "请选择创建人所属组织",
        },
      ];
    },
  },
  mounted() {
    this.token = getToken();
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { pageNum: 1, pageSize: 10, ...this.$route.params };
      this.activeName = this.$route.params.activeName || "3";
    }
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getFlowStatusDicts(),
      this.getFlowBusinessType(),
      this.getStationList(),
      this.getUseFlowList(),
      this.getProjectInfoList(),
      this.getListUser(),
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
    //获取工单列表
    this.getFlowList(this.searchForm);
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { pageNum: 1, pageSize: 10, ...this.$route.params };
      this.activeName = this.$route.params.activeName || this.activeName;
    }
    Promise.all([
      this.getFlowStatusDicts(),
      this.getFlowBusinessType(),
      this.getStationList(),
      this.getUseFlowList(),
      this.getProjectInfoList(),
      this.getListUser(),
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getFlowList(this.searchForm);
        });
      }, 500);
    });
  },
  watch: {
    $route: {
      handler(newVal) {
        //console.log("newVal", newVal);
        this.searchForm.projectId = newVal.query.projectId;
        if (newVal.query.relaBizType) {
          this.searchForm.relaBizType = newVal.query.relaBizType;
        }
      },
      immediate: true,
    },
  },
  methods: {
    checkPermission,
    //获取项目列表
    async getProjectInfoList() {
      const { code, data } = await projectInfoList(this.searchForm);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.projectId;
        element.dictLabel = element.projectName + "-" + element.projectCode;
      });
      this.projectOption = data;
    },
    //工单类型
    async getUseFlowList(businessType) {
      let params = {
        businessType,
      };
      const { code, data } = await useFlowList(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.flowType;
        element.dictLabel = element.flowTypeName;
      });
      this.orderTypeOption = data;
    },
    //站点
    async getStationList() {
      const { code, data } = await stationList();
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.stationId;
        element.dictLabel = element.stationName;
      });
      this.stationOption = data;
    },
    getFlowList(params) {
      const args = this._.cloneDeep(params ? params : this.searchForm);
      // args.deviceTypeNo = this.searchForm.deviceTypeNo; //设备类型
      this.loading = true;
      this.finallySearch = args;
      if (Array.isArray(args.createTime)) {
        args.startTime = args.createTime[0] + " 00：00：00";
        args.endTime = args.createTime[1] + " 23：59：59";
        delete args.createTime;
      }
      if (Array.isArray(args.handleEndTime)) {
        args.startFinishTime = args.handleEndTime[0] + " 00：00：00";
        args.endFinishTime = args.handleEndTime[1] + " 23：59：59";
        delete args.handleEndTime;
      } else {
        delete args.startFinishTime;
        delete args.endFinishTime;
        delete args.handleEndTime;
      }
      if (args.region && Array.isArray(args.region)) {
        args["provinceList"] = [];
        args["cityList"] = [];
        args["countyList"] = [];
        args.region.forEach((item) => {
          if (item[0]) {
            args["provinceList"].push(item[0]);
          }
          if (item[1]) {
            args["cityList"].push(item[1]);
          }
          if (item[2]) {
            args["countyList"].push(item[2]);
          }
        });
        delete args.region;
      }
      if (this.activeName === "1") {
        businessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else if (this.activeName === "2") {
        handleBusinessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else if (this.activeName === "3") {
        allBusinessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else if (this.activeName === "4") {
        createBusinessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      } else if (this.activeName === "5") {
        transferBusinessList(args)
          .then((res) => {
            this.loading = false;
            this.businessTable = res?.data;
            this.businessTableTotal = res?.total;
          })
          .catch((err) => {
            this.loading = false;
          });
      }
    },
    //切换业务类型
    async changeRelaBizType(val) {
      await this.getUseFlowList(val);
      this.initConfig();
    },
    //初始化
    initConfig() {
      // this.;
    },
    async getFlowStatusDicts() {
      //获取工单状态字典
      return await getDicts("cm_flow_status").then((response) => {
        this.statusDict = response?.data;
      });
    },
    async getFlowBusinessType() {
      //业务类型字典
      return await getDicts("flow_business_type").then((response) => {
        this.businessTypeOption = response?.data;
      });
    },
    //工单处理
    businessHandle(row) {
      this.$router.push({
        path: "/orderDeal",
        query: {
          businessNo: row.businessNo,
          flowStatus: row.flowStatus,
          taskId: row.taskId,
          router: "/workOrderWorkBench/workOrderWorkbench",
        },
      });
    },
    //工单详情
    businessDetail(row) {
      this.$router.push({
        path: "/orderDetail",
        query: {
          businessNo: row.businessNo,
          flowStatus: row.flowStatus,
          taskId: row.taskId,
        },
      });
    },
    handleInvalid(row) {
      this.$confirm("确定作废该工单么吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          taskId: row.taskId,
          businessNo: row.businessNo,
        };

        const loading = this.$loading({
          lock: true,
          text: "作废中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });

        invalidFlow(data)
          .then((res) => {
            this.handleQuery(this.searchForm);
            loading, close();
            this.$message.success("作废成功");
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    // 指派
    handleOver(row) {
      this.handleOverVisible = true;
      this.form.businessNo = row.businessNo;
      this.form.taskId = row.taskId;
    },
    //table内容格式化
    statusFormat(val) {
      return this.selectDictLabel(this.statusDict, val.deviceStatus);
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    //查询列表
    handleQuery(params) {
      console.log("调用了handleQuery");
      if (this.activeName === "1" || this.activeName === "2") {
        this.searchForm.assignee = undefined;
        this.searchForm.region = undefined;
        if (this.activeName === "1") {
          this.searchForm.handleEndTime = undefined;
        }
      }
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.getFlowList(params);

      //点击事件上报
      this.trackReport();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getFlowList();
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.getFlowList(this.finallySearch);
    },
    //获取用户列表
    async getListUser(param) {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      if (param) {
        params.orgNo = Number(param);
      }
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
      this.userOptionCopy = data;
    },
    closeDialog() {
      this.$refs.form.resetFields();
      this.handleOverVisible = false;
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
          };

          const loading = this.$loading({
            lock: true,
            text: "指派中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          businessTransfer(params)
            .then((res) => {
              if (res?.success) {
                this.closeDialog();
                this.handleQuery(this.searchForm);
                loading.close();
                this.$message.success("指派成功");
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.form.targetUserId = undefined;
      this.getListUser(data.deptId);
    },
    beforeClearAll() {
      this.form.targetUserId = undefined;
      this.form.deptId = undefined;
      this.getListUser();
    },
    handleExport(params) {
      const args = this._.cloneDeep(params ? params : this.searchForm);
      // args.deviceTypeNo = this.searchForm.deviceTypeNo; //设备类型
      this.loading = true;
      this.finallySearch = args;
      if (Array.isArray(args.createTime)) {
        args.startTime = args.createTime[0] + " 00：00：00";
        args.endTime = args.createTime[1] + " 23：59：59";
        delete args.createTime;
      }
      if (Array.isArray(args.handleEndTime)) {
        args.startFinishTime = args.handleEndTime[0] + " 00：00：00";
        args.endFinishTime = args.handleEndTime[1] + " 23：59：59";
        delete args.handleEndTime;
      } else {
        delete args.startFinishTime;
        delete args.endFinishTime;
        delete args.handleEndTime;
      }
      if (args.region && Array.isArray(args.region)) {
        args["provinceList"] = [];
        args["cityList"] = [];
        args["countyList"] = [];
        args.region.forEach((item) => {
          if (item[0]) {
            args["provinceList"].push(item[0]);
          }
          if (item[1]) {
            args["cityList"].push(item[1]);
          }
          if (item[2]) {
            args["countyList"].push(item[2]);
          }
        });
        delete args.region;
      }
      this.$confirm("是否确认导出所有数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportBusinessList(args);

        //点击事件上报
        this.reportTrackEvent(WORKBENCH_CLICK_REPORT);
        setTimeout(() => {
          this.openFileDownLoadPage();
        }, 1000);
        this.$message.success("导出任务已提交，稍后在下载中心下载或查看");
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    trackReport() {
      let tabname = "";
      switch (this.activeName) {
        case "1":
          tabname = "待处理";
          break;
        case "2":
          tabname = "已处理";
          break;
        case "3":
          tabname = "全部";
          break;
        case "4":
          tabname = "我创建的";
          break;
        case "5":
          tabname = "我转派的";
          break;
      }

      this.reportTrackEvent(WORKBENCH_CLICK_TAB, {
        tabname,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
</style>
