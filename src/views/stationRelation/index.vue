<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            type="primary"-->
          <!--            icon="el-icon-plus"-->
          <!--            @click.stop="handleCreate"-->
          <!--          >-->
          <!--            新增-->
          <!--          </el-button>-->
        </template>
        <template slot="projectCode" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="handleProjectList(row)">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="stationTag" slot-scope="{ row, $index }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in row.stationTagList"
          >
            {{ item }}
          </el-tag>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="tagManage(row)"
            v-has-permi="['operation:stationRelation:manage']"
          >
            配置
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <detail
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :row-info="rowInfo"
      @update-list="getList"
    />
    <AddProject
      v-if="addProjectDialogVisible"
      :visible.sync="addProjectDialogVisible"
      :page-type="pageType"
      :project-id="projectId"
      :title="dialogTitle"
    />
    <Configure
      ref="configure"
      v-if="configureVisible"
      :visible.sync="configureVisible"
    ></Configure>
    <el-dialog
      title="配置责任和服务站点"
      :visible.sync="tagManageVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleCancel"
      class="transfer-dialog"
    >
      <h3>责任人配置</h3>
      <div style="display: flex;justify-content:center">
        <TransferTree
          :titles="['未配置场站', '已配置场站']"
          :cascadeData="transferData"
          v-model="chargeChecked"
          ref="chargeTransfer"
        ></TransferTree>
        <!-- <el-transfer
          ref="chargeTransfer"
          v-model="chargeChecked"
          :data="transferData"
          filterable
          :titles="['未配置场站', '已配置场站']"
          :props="{ key: 'stationId', label: 'stationName' }"
        ></el-transfer> -->
      </div>
      <h3 class="transfer-title">服务人配置</h3>
      <TransferTree
        :titles="['未配置场站', '已配置场站']"
        :cascadeData="transferData"
        v-model="serviceChecked"
        ref="serviceTransfer"
      ></TransferTree>
      <!-- <el-transfer
        ref="serviceTransfer"
        v-model="serviceChecked"
        :data="transferData"
        filterable
        :titles="['未配置场站', '已配置场站']"
        :props="{ key: 'stationId', label: 'stationName' }"
      ></el-transfer> -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel" size="small">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import Detail from "@/views/station/components/detail.vue";
import AddProject from "@/views/projectManage/addProject.vue";
import {
  getStationRelationsByUserId,
  queryStationRelationPage,
  saveStationRelation,
} from "@/api/station/relation";
import { stationListByOrgNoList } from "@/api/workOrderWorkbench";
import { listAllUser, listDept } from "@/api/common";
import { regionData } from "element-china-area-data";
import TransferTree from "@/components/TransferTree/index.vue";
import { MAINTENANCE_ORDER_STATION_RELATINO_SETTING } from "@/utils/track/track-event-constants";
import Configure from "./components/configure.vue";
export default {
  name: "stationList",
  components: {
    AdvancedForm,
    GridTable,
    Detail,
    AddProject,
    TransferTree,
    Configure,
  },
  data() {
    return {
      configureVisible: false,
      config: [],
      // 已经 勾选的
      chargeChecked: [],
      serviceChecked: [],
      transferData: [],
      stationId: undefined,
      columns: [
        {
          field: "nickName",
          title: "姓名（用户昵称）",
          treeNode: true,
        },
        {
          field: "phone",
          title: "手机号",
          showOverflowTooltip: true,
        },
        {
          field: "deptName",
          title: "所属部门",
          // formatter: this.stationTypeFormat
        },
        {
          field: "chargeStationCount",
          title: "责任站点",
        },
        {
          field: "serviceStationCount",
          title: "服务站点",
          showOverflowTooltip: true,
        },
        {
          field: "createUserTime",
          title: "配置时间",
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      deptOptions: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "stationList",
      detailVisible: false,
      rowInfo: {},
      stationTypeDict: [],
      userOption: [],
      stationTagDict: [],
      addProjectDialogVisible: false,
      pageType: "detail",
      userId: "",
      userName: "",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      cityData: [],
    };
  },
  async created() {
    // this.initConfig();
    // this.getList();
    // this.getStationList();
    this.getCityData();
    this.getTreeselect();
  },
  mounted() {
    Promise.all([
      // this.getStationList(),
      this.getList(),
      this.getListUser(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  watch: {},
  methods: {
    async getStationList(userId) {
      const res = await stationListByOrgNoList({ userId: userId });
      if (res) {
        this.transferData = res?.data.map((x) => {
          return {
            ...x,
            id: x.city,
            label: this.cityData.find((y) => y.value === x.city)?.label,
            children: x.children?.map((i) => {
              return {
                ...i,
                id: i.stationId,
                label: i.stationName,
                pid: i.city,
                plabel: this.cityData.find((y) => y.value === x.city)?.label,
              };
            }),
          };
        });
        console.log("this.transferData", res.data, this.transferData);
      }
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    handleSubmit() {
      let arr1 = [];
      this.chargeChecked?.forEach((element) => {
        element.children?.map((x) => {
          arr1.push(x.id);
        });
      });
      let arr2 = [];
      this.serviceChecked?.forEach((element) => {
        element.children?.map((x) => {
          arr2.push(x.id);
        });
      });
      let param = {
        chargeList: arr1,
        serviceList: arr2,
        userId: this.userId,
        userName: this.userName,
      };
      console.log(param);
      saveStationRelation(param).then((res) => {
        this.tagManageVisible = false;
        this.getList();

        this.reportTrackEvent(MAINTENANCE_ORDER_STATION_RELATINO_SETTING, {
          userId: this.userId,
          userName: this.userName,
        });
      });
    },
    // getStationList(userId) {
    //   let param = {
    //     userId: userId,
    //   };
    //   stationListByOrgNoList(param).then((res) => {
    //     this.transferData = res?.data;
    //   });
    // },
    //项目列表
    handleProjectList(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.addProjectDialogVisible = true;
    },
    showDetail(row) {
      this.rowInfo = row;
      this.detailVisible = true;
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
        console.log(this.deptOptions);
      });
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.value = item.deptId;
        return item;
      });
    },
    async tagManage(row) {
      this.configureVisible = true;
      this.$nextTick(() => {
        this.$refs.configure.openDialog(row);
      });
      // await this.getStationList(row.userId);

      // this.userId = row.userId;
      // this.userName = row.nickName;
      // let param = {
      //   userId: row.userId,
      // };

      // const loading = this.$loading({
      //   lock: true,
      //   text: "查询场站绑定信息",
      //   spinner: "el-icon-loading",
      //   background: "rgba(0, 0, 0, 0.7)",
      // });

      // getStationRelationsByUserId(param)
      //   .then((res) => {
      //     // this.chargeChecked = res?.data.chargeList.map((obj) => obj.stationId);
      //     // this.serviceChecked = res?.data.serviceList.map(
      //     //   (obj) => obj.stationId
      //     // );
      //     console.log(res.data);
      //     this.chargeChecked = res?.data.chargeList.map((x) => {
      //       return {
      //         ...x,
      //         id: x.city,
      //         label: this.cityData.find((y) => y.value === x.city)?.label,
      //         children: x.children?.map((i) => {
      //           return {
      //             ...i,
      //             id: i.stationId,
      //             label: i.stationName,
      //             pid: i.city,
      //             plabel: this.cityData.find((y) => y.value === x.city)?.label,
      //           };
      //         }),
      //       };
      //     });
      //     this.serviceChecked = res?.data.serviceList.map((x) => {
      //       return {
      //         ...x,
      //         id: x.city,
      //         label: this.cityData.find((y) => y.value === x.city)?.label,
      //         children: x.children?.map((i) => {
      //           return {
      //             ...i,
      //             id: i.stationId,
      //             label: i.stationName,
      //             pid: i.city,
      //             plabel: this.cityData.find((y) => y.value === x.city)?.label,
      //           };
      //         }),
      //       };
      //     });
      //     console.log("责任人", this.chargeChecked);
      //     console.log("服务人", this.serviceChecked);
      //     this.tagManageVisible = true;
      //     this.$nextTick(() => {
      //       console.log("--==", this.transferData);
      //       this.$refs.chargeTransfer.getDefaultLeftData();
      //       this.$refs.serviceTransfer.getDefaultLeftData();
      //       // this.$refs.chargeTransfer.clearQuery("left");
      //       // this.$refs.chargeTransfer.clearQuery("right");
      //       // this.$refs.serviceTransfer.clearQuery("left");
      //       // this.$refs.serviceTransfer.clearQuery("right");
      //     });
      //     loading.close();
      //   })
      //   .catch(() => {
      //     loading.close();
      //   });
    },
    getCityData() {
      this.cityData = [];
      regionData.map((x) => {
        if (x.children.length > 0) {
          x.children.map((y) => {
            this.cityData.push(y);
          });
        }
      });
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };

      // const args = this._.cloneDeep(params ? params : this.searchForm);
      // args.deviceTypeNo = this.searchForm.deviceTypeNo; //设备类型
      // this.finallySearch = args;
      // if (Array.isArray(args.createTime)) {
      //   args.startTime = args.createTime[0] + " 00：00：00";
      //   args.endTime = args.createTime[1] + " 23：59：59";
      //   delete args.createTime;
      // }
      if (params.rangeTime) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
      }

      queryStationRelationPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.tableData.forEach((element) => {
              element.dictValue = element.userId;
              element.dictLabel = element.nickName;
            });
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "consNameOrPhone",
          title: "姓名或手机号",
          type: "input",
          placeholder: "请输入姓名或手机号查询",
        },
        {
          key: "createUser",
          title: "配置人",
          type: "select",
          placeholder: "请选择配置人",
          options: this.userOption,
        },
        {
          key: "deptIds",
          title: "所属部门",
          type: "deptTree",
          placeholder: "请选择所属部门",
          options: this.deptOptions,
        },
        {
          key: "rangeTime",
          title: "配置时间",
          type: "dateRange",
          placeholder: "请选择配置时间",
          startPlaceholder: "配置开始时间",
          endPlaceholder: "配置结束时间",
        },
      ];
    },

    handleCancel() {
      this.tagManageVisible = false;
    },
  },
};
</script>

<style scoped lang="less">
.transfer-dialog {
  h3 {
    margin: 0 0 12px;
  }
  .transfer-title {
    margin-top: 20px;
  }
}
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;
  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}
/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}
</style>
