<template>
  <el-dialog
    title="配置责任和服务站点"
    :visible.sync="configVisible"
    :close-on-click-modal="false"
    width="90%"
    append-to-body
    @close="handleCancel"
  >
    <el-radio-group
      v-model="activeTab"
      class="radio-group"
      size="medium"
      @input="handleTabChange"
    >
      <el-radio-button label="charge">责任站点</el-radio-button>
      <el-radio-button label="service">服务站点</el-radio-button>
    </el-radio-group>
    <div class="box-content">
      <div class="box-left" style="margin-right: 20px;">
        <h4>未配置站点</h4>
        <AdvancedForm
          :config="config"
          :queryParams="searchLeftForm"
          ref="AdvancedForm"
          v-if="config.length"
          :defaultCol="12"
          @confirm="handleLeftQuery"
          @resetQuery="resetLeftQuery"
        >
        </AdvancedForm>
        <el-card>
          <GridTable
            ref="leftGridTable"
            :columns="leftColumns"
            :tableData="tableLeftData"
            :currentPage.sync="searchLeftForm.pageNum"
            :pageSize.sync="searchLeftForm.pageSize"
            :total.sync="leftTotal"
            @changePage="getLeftList"
            :loading="leftLoading"
            :tableId="leftTableId"
            :batchDelete="true"
            :checkbox="true"
            @handleSelectionChange="leftTableSelect"
            isSmallPagination
            :columnAutoFit="false"
            rowId="stationId"
          >
            <template slot="xToolbarBtn" slot-scope="{}">
              <el-button type="primary" size="mini" @click="handleLeftConfig"
                >配置站点
              </el-button>
              <el-button type="primary" size="mini" @click="handleLeftAllConfig"
                >全部站点一键配置
              </el-button>
            </template>
          </GridTable>
        </el-card>
      </div>
      <div class="box-left">
        <h4>已配置站点</h4>
        <AdvancedForm
          :config="config"
          :queryParams="searchRightForm"
          ref="AdvancedForm"
          v-if="config.length"
          :defaultCol="12"
          @confirm="handleRightQuery"
          @resetQuery="resetRightQuery"
        >
        </AdvancedForm>
        <el-card>
          <GridTable
            ref="rightGridTable"
            :columns="rightColumns"
            :tableData="tableRightData"
            :currentPage.sync="searchRightForm.pageNum"
            :pageSize.sync="searchRightForm.pageSize"
            :total.sync="rightTotal"
            @changePage="getRightList"
            :loading="rightLoading"
            :tableId="rightTableId"
            :batchDelete="true"
            :checkbox="true"
            @handleSelectionChange="rightTableSelect"
            isSmallPagination
            :columnAutoFit="false"
            rowId="stationId"
          >
            <template slot="xToolbarBtn" slot-scope="{}">
              <el-button type="primary" size="mini" @click="handleRightConfig">
                移除站点
              </el-button>
              <el-button
                type="primary"
                size="mini"
                @click="handleRightAllConfig"
              >
                全部站点一键清空
              </el-button>
            </template>
          </GridTable>
        </el-card>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { regionData } from "element-china-area-data";
import * as api from "@/api/station/relationConfig.js";
export default {
  components: { AdvancedForm, GridTable },
  props: {
    visible: { type: Boolean, default: false },
  },
  data() {
    return {
      configVisible: this.visible,
      userName: "",
      userId: "",
      activeTab: "charge",
      searchLeftForm: {
        stationName: undefined,
        region: undefined,
        pageNum: 1,
        pageSize: 10,
      },
      searchRightForm: {
        stationName: undefined,
        region: undefined,
        pageNum: 1,
        pageSize: 10,
      },

      tableLeftData: [],
      leftTotal: 0,
      leftLoading: false,
      leftTableId: "leftStationConfig",
      leftStationIds: [],
      tableRightData: [],
      rightTotal: 0,
      rightLoading: false,
      rightTableId: "rightStationConfig",
      rightStationIds: [],
    };
  },
  computed: {
    leftColumns() {
      return [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "belongPlace",
          title: "所在区域",
        },
        {
          field: "stationName",
          title: "站点(" + this.leftTotal + ")",
        },
      ];
    },
    rightColumns() {
      return [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "belongPlace",
          title: "所在区域",
        },
        {
          field: "stationName",
          title: "站点(" + this.rightTotal + ")",
        },
      ];
    },
    config() {
      return [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称搜索",
        },
        {
          key: "region",
          title: "所在区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择所在区域",
          //   props: {
          //     multiple: false,
          //   },
        },
      ];
    },
  },

  methods: {
    handleTabChange() {
      this.resetLeftQuery();
      this.resetRightQuery();
    },
    resetLeftQuery() {
      this.searchLeftForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getLeftList();
      this.$refs.leftGridTable.clearTips();
    },
    resetRightQuery() {
      this.searchRightForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getRightList();
      this.$refs.rightGridTable.clearTips();
    },
    openDialog(row) {
      this.userName = row.nickName;
      this.userId = row.userId;
      this.getLeftList();
      this.getRightList();
    },
    handleCancel() {
      this.configVisible = false;
      this.$emit("update:visible", this.configVisible);
    },
    handleLeftQuery() {
      this.searchLeftForm.pageNum = 1;
      this.getLeftList();
    },
    handleRightQuery() {
      this.searchRightForm.pageNum = 1;
      this.getRightList();
    },
    getLeftList() {
      this.leftLoading = true;
      let params = {
        ...this.searchLeftForm,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }

      api
        .queryLeftList({ ...params, userId: this.userId, type: this.activeTab })
        .then((res) => {
          this.leftLoading = false;
          this.tableLeftData = res?.data;
          this.leftTotal = res?.total;
        })
        .catch(() => {
          this.leftLoading = false;
        });
    },
    getRightList() {
      this.rightLoading = true;
      let params = {
        ...this.searchRightForm,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }

      api
        .queryRightList({
          ...params,
          userId: this.userId,
          type: this.activeTab,
        })
        .then((res) => {
          this.rightLoading = false;
          this.tableRightData = res?.data;
          this.rightTotal = res?.total;
        })
        .catch(() => {
          this.rightLoading = false;
        });
    },
    //左侧-配置站点
    handleLeftConfig() {
      if (this.leftStationIds?.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }
      let params = {
        userName: this.userName,
        userId: this.userId,
      };
      params[
        this.activeTab === "charge" ? "chargeList" : "serviceList"
      ] = this.leftStationIds;
      api.setStationConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.getLeftList();
          this.getRightList();
          this.$refs.leftGridTable.clearTips();
        }
      });
    },
    //左侧-配置全部站点
    handleLeftAllConfig() {
      this.$confirm("是否确认配置全部站点?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        api
          .setAllConfig({ userId: this.userId, type: this.activeTab })
          .then((res) => {
            if (res?.code === "10000") {
              this.$message.success("配置成功");
              this.getLeftList();
              this.getRightList();
            }
          });
      });
    },
    //右侧-移除站点
    handleRightConfig() {
      if (this.rightStationIds?.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }
      let params = {
        userId: this.userId,
      };
      params[
        this.activeTab === "charge" ? "chargeList" : "serviceList"
      ] = this.rightStationIds;
      api.clearConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.getLeftList();
          this.getRightList();
          this.$refs.rightGridTable.clearTips();
        }
      });
    },
    //右侧-清空全部站点
    handleRightAllConfig() {
      this.$confirm("是否确认清空全部站点?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        api
          .clearAllConfig({ userId: this.userId, type: this.activeTab })
          .then((res) => {
            if (res?.code === "10000") {
              this.$message.success("配置成功");
              this.getLeftList();
              this.getRightList();
            }
          });
      });
    },
    leftTableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.leftStationIds = tableData?.map((v) => v.stationId);
      console.log("处理后勾选中的数据", this.leftStationIds);
    },
    rightTableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.rightStationIds = tableData?.map((v) => v.stationId);
      console.log("处理后勾选中的数据", this.rightStationIds);
    },
  },
};
</script>

<style lang="less" scoped>
.box-content {
  display: flex;
  .box-left {
    flex: 1;
  }
}
.btn-wrap {
  display: flex;
  justify-content: space-evenly;
}
.radio-group {
  margin-bottom: 10px;
  display: flex;
  /deep/ .el-radio-button {
    flex: 1;
  }
  /deep/ .el-radio-button__inner {
    width: 100%;
  }
}
// /deep/ .el-cascader .el-input .el-input__inner {
//   height: 32px !important;
// }
// /deep/ .el-cascader__search-input {
//   height: 0;
// }
</style>
