//充电枪
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['chargingStation:gun:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="gunId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchTypes"
            v-has-permi="['maintenance:device:batchImport']"
          >
            批量导入质保
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="goEquipmentBrandWarranty"
            v-has-permi="['maintenance:device:equipmentBrandWarranty']"
          >
            设备品牌质保
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['maintenance:device:detail']"
          >
            设备详情
          </el-button>
          <el-dropdown @command="(command) => handleCommand(command, row)">
            <el-button type="text" size="large">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="item.command"
                v-for="(item, index) in moreBtnList"
                :key="index"
                v-has-permi="[item.permission]"
              >
                <!-- v-has-permi="[item.permission]" -->
                <el-button type="text" size="large">
                  {{ item.title }}
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            :disabled="!checkPermission(['maintenance:device:detail'])"
          >
            {{ row.deviceNo }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <StatusLog
      @close="logVisible = false"
      :visible="logVisible"
      :gunId="gunId"
    ></StatusLog>
    <BatchUploadTypes
      @uploadSuccess="getList"
      ref="batchUploadTypes"
    ></BatchUploadTypes>
    <BaseFormModal
      ref="formModal"
      modalTitle="修改质保"
      :config="typeConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
    >
    </BaseFormModal>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import BatchUploadTypes from "./components/batchUploadTypes.vue";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import { initParams } from "@/utils/buse.js";
import StatusLog from "./components/statusLog.vue";
import {
  queryGunList,
  exportGunExcel,
  queryBrandOpts,
  queryModelOpts,
  queryChargingDict,
  saveWarranty,
  queryDeviceModelDict,
} from "@/api/operationMaintenanceManage/deviceList/index.js";
import { regionData } from "element-china-area-data";
import checkPermission from "@/utils/permission.js";
import { getToken } from "@/utils/auth";
// import { DATA_INFO_CLICK_CHARING_GUN_REPORT } from "@/utils/track/track-event-constants";

export default {
  name: "maintenanceChargingGun",
  components: {
    AdvancedForm,
    GridTable,
    StatusLog,
    BatchUploadTypes,
    BaseFormModal,
  },
  data() {
    return {
      gunId: "",
      logVisible: false,
      // config: [],
      columns: [
        // {
        //   type: "checkbox",
        //   customWidth: 60,
        // },
        {
          field: "deviceNo",
          title: "设备编码",
          slots: { default: "jump" },
        },
        {
          field: "deviceName",
          title: "设备名称",
        },

        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "provinceInfo",
          title: "省市区",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 170,
        },
        {
          field: "runStatusName",
          title: "枪运行状态",
        },
        {
          field: "operStatusName",
          title: "枪运营状态",
        },
        {
          field: "subTypeName",
          title: "设备类型",
        },
        {
          field: "operationMode",
          title: "运营类型",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "brandName",
          title: "设备品牌",
        },
        {
          field: "modelName",
          title: "设备型号",
        },
        {
          field: "openDate",
          title: "投运日期",
        },
        {
          field: "deviceWarrantyExpiresTime",
          title: "质保到期时间",
          slots: {
            default: ({ row }) => {
              return [
                <span
                  style={{
                    color:
                      row.deviceWarrantyExpiresTimeFlag == "1" ? "red" : "",
                  }}
                >
                  {row.deviceWarrantyExpiresTime}
                </span>,
              ];
            },
          },
        },
        {
          field: "deviceModelName",
          title: "质保供应商",
        },
        {
          field: "deviceVersion",
          title: "固件版本号",
        },
        // {
        //   field: "stationNo",
        //   title: "站点编码",
        // },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "chargingStationList",
      recordList: [],
      operStatusOptions: [],
      subTypeOptions: [],
      runStatusOptions: [],
      handRow: {
        stationIds: [],
      },
      token: "",
      brandOptions: [],
      modelOptions: [],
      operationModeOptions: [],
      //操作列 更多下按钮
      moreBtnList: [
        {
          title: "订单信息",
          command: "order",
          clickFn: (row) => {
            this.handleJumpOrder(row);
          },
          permission: "maintenance:device:order",
        },
        {
          title: "状态日志",
          command: "statusLog",
          clickFn: (row) => {
            this.handleStatusLog(row);
          },
          permission: "maintenance:device:log",
        },
        {
          title: "维修记录",
          command: "maintenanceRecord",
          clickFn: (row) => {
            this.$router.push({
              name: "operationWorkOrderNew",
              params: {
                orderTypeParentNames: ["充电桩故障工单", "告警工单"],
                deviceNo: row.deviceNo,
                deviceName: row.deviceName,
              },
            });
          },
          permission: "maintenance:device:maintenanceRecord",
        },
        {
          title: "修改质保",
          command: "warranty",
          clickFn: (row) => {
            this.handleEditType(row);
          },
          permission: "maintenance:device:warranty",
        },
      ],
      deviceModelNameOptions: [],
    };
  },
  created() {},
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { pageNum: 1, pageSize: 10, ...this.$route.params };
    }
    queryChargingDict({ dictCode: "subType" }).then((res) => {
      this.subTypeOptions = res.data;
    });
    queryChargingDict({ dictCode: "pileGunOperStatus" }).then((res) => {
      this.operStatusOptions = res.data;
    });
    queryChargingDict({ dictCode: "gunCouplerRunStatus" }).then((res) => {
      this.runStatusOptions = res.data;
    });
    queryDeviceModelDict({}).then((res) => {
      this.deviceModelNameOptions = res.data?.map((x) => {
        return { value: x };
      });
    });
    this.queryBrandOpts();
    this.queryModelOpts();
    Promise.all([
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          // this.initConfig();
        });
      }, 500);
    });
    this.token = getToken();
  },
  methods: {
    checkPermission,
    modalConfirmHandler(formParams) {
      saveWarranty(formParams).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.$refs.formModal.closeVisible();
          this.getList();
        }
      });
    },
    goEquipmentBrandWarranty() {
      this.$router.push({
        path: "/operationMaintenanceManage/infoQuery/equipmentBrandWarranty",
      });
    },
    handleEditType(row) {
      this.$refs.formModal.open({
        ...initParams(this.typeConfig),
        deviceNo: row.deviceNo,
        deviceWarrantyExpiresTime: row.deviceWarrantyExpiresTime,
      });
    },
    handleBatchTypes() {
      this.$refs.batchUploadTypes.open();
    },
    handleCommand(command, row) {
      this.moreBtnList?.find((x) => x.command == command)?.clickFn(row);
    },
    handleJumpOrder(row) {
      this.$router.push({
        name: "chargingOrder2",
        params: {
          deviceNo: row.deviceNo,
          stationNo: row.stationNo,
        },
      });
    },
    handleStatusLog(row) {
      this.logVisible = true;
      this.gunId = row.gunId;
    },
    queryBrandOpts() {
      queryBrandOpts({ brandName: "" }).then((res) => {
        this.brandOptions = res.data;
      });
    },
    queryModelOpts() {
      queryModelOpts({ modelName: "" }).then((res) => {
        this.modelOptions = res.data;
      });
    },
    showDetail(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/deviceList/gunDetail",
        query: {
          pileId: row.pileId,
          gunId: row.gunId,
          stationNo: row.stationNo,
          deviceNo: row.deviceNo,
        },
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      // this.handRow.stationIds.length == 0
      //   ? "是否确认导出所有数据?"
      //   : "是否确认导出所选数据?";
      const params = {
        ...this.searchForm,
        // stationIds: this.handRow.stationIds,
      };
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0]
          ? params.region[0] + "0000"
          : undefined;
        params["city"] = params.region[1] ? params.region[1] + "00" : undefined;
        params["county"] = params.region[2];
        delete params.region;
      }
      if (Array.isArray(params.rangeTime)) {
        params.operStartTime = params.rangeTime[0] + " 00:00:00";
        params.operEndTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      console.log("查询", params);

      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportGunExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          // this.reportTrackEvent(DATA_INFO_CLICK_CHARING_GUN_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0]
          ? params.region[0] + "0000"
          : undefined;
        params["city"] = params.region[1] ? params.region[1] + "00" : undefined;
        params["county"] = params.region[2];
        delete params.region;
      }
      if (Array.isArray(params.rangeTime)) {
        params.operStartTime = params.rangeTime[0] + " 00:00:00";
        params.operEndTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      console.log("查询", params);

      queryGunList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
  },
  computed: {
    typeConfig() {
      return [
        {
          field: "deviceWarrantyExpiresTime",
          element: "el-date-picker",
          title: "质保到期时间",
          props: {
            valueFormat: "yyyy-MM-dd",
          },
          rules: [
            {
              required: true,
              message: "质保到期时间不能为空",
              trigger: "blur",
            },
          ],
        },
        {
          field: "deviceModelName",
          element: "el-select",
          title: "质保供应商",
          props: {
            options: this.deviceModelNameOptions,
            optionLabel: "value",
            optionValue: "value",
            filterable: true,
          },
          rules: [
            { required: true, message: "质保供应商不能为空", trigger: "blur" },
          ],
        },
      ];
    },
    config() {
      return [
        {
          key: "deviceNo",
          title: "设备编码",
          type: "input",
          placeholder: "请输入设备编码",
        },
        {
          key: "runStatus",
          title: "枪运行状态",
          type: "select",
          placeholder: "请选择枪运行状态",
          options: this.runStatusOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
        },
        {
          key: "gunBrandId",
          title: "设备品牌",
          type: "select",
          options: this.brandOptions,
          optionLabel: "brandName",
          optionValue: "brandId",
          placeholder: "请选择设备品牌",
        },
        {
          key: "rangeTime",
          title: "投运日期",
          type: "dateRange",
          placeholder: "请选择投运日期",
          startPlaceholder: "开始日期",
          endPlaceholder: "结束日期",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请输入设备名称",
        },
        {
          key: "operStatus",
          title: "枪运营状态",
          type: "select",
          options: this.operStatusOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
          placeholder: "请选择枪运营状态",
        },
        {
          key: "gunModelId",
          title: "设备型号",
          type: "select",
          options: this.modelOptions,
          optionLabel: "modelName",
          optionValue: "modelId",
          placeholder: "请选择设备型号",
        },
        {
          key: "region",
          title: "省市区",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
          props: {
            multiple: false,
          },
        },
        // {
        //   key: "pileNo",
        //   title: "充电桩编码",
        //   type: "input",
        //   placeholder: "请输入充电桩编码",
        // },
        {
          key: "stationNo",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "subTypeCode",
          title: "设备类型",
          type: "select",
          placeholder: "请选择设备类型",
          options: this.subTypeOptions,
          optionLabel: "dictValue",
          optionValue: "dictKey",
        },
        // {
        //   key: "pileName",
        //   title: "充电桩名称",
        //   type: "input",
        //   placeholder: "请输入充电桩名称",
        // },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "operationMode",
          title: "运营类型",
          type: "select",
          placeholder: "请选择运营类型",
          options: this.operationModeOptions,
        },
      ];
    },
  },
};
</script>

<style></style>
