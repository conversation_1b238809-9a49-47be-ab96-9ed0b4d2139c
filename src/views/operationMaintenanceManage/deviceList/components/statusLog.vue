<template>
  <el-dialog
    title="状态日志"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    width="70%"
  >
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="true"
      @handleExport="handleExport"
    >
      <template slot="timeRange">
        <el-date-picker
          v-model="searchForm.timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="float: right"
          value-format="yyyy-MM-dd"
          :clearable="false"
          :picker-options="pickerOptions"
          @blur="resetDisableDate"
        >
        </el-date-picker>
      </template>
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
      </GridTable>
    </el-card>
  </el-dialog>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import moment from "moment";
import GridTable from "@/components/GridTable/index.vue";
import { getToken } from "@/utils/auth";

import {
  queryStatusLog,
  exportLogExcel,
} from "@/api/operationMaintenanceManage/deviceList/index.js";
export default {
  components: { AdvancedForm, GridTable },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    gunId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      token: "",
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        timeRange: ["", ""],
      },
      total: 0,
      loading: false,
      tableId: "statusLogList",
      columns: [
        {
          field: "dataTime",
          title: "上报时间",
        },
        // {
        //   field: "gunName",
        //   title: "状态码",
        // },
        {
          field: "runStatusName",
          title: "上报信息",
        },
        // {
        //   field: "runStatusName",
        //   title: "原始报文",
        // },
      ],
      disabledCurrent: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log("onPick", maxDate, minDate);
          if (!maxDate) {
            this.disabledCurrent = minDate;
          }
        },
        disabledDate: (current) => {
          if (!this.disabledCurrent) return false;
          return (
            (current &&
              current <
                moment(this.disabledCurrent)
                  .subtract(1, "Y")
                  .startOf("day")) ||
            current >
              moment(this.disabledCurrent)
                .add(1, "Y")
                .endOf("day")
          );
        },
      },
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetQuery();
      }
    },
  },
  computed: {
    config() {
      return [
        {
          key: "timeRange",
          title: "上报日期",
          type: "slot",
          slotName: "timeRange",
          colNum: 12,
        },
      ];
    },
  },
  created() {
    this.token = getToken();
  },
  methods: {
    // 每次失焦重置disableDate
    resetDisableDate() {
      this.disabledCurrent = null;
    },
    closeVisible() {
      this.$emit("close");
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        timeRange: [
          moment()
            .subtract(6, "days")
            .format("YYYY-MM-DD"),
          moment().format("YYYY-MM-DD"),
        ],
      };
      this.getList();
    },
    getList() {
      this.loading = true;
      console.log(this.searchForm, "查询");
      let params = {
        ...this.searchForm,
        startDataDay: this.searchForm.timeRange?.[0] || "",
        endDataDay: this.searchForm.timeRange?.[1] || "",
        gunId: this.gunId,
      };

      queryStatusLog(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      let params = {
        ...this.searchForm,
        startDataDay: this.searchForm.timeRange?.[0] || "",
        endDataDay: this.searchForm.timeRange?.[1] || "",
        gunId: this.gunId,
      };
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportLogExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    changePage() {
      this.getList();
    },
  },
};
</script>

<style></style>
