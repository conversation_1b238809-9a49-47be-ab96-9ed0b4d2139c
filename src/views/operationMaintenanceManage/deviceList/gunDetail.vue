//充电枪详情页
<template>
  <div class="app-container">
    <h4>设备编码:{{ deviceNo }}</h4>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <!-- 基本信息tab-S -->
      <el-tab-pane label="基本信息" name="info">
        <!-- 枪信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>枪信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in infoList"
              :key="index"
              :label="item.title"
            >
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 枪信息-E -->
        <!-- 桩信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>桩信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in pileList"
              :key="index"
            >
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 桩信息-E -->
        <!-- 桩计费信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>桩计费信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item v-for="(item, index) in feeList" :key="index">
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 桩计费信息-E -->
      </el-tab-pane>
      <!-- 基本信息tab-E -->
      <!-- 日志记录tab-S -->
      <!-- <el-tab-pane label="日志记录" name="record">
        <el-card style="margin-bottom: 10px;">
          <div
            slot="header"
            class="card-title-wrap"
            style="justify-content: space-between"
          >
            <div style="display: flex;">
              <div class="card-title-line"></div>
              <span>枪上报的日志记录</span>
            </div>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="float: right"
              value-format="yyyy-MM-dd"
              @change="handleDateChange"
              :clearable="false"
              :picker-options="pickerOptions"
              @blur="resetDisableDate"
            >
            </el-date-picker>
          </div>
          <GridTable
            :columns="columns"
            :tableData="tableData"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="total"
            @changePage="getRecord"
            :loading="loading"
            :tableId="tableId"
          ></GridTable>
        </el-card>
      </el-tab-pane> -->
      <!-- 日志记录tab-E -->
    </el-tabs>
  </div>
</template>

<script>
import {
  queryGunInfo,
  queryGunRecord,
} from "@/api/operationMaintenanceManage/deviceList/index.js";
// import GridTable from "@/components/GridTable/index.vue";
import moment from "moment";
export default {
  components: {
    // GridTable,
  },
  data() {
    return {
      columns: [
        {
          field: "dataTime",
          title: "上报时间",
        },
        {
          field: "runStatusName",
          title: "上报内容",
        },
      ],
      tableData: [],
      dateRange: ["", ""],
      searchForm: { pageNum: 1, pageSize: 10 },
      total: 0,
      loading: false,
      tableId: "gunDetailRecordList",
      activeName: "info",
      deviceNo: undefined,
      infoList: [],
      feeList: [],
      pileList: [],
      searchParams: {},
      disabledCurrent: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log("onPick", maxDate, minDate);
          if (!maxDate) {
            this.disabledCurrent = minDate;
          }
        },
        disabledDate: (current) => {
          if (!this.disabledCurrent) return false;
          return (
            (current &&
              current <
                moment(this.disabledCurrent)
                  .subtract(1, "Y")
                  .startOf("day")) ||
            current >
              moment(this.disabledCurrent)
                .add(1, "Y")
                .endOf("day")
          );
        },
      },
    };
  },
  created() {
    const { deviceNo, ...rest } = this.$route.query;
    this.deviceNo = deviceNo;
    this.searchParams = { ...rest };
    this.getInfo();
  },
  methods: {
    // 每次失焦重置disableDate
    resetDisableDate() {
      this.disabledCurrent = null;
    },
    getInfo() {
      queryGunInfo(this.searchParams).then((res) => {
        const { gunDetailVOS, chargingPileBillingVO, ...rest } = res.data;
        this.infoList = [
          { title: "枪编码", value: gunDetailVOS[0]?.gunNo },
          { title: "枪名称", value: gunDetailVOS[0]?.gunName },
          { title: "品牌", value: gunDetailVOS[0]?.gunBrandName },
          { title: "型号", value: gunDetailVOS[0]?.gunModelName },
          { title: "充电接口类型", value: gunDetailVOS[0]?.couplertypeName },
          { title: "均摊功率（kW）", value: gunDetailVOS[0]?.amortizedPower },
          { title: "额定功率（kW）", value: gunDetailVOS[0]?.ratePower },
          { title: "充电堆枪口编号", value: gunDetailVOS[0]?.stackGunNo },
          { title: "运行状态", value: gunDetailVOS[0]?.runStatusName },
          { title: "运营状态", value: gunDetailVOS[0]?.operStatusName },
          { title: "站点名称", value: rest?.stationName },
          { title: "站点编码", value: rest?.stationNo },
          { title: "设备类型", value: rest?.subTypeName },
          {
            title: "运营类型",
            value:
              rest?.operationMode == "1"
                ? "自营"
                : rest?.operationMode == "2"
                ? "代运营"
                : "",
          },
          { title: "省市区", value: rest?.belongPlace },
          { title: "详细地址", value: rest?.address },
        ];
        this.pileList = [
          { title: "充电桩编号", value: rest?.pileNo },
          { title: "充电桩名称", value: rest?.pileName },
          { title: "站点名称", value: rest?.stationName },
          { title: "站点编码", value: rest?.stationNo },
          { title: "设备类型", value: rest?.subTypeName },
          { title: "设备型号", value: rest?.modelName },
          { title: "设备品牌", value: rest?.brandName },
          { title: "资产编号", value: rest?.assetCode },
          {
            title: "运营类型",
            value:
              rest?.operationMode == "1"
                ? "自营"
                : rest?.operationMode == "2"
                ? "代运营"
                : "",
          },
          { title: "资产单位", value: rest?.operatorName },
          { title: "站点运营状态", value: rest?.operationStatusName },
          { title: "站点上线日期", value: rest?.onlineDate },
          { title: "桩投运日期", value: rest?.operDate },
          { title: "通讯模块协议", value: rest?.protocolName },
          { title: "桩通讯地址", value: rest?.pilePostalAddress },
          {
            title: "是否开放",
            value:
              rest?.openFlag == "1" ? "是" : rest?.openFlag == "0" ? "否" : "",
          },
        ];
        this.feeList = [
          {
            title: "当前计费名称",
            value:
              (chargingPileBillingVO?.chcName || "") +
              "-" +
              (chargingPileBillingVO?.chcNo || ""),
          },
          { title: "计费模型编码", value: chargingPileBillingVO?.chcNo },
          {
            title: "操作人",
            value: chargingPileBillingVO?.updateUser,
          },
          { title: "操作时间", value: chargingPileBillingVO?.updateTime },
          {
            title: "最后下发计费名称",
            value:
              (chargingPileBillingVO?.planChcName || "") +
              "-" +
              (chargingPileBillingVO?.planChcNo || ""),
          },
          {
            title: "最后下发时间",
            value: chargingPileBillingVO?.lastDistributeTime,
          },
          {
            title: "最后下发状态",
            value: chargingPileBillingVO?.issueStatusName,
          },
        ];
      });
    },
    getRecord() {
      const params = {
        gunId: this.$route.query.gunId,
        startDataDay: this.dateRange?.[0] || "",
        endDataDay: this.dateRange?.[1] || "",
        ...this.searchForm,
      };
      this.loading = true;
      console.log(params, "params");
      queryGunRecord(params).then((res) => {
        if (res.code === "10000") {
          this.tableData = res.data;
          this.total = res.total;
          this.loading = false;
        } else {
          this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    handleClick(val) {
      console.log(val.name, "点击");
      if (val.name === "info") {
        this.getInfo();
      } else {
        this.dateRange = [
          moment()
            .subtract(6, "days")
            .format("YYYY-MM-DD"),
          moment().format("YYYY-MM-DD"),
        ];
        this.getRecord();
      }
    },

    handleDateChange() {
      this.searchForm = { pageNum: 1, pageSize: 10 };
      this.getRecord();
    },
  },
};
</script>

<style lang="less" scoped>
.descriptions {
  margin: 0 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
  }
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic .head {
  margin-bottom: 12px;
}
</style>
