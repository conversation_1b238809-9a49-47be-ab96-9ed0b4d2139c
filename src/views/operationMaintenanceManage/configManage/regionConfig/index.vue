// 区域配置
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="rowAdd"
          v-has-permi="['maintenance:regionConfig:add']"
          >新增配置</el-button
        >
      </template>
      <template slot="cityDetail" slot-scope="{ row }">
        <el-button type="text" size="large" @click="handleCityDetail(row)"
          >查看</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          v-has-permi="['maintenance:timeConfig:export']"
          >导出
        </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
    <el-dialog
      title="查看所属省市"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="visible = false"
      append-to-body
      width="50%"
    >
      <el-tree
        :data="cityDetailTree"
        :props="defaultProps"
        default-expand-all
      ></el-tree>
    </el-dialog>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/operationMaintenanceManage/configManage/regionConfig.js";
import { queryCityTree } from "@/api/common.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { getDeptList } from "@/api/operationWorkOrder/index.js";

export default {
  name: "regionConfigPage",
  mixins: [exportMixin],
  data() {
    return {
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],
      tableColumn: [
        {
          field: "regionName",
          title: "能投大区",
        },
        {
          field: "orderName",
          title: "所属省市",
          slots: { default: "cityDetail" },
        },
        {
          field: "nickName",
          title: "更新人",
        },
        {
          field: "updateTime",
          title: "更新时间",
        },
      ],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //buse参数-e
      deptOptionList: [],
      regionData: [],
      cityData: [],
      visible: false,
      defaultProps: {
        children: "children",
        value: "areaCode",
        label: "areaName",
      },
      cityDetailTree: [],
      deptPermiOptionList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        //默认显示筛选项的个数
        showCount: 5,
        layout: "right",
        inline: true,
        labelWidth: "130px",
        //筛选控件配置
        config: [
          {
            field: "regionOrgList",
            title: "能投大区",
            element: "el-select",
            props: {
              options: this.deptOptionList,
              optionLabel: "deptName",
              optionValue: "deptId",
              filterable: true,
              multiple: true,
            },
          },
          {
            field: "region",
            title: "所属省市",
            element: "custom-cascader",
            attrs: {
              options: this.regionData,
              props: {
                multiple: true,
                value: "areaCode",
                label: "areaName",
              },
              clearable: true,
              filterable: true,
              collapseTags: true,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        addTitle: "新增配置",
        editBtn: checkPermission(["maintenance:regionConfig:edit"]),
        editTitle: "编辑配置",
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "regionOrg",
            title: "能投大区",
            element: "el-select",
            props: {
              //这里是通过接口异步获取，也可以直接在这写死
              options: this.deptPermiOptionList,
              optionLabel: "deptName",
              optionValue: "deptId",
              filterable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择能投大区",
              },
            ],
            on: {
              change: (val) => {
                this.handleDeptChange(val, "modal");
              },
            },
          },
          {
            field: "region",
            title: "所属省市",
            element: "custom-cascader",
            attrs: {
              options: this.cityData,
              props: {
                multiple: true,
                value: "areaCode",
                label: "areaName",
              },
              filterable: true,
              collapseTags: true,
              clearable: true,
            },
            rules: [
              {
                required: true,
                message: "请选择所属省市",
              },
            ],
          },
        ],
        customOperationTypes: [
          // {
          //   title: "配置站点",
          //   typeName: "stationConfig",
          //   event: (row) => {
          //     return this.handleStationConfig(row);
          //   },
          //   condition: (row) => {
          //     return checkPermission(["maintenance:timeConfig:stationConfig"]);
          //   },
          // },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
        },
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
  },
  mounted() {
    Promise.all([
      this.getCityRegionData(),
      this.getDeptList(),
      // this.getDicts("order_business_type").then((response) => {
      //   this.businessTypeOptions = response.data;
      // }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.loadData();
        });
      }, 500);
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    checkPermission,
    handleCityDetail(row) {
      this.visible = true;
      api.cityDetail({ regionOrg: row.regionOrg }).then((res) => {
        this.cityDetailTree = res.data;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    //去除children为空的children字段
    cleanTree(arr) {
      return arr.map((item) => {
        const newItem = { ...item };
        if (newItem.children) {
          newItem.children = this.cleanTree(newItem.children);
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }
        return newItem;
      });
    },
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data;
      });
    },
    getPermiDeptList(regionOrg) {
      api.getPermiDeptList({ regionOrg: regionOrg }).then((res) => {
        this.deptPermiOptionList = res.data;
      });
    },
    handleExport() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleCommonExport(api.export, params);
    },
    handleDeptChange(val) {
      this.$refs.crud.setFormFields({ region: undefined });
      if (val) {
        api.queryFilterCity({ regionOrg: val }).then((res) => {
          this.cityData = this.cleanTree(res.data);
        });
      } else {
        this.cityData = [];
      }
    },
    handleRegionParam(params, province = "provinceList", city = "cityList") {
      if (Array.isArray(params.region)) {
        params[province] = [];
        params[city] = [];
        // params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params[province].push(item[0]);
          }
          if (item[1]) {
            params[city].push(item[1]);
          }
          if (item[2]) {
            // params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.handleRegionParam(params);
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      console.log(crudOperationType, formParams, "提交");
      let params = { ...formParams };
      this.handleRegionParam(params, "province", "city");
      params.regionName = this.deptOptionList?.find(
        (x) => x.deptId == params.regionOrg
      )?.deptName;
      //crudOperationType:add/update
      const res = await api[crudOperationType](params);
      if (res.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    rowAdd() {
      this.cityData = [];
      this.getPermiDeptList();
      this.$refs.crud.switchModalView(true, "ADD");
    },
    rowEdit(row) {
      if (row.regionOrg) {
        api.queryFilterCity({ regionOrg: row.regionOrg }).then((res) => {
          this.cityData = this.cleanTree(res.data);
        });
        this.getPermiDeptList(row.regionOrg);
      }
      const params = {
        ...row,
        region: row.provinceList?.map((item, index) => {
          return [item, row.cityList[index]];
        }),
        oldRegionOrg: row.regionOrg,
      };
      this.$refs.crud.switchModalView(true, "UPDATE", params);
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
</style>
