<!-- 表单设计器 -->
<template>
  <div class="">
    <el-row type="flex" justify="end">
      <!-- <el-button @click="handleDownloadRule()" type="primary" style="margin: 10px"> 生成表单JSON </el-button>
      <el-button @click="handleDownloadOption()" type="primary" style="margin: 10px"> 生成表单配置 </el-button> -->
      <!-- <el-button @click="handleUploadRule()" type="primary" style="margin: 10px"> 导入表单JSON </el-button>
      <el-button @click="handleUploadOption()" type="primary" style="margin: 10px"> 导入表单配置 </el-button> -->
      <el-button @click="handleSave" type="primary" style="margin: 10px">
        保存</el-button
      >
    </el-row>
    <el-row class="top-desc">
      <el-col :span="2" style="text-align: right;">特殊说明：</el-col>
      <el-col :span="22">
        <div>
          表单组件如需在工勘工单报告中展示，需要在组件配置中将该字段ID按照既定规范填入对应的英文标识，否则无法获取。工勘工单报告目前仅支持以下9个字段：
        </div>
        <div class="top-desc-content">
          <div>1、变压器台数：transformerCount</div>
          <div>2、高低压是否有联络：isContact</div>
          <div>3、穿CT铜牌规格：standards</div>
          <div>4、变压器铭牌：nameplateImg</div>
          <div>5、并网点位置：networkImg</div>
          <div>6、储能位置：energyImg</div>
          <div>7、线路铺设：lineImg</div>
          <div>8、穿CT铜牌照片：medalImg</div>
          <div>9、备注：remark</div>
        </div>
      </el-col>
    </el-row>
    <fc-designer ref="designer" height="780px" :mask="false"></fc-designer>

    <el-dialog :title="title" :visible.sync="visible" width="500px">
      <el-form :model="form" ref="form" :rules="rules" label-width="80px">
        <el-form-item label="表单标题" prop="formTitle">
          <el-input v-model="form.formTitle" />
        </el-form-item>
        <el-form-item label="表单平台" prop="formPlat">
          <el-select style="width: 100%" v-model="form.formPlat">
            <el-option
              v-for="dict in formPlatOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="form.formDesc" :rows="5" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div>
          <el-button type="primary" @click="submit">确 定</el-button>
          <el-button @click.stop="visible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  formSave,
  getFormData,
  getAppUploadPath,
} from "@/api/demandPool/formDesign.js";
//自定义电子签名组件生成规则
import { signboard } from "@/components/FormCreate/rules/signBoardCreateRule";
import { cameraPhotoUpload } from "@/components/FormCreate/rules/CameraPhotoUploadCreateRule";
import { locate } from "@/components/FormCreate/rules/LocateRule";
import { formGroup } from "@/components/FormCreate/rules/FormGroupRule";
import { formRow } from "@/components/FormCreate/rules/FormRowRule";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      text: "",
      dialogTitle: "", // 对话框标题
      dialogState: false, // 对话框状态
      dialogMenu: false, // 对话框菜单状态

      fApi: {},
      foptions: {},
      frule: [],

      // codemirror配置
      codemirrorOptions: {
        mode: "application/json",
        theme: "default",
        gutters: ["CodeMirror-lint-markers"],
        tabSize: 2,
        lint: true,
        line: true,
        lineNumbers: true,
        matchBrackets: true,
        lineWrapping: true,
        styleActiveLine: true,
        readOnly: false,
      },
      // codemirror内容
      codemirrorContent: null,

      title: "",
      visible: false,
      form: {
        formTitle: "",
        formPlat: "",
        formDesc: "",
      },
      rules: {
        formTitle: [
          { required: true, message: "请输入表单标题", trigger: "blur" },
        ],
        formPlat: [
          { required: true, message: "请选择表单平台", trigger: "change" },
        ],
      },
      formPlatOptions: [
        {
          dictValue: "PC",
          dictLabel: "PC",
        },
        {
          dictValue: "APP",
          dictLabel: "移动端",
        },
        {
          dictValue: "ALL",
          dictLabel: "全部",
        },
      ],
      actionUrl: "", //图片上传地址
    };
  },
  computed: {},
  watch: {
    visible(newVal) {
      if (!newVal) {
        this.form = {};
      }
    },
  },
  //方法集合
  methods: {
    //获取表单数据
    getFormData() {
      let params = {
        formId: this.$route.query.formId,
      };
      getFormData(params).then((res) => {
        this.foptions = JSON.parse(res.data.formConfig);
        this.frule = JSON.parse(res.data.formJson);
        //回显数据
        this.$refs.designer.setRule(this.frule);
        this.$refs.designer.setOption(this.foptions);
      });
    },
    delVisitedView() {
      this.$store.dispatch("tagsView/delVisitedView", this.$route);
    },
    //保存表单
    handleSave() {
      let formId = this.$route.query.formId;
      if (formId) {
        this.formSave();
      } else {
        this.visible = true;
      }
    },
    //保存表单
    formSave() {
      this.handleDownloadRule();
      this.handleDownloadOption();
      let params = {
        formKey: this.$route.query.formKey,
        formId: this.$route.query.formId,
        formJson: this.handleDownloadRule(),
        formConfig: this.handleDownloadOption(),
      };
      formSave(params).then((res) => {
        this.$message.success("保存成功！");
        this.delVisitedView();
        this.$router.back();
      });
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
            formKey: this.$route.query.formKey,
            formJson: this.handleDownloadRule(),
            formConfig: this.handleDownloadOption(),
          };
          formSave(params).then((res) => {
            this.$message.success("保存成功！");
            this.delVisitedView();
            this.$router.back();
          });
        }
      });
    },
    //获取表单图片上传地址
    async getAppUploadPath() {
      let { data = "" } = await getAppUploadPath();
      this.actionUrl = data;
    },
    // 导出表单JSON
    handleDownloadRule() {
      this.dialogTitle = "表单规则";

      this.codemirrorOptions.readOnly = true;
      let rules = this.$refs.designer.getRule();
      rules.forEach((item) => this.handleFormRule(item));
      console.log("rules", rules);
      this.codemirrorContent = JSON.stringify(rules, null, 2);
      return this.codemirrorContent;
    },
    handleFormRule(item) {
      //官方内置组件的field为自动生成的，首字符可能为数字
      //field如果以数字开头会影响排他网关的判断，如果field以数字开头时手动添加一个字符a
      if (item.field) {
        let fieldStartWithNumber = /^[0-9]/.test(item.field);
        if (fieldStartWithNumber) {
          item.field = "a" + item.field;
        }
      }

      if (item.type == "upload" || item.type == "CameraPhotoUpload") {
        //CameraPhotoUpload水印相机
        this.$set(item.props, "name", "file");
        if (
          item.props?.uploadType == "file" ||
          item.props?.uploadType == "image"
        ) {
        } else {
          this.$set(item.props, "uploadType", "image");
        }
        // let env = process.env.NOOD_ENV == "production" ? "" : "saas-water"

        this.$set(item.props, "action", process.env.VUE_APP_BASE_UPLOAD_URL);

        this.$set(item.props, "onSuccess", function(res, file, fileList) {
          file.url = res.data;
          console.log("file", file);
        });
      }
      //自定义电子签名组件，保存组件规则时去掉component属性，否则根据规则动态绘制菜单时找不到对应路径的组件会报错
      //动态绘制时通过formcreate注入组件的方式提前注入自定义组件
      if (
        item.type === "SignBoard" ||
        item.type == "CameraPhotoUpload" ||
        item.type == "Locate" ||
        item.type == "FormGroup"
      ) {
        Reflect.deleteProperty(item, "component");
      }
      // 递归设置表单规则
      if (item.children && item.children.length > 0) {
        item.children.map((object) => this.handleFormRule(object));
      }
    },
    // 导出表单配置
    handleDownloadOption() {
      this.dialogTitle = "表单配置";

      this.codemirrorOptions.readOnly = true;
      let option = this.$refs.designer.getOption();
      option.submitBtn = false; //隐藏提交按钮
      this.codemirrorContent = JSON.stringify(option, null, 2);
      return this.codemirrorContent;
    },
    // 导入表单JSON
    handleUploadRule(arr) {
      this.dialogTitle = "导入表单规则";
      // this.dialogState = true;
      // this.dialogMenu = true;

      // this.codemirrorOptions.readOnly = false;
      // this.codemirrorContent = JSON.stringify(arr, null, 2);
    },
    // 导入表单配置
    handleUploadOption(obj) {
      this.dialogTitle = "导入表单配置";
      // this.dialogState = true;
      // this.dialogMenu = true;

      // this.codemirrorOptions.readOnly = false;
      // this.codemirrorContent = JSON.stringify(obj, null, 2);
    },
    // 配置导入
    handleImport() {
      try {
        let content = JSON.parse(this.codemirrorContent);
        if (this.dialogTitle == "导入表单规则") {
          this.$refs.designer.setRule(content);
        }
        if (this.dialogTitle == "导入表单配置") {
          this.$refs.designer.setOptions(content);
        }
        this.dialogState = false;
      } catch (e) {
        alert("输入内容格式有误!");
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.$nextTick(() => {
      //插入组件规则
      this.$refs.designer.addComponent(signboard);
      this.$refs.designer.addComponent(cameraPhotoUpload);
      this.$refs.designer.addComponent(locate);
      this.$refs.designer.addComponent(formGroup);
      // this.$refs.designer.addComponent(formRow);

      //插入自定义菜单
      this.$refs.designer.addMenu({
        title: "自定义组件",
        name: "custom",
        list: [
          {
            icon: signboard.icon,
            name: signboard.name,
            label: signboard.label,
          },
          {
            icon: cameraPhotoUpload.icon,
            name: cameraPhotoUpload.name,
            label: cameraPhotoUpload.label,
          },
          {
            icon: locate.icon,
            name: locate.name,
            label: locate.label,
          },
          {
            icon: formGroup.icon,
            name: formGroup.name,
            label: formGroup.label,
          },
          // {
          //   icon: formRow.icon,
          //   name: formRow.name,
          //   label: formRow.label
          // }
        ],
      });
    });
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  async mounted() {
    if (this.$route.query.isNew != "true") {
      this.getFormData();
    }
    await this.getAppUploadPath();
  },
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ aside {
  padding: 0;
  background: none;
}
.top-desc {
  color: red;
  font-size: 14px;
  line-height: 28px;
  &-content {
    display: flex;
    flex-wrap: wrap;
    > div {
      margin-right: 20px;
    }
  }
}
</style>
