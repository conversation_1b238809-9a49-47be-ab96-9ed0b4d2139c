//检查项
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="queryParams"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    ></AdvancedForm>

    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData1"
        :currentPage.sync="queryParams.pageNum"
        :pageSize.sync="queryParams.pageSize"
        :total.sync="pageTotal1"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{ xToolbarData }">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            style="margin: 10px 0"
            @click="handleAdd"
            >新增
          </el-button>
        </template>
        <!-- 创建时间 -->
        <template slot="createTime" slot-scope="{ row }">
          {{ moment(row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
        <!-- 修改时间 -->
        <template slot="updateTime" slot-scope="{ row }">
          {{ moment(row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
        <!-- 状态 -->
        <template slot="formStatus" slot-scope="{ row }">
          {{ row.formStatus == "1" ? "启用" : "未启用" }}
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click.stop="handleEdit(row)"
            style="margin-right: 10px"
            >编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            :disabled="row.formType === '1'"
            @click.stop="goToFormCreate(row)"
            style="margin-right: 10px"
            >新增版本
          </el-button>
          <el-button
            type="text"
            size="large"
            :disabled="row.formType === '1'"
            @click.stop="formView(row)"
            style="margin-right: 10px"
            >版本管理
          </el-button>
          <el-button
            type="text"
            size="large"
            :disabled="row.mrStatusCode === '01'"
            @click.stop="deleteItem(row)"
            >删除
          </el-button>
          <el-button
            type="text"
            size="large"
            :disabled="row.formType === '1'"
            @click.stop="handleCopy(row)"
            style="margin-right: 10px"
            >复制
          </el-button>
          <el-button
            type="text"
            size="large"
            @click.stop="handleBusiness(row)"
            style="margin-right: 10px"
            >业务属性
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <el-dialog
      :title="title"
      v-if="visibleAdd"
      :visible.sync="visibleAdd"
      @close="handleCancel"
      width="500px"
    >
      <el-form :model="form" ref="form" :rules="rules" label-width="110px">
        <el-form-item label="检查项名称" prop="formName">
          <el-input v-model="form.formName" />
        </el-form-item>

        <el-form-item label="表单类型" prop="formType">
          <el-radio
            v-model="form.formType"
            label="0"
            @change="handleOptionChange"
            :disabled="form.defId != ''"
            >表单设计器</el-radio
          >
          <!-- <el-radio
            v-model="form.formType"
            label="1"
            @change="handleOptionChange"
            :disabled="form.defId != ''"
            >自定义表单</el-radio
          > -->
        </el-form-item>

        <el-form-item label="表单编码" prop="formKey">
          <el-input v-model="form.formKey" :disabled="form.formType === '0'" />
        </el-form-item>

        <el-form-item label="检查项描述" prop="formDefDesc">
          <el-input
            type="textarea"
            v-model="form.formDefDesc"
            :rows="5"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div>
          <el-checkbox
            v-if="!visibleCopy"
            :disabled="form.defId != ''"
            v-model="isOpenChecked"
            >添加后自动打开设计</el-checkbox
          >
        </div>
        <div>
          <el-button type="primary" @click="submit">确 定</el-button>
          <el-button @click="handleCancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="版本管理" :visible.sync="visible" width="70%">
      <el-table v-loading="loading" ref="multipleTable" :data="tableData">
        <el-table-column
          label="表单标题"
          align="center"
          prop="formTitle"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="平台"
          align="center"
          prop="formPlat"
          :show-overflow-tooltip="true"
          :formatter="formatFormPlat"
        />
        <el-table-column label="版本号" align="center" prop="formVersion" />
        <el-table-column
          label="状态"
          align="center"
          prop="formStatus"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <el-switch
              @change="changeFormStatus(scope.row)"
              v-model="scope.row.formStatus"
              active-value="1"
              inactive-value="2"
              active-color="#3f9eff"
              inactive-color="#c0c2be"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="formDesc"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建人"
          align="center"
          prop="formCreatorName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column
          label="更新时间"
          align="center"
          prop="updateTime"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          prop="updateTime"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              @click.stop="editFormDesign(scope.row)"
              style="margin-right: 10px"
              >表单设计
            </el-link>
            <el-link
              type="primary"
              @click.stop="edit(scope.row)"
              style="margin-right: 10px"
              >编辑
            </el-link>
            <el-link
              type="primary"
              @click.stop="copy(scope.row)"
              style="margin-right: 10px"
              >复制
            </el-link>
            <el-link type="primary" @click.stop="deleteInner(scope.row)"
              >删除
            </el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog
        title=""
        :visible.sync="innerVisible"
        append-to-body
        width="30%"
      >
        <el-form :model="form2" ref="form2" :rules="rules2" label-width="80px">
          <el-form-item label="表单标题" prop="formTitle">
            <el-input v-model="form2.formTitle" />
          </el-form-item>
          <el-form-item label="表单平台" prop="formPlat">
            <el-select style="width: 100%" v-model="form2.formPlat">
              <el-option
                v-for="dict in formPlatOptions"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form2.formDesc" :rows="5" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <div></div>
          <div>
            <el-button type="primary" @click="innerSubmit">确 定</el-button>
            <el-button @click.stop="innerVisible = false">取 消</el-button>
          </div>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <div></div>
        <div>
          <!-- <el-button type="primary" @click="submit">确 定</el-button> -->
          <el-button @click.stop="visible = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="业务属性"
      v-if="businessVisible"
      :visible.sync="businessVisible"
      @close="handleBusinessCancel"
      width="500px"
    >
      <el-form
        :model="businessForm"
        ref="businessForm"
        :rules="businessRules"
        label-width="110px"
      >
        <el-form-item label="业务类型" prop="businessTypes">
          <el-select
            v-model="businessForm.businessTypes"
            filterable
            clearable
            multiple
            style="width: 100%"
          >
            <el-option
              :label="item.dictLabel"
              v-for="item in businessTypeOptions"
              :key="item.dictValue"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查对象" prop="checkTargets">
          <el-checkbox-group v-model="businessForm.checkTargets">
            <el-checkbox
              :label="item.dictValue"
              v-for="item in checkObjectOptions"
              :key="item.dictValue"
              >{{ item.dictLabel }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="检查项属性" prop="itemAttribute">
          <el-autocomplete
            v-model="businessForm.itemAttribute"
            clearable
            style="width: 100%"
            :fetch-suggestions="querySearch"
          >
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="设备类型" prop="equipType">
          <el-select
            v-model="businessForm.equipType"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              :label="item.dictLabel"
              v-for="item in deviceTypeOptions"
              :key="item.dictValue"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div></div>
        <div>
          <el-button type="primary" @click="businessSubmit">确 定</el-button>
          <el-button @click="handleBusinessCancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  add,
  update,
  copy,
  deleteformdef,
  deleteItem,
  formSave,
  getKey,
  list,
  modifyStatus,
  copyFormDef,
  queryVersionList,
  submitBusiness,
  queryPropOptions,
} from "@/api/operationMaintenanceManage/configManage/checkItems.js";
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
export default {
  name: "checkItemsManage",
  //import引入的组件需要注入到对象中才能使用
  components: { GridTable, AdvancedForm },
  data() {
    //这里存放数据
    return {
      businessForm: {
        businessTypes: [],
        checkTargets: [],
        equipType: "",
        itemAttribute: "",
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        formName: "",
        status: "",
      },
      finallySearch: null,
      formStatusOptions: [
        {
          dictValue: "1",
          dictLabel: "启用",
        },
        {
          dictValue: "2",
          dictLabel: "未启用",
        },
      ],
      loading: false,
      tableData1: [],
      tableData: [],
      pageTotal1: 0,
      pageTotal: 0,
      pageNum1: 1,
      pageNum: 1,
      pageSize1: 10,
      pageSize: 10,
      title: "新增",
      copyFormDefId: "",
      defId: "",
      visibleAdd: false,
      visibleCopy: false,
      visible: false,
      form: {
        formType: "0",
        formName: "",
        formKey: "",
        formDefDesc: "",
        defCreator: "1",
        defLastOperator: "1",
      },
      rules: {
        formName: [
          { required: true, message: "请输入表单名称", trigger: "blur" },
        ],
        formKey: [
          { required: true, message: "请输入表单编码", trigger: "blur" },
        ],
        formType: [
          { required: true, message: "请选择表单类型", trigger: "blur" },
        ],
        formDefDesc: [
          { required: true, message: "请输入检查项描述", trigger: "blur" },
        ],
      },
      form2: {
        formTitle: "",
        formPlat: "",
        formDesc: "",
        formKey: "",
        formId: "",
      },
      rules2: {
        formTitle: [
          { required: true, message: "请输入表单标题", trigger: "blur" },
        ],
        formPlat: [
          { required: true, message: "请输入表单平台", trigger: "change" },
        ],
      },
      isOpenChecked: false,
      innerVisible: false,
      formPlatOptions: [
        {
          dictValue: "PC",
          dictLabel: "PC",
        },
        {
          dictValue: "APP",
          dictLabel: "移动端",
        },
        {
          dictValue: "ALL",
          dictLabel: "全部",
        },
      ],
      // config: [],
      tableId: "formCreateTable",
      columns: [
        {
          field: "formName",
          title: "检查项名称",
          showOverflowTooltip: true,
        },
        {
          field: "formDefDesc",
          title: "检查项描述",
          customWidth: 220,
        },
        {
          field: "businessType",
          title: "业务类型",
          customWidth: 200,
        },
        {
          field: "itemAttribute",
          title: "检查项属性",
          customWidth: 120,
        },
        {
          field: "checkTarget",
          title: "检查对象",
        },
        {
          field: "equipType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return (
              this.deviceTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "formTypeValue",
          title: "表单类型",
          showOverflowTooltip: true,
        },
        {
          field: "formKey",
          title: "表单编码",
          showOverflowTooltip: true,
        },
        {
          field: "defCreatorName",
          title: "创建人",
          showOverflowTooltip: true,
        },
        {
          field: "createTime",
          title: "创建时间",
          showOverflowTooltip: true,
          width: 180,
        },
        {
          field: "defLastOperatorName",
          title: "修改人",
          showOverflowTooltip: true,
        },
        {
          field: "updateTime",
          title: "修改时间",
          showOverflowTooltip: true,
          width: 180,
        },
        {
          field: "formStatus",
          title: "状态",
          showOverflowTooltip: true,
          formatter: ({ cellValue }) => {
            return (
              this.formStatusOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          title: "操作",
          minWidth: 300,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      disabled: false,
      businessTypeOptions: [
        // { dictLabel: "充电", dictValue: "1" },
        // { dictLabel: "储能", dictValue: "2" },
        // { dictLabel: "光伏", dictValue: "3" },
      ],
      checkObjectOptions: [
        { dictLabel: "设备", dictValue: "1" },
        { dictLabel: "站点", dictValue: "2" },
      ],
      deviceTypeOptions: [
        { dictLabel: "直流", dictValue: "1" },
        { dictLabel: "交流", dictValue: "2" },
      ],
      businessVisible: false,
    };
  },
  computed: {
    businessRules() {
      return {
        businessTypes: [
          { required: true, message: "请选择业务类型", trigger: "change" },
        ],
        checkTargets: [
          { required: true, message: "请选择检查对象", trigger: "change" },
        ],
        equipType: [
          {
            required: this.businessForm.checkTargets?.some((x) => x == "1"),
            message: "请选择设备类型",
            trigger: "change",
          },
        ],
      };
    },
    config() {
      return [
        {
          key: "formName",
          title: "检查项名称",
          type: "input",
          placeholder: "请输入检查项名称",
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "select",
          placeholder: "请选择业务类型",
          options: this.businessTypeOptions,
        },
        {
          key: "checkTarget",
          title: "检查对象",
          type: "select",
          placeholder: "请选择检查对象",
          options: this.checkObjectOptions,
        },
        {
          key: "equipType",
          title: "设备类型",
          type: "select",
          placeholder: "请选择设备类型",
          options: this.deviceTypeOptions,
        },
        {
          key: "formStatus",
          title: "状态",
          type: "select",
          placeholder: "请选择状态",
          options: this.formStatusOptions,
        },
        {
          key: "itemAttribute",
          title: "检查项属性",
          type: "autocomplete",
          placeholder: "请选择检查项属性",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb);
            },
          },
        },
      ];
    },
  },
  watch: {
    visibleAdd(newVal) {
      if (newVal) {
        this.getKey();
      } else {
        this.form = { formType: "0" };
        this.isOpenChecked = false;
      }
    },
  },
  //方法集合
  methods: {
    querySearch(queryString, cb) {
      queryPropOptions({
        itemAttributeName: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    businessSubmit() {
      this.$refs.businessForm.validate((valid) => {
        if (!valid) {
          return false;
        }
        const params = { ...this.businessForm, defId: this.defId };
        submitBusiness(params).then((res) => {
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.handleBusinessCancel();
            this.getList();
          }
        });
      });
    },
    handleBusinessCancel() {
      this.businessVisible = false;
      this.$refs.businessForm.resetFields();
    },
    handleBusiness(row) {
      this.defId = row.defId;
      this.businessForm = {
        businessTypes: row.businessTypes || [],
        checkTargets: row.checkTargets || [],
        equipType: row.equipType || "",
        itemAttribute: row.itemAttribute || "",
      };
      this.businessVisible = true;
    },

    formTypeFormat(row) {
      if (row.formType == "0") {
        return "表单设计器";
      } else if (row.formType == "1") {
        return "自定义表单";
      }
    },

    handleOptionChange() {
      // 执行其他逻辑
      if (this.form.formType == "0") {
        this.getKey();
      } else {
        this.$set(this.form, "formKey", "");
      }
    },

    async changeFormStatus(row) {
      let params = {
        formId: row.formId,
        status: row.formStatus == "1" ? "2" : "1",
        formKey: row.formKey,
      };
      await modifyStatus(params)
        .then(() => {
          // for (const iterator of this.tableData) {
          //   if (iterator.formId == row.formId) {
          //     iterator.formStatus == "1"
          //       ? (row.formStatus = "2")
          //       : (row.formStatus = "1");
          //   }
          // }
          this.getList();
        })
        .catch(() => {
          if (row.formStatus == 2) {
            row.formStatus = 1;
          } else if (row.formStatus == 1) {
            row.formStatus = 2;
          }
        });
    },
    //二级弹框确认
    innerSubmit() {
      this.$refs.form2.validate((valid) => {
        if (valid) {
          let params = {
            ...this.form2,
          };
          formSave(params).then((res) => {
            this.tableData.forEach((element) => {
              if (element.formId == this.form2.formId) {
                this.$set(element, "formTitle", res.data.formTitle);
                this.$set(element, "formPlat", res.data.formPlat);
                this.$set(element, "formDesc", res.data.formDesc);
                this.$set(element, "formVersion", res.data.formVersion);
                this.$set(element, "updateTime", res.data.updateTime);
              }
            });
            this.innerVisible = false;
          });
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery(params) {
      this.queryParams.pageNum = 1;
      if (params) {
        params.pageSize = this.queryParams.pageSize;
      }
      this.getList(params);
    },
    resetQuery() {
      this.queryParams = this.$options.data.call(this).queryParams;
      this.getList();
    },
    //新增
    handleAdd() {
      this.title = "新增";
      this.defId = "";
      this.form = {
        formType: "0",
        formName: "",
        formKey: "",
        formDefDesc: "",
        defCreator: "1",
        defLastOperator: "1",
        defId: "",
      };
      this.visibleAdd = true;
    },
    //编辑
    handleEdit(row) {
      this.title = "编辑";
      this.visibleAdd = true;
      // this.form = ((this.form)=> (this.form))(row);
      // obj2 = (({ name,age}) => ({ name,age}))(obj1)
      this.form = { ...row };
      this.defId = row.defId;
    },

    handleCopy(row) {
      this.title = "复制";
      this.visibleCopy = true;
      this.visibleAdd = true;
      this.copyFormDefId = row.defId;
    },

    handleCancel() {
      this.visibleAdd = false;
      this.visibleCopy = false;
    },

    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log(this.defId, "defId");
          if (this.visibleCopy) {
            let params = {
              ...this.form,
              defId: this.copyFormDefId,
            };
            copyFormDef(params).then((res) => {
              this.visibleAdd = false;
              this.visibleCopy = false;
              this.getList();
            });
          } else if (!this.defId) {
            add(this.form).then((res) => {
              if (this.isOpenChecked) {
                this.$router.push({
                  path:
                    "/operationMaintenanceManage/configManage/checkItems/formDesign",
                  query: { formKey: this.form.formKey, isNew: true },
                });
              }
              this.visibleAdd = false;
              this.getList();
            });
          } else {
            update({
              defId: this.defId,
              formName: this.form.formName,
              formDefDesc: this.form.formDefDesc,
            }).then((res) => {
              this.visibleAdd = false;
              this.getList();
            });
          }
        }
      });
    },
    getKey() {
      getKey().then((res) => {
        this.$set(this.form, "formKey", res.data);
      });
    },
    //设计表单
    goToFormCreate(row) {
      if (row.platForms && row.platForms.length >= 6) {
        this.$message.info("版本数量已达到上线，无法新建版本");
        return;
      }
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/checkItems/formDesign",
        query: { formKey: row.formKey, isNew: true },
      });
    },
    //版本管理
    formView(row) {
      this.visible = true;
      queryVersionList({ formKey: row.formKey }).then((res) => {
        if (res?.code === "10000") {
          this.tableData = res.data;
        }
      });
    },
    formatFormPlat(row) {
      return this.selectDictLabel(this.formPlatOptions, row.formPlat);
    },
    //外层删除
    deleteItem(row) {
      let that = this;
      this.$confirm(
        '是否确认删除表单名称为"' + row.formName + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then((res) => {
        let params = {
          defId: row.defId,
        };
        deleteformdef(params).then((res) => {
          that.getList();
        });
      });
    },
    //内部删除
    deleteInner(row) {
      let that = this;
      this.$confirm(
        '是否确认删除表单标题为"' + row.formTitle + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then((res) => {
        let params = {
          formId: row.formId,
        };
        deleteItem(params).then((res) => {
          for (let i = 0; i < that.tableData.length; i++) {
            if (that.tableData[i].formId == row.formId) {
              that.tableData.splice(i, 1);
            }
          }
        });
      });
    },
    //复制
    copy(row) {
      if (this.tableData.length >= 6) {
        this.$message.info("版本数量已达到上线，无法复制版本");
        return;
      }
      let params = {
        formId: row.formId,
      };
      copy(params).then((res) => {
        res.data.createTime = new Date();
        res.data.updateTime = res.data.createTime;
        res.data.formCreator = row.formCreator;
        this.tableData.unshift(res.data);
      });
    },
    //编辑
    edit(row) {
      this.innerVisible = true;
      let { formTitle, formPlat, formDesc, formKey, formId } = row;
      this.form2 = {
        formTitle,
        formPlat,
        formDesc,
        formKey,
        formId,
      };
    },
    //修改表单
    editFormDesign(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/checkItems/formDesign",
        query: { formKey: row.formKey, formId: row.formId },
      });
    },
    getList(params) {
      const args = this._.cloneDeep(params ? params : this.queryParams);
      this.loading = true;
      this.finallySearch = args;
      list(args)
        .then((res) => {
          this.tableData1 = res.data;
          this.pageTotal1 = res.total;
          this.tableData1.forEach((element) => {
            //表单类型
            this.$set(element, "formTypeValue", this.formTypeFormat(element));
            //状态
            this.$set(
              element,
              "formStatusValue",
              element.formStatus == 1 ? "启用" : "未启用"
            );
            //创建时间
            this.$set(
              element,
              "createTimeValue",
              this.parseTime(element.createTime)
            );
            //修改时间
            this.$set(
              element,
              "updateTimeValue",
              this.parseTime(element.updateTime)
            );
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.queryParams.pageNum;
        this.finallySearch.pageSize = this.queryParams.pageSize;
      }
      this.getList(this.finallySearch);
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getDicts("cm_sub_type").then((response) => {
      this.deviceTypeOptions = response.data;
    });
    this.getDicts("order_business_type").then((response) => {
      this.businessTypeOptions = response.data;
    });
    // this.initConfig();
    this.getList();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {},
  beforeMount() {},
  beforeUpdate() {},
  updated() {},
  beforeDestroy() {},
  destroyed() {},
  activated() {},
};
</script>
<style lang="less" scoped>
//@import url(); 引入公共css类
/deep/ .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
