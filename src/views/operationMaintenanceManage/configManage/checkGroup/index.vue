//检查组
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
      <template slot="businessType">
        <el-select
          v-model="searchForm.businessType"
          placeholder="请选择"
          clearable
          filterable
          style="width: 100%"
          @change="businessTypeFilterChange"
        >
          <el-option
            v-for="dict in businessTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </template>
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="queryData"
        :loading="loading"
        :tableId="tableId"
        row-id="reportId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleAdd('add')"
            v-has-permi="['maintenance:checkGroup:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <!-- v-if="checkPermission(['errorPush:ignore'])" -->
          <el-button
            type="text"
            size="large"
            @click="handleAdd('edit', row)"
            v-has-permi="['maintenance:checkGroup:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleConfig(row)"
            v-has-permi="['maintenance:checkGroup:config']"
          >
            配置检查项
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleCopy(row)"
            v-has-permi="['maintenance:checkGroup:copy']"
          >
            复制
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleDelete(row)"
            v-has-permi="['maintenance:checkGroup:delete']"
          >
            删除
          </el-button>
        </template>

        <template slot="status" slot-scope="{ row }"
          ><el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
            :disabled="!checkPermission(['maintenance:checkGroup:status'])"
          >
          </el-switch>
        </template>
      </GridTable>
    </el-card>
    <el-drawer title="检查项明细" :visible.sync="drawerVisible" size="60%">
      <el-card>
        <GridTable
          ref="gridTable"
          :columns="detailColumns"
          :tableData="detailData"
          :currentPage.sync="detailForm.pageNum"
          :pageSize.sync="detailForm.pageSize"
          :total.sync="detailForm.tableTotal"
          @changePage="queryData"
          :loading="detailLoading"
          tableId="checkDetailTable"
        ></GridTable>
      </el-card>
    </el-drawer>
    <el-dialog
      title="新增检查组"
      :visible.sync="addVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="50%"
    >
      <el-form
        :model="basicForm"
        :rules="formRules"
        label-width="110px"
        ref="basicForm"
      >
        <el-row type="flex" style="flex-wrap: wrap;">
          <el-col :span="24">
            <el-form-item prop="groupName" label="检查组名称">
              <el-input
                v-model="basicForm.groupName"
                placeholder="请输入具体的名称，长度500个字符以内"
                maxlength="500"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="businessType" label="业务类型">
              <el-select
                v-model="basicForm.businessType"
                placeholder="请选择业务类型"
                style="width: 100%"
                @change="businessTypeChange"
                clearable
                filterable
              >
                <el-option
                  v-for="i in businessTypeOptions"
                  :key="i.dictValue"
                  :label="i.dictLabel"
                  :value="i.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="groupType" label="检查组类别">
              <el-select
                v-model="basicForm.groupType"
                placeholder="请选择检查组类别"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="i in groupTypeOptions"
                  :key="i.id"
                  :label="i.typeName"
                  :value="i.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取消</el-button>
        <el-button type="primary" @click.stop="handleSubmit">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryGroupList,
  changeStatus,
  deleteGroup,
  copyGroup,
  queryItemDetail,
  submitForm,
  queryFirstOrderType,
} from "@/api/operationMaintenanceManage/configManage/checkGroup.js";

import checkPermission from "@/utils/permission.js";
export default {
  name: "checkGroupManage",
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      basicForm: { groupName: "", businessType: "", groupType: "" },
      formRules: {
        groupName: [
          { required: true, message: "请输入检查组名称", trigger: "blur" },
        ],
        groupType: [
          { required: true, message: "请选择检查组类别", trigger: "blur" },
        ],
        businessType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
      },
      addVisible: false,
      drawerVisible: false,
      groupStatusOptions: [
        // { label: "全部", value: "all" },
        { dictLabel: "停用", dictValue: 1 },
        { dictLabel: "启用", dictValue: 0 },
      ],
      groupTypeOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        groupType: undefined,
      },
      options: [],
      finallySearch: null,
      tableData: [],
      tableTotal: 0,
      detailForm: {
        pageNum: 1,
        pageSize: 10,
        tableTotal: 0,
      },
      detailLoading: false,
      detailData: [],
      detailColumns: [
        {
          field: "formName",
          title: "检查项名称",
        },
        {
          field: "formDefDesc",
          title: "检查项描述",
        },
        {
          field: "businessType",
          title: "业务类型",
          formatter: ({ cellValue }) => {
            return (
              this.businessTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "checkTarget",
          title: "检查对象",
          formatter: ({ cellValue }) => {
            return cellValue == "1"
              ? "设备"
              : cellValue == "2"
              ? "站点"
              : cellValue;
          },
        },
        {
          field: "equipType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return (
              this.equipTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
      ],
      //   config: [],
      columns: [
        {
          field: "groupName",
          title: "检查组名称",
        },
        {
          field: "groupTypeName",
          title: "检查组类别",
          // formatter: ({ cellValue }) => {
          //   return (
          //     this.groupTypeOptions?.find((x) => x.dictValue === cellValue)
          //       ?.dictLabel || cellValue
          //   );
          // },
        },
        {
          field: "businessType",
          title: "业务类型",
          formatter: ({ cellValue }) => {
            return (
              this.businessTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "checkItemCount",
          title: "检查项",
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  props={{ type: "text" }}
                  onClick={() => {
                    this.handleItemDialog(row);
                  }}
                >
                  {row.checkItemCount}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "createByName",
          title: "创建人",
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "updateByName",
          title: "修改人",
        },
        {
          field: "updateTime",
          title: "修改时间",
        },
        {
          field: "status",
          title: "状态",
          slots: { default: "status" },
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "checkGroupTable", //tableId必须项目唯一，用于缓存展示列
      recordList: [],
      businessTypeOptions: [
        // {
        //   dictLabel: "充电",
        //   dictValue: "1",
        // },
        // {
        //   dictLabel: "储能",
        //   dictValue: "2",
        // },
        // {
        //   dictLabel: "光伏",
        //   dictValue: "3",
        // },
      ],
      equipTypeOptions: [],
      groupTypeFilterOptions: [],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "groupName",
          title: "检查组名称",
          type: "input",
          placeholder: "分组名称",
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "slot",
          placeholder: "业务类型",
          options: this.businessTypeOptions,
          //   optionLabel: "typeName",
          //   optionValue: "id",
        },
        {
          key: "groupType",
          title: "检查组类别",
          type: "select",
          placeholder: "检查组类别",
          options: this.groupTypeFilterOptions,
          optionLabel: "typeName",
          optionValue: "id",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "状态",
          options: this.groupStatusOptions,
        },
      ];
    },
  },

  activated() {
    this.getDicts("check_item_equip_type").then((response) => {
      this.equipTypeOptions = response.data;
    });
    this.getDicts("order_business_type").then((response) => {
      this.businessTypeOptions = response.data;
    });
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      // this.getDicts("check_group_type").then((response) => {
      //   this.groupTypeOptions = response.data;
      // }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    businessTypeFilterChange(val) {
      if (val) {
        queryFirstOrderType({ businessType: val }).then((res) => {
          this.groupTypeFilterOptions = res.data;
        });
      } else {
        this.groupTypeFilterOptions = [];
      }
      this.searchForm.groupType = undefined;
    },
    businessTypeChange(val) {
      if (val) {
        queryFirstOrderType({ businessType: val }).then((res) => {
          this.groupTypeOptions = res.data;
        });
      } else {
        this.groupTypeOptions = [];
      }
      this.basicForm.groupType = undefined;
    },
    handleSubmit() {
      this.$refs.basicForm.validate(async (valid) => {
        if (!valid) {
          return false;
        }
        const params = {
          ...this.basicForm,
        };
        this.loading = true;
        const res = await submitForm(params);
        this.loading = false;
        if (res?.code === "10000") {
          this.$message.success("保存成功");
          this.closeDialog();
          this.queryData();
          //   this.goBack();
        }
      });
    },
    closeDialog() {
      this.basicForm = { groupName: "", businessType: "", groupType: "" };
      this.addVisible = false;
    },
    handleAdd(type = "add", row) {
      this.addVisible = true;
      if (type === "edit") {
        this.basicForm = {
          groupName: row.groupName,
          businessType: row.businessType,
          groupType: row.groupType,
          checkGroupId: row.checkGroupId,
        };
        queryFirstOrderType({ businessType: row.businessType }).then((res) => {
          this.groupTypeOptions = res.data;
        });
      } else {
        this.groupTypeOptions = [];
      }
    },
    handleItemDialog(row) {
      console.log(row, "点击检查项");
      let params = { ...this.detailForm, checkGroupId: row.checkGroupId };
      this.detailLoading = true;
      queryItemDetail(params)
        .then((res) => {
          this.detailLoading = false;
          this.detailData = res?.data;
          this.detailForm.tableTotal = res?.total;
        })
        .catch((err) => {
          this.detailLoading = false;
        });
      this.drawerVisible = true;
    },
    //状态切换
    handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";
      changeStatus({ status: row.status, checkGroupId: row.checkGroupId })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.queryData();
          } else {
            row.status = row.status == 1 ? 0 : 1;
          }
        })
        .catch(function() {
          row.status = row.status == 1 ? 0 : 1;
        });
    },
    handleCopy(row) {
      this.$confirm("确定要复制该检查组吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          checkGroupId: row.checkGroupId,
        };
        copyGroup(data).then((res) => {
          if (res?.success) {
            this.$message.success("复制成功");
            //更新列表
            this.queryData();
          } else {
            this.$message.error("复制失败");
          }
        });
      });
    },
    handleDelete(row) {
      this.$confirm("确定要删除该检查组吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          checkGroupId: row.checkGroupId,
        };
        deleteGroup(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.queryData();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.groupTypeFilterOptions = [];
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };

      // this.finallySearch = args;
      queryGroupList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
      // this.tableData = [
      //   {
      //     groupName: "名称1",
      //     groupType: "手动组",
      //     groupMember: "1",
      //     status: "0",
      //     configTime: "2023-10-10 10:12:32",
      //     createBy: "张三",
      //   },
      // ];
      // this.loading = false;
    },
    //新增/修改
    handleConfig(row) {
      this.$router.push({
        path: "/configManage/checkGroup/edit",
        query: {
          checkGroupId: row?.checkGroupId,
        },
      });
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
