<template>
  <div class="app-container" v-loading="pageLoading">
    <div class="page-title">
      <h3>{{ checkGroupId ? "编辑" : "新增" }}检查组</h3>
      <!-- <div>
        <el-button type="text" icon="el-icon-arrow-left" @click="goBack"
          >返回</el-button
        >
        <el-button type="primary" @click="saveForm" :loading="loading"
          >保存</el-button
        >
      </div> -->
    </div>
    <el-card>
      <!-- 配置项组件 -->
      <TransferTable
        ref="transferTable"
        leftTitle="未配置检查项"
        rightTitle="已配置检查项"
        :config="config"
        :leftParams="leftParams"
        :rightParams="rightParams"
        :leftColumns="columns"
        :rightColumns="rightColumns"
        :tableLeftData="tableLeftData"
        :tableRightData="tableRightData"
        :leftPropsObj="leftPropsObj"
        :rightPropsObj="rightPropsObj"
        :rightEventsObj="rightEventsObj"
        @handleConfig="handleConfig"
        @handleClear="handleClear"
        @handleLeftQuery="handleLeftQuery"
        @handleRightQuery="handleRightQuery"
        @handleAllConfig="handleAllConfig"
        @handleAllClear="handleAllClear"
      ></TransferTable>
    </el-card>
  </div>
</template>

<script>
import * as api from "@/api/operationMaintenanceManage/configManage/checkGroup.js";
import { queryPropOptions } from "@/api/operationMaintenanceManage/configManage/checkItems.js";
import TransferTable from "@/components/TransferTable/index.vue";
export default {
  components: {
    TransferTable,
  },
  data() {
    return {
      pageLoading: false,
      loading: false,
      checkGroupId: "",

      businessTypeOptions: [
        {
          dictLabel: "充电",
          dictValue: "1",
        },
        {
          dictLabel: "储能",
          dictValue: "2",
        },
        {
          dictLabel: "光伏",
          dictValue: "3",
        },
      ],
      checkObjectOptions: [
        { dictValue: "1", dictLabel: "设备" },
        { dictValue: "2", dictLabel: "站点" },
      ],
      deviceTypeOptions: [],
      groupTypeOptions: [],
      //穿梭表格参数--start
      leftParams: { pageNum: 1, pageSize: 10 },
      rightParams: { pageNum: 1, pageSize: 10 },
      columns: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "formName",
          title: "检查项名称",
        },
        {
          field: "formDefDesc",
          title: "检查项描述",
        },
        {
          field: "businessType",
          title: "业务类型",
        },
        {
          field: "itemAttribute",
          title: "检查项属性",
        },
        {
          field: "checkTarget",
          title: "检查对象",
        },
        {
          field: "equipType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return (
              this.equipTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
      ],
      rightColumns: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "defSort",
          title: "排序",
          editRender: {
            name: "input",
            immediate: true,
            attrs: { type: "number", step: 1, min: 1 },
          },
        },
        {
          field: "formName",
          title: "检查项名称",
        },
        {
          field: "formDefDesc",
          title: "检查项描述",
        },
        {
          field: "businessType",
          title: "业务类型",
        },
        {
          field: "itemAttribute",
          title: "检查项属性",
        },
        {
          field: "checkTarget",
          title: "检查对象",
        },
        {
          field: "equipType",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return (
              this.equipTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
      ],
      tableLeftData: [],
      tableRightData: [],
      leftPropsObj: {
        loading: false,
        total: 0,
        rowId: "stationId",
        tableId: "leftTableList",
      },
      rightPropsObj: {
        loading: false,
        total: 0,
        rowId: "stationId",
        tableId: "rightTableList",
        editConfig: { trigger: "click", mode: "cell" },
        editRules: {
          defSort: [{ pattern: /^[1-9]\d*$/, message: "请输入正整数" }],
        },
        keepSource: true,
      },
      rightEventsObj: {
        "edit-closed": (val) => {
          return this.handleEditClose(val);
        },
      },
      //穿梭表格参数--end
      equipTypeOptions: [],
    };
  },
  created() {
    this.checkGroupId = this.$route.query.checkGroupId || "";
    this.getDicts("cm_sub_type").then((response) => {
      this.equipTypeOptions = response.data;
    });
    this.getDicts("check_group_type").then((response) => {
      this.groupTypeOptions = response.data;
    });
    this.getDetail();
    this.handleLeftQuery(this.leftParams);
    this.handleRightQuery(this.rightParams);
  },
  computed: {
    config() {
      return [
        {
          key: "formName",
          title: "检查项名称",
          type: "input",
          placeholder: "检查项名称",
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "select",
          placeholder: "业务类型",
          options: this.businessTypeOptions,
          //   optionLabel: "typeName",
          //   optionValue: "id",
        },
        {
          key: "checkTarget",
          title: "检查对象",
          type: "select",
          placeholder: "检查对象",
          options: this.checkObjectOptions,
          //   optionLabel: "typeName",
          //   optionValue: "id",
        },
        {
          key: "equipType",
          title: "设备类型",
          type: "select",
          placeholder: "设备类型",
          options: this.equipTypeOptions,
          //   optionLabel: "typeName",
          //   optionValue: "id",
        },
        {
          key: "itemAttribute",
          title: "检查项属性",
          type: "autocomplete",
          placeholder: "请选择检查项属性",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb);
            },
          },
        },
      ];
    },
  },
  methods: {
    querySearch(queryString, cb) {
      queryPropOptions({
        itemAttributeName: queryString || "",
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
    //穿梭表格的方法===start
    async handleEditClose({ row, column }) {
      const $table = this.$refs.transferTable.$refs.rightGridTable.$refs.xTable;
      const errMap = await $table.validate(true).catch((errMap) => errMap);
      if (errMap) {
        return false;
      }
      //表格数据有更新时调接口
      if ($table.isUpdateByRow(row, column.field)) {
        api
          .setSequence({ ...row, checkGroupId: this.checkGroupId })
          .then((res) => {
            if (res.code === "10000") {
              this.$message.success("保存成功");
            }
            this.handleRightQuery(this.rightParams);
          })
          .catch(() => {
            this.handleRightQuery(this.rightParams);
          });
      }
    },
    //全部配置
    handleAllConfig() {
      api
        .setAllConfig({ ...this.leftParams, checkGroupId: this.checkGroupId })
        .then((res) => {
          if (res?.code === "10000") {
            this.$message.success("配置成功");
            this.handleLeftQuery(this.leftParams);
            this.handleRightQuery(this.rightParams);
            this.$refs.transferTable.clearTips("left");
            this.$refs.transferTable.clearTips("right");
          }
        });
    },
    //全部清除
    handleAllClear() {
      api.clearAllConfig({ checkGroupId: this.checkGroupId }).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.handleLeftQuery(this.leftParams);
          this.handleRightQuery(this.rightParams);
          this.$refs.transferTable.clearTips("left");
          this.$refs.transferTable.clearTips("right");
        }
      });
    },
    //配置
    handleConfig(data) {
      console.log(data);
      let params = {
        checkGroupId: this.checkGroupId,
        defIds: data?.map((x) => x.defId),
      };
      api.setStationConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.handleLeftQuery(this.leftParams);
          this.handleRightQuery(this.rightParams);
          this.$refs.transferTable.clearTips("left");
        }
      });
    },
    //移除
    handleClear(data) {
      let params = {
        checkGroupId: this.checkGroupId,
        defIds: data?.map((x) => x.defId),
      };
      api.clearConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.handleLeftQuery(this.leftParams);
          this.handleRightQuery(this.rightParams);
          this.$refs.transferTable.clearTips("right");
        }
      });
    },
    //查询左侧
    handleLeftQuery(params) {
      this.leftParams = { ...params };
      this.leftPropsObj.loading = true;
      api
        .queryLeftList({ ...params, checkGroupId: this.checkGroupId })
        .then((res) => {
          this.leftPropsObj.loading = false;
          this.tableLeftData = res?.data;
          this.leftPropsObj.total = res?.total;
        })
        .catch(() => {
          this.leftPropsObj.loading = false;
        });
    },
    //查询右侧
    handleRightQuery(params) {
      this.rightParams = { ...params };
      this.rightPropsObj.loading = true;
      api
        .queryRightList({
          ...params,
          checkGroupId: this.checkGroupId,
        })
        .then((res) => {
          this.rightPropsObj.loading = false;
          this.tableRightData = res?.data;
          this.rightPropsObj.total = res?.total;
        })
        .catch(() => {
          this.rightPropsObj.loading = false;
        });
    },
    //穿梭表格的方法===end

    goBack() {
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/checkGroup",
      });
    },
    async getDetail() {
      this.pageLoading = true;
      api
        .queryDetail({ checkGroupId: this.checkGroupId })
        .then(async (res) => {
          this.pageLoading = false;
          if (res?.code === "10000") {
            this.basicForm = { ...res?.data };
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  h3 {
    margin: 12px;
  }
  // margin-bottom: 20px;
}
/deep/ .vxe-table--render-default {
  font-size: 12px;
}
.app-container {
  padding: 10px;
}
</style>
