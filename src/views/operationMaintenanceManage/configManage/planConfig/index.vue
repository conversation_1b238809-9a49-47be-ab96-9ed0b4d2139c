//计划配置
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
      <template slot="businessType">
        <el-select
          v-model="searchForm.businessType"
          placeholder="请选择业务类型"
          @change="handleBusinessChange"
          style="width:100%"
          clearable
          filterable
        >
          <el-option
            v-for="item in businessTypeOptions"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          ></el-option>
        </el-select>
      </template>
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="queryData"
        :loading="loading"
        :tableId="tableId"
        row-id="configId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleAdd('add')"
            v-has-permi="['maintenance:planConfig:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <!-- v-if="checkPermission(['errorPush:ignore'])" -->
          <el-button
            type="text"
            size="large"
            @click="handleAdd('edit', row)"
            v-has-permi="['maintenance:planConfig:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleStation(row)"
            v-has-permi="['maintenance:planConfig:stationConfig']"
          >
            配置站点
          </el-button>

          <!-- <el-button
            type="text"
            size="large"
            @click="handleLog(row)"
            v-has-permi="['maintenance:planConfig:log']"
          >
            日志
          </el-button> -->

          <el-button
            type="text"
            size="large"
            @click="handleExport(row)"
            v-has-permi="['maintenance:planConfig:export']"
          >
            导出站点
          </el-button>
        </template>

        <template slot="status" slot-scope="{ row }"
          ><el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
            :disabled="!checkPermission(['maintenance:planConfig:status'])"
          >
          </el-switch>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showStation(row)">
            {{ row.stationNum }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <el-dialog
      title="操作日志"
      :visible.sync="logVisible"
      :close-on-click-modal="false"
      @close="logVisible = false"
      append-to-body
      width="70%"
    >
      <Timeline :list="recordList"></Timeline>
    </el-dialog>
    <StationDialog ref="stationDialog"></StationDialog>
  </div>
</template>

<script>
import { queryFirstOrderType } from "@/api/operationMaintenanceManage/configManage/checkGroup.js";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import Timeline from "@/components/Timeline/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import StationDialog from "./components/stationDialog.vue";
import {
  queryPlanList,
  changeStatus,
  exportExcel,
  queryLog,
} from "@/api/operationMaintenanceManage/configManage/planConfig.js";
import { getToken } from "@/utils/auth";

import checkPermission from "@/utils/permission.js";
export default {
  name: "planConfigManage",
  components: {
    AdvancedForm,
    GridTable,
    Timeline,
    StationDialog,
  },
  data() {
    return {
      recordList: [],
      logVisible: false,
      drawerVisible: false,
      groupStatusOptions: [
        // { label: "全部", value: "all" },
        { dictLabel: "停用", dictValue: 1 },
        { dictLabel: "启用", dictValue: 0 },
      ],
      orderTypeOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderType: "",
      },
      options: [],
      finallySearch: null,
      tableData: [],
      tableTotal: 0,
      periodOptions: [
        {
          dictLabel: "周",
          dictValue: "1",
        },
        {
          dictLabel: "月",
          dictValue: "2",
        },
        {
          dictLabel: "自定义",
          dictValue: "3",
        },
        {
          dictLabel: "单次",
          dictValue: "4",
        },
      ],
      //   config: [],
      columns: [
        {
          field: "ruleName",
          title: "规则名称",
        },
        {
          field: "cycle",
          title: "周期",
          formatter: ({ cellValue }) => {
            return (
              this.periodOptions.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "startDate",
          title: "开始日期",
        },
        {
          field: "endDate",
          title: "结束日期",
        },
        {
          field: "businessType",
          title: "业务类型",
          formatter: ({ cellValue }) => {
            return (
              this.businessTypeOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "orderType",
          title: "工单类型",
          formatter: ({ cellValue }) => {
            return (
              this.allOrderOptions?.find((x) => x.id == cellValue)?.typeName ||
              cellValue
            );
          },
        },
        {
          field: "stationNum",
          title: "已配置站点",
          slots: { default: "jump" },
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "configUserName",
          title: "状态",
          slots: { default: "status" },
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "planConfigTable", //tableId必须项目唯一，用于缓存展示列
      businessTypeOptions: [],
      token: "",
      allOrderOptions: [],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "ruleName",
          title: "规则名称",
          type: "input",
          placeholder: "规则名称",
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "slot",
          placeholder: "业务类型",
          // multiple: true,
          options: this.businessTypeOptions,
          //   optionLabel: "typeName",
          //   optionValue: "id",
        },
        {
          key: "orderType",
          title: "工单类型",
          type: "select",
          placeholder: "工单类型",
          options: this.orderTypeOptions,
          optionLabel: "typeName",
          optionValue: "id",
        },
        {
          key: "rangeTime",
          title: "创建时间",
          type: "dateRange",
          placeholder: "请选择创建时间",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "状态",
          options: this.groupStatusOptions,
        },
      ];
    },
  },
  created() {
    this.token = getToken();
  },
  activated() {
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response.data;
      }),
      queryFirstOrderType({}).then((res) => {
        this.allOrderOptions = res.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    handleBusinessChange(val) {
      this.searchForm.orderType = "";
      if (val) {
        queryFirstOrderType({ businessType: val }).then((res) => {
          this.orderTypeOptions = res.data?.filter(
            (x) => x.typeName == "巡检工单" || x.typeName == "保养工单"
          );
        });
      } else {
        this.orderTypeOptions = [];
      }
    },
    showStation(row) {
      this.$refs.stationDialog.open(row);
    },
    handleStation(row) {
      this.$router.push({
        path: "/configManage/planConfig/stationConfig",
        query: { configId: row.configId },
      });
    },
    handleLog(row) {
      this.logVisible = true;
      queryLog().then((res) => {
        this.recordList = res.data;
      });
    },
    //状态切换
    handleStatusChange(row) {
      if (row.status == "0" && row.stationNum == 0) {
        this.$message.warning("自动派单的站点不能为空，请配置站点！");
        row.status = row.status == 1 ? 0 : 1;
        return;
      }
      const text = row.status == "0" ? "启用" : "停用";
      changeStatus({ config: { status: row.status, configId: row.configId } })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
          } else {
            row.status = row.status == 1 ? 0 : 1;
          }
        })
        .catch(function() {
          row.status = row.status == 1 ? 0 : 1;
        });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport(row) {
      this.$confirm("确定导出站点吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel({ configId: row.configId }).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.createTimeStart = params.rangeTime[0] + " 00:00:00";
        params.createTimeEnd = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      // this.finallySearch = args;
      queryPlanList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //新增/修改
    handleAdd(type = "add", row = {}) {
      this.$router.push({
        path:
          type === "add"
            ? "/configManage/planConfig/add"
            : "/configManage/planConfig/edit",
        query: {
          configId: row?.configId,
        },
      });
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
