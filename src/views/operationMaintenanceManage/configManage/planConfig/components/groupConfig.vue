<template>
  <div>
    <el-card class="card-box">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>选择检查组</span>
      </div>
      <el-transfer
        v-model="checkedData"
        :data="allData"
        :titles="['未配置检查组', '已配置检查组']"
        :props="defaultProps"
      ></el-transfer>
    </el-card>
    <div class="btn-group">
      <el-button @click="handleBack" size="medium">上一步</el-button>
      <el-button @click="handleSubmit" size="medium" type="primary"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  queryAllCheckList,
  queryDetail,
} from "@/api/operationMaintenanceManage/configManage/planConfig.js";
export default {
  data() {
    return {
      checkedData: [],
      allData: [],
      defaultProps: {
        key: "checkGroupId",
        label: "groupName",
      },
    };
  },
  props: {
    configId: {
      type: String,
      default: "",
    },
    businessType: {
      type: String,
      default: "",
    },
    orderType: {
      type: String,
      default: "",
    },
  },
  created() {
    this.getAllList();
    this.configId && this.getCheckedList();
  },
  watch: {
    businessType() {
      this.getAllList();
      this.configId && this.getCheckedList();
    },
    orderType() {
      this.getAllList();
      this.configId && this.getCheckedList();
    },
  },
  methods: {
    handleSubmit() {
      const checkedObj = this.allData.filter((x) =>
        this.checkedData.includes(x.checkGroupId)
      );
      this.$emit("handleSubmit", checkedObj);
    },
    handleBack() {
      this.$emit("handleBack");
    },
    async getAllList() {
      const res = await queryAllCheckList({
        configId: this.configId,
        pageNum: 1,
        pageSize: 9999,
        status: 0,
        businessType: this.businessType,
        groupType: this.orderType,
      });
      if (res?.code === "10000") {
        this.allData = res.data;
      }
    },
    async getCheckedList() {
      const res = await queryDetail({ configId: this.configId });
      if (res?.code === "10000") {
        this.checkedData = res.data?.checkGroups.map((x) => x.checkGroupId);
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;
  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}
/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}
.card-box {
  margin-bottom: 16px;
}
.btn-group {
  display: flex;
  justify-content: center;
  margin: 40px 0;
  /deep/ .el-button--medium {
    padding: 12px 24px;
    font-size: 18px;
    border-radius: 4px;
  }
  .el-button + .el-button {
    margin-left: 30px;
  }
}
</style>
