<template>
  <el-dialog
    title="选择站点"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="80%"
  >
    <el-row :gutter="10">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-tree
            :data="treeOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            ref="tree"
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <AdvancedForm
          :config="config"
          :queryParams="searchForm"
          ref="AdvancedForm"
          showMore
          @confirm="handleQuery"
          @resetQuery="resetQuery"
          v-if="config.length"
        >
        </AdvancedForm>
        <el-card>
          <GridTable
            ref="gridTable"
            :columns="columns"
            :tableData="tableData"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="tableTotal"
            @changePage="queryData"
            :loading="loading"
            :tableId="tableId"
            row-id="stationId"
            :batchDelete="true"
            :checkbox="true"
            @handleSelectionChange="tableSelect"
            class="dialog-table"
          >
          </GridTable>
        </el-card>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="handleConfirm">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import { regionTreeList } from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
import {
  queryUnselectedStation,
  submitStation,
} from "@/api/operationMaintenanceManage/configManage/planConfig.js";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  props: {
    configId: {
      type: String,
      default: "",
    },
    deptOptionList: {
      type: Array,
      default: () => [],
    },
    levelOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      selectedObj: {},
      loading: false,
      tableId: "selectBatchStationList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        nickName: undefined,
        phonenumber: undefined,
        deptId: undefined,
      },
      treeOptions: [],
      defaultProps: {
        children: "children",
        label: "areaName",
        value: "areaCode",
      },
      tableData: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "stationName",
          title: "站点名称",
          customWidth: 150,
        },
        {
          field: "stationAddress",
          title: "站点地址",
          customWidth: 150,
        },
        {
          field: "operationMode",
          title: "运营模式",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationGrade",
          title: "运维等级",
          formatter: ({ cellValue }) => {
            return (
              this.levelOptions?.find((x) => x.gradeId === cellValue)
                ?.gradeName || cellValue
            );
          },
        },
        {
          field: "status",
          title: "运营状态",
          formatter: ({ cellValue }) => {
            return (
              this.operateStatusOptions?.find(
                (item) => item.dictValue == cellValue
              )?.dictLabel || cellValue
            );
          },
        },
        {
          field: "onlineDate",
          title: "上线日期",
        },
        {
          field: "orgNo",
          title: "能投大区",
          formatter: ({ cellValue }) => {
            return (
              this.deptOptionList?.find((item) => item.dictValue == cellValue)
                ?.dictLabel || ""
            );
          },
        },
      ],
      operationModeOptions: [],
      operateStatusOptions: [],
      stations: [],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "operationMode",
          title: "运营模式",
          type: "select",
          placeholder: "请选择运营模式",
          options: this.operationModeOptions,
        },
        {
          key: "stationGrade",
          title: "运维等级",
          type: "select",
          placeholder: "请选择运维等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },
        {
          key: "status",
          title: "运营状态",
          type: "select",
          placeholder: "请选择运营状态",
          options: this.operateStatusOptions,
        },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
      ];
    },
  },
  created() {
    this.getTreeselect();
    Promise.all([
      this.getDicts("cm_station_status").then((response) => {
        this.operateStatusOptions = response.data;
      }),
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    // 节点单击事件
    handleNodeClick(data) {
      const { areaCode } = data;
      const len = areaCode.length;
      this.searchForm = {
        ...this.searchForm,
        province: "",
        city: "",
        county: "",
      };
      len === 2 && (this.searchForm.province = areaCode);
      len === 4 && (this.searchForm.city = areaCode);
      len === 6 && (this.searchForm.county = areaCode);
      this.queryData();
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.stations = tableData.map((x) => x);
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      regionTreeList({}).then((response) => {
        console.log("部门权限 树", response.data);
        this.treeOptions = response.data;
      });
    },
    getSelectUser(row) {
      this.baseForm.selectedUser = { ...row };
      console.log(row, "----选中的用户");
    },
    open() {
      this.$refs.gridTable?.clearTips();
      this.visible = true;
      this.resetQuery();
    },
    closeDialog() {
      this.visible = false;
    },
    handleConfirm() {
      if (this.stations?.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      submitStation({ stations: this.stations, configId: this.configId }).then(
        (res) => {
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.closeDialog();
            this.$emit("handleSubmit");
          }
        }
      );
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取列表
    queryData() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        configId: this.configId,
      };
      this.selectedObj = {};
      // this.finallySearch = args;
      queryUnselectedStation(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.head-container {
  height: 70vh;
  overflow: auto;
}
/deep/ .el-card__body {
  padding: 12px;
  padding-bottom: 8px;
}
/deep/.dialog-table .vxe-body--column {
  height: 32px !important;
}
/deep/ .vxe-grid--toolbar-wrapper {
  display: none;
}
/deep/ .el-dialog {
  margin-top: 2vh !important;
}
/deep/ .el-dialog__body {
  padding: 10px 20px;
}
/deep/ .pagination-container {
  margin-top: 0;
}
/deep/ .el-form-item--small.el-form-item {
  margin-bottom: 4px;
}
</style>
