<template>
  <div>
    <el-form
      :model="basicForm"
      ref="basicForm"
      label-width="110px"
      :rules="basicRules"
    >
      <!-- 基本信息-S -->
      <el-card class="card-box">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>基本信息</span>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input
                v-model="basicForm.ruleName"
                placeholder="请输入规则名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务类型" prop="businessType">
              <el-select
                v-model="basicForm.businessType"
                placeholder="请选择业务类型"
                @change="handleBusinessChange"
                style="width:100%"
                clearable
                filterable
              >
                <el-option
                  v-for="item in businessTypeOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工单类型" prop="orderType">
              <el-select
                v-model="basicForm.orderType"
                placeholder="请选择工单类型"
                style="width:100%"
                clearable
                filterable
              >
                <el-option
                  v-for="item in orderTypeOptions"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="说明" prop="remark">
              <el-input
                type="textarea"
                maxlength="500"
                show-word-limit
                :rows="5"
                placeholder="请输入说明"
                v-model="basicForm.remark"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- 基本信息-E -->
      <!-- 周期计划-S -->
      <el-card class="card-box">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>周期计划</span>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="周期" prop="cycle">
              <el-radio-group v-model="basicForm.cycle">
                <el-radio label="1">周</el-radio>
                <el-radio label="2">月</el-radio>
                <el-radio label="3">自定义</el-radio>
                <el-radio label="4">单次</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="每周"
              prop="week"
              v-if="basicForm.cycle === '1'"
            >
              <el-select
                v-model="basicForm.week"
                style="width:90%"
                clearable
                filterable
              >
                <el-option
                  v-for="item in weekOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="每月"
              prop="month"
              v-if="basicForm.cycle === '2'"
            >
              <el-select
                v-model="basicForm.month"
                style="width:90%;margin-right:5px"
                clearable
                filterable
              >
                <el-option
                  v-for="item in monthOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                ></el-option> </el-select
              >号
            </el-form-item>
            <el-form-item
              label="开始日期"
              prop="times"
              v-if="basicForm.cycle === '3'"
            >
              <el-date-picker
                type="dates"
                v-model="basicForm.times"
                placeholder="选择一个或多个日期"
                style="width:90%"
                value-format="yyyy-MM-dd"
                clearable
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              label="开始日期"
              prop="time"
              v-if="basicForm.cycle === '4'"
            >
              <el-date-picker
                type="date"
                v-model="basicForm.time"
                placeholder="请选择日期"
                style="width:90%"
                value-format="yyyy-MM-dd"
                clearable
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="日期范围"
              prop="dateRange"
              v-if="['1', '2'].includes(basicForm.cycle)"
            >
              <el-date-picker
                v-model="basicForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width:100%"
                value-format="yyyy-MM-dd"
                clearable
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- 周期计划-E -->
      <!-- 处理对象-S -->
      <el-card class="card-box">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>处理对象</span>
        </div>
        <el-form-item label="处理人" prop="handleUserName">
          <el-row>
            <el-col :span="10">
              <el-input v-model="basicForm.handleUserName" disabled> </el-input>
            </el-col>
            <el-col :span="10" style="margin-left:10px">
              <el-button @click="handleSelectUser">选择</el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-card>
      <!-- 处理对象-E -->
    </el-form>
    <div class="btn-group">
      <el-button @click="handleCancel" size="medium">取消</el-button>
      <el-button @click="handleNext" size="medium" type="primary"
        >下一步</el-button
      >
    </div>
    <HandleUserDialog
      ref="userDialog"
      @confirm="getSelectedUser"
    ></HandleUserDialog>
  </div>
</template>

<script>
import HandleUserDialog from "@/components/selectDialog/handleUserDialog/index.vue";
import { queryFirstOrderType } from "@/api/operationMaintenanceManage/configManage/checkGroup.js";
import { queryDetail } from "@/api/operationMaintenanceManage/configManage/planConfig.js";
export default {
  components: {
    HandleUserDialog,
  },
  props: {
    configId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      basicForm: {
        ruleName: "",
        businessType: "",
        orderType: "",
        remark: "",
        cycle: "1",
        week: "",
        month: "",
        times: [],
        time: "",
        dateRange: [],
        handleUserName: "",
        handleType: 1,
      },

      businessTypeOptions: [],
      orderTypeOptions: [],
      weekOptions: [
        { dictValue: "1", dictLabel: "星期一" },
        { dictValue: "2", dictLabel: "星期二" },
        { dictValue: "3", dictLabel: "星期三" },
        { dictValue: "4", dictLabel: "星期四" },
        { dictValue: "5", dictLabel: "星期五" },
        { dictValue: "6", dictLabel: "星期六" },
        { dictValue: "7", dictLabel: "星期日" },
      ],
      monthOptions: [],
    };
  },
  computed: {
    basicRules() {
      return {
        ruleName: [
          { required: true, message: "请输入规则名称", trigger: "blur" },
        ],
        businessType: [
          { required: true, message: "请选择业务类型", trigger: "blur" },
        ],
        orderType: [
          { required: true, message: "请选择工单类型", trigger: "blur" },
        ],
        cycle: [{ required: true, message: "请选择周期", trigger: "blur" }],
        week: [
          {
            required: this.basicForm.cycle === "1",
            message: "请选择",
            trigger: "blur",
          },
        ],
        month: [
          {
            required: this.basicForm.cycle === "2",
            message: "请选择",
            trigger: "blur",
          },
        ],
        times: [
          {
            required: this.basicForm.cycle === "3",
            message: "请选择",
            trigger: "blur",
          },
        ],
        time: [
          {
            required: this.basicForm.cycle === "4",
            message: "请选择",
            trigger: "blur",
          },
        ],
        dateRange: [
          {
            required: ["1", "2"].includes(this.basicForm.cycle),
            message: "请选择",
            trigger: "blur",
          },
        ],
        handleUserName: [
          { required: true, message: "请选择处理人", trigger: "blur" },
        ],
      };
    },
  },
  created() {
    this.getDicts("order_business_type").then((response) => {
      this.businessTypeOptions = response.data;
    });
    for (let i = 1; i <= 28; i++) {
      this.monthOptions.push({ dictValue: i, dictLabel: i });
    }
    if (this.configId) {
      this.getDetail();
    }
  },
  methods: {
    getDetail() {
      queryDetail({ configId: this.configId }).then((res) => {
        if (res?.code === "10000") {
          const { config } = res.data;
          const {
            startDate = "",
            endDate = "",
            time,
            cycle,
            businessType,
            handleUserName,
          } = config;
          this.basicForm = {
            ...this.basicForm,
            ...config,
            dateRange: [startDate, endDate],
            times: cycle == "3" ? JSON.parse(time) : [],
            handleUserName: handleUserName,
          };
          console.log(this.basicForm, "------详情");
          queryFirstOrderType({ businessType: businessType }).then((res) => {
            this.orderTypeOptions = res.data?.filter(
              (x) => x.typeName == "巡检工单" || x.typeName == "保养工单"
            );
          });
        }
      });
    },
    handleBusinessChange(val) {
      this.basicForm.orderType = "";
      if (val) {
        queryFirstOrderType({ businessType: val }).then((res) => {
          this.orderTypeOptions = res.data?.filter(
            (x) => x.typeName == "巡检工单" || x.typeName == "保养工单"
          );
        });
      } else {
        this.orderTypeOptions = [];
      }
    },
    handleCancel() {
      this.$router.push({ name: "planConfigManage" });
    },
    getSelectedUser(row) {
      const { userType, selectedUser } = row;
      this.basicForm["handleUserName"] =
        userType === "person" ? selectedUser.nickName : selectedUser.groupName;
      this.basicForm.handleUser =
        userType === "person" ? selectedUser.userId : selectedUser.groupId;
      this.basicForm.handleType = userType === "person" ? 1 : 2;
      console.log(this.basicForm, "-----父组件");
    },
    handleSelectUser() {
      const { handleType, handleUser, handleUserName } = this.basicForm;
      const params = {
        userType: handleType == 1 ? "person" : "group",
        selectedUser:
          handleType == 1
            ? { nickName: handleUserName, userId: handleUser }
            : { groupName: handleUserName, groupId: handleUser },
      };
      this.$refs.userDialog.open(params);
    },
    handleNext() {
      this.$refs.basicForm.validate((valid) => {
        if (!valid) {
          return;
        }
        const {
          week = "",
          month = "",
          times = [],
          time = "",
          dateRange = ["", ""],
          endDate = "",
          startDate = "",
          ...rest
        } = this.basicForm;
        let config = { ...rest };
        if (config.cycle === "1") {
          config["week"] = week;
          config["startDate"] = dateRange[0];
          config["endDate"] = dateRange[1];
        }
        if (config.cycle === "2") {
          config["month"] = month;
          config["startDate"] = dateRange[0];
          config["endDate"] = dateRange[1];
        }
        if (config.cycle === "3") {
          config["time"] = times;
        }
        if (config.cycle === "4") {
          config["time"] = time;
        }

        console.log(config, "第一步");
        this.$emit("handleNext", config);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.card-box {
  margin-bottom: 16px;
}
.btn-group {
  display: flex;
  justify-content: center;
  margin: 40px 0;
  /deep/ .el-button--medium {
    padding: 12px 24px;
    font-size: 18px;
    border-radius: 4px;
  }
  .el-button + .el-button {
    margin-left: 30px;
  }
}
</style>
