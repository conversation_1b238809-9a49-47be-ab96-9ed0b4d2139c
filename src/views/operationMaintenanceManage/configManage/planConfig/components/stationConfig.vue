<template>
  <div class="app-container">
    <div class="page-title">
      <h3>配置站点</h3>
    </div>
    <el-card class="card-box">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>派单站点</span>
      </div>
      <AdvancedForm
        :config="config"
        :queryParams="searchForm"
        ref="AdvancedForm"
        showMore
        @confirm="handleQuery"
        @resetQuery="resetQuery"
        v-if="config.length"
      >
      </AdvancedForm>
      <el-card>
        <GridTable
          ref="gridTable"
          :columns="columns"
          :tableData="tableData"
          :batchDelete="true"
          :checkbox="true"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="tableTotal"
          @changePage="getList"
          :loading="loading"
          :tableId="tableId"
          row-id="groupId"
          @handleSelectionChange="tableSelect"
        >
          <template slot="xToolbarBtn" slot-scope="{}">
            <el-button size="mini" type="primary" @click.stop="handleCreate">
              添加
            </el-button>
            <el-button
              size="mini"
              type="primary"
              @click.stop="handleBatchDelete"
            >
              删除
            </el-button>
          </template>
          <template slot="operation" slot-scope="{ row }">
            <el-button type="text" size="large" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </GridTable>
      </el-card>
    </el-card>
    <!-- <div class="btn-group">
      <el-button @click="handleBack" size="medium">上一步</el-button>
      <el-button @click="handleNext" size="medium" type="primary"
        >下一步</el-button
      >
    </div> -->
    <StationDialog
      ref="stationDialog"
      :configId="configId"
      :deptOptionList="deptOptionList"
      :levelOptions="levelOptions"
      @handleSubmit="getList"
    ></StationDialog>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import {
  querySelectedList,
  deleteStation,
} from "@/api/operationMaintenanceManage/configManage/planConfig.js";
import StationDialog from "./batchDialog.vue";
import { regionData } from "element-china-area-data";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
import { maintenanceTypeDict } from "@/views/operationMaintenanceManage/station/dict.js";
import { queryLevelList } from "@/api/operationMaintenanceManage/station/index.js";
export default {
  components: {
    AdvancedForm,
    GridTable,
    StationDialog,
  },
  data() {
    return {
      loading: false,
      tableId: "stationConfigList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
          fixed: "left",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "站点地址",
        },
        {
          field: "orgNo",
          title: "能投大区",
          formatter: ({ cellValue }) => {
            return (
              this.deptOptionList?.find((item) => item.dictValue == cellValue)
                ?.dictLabel || ""
            );
          },
        },
        {
          field: "status",
          title: "运营状态",
          formatter: ({ cellValue }) => {
            return (
              this.operateStatusOptions?.find(
                (item) => item.dictValue == cellValue
              )?.dictLabel || cellValue
            );
          },
        },
        {
          field: "operationMode",
          title: "运营模式",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "onlineDate",
          title: "上线日期",
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      stations: [],
      configId: "",
      deptOptionList: [],
      operationModeOptions: [],
      operateStatusOptions: [],
      maintenanceTypeFilterOptions: [],
      maintenanceAllTypeOptions: [],
      levelOptions: [],
    };
  },
  computed: {
    config() {
      return [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "stationGrade",
          title: "站点运维等级",
          type: "select",
          placeholder: "请选择站点运维等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },
        {
          key: "operationMode",
          title: "运营模式",
          type: "select",
          placeholder: "请选择运营模式",
          options: this.operationModeOptions,
          events: {
            change: (val) => {
              this.handleFilterModeChange(val);
            },
          },
        },
        // {
        //   key: "maintenanceType",
        //   title: "运维类型",
        //   type: "select",
        //   placeholder: "请选择运维类型",
        //   options: this.maintenanceTypeFilterOptions,
        // },
        {
          key: "status",
          title: "运营状态",
          type: "select",
          placeholder: "请选择运营状态",
          options: this.operateStatusOptions,
        },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
  },
  created() {
    this.configId = this.$route.query.configId || "";
    Promise.all([
      this.getDeptList(),
      this.getDicts("cm_station_status").then((response) => {
        this.operateStatusOptions = response.data;
      }),
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
      this.getDicts("maintenance_type").then((response) => {
        this.maintenanceAllTypeOptions = response?.data;
        this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions;
      }),
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
        });
      }, 500);
    });
  },
  methods: {
    handleFilterModeChange(val) {
      if (!val) {
        this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions;
        return;
      }
      const label = this.operationModeOptions?.find((x) => x.dictValue == val)
        ?.dictLabel;
      const arr = maintenanceTypeDict[label] || [];
      this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions?.filter(
        (x) => arr.includes(x.dictLabel)
      );
    },
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data?.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    handleNext() {
      this.$emit("handleNext");
    },
    handleBack() {
      this.$emit("handleBack");
    },
    handleCreate() {
      console.log(this.$refs);
      this.$refs.stationDialog.open();
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.stations = tableData.map((x) => x);
    },
    handleBatchDelete() {
      if (this.stations?.length === 0) {
        this.$message.warning("请勾选至少一条数据");
        return;
      }
      this.$confirm("确认删除所选站点吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          stations: this.stations,
          configId: this.configId,
        };
        deleteStation(params).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    handleDelete(row) {
      this.$confirm("确认删除该站点吗?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          stations: [row],
          configId: this.configId,
        };
        deleteStation(params).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.getList();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.getList();
    },

    //获取列表
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        configId: this.configId,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      // this.finallySearch = args;
      querySelectedList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.btn-group {
  display: flex;
  justify-content: center;
  margin: 40px 0;
  /deep/ .el-button--medium {
    padding: 12px 24px;
    font-size: 18px;
    border-radius: 4px;
  }
  .el-button + .el-button {
    margin-left: 30px;
  }
}
.card-box {
  margin-bottom: 16px;
}
</style>
