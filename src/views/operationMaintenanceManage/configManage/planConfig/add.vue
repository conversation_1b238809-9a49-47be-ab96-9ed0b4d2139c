<template>
  <div class="app-container">
    <div class="page-title">
      <h3>{{ configId ? "编辑" : "新增" }}计划配置</h3>
    </div>
    <div class="step-card">
      <el-steps :active="active" :space="300" align-center>
        <el-step title="配置周期" icon="el-icon-star-on"></el-step>
        <!-- <el-step title="配置站点" icon="el-icon-star-on"></el-step> -->
        <el-step title="配置检查组" icon="el-icon-star-on"></el-step>
      </el-steps>
    </div>
    <PeriodConfig
      v-show="active === 0"
      @handleNext="handlePeriodNext"
      :configId="configId"
    ></PeriodConfig>
    <!-- <StationConfig
      v-show="active === 1"
      @handleBack="handleBack"
      @handleNext="handleNext"
    ></StationConfig> -->
    <GroupConfig
      v-show="active === 1"
      @handleBack="handleBack"
      @handleSubmit="handleSubmit"
      :configId="configId"
      :businessType="baseForm.businessType"
      :orderType="baseForm.orderType"
    ></GroupConfig>
    <!-- <div class="btn-group">
      <el-button @click="handleCancel" size="medium" v-if="active === 0"
        >取消</el-button
      >
      <el-button @click="handleBack" size="medium" v-if="active > 0"
        >上一步</el-button
      >
      <el-button
        @click="handleNext"
        size="medium"
        type="primary"
        v-if="active < 2"
        >下一步</el-button
      >
      <el-button
        @click="handleNext"
        size="medium"
        type="primary"
        v-if="active === 2"
        >提交</el-button
      >
    </div> -->
  </div>
</template>

<script>
import PeriodConfig from "./components/periodConfig.vue";
// import StationConfig from "./components/stationConfig.vue";
import GroupConfig from "./components/groupConfig.vue";
import { submitPlanConfig } from "@/api/operationMaintenanceManage/configManage/planConfig.js";
export default {
  components: {
    PeriodConfig,
    //  StationConfig,
    GroupConfig,
  },
  data() {
    return { active: 0, configId: "", baseForm: {} };
  },
  created() {
    this.configId = this.$route.query.configId || "";
  },
  methods: {
    handlePeriodNext(form) {
      this.active++;
      this.baseForm = { ...this.baseForm, ...form };
    },
    handleNext() {
      this.active++;
    },
    handleBack() {
      this.active--;
    },
    handleSubmit(checked) {
      const params = {
        config: { ...this.baseForm, configId: this.configId },
        checkGroups: checked,
      };
      submitPlanConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.$router.push({ name: "planConfigManage" });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.step-card {
  margin-bottom: 20px;
  /deep/ .el-steps {
    justify-content: center;
    .el-step__icon-inner[class*="el-icon"]:not(.is-status) {
      font-size: 40px;
    }
  }
}
.btn-group {
  display: flex;
  justify-content: center;
  margin: 40px 0;
  /deep/ .el-button--medium {
    padding: 12px 24px;
    font-size: 18px;
    border-radius: 4px;
  }
  .el-button + .el-button {
    margin-left: 30px;
  }
}
</style>
