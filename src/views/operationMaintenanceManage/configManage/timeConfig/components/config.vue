<template>
  <div class="app-container" v-loading="pageLoading">
    <div class="page-title">
      <h3>配置站点</h3>
      <div>
        <el-button type="text" icon="el-icon-arrow-left" @click="goBack"
          >返回</el-button
        >
        <!-- <el-button type="primary" @click="saveForm" :loading="loading"
          >保存</el-button
        > -->
      </div>
    </div>
    <el-card style="margin-bottom:16px">
      <DynamicForm
        :config="formConfig"
        :params="formParams"
        ref="dynamicForm"
        :defaultColSpan="12"
        labelPosition="right"
        labelWidth="200px"
        :preview="true"
      ></DynamicForm>
    </el-card>
    <el-card>
      <!-- 配置项组件 -->
      <TransferTable
        ref="transferTable"
        leftTitle="未配置站点"
        rightTitle="已配置站点"
        :config="config"
        :leftParams="leftParams"
        :rightParams="rightParams"
        :leftColumns="columns"
        :rightColumns="columns"
        :tableLeftData="tableLeftData"
        :tableRightData="tableRightData"
        :leftPropsObj="leftPropsObj"
        :rightPropsObj="rightPropsObj"
        @handleConfig="handleConfig"
        @handleClear="handleClear"
        @handleLeftQuery="handleLeftQuery"
        @handleRightQuery="handleRightQuery"
        @handleAllConfig="handleAllConfig"
        @handleAllClear="handleAllClear"
      >
        <!-- <template slot="rightTitle">
          <div style="width:130px">已配置站点</div>
          <div style="font-size:12px;color:#bbbbbb;font-weight:400">
            (已配置站点数量≥1时，该工单类型的站点时效按照已配置站点计算时效，未配置的站点不计算时效)
          </div>
        </template> -->
      </TransferTable>
    </el-card>
  </div>
</template>

<script>
import api from "@/api/operationMaintenanceManage/configManage/timeConfig.js";

import { initParams } from "@/utils/buse.js";
import { regionData } from "element-china-area-data";
import { queryLevelList } from "@/api/station/station";

import TransferTable from "@/components/TransferTable/index.vue";
export default {
  components: {
    TransferTable,
  },
  data() {
    return {
      formParams: {},
      pageLoading: false,
      loading: false,
      agingId: "",
      businessTypeOptions: [
        {
          dictLabel: "充电",
          dictValue: "1",
        },
        {
          dictLabel: "储能",
          dictValue: "2",
        },
        {
          dictLabel: "光伏",
          dictValue: "3",
        },
      ],
      checkObjectOptions: [
        { dictValue: "1", dictLabel: "设备" },
        { dictValue: "2", dictLabel: "站点" },
      ],
      deviceTypeOptions: [],
      groupTypeOptions: [],
      //穿梭表格参数--start
      leftParams: { pageNum: 1, pageSize: 10 },
      rightParams: { pageNum: 1, pageSize: 10 },
      columns: [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "city",
          title: "城市",
          formatter: ({ row }) => {
            return row.provinceName + "-" + row.cityName + "-" + row.countyName;
          },
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationGradeName",
          title: "站点运维等级",
        },
      ],
      tableLeftData: [],
      tableRightData: [],
      leftPropsObj: {
        loading: false,
        total: 0,
        rowId: "stationId",
        tableId: "leftTableStationList",
      },
      rightPropsObj: {
        loading: false,
        total: 0,
        rowId: "stationId",
        tableId: "rightTableStationList",
      },
      //穿梭表格参数--end
      levelOptions: [],
    };
  },
  async created() {
    this.formParams = initParams(this.formConfig);

    this.agingId = this.$route.query.agingId || "";

    queryLevelList({}).then((res) => {
      this.levelOptions = res.data;
    });
    await this.getDetail();
    this.handleLeftQuery(this.leftParams);
    this.handleRightQuery(this.rightParams);
  },
  computed: {
    formConfig() {
      return [
        {
          element: "el-input",
          field: "businessName",
          title: "业务类型：",
        },
        {
          element: "el-input",
          field: "orderName",
          title: "工单类型：",
        },
        {
          element: "el-input",
          field: "treatmentAging",
          title: "处理时效：",
          previewFormatter: (val) => {
            return (val ?? "") + "h";
          },
        },
        {
          element: "el-input",
          field: "auditAging",
          title: "审核时效：",
          previewFormatter: (val) => {
            return (val ?? "") + "h";
          },
        },
      ];
    },
    config() {
      return [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "站点名称",
        },
        {
          key: "region",
          title: "省市区",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
          props: {
            multiple: false,
          },
        },
        {
          key: "stationGrade",
          title: "站点等级",
          type: "select",
          placeholder: "请选择站点等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },
      ];
    },
  },
  methods: {
    //穿梭表格的方法===start
    //全部配置
    handleAllConfig() {
      api
        .setAllConfig({
          ...this.leftParams,
          agingId: this.agingId,
          businessType: this.formParams.businessType,
          orderType: this.formParams.orderType,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.$message.success("配置成功");
            this.handleLeftQuery(this.leftParams);
            this.handleRightQuery(this.rightParams);
            this.$refs.transferTable.clearTips("left");
            this.$refs.transferTable.clearTips("right");
          }
        });
    },
    //全部清除
    handleAllClear() {
      api
        .clearAllConfig({
          agingId: this.agingId,
          businessType: this.formParams.businessType,
          orderType: this.formParams.orderType,
        })
        .then((res) => {
          if (res?.code === "10000") {
            this.$message.success("配置成功");
            this.handleLeftQuery(this.leftParams);
            this.handleRightQuery(this.rightParams);
            this.$refs.transferTable.clearTips("left");
            this.$refs.transferTable.clearTips("right");
          }
        });
    },
    //配置
    handleConfig(data) {
      console.log(data);
      let params = {
        businessType: this.formParams.businessType,
        orderType: this.formParams.orderType,
        agingId: this.agingId,
        stationIdList: data?.map((x) => x.stationId),
      };
      api.setStationConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.handleLeftQuery(this.leftParams);
          this.handleRightQuery(this.rightParams);
          this.$refs.transferTable.clearTips("left");
        }
      });
    },
    //移除
    handleClear(data) {
      let params = {
        businessType: this.formParams.businessType,
        orderType: this.formParams.orderType,
        agingId: this.agingId,
        stationIdList: data?.map((x) => x.stationId),
      };
      api.clearConfig(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功");
          this.handleLeftQuery(this.leftParams);
          this.handleRightQuery(this.rightParams);
          this.$refs.transferTable.clearTips("right");
        }
      });
    },
    //查询左侧
    handleLeftQuery(data) {
      const params = { ...data };
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0] ? params.region[0] : undefined;
        params["city"] = params.region[1] ? params.region[1] : undefined;
        params["county"] = params.region[2];
      }
      this.leftParams = { ...params };
      this.leftPropsObj.loading = true;
      api
        .queryLeftList({
          ...params,
          businessType: this.formParams.businessType,
          orderType: this.formParams.orderType,
        })
        .then((res) => {
          this.leftPropsObj.loading = false;
          this.tableLeftData = res?.data;
          this.leftPropsObj.total = res?.total;
        })
        .catch(() => {
          this.leftPropsObj.loading = false;
        });
    },
    //查询右侧
    handleRightQuery(data) {
      const params = { ...data };
      if (Array.isArray(params.region)) {
        params["province"] = params.region[0] ? params.region[0] : undefined;
        params["city"] = params.region[1] ? params.region[1] : undefined;
        params["county"] = params.region[2];
      }
      this.rightParams = { ...params };
      this.rightPropsObj.loading = true;
      api
        .queryRightList({
          ...params,
          agingId: this.agingId,
        })
        .then((res) => {
          this.rightPropsObj.loading = false;
          this.tableRightData = res?.data;
          this.rightPropsObj.total = res?.total;
        })
        .catch(() => {
          this.rightPropsObj.loading = false;
        });
    },
    //穿梭表格的方法===end

    goBack() {
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/timeConfig",
      });
    },
    async getDetail() {
      this.pageLoading = true;
      const res = await api.getTableData({
        agingId: this.agingId,
        pageNum: 1,
        pageSize: 10,
      });
      this.pageLoading = false;
      if (res?.code === "10000") {
        this.formParams = { ...res?.data[0] };
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 20px;
}
/deep/ .vxe-table--render-default {
  font-size: 12px;
}
</style>
