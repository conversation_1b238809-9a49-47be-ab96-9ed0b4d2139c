<template>
  <el-dialog
    title="查看站点"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="visible = false"
    append-to-body
    width="70%"
  >
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="queryData"
        :loading="loading"
        :tableId="tableId"
      >
      </GridTable>
    </el-card>
  </el-dialog>
</template>

<script>
import api from "@/api/operationMaintenanceManage/configManage/timeConfig.js";
import GridTable from "@/components/GridTable/index.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";

export default {
  components: {
    GridTable,
    AdvancedForm,
  },
  data() {
    return {
      agingId: "",
      visible: false,
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      tableTotal: 0,
      columns: [
        {
          field: "stationId",
          title: "站点编号",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationGradeName",
          title: "站点运维等级",
        },
      ],
      tableId: "timeConfigStationTable", //tableId必须项目唯一，用于缓存展示列
    };
  },
  computed: {
    config() {
      return [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
      ];
    },
  },
  methods: {
    open(row) {
      this.visible = true;
      this.agingId = row.agingId;
      this.resetQuery();
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
        agingId: this.agingId,
      };

      // this.finallySearch = args;
      api
        .queryRightList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style></style>
