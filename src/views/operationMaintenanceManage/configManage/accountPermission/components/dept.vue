<template>
  <el-card style="margin-bottom: 10px;">
    <div slot="header" style=" display: flex;justify-content: space-between;">
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>配置能投大区</span>
      </div>
      <el-button @click="handleSubmit" type="primary">保存</el-button>
    </div>
    <TransferTree
      :cascadeData="cascadeData"
      v-model="checkedData"
      ref="transferTree"
      :titles="['未配置能投大区', '已配置能投大区']"
    ></TransferTree>
  </el-card>
</template>

<script>
import TransferTree from "@/components/TransferTree/index2.vue";
import {
  queryDeptChecked,
  submitDept,
  queryDeptTree,
} from "@/api/operationMaintenanceManage/configManage/accountPermission.js";

export default {
  components: { TransferTree },
  props: {
    userId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      cascadeData: [],
      checkedData: [],
    };
  },
  async created() {
    await this.getTreeList();
    this.getDetail();
    // this.cascadeData = JSON.parse(JSON.stringify(regionData));
    // this.traverseArr(this.cascadeData);
  },
  methods: {
    getDetail() {
      queryDeptChecked({ userId: this.userId }).then((res) => {
        if (res?.code === "10000") {
          this.checkedData = res.data;
          this.traverseArr(this.checkedData);
          this.$nextTick(() => {
            this.$refs.transferTree.getDefaultLeftData();
          });
        }
      });
    },
    //获取所有组织-用户树形结构
    async getTreeList() {
      const res = await queryDeptTree({});
      if (res) {
        this.cascadeData = res.data;
        this.traverseArr(this.cascadeData);
        console.log(this.cascadeData, "dept");
      }
    },
    //提交
    async handleSubmit() {
      // let arr = this.flattenArray(this.checkedData);
      // let userIds = [];
      // arr.map((x) => {
      //   if (!x.hasOwnProperty("children")) {
      //     userIds.push(x.id);
      //   }
      // });
      submitDept({ userId: this.userId, deptList: this.checkedData }).then(
        (res) => {
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.getDetail();
          }
        }
      );
      console.log(this.checkedData, "-----提交");
    },
    traverseArr(arr, pid) {
      arr.forEach((obj) => {
        // 添加id和label
        obj.id = obj.deptId;
        obj.label = obj.deptName;
        obj.pid = obj.parentId;
        if (pid) {
          obj.pid = pid;
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children, obj.deptId);
        } else {
          delete obj.children;
        }
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
  },
};
</script>

<style lang="less" scoped></style>
