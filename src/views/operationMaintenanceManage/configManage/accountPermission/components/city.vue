<template>
  <el-card style="margin-bottom: 10px;">
    <div slot="header" style=" display: flex;justify-content: space-between;">
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>配置城市</span>
      </div>
      <el-button @click="handleSubmit" type="primary">保存</el-button>
    </div>
    <TransferTree
      :cascadeData="cascadeData"
      v-model="checkedData"
      ref="transferTree"
      :titles="['未配置省市区', '已配置省市区']"
    ></TransferTree>
  </el-card>
</template>

<script>
import TransferTree from "@/components/TransferTree/index2.vue";
import { regionData } from "element-china-area-data";
import {
  queryCityChecked,
  regionTreeList,
  submitCity,
} from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
export default {
  components: { TransferTree },
  props: {
    userId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      cascadeData: [],
      checkedData: [],
    };
  },
  async created() {
    await this.getRegionTree();
    if (this.userId) {
      this.getDetail();
    }
  },
  methods: {
    async getRegionTree() {
      const res = await regionTreeList({});
      if (res?.code === "10000") {
        this.cascadeData = res.data;
        this.traverseArr(this.cascadeData);
      }
    },
    getDetail() {
      queryCityChecked({ userId: this.userId }).then((res) => {
        if (res?.code === "10000") {
          this.checkedData = res.data;
          this.traverseArr(this.checkedData);
          this.$nextTick(() => {
            this.$refs.transferTree.getDefaultLeftData();
          });
        }
      });
    },
    //提交
    async handleSubmit() {
      submitCity({ userId: this.userId, cityList: this.checkedData }).then(
        (res) => {
          if (res?.code === "10000") {
            this.$message.success("保存成功");
            this.getDetail();
          }
        }
      );
    },
    traverseArr(arr, pid) {
      arr.forEach((obj) => {
        // 添加id和label
        obj.id = obj.areaCode;
        obj.label = obj.areaName;
        if (pid) {
          obj.pid = pid;
        }
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children, obj.areaCode);
        } else {
          delete obj.children;
        }
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
  },
};
</script>

<style lang="less" scoped></style>
