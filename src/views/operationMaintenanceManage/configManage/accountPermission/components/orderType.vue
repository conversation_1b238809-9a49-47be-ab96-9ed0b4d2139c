<template>
  <el-card style="margin-bottom: 10px;">
    <div slot="header" style=" display: flex;justify-content: space-between;">
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>配置工单类型</span>
      </div>
      <el-button @click="handleSubmit" type="primary">保存</el-button>
    </div>
    <div style="display: flex;justify-content: flex-end;align-items: center;">
      <el-switch
        v-model="permissionOrderTypeFlag"
        active-text="已配置的一级工单默认都可见"
        active-value="0"
        inactive-value="1"
      >
      </el-switch>
      <el-tooltip
        effect="dark"
        content="已配置的一级工单默认都可见：指的是配置了一级工单类型后，该一级类型以后新增的二三级类型，默认都会给该账号匹配上数据权限，不需要再对新增的二三级类型单独设置权限"
      >
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        :label="item.dictLabel"
        :name="item.dictValue"
        v-for="item in tabList"
        :key="item.dictValue"
      ></el-tab-pane>
    </el-tabs>
    <TransferTree
      :cascadeData="cascadeData"
      v-model="checkedData"
      ref="transferTree"
      :titles="['未配置工单类型', '已配置工单类型']"
    ></TransferTree>
  </el-card>
</template>

<script>
import TransferTree from "@/components/TransferTree/index2.vue";
import {
  queryOrderChecked,
  submitOrder,
  queryOrderTypeTree,
} from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
export default {
  components: { TransferTree },
  props: {
    userId: {
      type: String,
      default: "",
    },
    permissionFlag: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      activeName: "",
      cascadeData: [],
      checkedData: [],
      tabList: [
        // { dictLabel: "充电业务", dictValue: "1" },
        // { dictLabel: "储能业务", dictValue: "2" },
        // { dictLabel: "光伏业务", dictValue: "3" },
      ],
      permissionOrderTypeFlag: "1",
    };
  },
  async created() {
    this.getDicts("order_business_type").then((response) => {
      this.tabList = response.data;
      this.activeName = this.tabList[0]?.dictValue;
    });
    // await this.getTreeList();
    // if (this.userId) {
    //   this.getDetail();
    // }
    // this.cascadeData = JSON.parse(JSON.stringify(regionData));
    // this.traverseArr(this.cascadeData);
  },
  mounted() {},
  watch: {
    permissionFlag: {
      async handler(val) {
        this.permissionOrderTypeFlag = val;
      },
      immediate: false,
    },
    activeName: {
      async handler(val) {
        if (val) {
          await this.getTreeList();
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  methods: {
    getDetail() {
      queryOrderChecked({
        userId: this.userId,
        businessType: this.activeName,
      }).then((res) => {
        if (res?.code === "10000") {
          this.checkedData = res.data;
          this.traverseArr(this.checkedData);
          this.$nextTick(() => {
            this.$refs.transferTree.getDefaultLeftData();
          });
        }
      });
    },
    //获取所有组织-用户树形结构
    async getTreeList() {
      const res = await queryOrderTypeTree({ businessType: this.activeName });
      if (res) {
        this.cascadeData = res.data;
        this.traverseArr(this.cascadeData);
        console.log(this.cascadeData, "dept");
      }
    },
    //提交
    async handleSubmit() {
      // let arr = this.flattenArray(this.checkedData);
      // let userIds = [];
      // arr.map((x) => {
      //   if (!x.hasOwnProperty("children")) {
      //     userIds.push(x.id);
      //   }
      // });
      submitOrder({
        userId: this.userId,
        orderTypeList: this.checkedData.map((x) => {
          return {
            ...x,
            childrenList: x.children,
          };
        }),
        businessType: this.activeName,
        permissionOrderTypeFlag: this.permissionOrderTypeFlag,
      }).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("保存成功");
          this.getDetail();
        }
      });
      console.log(this.checkedData, "-----提交");
    },
    traverseArr(arr, pid) {
      arr.forEach((obj) => {
        // 添加id和label
        obj.label = obj.typeName;
        obj.pid = obj.parentId;
        if (pid) {
          obj.pid = pid;
        }
        obj.children = obj.childrenList;
        // 继续遍历children
        if (obj.children && obj.children.length > 0) {
          this.traverseArr(obj.children, obj.id);
        } else {
          delete obj.children;
        }
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
          // delete item.children; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
  },
};
</script>

<style></style>
