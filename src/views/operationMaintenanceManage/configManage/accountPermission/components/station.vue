<template>
  <el-card style="margin-bottom: 10px;">
    <div slot="header" style=" display: flex;justify-content: space-between;">
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>配置工单站点</span>
      </div>
      <el-button @click="handleSubmit" type="primary" :loading="btnLoading"
        >保存</el-button
      >
    </div>
    <div style="display: flex;justify-content: flex-end;align-items: center;">
      <el-switch
        v-model="permissionStationFlag"
        active-text="已配置的省市所有站点默认都可见"
        active-value="0"
        inactive-value="1"
      >
      </el-switch>
      <el-tooltip
        effect="dark"
        content="已配置的省市所有站点默认都可见：指的是配置了省市后，该省市以后新增的站点，默认都会给该账号匹配上数据权限，不需要再对新增的站点单独设置权限"
      >
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        :label="item.dictLabel"
        :name="item.dictValue"
        v-for="item in tabList"
        :key="item.dictValue"
      ></el-tab-pane>
    </el-tabs>
    <div class="tab-content">
      <SingleTree
        title="配置省市区"
        :list="cityTreeList"
        :defaultProps="{
          children: 'children',
          label: 'areaName',
        }"
        v-model="checkedRegionKeys"
        node-key="areaCode"
        class="tab-content-left"
        :loading="treeLoading"
        :defaultExpandAll="false"
      >
      </SingleTree>
      <div class="tab-content-right">
        <div class="tab-content-right-title">
          <h3>选择站点</h3>
          <span class="grey-title">（已配置站点{{ bindCount }}个）</span>
        </div>
        <AdvancedForm
          :config="config"
          :queryParams="searchForm"
          ref="AdvancedForm"
          @confirm="handleQuery"
          @resetQuery="resetQuery"
          v-if="config.length"
        >
        </AdvancedForm>
        <el-card>
          <CountTips
            v-if="selectNum > 0"
            :count="selectNum"
            @clear="clearTips"
          />
          <GridTable
            ref="gridTable"
            :columns="columns"
            :tableData="tableData"
            :batchDelete="true"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="total"
            @changePage="changePage"
            :loading="loading"
            :tableId="tableId"
            row-id="stationId"
            :checkRowKeys="checkedStationKeys"
            @checkbox-change="checkboxChange"
            @checkbox-all="checkboxAll"
          >
            <template slot="xToolbarBtn" slot-scope="{}">
              <el-button
                size="mini"
                type="primary"
                @click.stop="handleAllConfig"
              >
                全部站点一键配置
              </el-button>
              <el-button size="mini" type="primary" @click.stop="handleClear">
                全部站点一键清空
              </el-button>
            </template>
          </GridTable>
        </el-card>
      </div>
    </div>
  </el-card>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index2.vue";
import SingleTree from "@/components/TransferTree/singleTree.vue";
import {
  regionTreeList,
  queryStationList,
  queryBindRegion,
  queryBindStation,
  submitStation,
  setAllConfig,
  clearAllConfig,
} from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
import CountTips from "@/components/GridTable/CountTips/index.vue";
export default {
  components: {
    SingleTree,
    AdvancedForm,
    GridTable,
    CountTips,
  },
  props: {
    userId: {
      type: String,
      default: "",
    },
    permissionFlag: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      btnLoading: false,
      bindCount: 0,
      bindFlagOptions: [
        { dictLabel: "是", dictValue: "1" },
        { dictLabel: "否", dictValue: "0" },
      ],
      activeName: "",
      tabList: [
        { dictLabel: "充电站", dictValue: "充电站" },
        { dictLabel: "光伏站", dictValue: "光伏站" },
        { dictLabel: "储能站", dictValue: "储能站" },
        { dictLabel: "光储充一体式站", dictValue: "光储充一体式站" },
      ],
      cityTreeList: [],
      treeLoading: false,
      total: 0,
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      tableId: "configStationList1",
      checkedRegionKeys: [],
      checkedStationList: [],
      permissionStationFlag: "1",
    };
  },
  computed: {
    selectNum() {
      console.log(this.checkedStationList?.length, "num");
      return this.checkedStationList?.length;
    },
    checkedStationKeys() {
      return this.checkedStationList?.map((x) => x.stationId);
    },
    columns() {
      return [
        {
          type: "checkbox",
          customWidth: "10%",
        },
        {
          field: "deptName",
          title: "所属组织",
          customWidth: "20%",
        },
        {
          field: "belongPlace",
          title: "所在区域",
          customWidth: "30%",
        },
        {
          field: "stationName",
          title: "站点(" + this.total + ")",
          customWidth: "30%",
        },
        {
          field: "bindFlag",
          title: "是否已配置",
          formatter: ({ cellValue }) => {
            return this.bindFlagOptions?.find((x) => x.dictValue == cellValue)
              ?.dictLabel;
          },
          customWidth: "10%",
        },
      ];
    },
    config() {
      return [
        {
          key: "bindFlag",
          title: "是否已配置",
          type: "select",
          placeholder: "请选择",
          options: this.bindFlagOptions,
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入",
        },
      ];
    },
  },
  watch: {
    permissionFlag: {
      async handler(val) {
        this.permissionStationFlag = val;
      },
      immediate: false,
    },
    activeName: {
      async handler(val) {
        if (val) {
          this.clearTips();
          await this.getBindRegion();
          await this.getBindStation();
          this.handleQuery();
        }
        // this.clearTips();
        // this.getCityTree();
      },
      immediate: false,
    },
    checkedRegionKeys: {
      async handler(val) {
        // if (val?.length > 0) {
        this.checkedStationList = this.checkedStationList?.filter((x) =>
          val.includes(x.areaCode)
        );
        console.log(this.tableData, this.checkedStationKeys, "======");
        this.tableData.map((x) => {
          if (!this.checkedStationKeys.includes(x.stationId)) {
            const table = this.$refs.gridTable?.$refs.xTable;
            let selectRow = table.getRowById(x.stationId);
            if (selectRow) {
              table.setCheckboxRow(selectRow, false);
            }
          }
        });
        // }
        this.handleQuery();
      },
      deep: true,
    },

    // tableData: {
    //   handler() {},
    //   deep: true,
    //   immediate: true,
    // },
  },
  async created() {
    await this.getCityTree();
    this.getDicts("business_mode").then((response) => {
      this.tabList = response.data;
      this.activeName = this.tabList[0]?.dictValue || "";
    });
    // this.handleLeftQuery(this.leftParams);
    // this.handleRightQuery(this.rightParams);
  },
  mounted() {},
  methods: {
    clearTips() {
      this.checkedStationList = [];
      const table = this.$refs.gridTable?.$refs.xTable;
      table?.setAllCheckboxRow(false);
    },
    checkboxChange({ checked, row, rowIndex, $rowIndex }) {
      console.log(checked, row, rowIndex, $rowIndex);
      if (checked) {
        this.checkedStationList.push({
          stationId: row.stationId,
          areaCode: row.county,
        });
      } else {
        this.checkedStationList = this.checkedStationList.filter(
          (x) => x.stationId !== row.stationId
        );
      }
    },
    checkboxAll({ checked }) {
      if (checked) {
        this.tableData.map((x) => {
          //hasChecked：之前就在checked列表中的
          const hasChecked = this.checkedStationList?.some(
            (i) => i.stationId === x.stationId
          );
          if (!hasChecked) {
            this.checkedStationList.push({
              stationId: x.stationId,
              areaCode: x.county,
            });
          }
        });
      } else {
        //取消全选：checked列表中删除当前表格数据
        const keys = this.tableData?.map((x) => x.stationId);
        this.checkedStationList = this.checkedStationList?.filter(
          (y) => !keys.includes(y.stationId)
        );
      }
    },
    handleAllConfig() {
      const params = {
        ...this.searchForm,
        countyList: this.checkedRegionKeys,
        userId: this.userId,
        businessMode: this.activeName,
      };
      setAllConfig(params).then(async (res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功！");
          this.clearTips();
          await this.getBindRegion();
          await this.getBindStation();
          this.handleQuery();
        }
      });
    },
    handleClear() {
      const params = {
        ...this.searchForm,
        countyList: this.checkedRegionKeys,
        userId: this.userId,
        businessMode: this.activeName,
      };
      clearAllConfig(params).then(async (res) => {
        if (res?.code === "10000") {
          this.$message.success("配置成功！");
          this.clearTips();
          await this.getBindRegion();
          await this.getBindStation();
          this.handleQuery();
        }
      });
    },
    handleSubmit() {
      const params = {
        stationIds: this.checkedStationKeys,
        countyList: this.checkedRegionKeys,
        userId: this.userId,
        businessMode: this.activeName,
        permissionStationFlag: this.permissionStationFlag,
      };
      this.btnLoading = true;
      submitStation(params)
        .then(async (res) => {
          this.btnLoading = false;
          if (res?.code === "10000") {
            this.$message.success("配置成功！");
            this.clearTips();
            await this.getBindRegion();
            await this.getBindStation();
            this.handleQuery();
          }
        })
        .catch(() => {
          this.btnLoading = false;
        });
      console.log(this.checkedRegionKeys, this.checkedStationList, "提交");
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    async getBindStation() {
      const res = await queryBindStation({
        userId: this.userId,
        countyList: this.checkedRegionKeys,
        businessMode: this.activeName,
      });
      if (res?.code === "10000") {
        this.checkedStationList = res.data;
        this.bindCount = this.checkedStationList?.length || 0;
        console.log(this.checkedStationList, "获取绑定");
      }
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        businessMode: this.activeName,
        userId: this.userId,
        countyList: this.checkedRegionKeys,
      };
      queryStationList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
            this.$nextTick(() => {
              const table = this.$refs.gridTable?.$refs.xTable;
              table?.setAllCheckboxRow(false);
              console.log(this.checkedStationList, "-查询");
              this.checkedStationKeys?.map((x) => {
                let selectRow = table.getRowById(x);
                if (selectRow) {
                  table.setCheckboxRow(selectRow, true);
                }
              });
            });
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleClick(val) {
      console.log(val, "tab");
    },
    async getCityTree() {
      this.treeLoading = true;
      const res = await regionTreeList();
      this.treeLoading = false;
      if (res?.code === "10000") {
        this.cityTreeList = res.data;
      }
    },
    async getBindRegion() {
      const res = await queryBindRegion({
        userId: this.userId,
        businessMode: this.activeName,
      });
      if (res?.code === "10000") {
        this.checkedRegionKeys = res.data?.cityBindList || [];
      }
    },
    handleParams(params) {
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.grey-title {
  color: #ccc;
  font-size: 12px;
  margin-left: 10px;
}
.tab-content {
  display: flex;
  &-left {
    margin-top: 1em;
    flex: 1;
  }
  &-right {
    margin-left: 20px;
    flex: 3;
    &-title {
      display: flex;
      align-items: center;
    }
  }
}
</style>
