//账号权限
<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>

    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="tableTotal"
        @changePage="queryData"
        :loading="loading"
        :tableId="tableId"
        row-id="userId"
      >
        <!-- <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleAdd('add')"
            v-has-permi="['maintenance:planConfig:add']"
          >
            新增
          </el-button>
        </template> -->
        <template slot="operation" slot-scope="{ row }">
          <!-- v-if="checkPermission(['errorPush:ignore'])" -->
          <el-button
            type="text"
            size="large"
            @click="handleEdit(row)"
            v-has-permi="['maintenance:accountPermission:edit']"
          >
            编辑
          </el-button>
          <!-- <el-button
            type="text"
            size="large"
            @click="handleLog(row)"
            v-has-permi="['maintenance:accountPermission:log']"
          >
            日志
          </el-button> -->
        </template>

        <template slot="status" slot-scope="{ row }"
          ><el-switch
            v-model="row.permissionEnableFlag"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
            :disabled="
              !checkPermission(['maintenance:accountPermission:status'])
            "
          >
          </el-switch>
        </template>
      </GridTable>
    </el-card>
    <el-dialog
      title="操作日志"
      :visible.sync="logVisible"
      :close-on-click-modal="false"
      @close="logVisible = false"
      append-to-body
      width="70%"
    >
      <Timeline :list="recordList"></Timeline>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import Timeline from "@/components/Timeline/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryPermissionList,
  changeStatus,
  queryLog,
} from "@/api/operationMaintenanceManage/configManage/accountPermission.js";
import { getToken } from "@/utils/auth";

import checkPermission from "@/utils/permission.js";
export default {
  name: "accountPermissionManage",
  components: {
    AdvancedForm,
    GridTable,
    Timeline,
  },
  data() {
    return {
      recordList: [],
      logVisible: false,
      drawerVisible: false,
      groupStatusOptions: [
        // { label: "全部", value: "all" },
        { dictLabel: "停用", dictValue: "1" },
        { dictLabel: "启用", dictValue: "0" },
      ],
      orderTypeOptions: [],
      loading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      options: [],
      finallySearch: null,
      tableData: [],
      tableTotal: 0,

      //   config: [],
      columns: [
        {
          field: "nickName",
          title: "用户姓名",
        },
        {
          field: "userName",
          title: "登录账号",
        },
        {
          field: "phonenumber",
          title: "手机号",
        },
        {
          field: "permissionEnableFlag",
          title: "状态",
          slots: { default: "status" },
        },
        {
          title: "操作",
          minWidth: 260,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableId: "planConfigTable", //tableId必须项目唯一，用于缓存展示列
      businessTypeOptions: [],
      token: "",
    };
  },
  computed: {
    config() {
      return [
        {
          key: "nickName",
          title: "用户姓名",
          type: "input",
          placeholder: "用户姓名",
        },
        {
          key: "permissionEnableFlag",
          title: "状态",
          type: "select",
          placeholder: "状态",
          options: this.groupStatusOptions,
        },
      ];
    },
  },
  created() {
    this.token = getToken();
  },
  activated() {
    this.getDicts("business_type").then((response) => {
      this.businessTypeOptions = response.data;
    });
    //用于获取查询条件的参数option，如果查询参数没有接口获取的可以直接this.initConfig()
    Promise.all([]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.queryData();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    handleLog(row) {
      this.logVisible = true;
      queryLog().then((res) => {
        this.recordList = res.data;
      });
    },
    //状态切换
    handleStatusChange(row) {
      const text = row.permissionEnableFlag == "0" ? "启用" : "停用";
      changeStatus({
        permissionEnableFlag: row.permissionEnableFlag,
        userId: row.userId,
      })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
          } else {
            row.permissionEnableFlag = row.permissionEnableFlag == 1 ? 0 : 1;
          }
        })
        .catch(function() {
          row.permissionEnableFlag = row.permissionEnableFlag == 1 ? 0 : 1;
        });
    },

    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = params.pageSize;
      this.queryData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.queryData();
    },

    //获取项目列表
    queryData() {
      this.loading = true;

      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      // this.finallySearch = args;
      queryPermissionList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //新增/修改
    handleEdit(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/configManage/accountPermission/edit",
        query: {
          userId: row.userId,
          nickName: row.nickName,
          userName: row.userName,
        },
      });
    },

    //分页切换
    changePage() {
      if (this.finallySearch) {
        this.finallySearch.pageNum = this.searchForm.pageNum;
        this.finallySearch.pageSize = this.searchForm.pageSize;
      }
      this.queryData(this.finallySearch);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/ .el-select-group__title {
    padding-left: 10px;
  }

  /deep/ .el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }

  /deep/ .el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
