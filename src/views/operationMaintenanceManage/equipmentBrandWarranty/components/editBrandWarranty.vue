//新增/编辑品牌质保
<template>
  <el-dialog
    title="新增/编辑品牌质保"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleCancel"
    v-loading="loading"
    element-loading-text="数据对比中，请耐心等待..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.7)"
  >
    <el-form
      :model="checkForm"
      ref="checkForm"
      label-width="150px"
      :rules="rules"
    >
      <el-form-item label="设备品牌:" prop="brandId">
        <el-select
          v-model="checkForm.brandId"
          placeholder="请选择"
          style="width: 85%;"
          @change="brandIdChange"
          filterable
          clearable
          :disabled="brandWarrantyObj.type !== 'add'"
        >
          <el-option
            v-for="item in brandOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="质保有效期:" prop="warrantyTime">
        <el-input
          v-model="checkForm.warrantyTime"
          style="width: 80%;margin-right: 20px;"
          @input="inpChange"
          clearable
          :disabled="brandWarrantyObj.type !== 'add'"
        ></el-input
        ><span>年</span>
      </el-form-item>
      <el-form-item label="设备型号:" prop="table">
        <el-table
          :data="tableData"
          bordered
          class="comment-table"
          style="width: 85%"
        >
          <el-table-column fixed prop="modelName" label="型号名称">
          </el-table-column>
          <el-table-column prop="warrantyTime" label="质保期（年）">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.warrantyTime"
                style="width: 50%;"
                :min="0"
                :precision="0"
                :max="9999"
              ></el-input-number>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" size="medium">取 消</el-button
      ><el-button type="primary" @click="handleSubmit" size="medium"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api/operationMaintenanceManage/equipmentBrandWarranty/index.js";
import moment from "moment";
export default {
  components: {},
  data() {
    return {
      loading: false,
      visible: false,
      rules: {
        warrantyTime: [
          { required: true, message: "请输入质保有效期", trigger: "blur" },
          {
            pattern: /^(0|[1-9]\d{0,3}|9999)$/,
            message: "请输入正确的数字",
          },
        ],
        brandId: [
          { required: true, message: "请选择设备品牌", trigger: "change" },
        ],
      },
      checkForm: { brandId: "", warrantyTime: undefined },
      brandOptions: [],
      tableData: [],
      brandWarrantyObj: {},
    };
  },
  async created() {},
  mounted() {},
  methods: {
    async getBusinessList() {
      const res = await api.queryABrandList();
      this.brandOptions = res.data.map((item) => {
        return {
          label: item?.brandName,
          value: item?.brandId,
        };
      });
    },
    async brandIdChange(event) {
      const params = {
        brandId: event,
      };
      const res = await api.queryModelNameById(params);
      this.tableData = res?.data.map((item) => {
        return { ...item, warrantyTime: undefined };
      });
    },
    inpChange(event) {
      if (this.brandWarrantyObj?.type === "add") {
        this.tableData.forEach((item) => {
          item.warrantyTime = event;
        });
      }
    },
    handleCancel() {
      this.visible = false;
      this.checkForm = { brandId: "", warrantyTime: undefined };
      this.brandOptions = [];
      this.tableData = [];
      this.brandWarrantyObj = {};
      this.$refs.checkForm.resetFields();
    },
    open(obj) {
      this.brandWarrantyObj = obj;
      this.visible = true;
      console.log(this.brandWarrantyObj);
      this.getBusinessList();
      if (this.brandWarrantyObj.type !== "add") {
        const { brandId, warrantyTime, modelName } = obj;
        this.checkForm = {
          brandId,
          warrantyTime,
        };
        this.tableData = [{ warrantyTime, modelName, brandId }];
      }
    },
    handleSubmit() {
      this.$refs.checkForm.validate(async (valid) => {
        if (!valid) return;
        console.log("提交的数据", this.checkForm, this.tableData);
        if (this.tableData.length === 0) {
          this.$message.error("该品牌没有设备型号/已存在质保有效期！");
          return;
        }
        if (this.brandWarrantyObj?.type === "add") {
          const params = {
            cmBrandWarrantys: this.tableData.map((item) => {
              return {
                ...item,
                brandName: this.brandOptions.filter((itemSub) => {
                  return itemSub.value === item.brandId;
                })[0]?.label,
              };
            }),
          };
          const res = await api.saveBrandWarranty(params);
          if (res.code == "10000") {
            this.$message.success("新增成功");
            this.handleCancel();
            this.$emit("refreshDataList");
          }
        } else {
          const params = {
            brandWarrantyId: this.brandWarrantyObj?.brandWarrantyId,
            // updateTime: moment(new Date()).format("YYYY-MM-DD HH:MM:SS"),
            ...this.tableData[0],
            brandName: this.brandOptions.filter((itemSub) => {
              return itemSub.value === this.tableData[0].brandId;
            })[0]?.label,
          };
          const res = await api.updateBrandWarranty(params);
          if (res.code == "10000") {
            this.$message.success("更新成功");
            this.handleCancel();
            this.$emit("refreshDataList");
          }
        }

        // updateCheckStation(params).then((res) => {
        //   if (res.code == "10000") {
        //     this.$message.success("更新成功");
        //     this.handleCancel();
        //     this.$emit("refreshDataList");
        //   }
        // });
      });
    },
  },
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: center;
}
/deep/ .comment-table thead th {
  background: #f5f5f5 !important;
}
</style>
