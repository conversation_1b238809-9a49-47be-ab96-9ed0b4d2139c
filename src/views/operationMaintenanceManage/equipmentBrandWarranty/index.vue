//项目投建批次列表
<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @loadData="loadData"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      tabType="card"
    >
      <template #toolbar_buttons>
        <el-button
          icon="el-icon-plus"
          v-has-permi="['maintenance:equipmentBrandWarranty:add']"
          type="primary"
          @click="addBrandWarranty"
          >新增</el-button
        >
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <!-- <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['maintenance:equipmentBrandWarranty:export']"
            >导出
          </el-button> -->
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
    <editBrandWarranty
      ref="editBrandWarranty"
      @refreshDataList="loadData"
    ></editBrandWarranty>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/operationMaintenanceManage/equipmentBrandWarranty/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import moment from "moment";
import editBrandWarranty from "./components/editBrandWarranty.vue";
export default {
  name: "investBatchList",
  components: { editBrandWarranty },
  mixins: [exportMixin],
  data() {
    return {
      radiusStatus: 0,
      fileActiveName: "01",
      radioList: [
        { dictValue: "01", dictLabel: "踏勘单" },
        { dictValue: "02", dictLabel: "场地双章投建协议" },
        { dictValue: "03", dictLabel: "采购下单" },
        { dictValue: "04", dictLabel: "付款单" },
      ],
      recordList: [],
      recordParams: {},
      businessTypeOptions: [],
      orderTypeOptions: [],
      activeTab: "0",
      //buse参数-s

      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        // showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "projectBatchId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "orderNotification",
      //buse参数-e
      selectedData: [],
      foldObj: {
        baseInfo: false,
        timeInfo: false,
        costInfo: false,
      },
      brandNameOptions: [],
      modelNameOptions: [],
      brandName: "",
    };
  },

  watch: {
    brandName: {
      handler(val) {
        this.getModelNameOptions(val);
      },
      immediate: true,
    },
    // fileActiveName: {
    //   handler(val) {
    //     console.log(val, "---tab");
    //   },
    // },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.getBrandNameOptions();
  },
  mounted() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    this.$nextTick(() => {
      this.loadData();
    });
  },
  methods: {
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    getBrandNameOptions() {
      api.queryBrandOptions({}).then((res) => {
        this.brandNameOptions = res.data?.map((x) => {
          return { value: x, label: x };
        });
      });
    },
    getModelNameOptions(val) {
      api.queryModelOptions({ brandName: val }).then((res) => {
        this.modelNameOptions = res.data?.map((x) => {
          return { value: x, label: x };
        });
      });
    },
    addBrandWarranty() {
      this.$refs.editBrandWarranty.open({ type: "add" });
    },

    handleCollapse(type) {
      this.foldObj[type] = !this.foldObj[type];
    },
    handleCancelCustom() {
      this.$refs.crud.switchModalView(false);
    },

    checkPermission,

    handleExport() {
      let params = {
        ...this.params,
      };

      this.handleCommonExport(api.export, params);
    },
    async deleteRow(row) {
      this.$confirm(`是否确定删除${row?.brandName}`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let params = {
          ...row,
        };
        const res = await api.deleteBrandWarranty(params);
        if (res.code == "10000") {
          this.$message.success("删除成功");
          this.loadData();
        }
      });
    },
    async loadData() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const res = await api.queryBrandWarrantyList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
      // if (this.brandNameOptions.length === 0) {
      //   const brandNameOptions = res.data.map((item) => {
      //     return {
      //       label: item?.brandName,
      //       value: item?.brandName,
      //     };
      //   });
      //   this.brandNameOptions = Array.from(
      //     new Set(brandNameOptions.map(JSON.stringify))
      //   ).map(JSON.parse);
      // }
      // if (this.modelNameOptions.length === 0) {
      //   const modelNameOptions = res.data.map((item) => {
      //     return {
      //       label: item?.modelName,
      //       value: item?.modelName,
      //     };
      //   });

      //   this.modelNameOptions = Array.from(
      //     new Set(modelNameOptions.map(JSON.stringify))
      //   ).map(JSON.parse);
      // }
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.brandName = "";
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "brandName",
          title: "设备品牌",
          width: 120,
        },
        { field: "modelName", title: "设备型号", width: 200 },
        { field: "warrantyTime", title: "质保有效期（年）", width: 100 },
        { field: "createTime", title: "创建时间", width: 200 },
        { field: "createBy", title: "创建人", width: 100 },
        {
          field: "updateTime",
          title: "修改时间",
          width: 200,
        },
        { field: "updateBy", title: "修改人", width: 100 },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "110px",
        //筛选控件配置
        config: [
          {
            field: "brandName",
            title: "品牌名称",
            element: "el-select",
            props: {
              options: this.brandNameOptions,
              optionLabel: "label", //自定义选项名
              optionValue: "value", //自定义选项值
              filterable: true,
            },
            on: {
              change: (val) => {
                this.brandName = val;
              },
            },
          },
          {
            field: "modelName",
            title: "设备型号",
            element: "el-select",
            props: {
              options: this.modelNameOptions,
              optionLabel: "label", //自定义选项名
              optionValue: "value", //自定义选项值
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },
    recordConfig() {
      return [
        {
          field: "applyFormType",
          title: "类型",
          element: "el-select",
          props: {
            options: this.applyFormTypeOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
          on: {
            change: (val) => {
              this.getOmApproveNoOptions(val);
            },
          },
          itemProps: {
            labelWidth: "80px",
          },
        },
        {
          field: "omApproveNo",
          title: "运管申请单号",
          element: "el-select",
          props: {
            options: this.omApplyNoOptions,
            filterable: true,
          },
        },
      ];
    },
    modalConfig() {
      const form = {};
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 260,
        menuFixed: "right",
        modalWidth:
          this.operationType === "record"
            ? "70%"
            : this.operationType === "upload"
            ? "60%"
            : "50%",

        customOperationTypes: [
          {
            title: "删除",
            typeName: "confirm",
            modalTitle: "删除",
            event: (row) => {
              return this.deleteRow(row);
            },
            condition: (row) => {
              return checkPermission([
                "maintenance:equipmentBrandWarranty:delete",
              ]);
            },
          },

          {
            title: "编辑",
            typeName: "record",
            slotName: "record",
            showForm: false,
            modalTitle: "编辑",
            event: (row) => {
              return this.$refs.editBrandWarranty.open({
                type: "edit",
                ...row,
              });
            },
            condition: (row) => {
              return checkPermission([
                "maintenance:equipmentBrandWarranty:edit",
              ]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: ["orderNotification", "transfer"].includes(
            this.operationType
          )
            ? "160px"
            : "110px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.file-link {
  padding: 20px 20px 0;
}
</style>
