<template>
  <el-dialog
    title="评价标准配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    width="600px"
    destroy-on-close
  >
    <el-form
      :model="form"
      ref="form"
      label-width="80px"
      :rules="rules"
      :disabled="disabled"
    >
      <el-form-item label="评价名称" prop="commentName">
        <el-input v-model="form.commentName" />
      </el-form-item>
      <el-form-item label="分值" prop="score">
        <el-input-number
          v-model="form.score"
          :precision="0"
          :step="1"
          :min="0"
          :max="9999"
          controls-position="right"
          style="width: 95%;"
        />
        分
      </el-form-item>
      <el-form-item label="名称描述" prop="commentDesc">
        <el-input
          v-model="form.commentDesc"
          type="textarea"
          :rows="5"
          maxlength="500"
          show-word-limit
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click.stop="closeVisible">取消 </el-button>
      <el-button
        :loading="loading"
        type="primary"
        @click="submit"
        :disabled="loading"
        >确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  addEvaluate,
  updateEvaluate,
} from "@/api/operationMaintenanceManage/evaluate/index";
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      form: {
        commentName: null,
        commentDesc: null,
        score: undefined,
      },

      loading: false,
      rules: {
        commentName: [
          { required: true, message: "请输入名称", trigger: "blur" },
        ],
        score: [{ required: true, message: "请输入分值", trigger: "blur" }],
      },
    };
  },
  methods: {
    closeVisible() {
      this.visible = false;
      this.form = {
        commentName: null,
        commentDesc: null,
        score: undefined,
      };
    },
    submit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          const params = {
            ...this.form,
          };

          let res = null;
          try {
            if (this.form.commentId) {
              res = await updateEvaluate(params);
            } else {
              res = await addEvaluate(params);
            }
          } catch (e) {
            this.loading = false;
          }

          if (res.code === "10000") {
            this.loading = false;
            this.$message.success("保存成功");
            this.visible = false;
            this.$emit("afterSubmit");
          } else {
            this.loading = false;
            this.$message.error(res.message);
          }
        }
      });
    },
    open(val) {
      this.visible = true;
      if (val) {
        this.form = JSON.parse(JSON.stringify(val));
      }
    },
  },
};
</script>
