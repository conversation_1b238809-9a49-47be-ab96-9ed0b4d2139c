<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"

    >
    <!--       :showExportButton="checkPermission(['maintenance:evaluate:export'])"
      @handleExport="handleExport" -->
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click="openConfig"
            v-hasPermi="['maintenance:evaluate:add']"
            >评价标准配置
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="(v) => changeStatus(v, row)"
            v-hasPermi="['maintenance:evaluate:changeStatus']"
          />
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-hasPermi="['maintenance:evaluate:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="deleteEvaluate(row)"
            v-hasPermi="['maintenance:evaluate:delete']"
          >
            删除
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <Dialog ref="dialog" @afterSubmit="getList" />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";

import { getToken } from "@/utils/auth";

import {
  queryEvaluateList,
  changeEvaluateStatus,
  exportEvaluatelist,
  delelteEvaluate,
} from "@/api/operationMaintenanceManage/evaluate/index";

import { statusList } from "./constant";
import Dialog from "./component/dialog.vue";

export default {
  name: "evaluate",
  components: {
    AdvancedForm,
    GridTable,
    Dialog,
  },
  data() {
    return {
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      stationId: undefined,
      columns: [
        {
          field: "commentName",
          title: "评价名称",
        },

        {
          field: "commentDesc",
          title: "名称描述",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "score",
          title: "分值",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "createBy",
          title: "创建人",
          showOverflowTooltip: true,
        },

        {
          field: "createTime",
          showOverflowTooltip: true,
          title: "创建时间",
        },
        {
          field: "updateBy",
          showOverflowTooltip: true,
          title: "修改人",
          customWidth: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
        },
        {
          field: "status",
          title: "状态",
          slots: { default: "status" },
        },

        {
          title: "操作",
          minWidth: 150,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        commentName: "",
        status: null,
      },
      total: 0,
      loading: false,
      rowInfo: {},
      pageType: "detail",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      submitLoading: false,
    };
  },
  async created() {},
  mounted() {
    this.token = getToken();
    this.getList();
  },
  methods: {
    checkPermission,
    async changeStatus(v, row) {
      const data = await changeEvaluateStatus({
        status: v + "",
        commentId: row.commentId,
      });
      if (data.code === "10000") {
        this.$message.success("操作成功");
      }
      this.getList();
    },
    openConfig(row = null) {
      this.$refs.dialog.open(row);
    },

    handleExport() {
      const params = this.getQueryParams();
      this.$confirm("是否确认导出所有数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportEvaluatelist(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },

    showDetail(row) {
      this.openConfig(row);
    },
    deleteEvaluate(row) {
      this.$confirm("是否删除该评价项？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await delelteEvaluate({ commentId: row.commentId });
        if (res.code === "10000") {
          this.$message.success("删除成功");
          this.getList();
        } else {
          this.$message.error("删除失败");
        }
      });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getQueryParams() {
      const { commentName, status, pageNum, pageSize } = this.searchForm;

      const params = {
        pageNum,
        pageSize,
        commentName,
        status,
      };

      return params;
    },
    getList() {
      this.loading = true;
      let params = this.getQueryParams();
      queryEvaluateList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {},

    close() {
      this.tagManageVisible = false;
    },
  },
  computed: {
    config() {
      return [
        {
          key: "commentName",
          title: "评价名称",
          type: "input",
          placeholder: "请填写评价名称",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "请选择状态",
          options: statusList,
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
