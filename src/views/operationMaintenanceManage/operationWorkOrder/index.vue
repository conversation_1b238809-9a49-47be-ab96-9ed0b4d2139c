<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['maintenance:workOrder:export'])"
      @handleExport="handleExport"
    >
      <template slot="orderTypeName">
        <el-row>
          <el-col :span="8">
            <el-select
              v-model="searchForm.orderTypeParentNames"
              clearable
              filterable
              multiple
              style="width: 100%"
              @change="
                (val) => {
                  return orderTypeParentChange(val, 2);
                }
              "
            >
              <el-option
                v-for="item in orderTypeParentOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.orderTypeNames"
              clearable
              filterable
              multiple
              style="width: 100%"
              :disabled="searchForm.tag == '1' || searchForm.tag == '2'"
              @change="
                (val) => {
                  return orderTypeParentChange(val, 3);
                }
              "
            >
              <el-option
                v-for="item in orderTypeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.threeOrderTypeNames"
              clearable
              filterable
              multiple
              style="width: 100%"
              :disabled="searchForm.tag == '1' || searchForm.tag == '2'"
            >
              <el-option
                v-for="(item, index) in threeOrderTypeOptions"
                :label="item.label"
                :value="item.label"
                :key="index"
              />
            </el-select>
          </el-col>
        </el-row>
      </template>
      <template slot="faultType">
        <el-row>
          <el-col :span="8">
            <el-select
              v-model="searchForm.oneFaultType"
              @change="handleFirstFaultStrChange"
              style="width: 100%;"
              clearable
              filterable
            >
              <el-option
                v-for="item in firstFaultOptions"
                :label="item.typeName"
                :value="item.id"
                :key="item.id"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.twoFaultType"
              @change="handleSecondFaultStrChange"
              style="width: 100%;"
              clearable
              filterable
            >
              <el-option
                v-for="item in secondFaultOptions"
                :label="item.typeName"
                :value="item.id"
                :key="item.id"
              />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-select
              v-model="searchForm.threeFaultType"
              style="width: 100%;"
              clearable
              filterable
            >
              <el-option
                v-for="item in thirdFaultOptions"
                :label="item.typeName"
                :value="item.id"
                :key="item.id"
              />
            </el-select>
          </el-col>
        </el-row>
      </template>
    </AdvancedForm>
    <el-tabs v-model="activeName" @tab-click="handleTabClick" type="card">
      <el-tab-pane label="全部" name="0"></el-tab-pane>
      <el-tab-pane label="待处理" name="1"></el-tab-pane>
      <el-tab-pane label="已处理" name="2"></el-tab-pane>
      <el-tab-pane label="我转派的" name="3"></el-tab-pane>
    </el-tabs>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :checkbox="true"
        :batchDelete="true"
        @handleSelectionChange="tableSelect"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <!--          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['maintenance:workOrder:create']"
            @click.stop="handleCreate"
          >
            创建工单
          </el-button>-->
          <!--          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['maintenance:workOrder:type']"
            @click.stop="handleOrderType"
          >
            工单类型
          </el-button>-->
          <!--          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            v-has-permi="['maintenance:workOrder:faultType']"
            @click.stop="handleFaultType"
          >
            故障类别
          </el-button>-->

          <el-button
            size="mini"
            type="primary"
            :icon="item.icon"
            @click.stop="item.clickFn()"
            v-for="(item, index) in toolBarBtnList"
            :key="index"
          >
            {{ item.title }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click.stop="item.clickFn(row)"
            v-for="(item, index) in operationBtnList"
            :key="index"
            v-show="item.show(row)"
            v-has-permi="[item.permission]"
          >
            {{ item.title }}
          </el-button>

          <!--          <el-button
            type="text"
            size="large"
            @click="handleRemark(row)"
            v-if="showOperateButton(row, 'remark')"
            v-has-permi="['maintenance:workOrder:remark']"
          >
            备注
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click.stop="openEditPage(row)"
            v-if="showOperateButton(row, 'edit')"
            v-has-permi="['maintenance:workOrder:edit']"
          >
            编辑
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="transfer(row)"
            v-if="showOperateButton(row, 'handleOver')"
            v-has-permi="['maintenance:workOrder:handleOver']"
          >
            转派
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="showHandleDialog(row)"
            v-if="showOperateButton(row, 'handle')"
            v-has-permi="['maintenance:workOrder:handle']"
          >
            处理
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="stopOrder(row)"
            v-if="showOperateButton(row, 'finish')"
            v-has-permi="['maintenance:workOrder:stop']"
          >
            结束
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="handleRemind(row)"
            v-if="showOperateButton(row, 'remind')"
            v-has-permi="['maintenance:workOrder:remind']"
          >
            催单
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['maintenance:workOrder:detail']"
          >
            详情
          </el-button>-->
          <!--          <el-button-->
          <!--            type="text"-->
          <!--            size="large"-->
          <!--            @click="check(row)"-->
          <!--            v-if="showOperateButton(row, 'check')"-->
          <!--            v-has-permi="['maintenance:workOrder:check']"-->
          <!--          >-->
          <!--            审核-->
          <!--          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="updateTag(row)"
            v-if="showOperateButton(row, 'tag')"
            v-has-permi="['maintenance:workOrder:tag']"
          >
            标签
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="handleRadius(row)"
            v-has-permi="['maintenance:workOrder:radius']"
          >
            处理半径
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="handleCancel(row)"
            v-has-permi="['maintenance:workOrder:cancel']"
          >
            取消
          </el-button>-->
          <!--          <el-button
            type="text"
            size="large"
            @click="handleReceive(row)"
            v-has-permi="['maintenance:workOrder:receive']"
          >
            接单
          </el-button>-->
        </template>
        <template slot="stationTag" slot-scope="{ row }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in row.stationNameArr"
          >
            {{ item }}
          </el-tag>
        </template>
        <template slot="orderDesc" slot-scope="{ row }">
          <el-button type="text" size="large" @click="openDescDetail(row)"
            >查看
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row, column }">
          <span :style="row.isTimeout === 'Y' ? 'color:red' : ''">{{
            row[column.property] === "Y"
              ? "是"
              : row[column.property] === "N"
              ? "否"
              : row[column.property]
          }}</span>
        </template>
        <template slot="tags" slot-scope="{ row }">
          <!-- <el-tag
            class="tags-item"
            :key="item"
            v-for="item in getFaultTypeArr(row)"
            type="info"
          >
            <el-tooltip :content="item" placement="top-start">
              <span class="tags-span">{{ item }}</span>
            </el-tooltip>
          </el-tag> -->
          <el-tooltip placement="top">
            <div slot="content">
              <el-tag
                class="tags-tooltip-item"
                type="info"
                :key="item"
                v-for="item in getFaultTypeArr(row)"
                >{{ item }}
              </el-tag>
            </div>
            <div style="display:flex;justify-content: center;">
              <el-tag
                type="info"
                class="tags-span"
                v-for="x in getFaultTypeArr(row, 2)"
                :key="x"
              >
                {{ x }}
              </el-tag>
              <span
                v-if="getFaultTypeArr(row).length > 2"
                style="margin-right: 10px; margin-bottom: 5px; display: inline-block;"
                >...</span
              >
            </div>
          </el-tooltip>
          <!-- <el-tag
            type="info"
            v-for="x in getFaultTypeArr(row)"
            :key="x"
            class="tags-span"
          >
            {{ x }}</el-tag
          > -->
        </template>
      </GridTable>
    </el-card>
    <!-- 审核工单-S -->
    <el-dialog
      title="审核工单"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeVisible"
      append-to-body
      width="50%"
    >
      <el-form :model="form" ref="form" label-width="110px" :rules="rules">
        <el-form-item label="工单状态" prop="orderStatus">
          <el-select v-model="form.orderStatus" style="width: 100%;" filterable>
            <el-option
              v-for="item in checkOrderOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知组" prop="groupIds">
          <el-select
            v-model="form.groupIds"
            multiple
            filterable
            @change="handleGroupChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupOptions"
              :label="item.groupName"
              :value="item.groupId"
              :key="item.groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知人" prop="userIds">
          <el-select
            v-model="form.userIds"
            multiple
            filterable
            @change="handleGroupMemberChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupMemberOptions"
              :label="item.nickName + '-' + item.userName"
              :value="item.userId"
              :key="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="form.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备注" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="checkLoading" @click.stop="closeVisible"
          >取消
        </el-button>
        <el-button :loading="checkLoading" type="primary" @click="submit"
          >确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 审核工单-E -->

    <!-- 转派工单-S -->
    <el-dialog
      title="转派工单"
      :visible.sync="handleOverVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="transferForm"
        ref="transferForm"
        label-width="110px"
        :rules="transferRules"
      >
        <el-form-item label="选择人员" prop="handleUser">
          <el-select
            v-model="transferForm.handleUser"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in transferUserOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原因" prop="remark">
          <el-input
            v-model="transferForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="transferForm.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <!-- <el-form
        :model="transferform"
        ref="transferform"
        label-width="110px"
        :rules="rules1"
        v-if="this.channel !== '03'"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model="transferform.deptId"
                :options="deptOptions"
                placeholder="请选择归属部门"
                @select="handleNodeClick"
                :beforeClearAll="beforeClearAll"
                :default-expand-level="1"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="人员" prop="handleUser">
              <el-select
                v-model="transferform.handleUser"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in userOption"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="transferform.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="改派原因" prop="remark">
          <el-input
            v-model="transferform.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <el-form
        :model="transferform2"
        ref="transferform2"
        label-width="110px"
        :rules="rules2"
        v-else
      >
        <el-form-item label="处理组" prop="groupIds">
          <el-select
            v-model="transferform2.groupIds"
            multiple
            @change="handleGroupIdsChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupOptions"
              :label="item.groupName"
              :value="item.groupId"
              :key="item.groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知人" prop="userIds">
          <el-select
            v-model="transferform2.userIds"
            multiple
            @change="handleGroupMemberChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupHandleMemberOptions"
              :label="item.nickName + '-' + item.userName"
              :value="item.userId"
              :key="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="transferform2.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="改派原因" prop="remark">
          <el-input
            v-model="transferform2.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form> -->
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit()">提交</el-button>
      </div>
    </el-dialog>
    <!-- 转派工单-E -->
    <!-- 结束工单-S -->
    <el-dialog
      title="结束工单"
      :visible.sync="stopVisible"
      @close="closeStopDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="stopForm" ref="stopForm" label-width="110px">
        <el-form-item label="结束原因" prop="remark">
          <el-input
            v-model="stopForm.remark"
            type="textarea"
            maxlength="500"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeStopDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleStopSubmit()"
          >提交
        </el-button>
      </div>
    </el-dialog>
    <!-- 结束工单-E -->
    <!-- 工单标签-S -->
    <el-dialog
      title="工单标签"
      :visible.sync="tagManageVisible"
      @close="close"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form :model="tagForm" ref="tagForm" label-width="110px">
        <el-form-item
          label="处理标签"
          prop="handleTag"
          :rules="{
            required: true,
            message: '处理标签不能为空',
            trigger: 'blur',
          }"
        >
          <el-select v-model="tagForm.handleTag" style="width: 100%;">
            <el-option
              v-for="item in handleTageOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="变更原因" prop="remark">
          <el-input
            v-model="tagForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="close">取 消</el-button>
        <el-button type="primary" @click.stop="handleTagSubmit()"
          >提交
        </el-button>
      </div>
    </el-dialog>
    <!-- 工单标签-E -->
    <!-- 工单处理-S -->
    <el-dialog
      title="工单处理"
      :visible.sync="handleDialogVisible"
      @close="close"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="handleForm"
        ref="handleForm"
        label-width="110px"
        :rules="handleFormRules"
      >
        <el-form-item label="处理类型" prop="handleType">
          <el-select v-model="handleForm.handleType" style="width: 100%;">
            <el-option
              v-for="item in handleTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理方式" prop="handleMethod">
          <el-select
            v-model="handleForm.handleMethod"
            multiple
            style="width: 100%;"
          >
            <el-option
              v-for="item in handleMethodOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="故障原因" prop="faultType">
          <el-select
            v-model="handleForm.faultType"
            multiple
            style="width: 100%;"
          >
            <el-option
              v-for="item in faultTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="处理说明" prop="handleDesc">
          <el-input
            v-model="handleForm.handleDesc"
            type="textarea"
            :rows="5"
            maxlength="500"
            style="width: 100%;"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="处理照片" prop="remark">
          <file-upload
            type="img"
            ref="upload"
            :limit="9"
            textTip="支持批量上传，上传格式为jpg/png文件"
          />
        </el-form-item>
        <el-form-item label="处理标签" prop="handleTag">
          <el-select v-model="handleForm.handleTag" style="width: 100%;">
            <el-option
              v-for="item in handleTageOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeHandleDialog">取 消</el-button>
        <el-button :loading="saveLoading" @click.stop="save">保存</el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click.stop="handleDialogSubmit()"
          >提交
        </el-button>
      </div>
    </el-dialog>
    <!-- 工单处理-E -->
    <el-drawer title="查看问题描述" :visible.sync="drawerVisible" size="60%">
      <div v-html="orderDesc" class="order-desc"></div>
    </el-drawer>
    <!-- 备注-S -->
    <el-dialog
      title="备注"
      :visible.sync="remarkVisible"
      @close="closeRemarkDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="remarkForm"
        ref="remarkForm"
        label-width="110px"
        :rules="remarkFormRules"
      >
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="remarkForm.remark"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="请输入"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeRemarkDialog">取 消</el-button>
        <el-button
          :loading="remarkLoading"
          @click.stop="saveRemark"
          type="primary"
          >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 备注-E -->
    <!-- 调整处理半径-S -->
    <el-dialog
      title="调整处理半径"
      :visible.sync="radiusVisible"
      @close="closeRadiusDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="radiusForm"
        ref="radiusForm"
        label-width="110px"
        :rules="radiusFormRules"
      >
        <el-form-item label="处理半径" prop="handleRadius">
          <el-input v-model="radiusForm.handleRadius" type="number">
            <template slot="append">m</template>
          </el-input>
        </el-form-item>
        <el-form-item label="原因" prop="remark">
          <el-input
            v-model="radiusForm.remark"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="500个字符以内"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeRadiusDialog">取 消</el-button>
        <el-button @click.stop="saveRadius" type="primary">确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 调整处理半径-E -->
    <!-- 取消-S -->
    <el-dialog
      title="取消工单"
      :visible.sync="cancelVisible"
      @close="closeCancelDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="cancelForm"
        ref="cancelForm"
        label-width="110px"
        :rules="cancelRules"
      >
        <el-form-item label="原因" prop="remark">
          <el-input
            v-model="cancelForm.remark"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
            placeholder="500个字符以内"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeCancelDialog">取 消</el-button>
        <el-button @click.stop="saveCancel" type="primary">确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 取消-E -->
    <!-- 催单-S -->
    <el-dialog
      title="催单"
      :visible.sync="remindVisible"
      @close="closeRemindDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="remindForm"
        ref="remindForm"
        label-width="110px"
        :rules="remindRules"
      >
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="remindForm.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeRemindDialog">取 消</el-button>
        <el-button @click.stop="saveRemind" type="primary">确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 催单-E -->
    <!-- 创建工单-S -->
    <el-dialog
      title="创建工单"
      :visible.sync="createVisible"
      @close="closeCreateDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="createForm"
        ref="createForm"
        label-width="110px"
        :rules="createRules"
      >
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            v-model="createForm.businessType"
            filterable
            style="width: 100%"
            @change="handleBusinessTypeChange"
          >
            <el-option
              v-for="item in businessTypeOptions"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工单类型" prop="orderType">
          <el-select
            v-model="createForm.orderType"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in bindOrderTypeOptions"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeCreateDialog">取消</el-button>
        <el-button @click.stop="handleNextCreate" type="primary"
          >下一步</el-button
        >
      </div>
    </el-dialog>
    <!-- 创建工单-E -->

    <!-- 评价dialog -->
    <evaluateDialog ref="evaluateDialog" @afterSubmit="handleQuery" />
  </div>
</template>

<script>
import {
  check,
  childrenMultiList,
  exportExcel,
  fistLevelList,
  handleDetail,
  handleOrder,
  pushMsg,
  queryList,
  stopOrder,
  submitRemark,
  transferOrder,
  updateOrderHandleTag,
  getDeptList,
  submitRadius,
  receiveOrder,
  submitCancel,
  queryBindOrderTypeList,
  getNewOrgNo,
  getReport,
  queryTransferUser,
  queryPlatformMerchant,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import checkPermission from "@/utils/permission.js";
import { queryTreeList } from "@/api/workOrderType/index.js";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { regionData } from "element-china-area-data";
import { queryGroupList, queryGroupMemberList } from "@/api/errorPush/index.js";
import { childFaultList, firstFaultList } from "@/api/faultType/index.js";
import { mapGetters } from "vuex";
import { listAllUser, listDept } from "@/api/common";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import FileUpload from "@/components/Upload/fileUpload2.vue";
import { getToken } from "@/utils/auth";
import { queryLevelList } from "@/api/station/station";
import {
  MAINTENANCE_ORDER_CLICK_PUSH,
  MAINTENANCE_ORDER_CLICK_REMARK,
  MAINTENANCE_ORDER_CLICK_REPORT,
  MAINTENANCE_ORDER_FINISH_SUCCESS,
  MAINTENANCE_ORDER_FLLOWUP_SUCCESS,
  MAINTENANCE_ORDER_HANDLE_SUCCESS,
  MAINTENANCE_ORDER_REJECT_SUCCESS,
  MAINTENANCE_ORDER_STOP_SUCCESS,
  MAINTENANCE_ORDER_TRANSFER_SUCCESS,
} from "@/utils/track/track-event-constants";
import { timeDiffFormat } from "@/utils/comm";
import evaluateDialog from "./components/evaluateDialog.vue";
import { orderDetail } from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
export default {
  name: "operationWorkOrderNew",
  components: {
    FileUpload,
    AdvancedForm,
    GridTable,
    Treeselect,
    evaluateDialog,
  },
  data() {
    return {
      transferUserOptions: [],
      bindOrderTypeOptions: [],
      createVisible: false,
      createForm: {
        businessType: "1",
        orderType: "",
      },
      createRules: {
        businessType: [
          { required: true, message: "请选择业务类型", trigger: "change" },
        ],
        orderType: [
          { required: true, message: "请选择工单类型", trigger: "change" },
        ],
      },
      remindVisible: false,
      remindForm: {
        messageTypes: [],
      },
      remindRules: {
        messageTypes: [
          { required: true, message: "请选择通知方式", trigger: "change" },
        ],
      },
      radiusVisible: false,
      radiusForm: {
        handleRadius: 2000,
        remark: "",
      },
      cancelVisible: false,
      cancelForm: {
        remark: "",
      },
      cancelRules: {
        remark: [{ required: true, message: "请输入原因", trigger: "blur" }],
      },
      radiusFormRules: {
        handleRadius: [{ validator: this.customValidation, trigger: "change" }],
      },
      transferForm: {
        handleUser: undefined,
        messageTypes: ["01", "02", "03"],
        remark: "",
      },
      transferRules: {
        handleUser: [
          { required: true, message: "请选择处理人员", trigger: "change" },
        ],
      },
      activeName: "0",
      transferform2: {
        groupIds: [],
        userIds: [],
        messageTypes: ["01", "02", "03"],
        remark: undefined,
        orderNo: undefined,
      },
      orderId: "",
      firstFaultOptions: [],
      secondFaultOptions: [],
      thirdFaultOptions: [],
      groupOptions: [],
      groupMemberOptions: [],
      checkOrderOptions: [
        { value: "4", label: "驳回" },
        { value: "7", label: "待回访" },
        { value: "5", label: "已完结" },
      ],
      remarkVisible: false,
      drawerVisible: false,
      remarkLoading: false,
      orderDesc: "",
      // config: [],
      stationId: undefined,
      toolBarBtnList: [
        {
          title: "创建工单",
          icon: "el-icon-plus",
          clickFn: () => {
            this.createVisible = true;
            queryBindOrderTypeList({ businessType: "1" }).then((res) => {
              if (res?.code === "10000") {
                this.bindOrderTypeOptions = res.data;
                this.createForm.orderType = this.bindOrderTypeOptions?.find(
                  (x) => x.typeName === "充电桩故障工单"
                )?.id;
              }
            });
          },
          permission: "maintenance:workOrder:create",
        },
        // {
        //   title: "工单类型",
        //   icon: "el-icon-plus",
        //   clickFn: () => {
        //     this.$router.push({
        //       path: "/workOrderType",
        //     });
        //   },
        //   permission: "maintenance:workOrder:type",
        // },
        // {
        //   title: "故障类别",
        //   icon: "el-icon-plus",
        //   clickFn: () => {
        //     this.$router.push({
        //       path: "/faultType",
        //     });
        //   },
        //   permission: "maintenance:workOrder:faultType",
        // },
      ],
      operationBtnList: [
        {
          title: "编辑",
          clickFn: (row) => {
            //编辑页面开启缓存 如果重新打开其他编辑 则将之前的编辑页面先关闭
            const editPage = this.$store.state.tagsView.visitedViews?.find(
              (x) =>
                x.path === "/operationMaintenanceManage/operationWorkOrder/edit"
            );
            if (editPage) {
              this.$store.dispatch("tagsView/delView", editPage);
            }
            this.$nextTick(() => {
              this.$router.push({
                path: "/operationMaintenanceManage/operationWorkOrder/edit",
                query: {
                  orderId: row.orderId,
                  orderNo: row.orderNo,
                  type: "edit",
                  orderTypeParentId: row.orderTypeParentId,
                  orderTypeParentName: row.orderTypeParentName,
                  businessType: row.businessType,
                  businessTypeName: row.businessTypeName,
                },
              });
            });
          },
          permission: "maintenance:workOrder:handleOver",
          show: (row) => {
            // 待接单
            const orderStatusList = ["8"];
            const orderStatus = orderStatusList.includes(row.orderStatus);
            return orderStatus && this.userId == row.createBy;
          },
        },
        {
          title: "取消",
          clickFn: (row) => {
            this.handleCancel(row);
          },
          permission: "maintenance:workOrder:cancel",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");
            //接单人
            const isSelf = row.createBy == this.userId;
            //（已处理之前）待接单、待处理、处理中
            const statusCondition = ["8", "1", "2"].includes(row.orderStatus);
            const condition2 =
              (isSupOM || isEngOM || isSelf) && statusCondition;
            // 待接单
            // const orderStatusList = ["8"];
            // const orderStatus = orderStatusList.includes(row.orderStatus);
            // const condition1 = orderStatus && this.userId == row.createBy;
            return condition2;
          },
        },
        {
          title: "接单",
          clickFn: (row) => {
            this.handleReceive(row);
          },
          permission: "maintenance:workOrder:receive",
          show: (row) => {
            //处理组内 userid list
            const handleGroupUserIds = row.handleGroupUserIds;

            //处理人
            const handleUser = row.handleUser;

            const createBy = row.createBy;

            // 待接单
            const orderStatusList = ["8"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            return (
              orderStatus &&
              (createBy == this.userId ||
                (handleUser
                  ? handleUser == this.userId
                  : handleGroupUserIds?.includes(this.userId)))
            );
          },
        },
        {
          title: "备注",
          clickFn: (row) => {
            this.handleRemark(row);
          },
          permission: "maintenance:workOrder:remark",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //创建人
            const createBy = row.createBy;

            // 待接单 待处理  处理中 已处理 已完结 已驳回 待回访
            const orderStatusList = ["8", "1", "2", "3", "4", "5", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            // 运维人员 可以审批的工单状态  已处理 已完结 已驳回 待回访
            const omOrderStatusList = ["3", "4", "5", "7"];
            const omOrderStatus = omOrderStatusList.includes(row.orderStatus);

            return (
              (orderStatus && createBy == this.userId) ||
              ((isSupOM || isEngOM) && omOrderStatus)
            );
          },
        },
        {
          title: "转派",
          clickFn: (row) => {
            this.transfer(row);
          },
          permission: "maintenance:workOrder:handleOver",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //创建人
            const createBy = row.createBy;

            // 待接单 待处理  待回访
            const orderStatusList = ["8", "1", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            // 运维人员 可以审批的工单状态  待回访
            const omOrderStatusList = ["7"];
            const omOrderStatus = omOrderStatusList.includes(row.orderStatus);

            // 处理人 可以审批的工单状态  待处理
            const handleOrderStatusList = ["1"];
            const handleOrderStatus = handleOrderStatusList.includes(
              row.orderStatus
            );

            return (
              (orderStatus && createBy == this.userId) ||
              (handleOrderStatus && row.handleUser == this.userId) ||
              ((isSupOM || isEngOM) && omOrderStatus)
            );
          },
        },
        {
          title: "处理",
          clickFn: (row) => {
            this.showHandleDialog(row);
          },
          permission: "maintenance:workOrder:handle",
          show: (row) => {
            // "调试工单","保养工单","巡检工单" 只能在 app 处理
            const orderTypeParentName = ["调试工单", "保养工单", "巡检工单"];

            // 待处理  处理中  已驳回  待回访
            const orderStatusList = ["1", "2", "4", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);
            return (
              orderStatus &&
              !orderTypeParentName.includes(row.orderTypeParentName) &&
              row.handleUser == this.userId
            );
          },
        },
        {
          title: "详情",
          clickFn: (row) => {
            this.showDetail(row);
          },
          permission: "maintenance:workOrder:detail",
          show: (row) => {
            return true;
          },
        },
        {
          title: "催单",
          clickFn: (row) => {
            this.handleRemind(row);
          },
          permission: "maintenance:workOrder:remind",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //待处理 待回访
            const orderStatusList = ["1", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            // 运维主管可以审批的工单状态
            const omOrderStatusList = ["7"];
            const omOrderStatus = omOrderStatusList.includes(row.orderStatus);

            return (
              (orderStatus && row.createBy == this.userId) ||
              ((isSupOM || isEngOM) && omOrderStatus)
            );
          },
        },
        {
          title: "工单报告",
          clickFn: (row) => {
            this.orderReport(row);
          },
          permission: "maintenance:workOrder:report",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //处理组内 userid list
            const handleGroupUserIds = row.handleGroupUserIds;

            //已处理 已完结 待回访 已驳回 已评价
            const orderStatusList = ["3", "4", "5", "6", "7", "9"];
            const orderStatus = orderStatusList.includes(row.orderStatus);
            return (
              orderStatus &&
              (row.createBy == this.userId ||
                row.handleUser == this.userId ||
                handleGroupUserIds?.includes(this.userId) ||
                isSupOM ||
                isEngOM)
            );
          },
        },
        // {
        //   title: "处理半径",
        //   clickFn: (row) => {
        //     this.handleRadius(row);
        //   },
        //   permission: "maintenance:workOrder:radius",
        //   show: (row) => {
        //     // 运维主管
        //     const isSupOM = this.roles.includes("supOM");

        //     //已驳回 待处理、待回访
        //     const orderStatusList = ["4", "1", "7"];
        //     const orderStatus = orderStatusList.includes(row.orderStatus);
        //     return orderStatus && isSupOM;
        //   },
        // },
        {
          title: "审核",
          clickFn: (row) => {
            this.check(row);
          },
          permission: "maintenance:workOrder:check",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");

            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //已驳回 已处理
            const orderStatusList = ["3", "4"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            //  运维主管可以审核 所有工单  运维工程师 不能审核自己创建的工单
            return (
              (orderStatus && isSupOM) ||
              (orderStatus && isEngOM && this.userId != row.createBy)
            );
          },
        },
        {
          title: "评价",
          clickFn: (row) => {
            this.$refs.evaluateDialog.open(row);
          },
          permission: "operation:workOrder:evaluate",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");

            // 运维工程师
            const isEngOM = this.roles.includes("engOM");
            //已完结 待回访
            const orderStatusList = ["5", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            //  运维主管可以审核 所有工单  运维工程师 不能审核自己创建的工单
            return (
              (orderStatus && isSupOM) ||
              (orderStatus && isEngOM && this.userId != row.createBy)
            );
          },
        },
      ],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "orderNo",
          title: "工单编号",
          treeNode: true,
        },
        {
          field: "channel",
          title: "工单来源",
          showOverflowTooltip: true,
          formatter: this.channelFormat,
          titlePrefix: {
            message: `来源说明：
              1、手动：在能源维保通的工单管理中，手动创建的工单；
              2、客服工单：客服工单系统推送过来的工单；
              3、充电平台：告警运维中心推送过来的告警信息，在能源维保通的异常信息中手动转工单或自动生成的运维工单。
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "businessType",
          title: "业务类型",
          showOverflowTooltip: true,
          formatter: this.businessTypeFormat,
        },
        {
          field: "orderTypeName",
          title: "工单类型",
          formatter: this.orderTypeFormat,
        },
        {
          field: "deviceNo",
          title: "设备编号",
          customWidth: 200,
        },
        {
          field: "deviceName",
          title: "设备名称",
          customWidth: 200,
        },

        {
          field: "faultTypeName",
          title: "故障类别",
          slots: { default: "tags" },
          // formatter: this.orderTypeFormat,
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "orderDesc1",
          title: "问题描述",
          // showOverflowTooltip: true,
          // showOverflow: true,
          slots: { default: "orderDesc" },
          width: "100px",
        },
        {
          field: "stationName",
          title: "站点名称",
          showOverflowTooltip: true,
        },
        {
          field: "stationGrade",
          title: "站点运维等级",
        },
        {
          field: "deptName",
          title: "能投大区",
        },
        {
          field: "operationMode",
          title: "运营模式",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationNameArr",
          title: "站点标签",
          slots: { default: "stationTag" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "address",
          title: "地址",
          showOverflowTooltip: true,
          customWidth: 200,
          formatter: this.addressFormat,
        },
        {
          field: "createByName",
          title: "创建人",
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "orderStatus",
          title: "工单状态",
          formatter: this.orderStatusFormat,
        },
        {
          field: "handleGroupName",
          title: "处理组",
        },
        {
          field: "handleUserName",
          title: "处理人",
        },
        {
          field: "handleTime",
          title: "处理时间",
        },
        {
          field: "isTreatment",
          title: "处理是否超时",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.isTreatment == "1" ? "color:red" : ""}>
                  {row.isTreatment == "1" ? "是" : "否"}
                </span>
              );
            },
          },
        },
        {
          field: "treatmentTimeOut",
          title: "处理超时时长",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.isTreatment == "1" ? "color:red" : ""}>
                  {row.treatmentTimeOut}
                </span>
              );
            },
          },
        },
        {
          field: "auditTime",
          title: "审核时间",
        },
        {
          field: "auditUserNickName",
          title: "审核人",
        },
        {
          field: "isAudit",
          title: "审核是否超时",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.isAudit == "1" ? "color:red" : ""}>
                  {row.isAudit == "1" ? "是" : "否"}
                </span>
              );
            },
          },
        },
        {
          field: "auditTimeOut",
          title: "审核超时时长",
          slots: {
            default: ({ row }) => {
              return (
                <span style={row.isAudit == "1" ? "color:red" : ""}>
                  {row.auditTimeOut}
                </span>
              );
            },
          },
        },
        // {
        //   field: "handleTag",
        //   title: "处理标签",
        //   formatter: this.handleTagFormat,
        // },
        // {
        //   field: "closeUserNickName",
        //   title: "关单人",
        // },
        // {
        //   field: "finishTime",
        //   title: "完结时间",
        // },
        {
          field: "remark",
          title: "备注",
          customWidth: 100,
        },
        {
          field: "scoreTotal",
          title: "评分",
          formatter({ cellValue }) {
            return cellValue || "-";
          },
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderTypeNames: [],
        orderTypeParentNames: [],
        threeOrderTypeNames: [],
        oneFaultType: "",
        threeFaultType: "",
        twoFaultType: "",
        transportQueryFlag:"N"
      },
      rules1: {
        handleUser: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
      },
      rules2: {
        groupIds: [
          { required: true, message: "请选择处理组", trigger: "change" },
        ],
      },
      total: 0,
      loading: false,
      tableId: "stationList",
      detailVisible: false,
      stopVisible: false,
      rowInfo: {},
      stationTypeDict: [],
      stationTagDict: [],
      addProjectDialogVisible: false,
      pageType: "detail",
      orderNo: undefined,
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handleOverVisible: false,
      businessTypeOptions: [],
      handleTageOptions: [],
      orderStatusOptions: [],
      areaData: [],
      deptOptions: [],
      userOption: [],
      transferform: {
        deptId: undefined,
        orderNo: undefined,
        handleUser: undefined,
        messageTypes: ["01", "02", "03"],
      },
      stopForm: {},
      tagForm: {},
      channelOptions: [
        { dictLabel: "手动", dictValue: "01" },
        { dictLabel: "客服", dictValue: "02" },
        { dictLabel: "充电平台", dictValue: "03" },
      ],
      orderTypeParentOptions: [
        { dictLabel: "故障工单", dictValue: "故障工单" },
        { dictLabel: "抄表工单", dictValue: "抄表工单" },
        { dictLabel: "真车充电测试派单", dictValue: "真车充电测试派单" },
      ],
      orderTypeOptions: [
        // { dictLabel: "急停故障", dictValue: "急停故障" },
        // { dictLabel: "离线断电", dictValue: "离线断电" },
        // { dictLabel: "离线断网", dictValue: "离线断网" },
        // {
        //   dictLabel: "电桩上报故障（不含急停）",
        //   dictValue: "电桩上报故障（不含急停）",
        // },
        // {
        //   dictLabel: "离线&故障（不含急停）同时发生",
        //   dictValue: "离线&故障（不含急停）同时发生",
        // },
        // { dictLabel: "潜藏故障", dictValue: "潜藏故障" },
        // { dictLabel: "抄表工单", dictValue: "抄表工单" },
        // { dictLabel: "真实充电测试派单", dictValue: "真实充电测试派单" },
      ],
      visible: false,
      form: {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      },
      flag: true,
      checkLoading: false,
      handleTypeOptions: [],
      handleMethodOptions: [],
      faultTypeOptions: [],
      handleForm: {
        handleType: "",
        handleMethod: "",
        faultType: "",
      },
      handleFormRules: {
        handleType: {
          required: true,
          message: "处理类型不能为空",
          trigger: "blur",
        },
        handleMethod: {
          required: true,
          message: "处理方式不能为空",
          trigger: "blur",
        },
        faultType: {
          required: true,
          message: "故障原因不能为空",
          trigger: "blur",
        },
        handleDesc: { max: 500, message: "500字符以内", trigger: "blur" },
      },
      submitLoading: false,
      saveLoading: false,
      handleDialogVisible: false,
      curOrderCreateTime: "",
      curOrderHandleTime: "",
      threeOrderTypeOptions: [],
      flattenData: [],
      remarkForm: {
        remark: "",
      },
      remarkFormRules: {
        remark: [
          { required: true, message: "备注不能为空", trigger: "blur" },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
      },
      remarkNo: "",
      handRow: {
        orderNos: [],
      },
      deptOptionList: [],
      isInit: true,
      channel: "01",
      levelOptions: [],
      groupAllOptions: [],
      groupHandleMemberOptions: [],
      operationModeOptions: [],
      platformMerchantOptions: [],
    };
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    rules() {
      return {
        orderStatus: [
          { required: true, trigger: "blur", message: "请选择工单状态" },
        ],
        reason: [
          {
            required: this.form.orderStatus == "4",
            message: "工单状态为驳回时，备注不能为空！",
            trigger: "blur",
          },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
        oneFaultTypes: [
          { required: true, message: "请选择故障类型", trigger: "blur" },
        ],
      };
    },
    config() {
      return [
        {
          key: "orderNo",
          title: "工单编号",
          type: "input",
          placeholder: "请填写工单编号",
        },
        {
          key: "createByName",
          title: "创建人",
          type: "input",
          placeholder: "请填写创建人",
        },
        {
          key: "orderStatus",
          title: "工单状态",
          type: "select",
          placeholder: "请选择工单状态",
          options: this.orderStatusOptions,
        },
        {
          key: "orderTypeName",
          parentKey: "orderTypeParentNames",
          title: "工单类型",
          type: "slot",
          placeholder: "请选择工单类型",
          span: 24,
          colNum: 16,
          // options: this.orderTypeOptions,
          // parentOptions: this.orderTypeParentOptions,
        },
        {
          key: "handleUserName",
          title: "处理人",
          type: "input",
          placeholder: "请输入处理人",
        },
        {
          key: "faultType",
          title: "故障类别",
          type: "slot",
          placeholder: "请选择故障类别",
          span: 24,
          colNum: 16,
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "channel",
          title: "工单来源",
          type: "select",
          placeholder: "请选择工单来源",
          options: this.channelOptions,
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请填写设备名称",
        },
        {
          key: "deviceNo",
          title: "设备编码",
          type: "input",
          placeholder: "请填写设备编码",
        },
        {
          key: "createTime",
          title: "创建时间",
          type: "dateRange",
          placeholder: "请选择创建时间",
          startPlaceholder: "创建开始时间",
          endPlaceholder: "创建结束时间",
        },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
        {
          key: "handleIsTimeOut",
          title: "处理是否超时",
          type: "select",
          placeholder: "请选择处理是否超时",
          options: [
            { dictValue: 1, dictLabel: "是" },
            { dictValue: 0, dictLabel: "否" },
          ],
        },
        {
          key: "handleTime",
          title: "处理时间",
          type: "dateRange",
          placeholder: "请选择处理时间",
        },
        {
          key: "approveIsTimeOut",
          title: "审核是否超时",
          type: "select",
          placeholder: "请选择审核是否超时",
          options: [
            { dictValue: 1, dictLabel: "是" },
            { dictValue: 0, dictLabel: "否" },
          ],
        },
        {
          key: "stationGrade",
          title: "站点运维等级",
          type: "select",
          placeholder: "请选择站点运维等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeName",
        },
        {
          key: "businessType",
          title: "业务类型",
          type: "select",
          placeholder: "请选择业务类型",
          options: this.businessTypeOptions,
        },
        {
          key: "platformMerchantId",
          title: "站点所属租户",
          type: "select",
          placeholder: "请选择站点所属租户",
          options: this.platformMerchantOptions,
          optionLabel: "platformMerchantName",
          optionValue: "platformMerchantId",
        },

        // {
        //   key: "handleGroup",
        //   title: "处理组",
        //   type: "select",
        //   placeholder: "请选择处理组",
        //   options: this.groupAllOptions,
        //   optionLabel: "groupName",
        //   optionValue: "groupId",
        // },

        // {
        //   key: "handleTag",
        //   title: "处理标签",
        //   type: "select",
        //   placeholder: "请选择处理标签",
        //   options: this.handleTageOptions,
        // },
        // {
        //   key: "closeUserNickName",
        //   title: "关单人",
        //   type: "input",
        //   placeholder: "请输入关单人",
        // },
        // {
        //   key: "finishTime",
        //   title: "完结时间",
        //   type: "dateRange",
        //   placeholder: "请选择完结时间",
        // },
        // {
        //   key: "handleType",
        //   title: "处理类型",
        //   type: "select",
        //   placeholder: "请选择处理类型",
        //   options: this.handleTypeOptions,
        // },
      ];
    },
  },

  async created() {
    //站点类型
    await this.getDicts("cm_station_type").then((response) => {
      this.stationTypeDict = response?.data;
    });
    await this.getDicts("cm_station_tag").then((response) => {
      this.stationTagDict = response?.data;
    });
    this.getPlatformMerchantOptions();
    this.getTreeselect();
    this.getOrderTypeOptions();
    this.getTreeData();
    this.getDeptList();
  },
  mounted() {
    Promise.all([
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
      this.getBusinessTypeOptions(),
      this.getOrderStatusOptions(),
      this.getAreaData(regionData),
      this.getHandleTypeOptions(),
      this.getHandleTagOptions(),
      this.getHandleMethodOptions(),
      this.getFaultTypeOptions(),
      this.getList(),
      this.getListUser(),
      this.getDeptList(),
      this.getGroupList(),
      firstFaultList().then((res) => {
        this.firstFaultOptions = res?.data;
      }),
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.initConfig();
        });
      }, 500);
    });
    this.token = getToken();
  },
  async activated() {
    //站点类型
    await this.getDicts("cm_station_type").then((response) => {
      this.stationTypeDict = response?.data;
    });
    await this.getDicts("cm_station_tag").then((response) => {
      this.stationTagDict = response?.data;
    });
    this.getTreeselect();
    this.getOrderTypeOptions();

    this.getDeptList();
    Promise.all([
      this.getTreeData(),
      this.getBusinessTypeOptions(),
      this.getOrderStatusOptions(),
      this.getAreaData(regionData),
      this.getHandleTypeOptions(),
      this.getHandleTagOptions(),
      this.getHandleMethodOptions(),
      this.getFaultTypeOptions(),
      this.getListUser(),
      this.getGroupList(),
    ]).then(() => {
      if (Object.keys(this.$route.params).length > 0) {
        this.searchForm = {
          pageNum: 1,
          pageSize: 10,
          orderTypeNames: [],
          orderTypeParentNames: [],
          threeOrderTypeNames: [],
          oneFaultType: "",
          threeFaultType: "",
          twoFaultType: "",
          ...this.$route.params,
        };
        console.log(this.searchForm, "======router");
        if (this.searchForm.orderTypeParentNames?.length > 0) {
          this.searchForm.orderTypeParentNames = this.flattenData
            ?.filter((x) =>
              this.searchForm.orderTypeParentNames.includes(x.typeName)
            )
            ?.map((i) => i.id);
          this.orderTypeParentChange(this.searchForm.orderTypeParentNames, 2);
        }
      }
      this.getList();
    });
    this.token = getToken();
  },
  methods: {
    checkPermission,
    getPlatformMerchantOptions() {
      queryPlatformMerchant({}).then((res) => {
        this.platformMerchantOptions = res.data;
      });
    },
    getTransferUserList() {
      queryTransferUser({ orderNo: this.orderNo }).then((res) => {
        this.transferUserOptions = res.data;
      });
    },
    handleBusinessTypeChange(val) {
      this.createForm.orderType = "";
      queryBindOrderTypeList({ businessType: val }).then((res) => {
        if (res?.code === "10000") {
          this.bindOrderTypeOptions = res.data;
        }
      });
    },
    // handleCreate() {
    //   this.createVisible = true;
    // },
    async orderReport(row) {
      const params = {
        orderNo: row.orderNo,
      };
      const res = await getReport(params);

      if (res?.code == "10000") {
        const pdfUrl = res.data;
        window.open(pdfUrl);
      }
    },
    closeCreateDialog() {
      this.createVisible = false;
      this.bindOrderTypeOptions = [];
      this.$refs.createForm.resetFields();
    },
    handleNextCreate() {
      this.$refs.createForm.validate(async (valid) => {
        if (!valid) return false;
        const { orderType, businessType } = this.createForm;
        const params = {
          orderTypeId: orderType,
        };
        const res = await getNewOrgNo(params);
        if (res?.code == "10000") {
          //新增页面开启缓存 如果重新打开其他编辑 则将之前的新增页面先关闭
          const addPage = this.$store.state.tagsView.visitedViews?.find(
            (x) =>
              x.path === "/operationMaintenanceManage/operationWorkOrder/add"
          );
          if (addPage) {
            this.$store.dispatch("tagsView/delView", addPage);
          }
          this.$nextTick(() => {
            this.$router.push({
              path: "/operationMaintenanceManage/operationWorkOrder/add",
              query: {
                orderNo: res.data,
                type: "add",
                orderTypeParentId: orderType,
                orderTypeParentName: this.bindOrderTypeOptions?.find(
                  (x) => x.id === orderType
                )?.typeName,
                businessType: businessType,
                businessTypeName: this.businessTypeOptions?.find(
                  (x) => x.dictValue === businessType
                )?.dictLabel,
              },
            });
            this.closeCreateDialog();
          });
        }
      });
    },
    saveRemind() {
      this.$refs.remindForm.validate(async (valid) => {
        if (!valid) return false;
        const params = {
          orderNo: this.orderNo,
          ...this.remindForm,
        };
        const res = await pushMsg(params);
        if (res?.code == "10000") {
          this.$message.success("提交成功");
          this.closeRemindDialog();
          this.getList();
        }
      });
    },
    handleRemind(row) {
      this.remindVisible = true;
      this.orderNo = row.orderNo;
    },
    closeRemindDialog() {
      this.remindVisible = false;
      this.$refs.remindForm.resetFields();
    },
    handleReceive(row) {
      this.$confirm("确定要接单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          orderNo: row.orderNo,
        };
        receiveOrder(data).then((res) => {
          if (res?.success) {
            this.$message.success("接单成功");
            //更新列表
            this.getList();
          } else {
            this.$message.error("接单失败");
          }
        });
      });
    },
    saveCancel() {
      this.$refs.cancelForm.validate(async (valid) => {
        if (!valid) return false;
        const params = {
          orderNo: this.orderNo,
          ...this.cancelForm,
        };
        const res = await submitCancel(params);
        if (res?.code == "10000") {
          this.$message.success("提交成功");
          this.closeCancelDialog();
          this.getList();
        }
      });
    },
    handleCancel(row) {
      this.cancelVisible = true;
      this.orderNo = row.orderNo;
    },
    closeCancelDialog() {
      this.cancelVisible = false;
      this.$refs.cancelForm.resetFields();
    },
    saveRadius() {
      this.$refs.radiusForm.validate(async (valid) => {
        if (!valid) return false;
        const params = {
          orderNo: this.orderNo,
          ...this.radiusForm,
        };
        const res = await submitRadius(params);
        if (res?.code == "10000") {
          this.$message.success("提交成功");
          this.closeRadiusDialog();
          this.getList();
        }
      });
    },
    handleRadius(row) {
      this.radiusVisible = true;
      this.orderNo = row.orderNo;
      this.radiusForm.handleRadius = row.handleRadius || 2000;
    },
    customValidation(rule, value, callback) {
      console.log(/^[1-9]\d*$/.test(value), value);
      if (!value) {
        callback(new Error("处理半径不能为空！"));
      } else if (
        value &&
        (value < 0 || value > 5000 || !/^[1-9]\d*$/.test(value))
      ) {
        callback(new Error("0≤处理半径≤5000，且为整数"));
      } else {
        callback();
      }
    },
    handleTabClick() {

      if(this.activeName == '3'){
        this.searchForm.transportQueryFlag = 'Y';
        this.searchForm.permissionEnableFlag = '1';
        this.getDicts("order_status").then((response) => {
          this.orderStatusOptions = response?.data;
          this.handleQuery();
        });
      }else{
        this.searchForm.transportQueryFlag = 'N';
        this.searchForm.permissionEnableFlag = '0';
        const arr = [
          { dictName: "order_status", tab: "0", title: "全部" },
          { dictName: "order_todo_status", tab: "1", title: "待处理" },
          { dictName: "order_done_status", tab: "2", title: "已处理" },
        ];
        const str = arr.find((x) => x.tab === this.activeName)?.dictName;
        this.getDicts(str).then((response) => {
          this.orderStatusOptions = response?.data;
          this.handleQuery();
        });
      }


    },
    getFaultTypeArr(row, num) {
      const { oneFaultType = "", twoFaultType = "", threeFaultType = "" } = row;
      let result = [];
      let arrA = oneFaultType.split(",").filter((item) => item !== "");
      let arrB = twoFaultType.split(",").filter((item) => item !== "");
      let arrC = threeFaultType.split(",").filter((item) => item !== "");

      result = result.concat(arrA, arrB, arrC);
      if (num) {
        return result.slice(0, num);
      }
      return result;
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        orderNos: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.orderNos = tableData.map((v) => v.orderNo);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleSecondFaultChange(val) {
      this.form.threeFaultTypes = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val }).then((res) => {
          this.thirdFaultOptions = res?.data;
        });
      }
    },
    handleSecondFaultStrChange(val) {
      this.searchForm.threeFaultType = "";
      if (val) {
        childFaultList({ ids: [val] }).then((res) => {
          this.thirdFaultOptions = res?.data;
        });
      } else {
        this.thirdFaultOptions = [];
      }
    },
    handleFirstFaultChange(val) {
      this.form.twoFaultTypes = [];
      this.form.threeFaultTypes = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val }).then((res) => {
          this.secondFaultOptions = res?.data;
        });
      }
    },
    handleFirstFaultStrChange(val) {
      this.searchForm.twoFaultType = "";
      this.searchForm.threeFaultType = "";
      if (val) {
        childFaultList({ ids: [val] }).then((res) => {
          this.secondFaultOptions = res?.data;
        });
      } else {
        this.secondFaultOptions = [];
        this.thirdFaultOptions = [];
      }
    },
    handleGroupMemberChange(val) {
      console.log("通知人---", val);
    },
    //处理组变化
    async handleGroupIdsChange(val) {
      if (!val || val?.length === 0) {
        this.groupHandleMemberOptions = [];
      } else {
        const res = await queryGroupMemberList({ groupIds: val });
        this.groupHandleMemberOptions = res?.data;
      }
      const arr = this.groupHandleMemberOptions?.map((x) => x.userId);
      this.transferform2.userIds = this.transferform2.userIds?.filter(
        (item) => arr.indexOf(item) > -1
      );
    },
    //通知组变化
    handleGroupChange(val) {
      console.log("通知组", val);
      queryGroupMemberList({ groupIds: val }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
    },
    async saveRemark() {
      this.$refs.remarkForm.validate(async (valid) => {
        if (!valid) return false;
        this.remarkLoading = true;
        const params = {
          orderNo: this.remarkNo,
          remark: this.remarkForm.remark,
        };
        const res = await submitRemark(params);
        this.remarkLoading = false;
        if (res?.code == "10000") {
          this.$message.success("备注提交成功");
          this.remarkVisible = false;
          this.getList();

          this.reportTrackEvent(MAINTENANCE_ORDER_CLICK_REMARK);
        }
      });
    },
    handleRemark(row) {
      this.remarkVisible = true;
      this.remarkNo = row.orderNo;
    },
    openDescDetail(row) {
      orderDetail({ orderId: row.orderId }).then((res) => {
        if (res?.code === "10000") {
          this.drawerVisible = true;
          this.orderDesc = res.data?.orderDescText;
        }
      });
    },
    getOrderTypeOptions() {
      fistLevelList().then((res) => {
        this.orderTypeParentOptions = res?.data?.map((x) => {
          return { label: x.typeName, value: x.id };
        });
        this.orderTypeParentOptions.push(
          ...[
            { label: "全部", value: "all" },
            { label: "其他（已删除类型）", value: "other" },
          ]
        );
      });
    },
    //工单类型按钮 跳转至工单类型页面
    // handleOrderType() {
    //   this.$router.push({
    //     path: "/workOrderType",
    //   });
    // },
    // handleFaultType() {
    //   this.$router.push({
    //     path: "/faultType",
    //   });
    // },
    orderTypeParentChange(val, level) {
      if (val === "all") {
        this.searchForm.tag = 1;
        this.searchForm.orderTypeNames = [];
        this.searchForm.threeOrderTypeNames = [];
        return;
      }
      if (val === "other") {
        this.searchForm.tag = 2;
        this.searchForm.orderTypeNames = [];
        this.searchForm.threeOrderTypeNames = [];
        return;
      }
      this.searchForm.tag = 0;
      if (level == 2) {
        this.searchForm.orderTypeNames = [];
        this.searchForm.threeOrderTypeNames = [];
        this.threeOrderTypeOptions = [];
        if (val && val.length > 0) {
          childrenMultiList({ ids: val }).then((res) => {
            this.orderTypeOptions = res?.data?.map((x) => {
              return { label: x.typeName, value: x.id };
            });
          });
        } else {
          this.orderTypeOptions = [];
        }
      } else {
        this.searchForm.threeOrderTypeNames = [];
        if (val && val.length > 0) {
          childrenMultiList({ ids: val }).then((res) => {
            this.threeOrderTypeOptions = res?.data?.map((x) => {
              return { label: x.typeName, value: x.id };
            });
          });
        } else {
          this.threeOrderTypeOptions = [];
        }
      }
    },
    //获取用户列表
    async getListUser(param) {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      if (param) {
        params.orgNo = Number(param);
      }
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
      this.userOptionCopy = data;
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    closeDialog() {
      // let formName = this.channel === "03" ? "transferform2" : "transferform";
      this.$refs.transferForm.resetFields();
      this.handleOverVisible = false;
    },
    closeStopDialog() {
      this.$refs.stopForm.resetFields();
      this.stopVisible = false;
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    beforeClearAll() {
      this.transferform.handleUser = undefined;
      this.transferform.deptId = undefined;
      this.getListUser();
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.transferform.handleUser = undefined;
      this.getListUser(data.deptId);
    },
    handleStopSubmit() {
      this.$refs.stopForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.stopForm,
            orderNo: this.orderNo,
          };

          const loading = this.$loading({
            lock: true,
            text: "结束中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          stopOrder(params)
            .then((res) => {
              if (res?.success) {
                this.closeStopDialog();
                this.handleQuery(this.searchForm);
                this.$message.success("结束成功");

                this.reportTrackEvent(MAINTENANCE_ORDER_STOP_SUCCESS, {
                  duration: timeDiffFormat(
                    new Date(this.curOrderCreateTime),
                    new Date()
                  ),
                });
              } else {
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    handleTagSubmit() {
      this.$refs.tagForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.tagForm,
            orderNo: this.orderNo,
          };

          const loading = this.$loading({
            lock: true,
            text: "修改中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          updateOrderHandleTag(params)
            .then((res) => {
              if (res?.success) {
                this.handleQuery(this.searchForm);
                loading.close();
                this.$message.success("成功");
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              this.close();
              loading.close();
            });
        }
      });
    },
    handleSubmit() {
      // let formName = this.channel === "03" ? "transferform2" : "transferform";
      this.$refs.transferForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.transferForm,
            orderNo: this.orderNo,
          };

          const loading = this.$loading({
            lock: true,
            text: "指派中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          transferOrder(params)
            .then((res) => {
              if (res?.success) {
                this.closeDialog();
                this.handleQuery(this.searchForm);
                loading.close();
                this.$message.success("指派成功");

                this.reportTrackEvent(MAINTENANCE_ORDER_TRANSFER_SUCCESS, {
                  noNotifyUsers:
                    !params?.handleUser || params?.handleUser?.length == 0,
                  noNotifyWays:
                    !params?.messageTypes || params?.messageTypes?.length == 0,
                  source: "web", //和app共用一个事件
                });
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    closeRemarkDialog() {
      this.remarkVisible = false;
      this.$refs.remarkForm.resetFields();
    },
    closeRadiusDialog() {
      this.radiusVisible = false;
      this.$refs.radiusForm.resetFields();
    },

    showDetail(row) {
      this.rowInfo = row;
      this.$router.push({
        path: "/operationMaintenanceManage/detail",
        query: {
          orderId: row.orderId,
          orderNo: row.orderNo,
          customizeFlag: row.customizeFlag,
        },
        // query: { orderNo: 'YW20240117095853601', orderStatus: row.orderStatus,  orderId: row.orderId}
      });
    },
    transfer(row) {
      this.channel = row.channel;
      this.handleOverVisible = true;
      this.groupHandleMemberOptions = [];
      // let formName = this.channel === "03" ? "transferform2" : "transferform";
      this.orderNo = row.orderNo;
      this.getTransferUserList();
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        orderTypeNames: [],
        orderTypeParentNames: [],
        threeOrderTypeNames: [],
      };
      this.orderTypeOptions = [];
      this.threeOrderTypeOptions = [];
      this.getList();
    },
    changePage() {
      this.getList();
    },
    stopOrder(row) {
      this.orderNo = row.orderNo;
      this.stopVisible = true;
      this.curOrderCreateTime = row.createTime;
    },
    orderPushMsg(row) {
      this.$confirm("确定APP和短信发送催单通知吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          orderNo: row.orderNo,
        };
        pushMsg(data).then((res) => {
          if (res?.success) {
            this.$message.success("催单成功");
            //更新列表
            this.getList();

            this.reportTrackEvent(MAINTENANCE_ORDER_CLICK_PUSH);
          } else {
            this.$message.error("催单失败");
          }
        });
      });
    },
    getTreeData() {
      queryTreeList({}).then((res) => {
        this.flattenData = this.flattenArray(res.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr.forEach((item) => {
          result.push(item);
          if (item.childrenList) {
            flatten(item.childrenList);
          }
          delete item.childrenList; // 删除childrenList属性
        });
      };
      flatten(arr);
      return result;
    },
    getList() {
      this.loading = true;
      const {
        orderTypeNames,
        orderTypeParentNames,
        // threeOrderTypeNames,
        oneFaultType,
        twoFaultType,
        threeFaultType,
        handleType,
        ...rest
      } = this.searchForm;
      let params = {
        ...rest,
      };
      params["orderTypeNames"] = this.flattenData
        ?.filter((x) => orderTypeNames.includes(x.id))
        ?.map((i) => i.typeName);
      params["orderTypeParentNames"] = this.flattenData
        ?.filter((x) => orderTypeParentNames.includes(x.id))
        ?.map((i) => i.typeName);
      // params["threeOrderTypeNames"] = this.flattenData
      //   ?.filter((x) => threeOrderTypeNames.includes(x.id))
      //   ?.map((i) => i.typeName);
      params["oneFaultType"] = this.firstFaultOptions?.find(
        (x) => x.id == oneFaultType
      )?.typeName;
      params["twoFaultType"] = this.secondFaultOptions?.find(
        (x) => x.id == twoFaultType
      )?.typeName;
      params["threeFaultType"] = this.thirdFaultOptions?.find(
        (x) => x.id == threeFaultType
      )?.typeName;
      params["handleTypeName"] = this.handleTypeOptions?.find(
        (x) => x.dictValue == handleType
      )?.dictLabel;

      if (Array.isArray(params.createTime)) {
        params.startCreateTime = params.createTime[0] + " 00:00:00";
        params.endCreateTime = params.createTime[1] + " 23:59:59";
        delete params.createTime;
      }
      if (Array.isArray(params.handleTime)) {
        params.startHandleTime = params.handleTime[0] + " 00:00:00";
        params.endHandleTime = params.handleTime[1] + " 23:59:59";
        delete params.handleTime;
      }
      if (Array.isArray(params.finishTime)) {
        params.startFinishTime = params.finishTime[0] + " 00:00:00";
        params.endFinishTime = params.finishTime[1] + " 23:59:59";
        delete params.finishTime;
      }

      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }

      queryList({
        ...params,
        orderStatusList: this.orderStatusOptions?.map((x) => x.dictValue),
        handleUserFlag: this.activeName != "0",
      })
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    // initConfig() {
    //   this.
    // },
    //工单类型转换
    orderTypeFormat({ cellValue, row }) {
      const arr = [
        row.orderTypeParentName,
        row.orderTypeName,
        row.threeOrderTypeName,
      ];
      return arr.filter((x) => x).join(" | ");
    },
    channelFormat({ cellValue, row }) {
      return this.selectDictLabel(this.channelOptions, cellValue);
    },
    //业务类型转换
    businessTypeFormat({ cellValue, row }) {
      return this.selectDictLabel(this.businessTypeOptions, cellValue) == ""
        ? row.businessTypeName
        : this.selectDictLabel(this.businessTypeOptions, cellValue);
    },
    handleTagFormat({ cellValue }) {
      return this.selectDictLabel(this.handleTageOptions, cellValue);
    },
    orderStatusFormat({ cellValue }) {
      return this.selectDictLabel(this.orderStatusOptions, cellValue);
    },
    addressFormat({ cellValue, row }) {
      const provinceAddress = this.areaData.find(
        (item) => item.value === row.province
      )?.label;
      const cityAddress = this.areaData.find((item) => item.value === row.city)
        ?.label;
      const countyAddress = this.areaData.find(
        (item) => item.value === row.county
      )?.label;
      return provinceAddress
        ? provinceAddress +
            "-" +
            cityAddress +
            "-" +
            countyAddress +
            "-" +
            (cellValue ? cellValue : "")
        : cellValue
        ? cellValue
        : "";
    },
    updateTag(row) {
      this.orderNo = row.orderNo;
      this.tagManageVisible = true;
    },
    close() {
      this.tagManageVisible = false;
    },

    getBusinessTypeOptions() {
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    getHandleTagOptions() {
      this.getDicts("handle_tag").then((response) => {
        this.handleTageOptions = response?.data;
      });
    },
    getOrderStatusOptions() {
      this.getDicts("order_status").then((response) => {
        this.orderStatusOptions = response?.data;
      });
    },
    getHandleMethodOptions() {
      this.getDicts("handle_method").then((response) => {
        this.handleMethodOptions = response?.data;
      });
    },
    getFaultTypeOptions() {
      this.getDicts("fault_type").then((response) => {
        this.faultTypeOptions = response?.data;
      });
    },
    getHandleTypeOptions() {
      this.getDicts("handle_type").then((response) => {
        this.handleTypeOptions = response?.data;
      });
    },
    getAreaData(list) {
      list.forEach((item) => {
        this.areaData.push({ label: item.label, value: item.value });
        if (item?.children) {
          this.getAreaData(item.children);
        }
      });
    },
    showOperateButton(row, type) {
      //运维角色
      const isSystemOM = this.roles.includes("systemOM");
      //工单处理人角色
      const isHandler = row.handleUser
        ? row.handleUser === this.userId
        : row.handleGroupUser?.split(",").includes(this.userId);
      //工单责任人角色
      const isCharge = row.chargeUser === this.userId;
      //站点服务人角色
      const isService = row.serviceUser?.split(",").includes(this.userId);
      const btnArr = [
        {
          type: "remark",
          title: "备注",
          condition: (row) => {
            return (
              isSystemOM && ["1", "2", "3", "4", "7"].includes(row.orderStatus)
            );
          },
        },
        {
          type: "edit",
          title: "编辑",
          condition: (row) => {
            return isSystemOM && ["8", "7"].includes(row.orderStatus);
          },
        },
        {
          type: "handleOver",
          title: "转派",
          condition: (row) => {
            return (
              (isSystemOM && ["1", "2", "7"].includes(row.orderStatus)) ||
              (isHandler && ["1", "7"].includes(row.orderStatus))
            );
          },
        },
        {
          type: "handle",
          title: "处理",
          condition: (row) => {
            return (
              (isSystemOM && ["1", "2", "7"].includes(row.orderStatus)) ||
              (isHandler && ["1", "2", "4", "7"].includes(row.orderStatus))
            );
          },
        },
        {
          type: "finish",
          title: "结束",
          condition: (row) => {
            return isSystemOM && ["1", "7"].includes(row.orderStatus);
          },
        },
        {
          type: "remind",
          title: "催单",
          condition: (row) => {
            return isSystemOM && ["1", "7"].includes(row.orderStatus);
          },
        },
        {
          type: "detail",
          title: "详情",
          condition: (row) => {
            return isSystemOM || isHandler || isService || isCharge;
          },
        },
        {
          type: "tag",
          title: "标签",
          condition: (row) => {
            return isSystemOM && ["2", "3", "4"].includes(row.orderStatus);
          },
        },
        {
          type: "check",
          title: "审核",
          condition: (row) => {
            return isSystemOM && ["3", "4"].includes(row.orderStatus);
          },
        },
      ];
      return btnArr.find((item) => item.type === type)?.condition(row);
      // if (type === "edit" || type === "finish" || type === "remind") {
      //   return row.createBy === this.userId && row.orderStatus === "1";
      // } else if (type === "handleOver") {
      //   return (
      //     (row.handleUser === this.userId ||
      //       row.chargeUser === this.userId ||
      //       row.createBy === this.userId) &&
      //     row.orderStatus === "1"
      //   );
      // } else if (type === "handle") {
      //   return (
      //     (row.createBy === this.userId && row.orderStatus === "1") ||
      //     (row.handleUser === this.userId &&
      //       (row.orderStatus === "1" ||
      //         row.orderStatus === "2" ||
      //         row.orderStatus === "4")) ||
      //     (row.chargeUser === this.userId &&
      //       (row.orderStatus === "1" ||
      //         row.orderStatus === "2" ||
      //         row.orderStatus === "4"))
      //   );
      // } else if (type === "finish") {
      //   return (
      //     ((row.createBy === this.userId || row.handleUser === this.userId) &&
      //       (row.orderStatus === "1" || row.orderStatus === "2")) ||
      //     (row.handleUser === this.userId && row.orderStatus === "4")
      //   );
      // } else if (type === "check") {
      //   return (
      //     row.createBy === this.userId &&
      //     (row.orderStatus === "3" || row.orderStatus === "4")
      //   );
      // } else if (type === "detail") {
      //   return (
      //     row.createBy === this.userId ||
      //     row.handleUser === this.userId ||
      //     row.chargeUser === this.userId ||
      //     (row.serviceUser && row.serviceUser.indexOf(this.userId) != -1)
      //   );
      // } else if (type === "tag") {
      //   return (
      //     (row.createBy === this.userId &&
      //       (row.orderStatus === "3" || row.orderStatus === "4")) ||
      //     (row.createBy === this.userId && row.orderStatus === "2")
      //   );
      // }
    },
    // openEditPage(row) {
    //   this.$router.push({
    //     path: "/operationMaintenanceManage/operationWorkOrder/edit",
    //     query: {
    //       orderId: row.orderId,
    //       orderNo: row.orderNo,
    //       type: "edit",
    //       orderTypeParentId: row.orderTypeParentId,
    //       orderTypeParentName: row.orderTypeParentName,
    //       businessType: row.businessType,
    //       businessTypeName: row.businessTypeName,
    //     },
    //   });
    // },
    async handleExport() {
      const {
        orderTypeNames,
        orderTypeParentNames,
        threeOrderTypeNames,
        ...rest
      } = this.searchForm;
      let params = {
        ...rest,
        orderNos: this.handRow.orderNos,
      };
      params["orderTypeNames"] = this.flattenData
        ?.filter((x) => orderTypeNames.includes(x.id))
        ?.map((i) => i.typeName);
      params["orderTypeParentNames"] = this.flattenData
        ?.filter((x) => orderTypeParentNames.includes(x.id))
        ?.map((i) => i.typeName);
      params["threeOrderTypeNames"] = this.flattenData
        ?.filter((x) => threeOrderTypeNames.includes(x.id))
        ?.map((i) => i.typeName);
      if (Array.isArray(params.createTime)) {
        params.startCreateTime = params.createTime[0] + " 00:00:00";
        params.endCreateTime = params.createTime[1] + " 23:59:59";
        delete params.createTime;
      }
      if (Array.isArray(params.handleTime)) {
        params.startHandleTime = params.handleTime[0] + " 00:00:00";
        params.endHandleTime = params.handleTime[1] + " 23:59:59";
        delete params.handleTime;
      }
      if (Array.isArray(params.finishTime)) {
        params.startFinishTime = params.finishTime[0] + " 00:00:00";
        params.endFinishTime = params.finishTime[1] + " 23:59:59";
        delete params.finishTime;
      }
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      let text =
        this.handRow.orderNos?.length == 0
          ? "是否确认导出所有数据?"
          : "是否确认导出所选数据?";
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          this.reportTrackEvent(MAINTENANCE_ORDER_CLICK_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    closeVisible() {
      this.visible = false;
      this.form = {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      };
    },
    validateReason(rule, value, callback) {
      if (this.flag && !value) {
        callback(new Error("审核不通过原因不能为空"));
      }
      callback();
    },
    //提交审核
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.checkLoading = true;
          const params = {
            ...this.form,
            orderNo: this.orderNo,
            orderId: this.orderId,
          };
          check(params)
            .then((res) => {
              this.checkLoading = false;
              this.$message.success("审核工单成功");
              this.visible = false;
              this.getList();

              this.reportAuditTrack();
            })
            .catch((err) => {
              this.checkLoading = false;
            });
        }
      });
    },
    reportAuditTrack() {
      switch (this.form.orderStatus) {
        case "4":
          //驳回
          this.reportTrackEvent(MAINTENANCE_ORDER_REJECT_SUCCESS, {
            orderStatus: "驳回",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            source: "web",
          });
          break;
        case "5":
          //已完结
          this.reportTrackEvent(MAINTENANCE_ORDER_FINISH_SUCCESS, {
            orderStatus: "已完结",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            finishDuration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ), //完结时长
            source: "web",
          });
          break;
        case "7":
          //待回访
          this.reportTrackEvent(MAINTENANCE_ORDER_FLLOWUP_SUCCESS, {
            orderStatus: "待回访",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            finishDuration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ), //完结时长
            source: "web",
          });
          break;
      }
    },
    getGroupList() {
      queryGroupList({ pageNum: 1, pageSize: 9999, status: 0 }).then((res) => {
        this.groupOptions = res?.data;
      });
      queryGroupList({ pageNum: 1, pageSize: 9999 }).then((res) => {
        this.groupAllOptions = res?.data;
      });
    },
    check(row) {
      this.visible = true;
      this.curOrderCreateTime = row.createTime;
      this.curOrderHandleTime = row.handleTime;
      this.secondFaultOptions = [];
      this.thirdFaultOptions = [];
      queryGroupMemberList({ groupIds: [] }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
      this.orderNo = row.orderNo;
      this.orderId = row.orderId;
      // this.form = { ...row };
    },
    save() {
      const vm = this;
      this.$refs.handleForm.validate((valid) => {
        if (valid) {
          // if (
          //   vm.handleForm.orderStatus === "2" &&
          //   vm.handleForm.handleUser !== vm.userId
          // ) {
          //   vm.$message.error("工单正在处理中，无法保存");
          //   return;
          // }
          vm.saveLoading = true;
          const params = { ...vm.handleForm };
          params.submitFlag = 0;
          vm.handleFormSubmit(params, "saveLoading", "保存");
        }
      });
    },
    handleDialogSubmit() {
      const vm = this;
      this.$refs.handleForm.validate((valid) => {
        if (valid) {
          // if (
          //   vm.handleForm.orderStatus === "2" &&
          //   vm.handleForm.handleUser !== vm.userId
          // ) {
          //   vm.$message.error("工单正在处理中，无法处理");
          //   return;
          // }
          if (
            vm.handleForm.orderStatus === "3" ||
            vm.handleForm.orderStatus === "5"
          ) {
            vm.$message.error("工单已处理完成，请勿重复提交");
            return;
          }
          vm.submitLoading = true;
          const params = { ...vm.handleForm };
          params.submitFlag = 1;
          vm.handleFormSubmit(params, "submitLoading", "提交");
        }
      });
    },
    handleFormSubmit(params, loading, text) {
      const vm = this;
      params.handleTypeName = vm.handleTypeOptions.find(
        (item) => item.dictValue === params.handleType
      ).dictLabel;
      const arr = [];
      params.handleMethod.forEach((o) => {
        const handleMethodName = vm.handleMethodOptions.find(
          (item) => item.dictValue === o
        ).dictLabel;
        arr.push(handleMethodName);
      });

      const faultArr = [];
      params.faultType.forEach((o) => {
        const faultTypeName = vm.faultTypeOptions.find(
          (item) => item.dictValue === o
        ).dictLabel;
        faultArr.push(faultTypeName);
      });

      params.docList = vm.$refs.upload.fileList;
      params.handleMethod = params.handleMethod.join(",");
      params.handleMethodName = arr.join(",");

      params.faultType = params.faultType.join(",");
      params.faultTypeName = faultArr.join(",");
      handleOrder(params)
        .then((res) => {
          vm[loading] = false;
          vm.$message.success(text + "成功");
          vm.handleDialogVisible = false;
          vm.getList();

          this.reportTrackEvent(MAINTENANCE_ORDER_HANDLE_SUCCESS, {
            isSubmit: params.submitFlag,
            noHandleTags: !params?.handleTag || params?.handleTag?.length == 0,
            duration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ),
            source: "web", //和app共用一个事件
          });
        })
        .catch((err) => {
          vm[loading] = false;
        });
    },
    showHandleDialog(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/operationWorkOrder/handle",
        query: {
          orderNo: row.orderNo,
          handleUser: row.handleUser,
          customizeFlag: row.customizeFlag,
        },
      });
      // this.handleForm = {};
      // this.handleForm.orderStatus = row.orderStatus;
      // this.handleForm.orderNo = row.orderNo;
      // this.handleForm.handleUser = row.handleUser;
      // this.handleDialogVisible = true;
      // this.curOrderCreateTime = row.createTime;
      // this.getHandleDetail(row.orderNo);
      // this.getHandleDetail('YW20240116035029204')
    },
    getHandleDetail(orderId) {
      const vm = this;
      handleDetail({ orderNo: orderId }).then((res) => {
        if (res.data) {
          vm.handleForm = { ...vm.handleForm, ...res.data };
          vm.handleForm.handleMethod = res.data.handleMethod.indexOf(",")
            ? res.data.handleMethod.split(",")
            : [res.data.handleMethod];

          vm.handleForm.faultType = res.data.faultType.indexOf(",")
            ? res.data.faultType.split(",")
            : [res.data.faultType];
          vm.$refs.upload.fileList = res.data.docList || [];
        } else {
          vm.handleForm = { ...vm.handleForm, ...{} };
          vm.$refs.upload.fileList = [];
        }
      });
    },
    closeHandleDialog() {
      this.handleDialogVisible = false;
    },
    // openStationManage() {
    //   this.$router.push({
    //     path: "/stationRelation",
    //   });
    // },
    handleProcessGroup() {
      this.$router.push({
        path: "/errorPush/setProcessGroup",
        query: {},
      });
    },
  },
  watch: {
    $route: {
      handler(newVal) {
        if (newVal.query.businessNo) {
          this.searchForm.orderNo = newVal.query.businessNo;
          this.getList();
        }
      },
      immediate: true,
    },
  },
};
</script>

<style scoped lang="less">
.order-desc {
  padding: 20px;
  margin: 0 20px;
  border: 1px solid #ccc;
  overflow: auto;
  white-space: pre-line;
}
/deep/ .upload-content {
  flex-direction: column;
}
.tags-item {
  margin-right: 10px;
  margin-bottom: 5px;
}
.tags-tooltip-item {
  margin-right: 10px;
  margin-bottom: 5px;
  white-space: normal;
  height: auto;
  max-width: 380px;
}
.tags-span {
  margin-right: 10px;
  margin-bottom: 5px;
  display: inline-block;
  width: 100px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
