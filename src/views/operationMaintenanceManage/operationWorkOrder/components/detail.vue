<!-- 项目管理 -->
<template>
  <div class="app-container">
    <h3>工单详情</h3>
    <!-- 工单进度-S -->
    <el-card class="step-card">
      <div
        slot="header"
        class="card-title-wrap"
        style="justify-content: space-between"
      >
        <div style="display: flex;">
          <div class="card-title-line"></div>
          <span>工单进度</span>
          <el-tag style="margin-left:10px">{{
            getDictLabel(basicForm.orderStatus, orderStatusDict)
          }}</el-tag>
        </div>
        <div>
          <el-button
            type="primary"
            @click.stop="item.clickFn(basicForm)"
            v-for="(item, index) in operationBtnList"
            :key="index"
            v-show="item.show(basicForm)"
            v-has-permi="[item.permission]"
          >
            {{ item.title }}
          </el-button>
        </div>
        <!-- <el-button type="primary" @click="report" v-if="showReport">
          工单报告
        </el-button> -->
      </div>
      <el-steps :space="200" align-center>
        <el-step
          v-for="(item, index) in stepsList"
          :key="index"
          :title="item.name"
          :status="item.status"
        />
      </el-steps>
    </el-card>
    <!-- 工单进度-E -->
    <el-card>
      <el-tabs v-model="activeName" @tab-click="activeNameChange">
        <el-tab-pane label="基本信息" name="info">
          <div class="common-box info-box">
            <div class="info-title">
              <el-tag style="margin-right:10px" effect="dark" type="danger">{{
                basicForm.urgencyLevel
              }}</el-tag>
              {{ orderNo
              }}<i
                class="el-icon-document-copy pointer-icon"
                @click="copyToClipboard"
              ></i>
            </div>
            <div class="info-desc" @click="openDescDetail">
              <div class="info-desc-left">
                问题描述：
                <span class="font-bold">{{ basicForm.orderDesc }}</span>
              </div>
              <div class="info-desc-right">
                <el-button @click="openDescDetail" type="text"
                  >详情>></el-button
                >
              </div>
            </div>
            <!-- 充电桩故障工单-故障信息-S -->
            <ul
              class="info-fault"
              @click="openFaultDetail"
              v-if="channel === '03'"
            >
              <li v-for="(item, index) in alarmList" :key="index">
                <div>
                  故障名称：<span class="font-bold">{{ item.errorName }}</span>
                </div>
                <div>
                  故障码：<span class="font-bold">{{ item.faultCode }}</span>
                </div>
                <div>
                  告警次数：<span class="font-bold">{{
                    item.triggerCount
                  }}</span>
                </div>
              </li>
            </ul>
            <!-- 充电桩故障工单-故障信息-E -->
          </div>
          <!-- 基础信息-S -->
          <div>
            <CommonTitle class="mb10" title="基础信息" />
            <div class="common-box gird-box">
              <ul>
                <li>
                  <div>业务类型</div>
                  <div>{{ basicForm.businessTypeName }}</div>
                </li>
                <li>
                  <div>工单类型</div>
                  <div>{{ basicForm.orderTypeStr }}</div>
                </li>
                <li>
                  <div>站点等级</div>
                  <div>{{ basicForm.stationGrade }}</div>
                </li>
                <li v-if="!isChargeOrder">
                  <div>站点标签</div>
                  <div>
                    <el-tag
                      style="margin-right: 10px;"
                      :key="x"
                      v-for="x in basicForm.stationTagList"
                    >
                      {{ x }}
                    </el-tag>
                  </div>
                </li>
                <li v-if="isChargeOrder">
                  <div>设备编号</div>
                  <div>{{ basicForm.deviceNo }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>设备名称</div>
                  <div>{{ basicForm.deviceName }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>设备类型</div>
                  <div>
                    {{ basicForm.deviceTypeName }}
                  </div>
                </li>
              </ul>
              <ul>
                <li>
                  <div>站点名称</div>
                  <div>{{ basicForm.stationName }}</div>
                </li>
                <li>
                  <div>地址</div>
                  <div>{{ basicForm.address }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>站点标签</div>
                  <div>
                    <el-tag
                      style="margin-right: 10px;"
                      :key="x"
                      v-for="x in basicForm.stationTagList"
                    >
                      {{ x }}
                    </el-tag>
                  </div>
                </li>
                <li v-if="isChargeOrder">
                  <div>设备型号</div>
                  <div>{{ basicForm.deviceModelName }}</div>
                </li>
              </ul>
            </div>
          </div>
          <!-- 基础信息-E -->
          <!-- 创建信息-S -->
          <div>
            <CommonTitle class="mb10" title="创建信息" />
            <div class="common-box gird-box">
              <ul>
                <li>
                  <div>创建时间</div>
                  <div>{{ basicForm.createTime }}</div>
                </li>
                <li>
                  <div>创建人</div>
                  <div>{{ basicForm.createByName }}</div>
                </li>
              </ul>
              <ul>
                <li>
                  <div>创建类型</div>
                  <div>{{ getDictLabel(basicForm.channel, channelDict) }}</div>
                </li>
              </ul>
            </div>
          </div>
          <!-- 创建信息-E -->
          <!-- 处理人-S -->
          <div>
            <CommonTitle class="mb10" title="处理人" />
            <div class="common-box gird-box">
              <ul>
                <li>
                  <div>处理组</div>
                  <div>{{ basicForm.handleGroupName }}</div>
                </li>
              </ul>
              <ul>
                <li>
                  <div>当前处理人</div>
                  <div>{{ basicForm.handleUserName }}</div>
                </li>
              </ul>
            </div>
          </div>
          <!-- 处理人-E -->
          <!-- 其他信息-S -->
          <div>
            <CommonTitle class="mb10" title="其他信息" />
            <div class="common-box gird-box">
              <ul>
                <li>
                  <div>客户名称</div>
                  <div>{{ basicForm.customerName }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>车辆品牌</div>
                  <div>{{ basicForm.carBrand }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>车辆型号</div>
                  <div>{{ basicForm.carModel }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>保内/保外</div>
                  <div>
                    {{ getDictLabel(basicForm.shelfLife, shelfLifeDict) }}
                  </div>
                </li>
              </ul>
              <ul>
                <li>
                  <div>联系人</div>
                  <div>{{ basicForm.contact }}</div>
                </li>
                <li>
                  <div>联系电话</div>
                  <div>{{ basicForm.contactTel }}</div>
                </li>
                <li v-if="isChargeOrder">
                  <div>预约时间</div>
                  <div>{{ basicForm.appointmentTime }}</div>
                </li>
              </ul>
            </div>
          </div>
          <!-- 其他信息-E -->
          <!-- 超时情况-S -->
          <div>
            <CommonTitle class="mb10" title="超时情况" />
            <div class="common-box gird-box">
              <ul>
                <li>
                  <div>处理是否超时</div>
                  <div>{{ basicForm.isTreatment == "1" ? "是" : "否" }}</div>
                </li>
                <li v-if="basicForm.isTreatment == '1'">
                  <div>处理超时时长</div>
                  <div>{{ basicForm.treatmentTime }}</div>
                </li>
                <li v-else-if="basicForm.isTreatAboutRunOut == '1'">
                  <div>处理到期提醒</div>
                  <div>
                    {{ basicForm.treatAboutRunOut }}
                  </div>
                </li>
              </ul>
              <ul>
                <li>
                  <div>审核是否超时</div>
                  <div>{{ basicForm.isAudit == "1" ? "是" : "否" }}</div>
                </li>
                <li v-if="basicForm.isAudit == '1'">
                  <div>审核超时时长</div>
                  <div>{{ basicForm.auditTime }}</div>
                </li>
                <li v-else-if="basicForm.isAuditAboutRunOut == '1'">
                  <div>审核到期提醒</div>
                  <div>
                    {{ basicForm.auditAboutRunOut }}
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <!-- 超时情况-E -->
        </el-tab-pane>
        <el-tab-pane label="流转记录" name="record">
          <el-timeline :hide-timestamp="true" v-if="recordList.length > 0">
            <el-timeline-item
              placement="top"
              v-for="item in recordList"
              :key="item.orderRecordId"
            >
              <el-card>
                <el-row style="margin-bottom: 16px">
                  <el-col :span="5">
                    <span class="timeline-title">{{
                      item.operatorTypeName
                    }}</span>
                  </el-col>
                  <el-col :span="5">
                    <span>操作人：{{ item.operatorUserName }}</span>
                  </el-col>
                  <el-col :span="5" :offset="9">
                    <span>{{ item.createTime }}</span>
                  </el-col>
                </el-row>
                <el-row>
                  <span
                    style="display:block;margin-top: 10px"
                    v-if="item.operatorTypeName === '完结工单'"
                    >完结备注: {{ item.remark }}</span
                  >
                  <span
                    style="display:block;margin-top: 10px"
                    v-else-if="item.operatorTypeName === '结束工单'"
                    >结束原因: {{ item.remark }}</span
                  >
                  <span
                    style="display:block;margin-top: 10px"
                    v-else-if="item.operatorTypeName === '处理标签'"
                    >{{ item.remark }}</span
                  >
                  <span
                    style="display:block;margin-top: 10px"
                    v-else-if="item.operatorTypeName === '驳回工单'"
                    >驳回原因: {{ item.remark }}</span
                  >
                  <span
                    style="display:block;margin-top: 10px"
                    v-else-if="item.operatorTypeName === '转派工单'"
                    >{{ item.remark }}</span
                  >
                  <template v-else-if="item.operatorTypeName === '处理工单'">
                    <span style="display:block;margin-top: 10px">{{
                      item.remark && item.remark.split("&")[0]
                    }}</span>
                    <div
                      style="display:block;margin-top: 10px"
                      v-if="showDocList(item)"
                    >
                      <el-row type="flex" style="flex-wrap:wrap">
                        <div
                          v-for="(o, index) in item.remark
                            .split('&')[1]
                            .split('|')"
                          :key="index"
                          class="file-item"
                        >
                          <el-image
                            style="width: 150px;height:150px;margin-right:10px"
                            :src="o"
                            alt="加载失败"
                            class="avatar"
                            v-if="
                              o.toLowerCase().indexOf('.jpg') > 0 ||
                                o.toLowerCase().indexOf('.jpeg') > 0 ||
                                o.toLowerCase().indexOf('.png') != -1
                            "
                            :preview-src-list="
                              item.remark
                                .split('&')[1]
                                .split('|')
                                .filter(
                                  (o) =>
                                    o.toLowerCase().indexOf('.jpg') > 0 ||
                                    o.toLowerCase().indexOf('.jpeg') > 0 ||
                                    o.toLowerCase().indexOf('.png') != -1
                                )
                            "
                          />
                          <!-- @click.stop="
                              clickImg(
                                index,
                                item.remark.split('&')[1].split('|')
                              )
                            " -->
                          <video
                            style="width: 150px;height:150px;margin-right:10px"
                            v-if="o.toLowerCase().indexOf('.mp4') > 0"
                            :src="o"
                            controls
                          ></video>
                        </div>
                      </el-row>
                    </div>
                  </template>
                  <span v-else style="display:block;margin-top: 10px">
                    {{ item.remark }}
                  </span>
                </el-row>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
        <el-tab-pane
          label="故障&通用&工勘处理结果"
          name="result"
          v-if="isChargeOrder || isCommonOrder || isEngineerOrder"
        >
          <el-tabs v-model="activeResult">
            <el-tab-pane label="故障&通用工单处理结果" name="commonResult">
              <div>
                <CommonTitle class="mb10" title="故障类别" />
                <div class="common-box">
                  <el-tree
                    :data="faultTypeList"
                    :props="defaultProps"
                    default-expand-all
                  ></el-tree>
                </div>
              </div>
              <div>
                <CommonTitle class="mb10" title="检查内容" />
                <div class="common-box ">{{ basicForm.checkContent }}</div>
              </div>
              <div>
                <CommonTitle class="mb10" title="处理措施及结果" />
                <div class="common-box">{{ basicForm.handleResult }}</div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="工勘工单处理结果" name="engineerResult">
              <div v-if="formArr && formArr.length > 0">
                <div v-for="(item, index) in formArr" :key="index">
                  <FormCreatePreview
                    :formData="item.formJson"
                  ></FormCreatePreview>
                </div>
              </div>
              <el-empty v-else></el-empty>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <!-- <el-tab-pane label="费用信息" name="fee"></el-tab-pane> -->
        <el-tab-pane label="附件" name="file">
          <div class="file-box" v-if="fileList && fileList.length > 0">
            <div v-for="(item, index) in fileList" :key="index">
              <el-image
                :src="item.storePath"
                alt="加载失败"
                class="file-item"
                v-if="isImage(item.storePath)"
                :preview-src-list="
                  fileList.map((x) => x.storePath).filter((i) => isImage(i))
                "
              />
              <!-- @click.stop="clickImg(index, fileList)" -->
              <video
                v-if="item.storePath.toLowerCase().indexOf('.mp4') > 0"
                :src="item.storePath"
                controls
                class="file-item"
              ></video>
            </div>
          </div>
          <el-empty v-else></el-empty>
        </el-tab-pane>

        <el-tab-pane label="评价" name="evaluate">
          <template v-if="commentData && basicForm.orderStatus === '9'">
            <el-row>
              <el-col>
                <div class="detail-item">
                  <span>评价总分：</span>
                  <span>{{ commentData.scoreTotal }}分</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div class="detail-item">
                  <span>评价时间：</span>
                  <span>{{ commentData.commentTime }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span>评价人：</span>
                  <span>{{ commentData.commentBy }}</span>
                </div>
              </el-col>
            </el-row>
            <el-table
              :data="commentData.orderComments"
              style="width: 100%; margin-top: 24px"
              bordered
              class="comment-table"
            >
              <el-table-column prop="commentName" label="评价项" align="center">
              </el-table-column>
              <el-table-column prop="scoreReal" label="分数" align="center">
              </el-table-column>
            </el-table>
          </template>
          <el-empty v-else></el-empty>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <!-- <PicPreview ref="picPreview"></PicPreview> -->
    <el-drawer title="查看问题描述" :visible.sync="drawerVisible" size="60%">
      <div v-html="basicForm.orderDescText" class="order-desc"></div>
    </el-drawer>
    <FaultDrawer
      ref="faultDrawer"
      :orderNo="this.orderNo"
      :orderType="this.basicForm.orderType"
    ></FaultDrawer>
    <!-- 转派工单-S -->
    <el-dialog
      title="转派工单"
      :visible.sync="handleOverVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="transferForm"
        ref="transferForm"
        label-width="110px"
        :rules="transferRules"
      >
        <el-form-item label="选择人员" prop="handleUser">
          <el-select
            v-model="transferForm.handleUser"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in transferUserOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原因" prop="remark">
          <el-input
            v-model="transferForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="transferForm.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click.stop="handleSubmit()">提交</el-button>
      </div>
    </el-dialog>
    <!-- 转派工单-E -->
    <!-- 审核工单-S -->
    <el-dialog
      title="审核工单"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeVisible"
      append-to-body
      width="50%"
    >
      <el-form :model="form" ref="form" label-width="110px" :rules="rules">
        <el-form-item label="工单状态" prop="orderStatus">
          <el-select v-model="form.orderStatus" style="width: 100%;" filterable>
            <el-option
              v-for="item in checkOrderOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知组" prop="groupIds">
          <el-select
            v-model="form.groupIds"
            multiple
            filterable
            @change="handleGroupChange"
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupOptions"
              :label="item.groupName"
              :value="item.groupId"
              :key="item.groupId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知人" prop="userIds">
          <el-select
            v-model="form.userIds"
            multiple
            filterable
            style="width: 100%;"
          >
            <el-option
              v-for="item in groupMemberOptions"
              :label="item.nickName + '-' + item.userName"
              :value="item.userId"
              :key="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="通知方式" prop="messageTypes">
          <el-checkbox-group v-model="form.messageTypes">
            <el-checkbox label="01">钉钉</el-checkbox>
            <el-checkbox label="02">短信</el-checkbox>
            <el-checkbox label="03">APP</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备注" prop="reason">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="5"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="checkLoading" @click.stop="closeVisible"
          >取消
        </el-button>
        <el-button :loading="checkLoading" type="primary" @click="submit"
          >确定
        </el-button>
      </div>
    </el-dialog>
    <!-- 审核工单-E -->
  </div>
</template>

<script>
import { queryGroupList, queryGroupMemberList } from "@/api/errorPush/index.js";
import FormCreatePreview from "@/components/formCreatePreview/index.vue";
import { mapGetters } from "vuex";
import {
  MAINTENANCE_ORDER_CLICK_PUSH,
  MAINTENANCE_ORDER_CLICK_REMARK,
  MAINTENANCE_ORDER_CLICK_REPORT,
  MAINTENANCE_ORDER_FINISH_SUCCESS,
  MAINTENANCE_ORDER_FLLOWUP_SUCCESS,
  MAINTENANCE_ORDER_HANDLE_SUCCESS,
  MAINTENANCE_ORDER_REJECT_SUCCESS,
  MAINTENANCE_ORDER_STOP_SUCCESS,
  MAINTENANCE_ORDER_TRANSFER_SUCCESS,
} from "@/utils/track/track-event-constants";
import FaultDrawer from "./faultDrawer.vue";
import {
  orderDetail,
  orderRecord,
  getReport,
  check,
  childrenList,
  exportExcel,
  fistLevelList,
  handleDetail,
  handleOrder,
  pushMsg,
  queryList,
  stopOrder,
  submitRemark,
  transferOrder,
  updateOrderHandleTag,
  getDeptList,
  submitRadius,
  receiveOrder,
  submitCancel,
  queryBindOrderTypeList,
  getNewOrgNo,
  queryTransferUser,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import { timeDiffFormat } from "@/utils/comm";

// import PicPreview from "@/components/Upload/picPreview.vue";
import CommonTitle from "@/components/commonTitle";
import { queryOperationWorkOrderEvaluate } from "@/api/operationMaintenanceManage/evaluate/index";
import formCreateMixin from "@/mixin/formCreate.js";

export default {
  name: "operationWorkOrderDetail",
  mixins: [formCreateMixin],
  components: {
    // PicPreview,
    CommonTitle,
    FaultDrawer,
    FormCreatePreview,
  },
  data() {
    return {
      formArr: [],
      activeResult: "commonResult",
      faultTypeList: [],
      defaultProps: {
        children: "childrenList",
        label: "typeName",
      },
      mockData: [
        {
          label: "一级 1",
          children: [
            {
              label: "二级 1-1",
              children: [
                {
                  label: "三级 1-1-1",
                },
              ],
            },
          ],
        },
        {
          label: "一级 2",
          children: [
            {
              label: "二级 2-1",
              children: [
                {
                  label: "三级 2-1-1",
                },
              ],
            },
            {
              label: "二级 2-2",
              children: [
                {
                  label: "三级 2-2-1",
                },
              ],
            },
          ],
        },
        {
          label: "一级 3",
          children: [
            {
              label: "二级 3-1",
              children: [
                {
                  label: "三级 3-1-1",
                },
              ],
            },
            {
              label: "二级 3-2",
              children: [
                {
                  label: "三级 3-2-1",
                },
              ],
            },
          ],
        },
      ],
      curOrderCreateTime: "",
      curOrderHandleTime: "",
      form: {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      },
      visible: false,
      transferUserOptions: [],
      transferForm: {
        handleUser: undefined,
        messageTypes: ["01", "02", "03"],
        remark: "",
      },
      transferRules: {
        handleUser: [
          { required: true, message: "请选择处理人员", trigger: "change" },
        ],
      },
      handleOverVisible: false,

      activeName: "info",
      groupOptions: [],
      groupMemberOptions: [],
      checkOrderOptions: [
        { value: "4", label: "驳回" },
        { value: "7", label: "待回访" },
        { value: "5", label: "已完结" },
      ],
      orderNo: "",
      recordList: [],

      checkLoading: false,
      orderStatusOptions: [],
      orderId: "",
      stepsList: [],
      basicForm: {},
      orderStatusDict: [],
      drawerVisible: false,
      channelDict: [
        { dictLabel: "手动", dictValue: "01" },
        { dictLabel: "客服", dictValue: "02" },
        { dictLabel: "充电平台", dictValue: "03" },
      ],
      shelfLifeDict: [
        { dictLabel: "保内", dictValue: "0" },
        { dictLabel: "保外", dictValue: "1" },
      ],
      alarmList: [],
      subTypeDict: [],
      fileList: [],
      channel: "",

      evaluateData: [],
      commentData: null,
    };
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    isChargeOrder() {
      return this.basicForm.orderTypeParentName === "充电桩故障工单";
    },
    isCommonOrder() {
      return this.basicForm.orderTypeParentName === "通用工单";
    },
    isEngineerOrder() {
      return this.$route.query.customizeFlag === "Y";
    },
    operationBtnList() {
      return [
        {
          title: "接单",
          clickFn: () => {
            this.handleReceive();
          },
          permission: "maintenance:workOrder:receive",
          show: (row) => {
            //处理组内 userid list
            const handleGroupUserIds = row.handleGroupUserIds;

            //处理人
            const handleUser = row.handleUser;

            const createBy = row.createBy;

            // 待接单
            const orderStatusList = ["8"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            return (
              orderStatus &&
              (createBy == this.userId ||
                (handleUser
                  ? handleUser == this.userId
                  : handleGroupUserIds?.includes(this.userId)))
            );
          },
        },
        {
          title: "处理",
          clickFn: (row) => {
            this.handleJump(row);
          },
          permission: "maintenance:workOrder:handle",
          show: (row) => {
            // "调试工单","保养工单","巡检工单" 只能在 app 处理
            const orderTypeParentName = ["调试工单", "保养工单", "巡检工单"];

            // 待处理  处理中  已驳回  待回访
            const orderStatusList = ["1", "2", "4", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);
            return (
              orderStatus &&
              !orderTypeParentName.includes(row.orderTypeParentName) &&
              row.handleUser == this.userId
            );
          },
        },
        {
          title: "转派",
          clickFn: () => {
            this.transfer();
          },
          permission: "maintenance:workOrder:handleOver",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //创建人
            const createBy = row.createBy;

            // 待接单 待处理  待回访
            const orderStatusList = ["8", "1", "7"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            // 运维人员 可以审批的工单状态  待回访
            const omOrderStatusList = ["7"];
            const omOrderStatus = omOrderStatusList.includes(row.orderStatus);

            // 处理人 可以审批的工单状态  待处理
            const handleOrderStatusList = ["1"];
            const handleOrderStatus = handleOrderStatusList.includes(
              row.orderStatus
            );

            return (
              (orderStatus && createBy == this.userId) ||
              (handleOrderStatus && row.handleUser == this.userId) ||
              ((isSupOM || isEngOM) && omOrderStatus)
            );
          },
        },
        {
          title: "审核",
          clickFn: (row) => {
            this.check(row);
          },
          permission: "maintenance:workOrder:check",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");

            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //已驳回 已处理
            const orderStatusList = ["3", "4"];
            const orderStatus = orderStatusList.includes(row.orderStatus);

            //  运维主管可以审核 所有工单  运维工程师 不能审核自己创建的工单
            return (
              (orderStatus && isSupOM) ||
              (orderStatus && isEngOM && this.userId != row.createBy)
            );
          },
        },
        {
          title: "工单报告",
          clickFn: () => {
            this.report();
          },
          permission: "maintenance:workOrder:report",
          show: (row) => {
            // 运维主管
            const isSupOM = this.roles.includes("supOM");
            // 运维工程师
            const isEngOM = this.roles.includes("engOM");

            //处理组内 userid list
            const handleGroupUserIds = row.handleGroupUserIds;

            //已处理 已完结 待回访 已驳回 已评价
            const orderStatusList = ["3", "4", "5", "6", "7", "9"];
            const orderStatus = orderStatusList.includes(row.orderStatus);
            return (
              orderStatus &&
              (row.createBy == this.userId ||
                row.handleUser == this.userId ||
                handleGroupUserIds?.includes(this.userId) ||
                isSupOM ||
                isEngOM)
            );
          },
        },
      ];
    },
    rules() {
      return {
        orderStatus: [
          { required: true, trigger: "blur", message: "请选择工单状态" },
        ],
        reason: [
          {
            required: this.form.orderStatus == "4",
            message: "工单状态为驳回时，备注不能为空！",
            trigger: "blur",
          },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
        oneFaultTypes: [
          { required: true, message: "请选择故障类型", trigger: "blur" },
        ],
      };
    },
  },
  created() {
    this.orderId = this.$route.query.orderId;
    this.orderNo = this.$route.query.orderNo;
    this.getDicts("order_status").then((response) => {
      this.orderStatusDict = response?.data;
    });
    this.getDicts("cm_sub_type").then((response) => {
      this.subTypeDict = response?.data;
    });
    this.getTransferUserList();
    this.getGroupList();
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      await this.getDetail();
      this.getRecordList();
      this.queryOperationWorkOrderEvaluate();
    },
    isImage(o) {
      return (
        o.toLowerCase().indexOf(".jpg") > 0 ||
        o.toLowerCase().indexOf(".jpeg") > 0 ||
        o.toLowerCase().indexOf(".png") != -1
      );
    },
    openFaultDetail() {
      this.$refs.faultDrawer.openDrawer();
    },
    openDescDetail() {
      this.drawerVisible = true;
    },
    copyToClipboard() {
      // 创建一个textarea元素
      const textarea = document.createElement("textarea");
      // 设置textarea的值为要复制的文本
      textarea.value = this.orderNo;
      // 将textarea添加到文档中
      document.body.appendChild(textarea);
      // 选中textarea中的文本
      textarea.select();
      try {
        // 尝试复制选中的文本
        const successful = document.execCommand("copy");
        const msg = successful ? "复制成功" : "复制失败";
        console.log(msg);
        this.$message.success(msg);
      } catch (err) {
        console.log("不能使用这种方法复制", err);
      }
      // 将textarea从文档中移除
      document.body.removeChild(textarea);
    },
    getDictLabel(value, dict) {
      return dict?.find((x) => x.dictValue == value)?.dictLabel || "";
    },
    async getDetail() {
      const res = await orderDetail({
        orderId: this.orderId,
      });
      if (res?.code === "10000") {
        this.basicForm = { ...res.data };
        this.faultTypeList = res.data?.faultTypeList || [];
        this.alarmList = res.data?.alarmList || [];
        this.channel = res.data?.channel;
        this.fileList = res.data?.handleDocList || [];
        this.stepsList = res.data?.progressList?.map((x) => {
          return {
            ...x,
            status:
              x.color === "green"
                ? "success"
                : x.color === "red"
                ? "error"
                : "",
          };
        });
        this.formArr = res.data?.customizeCheckItems?.map((x) => {
          return {
            formConfig: JSON.parse(x.formConfig),
            formJson: JSON.parse(x.formJson)?.map((item) => {
              this.handlePreviewFormRule(item);
              return item;
            }),
          };
        });
      }
    },

    getRecordList() {
      orderRecord({ orderNo: this.orderNo }).then((res) => {
        this.recordList = res.data;
        if (this.recordList.length < 1) {
          this.$message.info("暂无流转记录");
        }
      });
    },
    clickImg(index, list) {
      this.$refs.picPreview.open(index, list);
    },

    report() {
      console.log("工单报告");
      getReport({ orderNo: this.orderNo }).then((res) => {
        if (res?.code === "10000") {
          const pdfUrl = res.data;
          window.open(pdfUrl);
        }
      });
    },

    async getOrderStatusOptions() {
      await this.getDicts("order_status").then((response) => {
        this.orderStatusOptions = response?.data;
      });
    },
    orderStatusFormat(value) {
      return this.selectDictLabel(this.orderStatusOptions, value);
    },
    showDocList(item) {
      const arr = item.remark?.split("&");
      return arr && arr[1];
    },

    activeNameChange(v) {
      console.log(this.activeName);
    },

    async queryOperationWorkOrderEvaluate() {
      const data = await queryOperationWorkOrderEvaluate({
        orderId: this.orderId,
      });
      if (data.code === "10000") {
        this.commentData = data.data;
      }
    },
    handleReceive() {
      this.$confirm("确定要接单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          orderNo: this.orderNo,
        };
        receiveOrder(data).then((res) => {
          if (res?.success) {
            this.$message.success("接单成功");
            //更新列表
            this.loadData();
          } else {
            this.$message.error("接单失败");
          }
        });
      });
    },
    transfer() {
      this.handleOverVisible = true;
    },
    closeDialog() {
      this.$refs.transferForm.resetFields();
      this.handleOverVisible = false;
    },
    //指派
    handleSubmit() {
      this.$refs.transferForm.validate((valid) => {
        if (valid) {
          let params = {
            ...this.transferForm,
            orderNo: this.orderNo,
          };

          const loading = this.$loading({
            lock: true,
            text: "指派中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          transferOrder(params)
            .then((res) => {
              if (res?.success) {
                this.closeDialog();
                this.loadData();
                loading.close();
                this.$message.success("指派成功");
                this.reportTrackEvent(MAINTENANCE_ORDER_TRANSFER_SUCCESS, {
                  noNotifyUsers:
                    !params?.handleUser || params?.handleUser?.length == 0,
                  noNotifyWays:
                    !params?.messageTypes || params?.messageTypes?.length == 0,
                  source: "web", //和app共用一个事件
                });
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    check(row) {
      this.visible = true;
      this.curOrderCreateTime = row.createTime;
      this.curOrderHandleTime = row.handleTime;
      queryGroupMemberList({ groupIds: [] }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
    },
    //提交审核
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.checkLoading = true;
          const params = {
            ...this.form,
            orderNo: this.orderNo,
            orderId: this.orderId,
          };
          check(params)
            .then((res) => {
              this.checkLoading = false;
              this.$message.success("审核工单成功");
              this.visible = false;
              this.loadData();

              this.reportAuditTrack();
            })
            .catch((err) => {
              this.checkLoading = false;
            });
        }
      });
    },
    reportAuditTrack() {
      switch (this.form.orderStatus) {
        case "4":
          //驳回
          this.reportTrackEvent(MAINTENANCE_ORDER_REJECT_SUCCESS, {
            orderStatus: "驳回",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            source: "web",
          });
          break;
        case "5":
          //已完结
          this.reportTrackEvent(MAINTENANCE_ORDER_FINISH_SUCCESS, {
            orderStatus: "已完结",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            finishDuration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ), //完结时长
            source: "web",
          });
          break;
        case "7":
          //待回访
          this.reportTrackEvent(MAINTENANCE_ORDER_FLLOWUP_SUCCESS, {
            orderStatus: "待回访",
            auditDuration: timeDiffFormat(
              new Date(this.curOrderHandleTime),
              new Date()
            ), //审核时长
            finishDuration: timeDiffFormat(
              new Date(this.curOrderCreateTime),
              new Date()
            ), //完结时长
            source: "web",
          });
          break;
      }
    },
    closeVisible() {
      this.visible = false;
      this.form = {
        messageTypes: [],
        orderStatus: "",
        groupIds: [],
        userIds: [],
        oneFaultTypes: [],
        twoFaultTypes: [],
        threeFaultTypes: [],
        reason: "",
      };
    },
    //通知组变化
    handleGroupChange(val) {
      console.log("通知组", val);
      queryGroupMemberList({ groupIds: val }).then((res) => {
        this.groupMemberOptions = res?.data;
      });
    },
    getGroupList() {
      queryGroupList({ pageNum: 1, pageSize: 9999, status: 0 }).then((res) => {
        this.groupOptions = res?.data;
      });
    },
    getTransferUserList() {
      queryTransferUser({ orderNo: this.orderNo }).then((res) => {
        this.transferUserOptions = res.data;
      });
    },
    handleJump(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/operationWorkOrder/handle",
        query: {
          orderNo: row.orderNo,
          handleUser: row.handleUser,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.step-card /deep/ .el-steps {
  justify-content: center;
}
/deep/ .el-card {
  margin-bottom: 10px;
}
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
.common-box {
  border: 1px dashed #029c7c;
  padding: 20px;
  border-radius: 4px;
}
.gird-box {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 两列，每列占据相等的空间 */
  grid-gap: 10px; /* 可选项，用于设置盒子之间的间距 */
  ul {
    list-style-type: none;
    padding: 20px;
    margin: 0;
    &:first-child {
      border-right: 1px dotted #029c7c;
    }
    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 20px 0;
      div:first-child {
        font-size: 15px;
        color: #5c5c5c;
        width: 100px;
      }
    }
  }
}
.info-box {
  .pointer-icon {
    cursor: pointer;
    margin-left: 5px;
    color: #bbbbbb;
    &:hover {
      color: #029c7c;
    }
  }
  .info-title {
    margin: 10px 0;
    font-size: 15px;
    color: #5c5c5c;
  }
  .info-desc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 10px 0;
    &:hover {
      color: #029c7c;
    }
    &-left {
      flex: 1; /* 左边列自适应宽度 */
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
    &-right {
      width: 100px;
    }
  }
  .info-fault {
    list-style-type: none;
    margin: 0;
    padding: 10px 0;
    cursor: pointer;
    &:hover {
      color: #029c7c;
    }
    li {
      display: flex;
      padding: 10px 0;
      > div {
        flex: 1;
      }
    }
  }
}
.order-desc {
  padding: 20px;
  margin: 0 20px;
  border: 1px solid #ccc;
  overflow: auto;
  white-space: pre-line;
}
.mb10 {
  margin: 20px 0 10px 0;
}
.file-box {
  display: flex;
  flex-wrap: wrap;
  .file-item {
    margin: 20px;
    width: 300px;
    height: 300px;
  }
}
.font-bold {
  font-weight: 700;
}
.detail-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 40px;
}

/deep/ .comment-table thead th {
  background: #f5f5f5 !important;
}
</style>
