<template>
  <div>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基本信息</span>
      </div>
      <el-form
        :model="basicForm"
        ref="basicForm"
        label-width="150px"
        :disabled="disabled"
      >
        <el-form-item label="工单编号：" prop="orderNo">
          <span>{{ basicForm.orderNo }}</span>
          <!-- <el-input
            v-model="basicForm.orderDesc"
            type="textarea"
            size="mini"
            :rows="5"
            :disabled="pageType === 'detail'"
          /> -->
        </el-form-item>
        <el-form-item label="工单来源：" prop="channel">
          <span>{{
            getDictName(channelOptions, basicForm.channel) || basicForm.channel
          }}</span>
        </el-form-item>
        <el-form-item
          label="业务类型："
          @change="getBusinessTypeName"
          prop="businessType"
        >
          <span>{{
            getDictName(businessTypeOptions, basicForm.businessType) ||
              basicForm.businessTypeName
          }}</span>
          <!-- <el-select v-model="basicForm.businessType">
            <el-option
              v-for="item in businessTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="工单类型：" prop="orderTypeParentName">
          <span>{{ getOrderTypeNames() }}</span>
          <!-- <el-row>
            <el-col :span="3">
              <el-select
                v-model="basicForm.orderTypeParentName"
                @change="orderTypeParentChange"
              >
                <el-option
                  v-for="item in orderTypeParentOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="basicForm.orderTypeName">
                <el-option
                  v-for="item in orderTypeOptions"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-col>
          </el-row> -->
        </el-form-item>
        <el-form-item label="处理人：" prop="handleUserName">
          <span>{{ basicForm.handleUserName }}</span>
        </el-form-item>
        <el-form-item label="问题描述：" prop="orderDesc">
          <!-- <span>{{ basicForm.orderDesc }}</span> -->
          <!-- <el-input
            v-model="basicForm.orderDesc"
            type="textarea"
            size="mini"
            :rows="5"
            :disabled="pageType === 'detail'"
          /> -->
          <div v-html="basicForm.orderDesc" class="order-desc"></div>
        </el-form-item>
        <el-form-item label="故障详情：" v-if="basicForm.channel == '03'">
          <el-button
            type="text"
            @click="openFaultDrawer"
            :disabled="false"
            size="medium"
            >查看</el-button
          >
        </el-form-item>
        <el-form-item label="站点名称：" prop="stationId">
          {{
            getDictName(
              stationOptions,
              basicForm.stationId,
              "stationId",
              "stationName"
            )
          }}
          <!-- <el-select
            v-model="basicForm.stationId"
            @change="getPileList"
            filterable
          >
            <el-option
              v-for="item in stationOptions"
              :label="item.stationName"
              :value="item.stationId"
              :key="item.stationId"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="所在位置：" prop="region">
          <span>{{
            getDetailRegion(basicForm.region, basicForm.address)
          }}</span>
          <!-- <el-cascader
            v-model="basicForm.region"
            placeholder="请选择区域"
            :options="areaOptions"
            clearable
            filterable
            size="mini"
            :disabled="pageType === 'detail'"
            :props="{ checkStrictly: true }"
            collapse-tags
          ></el-cascader> -->
        </el-form-item>
        <el-form-item
          label="用户姓名："
          prop="customerUserName"
          v-if="basicForm.channel == '02' && basicForm.customerUserName"
        >
          <span>{{ basicForm.customerUserName }}</span>
        </el-form-item>
        <el-form-item
          label="用户手机号："
          prop="customerUserPhone"
          v-if="basicForm.channel == '02' && basicForm.customerUserPhone"
        >
          <span>{{ basicForm.customerUserPhone }}</span>
        </el-form-item>
        <el-form-item
          label="车型信息："
          prop="region"
          v-if="
            basicForm.channel == '02' &&
              (basicForm.customerCarBrand ||
                basicForm.customerCarModel ||
                basicForm.customerCarYear)
          "
        >
          <span
            >{{ basicForm.customerCarBrand || "" }}-{{
              basicForm.customerCarModel || ""
            }}-{{ basicForm.customerCarYear || "" }}
          </span>
        </el-form-item>
        <!-- <el-form-item label="详细地址：">
          <el-input
            v-model="basicForm.address"
            type="input"
            size="mini"
            :disabled="pageType === 'detail'"
          />
        </el-form-item> -->
        <el-form-item label="问题发生时间：">
          <span>{{ showOccurrenceTime() }}</span>
          <!-- <el-date-picker
            type="datetime"
            v-model="basicForm.occurrenceDate"
            @blur="changeOccurrenceDate"
          />
          <el-radio-group
            v-model="basicForm.occurrenceDateFlag"
            @change="changeOccurrenceDateFlag"
          >
            <el-radio label="0">不明确</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="期望处理时间：">
          <span>{{ showExpectTime() }}</span>
          <!-- <el-date-picker
            type="datetime"
            v-model="basicForm.expectHandleDate"
            @blur="changeExpectHandleDate"
          />
          <el-radio-group
            v-model="basicForm.expectHandleType"
            @change="changeExpectHandleDateType"
          >
            <el-radio label="1">今天</el-radio>
            <el-radio label="2">明天</el-radio>
            <el-radio label="3">3天内</el-radio>
            <el-radio label="4">7天内</el-radio>
            <el-radio label="5">不要求</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="紧急程度：" prop="urgencyLevel">
          <span>{{
            basicForm.urgencyLevel == 0
              ? "紧急"
              : basicForm.urgencyLevel == 1
              ? "一般"
              : ""
          }}</span>
          <!-- <el-radio-group v-model="basicForm.urgencyLevel">
            <el-radio label="0">紧急</el-radio>
            <el-radio label="1">一般</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="问题图片：">
          <!-- <file-upload
            type="img"
            ref="upload"
            :limit="9"
            :disabled="disabled"
          /> -->
          <el-row type="flex" style="flex-wrap:wrap">
            <div v-for="(o, index) in picList" :key="index" class="file-item">
              <el-image
                style="width: 150px;height:150px;margin-right:10px"
                :src="o.storePath"
                alt="加载失败"
                class="avatar"
                v-if="
                  o.storePath.toLowerCase().indexOf('.jpg') > 0 ||
                    o.storePath.toLowerCase().indexOf('.jpeg') > 0 ||
                    o.storePath.toLowerCase().indexOf('.png') != -1
                "
                @click.stop="clickImg(index)"
              />
              <video
                style="width: 150px;height:150px;margin-right:10px"
                v-if="o.storePath.toLowerCase().indexOf('.mp4') > 0"
                :src="oo.storePath"
                controls
                @click.stop="clickImg(index)"
              ></video></div
          ></el-row>
        </el-form-item>
        <el-form-item label="处理组：">
          <span>{{ basicForm.groupName }}</span>
        </el-form-item>
      </el-form>
      <el-form
        :model="chargingPileForm"
        ref="chargingPileForm"
        label-width="150px"
        :rules="rules"
      >
        <el-row
          v-for="(item, index) in chargingPileForm.pileGunList"
          :key="index"
        >
          <el-col :span="14">
            <el-form-item label="充电桩：">
              <!-- <span>{{ getPileName(item.pileCode) }}</span> -->
              <span>{{
                (item.pileName || "") + " - " + (item.pileCode || "")
              }}</span>
              <!-- <el-select
                v-model="item.pileCode"
                @change="(value) => getPileInfo(value, item)"
              >
                <el-option
                  v-for="i in availableOptions"
                  :label="`${i.pileCode}-${i.pileName}`"
                  :value="i.pileCode"
                  :key="i.pileCode"
                />
              </el-select> -->
            </el-form-item>
            <el-form-item label="充电桩故障描述：">
              {{ item.pileDesc }}
              <!-- <el-input
                v-model="item.pileDesc"
                type="textarea"
                size="mini"
                :disabled="pageType === 'detail'"
                max="500"
                :rows="5"
              /> -->
            </el-form-item>
            <el-form-item label="充电枪：">
              <!-- <el-row>
                <el-col :span="5">
                  <span>共{{ item.count }}枪</span>
                </el-col>
                <el-col :span="5">
                  <el-form-item>
                    <el-button
                      v-if="!disabled"
                      :disabled="!item.pileCode"
                      :loading="loading"
                      @click="queryRunningStatus(item)"
                      >查询枪运行状态</el-button
                    >
                  </el-form-item>
                </el-col>
              </el-row> -->
              <template
                v-if="item.gunList.length > 0 && item.gunList[0].runStatusName"
              >
                <el-row v-for="(object, index) in item.gunList" :key="index">
                  <!-- <el-col :span="5"> -->
                  <!-- <el-checkbox v-model="object.checked" /> -->
                  <span>{{ object.gunNo }}枪：</span>
                  <!-- </el-col> -->
                  <!-- <el-col :span="7">
                    <el-row>
                      <el-button type="info">{{
                        object.runStatusName
                      }}</el-button>
                    </el-row>
                    <el-row>
                      <span>上报时间：{{ object.updateTime }}</span>
                    </el-row>
                  </el-col> -->
                  <!-- <el-col :span="12"> -->
                  {{ object.gunDesc }}
                  <!-- <el-form-item label="问题描述" prop="gunDesc">
                      <el-input
                        v-model="object.gunDesc"
                        type="textarea"
                        size="mini"
                        :rows="5"
                        :disabled="pageType === 'detail'"
                      />
                    </el-form-item> -->
                  <!-- </el-col> -->
                </el-row>
              </template>
            </el-form-item>

            <!-- <el-form-item label="通知人：">
              <span>{{ item.handleUserName }}</span>
            </el-form-item> -->
          </el-col>
          <!-- <el-col :span="1">
            <el-form-item>
              <el-button
                v-if="item.pileCode"
                type="primary"
                circle
                style="margin-top: 90px"
                icon="el-icon-plus"
                @click="addItem"
              />
              <el-button
                v-if="index > 0"
                type="primary"
                circle
                style="margin-top: 90px"
                icon="el-icon-minus"
                @click="removeItem(index)"
              />
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
    </el-card>
    <!-- <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>充电桩信息</span>
      </div>
    </el-card> -->
    <div slot="footer" class="dialog-footer" v-if="!disabled">
      <el-button @click.stop="closeVisible">取 消</el-button>
      <el-button @click="submit" type="primary">提交</el-button>
    </div>
    <el-dialog
      title=""
      width="50%"
      :visible.sync="showImgPreview"
      @close="closePreviewDialog"
      append-to-body
      :before-close="resetPreview"
    >
      <div style="display: flex; justify-content: center; padding: 20px">
        <el-image
          :src="previewImgUrl"
          fit="contain"
          alt="加载失败"
          ref="previewImage"
          @mousewheel.prevent="handleImageWheel"
          :style="imageStyle"
        />
      </div>
    </el-dialog>
    <PicPreview ref="picPreview"></PicPreview>
    <FaultDrawer
      ref="faultDrawer"
      :orderType="basicForm.orderType"
      :orderNo="basicForm.orderNo"
    ></FaultDrawer>
  </div>
</template>
<script>
import { stationList } from "@/api/workOrderWorkbench";
import { regionData } from "element-china-area-data";
import { pileList } from "@/api/business/flow/flow";
import { detail, pileGunList, saveOrder } from "@/api/operationWorkOrder";
import FileUpload from "@/components/Upload/fileUpload.vue";
import moment from "moment";
import PicPreview from "@/components/Upload/picPreview.vue";
import FaultDrawer from "./faultDrawer";
export default {
  components: {
    FileUpload,
    PicPreview,
    FaultDrawer,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      handleTypeOptions: [
        { label: "今天", value: "1" },
        { label: "明天", value: "2" },
        { label: "3天内", value: "3" },
        { label: "7天内", value: "4" },
        { label: "不要求", value: "5" },
      ],
      basicForm: {
        occurrenceDate: "",
        expectHandleDate: "",
        expectHandleType: "",
        urgencyLevel: "",
        orderTypeName: "",
      },
      chargingPileForm: {
        pileGunList: [
          {
            count: 0,
            gunList: [
              {
                gunDesc: "",
              },
            ],
          },
        ],
      },
      businessTypeOptions: [],
      orderTypeParentOptions: [
        { label: "故障工单", value: "故障工单" },
        { label: "抄表工单", value: "抄表工单" },
        { label: "真车充电测试派单", value: "真车充电测试派单" },
      ],

      pageType: undefined,
      areaOptions: regionData,
      rules: {
        businessType: {
          required: true,
          message: "业务类型不能为空",
          trigger: "blur",
        },
        orderTypeParentName: [
          { required: true, message: "工单类型不能为空", trigger: "blur" },
          { validator: this.validateOrderTypeParentName, trigger: "blur" },
        ],
        orderDesc: [
          { required: true, message: "问题描述不能为空", trigger: "blur" },
          { max: 500, message: "500个字符以内", trigger: "blur" },
        ],
        stationId: {
          required: true,
          message: "站点名称不能为空",
          trigger: "blur",
        },
        region: {
          validator: this.validateRegion,
          trigger: "blur",
        },
        urgencyLevel: {
          required: true,
          message: "紧急程度不能为空",
          trigger: "blur",
        },
      },
      count: 0,
      stationOptions: [],
      pileOptions: [],
      loading: false,
      picList: [],
      showImgPreview: false,
      previewImgUrl: "",
      imageStyle: { width: "100vh", height: "100vh" },
      channelOptions: [
        { dictLabel: "手动", dictValue: "01" },
        { dictLabel: "客服", dictValue: "02" },
        { dictLabel: "充电平台", dictValue: "03" },
      ],
    };
  },
  computed: {
    orderTypeOptions() {
      const arr = [
        {
          label: "故障工单",
          value: "故障工单",
          children: [
            { label: "急停故障", value: "急停故障" },
            { label: "离线-断电", value: "离线-断电" },
            { label: "离线-断网", value: "离线-断网" },
            {
              label: "电桩上报故障(不含急停)",
              value: "电桩上报故障(不含急停)",
            },
            {
              label: "离线&故障(不含急停)同时发生",
              value: "离线&故障(不含急停)同时发生",
            },
            { label: "潜藏故障", value: "潜藏故障" },
          ],
        },
        {
          label: "抄表工单",
          value: "抄表工单",
          children: [{ label: "抄表工单", value: "抄表工单" }],
        },
        {
          label: "真车充电测试派单",
          value: "真车充电测试派单",
          children: [{ label: "真车充电测试派单", value: "真车充电测试派单" }],
        },
      ];
      return arr.find((x) => x.value === this.basicForm.orderTypeParentName)
        ?.children;
    },
    availableOptions() {
      const codeArr = this.chargingPileForm.pileGunList?.map((x) => {
        return x.pileCode;
      });
      return this.pileOptions?.filter(
        (item) => !codeArr.includes(item.pileCode)
      );
    },
  },
  methods: {
    //打开故障详情抽屉
    openFaultDrawer() {
      this.$refs.faultDrawer.openDrawer();
    },
    showOccurrenceTime() {
      console.log("时间", this.basicForm.occurrenceDate);
      return this.basicForm.occurrenceDateFlag == 0
        ? "不明确"
        : moment(this.basicForm.occurrenceDate).format("YYYY-MM-DD HH:mm:ss");
    },
    showExpectTime() {
      console.log("期望处理时间", this.basicForm);
      const text = this.handleTypeOptions.find(
        (x) => x.value == this.basicForm.expectHandleType
      )?.label;
      return (
        moment(this.basicForm.expectHandleDate).format("YYYY-MM-DD HH:mm:ss") ||
        text
      );
    },
    getOrderTypeNames() {
      const arr = [
        this.basicForm.orderTypeParentName,
        this.basicForm.orderTypeName,
        this.basicForm.threeOrderTypeName,
      ];
      return arr.filter((x) => x).join(" — ");
    },
    clickImg(index) {
      // //点击预览图片
      // this.showImgPreview = true;
      // this.previewImgUrl = o;
      this.$refs.picPreview.open(index, this.picList);
    },
    //关闭图片预览弹框
    closePreviewDialog() {
      this.showImgPreview = false;
    },
    resetPreview(done) {
      this.zoomLevel = 100;
      done();
    },
    handleImageWheel(event) {
      event.preventDefault();
      const delta = Math.max(
        -1,
        Math.min(1, event.deltaY || -event.wheelDelta || event.detail)
      );
      const zoomStep = 10;
      if (delta > 0) {
        // 放大图片
        this.zoomLevel += zoomStep;
      } else {
        // 缩小图片
        this.zoomLevel -= zoomStep;
      }
      // 根据缩放级别调整图片大小
      this.imageStyle = {
        ...this.imageStyle,
        transform: `scale(${this.zoomLevel / 100})`,
      };
    },
    getPileName(code) {
      const obj = this.pileOptions?.find((x) => x.pileCode === code);
      return (obj?.pileCode || "") + "-" + (obj?.pileName || "");
    },
    getDetailRegion(region, addr) {
      let regionStr = "";
      if (Array.isArray(region)) {
        const [region1, region2, region3] = region;
        let obj1 = regionData.find((y) => y.value === region1);
        let obj2 = obj1?.children?.find((y) => y.value === region2);
        let obj3 = obj2?.children?.find((y) => y.value === region3);
        regionStr =
          (obj1?.label || "") +
          "-" +
          (obj2?.label || "") +
          "-" +
          (obj3?.label || "") +
          "-";
        console.log("regionStr", regionStr);
      }
      return regionStr + (addr || "");
    },
    getDictName(option, val, valueName = "dictValue", labelName = "dictLabel") {
      return option?.find((item) => item[valueName] == val)?.[labelName];
    },
    orderTypeParentChange() {
      this.basicForm.orderTypeName = undefined;
    },
    validateOrderTypeParentName(rule, value, callback) {
      if (
        !this.basicForm.orderTypeParentName ||
        !this.basicForm.orderTypeName
      ) {
        callback(new Error("工单类型不能为空"));
      }
      callback();
    },
    validateRegion(rule, value, callback) {
      if (value && value.length < 3) {
        callback(new Error("省市区域不能为空"));
      }
      callback();
    },
    closeVisible() {
      this.$router.push({
        path: "order/operationWorkOrder/list",
      });
    },
    addItem() {
      this.chargingPileForm.pileGunList.push({
        count: 0,
        gunList: [
          {
            gunDesc: "",
          },
        ],
      });
    },
    removeItem(index) {
      this.chargingPileForm.pileGunList.splice(index, 1);
    },
    getBusinessTypeOptions() {
      this.getDicts("business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    async getStationList() {
      stationList().then((res) => {
        this.stationOptions = res?.data;
      });
    },
    getCheckedGunMap(gunList) {
      let obj = {};
      gunList.forEach((item) => {
        obj[item.gunNo] = item;
      });
      return obj;
    },
    getPileList(stationId, flag) {
      if (!flag) {
        this.chargingPileForm = {
          pileGunList: [
            {
              count: 0,
              gunList: [
                {
                  gunDesc: "",
                },
              ],
            },
          ],
        };
      }
      const station = this.stationOptions.find(
        (item) => item.stationId === stationId
      );
      this.basicForm.region = station
        ? [station.province, station.city, station.county]
        : [this.basicForm.province, this.basicForm.city, this.basicForm.county];
      this.basicForm.address = station
        ? station.stationAddress
        : this.basicForm.address;
      this.basicForm.stationName = station
        ? station.stationName
        : this.basicForm.address?.stationName;
      pileList({ stationId: stationId }).then((response) => {
        // pileList({stationId: "369539647983980544"}).then((response) => {
        this.pileOptions = response?.data;
      });
    },
    queryRunningStatus(item) {
      this.loading = true;
      pileGunList({ pileNo: item.pileCode, pileName: item.pileName }).then(
        (response) => {
          // pileGunList({ pileNo: "***********", pileName: "12号桩"}).then((response) => {
          this.loading = false;
          item.gunList = [...response?.data];
          item.count = item.gunList.length;
        }
      );
    },
    getPileInfo(value, item) {
      item.pileCode = value;
      item.pileName = this.pileOptions.find(
        (x) => x.pileCode === value
      ).pileName;
      this.queryRunningStatus(item);
    },
    getBusinessTypeName(e) {
      this.basicForm.businessTypeName = this.businessTypeOptions.find(
        (item) => item.dictValue === e
      ).dictLabel;
    },
    submit() {
      const vm = this;
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          let flag = null;
          for (const item of vm.chargingPileForm.pileGunList) {
            for (const o of item.gunList) {
              if (o.gunDesc?.length > 500) {
                flag = true;
                break;
              }
            }
          }
          if (flag) {
            this.$message.error("充电枪问题描述长度不能超过500");
            return;
          }
          this.$confirm("是否确认提交", "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "info",
          }).then(function() {
            const form = { ...vm.basicForm };
            if (form.occurrenceDate) {
              form.occurrenceDateFlag = "1";
            } else {
              form.occurrenceDateFlag = "0";
            }
            if (Array.isArray(form.region)) {
              form.province = form.region[0];
              form.city = form.region[1];
              form.county = form.region[2];
              form.provinceAddress = regionData.find(
                (item) => item.value === form.province
              )?.label;
              form.cityAddress = regionData.find(
                (item) => item.value === form.city
              )?.label;
              form.countyAddress = regionData.find(
                (item) => item.value === form.county
              )?.label;
              delete form.region;
            }
            const pileGunList = [];
            vm.chargingPileForm.pileGunList.forEach((item) => {
              item.gunList.forEach((object) => {
                if (object.checked) {
                  const itemCopy = { ...item };
                  delete itemCopy.gunList;
                  pileGunList.push({ ...object, ...itemCopy });
                }
              });
            });
            form.pileGunList = pileGunList;
            form.docList = vm.$refs.upload.fileList;
            form.orderId = vm.orderId;
            saveOrder(form).then((res) => {
              if (vm.orderId) {
                vm.$message.success("编辑工单成功");
              } else {
                vm.$message.success("创建工单成功");
              }
              vm.$router.push({
                path: "order/operationWorkOrder/list",
              });
            });
          });
        }
      });
    },
    changeOccurrenceDateFlag() {
      this.basicForm.occurrenceDate = "";
    },
    changeOccurrenceDate() {
      this.$set(
        this.basicForm,
        "occurrenceDate",
        this.basicForm.occurrenceDate
      );
      if (this.basicForm.occurrenceDate) {
        this.$set(this.basicForm, "occurrenceDateFlag", "");
      }
    },
    changeExpectHandleDateType() {
      this.basicForm.expectHandleDate = "";
    },
    changeExpectHandleDate() {
      this.$set(
        this.basicForm,
        "expectHandleDate",
        this.basicForm.expectHandleDate
      );
      if (this.basicForm.expectHandleDate) {
        this.$set(this.basicForm, "expectHandleType", "");
      }
    },
  },
  mounted() {
    this.basicForm.occurrenceDate = moment(new Date()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.basicForm.expectHandleType = "4";
    this.basicForm.urgencyLevel = "0";
    this.getBusinessTypeOptions();
  },
  watch: {
    $route: {
      handler(newVal) {
        if (newVal.query.orderId) {
          this.orderId = newVal.query.orderId;
          detail({ orderId: this.orderId }).then(async (res) => {
            this.basicForm = { ...res.data };
            // if (this.basicForm.expectHandleType) {
            //   this.basicForm.expectHandleDate = "";
            // }
            if (this.basicForm.occurrenceDateFlag == 0) {
              this.basicForm.occurrenceDate = "";
            }
            this.getPileList(this.basicForm.stationId, true);
            if (res.data.pileGunList.length > 0) {
              this.chargingPileForm = { ...res.data };
              this.chargingPileForm.pileGunList.forEach((item) => {
                const arr = [...item.gunList];
                this.loading = true;
                pileGunList({
                  pileNo: item.pileCode,
                  pileName: item.pileName,
                }).then((response) => {
                  // pileGunList({ pileNo: "***********", pileName: "12号桩"}).then((response) => {
                  this.loading = false;
                  item.gunList = [...response?.data];
                  item.count = item.gunList.length;
                  const gunMap = this.getCheckedGunMap(arr);
                  const list = [];
                  item.gunList.forEach((o) => {
                    if (gunMap.hasOwnProperty(o.gunNo)) {
                      o["checked"] = true;
                      o["gunDesc"] = gunMap[o.gunNo]["gunDesc"];
                    }
                    list.push(o);
                  });
                  item.gunList = [...list];
                });
              });
            }
            // this.$refs.upload.fileList = res.data.createDocList;
            this.picList = res.data.createDocList;
            console.log("图片", res.data.createDocList);
          });
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getStationList();
  },
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: center;
}
.order-desc {
  max-height: 300px;
  overflow: auto;
  border: 1px solid #ccc;
  padding: 10px;
  white-space: pre-line;
}
</style>
