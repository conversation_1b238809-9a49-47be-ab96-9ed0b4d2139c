<template>
  <div class="app-container">
    <h3>{{ type === "add" ? "创建" : "编辑" }}工单</h3>
    <el-form
      :model="basicForm"
      ref="basicForm"
      label-width="110px"
      :rules="rules"
    >
      <!-- 基本信息-S -->
      <el-card id="info">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>基本信息</span>
        </div>
        <el-form-item label="工单类型" prop="orderTypeParentId">
          <el-row>
            <el-col :span="3.3">
              <el-select v-model="basicForm.orderTypeParentId" disabled>
                <el-option
                  v-for="item in bindOrderTypeOptions"
                  :label="item.typeName"
                  :value="item.id"
                  :key="item.id"
                />
              </el-select>
            </el-col>
            <el-col :span="3.3">
              <el-select
                v-model="basicForm.orderTypeId"
                @change="
                  (val) => {
                    orderTypeParentChange(val, 3);
                  }
                "
                clearable
              >
                <el-option
                  v-for="item in orderTypeOptions"
                  :label="item.typeName"
                  :value="item.id"
                  :key="item.id"
                />
              </el-select>
            </el-col>
            <el-col :span="3.3">
              <el-select v-model="basicForm.threeOrderTypeId" clearable>
                <el-option
                  v-for="item in threeOrderTypeOptions"
                  :label="item.typeName"
                  :value="item.id"
                  :key="item.id"
                />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="紧急程度" prop="urgencyLevel">
          <el-radio-group v-model="basicForm.urgencyLevel">
            <el-radio
              v-for="item in urgencyLevelOptions"
              :key="item.dictValue"
              :label="item.dictValue"
              >{{ item.dictLabel }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="问题描述" prop="orderDesc">
          <Editor v-model="basicForm.orderDesc" />
        </el-form-item>
        <el-form-item
          label="设备编号"
          prop="deviceNo"
          v-if="showType === 'charge'"
        >
          <el-row>
            <el-col :span="10">
              <el-input v-model="basicForm.deviceNo" disabled> </el-input>
            </el-col>
            <el-col :span="10" style="margin-left:10px">
              <el-button @click="handleSelectDevice('radio')">选择</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          label="设备类型"
          prop="deviceTypeName"
          v-if="showType === 'charge'"
        >
          <el-input v-model="basicForm.deviceTypeName" disabled> </el-input>
          <!-- <span>{{ basicForm.deviceTypeName }}</span> -->
        </el-form-item>
        <el-form-item
          label="设备型号"
          prop="deviceModelName"
          v-if="showType === 'charge'"
        >
          <el-input v-model="basicForm.deviceModelName" disabled> </el-input>
          <!-- <span>{{ basicForm.deviceModelName }}</span> -->
        </el-form-item>
        <el-form-item label="站点名称" prop="stationName">
          <el-row v-if="showType !== 'charge'">
            <el-col :span="10">
              <el-input v-model="basicForm.stationName" disabled> </el-input>
            </el-col>
            <el-col :span="10" style="margin-left:10px">
              <el-button @click="handleSelectStation">选择</el-button>
            </el-col>
          </el-row>
          <!-- <span v-else>{{ basicForm.stationName }}</span> -->
          <el-input v-model="basicForm.stationName" disabled v-else> </el-input>
        </el-form-item>
        <el-form-item label="所在区域" prop="region">
          <CustomCascader
            v-model="basicForm.region"
            placeholder="请选择区域"
            :options="areaOptions"
            clearable
            filterable
            :props="{ checkStrictly: true }"
            disabled
            style="width:100%"
          ></CustomCascader>
          <!-- <el-input v-model="basicForm.region" disabled v-else> </el-input> -->
          <!-- <span v-else>{{ basicForm.region }}</span> -->
        </el-form-item>
        <el-form-item label="详细地址">
          <el-input v-model="basicForm.address" type="input" disabled />
          <!-- <span>{{ basicForm.address }}</span> -->
        </el-form-item>
        <el-form-item label="能投区域">
          <el-input v-model="basicForm.regionName" type="input" disabled />
        </el-form-item>
        <el-form-item label="运维等级">
          <el-input v-model="basicForm.gradeName" type="input" disabled />
        </el-form-item>
        <el-form-item label="处理人" prop="handleUserName">
          <el-row>
            <el-col :span="10">
              <el-input v-model="basicForm.handleUserName" disabled> </el-input>
            </el-col>
            <el-col :span="10" style="margin-left:10px">
              <el-button @click="handleSelectUser">选择</el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-card>
      <!-- 基本信息-E -->
      <!-- 设备信息列表-S -->
      <el-card v-if="showType === 'debug'" id="device">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>设备信息</span>
        </div>
        <GridTable
          :columns="columns"
          :tableData="tableData"
          :currentPage.sync="searchForm.pageNum"
          :pageSize.sync="searchForm.pageSize"
          :total.sync="tableTotal"
          @changePage="getDeviceList"
          :loading="loading"
          :tableId="tableId"
          row-id="orderId"
        >
          <template slot="xToolbarBtn" slot-scope="{}">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-plus"
              @click.stop="handleSelectDevice('checkbox')"
            >
              添加
            </el-button>
          </template>
          <template slot="operation" slot-scope="{ row }">
            <el-button type="text" size="large" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </GridTable>
      </el-card>
      <!-- 设备信息列表-E -->

      <!-- 选择检查组-S -->
      <el-card v-if="showCheckGroup" id="checkGroup">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>选择检查组</span>
        </div>
        <el-form-item label="" prop="checkGroupIds">
          <el-transfer
            v-model="basicForm.checkGroupIds"
            :data="allData"
            :titles="['未配置检查组', '已配置检查组']"
            :props="defaultProps"
            target-order="push"
          ></el-transfer>
        </el-form-item>
      </el-card>
      <!-- 选择检查组-E -->
      <!-- 补充信息-S -->
      <el-card v-if="showType === 'debug'" id="more">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>补充信息</span>
        </div>
        <el-form-item label="安装方式" prop="installMethod">
          <el-select v-model="basicForm.installMethod" clearable filterable>
            <el-option
              v-for="item in installMethodOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主要用户对象" prop="mainUser">
          <el-select v-model="basicForm.mainUser" clearable filterable>
            <el-option
              v-for="item in mainUserOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接入平台" prop="platform">
          <el-select v-model="basicForm.platform" clearable filterable>
            <el-option
              v-for="item in orderPlatformOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型" prop="projectType">
          <el-select v-model="basicForm.projectType" clearable filterable>
            <el-option
              v-for="item in projectTypeOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目编号" prop="projectCode">
          <el-input v-model="basicForm.projectCode" clearable> </el-input>
        </el-form-item>
        <el-form-item label="安装单位" prop="installUnit">
          <el-select v-model="basicForm.installUnit" clearable filterable>
            <el-option
              v-for="item in installUnitOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="启用方式" prop="enableMethod">
          <el-select v-model="basicForm.enableMethod" clearable filterable>
            <el-option
              v-for="item in enableMethodOptions"
              :label="item.dictLabel"
              :value="item.dictValue"
              :key="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="销售人员" prop="salesmanName">
          <el-input v-model="basicForm.salesmanName" clearable> </el-input>
        </el-form-item>
      </el-card>
      <!-- 补充信息-E -->
      <!-- 其他信息-S -->
      <el-card id="other">
        <div slot="header" class="card-title-wrap">
          <div class="card-title-line"></div>
          <span>其他信息</span>
        </div>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="basicForm.customerName" maxlength="1000" />
        </el-form-item>
        <el-form-item label="联系人" prop="contact">
          <el-input v-model="basicForm.contact" maxlength="50" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactTel">
          <el-input v-model="basicForm.contactTel" maxlength="50" />
        </el-form-item>
        <el-form-item
          label="车辆品牌"
          prop="carBrand"
          v-if="this.showType === 'charge'"
        >
          <el-input v-model="basicForm.carBrand" maxlength="100" />
        </el-form-item>
        <el-form-item
          label="车辆型号"
          prop="carModel"
          v-if="this.showType === 'charge'"
        >
          <el-input v-model="basicForm.carModel" type="input" maxlength="100" />
        </el-form-item>
        <el-form-item
          label="预约时间"
          prop="appointmentTime"
          v-if="this.showType === 'charge' || this.showType === 'debug'"
        >
          <el-date-picker
            v-model="basicForm.appointmentTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item
          label="保内/保外"
          prop="shelfLife"
          v-if="this.showType === 'charge'"
        >
          <el-radio-group v-model="basicForm.shelfLife">
            <el-radio label="0">保内</el-radio>
            <el-radio label="1">保外</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item
          label="处理半径"
          prop="handleRadius"
          v-if="this.showType !== 'common'"
        >
          <el-input v-model="basicForm.handleRadius" type="number">
            <template slot="append">m</template>
          </el-input>
        </el-form-item> -->
      </el-card>
      <!-- 其他信息-E -->
    </el-form>
    <StationDialog
      ref="stationDialog"
      @confirm="getSelectStation"
    ></StationDialog>
    <HandleUserDialog
      ref="userDialog"
      @confirm="getSelectedUser"
    ></HandleUserDialog>
    <HandleDeviceDialog
      ref="deviceDialog"
      @confirm="getSelectedDevice"
    ></HandleDeviceDialog>
    <BatchDeviceDialog
      ref="batchDeviceDialog"
      :orderNo="orderNo"
      @confirm="getBatchSelectedDevice"
    ></BatchDeviceDialog>
    <div class="dialog-footer">
      <el-button @click.stop="goBack" size="medium" :loading="btnLoading"
        >取 消</el-button
      >
      <el-button
        @click="submit"
        type="primary"
        size="medium"
        :loading="btnLoading"
        >提交</el-button
      >
    </div>
    <!-- <ul class="anchor">
      <li v-for="item in anchorList" :key="item.id" @click="achor(item)">
        {{ item.name }}
      </li>
    </ul> -->
  </div>
</template>
<script>
import moment from "moment";
import { queryAllCheckList } from "@/api/operationMaintenanceManage/configManage/planConfig.js";
import Editor from "@/components/Editor/index.vue";
import { regionData } from "element-china-area-data";
import HandleUserDialog from "@/components/selectDialog/handleUserDialog/index.vue";
import StationDialog from "@/components/selectDialog/handleStationDialog/index.vue";
import { validMobile } from "@/utils/validate";
import HandleDeviceDialog from "@/components/selectDialog/handleDeviceDialog/index.vue";
import BatchDeviceDialog from "@/views/operationMaintenanceManage/operationWorkOrder/components/batchDeviceDialog.vue";
// import {
//   MAINTENANCE_ORDER_CLICK_EDIT,
//   MAINTENANCE_ORDER_CREATE,
// } from "@/utils/track/track-event-constants";
import {
  childrenList,
  queryBindOrderTypeList,
  saveDevice,
  querySelectedDeviceList,
  saveDeviceList,
  deleteDevice,
  saveOrder,
  queryDetail,
  deleteAllDevice,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import GridTable from "@/components/GridTable/index.vue";

export default {
  name: "operationWorkOrderNewAdd",
  components: {
    Editor,
    StationDialog,
    HandleUserDialog,
    HandleDeviceDialog,
    BatchDeviceDialog,
    GridTable,
  },
  data() {
    return {
      anchorList: [
        { id: 1, name: "基本信息", key: "info" },
        { id: 2, name: "设备信息", key: "device" },
        { id: 3, name: "检查组", key: "checkGroup" },
        { id: 3, name: "补充信息", key: "more" },
        { id: 3, name: "其他信息", key: "other" },
      ],
      btnLoading: false,
      //表格参数-S
      loading: false,
      tableId: "deviceSelectConfigList",
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      columns: [
        {
          field: "deviceNo",
          title: "设备编码",
        },
        {
          field: "deviceName",
          title: "设备名称",
        },
        {
          field: "deviceTypeName",
          title: "设备类型",
          formatter: ({ cellValue }) => {
            return (
              this.subTypeOptions?.find((item) => item.dictValue == cellValue)
                ?.dictLabel || ""
            );
          },
        },
        {
          field: "deviceStationName",
          title: "所属站点",
        },
        {
          field: "deviceRegion",
          title: "省市区",
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      //表格参数-E

      allData: [],
      defaultProps: {
        key: "checkGroupId",
        label: "groupName",
      },
      orderNo: "",
      orderId: "",
      areaOptions: regionData,
      type: "add",
      basicForm: {
        orderTypeParentId: "",
        orderTypeId: "",
        threeOrderTypeId: "",
        urgencyLevel: "1",
        orderDesc: "",
        stationName: "",
        region: undefined,
        address: "",
        regionName: "",
        gradeName: "",
        handleUserName: "",
        //检查组
        checkGroupIds: [],
        //选择设备
        deviceNo: "",
        deviceTypeName: "",
        deviceModelName: "",
        //其他信息
        customerName: "",
        contact: "",
        contactTel: "",
        carBrand: "",
        carModel: "",
        appointmentTime: "",
        shelfLife: "0",
        // handleRadius: 2000,
        //补充信息
        installMethod: "",
        mainUser: "",
        platform: "",
        projectType: "",
        projectCode: "",
        installUnit: "",
        enableMethod: "",
        salesmanName: "",
      },
      bindOrderTypeOptions: [],
      orderTypeOptions: [],
      threeOrderTypeOptions: [],
      urgencyLevelOptions: [],
      typeDict: [
        { typeName: "充电桩故障工单", typeVal: "charge" },
        { typeName: "通用工单", typeVal: "common" },
        { typeName: "巡检工单", typeVal: "inspection" },
        { typeName: "保养工单", typeVal: "maintain" },
        { typeName: "调试工单", typeVal: "debug" },
        { typeName: "工勘工单", typeVal: "engineer" },
      ],
      showType: "common",
      subTypeOptions: [],
      installMethodOptions: [],
      mainUserOptions: [],
      orderPlatformOptions: [],
      projectTypeOptions: [],
      installUnitOptions: [],
      enableMethodOptions: [],
      originThreeOrderId: "",
      originOrderId: "",
      originParentOrderId: "",
    };
  },
  computed: {
    //展示检查组（巡检、保养、调试）
    showCheckGroup() {
      return ["inspection", "maintain", "debug"].includes(this.showType);
    },
    rules() {
      return {
        orderTypeParentId: [
          { required: true, message: "请选择工单类型", trigger: "change" },
        ],
        urgencyLevel: [
          { required: true, message: "请选择紧急程度", trigger: "change" },
        ],
        orderDesc: [
          { required: true, message: "请输入问题描述", trigger: "change" },
        ],
        stationName: [
          {
            required: !["charge", "engineer"].includes(this.showType),
            message: "请选择站点名称",
            trigger: "change",
          },
        ],
        handleUserName: [
          { required: true, message: "请选择处理人", trigger: "change" },
        ],
        //检查组
        checkGroupIds: [
          {
            required: this.showCheckGroup,
            message: "请选择检查组",
            trigger: "change",
          },
        ],
        //其他信息
        contactTel: [
          {
            validator: (rule, value, callback) => {
              if (!value || validMobile(value)) {
                callback();
              } else {
                callback(new Error("请输入正确的电话号码"));
              }
            },
          },
        ],
        // handleRadius: [{ validator: this.customValidation, trigger: "change" }],
        //补充信息（调试工单）
        installMethod: [
          {
            required: this.showType === "debug",
            message: "请选择安装方式",
            trigger: "change",
          },
        ],
        mainUser: [
          {
            required: this.showType === "debug",
            message: "请选择主要用户对象",
            trigger: "change",
          },
        ],
        platform: [
          {
            required: this.showType === "debug",
            message: "请选择接入平台",
            trigger: "change",
          },
        ],
        projectType: [
          {
            required: this.showType === "debug",
            message: "请选择项目类型",
            trigger: "change",
          },
        ],
        deviceNo: [
          {
            required: this.showType === "charge",
            message: "请选择设备编号",
            trigger: "change",
          },
        ],
      };
    },
  },
  async created() {
    this.getDicts("urgency_level").then((response) => {
      this.urgencyLevelOptions = response?.data;
    });
    const { orderNo, type, orderTypeParentName, ...rest } = this.$route.query;
    this.orderNo = orderNo;
    this.type = type;
    this.basicForm = { ...this.basicForm, ...rest };
    //根据5种工单类型展示不同表单 默认通用工单
    this.showType =
      this.typeDict.find((x) => x.typeName == orderTypeParentName)?.typeVal ||
      "common";
    this.getParentOrderTypeOptions();
    await this.orderTypeParentChange(this.basicForm.orderTypeParentId, 2);
    console.log(this.showType);
    if (type === "edit") {
      this.orderId = this.$route.query.orderId;
      this.getDetail();
    }
  },
  mounted() {
    this.getDicts("install_method").then((response) => {
      this.installMethodOptions = response?.data;
    });
    this.getDicts("main_user").then((response) => {
      this.mainUserOptions = response?.data;
    });
    this.getDicts("order_platform").then((response) => {
      this.orderPlatformOptions = response?.data;
    });
    this.getDicts("project_type").then((response) => {
      this.projectTypeOptions = response?.data;
    });
    this.getDicts("install_unit").then((response) => {
      this.installUnitOptions = response?.data;
    });
    this.getDicts("enable_method").then((response) => {
      this.enableMethodOptions = response?.data;
    });
    Promise.all([
      this.getDicts("cm_sub_type").then((response) => {
        this.subTypeOptions = response?.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getDeviceList();
        });
      }, 500);
    });
    this.getAllGroupList();
  },
  watch: {},

  methods: {
    // achor(row) {
    //   this.activeSet = row.id;
    //   // row.key   为对应的id名
    //   document.querySelector(`#${row.key}`).scrollIntoView({
    //     behavior: "smooth",
    //     // 定义动画过渡效果， "auto"或 "smooth" 之一。默认为 "auto"
    //     block: "start",
    //     // 定义垂直方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "start"
    //     inline: "nearest",
    //     // 定义水平方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "nearest"
    //   });
    // },
    async getDetail() {
      queryDetail({ orderId: this.orderId }).then(async (res) => {
        if (res?.code === "10000") {
          const {
            orderTypeName = "",
            orderTypeId = "",
            orderTypeParentId = "",
            orderTypeParentName = "",
            threeOrderTypeId = "",
            threeOrderTypeName = "",
            handleUser,
            handleGroup,
            handleGroupName,
            handleUserName,
          } = res.data;
          this.basicForm = { ...res.data };
          this.basicForm.region = [
            res.data.province,
            res.data.city,
            res.data.county,
          ];
          this.$set(
            this.basicForm,
            "handleUserName",
            handleUserName || handleGroupName
          );
          this.$set(this.basicForm, "handleUser", handleUser || handleGroup);
          this.$set(
            this.basicForm,
            "userType",
            handleUser ? "person" : "group"
          );
          if (orderTypeId) {
            const res1 = await childrenList({ id: orderTypeId });
            if (res1) {
              this.threeOrderTypeOptions = res1.data;
            }
          }
          if (
            !this.bindOrderTypeOptions?.some((x) => x.id == orderTypeParentId)
          ) {
            this.originParentOrderId = orderTypeParentId;
            this.basicForm.orderTypeParentId = orderTypeParentName;
          }
          if (!this.orderTypeOptions?.some((x) => x.id == orderTypeId)) {
            this.originOrderId = orderTypeId;
            this.basicForm.orderTypeId = orderTypeName;
          }
          if (
            !this.threeOrderTypeOptions?.some((x) => x.id == threeOrderTypeId)
          ) {
            this.originThreeOrderId = threeOrderTypeId;
            this.basicForm.threeOrderTypeId = threeOrderTypeName;
          }
        }
      });
    },
    submit() {
      console.log(this.basicForm, "----提交");
      this.$refs.basicForm.validate((valid) => {
        if (!valid) {
          return;
        }
        const {
          userType = "",
          handleUser,
          handleUserName,
          handleGroup,
          handleGroupName,
          ...rest
        } = this.basicForm;
        let params = {
          ...rest,
          orderNo: this.orderNo,
        };
        if (
          params.threeOrderTypeId &&
          !this.threeOrderTypeOptions?.some(
            (x) => x.id == params.threeOrderTypeId
          )
        ) {
          params.threeOrderTypeId = this.originThreeOrderId;
        }
        if (
          params.orderTypeId &&
          !this.orderTypeOptions?.some((x) => x.id == params.orderTypeId)
        ) {
          params.orderTypeId = this.originOrderId;
        }
        if (
          params.orderTypeParentId &&
          !this.bindOrderTypeOptions?.some(
            (x) => x.id == params.orderTypeParentId
          )
        ) {
          params.orderTypeParentId = this.originParentOrderId;
        }
        if (userType === "person") {
          params["handleUser"] = handleUser;
          params["handleUserName"] = handleUserName;
        } else {
          params["handleGroup"] = handleUser;
        }
        this.btnLoading = true;
        saveOrder(params)
          .then((res) => {
            this.btnLoading = false;
            if (res.code === "10000") {
              this.$message.success("提交成功");
              this.$store.dispatch("tagsView/delView", this.$route);
              this.goBack();
            }
          })
          .catch(() => {
            this.btnLoading = false;
          });
      });
    },
    goBack() {
      this.$router.push({
        path: "/operationMaintenanceManage/operationWorkOrder/list",
      });
    },
    //删除设备
    handleDelete(row) {
      this.$confirm("确定要删除该设备吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          id: row.id,
        };
        deleteDevice(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getDeviceList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    getBatchSelectedDevice(list) {
      console.log(list, "选中的设备");
      const params = { orderNo: this.orderNo, deviceList: list };
      saveDeviceList(params).then((res) => {
        if (res?.code === "10000") {
          this.$refs.batchDeviceDialog.closeDialog();
          this.$message.success("保存设备成功");
          this.getDeviceList();
        }
      });
    },
    //获取已选择的设备列表（调试工单）
    getDeviceList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        orderNo: this.orderNo,
      };
      // this.finallySearch = args;
      querySelectedDeviceList(params)
        .then((res) => {
          this.loading = false;
          this.tableData = res?.data;
          this.tableTotal = res?.total;
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    customValidation(rule, value, callback) {
      console.log(/^[1-9]\d*$/.test(value), value);
      if (this.showType !== "common" && !value) {
        callback(new Error("处理半径不能为空！"));
      } else if (
        value &&
        (value < 0 || value > 5000 || !/^[1-9]\d*$/.test(value))
      ) {
        callback(new Error("0≤处理半径≤5000，且为整数"));
      } else {
        callback();
      }
    },
    async getAllGroupList() {
      const res = await queryAllCheckList({
        pageNum: 1,
        pageSize: 9999,
        status: 0,
        businessType: this.basicForm.businessType,
        groupType: this.basicForm.orderTypeParentId,
      });
      if (res?.code === "10000") {
        this.allData = res.data;
      }
    },

    handleSelectDevice(type = "radio") {
      if (type === "radio") {
        this.$refs.deviceDialog.open({ ...this.basicForm });
      } else {
        if (!this.basicForm.stationName) {
          this.$message.warning("请先选择站点！");
          return;
        }
        this.$refs.batchDeviceDialog.open(this.basicForm.stationCode);
      }
    },
    getSelectedDevice(row) {
      console.log(row);
      const params = {
        orderNo: this.orderNo,
        deviceList: [
          { ...row, modelName: row.modelName || row.deviceModelName },
        ],
      };
      saveDevice(params).then((res) => {
        if (res?.code === "10000") {
          this.$refs.deviceDialog.closeDialog();
          this.$message.success("保存设备成功");
          this.basicForm.stationName = row.stationName;
          this.basicForm.region = [
            row.province.slice(0, 2),
            row.city.slice(0, 4),
            row.county,
          ];
          this.basicForm.address = row.stationAddress || row.address;
          this.basicForm.deviceNo = row.deviceNo;
          this.basicForm.deviceTypeName =
            this.subTypeOptions?.find((x) => x.dictValue === row.subType)
              ?.dictLabel || row.deviceTypeName;
          this.basicForm.deviceModelName = row.modelName || row.deviceModelName;
          this.basicForm.regionName = row.regionName;
          this.basicForm.gradeName = row.gradeName;
          this.basicForm.stationCode = row.stationNo || row.stationCode;
          this.basicForm.stationId = undefined; //编辑时详情接口会返回stationId，选择设备后stationId没有更新（设备接口没这个字段），因此置为undefined
        }
      });
    },
    getSelectedUser(row) {
      const { userType, selectedUser } = row;
      this.basicForm.handleUserName =
        userType === "person" ? selectedUser.nickName : selectedUser.groupName;
      this.basicForm["userType"] = userType;
      this.basicForm.handleUser =
        userType === "person" ? selectedUser.userId : selectedUser.groupId;
    },
    handleSelectUser() {
      console.log(this.basicForm);
      const { userType, handleUser, handleUserName } = this.basicForm;
      const params = {
        userType: userType,
        selectedUser:
          userType === "person"
            ? { nickName: handleUserName, userId: handleUser }
            : { groupName: handleUserName, groupId: handleUser },
      };
      this.$refs.userDialog.open(params);
    },
    getSelectStation(row) {
      if (
        this.showType === "debug" &&
        this.basicForm.stationCode &&
        this.basicForm.stationCode !== row.stationCode
      ) {
        deleteAllDevice({ orderNo: this.orderNo }).then((res) => {
          if (res?.code === "10000") {
            this.getDeviceList();
          }
        });
      }
      this.basicForm = {
        ...this.basicForm,
        ...row,
        address: row.stationAddress || row.address,
        regionName: row.regionName,
        gradeName: row.gradeName,
      };
      this.basicForm.region = [row.province, row.city, row.county];
    },
    handleSelectStation() {
      this.$refs.stationDialog.open({ ...this.basicForm });
    },
    async orderTypeParentChange(val, level) {
      if (level == 2) {
        const res = await childrenList({ id: val });
        this.orderTypeOptions = res?.data;
      } else {
        this.basicForm.threeOrderTypeId = "";
        const res1 = await childrenList({ id: val });
        this.threeOrderTypeOptions = res1?.data;
      }
    },
    getParentOrderTypeOptions() {
      queryBindOrderTypeList({
        businessType: this.basicForm.businessType,
      }).then((res) => {
        if (res?.code === "10000") {
          this.bindOrderTypeOptions = res.data;
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

/deep/ .el-card {
  margin-bottom: 10px;
}

/deep/ .el-button--medium {
  padding: 12px 24px;
  font-size: 18px;
  border-radius: 4px;
}
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;
  .el-transfer-panel {
    flex: 1;
  }
  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}
/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}
.anchor {
  position: fixed;
  right: 0;
  top: 40%;
  z-index: 99;
  list-style-type: none;
  li {
    padding: 20px 40px;
    background-color: #029c7c;
    color: #fff;
    border-radius: 10px 0 0 10px;
    margin-bottom: 2px;
    cursor: pointer;
  }
}
</style>
