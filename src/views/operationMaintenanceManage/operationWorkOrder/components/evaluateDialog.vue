<template>
  <el-dialog
    title="评价工单"
    :visible.sync="visible"
    @close="closeDialog"
    :close-on-click-modal="false"
    width="600px"
    append-to-body
    destroy-on-close
    v-loading="loading"
  >
    <el-form
      :model="formValue"
      ref="formRef"
      label-width="400px"
      label-position="left"
    >
      <el-form-item
        :prop="`score${index}`"
        :rules="[{ required: true, message: `请填写分值` }]"
        v-for="(item, index) in evaluateList"
        :key="index"
      >
        <template slot="label">
          <span>{{ item.commentName }}</span>
          <el-tooltip
            v-if="item.commentDesc"
            class="item"
            effect="light"
            :content="item.commentDesc"
            placement="top-start"
          >
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <el-input-number
          v-model="formValue[`score${index}`]"
          :min="0"
          :max="item.score"
          :precision="0"
          placeholder="请输入"
          @change="handleChange()"
        />
        分
      </el-form-item>
    </el-form>
    <div class="total-score">
      <span>总分：</span>
      <span>{{ allScore }}</span>
      <span>分</span>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button :loading="submitLoading" @click.stop="submit" type="primary"
        >确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  queryOperationWorkOrderEvaluate,
  saveOperationWorkOrderEvaluate,
} from "@/api/operationMaintenanceManage/evaluate/index";
export default {
  data() {
    return {
      visible: false,
      loading: false,
      submitLoading: false,
      row: {},
      formValue: {},
      evaluateList: [],
      allScore: 0,
    };
  },
  methods: {
    handleChange() {
      let allScore = 0;
      Object.values(this.formValue).forEach((i = 0) => {
        allScore = this.NP.plus(allScore, i);
      });
      this.allScore = allScore;
    },
    closeDialog() {
      this.visible = false;
      this.formValue = {};
      this.allScore = 0;
    },
    submit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.$confirm("是否确定提交该评价", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            const data = await saveOperationWorkOrderEvaluate({
              orderId: this.row.orderId,
              orderComments: this.evaluateList.map((i, k) => ({
                commentName: i.commentName,
                commentDesc: i.commentDesc,
                score: i.score,
                scoreReal: this.formValue[`score${k}`],
              })),
            });
            if (data.code === "10000") {
              this.$message.success("提交成功");
              this.closeDialog();
              this.$emit("afterSubmit");
            } else {
              this.$message.error(data.message);
            }
          });
        }
      });
    },
    async open(rowdata) {
      this.row = rowdata;
      this.visible = true;

      this.loading = true;
      const data = await queryOperationWorkOrderEvaluate({
        orderId: rowdata.orderId,
      });
      this.loading = false;
      if (data.code === "10000") {
        this.evaluateList = data?.data?.orderComments || [];
      } else {
        this.$message.error(data.message);
      }
    },
  },
};
</script>

<style scoped lang="less">
.total-score {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  span:nth-child(2) {
    font-size: 20px;
    color: red;
    margin: 0 6px;
  }
}
</style>
