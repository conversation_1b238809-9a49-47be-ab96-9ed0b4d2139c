<template>
  <div class="app-container">
    <h3>处理工单</h3>

    <el-card>
      <el-form
        :model="basicForm"
        ref="basicForm"
        label-width="130px"
        :rules="rules"
        v-if="!isEngineer"
      >
        <el-form-item label="作业方式">
          <span>远程运维</span>
        </el-form-item>
        <el-form-item label="故障类别" prop="oneFaultType">
          <el-row>
            <el-col :span="8">
              <el-select
                v-model="basicForm.oneFaultType"
                multiple
                filterable
                @change="handleFirstFaultChange"
                style="width: 100%;"
                value-key="id"
              >
                <el-option
                  v-for="item in firstFaultOptions"
                  :label="item.typeName"
                  :value="item"
                  :key="item.id"
                />
                <el-option
                  v-for="i in defaultFirstFaultOptions"
                  :label="i.typeName"
                  :key="i.id"
                  :value="i"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="basicForm.twoFaultType"
                multiple
                filterable
                @change="handleSecondFaultChange"
                style="width: 100%;"
                value-key="id"
              >
                <el-option
                  v-for="item in secondFaultOptions"
                  :label="item.typeName"
                  :value="item"
                  :key="item.id"
                />
                <el-option
                  v-for="i in defaultSecondFaultOptions"
                  :label="i.typeName"
                  :key="i.id"
                  :value="i"
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="basicForm.threeFaultType"
                multiple
                filterable
                style="width: 100%;"
                value-key="id"
              >
                <el-option
                  v-for="item in thirdFaultOptions"
                  :label="item.typeName"
                  :value="item"
                  :key="item.id"
                />
                <el-option
                  v-for="i in defaultThreeFaultType"
                  :label="i.typeName"
                  :key="i.id"
                  :value="i"
                />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="检查内容" prop="checkContent">
          <el-input
            type="textarea"
            v-model="basicForm.checkContent"
            :rows="5"
            maxlength="10000"
            placeholder="10000个字符以内"
          ></el-input>
        </el-form-item>
        <el-form-item label="处理措施及结果" prop="handleResult">
          <el-input
            type="textarea"
            v-model="basicForm.handleResult"
            :rows="5"
            maxlength="10000"
            placeholder="10000个字符以内"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="handleDesc">
          <el-input
            type="textarea"
            v-model="basicForm.handleDesc"
            :rows="5"
            maxlength="10000"
            placeholder="10000个字符以内"
          ></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <file-upload
            ref="upload"
            v-model="basicForm.docList"
            :limit="20"
            accept=".jpg, .jpeg, .png, .mp4, .mov"
            :fileMaxSize="5"
            textTip="支持批量上传，上传格式为jpg、jpeg、png、mp4、mov文件"
          />
        </el-form-item>
      </el-form>
      <div v-else>
        <el-form label-width="130px">
          <el-form-item label="作业方式">
            <span>远程运维</span>
          </el-form-item></el-form
        >
        <form-create
          v-for="(item, index) in formArr"
          :key="index"
          :rule="item.formJson"
          v-model="dealFormData[index]"
          :option="item.formConfig"
        />
      </div>

      <div class="dialog-footer">
        <el-button @click.stop="goBack" size="medium" :loading="btnLoading"
          >取 消</el-button
        >
        <el-button
          @click="handleSubmit(0)"
          type="primary"
          size="medium"
          :loading="btnLoading"
          >保存</el-button
        >
        <el-button
          @click="handleSubmit(1)"
          type="primary"
          size="medium"
          :loading="btnLoading"
          >提交</el-button
        >
      </div>
    </el-card>
  </div>
</template>

<script>
import FileUpload from "@/components/Upload/fileUpload4.vue";
import {
  handleDetail,
  handleOrder,
  queryHandleForm,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import { childFaultList, firstFaultList } from "@/api/faultType/index.js";
import formCreateMixin from "@/mixin/formCreate.js";

export default {
  name: "operationWorkOrderHandle",
  mixins: [formCreateMixin],
  components: {
    FileUpload,
  },
  data() {
    return {
      handleUser: "",
      orderNo: "",
      basicForm: {
        oneFaultType: [],
        twoFaultType: [],
        threeFaultType: [],
        checkContent: "",
        handleResult: "",
        handleDesc: "",
      },
      rules: {
        oneFaultType: [
          { required: true, message: "请选择故障类别", trigger: "change" },
        ],
        checkContent: [
          { required: true, message: "请输入检查内容", trigger: "change" },
        ],
        handleResult: [
          {
            required: true,
            message: "请输入处理措施及结果",
            trigger: "change",
          },
        ],
      },
      firstFaultOptions: [],
      secondFaultOptions: [],
      thirdFaultOptions: [],
      btnLoading: false,
      //formCreate-s
      showfileName: false,
      formJson: [],
      formConfig: {},
      dealFormData: [],
      formArr: [],
      //formCreate-e
      isEngineer: false,
    };
  },
  computed: {
    defaultThreeFaultType() {
      return this.basicForm.threeFaultType?.filter((x) => {
        return !this.thirdFaultOptions?.some((y) => y.id == x.id);
      });
    },
    defaultSecondFaultOptions() {
      return this.basicForm.twoFaultType?.filter((x) => {
        return !this.secondFaultOptions?.some((y) => y.id == x.id);
      });
    },
    defaultFirstFaultOptions() {
      return this.basicForm.oneFaultType?.filter((x) => {
        return !this.firstFaultOptions?.some((y) => y.id == x.id);
      });
    },
  },
  created() {
    this.orderNo = this.$route.query.orderNo;
    this.handleUser = this.$route.query.handleUser;
    firstFaultList().then((res) => {
      this.firstFaultOptions = res?.data;
    });
    this.isEngineer = this.$route.query.customizeFlag === "Y";
    if (this.isEngineer) {
      this.getFormData();
    } else {
      this.getDetail();
    }
  },
  mounted() {},
  methods: {
    async checkDealForms() {
      let flag = true;
      if (this.formArr?.length > 0) {
        flag = false;
        for (let i = 0; i < this.dealFormData.length; i++) {
          await this.dealFormData[i].validate((valid) => {
            if (valid) {
              flag = true;
            } else {
              this.$message.warning("请填写完整表单信息!");
            }
          });
        }
      }
      return flag;
    },
    getFormData() {
      queryHandleForm({ orderNo: this.orderNo })
        .then((res) => {
          if (res?.code === "10000") {
            this.formArr = res.data?.map((x) => {
              return {
                ...x,
                formConfig: JSON.parse(x.formConfig),
                formJson: JSON.parse(x.formJson)?.map((item) => {
                  this.handleFormRule(item);
                  return item;
                }),
                dealFormData: {},
              };
            });
          } else {
            this.formArr = [];
          }
        })
        .catch(() => {
          this.formArr = [];
        });
    },
    async handleSubmit(flag) {
      if (this.isEngineer) {
        // 表单提交以及审核通过需要校验填写表单的内容
        if (!(await this.checkDealForms())) {
          return;
        }
        const msg = flag == 1 ? "提交" : "保存";
        this.$confirm("是否确认" + msg + "?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          const params = {
            submitFlag: flag,
            orderNo: this.orderNo,
            operationMode: "1",
            handleUser: this.handleUser,
            customizeCheckItems: this.formArr?.map((x) => {
              return {
                ...x,
                formJson: JSON.stringify(x.formJson),
                formConfig: JSON.stringify(x.formConfig),
              };
            }),
          };
          handleOrder(params)
            .then((res) => {
              this.btnLoading = false;
              if (res?.code === "10000") {
                this.$message.success(msg + "成功");
                this.$store.dispatch("tagsView/delView", this.$route);
                this.goBack();
              }
            })
            .then(() => {
              this.btnLoading = false;
            });
        });
      } else {
        this.$refs.basicForm.validate((valid) => {
          if (!valid) {
            return;
          }
          const msg = flag == 1 ? "提交" : "保存";
          this.$confirm("是否确认" + msg + "?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            const {
              oneFaultType,
              twoFaultType,
              threeFaultType,
              ...rest
            } = this.basicForm;
            const params = {
              ...rest,
              submitFlag: flag,
              orderNo: this.orderNo,
              // docList: this.$refs.upload.fileList,
              operationMode: "1",
              handleUser: this.handleUser,
            };
            params["oneFaultType"] =
              oneFaultType.length == 0 ? "" : JSON.stringify(oneFaultType);
            params["twoFaultType"] =
              twoFaultType.length == 0 ? "" : JSON.stringify(twoFaultType);
            params["threeFaultType"] =
              threeFaultType.length == 0 ? "" : JSON.stringify(threeFaultType);
            console.log(params, "提交");
            this.btnLoading = true;
            handleOrder(params)
              .then((res) => {
                this.btnLoading = false;
                if (res?.code === "10000") {
                  this.$message.success(msg + "成功");
                  this.$store.dispatch("tagsView/delView", this.$route);
                  this.goBack();
                }
              })
              .then(() => {
                this.btnLoading = false;
              });
          });
        });
      }
    },
    goBack() {
      this.$router.push({
        path: "/operationMaintenanceManage/operationWorkOrder/list",
      });
    },
    handleFirstFaultChange(val) {
      this.basicForm.twoFaultType = [];
      this.basicForm.threeFaultType = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val.map((x) => x.id) }).then((res) => {
          this.secondFaultOptions = res?.data;
        });
      }
      this.thirdFaultOptions = [];
    },
    handleSecondFaultChange(val) {
      this.basicForm.threeFaultType = [];
      if (val && val.length > 0) {
        childFaultList({ ids: val.map((x) => x.id) }).then((res) => {
          this.thirdFaultOptions = res?.data;
        });
      }
    },
    getDetail() {
      handleDetail({ orderNo: this.orderNo }).then((res) => {
        if (res.data) {
          const {
            oneFaultType,
            twoFaultType,
            threeFaultType,
            // docList = [],
          } = res.data;
          // this.$refs.upload.fileList = docList;

          this.basicForm = {
            ...this.basicForm,
            ...res.data,
            oneFaultType: oneFaultType ? JSON.parse(oneFaultType) : [],
            twoFaultType: twoFaultType ? JSON.parse(twoFaultType) : [],
            threeFaultType: threeFaultType ? JSON.parse(threeFaultType) : [],
          };
          childFaultList({
            ids: JSON.parse(oneFaultType)?.map((x) => x.id),
          }).then((res) => {
            this.secondFaultOptions = res?.data;
          });
          childFaultList({
            ids: JSON.parse(twoFaultType)?.map((x) => x.id),
          }).then((res) => {
            this.thirdFaultOptions = res?.data;
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}
</style>
