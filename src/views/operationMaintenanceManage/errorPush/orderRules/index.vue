// 建单规则
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="id"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleRefresh"
            v-has-permi="['maintenanceErrorPush:orderRules:refresh']"
          >
            刷新
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCreate"
            v-has-permi="['maintenanceErrorPush:orderRules:batchConfig']"
          >
            批量配置
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="handleUpdate(row)"
            v-has-permi="['maintenanceErrorPush:orderRules:config']"
          >
            配置
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleLog(row)"
            v-has-permi="['maintenanceErrorPush:orderRules:log']"
          >
            日志
          </el-button>
        </template>
      </GridTable>
      <el-dialog
        title="操作日志"
        :visible.sync="logVisible"
        :close-on-click-modal="false"
        @close="closeLogDialog"
        append-to-body
        width="70%"
      >
        <Timeline :list="recordList"></Timeline>
      </el-dialog>
      <el-dialog
        :title="title"
        :visible.sync="addVisible"
        :close-on-click-modal="false"
        @close="closeAddDialog"
        append-to-body
        width="50%"
      >
        <div class="queryParamsWrap">
          <el-form
            :model="addForm"
            ref="addForm"
            :inline="true"
            label-width="110px"
            :rules="rules"
          >
            <el-row v-if="handRow.ids.length > 0">
              <el-col>
                <el-form-item label="已选故障名称:">
                  <span>{{ handRow.ids.length }}条</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="建单规则:" prop="orderRule">
                  <el-select
                    v-model="addForm.orderRule"
                    placeholder="请选择建单规则"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in ruleOptions"
                      :key="item.dictValue"
                      :label="item.dictLabel"
                      :value="item.dictValue"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="处理人" prop="handleUserName">
                  <el-row>
                    <el-col :span="10">
                      <el-input v-model="addForm.handleUserName" disabled>
                      </el-input>
                    </el-col>
                    <el-col :span="10" style="margin-left:10px">
                      <el-button @click="handleSelectUser">选择</el-button>
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="备注:" prop="remark">
                  <el-input
                    v-model="addForm.remark"
                    type="textarea"
                    maxlength="500"
                    show-word-limit
                    size="mini"
                    rows="5"
                    placeholder="请输入原因，长度500个字符以内"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <div class="tip">
                <div class="tip-label">说明：</div>
                <div>
                  <p>
                    1、自动创建工单
                    同时合并工单（储能-站点维度）：指的是对于同一个储能站点的告警信息系统将自动生成告警工单，如果工单处于待处理状态，符合该建单规则的故障数据，将插入到待处理的告警工单中，不再新增。
                  </p>
                  <p>
                    2、自动生成工单并合并：指的是同一个桩的告警信息系统将自动生成告警工单，如果工单处于待处理状态，符合该建单规则的故障数据，将插入到待处理的告警工单中，不再新增。
                  </p>
                  <p>
                    3、自动生成工单不合并：指的是按照故障名称生成告警工单，如果该故障名称产生了多条告警数据，则生成多条告警工单。
                  </p>
                  <p>
                    4、不自动生成工单：只接收告警信息，不自动创建告警工单。
                  </p>
                </div>
              </div>
            </el-row>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click.stop="closeAddDialog">取 消</el-button>
          <el-button type="primary" @click.stop="submitForm">确认</el-button>
        </div>
      </el-dialog>
    </el-card>
    <HandleUserDialog
      ref="userDialog"
      @confirm="getSelectedUser"
    ></HandleUserDialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import HandleUserDialog from "@/components/selectDialog/handleUserDialog/index.vue";
import Timeline from "@/components/Timeline/index.vue";
import {
  queryOrderRuleList,
  updateRuleConfig,
  queryRuleLog,
  refreshRules,
} from "@/api/operationMaintenanceManage/errorPush/index.js";
// import {
//   ERROR_CLICK_BATCH_CREATE_ORDER_RULE,
//   ERROR_CLICK_CREATE_ORDER_RULE,
// } from "@/utils/track/track-event-constants";

export default {
  components: { AdvancedForm, GridTable, Timeline, HandleUserDialog },
  data() {
    return {
      config: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "typeName",
          title: "告警类型",
        },
        {
          field: "faultName",
          title: "故障名称",
          customWidth: 200,
        },
        {
          field: "faultCode",
          title: "故障码",
        },
        {
          field: "equipType",
          title: "设备类别",
        },
        {
          field: "faultDesc",
          title: "故障描述",
          customWidth: 200,
        },
        {
          field: "status",
          title: "启用状态",
          formatter: ({ cellValue }) => {
            if (cellValue == 0) {
              return "启用";
            } else {
              return "禁用";
            }
          },
        },
        {
          field: "syncTime",
          title: "同步时间",
        },
        {
          field: "orderRuleDesc",
          title: "建单规则",
        },
        {
          field: "ruleStatus",
          title: "建单规则配置状态",
          formatter: ({ cellValue }) => {
            if (cellValue == 1) {
              return "已配置";
            } else {
              return "未配置";
            }
          },
        },
        {
          field: "configTime",
          title: "配置时间",
        },
        {
          field: "configByName",
          title: "配置人",
        },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "stationLevelList",
      userOption: [], //用户列表
      recordList: [],
      logVisible: false,
      title: "新增站点运维等级",
      addForm: {
        orderRule: undefined,
        remark: undefined,
        handleUserName: "",
        handleUser: "",
      },
      rules: {
        orderRule: [
          { required: true, message: "请选择建单规则", trigger: "blur" },
        ],
        handleUserName: [
          { required: true, message: "请选择处理人", trigger: "blur" },
        ],
      },
      addVisible: false,
      isInputActive: false,
      ruleOptions: [],
      orderRuleIds: [],
      handRow: { ids: [] },
    };
  },
  created() {
    this.getDicts("order_rule").then((response) => {
      this.ruleOptions = response.data;
    });
  },
  mounted() {
    Promise.all([]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    getSelectedUser(row) {
      const { userType, selectedUser } = row;
      this.addForm.handleUserName =
        userType === "person" ? selectedUser.nickName : selectedUser.groupName;
      this.addForm["userType"] = userType;
      this.addForm.handleUser =
        userType === "person" ? selectedUser.userId : selectedUser.groupId;
      console.log(this.addForm, "==");
    },
    handleSelectUser() {
      const { userType, handleUser, handleUserName } = this.addForm;
      const params = {
        userType: userType,
        selectedUser:
          userType === "person"
            ? { nickName: handleUserName, userId: handleUser }
            : { groupName: handleUserName, groupId: handleUser },
      };
      this.$refs.userDialog.open(params);
    },
    handleRefresh() {
      this.loading = true;
      refreshRules({})
        .then((res) => {
          if (res.code === "10000") {
            this.resetQuery();
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        ids: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.ids = tableData.map((v) => v.id);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    //提交
    async submitForm() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          const {
            userType = "",
            handleUser,
            handleUserName,
            handleGroup,
            handleGroupName,
            ...rest
          } = this.addForm;
          let form = {
            ...rest,
            orderRuleDesc: this.ruleOptions?.find(
              (item) => item.dictValue == this.addForm.orderRule
            )?.dictLabel,
            orderRuleIds: this.orderRuleIds,
          };
          if (userType === "person") {
            form["handleUser"] = handleUser;
            form["handleUserName"] = handleUserName;
          } else {
            form["handleGroup"] = handleUser;
          }

          const { code, data } = await updateRuleConfig(form);
          if (code != 10000) return;
          this.$message.success("保存成功");
          this.closeAddDialog();
          this.getList();

          if (form?.orderRuleIds?.length > 1) {
            //批量配置
            // this.reportTrackEvent(ERROR_CLICK_BATCH_CREATE_ORDER_RULE);
          } else {
            // this.reportTrackEvent(ERROR_CLICK_CREATE_ORDER_RULE);
          }
        }
      });
    },

    handleCreate() {
      if (this.handRow.ids.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }
      this.addVisible = true;
      this.orderRuleIds = this.handRow.ids;
      this.title = "批量配置建单规则";
    },
    handleUpdate(row) {
      this.addVisible = true;
      this.title = "配置建单规则";
      const {
        handleUser = "",
        handleGroup = "",
        handleGroupName = "",
        handleUserName = "",
      } = row;
      this.addForm = {
        ...this.addForm,
        ...row,
      };
      this.$set(
        this.addForm,
        "handleUserName",
        handleUserName || handleGroupName
      );
      this.$set(this.addForm, "handleUser", handleUser || handleGroup);
      this.$set(this.addForm, "userType", handleUser ? "person" : "group");
      this.orderRuleIds = [row.id];
    },
    closeAddDialog() {
      this.addVisible = false;
      this.addForm = {
        orderRule: undefined,
        remark: undefined,
        handleUserName: "",
        handleUser: "",
      };
      this.$refs.gridTable.clearTips();
    },

    handleLog(row) {
      this.logVisible = true;
      queryRuleLog({ id: row.id }).then((res) => {
        this.recordList = res.data;
      });
    },
    closeLogDialog() {
      this.logVisible = false;
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime1)) {
        params.syncBeginTime = params.rangeTime1[0] + " 00:00:00";
        params.syncEndTime = params.rangeTime1[1] + " 23:59:59";
        delete params.rangeTime1;
      }
      if (Array.isArray(params.rangeTime2)) {
        params.configBeginTime = params.rangeTime2[0] + " 00:00:00";
        params.configEndTime = params.rangeTime2[1] + " 23:59:59";
        delete params.rangeTime2;
      }
      queryOrderRuleList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "typeName",
          title: "告警类型",
          type: "select",
          placeholder: "请选择告警类型",
          options: [
            { dictLabel: "业务告警", dictValue: "业务告警" },
            { dictLabel: "故障告警", dictValue: "故障告警" },
          ],
        },
        {
          key: "faultName",
          title: "故障名称",
          type: "input",
          placeholder: "请输入故障名称",
        },
        {
          key: "rangeTime1",
          title: "同步时间",
          type: "dateRange",
          placeholder: "请选择同步时间",
        },
        {
          key: "ruleStatus",
          title: "配置状态",
          type: "select",
          placeholder: "请选择配置状态",
          options: [
            { dictLabel: "未配置", dictValue: "0" },
            { dictLabel: "已配置", dictValue: "1" },
          ],
        },
        {
          key: "equipType",
          title: "设备类别",
          type: "select",
          placeholder: "请输入设备类别",
          options: [
            { dictLabel: "充电桩", dictValue: "充电桩" },
            { dictLabel: "电表", dictValue: "电表" },
            { dictLabel: "BMS", dictValue: "BMS" },
            { dictLabel: "PCS", dictValue: "PCS" },
            { dictLabel: "储能柜", dictValue: "储能柜" },
            { dictLabel: "空调", dictValue: "空调" },
            { dictLabel: "储能业务告警", dictValue: "储能业务告警" },
            { dictLabel: "EMS", dictValue: "EMS" },
          ],
        },
        {
          key: "rangeTime2",
          title: "配置时间",
          type: "dateRange",
          placeholder: "请选择配置时间",
        },
        {
          key: "status",
          title: "启用状态",
          type: "select",
          placeholder: "请选择启用状态",
          options: [
            { dictLabel: "启用", dictValue: "0" },
            { dictLabel: "禁用", dictValue: "1" },
          ],
        },
      ];
    },
  },
};
</script>

<style lang="less" scoped>
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
.tip {
  font-size: 12px;
  color: rgb(193, 196, 203);
  display: flex;
  &-label {
    width: 110px;
    text-align: right;
    margin: 12px;
  }
}
</style>
