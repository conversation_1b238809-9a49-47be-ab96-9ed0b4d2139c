<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="false"
      :rules="searchRules"
    >
      <template slot="countTime">
        <el-row>
          <el-col :span="6">
            <el-select
              v-model="searchForm.dateFlag"
              style="width: 100%"
              @change="handleDateTypeChange"
            >
              <el-option
                v-for="item in dataType"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="18">
            <el-date-picker
              v-model="searchForm.countTime"
              :type="searchForm.dateFlag === 0 ? 'daterange' : 'monthrange'"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              :picker-options="pickerOptions"
              :clearable="false"
            >
            </el-date-picker>
          </el-col>
        </el-row>
      </template>
    </AdvancedForm>

    <el-row :gutter="12">
      <el-col :span="14">
        <el-card v-loading="lineChartLoading">
          <div>
            订单启动成功率统计图表
            <el-tooltip
              content="订单启动成功率=充电启动成功且整个过程不跳枪的订单数/总订单数*100%"
              placement="top-start"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <LineChart
            :axisData="lineChartData.time"
            :serieData="lineChartData.tendencyArr"
            lineType="line"
            :symbolSize="10"
            :symbol="null"
            :lineColor="['#f59a23']"
            v-if="lineChartData.time && lineChartData.time.length > 0"
            height="500px"
            :showZoomNum="7"
            toolTipValUnit="%"
          ></LineChart>
          <el-empty v-else style="height:500px"></el-empty>
        </el-card>
      </el-col>

      <el-col :span="10">
        <el-card v-loading="barChartLoading">
          <div>
            订单启动成功率场站排名
            <el-tooltip content="仅展示TOP50的场站" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <BarChart
            v-if="barChartData.seriesData.length"
            :seriesData="barChartData.seriesData"
            :yAxisData="barChartData.yAxisData"
            itemStyleColor="#02a7f0"
            yAxisName="场站"
            xAxisName="订单启动成功率"
            height="500px"
            :tooltip="tooltip"
            :xAxisMax="100"
          />
          <el-empty v-else style="height:500px"></el-empty>
        </el-card>
      </el-col>
    </el-row>

    <el-card style="margin-top: 12px">
      <div style="display:flex;justify-content: space-between;">
        <div>订单启动成功率统计报表</div>
        <el-button
          type="primary"
          icon="el-icon-download"
          @click.stop="handleExport"
          >导出</el-button
        >
      </div>
      <GridTable
        :columns="[...columns0, ...columns1, ...columns2]"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import { dataType } from "../constant";
import { regionData } from "element-china-area-data";
import LineChart from "@/components/Echarts/LineChart.vue";
import BarChart from "@/components/Echarts/BarChart.vue";

import {
  queryBrandOpts,
  queryGunModelList,
} from "@/api/chargingStation/index.js";
import { queryLevelList } from "@/api/operationMaintenanceManage/station/index.js";
import {
  getOperatorList,
  getOrderSuccessRate,
  getOrderSuccessRank,
  getOrderSuccessRateList,
  exportReport,
} from "@/api/operationWorkOrder/index.js";
import moment from "moment";
import { operationModelist } from "../constant";
import exportMixin from "@/mixin/export.js";
export default {
  name: "eqOnlineRate",
  mixins: [exportMixin],
  components: {
    AdvancedForm,
    GridTable,
    LineChart,
    BarChart,
  },
  data() {
    return {
      selectDate: "",
      pickerOptions: {
        onPick: ({
          maxDate, // 选择的日期范围的最大日期
          minDate, // 选择的日期范围的最小日期
        }) => {
          this.selectDate = minDate.getTime(); // 将最小日期的时间戳存储在selectDate变量中
          if (maxDate) {
            this.selectDate = ""; // 如果存在最大日期，则将selectDate重置为空字符串
          }
        },
        disabledDate: (time) => {
          // 如果日期的时间戳大于等于当前时间的时间戳
          if (time.getTime() >= new Date().getTime()) {
            return true; // 禁用该日期 今天之后的时间不可选
          }
          if (this.selectDate !== "") {
            if (this.searchForm.dateFlag == "0") {
              return (
                time <
                  moment(this.selectDate)
                    .subtract(1, "M")
                    .startOf("day") ||
                time >
                  moment(this.selectDate)
                    .add(1, "M")
                    .endOf("day")
              );
            } else {
              return (
                time <
                  moment(this.selectDate)
                    .subtract(2, "Y")
                    .startOf("month") ||
                time >
                  moment(this.selectDate)
                    .add(2, "Y")
                    .endOf("month")
              );
            }
          }
        },
      },
      assetList: [], // 资产单位
      operationList: [], // 运营单位
      dataType,
      gunModelList: [], // 设备型号  (取自枪型号列表)
      columns0: [
        {
          type: "seq",
          title: "序号",
          width: 80,
          formatter: ({ rowIndex }) => rowIndex,
        },
        {
          field: "countTime",
          title: "统计时间",
          minWidth: 180,
          showOverflowTooltip: true,
          formatter: () => {
            return `${moment(this.searchForm.countTime[0]).format(
              "YYYY-MM-DD"
            )}~${moment(this.searchForm.countTime[1]).format("YYYY-MM-DD")}`;
          },
        },
        {
          field: "pileNo",
          title: "设备编码",
          showOverflowTooltip: true,
          minWidth: 180,
          formatter: ({ row }) => {
            return (row.pileNo || "") + "-" + (row.gunNo || "");
          },
        },

        {
          field: "pileName",
          title: "设备名称",
          showOverflowTooltip: true,
          minWidth: 180,
          formatter: ({ row }) => {
            return (row.pileName || "") + "-" + (row.gunName || "");
          },
        },

        {
          field: "modelName",
          title: "设备型号",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "subTypeName",
          title: "设备类型",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "brandName",
          title: "品牌",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "totalCount",
          title: "总订单数",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: {
            message: "总订单数：在统计时间内，该充电枪产生的所有的订单数量",
          },
        },
        {
          field: "successCount",
          title: "启动成功订单数",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: {
            message: "启动成功订单数：在统计时间内，该充电枪成功启动的订单数量",
          },
        },
        {
          field: "successRate",
          title: "订单启动成功率",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: {
            message: "订单启动成功率：启动成功订单数/总订单数*100%",
          },
          formatter: ({ cellValue }) => (cellValue ? `${cellValue}%` : ""),
        },
      ],
      columns1: [],
      columns2: [
        {
          field: "stationName",
          title: "所属站点名称",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "stationNo",
          title: "站点编码",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "orgNoName",
          showOverflowTooltip: true,
          title: "所属大区",
          minWidth: 180,
        },
        {
          field: "provinceInfo",
          showOverflowTooltip: true,
          title: "省市区",
          minWidth: 150,
        },
        {
          field: "operationMode",
          title: "运营类型",
          showOverflowTooltip: true,
          minWidth: 180,
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((i) => i.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "stationGradeName",
          title: "运维等级",
          showOverflowTooltip: true,
          minWidth: 180,
        },

        {
          field: "assetName",
          title: "资产单位",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "operationName",
          title: "运营单位",
          showOverflowTooltip: true,
          minWidth: 180,
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        dateFlag: 0,
        countTime: [moment().subtract(7, "days"), moment()],
      },
      total: 0,
      loading: false,
      chargingChannelOptions: [], // 渠道
      logVisible: false,
      levelVisible: false,
      levelOptions: [], // 运维等级
      deptOptionList: [], // 能投大区
      operationModeOptions: [], // 运营类型

      brandOptions: [], // 品牌列表
      subTypeOptions: [], // 设备类型列表

      lineChartData: {
        time: [],
        tendencyArr: [
          {
            name: "订单启动成功率",
            data: [],
          },
        ],
      },
      lineChartLoading: false,
      barChartData: {
        seriesData: [],
        yAxisData: [],
      },
      barChartLoading: false,
      searchRules: {
        deviceCode: [
          {
            pattern: /^[^\s-]+-.*$/,
            message: "请输入正确的格式(桩编码-枪编码/桩编码-)",
          },
        ],
        deviceName: [
          {
            pattern: /^[^\s-]+-.*$/,
            message: "请输入正确的格式(桩名称-枪名称/桩名称-)",
          },
        ],
      },
    };
  },
  async created() {
    this.getBusinessTypeOptions();

    this.getDicts("cm_operation_mode").then((response) => {
      this.operationModeOptions = response?.data;
    });
    this.getDicts("CHARGE_CHANNEL").then((response) => {
      this.chargingChannelOptions = response.data;
    });
    this.getDicts("cm_sub_type").then((response) => {
      this.subTypeOptions = response.data;
    });

    this.getLevel();
    this.getDeptList();
    this.queryBrandOpts();
    this.queryGunModelList();
    this.$nextTick(() => {
      this.loadData();
    });
  },
  mounted() {},
  methods: {
    handleDateTypeChange(val) {
      this.searchForm.countTime =
        val == "0"
          ? [moment().subtract(7, "days"), moment()]
          : [moment(), moment()];
    },
    queryGunModelList() {
      queryGunModelList({ modelName: "" }).then((res) => {
        this.gunModelList = res.data;
      });
    },
    getLevel() {
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      });
    },
    queryBrandOpts() {
      queryBrandOpts({ brandName: "" }).then((res) => {
        this.brandOptions = res.data;
      });
    },

    getBusinessTypeOptions() {
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    checkPermission,
    async getDeptList() {
      // 大区
      getOperatorList({
        unitTypeFlag: "03",
        operatorName: "",
      }).then((res) => {
        this.deptOptionList = res.data?.data.map((x) => {
          return { ...x, dictValue: x.operatorId, dictLabel: x.operatorName };
        });
      });
      // 资产
      getOperatorList({
        unitTypeFlag: "01",
        operatorName: "",
      }).then((res) => {
        this.assetList = res.data?.data.map((x) => {
          return { ...x, dictValue: x.operatorId, dictLabel: x.operatorName };
        });
      });
      // 02：运营单位
      getOperatorList({
        unitTypeFlag: "02",
        operatorName: "",
      }).then((res) => {
        this.operationList = res.data?.data.map((x) => {
          return { ...x, dictValue: x.operatorId, dictLabel: x.operatorName };
        });
      });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.loadData();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        dateFlag: 0,
        countTime: [moment().subtract(7, "days"), moment()],
      };
      this.loadData();
    },
    changePage() {
      this.loadData();
    },
    getQueryParams() {
      const params = {
        ...this.searchForm,
      };
      if (params.region && params.region.length > 0) {
        const len = params.region.length;
        params.areaCode = params.region[len - 1];
        delete params.region;
      }
      if (params.countTime) {
        if (params.dateFlag === 0) {
          params.startDay = moment(params.countTime[0]).format("YYYY-MM-DD");
          params.endDay = moment(params.countTime[1]).format("YYYY-MM-DD");
        } else {
          params.startDay = moment(params.countTime[0]).format("YYYY-MM");
          params.endDay = moment(params.countTime[1]).format("YYYY-MM");
        }
      } else {
        params.startDay = moment()
          .subtract(7, "days")
          .format("YYYY-MM-DD");
        params.endDay = moment().format("YYYY-MM-DD");
      }
      delete params.countTime;

      return params;
    },
    loadData() {
      let params = this.getQueryParams();
      this.loadLineChart(params);
      this.loadBarChart(params);
      this.loadList(params);
    },
    handleExport() {
      let params = this.getQueryParams();
      this.handleCommonExport(exportReport, params);
    },
    loadLineChart(params) {
      this.lineChartLoading = true;
      getOrderSuccessRate(params)
        .then((res) => {
          if (res.code === "10000") {
            this.lineChartData = {
              time: res.data.map((i) => i.date),
              tendencyArr: [
                {
                  name: "订单启动成功率",
                  data: res.data.map((i) => i.rate),
                },
              ],
            };
            this.lineChartLoading = false;
          } else {
            this.$message.error(res.message);
            this.lineChartLoading = false;
          }
        })
        .catch((err) => {
          this.lineChartLoading = false;
        });
    },
    loadBarChart(params) {
      this.barChartLoading = true;
      getOrderSuccessRank(params)
        .then((res) => {
          if (res.code === "10000") {
            const yAxisData = [],
              seriesData = [];
            res.data.reverse().forEach((i) => {
              seriesData.push(i.rate);
              yAxisData.push(i.stationName);
            });
            this.barChartData = {
              seriesData,
              yAxisData,
            };
            this.barChartLoading = false;
          } else {
            this.$message.error(res.message);
            this.barChartLoading = false;
          }
        })
        .catch((err) => {
          this.barChartLoading = false;
        });
    },
    loadList(params) {
      this.loading = true;
      getOrderSuccessRateList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data.dataRate.map((i) => {
              i?.channelRateVOList?.forEach((c) => {
                Object.assign(i, { [c.channelName]: c });
              });
              return i;
            });
            this.columns1 = res.data.channel.map((i) => ({
              field: i,
              title: i,
              children: [
                {
                  field: `${i}.channelTotalCount`,
                  title: i + "订单数",
                  showOverflowTooltip: true,
                  width: 150,
                },
                {
                  field: `${i}.channelSuccessCount`,
                  title: i + "启动成功订单",
                  showOverflowTooltip: true,
                  width: 150,
                },
                {
                  field: `${i}.channelSuccessRate`,
                  title: i + "订单启动成功率",
                  showOverflowTooltip: true,
                  width: 160,
                  formatter: ({ cellValue }) =>
                    cellValue ? `${cellValue}%` : "",
                },
              ],
            }));
            console.log(this.columns1, '+++')
            this.total = res.data.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
  computed: {
    tooltip() {
      return {
        formatter: (p) => {
          return `
            <h4>${p.name}</h4>
            <div>订单启动成功率 ${p.data}%</div>
            <div>排名 ${this.barChartData.seriesData.length - p.dataIndex}</div>
          `;
        },
        trigger: "item",
      };
    },
    config() {
      return [
        {
          key: "deviceCode",
          title: "设备编码",
          type: "input",
          placeholder: "请填写设备编码",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请填写设备名称",
        },
        {
          key: "modelId",
          title: "设备型号",
          type: "select",
          placeholder: "请填写设备型号",
          options: this.gunModelList,
          optionLabel: "modelName",
          optionValue: "modelId",
        },
        {
          key: "brandId",
          title: "品牌",
          type: "select",
          options: this.brandOptions,
          optionLabel: "brandName",
          optionValue: "brandId",
          placeholder: "请选择品牌",
        },
        {
          key: "equipTypeCode",
          title: "设备类型",
          type: "select",
          placeholder: "请选择设备类型",
          options: this.subTypeOptions,
          optionLabel: "dictLabel",
          optionValue: "dictValue",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请填写站点编码",
        },
        {
          key: "orgNoCode",
          title: "所属大区",
          type: "select",
          placeholder: "请选择所属大区",
          options: this.deptOptionList,
        },
        {
          key: "region",
          title: "省市区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区域",
          props: {
            multiple: false,
          },
        },
        {
          key: "operationMode",
          title: "运营类型",
          type: "select",
          placeholder: "请选择运营类型",
          options: this.operationModeOptions,
        },
        {
          key: "stationGradeId",
          title: "运维等级",
          type: "select",
          placeholder: "请选择运维等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },

        {
          key: "assetBuildIds",
          title: "资产单位",
          type: "select",
          placeholder: "请填写资产单位",
          options: this.assetList,
          multiple: true,
        },
        {
          key: "operationBuildIds",
          title: "运营单位",
          type: "select",
          placeholder: "请填写运营单位",
          options: this.operationList,
          multiple: true,
        },
        {
          key: "chargeChannel",
          title: "充电渠道",
          type: "select",
          placeholder: "请选择充电渠道",
          options: this.chargingChannelOptions,
          optionLabel: "dictLabel",
          optionValue: "dictValue",
        },
        {
          key: "countTime",
          title: "统计时间",
          type: "slot",
          placeholder: "请选择工单类型",
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
