<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="false"
    >
      <template slot="countTime">
        <el-row>
          <el-col :span="6">
            <el-select
              v-model="searchForm.dateFlag"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in dataType"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              />
            </el-select>
          </el-col>
          <el-col :span="18">
            <el-date-picker
              :disabled="searchForm.dateFlag === undefined"
              v-model="searchForm.countTime"
              :type="searchForm.dateFlag === 0 ? 'daterange' : 'monthrange'"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-col>
        </el-row>
      </template>
    </AdvancedForm>

    <el-row :gutter="12">
      <el-col :span="14">
        <el-card v-loading="lineChartLoading">
          <div>
            设备在线率统计图表
            <el-tooltip
              content="设备在线率=设备实际在线时长/设备运营时长*100%"
              placement="top-start"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <LineChart
            :axisData="lineChartData.time"
            :serieData="lineChartData.tendencyArr"
            lineType="line"
            :symbolSize="10"
            :symbol="null"
            :lineColor="['#f59a23']"
            v-if="lineChartData.time && lineChartData.time.length > 0"
            height="500px"
            :showZoomNum="7"
            toolTipValUnit="%"
          ></LineChart>
          <el-empty v-else style="height:500px"></el-empty>
        </el-card>
      </el-col>

      <el-col :span="10">
        <el-card v-loading="barChartLoading">
          <div>
            设备在线率场站排名
            <el-tooltip content="仅展示TOP50的场站" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <BarChart
            v-if="barChartData.seriesData.length"
            :seriesData="barChartData.seriesData"
            :yAxisData="barChartData.yAxisData"
            itemStyleColor="#02a7f0"
            yAxisName="场站"
            xAxisName="平均设备在线率"
            height="500px"
            :tooltip="tooltip"
            :xAxisMax="100"
          />
          <el-empty v-else style="height:500px"></el-empty>
        </el-card>
      </el-col>
    </el-row>

    <el-card style="margin-top: 12px">
      <div>设备在线率统计报表</div>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import { dataType } from "../constant";
import { regionData } from "element-china-area-data";
import LineChart from "@/components/Echarts/LineChart.vue";
import BarChart from "@/components/Echarts/BarChart.vue";
import moment from "moment";
import {
  queryBrandOpts,
  queryChargingDict,
  queryGunModelList,
} from "@/api/chargingStation/index.js";
import { queryLevelList } from "@/api/operationMaintenanceManage/station/index.js";

import {
  getOperatorList,
  getOrderSuccessRate,
  getOrderSuccessRank,
  getOrderSuccessRateList,
} from "@/api/operationWorkOrder/index.js";

export default {
  name: "eqOnlineRate",
  components: {
    AdvancedForm,
    GridTable,
    LineChart,
    BarChart,
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() ||
            time.getTime() <
              moment()
                .subtract(1, "years")
                .valueOf()
          );
        },
      },
      assetList: [], // 资产单位
      operationList: [], // 运营单位
      dataType,
      gunModelList: [], // 设备型号  (取自枪型号列表)
      columns: [
        {
          type: "seq",
          title: "序号",
          width: 80,
        },
        {
          field: "countTime",
          title: "统计时间",
          minWidth: 180,
          showOverflowTooltip: true,
          formatter: () => {
            return `${moment(this.searchForm.countTime[0]).format(
              "YYYY-MM-DD"
            )}~${moment(this.searchForm.countTime[1]).format("YYYY-MM-DD")}`;
          },
        },
        {
          field: "deviceNo",
          title: "设备编码",
          showOverflowTooltip: true,
          minWidth: 180,
        },

        {
          field: "deviceName",
          title: "设备名称",
          showOverflowTooltip: true,
          minWidth: 180,
        },

        {
          field: "deviceType",
          title: "设备型号",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "deviceCategory",
          title: "设备类型",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "brand",
          title: "品牌",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "yunyintime",
          title: "运营时长",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: { message: "运营时长：指在筛选时间内的设备理应在线时长" },
        },
        {
          field: "onlineTime",
          title: "在线时长",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: { message: "在线时长：指在筛选时间内的设备实际在线时长" },
        },
        {
          field: "onlineStatus",
          title: "在线率",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: { message: "在线率：在线时长/运营时长*100%" },
        },
        {
          field: "offlineCount",
          title: "离线次数",
          showOverflowTooltip: true,
          minWidth: 180,
          titleHelp: { message: "离线次数：指在筛选时间内的设备的离线次数" },
        },
        {
          field: "stationName",
          title: "所属站点名称",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "stationNo",
          title: "站点编码",
          showOverflowTooltip: true,
          minWidth: 180,
        },
        {
          field: "orgNo",
          showOverflowTooltip: true,
          title: "所属大区",
        },
        {
          field: "provinceAddress",
          showOverflowTooltip: true,
          title: "省市区",
          minWidth: 150,
        },
        {
          field: "businessType",
          title: "运营类型",
          showOverflowTooltip: true,
          formatter: this.businessTypeFormat,
        },
        {
          field: "yyLevel",
          title: "运维等级",
          showOverflowTooltip: true,
        },

        {
          field: "zc",
          title: "资产单位",
          showOverflowTooltip: true,
        },

        {
          field: "yydw",
          title: "运营单位",
          showOverflowTooltip: true,
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        dateFlag: 0,
        countTime: [moment().subtract(7, "days"), moment()],
      },
      total: 0,
      loading: false,

      levelOptions: [], // 运维等级
      deptOptionList: [], // 能投大区
      operationModeOptions: [], // 运营类型

      brandOptions: [], // 品牌列表
      subTypeOptions: [], // 设备类型列表
      barChartData: {
        seriesData: [],
        yAxisData: [],
      },
      lineChartData: {
        time: [],
        tendencyArr: [],
      },
      lineChartLoading: false,
      barChartLoading: false,
    };
  },
  async created() {
    this.getBusinessTypeOptions();

    this.getDicts("cm_operation_mode").then((response) => {
      this.operationModeOptions = response?.data;
    });
    this.getLevel();
    this.getDeptList();
    this.querySubTypeOpts();
    this.queryBrandOpts();
    this.$nextTick(() => {
      this.loadData();
    });
  },
  mounted() {},
  methods: {
    queryGunModelList() {
      queryGunModelList({ modelName: "" }).then((res) => {
        this.gunModelList = res.data;
      });
    },
    getLevel() {
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      });
    },
    querySubTypeOpts() {
      queryChargingDict({ dictCode: "subType" }).then((res) => {
        this.subTypeOptions = res.data;
      });
    },
    queryBrandOpts() {
      queryBrandOpts({ brandName: "" }).then((res) => {
        this.brandOptions = res.data;
      });
    },

    //业务类型转换
    businessTypeFormat({ cellValue, row }) {
      return this.selectDictLabel(this.businessTypeOptions, cellValue) == ""
        ? row.businessTypeName
        : this.selectDictLabel(this.businessTypeOptions, cellValue);
    },
    getBusinessTypeOptions() {
      this.getDicts("order_business_type").then((response) => {
        this.businessTypeOptions = response?.data;
      });
    },
    checkPermission,
    async getDeptList() {
      // 大区
      getOperatorList({
        unitTypeFlag: "03",
        operatorName: "",
      }).then((res) => {
        this.deptOptionList = res.data?.data.map((x) => {
          return { ...x, dictValue: x.operatorId, dictLabel: x.operatorName };
        });
      });
      // 资产
      getOperatorList({
        unitTypeFlag: "01",
        operatorName: "",
      }).then((res) => {
        this.assetList = res.data?.data.map((x) => {
          return { ...x, dictValue: x.operatorId, dictLabel: x.operatorName };
        });
      });
      // 02：运营单位
      getOperatorList({
        unitTypeFlag: "02",
        operatorName: "",
      }).then((res) => {
        this.operationList = res.data?.data.map((x) => {
          return { ...x, dictValue: x.operatorId, dictLabel: x.operatorName };
        });
      });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.loadData();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.loadData();
    },
    changePage() {
      this.loadData();
    },
    getQueryParams() {
      const params = {
        ...this.searchForm,
      };
      if (params.countTime) {
        if (params.dateFlag === 0) {
          params.startDay = moment(params.countTime[0]).format("YYYY-MM-DD");
          params.endDay = moment(params.countTime[1]).format("YYYY-MM-DD");
        } else {
          params.startDay = moment(params.countTime[0]).format("YYYY-MM");
          params.endDay = moment(params.countTime[1]).format("YYYY-MM");
        }
      } else {
        params.startDay = moment()
          .subtract(7, "days")
          .format("YYYY-MM-DD");
        params.endDay = moment().format("YYYY-MM-DD");
      }
      delete params.countTime;

      return params;
    },
    loadData() {
      let params = this.getQueryParams();
      this.loadLineChart(params);
      this.loadBarChart(params);
      this.loadList(params);
    },
    loadLineChart(params) {
      this.lineChartLoading = true;
      getOrderSuccessRate(params)
        .then((res) => {
          if (res.code === "10000") {
            this.lineChartData = {
              time: res.data.map((i) => i.date),
              tendencyArr: [
                {
                  name: "订单启动成功率",
                  data: res.data.map((i) => i.rate),
                },
              ],
            };
            this.lineChartLoading = false;
          } else {
            this.$message.error(res.message);
            this.lineChartLoading = false;
          }
        })
        .catch((err) => {
          this.lineChartLoading = false;
        });
    },
    loadBarChart(params) {
      this.barChartLoading = true;
      getOrderSuccessRank(params)
        .then((res) => {
          if (res.code === "10000") {
            const yAxisData = [],
              seriesData = [];
            res.data.reverse().forEach((i) => {
              seriesData.push(i.rate);
              yAxisData.push(i.stationName);
            });
            this.barChartData = {
              seriesData,
              yAxisData,
            };
            this.barChartLoading = false;
          } else {
            this.$message.error(res.message);
            this.barChartLoading = false;
          }
        })
        .catch((err) => {
          this.barChartLoading = false;
        });
    },
    loadList(params) {
      this.loading = true;
      getOrderSuccessRateList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data.dataRate;
            this.total = res.data.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
  computed: {
    tooltip() {
      return {
        formatter: (p) => {
          return `
            <h4>${p.name}</h4>
            <div>订单启动成功率 ${p.data}%</div>
            <div>排名 ${this.barChartData.seriesData.length - p.dataIndex}</div>
          `;
        },
        trigger: "item",
      };
    },
    config() {
      return [
        {
          key: "deviceCode",
          title: "设备编码",
          type: "input",
          placeholder: "请填写设备编码",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请填写设备名称",
        },
        {
          key: "modelId",
          title: "设备型号",
          type: "select",
          placeholder: "请填写设备型号",
          options: this.gunModelList,
          optionLabel: "modelName",
          optionValue: "modelId",
        },
        {
          key: "brand",
          title: "品牌",
          type: "select",
          options: this.brandOptions,
          optionLabel: "brandName",
          optionValue: "brandId",
          placeholder: "请选择品牌",
        },
        {
          key: "equipTypeCode",
          title: "设备类型",
          type: "select",
          placeholder: "请选择设备类型",
          options: this.subTypeOptions,
          optionLabel: "dictLabel",
          optionValue: "dictValue",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        {
          key: "stationNo",
          title: "站点编码",
          type: "input",
          placeholder: "请填写站点编码",
        },
        {
          key: "orgNo",
          title: "所属大区",
          type: "select",
          placeholder: "请选择所属大区",
          options: this.deptOptionList,
        },
        {
          key: "areaCode",
          title: "省市区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区域",
        },
        {
          key: "operationMode",
          title: "运营类型",
          type: "select",
          placeholder: "请选择运营类型",
          options: this.operationModeOptions,
        },
        {
          key: "stationGradeId",
          title: "运维等级",
          type: "select",
          placeholder: "请选择运维等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },

        {
          key: "assetBuildIds",
          title: "资产单位",
          type: "select",
          placeholder: "请填写资产单位",
          options: this.assetList,
          multiple: true,
        },
        {
          key: "operationBuildIds",
          title: "运营单位",
          type: "select",
          placeholder: "请填写运营单位",
          options: this.operationList,
          multiple: true,
        },
        {
          key: "countTime",
          title: "统计时间",
          type: "slot",
          placeholder: "请选择工单类型",
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
