//充电订单
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      @handleExport="handleExport"
      :showExportButton="checkPermission(['maintenance:chargingOrder:export'])"
    >
      <!-- showExportButton -->
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="false"
        @handleSelectionChange="tableSelect"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <!-- <el-button size="mini" type="primary" @click.stop="handleChargingGun">
            充电枪
          </el-button> -->
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row, 'info')"
            v-has-permi="['maintenance:chargingOrder:detail']"
          >
            订单详情
          </el-button>
          <el-dropdown @command="(command) => handleCommand(command, row)">
            <el-button type="text" size="large">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="item.command"
                v-for="(item, index) in moreBtnList"
                :key="index"
                v-has-permi="[item.permission]"
              >
                <!-- v-has-permi="[item.permission]" -->
                <el-button type="text" size="large">
                  {{ item.title }}
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row, 'info')"
            :disabled="!checkPermission(['maintenance:chargingOrder:detail'])"
          >
            {{ row.orderId }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { regionData, provinceAndCityData } from "element-china-area-data";
import checkPermission from "@/utils/permission.js";
import {
  queryOrderList,
  exportExcel,
} from "@/api/operationMaintenanceManage/chargingOrder/index.js";
import { getAllDeptList } from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import { getToken } from "@/utils/auth";
import moment from "moment";
import { queryCityTree } from "@/api/common.js";
export default {
  name: "chargingOrder2",
  components: { AdvancedForm, GridTable },
  data() {
    return {
      // config: [],
      columns: [
        // {
        //   type: "checkbox",
        //   customWidth: 60,
        // },
        {
          field: "orderId",
          title: "订单号",
          slots: { default: "jump" },
        },
        {
          field: "partOrderNo",
          title: "第三方订单号",
          customWidth: 200,
        },
        {
          field: "subType",
          title: "充电类型",
        },
        {
          field: "operationMode",
          title: "运营类型",
        },
        {
          field: "deviceName",
          title: "设备名称",
        },
        {
          field: "deviceNo",
          title: "设备编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 200,
        },
        {
          field: "channel",
          title: "下单渠道",
        },
        {
          field: "createTime",
          title: "订单创建时间",
        },
        {
          field: "bgnTime",
          title: "充电开始时间",
        },
        {
          field: "endTime",
          title: "充电结束时间",
        },
        {
          field: "chargeTimes",
          title: "充电时长",
        },
        {
          field: "msg",
          title: "充电停止原因",
          customWidth: 200,
        },
        {
          field: "startSoc",
          title: "开始SOC(%)",
        },
        {
          field: "endSoc",
          title: "结束SOC(%)",
        },
        {
          field: "phone",
          title: "用户手机号",
        },
        {
          field: "licenseNo",
          title: "车牌号",
        },
        {
          field: "chargeStatus",
          title: "充电状态",
        },
        {
          field: "orderStatus",
          title: "订单状态",
          //           titlePrefix: {
          //             message: `订单状态定义
          // 结算中：订单充电结束但未结算
          // 待审核：该状态暂未使用
          // 待支付：用户提交订单后未支付
          // 待启动：用户提交订单并支付后，设备还未上电
          // 执行中：订单充电中
          // 待补收：该状态暂未使用
          // 交易完成：用户评价订单后
          // 待评价：订单充电结束后，用户还未评价
          // 退款中：用户支付订单后、充电停止后申请退款，或后台操作的退款，钱还未退还给用户
          // 交易取消：用户提交订单后取消了订单，属于无效订单
          //               `,
          //             icon: "vxe-icon-question-circle-fill",
          //           },
        },
        {
          field: "chargePq",
          title: "充电电量(kWh)",
        },

        {
          field: "chargeAmt",
          title: "订单总金额(元)",
        },
        {
          field: "elecAmt",
          title: "充电费(元)",
        },
        {
          field: "serviceAmt",
          title: "服务费(元)",
        },
        // {
        //   field: "pileName",
        //   title: "充电桩名称",
        // },
        // {
        //   field: "tariffType",
        //   title: "计费方式",
        //   formatter: ({ cellValue }) => {
        //     return (
        //       this.tariffTypeOptions?.find((x) => x.dictValue === cellValue)
        //         ?.dictLabel || cellValue
        //     );
        //   },
        // },
        // {
        //   field: "backAmt",
        //   title: "退款金额(元)",
        // },
        // {
        //   field: "discountAmt",
        //   title: "活动金额(元)",
        // },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        rangeTime: [],
      },
      total: 0,
      loading: false,
      tableId: "chargingStationList",
      recordList: [],
      chargingChannelOptions: [],
      orderStatusOptions: [],
      tariffTypeOptions: [],
      chargeStatusOptions: [],
      handRow: {
        stationIds: [],
      },
      deptOptionList: [],
      operationModeOptions: [],
      //操作列 更多下按钮
      moreBtnList: [
        {
          title: "充电数据",
          command: "charging",
          clickFn: (row) => {
            this.showDetail(row, "charging");
          },
          permission: "maintenance:chargingOrder:detail",
        },
        {
          title: "桩连平台日志",
          command: "log",
          clickFn: (row) => {
            this.showDetail(row, "log");
          },
          permission: "maintenance:chargingOrder:detail",
        },
      ],
      token: "",
      regionData: [],
    };
  },
  created() {
    this.searchForm.rangeTime = [
      moment()
        .subtract(14, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
    this.token = getToken();
    // this.regionData = provinceAndCityData;
  },
  activated() {
    this.getDicts("cm_operation_mode").then((response) => {
      this.operationModeOptions = response.data;
    });
    this.getDicts("CHARGE_STATUS").then((response) => {
      this.chargeStatusOptions = response.data;
    });
    this.getDicts("CHARGE_CHANNEL").then((response) => {
      this.chargingChannelOptions = response.data;
    });

    this.getDicts("charge_order_status").then((response) => {
      this.orderStatusOptions = response.data;
    });
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        rangeTime: [
          moment()
            .subtract(14, "days")
            .format("YYYY-MM-DD"),
          moment().format("YYYY-MM-DD"),
        ],
        ...this.$route.params,
      };
    }
    Promise.all([
      this.getDicts("TARIFF_TYPE").then((response) => {
        this.tariffTypeOptions = response.data;
      }),
      this.getDeptList(),
      this.getCityRegionData(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          // this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    handleCommand(command, row) {
      this.moreBtnList?.find((x) => x.command == command)?.clickFn(row);
    },
    //能投大区下拉选项
    getDeptList() {
      getAllDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    showDetail(row, tab) {
      this.$router.push({
        path: "/operationMaintenanceManage/chargingOrder/detail",
        query: {
          orderId: row.orderId,
          pileId: row.pileId,
          endTime: row.endTime,
          activeName: tab,
        },
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      if (Array.isArray(params.chargeRangeTime)) {
        params.chargeStartTime = params.chargeRangeTime[0] + " 00:00:00";
        params.chargeEndTime = params.chargeRangeTime[1] + " 23:59:59";
        delete params.chargeRangeTime;
      }
      if (Array.isArray(params.region)) {
        // params["provinceList"] = [];
        params["cityList"] = [];
        // params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            // params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            // params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      console.log("查询", params);
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      if (Array.isArray(params.chargeRangeTime)) {
        params.chargeStartTime = params.chargeRangeTime[0] + " 00:00:00";
        params.chargeEndTime = params.chargeRangeTime[1] + " 23:59:59";
        delete params.chargeRangeTime;
      }
      if (Array.isArray(params.region)) {
        // params["provinceList"] = [];
        params["cityList"] = [];
        // params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            // params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            // params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      console.log("查询", params);

      queryOrderList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        rangeTime: [
          moment()
            .subtract(14, "days")
            .format("YYYY-MM-DD"),
          moment().format("YYYY-MM-DD"),
        ],
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
    querySearchReason(queryString, cb) {
      cb([]);
      // queryPartnerList({
      //   pageNum: 1,
      //   pageSize: 9999,
      //   status: 0,
      //   partnerName: queryString,
      // }).then((res) => {
      //   const result = res.data;
      //   // this.partnerOptions = res.data;
      //   cb(result);
      // });
    },
  },
  computed: {
    config() {
      return [
        {
          key: "partOrderNo",
          title: "第三方订单号",
          type: "input",
          placeholder: "请输入第三方订单号",
        },
        {
          key: "deviceNo",
          title: "设备编码",
          type: "input",
          placeholder: "请输入设备编码",
        },
        {
          key: "channel",
          title: "下单渠道",
          type: "select",
          placeholder: "请选择下单渠道",
          options: this.chargingChannelOptions,
        },
        {
          key: "rangeTime",
          title: "订单创建时间",
          type: "dateRange",
          placeholder: "请选择订单创建时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "orderId",
          title: "订单号",
          type: "input",
          placeholder: "请输入订单号",
        },
        {
          key: "deviceName",
          title: "设备名称",
          type: "input",
          placeholder: "请输入设备名称",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "chargeRangeTime",
          title: "充电时间",
          type: "dateRange",
          placeholder: "请选择充电时间",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
        },
        {
          key: "phone",
          title: "用户手机号",
          type: "input",
          placeholder: "请输入用户手机号",
        },
        {
          key: "chargeStatus",
          title: "充电状态",
          type: "select",
          options: this.chargeStatusOptions,
          //   optionLabel: "modelNo",
          //   optionValue: "deviceModelId",
          placeholder: "请选择充电状态",
        },
        {
          key: "stationNo",
          title: "站点编号",
          type: "input",
          placeholder: "请输入站点编号",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: this.regionData, //省市数据,
          placeholder: "请选择省市",
          props: {
            checkStrictly: false,
            value: "areaCode",
            label: "areaName",
          },
        },
        // {
        //   key: "chargeEndMsg",
        //   title: "充电停止原因",
        //   type: "autocomplete",
        //   props: {
        //     fetchSuggestions: this.querySearchReason,
        //   },
        //   placeholder: "请输入充电停止原因",
        // },
        {
          key: "orderStatus",
          title: "订单状态",
          type: "select",
          options: this.orderStatusOptions,
          //   optionLabel: "modelNo",
          //   optionValue: "deviceModelId",
          placeholder: "请选择订单状态",
        },
        {
          key: "tariffType",
          title: "计费方式",
          type: "select",
          placeholder: "请选择计费方式",
          options: this.tariffTypeOptions,
          //   optionLabel: "modelNo",
          //   optionValue: "deviceModelId",
        },
        // {
        //   key: "operationMode",
        //   title: "运营类型",
        //   type: "select",
        //   placeholder: "请选择运营类型",
        //   options: this.operationModeOptions,
        // },
        // {
        //   key: "orgNo",
        //   title: "能投大区",
        //   type: "select",
        //   placeholder: "请选择能投大区",
        //   options: this.deptOptionList,
        // },
        // {
        //   key: "pileName",
        //   title: "充电桩",
        //   type: "input",
        //   placeholder: "请输入充电桩",
        // },

        // {
        //   key: "phone",
        //   title: "用户手机号",
        //   type: "input",
        //   placeholder: "请输入用户手机号",
        // },
      ];
    },
  },
};
</script>

<style></style>
