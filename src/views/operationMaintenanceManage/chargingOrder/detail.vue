//充电订单详情
<template>
  <div class="app-container">
    <div style="display: flex;align-items: center">
      <h4>充电订单编号：{{ orderId }}</h4>
      <el-tag style="margin-left: 10px">{{ chargeStatus }}</el-tag>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <!-- 订单信息tab-S -->
      <el-tab-pane label="订单信息" name="info">
        <!-- 订单基本信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>订单基本信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in infoList"
              :key="index"
              :label="item.title"
              ><el-button type="text" @click="item.method" v-if="item.jump">{{
                item.value
              }}</el-button>
              <el-tooltip :content="item.value" placement="top-start" v-else>
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 订单基本信息-E -->
        <!-- 充电信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item v-for="(item, index) in feeList" :key="index">
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 充电信息-E -->
        <!-- 交易信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>交易信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in chargingList"
              :key="index"
            >
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 交易信息-E -->
      </el-tab-pane>
      <!-- 订单信息tab-E -->
      <!-- 充电数据tab-S -->
      <el-tab-pane label="充电数据" name="charging">
        <!-- 电压 -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>电压（V）</span>
          </div>
          <LineChart
            :axisData="voltageTendObj.time"
            :serieData="voltageTendObj.tendencyArr"
            lineType="line"
            v-if="voltageTendObj.time && voltageTendObj.time.length > 0"
            :showZoomNum="999"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
        <!-- 电流 -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>电流（A）</span>
          </div>
          <LineChart
            :axisData="currentTendObj.time"
            :serieData="currentTendObj.tendencyArr"
            lineType="line"
            v-if="currentTendObj.time && currentTendObj.time.length > 0"
            :showZoomNum="999"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
        <!-- soc -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>SOC（%）</span>
          </div>
          <LineChart
            :axisData="socTendObj.time"
            :serieData="socTendObj.tendencyArr"
            lineType="line"
            v-if="socTendObj.time && socTendObj.time.length > 0"
            :showZoomNum="999"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>电量（k.Wh）</span>
          </div>
          <LineChart
            :axisData="chargeTendObj.time"
            :serieData="chargeTendObj.tendencyArr"
            lineType="line"
            chartStyle="height:320px"
            v-if="chargeTendObj.time && chargeTendObj.time.length > 0"
            :showZoomNum="999"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>功率（kW）</span>
          </div>
          <LineChart
            :axisData="powerTendObj.time"
            :serieData="powerTendObj.tendencyArr"
            lineType="line"
            chartStyle="height:320px"
            v-if="powerTendObj.time && powerTendObj.time.length > 0"
            :showZoomNum="999"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
      </el-tab-pane>
      <!-- 充电数据tab-E -->
      <!-- 操作记录tab-S -->
      <el-tab-pane label="操作记录" name="handle">
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电操作记录</span>
          </div>
          <Timeline :list="handleRecord"></Timeline>
        </el-card>
      </el-tab-pane>
      <!-- 操作记录tab-E -->
      <!-- 异常记录tab-S -->
      <el-tab-pane label="异常记录" name="record">
        <!-- 故障记录-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电异常记录</span>
          </div>
          <GridTable
            :columns="columns"
            :tableData="tableData"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="total"
            @changePage="getRecord"
            :loading="loading"
            :tableId="tableId"
          ></GridTable>
        </el-card>
        <!-- 故障记录-E -->
      </el-tab-pane>
      <!-- 异常记录tab-E -->
      <!-- 桩连平台日志tab-S -->
      <el-tab-pane label="桩连平台日志" name="log">
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>桩连平台日志</span>
          </div>
          <AdvancedForm
            :config="logConfig"
            :queryParams="logSearchForm"
            ref="AdvancedForm"
            @confirm="handleQuery"
            @resetQuery="resetQuery"
            v-if="logConfig.length"
            @handleExport="handleExport"
            :showExportButton="true"
            :rules="logRules"
          ></AdvancedForm>
          <GridTable
            :columns="logColumns"
            :tableData="logTableData"
            :currentPage.sync="logSearchForm.pageNum"
            :pageSize.sync="logSearchForm.pageSize"
            :total.sync="logTotal"
            @changePage="getPileLog"
            :loading="logLoading"
            tableId="chargingOrderLogTable"
          ></GridTable>
        </el-card>
      </el-tab-pane>
      <!-- 桩连平台日志tab-E -->
    </el-tabs>
  </div>
</template>

<script>
import GridTable from "@/components/GridTable/index.vue";
import LineChart from "@/components/Echarts/LineChart.vue";
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import checkPermission from "@/utils/permission.js";
import Timeline from "@/components/Timeline/index.vue";
import {
  queryOrderInfo,
  queryOrderRecord,
  queryChargingInfo,
  queryHandleRecord,
  queryPileLog,
  exportLogExcel,
} from "@/api/operationMaintenanceManage/chargingOrder/index.js";
import { getToken } from "@/utils/auth";
import moment from "moment";
export default {
  components: {
    GridTable,
    AdvancedForm,
    LineChart,
    Timeline,
  },
  data() {
    return {
      chargeStatus: "",
      columns: [
        {
          type: "seq",
          title: "序号",
        },
        {
          field: "type",
          title: "异常原因",
        },
        {
          field: "msg",
          title: "异常类型",
        },
        {
          field: "createTime",
          title: "异常上报时间",
        },
      ],
      logColumns: [
        {
          field: "createTime",
          title: "时间",
        },
        {
          field: "eType",
          title: "类型",
        },
        {
          field: "eMsgType",
          title: "消息类型",
          customWidth: 100,
          showOverflow: false,
        },
        {
          field: "eMetadata",
          title: "数据",
          customWidth: 300,
          showOverflow: false,
        },
        {
          field: "eData",
          title: "元数据",
          customWidth: 300,
          showOverflow: false,
        },
      ],
      tableData: [],
      dateRange: ["", ""],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        rangeTime: ["", ""],
      },
      total: 0,
      loading: false,
      logSearchForm: { pageNum: 1, pageSize: 10 },
      logTotal: 0,
      logLoading: false,
      logTableData: [],
      tableId: "chargingOrderRecordList",
      activeName: "info",
      orderId: undefined,
      infoList: [],
      feeList: [],
      chargingList: [],
      chargingTendObj: {
        time: [],
        tendencyArr: [],
      },
      voltageTendObj: {
        time: [],
        tendencyArr: [],
      },
      currentTendObj: {
        time: [],
        tendencyArr: [],
      },
      socTendObj: {
        time: [],
        tendencyArr: [],
      },
      chargeTendObj: {
        time: [],
        tendencyArr: [],
      },
      powerTendObj: {
        time: [],
        tendencyArr: [],
      },
      handleRecord: [],
      chargeChannelOptions: [],
      pileId: "",
      defaultEndTime: undefined,
      logRules: {
        rangeTime: [
          { required: true, message: "请选择时间范围", trigger: "blur" },
        ],
      },
      token: "",
      disabledCurrent: null,
      selectableRange1: "00:00:00 - 23:59:59",
      selectableRange2: "00:00:00 - 23:59:59",
      maxTime: "",
      chargeEndTime: "",
      chargeBeginTime: "",
    };
  },
  computed: {
    logConfig() {
      // const that = this;
      return [
        {
          key: "rangeTime",
          title: "时间范围",
          type: "datetimeRange",
          placeholder: "请选择时间范围",
          startPlaceholder: "开始时间",
          endPlaceholder: "结束时间",
          colNum: 12,
          clearable: false,
          props: {
            popperClass: "noClear",
            pickerOptions: {
              disabledDate: (time) => {
                // console.log(time, "disabledDate");
                if (
                  this.chargeBeginTime &&
                  moment(time).isBefore(this.chargeBeginTime, "day")
                ) {
                  return true;
                }
                if (
                  this.chargeEndTime &&
                  moment(time).isAfter(this.chargeEndTime, "day")
                ) {
                  return true;
                }
                return false;
              },
            },
          },
        },
        // {
        //   key: "startTime",
        //   title: "开始时间",
        //   type: "datetime",
        //   placeholder: "请选择开始时间",
        //   colNum: 12,
        //   clearable: false,
        //   props: {
        //     pickerOptions: {
        //       selectableRange: that.selectableRange1,
        //       disabledDate: (time) => {
        //         return false;
        //       },
        //     },
        //   },
        // },
        // {
        //   key: "endTime",
        //   title: "结束时间",
        //   type: "datetime",
        //   placeholder: "请选择结束时间",
        //   colNum: 12,
        //   clearable: false,
        //   props: {
        //     pickerOptions: {
        //       selectableRange: that.selectableRange2,
        //       disabledDate: (time) => {
        //         console.log(
        //           "disabledDate",
        //           time,
        //           that.maxTime,
        //           moment(time).isAfter(moment(that.maxTime), "day"),
        //           moment(time).isSame(
        //             moment(that.logSearchForm.startTime, "day")
        //           ),
        //           moment(time).isBefore(
        //             moment(that.logSearchForm.startTime, "day")
        //           )
        //         );
        //         return (
        //           moment(time).isAfter(moment(that.maxTime), "day") ||
        //           moment(time).isSame(
        //             moment(that.logSearchForm.startTime, "day")
        //           ) ||
        //           moment(time).isBefore(
        //             moment(that.logSearchForm.startTime, "day")
        //           )
        //         );
        //       },
        //     },
        //   },
        // },
      ];
    },
  },
  watch: {
    // "logSearchForm.startTime": {
    //   handler(newVal) {
    //     if (newVal) {
    //       this.maxTime = moment(newVal)
    //         .add(1, "hours")
    //         .format("YYYY-MM-DD HH:mm:ss");
    //       console.log(
    //         "time",
    //         moment(newVal).format("HH:mm:ss"),
    //         moment(newVal)
    //           .add(1, "hours")
    //           .format("HH:mm:ss")
    //       );
    //       this.selectableRange2 =
    //         moment(newVal).format("HH:mm:ss") +
    //         " - " +
    //         moment(newVal)
    //           .add(1, "hours")
    //           .format("HH:mm:ss");
    //     }
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },
  async created() {
    this.token = getToken();
    this.orderId = this.$route.query.orderId;
    this.pileId = this.$route.query.pileId;

    this.activeName = this.$route.query.activeName || "info";
    this.getDicts("chargeChannel").then((response) => {
      this.chargeChannelOptions = response.data;
    });
    await this.getInfo();
    this.setDefaultTime();
    this.handleClick({ name: this.activeName });
  },
  mounted() {},
  methods: {
    setDefaultTime() {
      const defaultEndTime =
        this.chargeEndTime || moment().format("YYYY-MM-DD HH:mm:ss");
      const beforeOneHour = moment(defaultEndTime)
        .subtract(1, "hours")
        .format("YYYY-MM-DD HH:mm:ss");
      //如果订单开始时间小于1小时，则默认开始时间为订单开始时间
      //如果订单开始时间大于1小时，则默认开始时间为结束时间前1小时
      const defaultBeginTime =
        this.chargeBeginTime &&
        moment(this.chargeBeginTime).isAfter(moment(beforeOneHour))
          ? this.chargeBeginTime
          : beforeOneHour;

      this.$set(this.logSearchForm, "rangeTime", [
        defaultBeginTime,
        moment(defaultEndTime).format("YYYY-MM-DD HH:mm:ss"),
      ]);
    },
    checkPermission,
    handleQuery() {
      const startTime = new Date(this.logSearchForm.rangeTime[0]);
      const endTime = new Date(this.logSearchForm.rangeTime[1]);

      // 计算时间差值（毫秒数）
      const timeDiff = endTime.getTime() - startTime.getTime();

      // 判断时间差值是否在一小时内
      if (timeDiff > 60 * 60 * 1000) {
        this.$message.warning("时间范围不能超过1小时");
        return;
      }
      if (
        (this.chargeBeginTime &&
          startTime.getTime() < new Date(this.chargeBeginTime).getTime()) ||
        (this.chargeEndTime &&
          endTime.getTime() > new Date(this.chargeEndTime).getTime())
      ) {
        this.$message.warning(
          `时间范围不能超过充电开始结束时间！开始时间：${this.chargeBeginTime ||
            "-"}，结束时间：${this.chargeEndTime || "-"}`
        );
        return;
      }

      this.logSearchForm.pageNum = 1;
      this.getPileLog();
    },
    resetQuery() {
      this.logSearchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.setDefaultTime();
      this.getPileLog();
    },
    getPileLog() {
      const params = {
        platFormOrderNo: this.orderId,
        platFormPileId: this.pileId,
        ...this.logSearchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0];
        params.endTime = params.rangeTime[1];
        delete params.rangeTime;
      }
      this.logLoading = true;
      console.log(params, "params");
      queryPileLog(params)
        .then((res) => {
          this.logLoading = false;
          if (res.code === "10000") {
            this.logTableData = res.data;
            this.logTotal = res.total;
          }
        })
        .catch(() => {
          this.logLoading = false;
        });
    },
    getChargingData() {
      queryChargingInfo({ orderId: this.orderId }).then((res) => {
        const { electricQuantityVOList, tendencyDataVOList } = res.data;
        this.voltageTendObj = {
          time: tendencyDataVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "输出电压（V）",
              data: tendencyDataVOList?.map((item) => item.outU),
              color: "#5d86e5",
            },
            // {
            //   name: "车端需求电压",
            //   data: tendencyDataVOList?.map((item) => item.outI),
            // },
          ],
        };
        this.currentTendObj = {
          time: tendencyDataVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "输出电流（A）",
              data: tendencyDataVOList?.map((item) => item.outI),
              color: "#5d86e5",
            },
            // {
            //   name: "车端需求电流",
            //   data: tendencyDataVOList?.map((item) => item.outI),
            // },
          ],
        };
        this.socTendObj = {
          time: electricQuantityVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "电池SOC（%）",
              data: electricQuantityVOList?.map((item) => item.soc),
              color: "#61d27e",
            },
          ],
        };
        this.chargeTendObj = {
          time: electricQuantityVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "充电量（k.Wh）",
              data: electricQuantityVOList?.map((item) => item.edPq),
              color: "#367750",
            },
          ],
        };
        this.powerTendObj = {
          time: tendencyDataVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "功率（kW）",
              data: tendencyDataVOList?.map((item) => item.outP),
              color: "#e8d467",
            },
          ],
        };
      });
    },
    getHandleRecord() {
      console.log(this.chargeChannelOptions, "chargeChannelOptions");
      queryHandleRecord({ orderId: this.orderId }).then((res) => {
        this.handleRecord = res.data.map((x) => {
          return {
            ...x,
            operateDetail: "说明：" + x.msg,
            operateType: this.chargeChannelOptions?.find(
              (i) => i.dictValue === x.type
            )?.dictLabel,
          };
        });
      });
    },
    async getInfo() {
      const res = await queryOrderInfo({ orderId: this.orderId });
      if (res?.code === "10000") {
        // const {infoObj,feeList,chargingList}=res.data
        this.chargeStatus = res.data.chargeStatus;
        this.chargeBeginTime = res.data?.bgnTime;
        this.chargeEndTime = res.data?.endTime;
        this.infoList = [
          { title: "用户手机号", value: res.data?.phone },
          { title: "车牌号", value: res.data?.licenseNo },
          { title: "下单渠道", value: res.data?.channel },
          { title: "计费方式", value: res.data?.tariffType },
          { title: "创建订单时间", value: res.data?.createTime },
          { title: "订单状态", value: res.data?.orderStatus },
          { title: "第三方订单号", value: res.data?.partOrderNo },
          { title: "订单号", value: res.data?.orderId },
          {
            title: "设备编码",
            value: res.data?.deviceNo,
            jump: true,
            method: () => {
              this.$router.push({
                path: "/operationMaintenanceManage/deviceList/gunDetail",
                query: {
                  pileId: res.data?.pileId,
                  gunId: res.data?.gunId,
                  stationNo: res.data?.stationNo,
                  deviceNo: res.data?.deviceNo,
                },
              });
            },
          },
          {
            title: "设备名称",
            value: res.data?.deviceName,
            jump: true,
            method: () => {
              this.$router.push({
                path: "/operationMaintenanceManage/deviceList/gunDetail",
                query: {
                  pileId: res.data?.pileId,
                  gunId: res.data?.gunId,
                  stationNo: res.data?.stationNo,
                  deviceNo: res.data?.deviceNo,
                },
              });
            },
          },
          { title: "充电类型", value: res.data?.subType },
          { title: "运营类型", value: res.data?.operationMode },
          {
            title: "站点编码",
            value: res.data?.stationNo,
            jump: res.data?.wbtStationId,
            method: () => {
              this.$router.push({
                path: "/operationMaintenanceManage/station/newDetailPage",
                query: {
                  stationId: res.data?.wbtStationId,
                  projectId: res.data?.projectId,
                  stationCode: res.data?.stationNo,
                  stationName: res.data?.stationName,
                },
              });
            },
          },
          {
            title: "站点名称",
            value: res.data?.stationName,
            jump: res.data?.wbtStationId,
            method: () => {
              this.$router.push({
                path: "/operationMaintenanceManage/station/newDetailPage",
                query: {
                  stationId: res.data?.wbtStationId,
                  projectId: res.data?.projectId,
                  stationCode: res.data?.stationNo,
                  stationName: res.data?.stationName,
                },
              });
            },
          },
          { title: "地址", value: res.data?.stationAddress },
          // {
          //   title: "充电桩编号",
          //   value: res.data?.pileNo,
          //   jump: true,
          //   method: () => {
          //     this.$router.push({
          //       path: "/chargingStation/components/stationDetail",
          //       query: {
          //         pileNo: res.data?.pileNo,
          //         pileId: res.data?.pileId,
          //         stationNo: res.data?.stationNo,
          //       },
          //     });
          //   },
          // },
          // {
          //   title: "充电桩名称",
          //   value: res.data?.pileName,
          //   jump: true,
          //   method: () => {
          //     this.$router.push({
          //       path: "/chargingStation/components/stationDetail",
          //       query: {
          //         pileNo: res.data?.pileNo,
          //         pileId: res.data?.pileId,
          //         stationNo: res.data?.stationNo,
          //       },
          //     });
          //   },
          // },
        ];
        this.feeList = [
          { title: "充电状态", value: res.data?.chargeStatus },
          { title: "充电时长（分钟）", value: res.data?.chargeTimes },
          { title: "启动方式", value: "" },
          { title: "电池电量（%）", value: "" },
          { title: "尖电量（k.Wh）", value: res.data?.sharpPq },
          { title: "峰电量（k.Wh）", value: res.data?.peakPq },
          { title: "平电量（k.Wh）", value: res.data?.flatPq },
          { title: "谷电量（k.Wh）", value: res.data?.valleyPq },
          { title: "充电开始时间", value: res.data?.bgnTime },
          { title: "充电结束时间", value: res.data?.endTime },
          { title: "开始SOC（%）", value: res.data?.startSoc },
          { title: "结束SOC（%）", value: res.data?.endSoc },
          { title: "充电停止原因", value: res.data?.chargeStopRemark },
          { title: "充电停止原因明细", value: res.data?.chargeStopRemark },
          { title: "充电电量（k.Wh）", value: res.data?.chargePq },
          { title: "", value: "" },
        ];
        this.chargingList = [
          { title: "订单总金额（元）", value: res.data?.chargeAmt },
          { title: "充电费（元）", value: res.data?.elecAmt },
          { title: "服务费（元）", value: res.data?.serviceAmt },
          { title: "退款金额（元）", value: "" },
          { title: "活动金额（元）", value: "" },
          { title: "是否异常结算", value: res.data?.isAbnormalSettlement },
          { title: "是否人工结算", value: res.data?.isManualSettlement },
          { title: "人工结算时间", value: res.data?.settlementTime },
          { title: "异常原因", value: res.data?.abnormalMsg },
          { title: "", value: "" },
          { title: "", value: "" },
          { title: "", value: "" },
        ];
      }
    },
    getRecord() {
      const params = {
        orderId: this.orderId,
        ...this.searchForm,
      };
      this.loading = true;
      console.log(params, "params");
      queryOrderRecord(params).then((res) => {
        if (res.code === "10000") {
          this.tableData = res.data;
          this.total = res.total;
          this.loading = false;
        } else {
          // this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    handleClick(val) {
      const arr = [
        { name: "info", method: "getInfo" },
        { name: "charging", method: "getChargingData" },
        { name: "handle", method: "getHandleRecord" },
        { name: "record", method: "getRecord" },
        { name: "log", method: "getPileLog" },
      ];
      const { method } = arr.find((x) => x.name === val.name);
      method && this[method]();
      console.log(val.name, "点击");
      // if (val.name === "info") {
      //   this.getInfo();
      // } else {
      //   this.getRecord();
      // }
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    handleExport() {
      let text = "是否确认导出所有数据?";
      const params = {
        platFormOrderNo: this.orderId,
        platFormPileId: this.pileId,
        ...this.logSearchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0];
        params.endTime = params.rangeTime[1];
        delete params.rangeTime;
      }
      console.log("查询", params);
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportLogExcel(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    handleDateChange() {
      this.searchForm = { pageNum: 1, pageSize: 10 };
      this.getRecord();
    },
  },
};
</script>

<style lang="less" scoped>
.descriptions {
  margin: 0 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    // max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
  }
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic .head {
  margin-bottom: 12px;
}
</style>
