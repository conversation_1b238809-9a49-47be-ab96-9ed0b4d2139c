<template>
  <el-dialog
    title="评价标准配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    width="600px"
    destroy-on-close
  >
    <el-form :model="form" ref="form" label-width="80px" :rules="rules">
      <el-form-item label="仓库名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="所在城市">
        <el-cascader
          v-model="form.city"
          :options="regionData"
          style="width: 100%"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input v-model="form.address" />
      </el-form-item>
      <el-form-item label="负责人" prop="fzr">
        <el-input v-model="form.fzr" />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="form.phone" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click.stop="closeVisible">取消 </el-button>
      <el-button :loading="loading" type="primary" @click="submit"
        >确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { regionData } from "element-china-area-data";
export default {
  data() {
    return {
      regionData,
      visible: false,
      form: {
        name: null,
        desc: null,
        points: undefined,
      },

      loading: false,
      rules: {
        name: [{ required: true, message: "请输入物料名称", trigger: "blur" }],
        city: [{ required: true, message: "请选择所在城市", trigger: "blur" }],
        address: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        fzr: [{ required: true, message: "请输入负责人", trigger: "blur" }],
        phone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
      },
    };
  },
  methods: {
    closeVisible() {
      this.visible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          //   const params = {
          //     ...this.form,
          //     orderNo: this.orderNo,
          //     orderId: this.orderId,
          //   };
          //   check(params)
          //     .then((res) => {
          //       this.loading = false;
          //       this.$message.success("审核工单成功");
          //       this.visible = false;
          //       this.getList();

          //       this.reportAuditTrack();
          //     })
          //     .catch((err) => {
          //       this.loading = false;
          //     });
        }
      });
    },
    open() {
      this.visible = true;
    },
  },
};
</script>
