<!-- 物料使用情况 -->
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['maintenance:report:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
        <template slot="status" slot-scope="{ row }">
          <el-switch v-model="row.status" />
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['maintenance:report:detail']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['maintenance:report:detail']"
          >
            查看
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <Dialog ref="dialog" />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";

import { getToken } from "@/utils/auth";

import {
  queryOrderReportlist,
  exportReport,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import {
  orderTypeList,
  consumptionTypeList,
  warehouseList,
} from "./constant.ts";

export default {
  name: "usage",
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      stationId: undefined,
      columns: [
        {
          field: "orderNo",
          title: "物料消耗类型",
        },

        {
          field: "deviceName",
          title: "物料名称",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "x",
          title: "物料编码",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "count",
          title: "数量",
          showOverflowTooltip: true,
        },
        {
          field: "a",
          title: "回收仓库",
          showOverflowTooltip: true,
        },

        {
          field: "t",
          showOverflowTooltip: true,
          title: "可用状态",
        },
        {
          field: "x",
          showOverflowTooltip: true,
          title: "置换金额",
          customWidth: 150,
        },
        {
          field: "xt",
          title: "备注",
        },
        {
          field: "zhje",
          title: "工单编号",
        },
        {
          field: "zhjea",
          title: "工单类型",
        },
        {
          field: "zhj2e",
          title: "工单处理人",
        },
        {
          field: "zhj1e",
          title: "工单处理时间",
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderTypeName: "",
        orderTypeParentName: "",
        threeOrderTypeName: "",
        oneFaultType: "",
        threeFaultType: "",
        twoFaultType: "",
      },
      total: 0,
      loading: false,
      rowInfo: {},
      pageType: "detail",
      projectId: undefined,
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      submitLoading: false,
    };
  },
  async created() {},
  mounted() {
    this.token = getToken();
  },
  methods: {
    checkPermission,
    handleBeforeUpload(file) {
      const isLt2G = file.size / 1024 / 1024 / 1024 < 2;
      if (!isLt2G) {
        this.$message.error("上传的文件大小不能超过2G!");
      }
      return isLt2G;
    },

    handleExport() {
      const params = this.getQueryParams();
      this.$confirm("是否确认导出所有数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportReport(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getQueryParams() {
      const {
        orderNo,
        handleUserName,
        channel,
        auditUserNickName,
        stationName,
        deptName,
        deviceName,
        deviceNo,
        region = [],
        createTime,
        handleTime,
        auditTime,
        pageNum,
        pageSize,
      } = this.searchForm;

      const provinceList = [];
      const cityList = [];
      const countyList = [];
      region.forEach((i) => {
        i[0] && provinceList.push(i[0]);
        i[1] && cityList.push(i[1]);
        i[2] && countyList.push(i[2]);
      });

      const startCreateTime = createTime?.[0]
        ? createTime[0] + " 00:00:00"
        : null;
      const endCreateTime = createTime?.[1]
        ? createTime?.[1] + " 23:59:59"
        : null;
      const startHandleTime = handleTime?.[0]
        ? handleTime?.[0] + " 00:00:00"
        : null;
      const endHandleTime = handleTime?.[1]
        ? handleTime?.[1] + " 23:59:59"
        : null;
      const startAuditTime = auditTime?.[0]
        ? auditTime?.[0] + " 00:00:00"
        : null;
      const endAuditTime = auditTime?.[1] ? auditTime?.[1] + " 23:59:59" : null;

      const params = {
        orderNo,
        handleUserName,

        channel,
        auditUserNickName,
        stationName,
        deptName,
        deviceName,
        deviceNo,
        provinceList: [...new Set(provinceList)],
        cityList: [...new Set(cityList)],
        countyList: [...new Set(countyList)],
        startCreateTime,
        endCreateTime,
        startHandleTime,
        endHandleTime,
        startAuditTime,
        endAuditTime,
        pageNum,
        pageSize,
      };

      return params;
    },
    getList() {
      this.loading = true;
      let params = this.getQueryParams();
      queryOrderReportlist(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {},

    close() {
      this.tagManageVisible = false;
    },
  },
  computed: {
    config() {
      return [
        {
          key: "name",
          title: "物料名称",
          type: "input",
          placeholder: "请填写物料名称",
        },
        {
          key: "materielNo",
          title: "物料编码",
          type: "input",
          placeholder: "请填写物料编码",
        },
        {
          key: "orderType",
          title: "工单类型",
          type: "select",
          placeholder: "请选择工单类型",
          options: orderTypeList,
        },
        {
          key: "consumptionType",
          title: "物料消耗类型",
          type: "select",
          placeholder: "请选择物料消耗类型",
          options: consumptionTypeList,
        },
        {
          key: "warehouse",
          title: "回收仓库",
          type: "select",
          placeholder: "请选择回收仓库",
          options: warehouseList,
        },
        {
          key: "operator",
          title: "处理人",
          type: "input",
          placeholder: "请输入处理人",
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
