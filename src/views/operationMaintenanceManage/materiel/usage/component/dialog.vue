<template>
  <el-dialog
    title="评价标准配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    width="600px"
    destroy-on-close
  >
    <el-form :model="form" ref="form" label-width="80px" :rules="rules">
      <el-form-item label="物料编码" prop="name">
        <el-input v-model="form.materielNo" />
      </el-form-item>
      <el-form-item label="物料名称" prop="points">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="单位" prop="desc">
        <el-select style="width: 100%"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click.stop="closeVisible">取消 </el-button>
      <el-button :loading="loading" type="primary" @click="submit"
        >确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      form: {
        name: null,
        desc: null,
        points: undefined,
      },

      loading: false,
      rules: {
        name: [{ required: true, message: "请输入物料名称", trigger: "blur" }],
        materielNo: [{ required: true, message: "请输入物料编码", trigger: "blur" }],
        desc: [{ required: true, message: "请输入单位", trigger: "blur" }],
      },
    };
  },
  methods: {
    closeVisible() {
      this.visible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          //   const params = {
          //     ...this.form,
          //     orderNo: this.orderNo,
          //     orderId: this.orderId,
          //   };
          //   check(params)
          //     .then((res) => {
          //       this.loading = false;
          //       this.$message.success("审核工单成功");
          //       this.visible = false;
          //       this.getList();

          //       this.reportAuditTrack();
          //     })
          //     .catch((err) => {
          //       this.loading = false;
          //     });
        }
      });
    },
    open() {
      this.visible = true;
    },
  },
};
</script>
