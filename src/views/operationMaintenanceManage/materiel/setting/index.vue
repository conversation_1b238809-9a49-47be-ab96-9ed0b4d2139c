<!-- 物料管理 -->
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['materiel:setting:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click="openConfig"
            v-has-permi="['materiel:setting:add']"
            >新增
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
            :disabled="!checkPermission(['materiel:setting:changeStatus'])"
          >
          </el-switch>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="openConfig(row)"
            v-has-permi="['materiel:setting:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="deleteRow(row)"
            v-has-permi="['materiel:setting:delete']"
          >
            删除
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <Dialog ref="dialog" @submitSuccess="getList" />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";

import { getToken } from "@/utils/auth";

import api from "@/api/operationMaintenanceManage/materiel/index.js";
import { statusList } from "./constant.ts";
import Dialog from "./component/dialog.vue";

export default {
  name: "materiel-setting",
  components: {
    AdvancedForm,
    GridTable,
    Dialog,
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/excel/stationGradeImport",
        updateAsCode: "",
      },
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      stationId: undefined,
      columns: [
        {
          field: "materialNo",
          title: "物料编码",
        },

        {
          field: "materialName",
          title: "物料名称",
          customWidth: 180,
        },
        {
          field: "materialModel",
          title: "规格型号",
          customWidth: 180,
          attrs: {
            rows: 1,
            maxlength: 50,
            showWordLimit: true,
            placeholder: "50个字符以内",
          },
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          customWidth: 180,
          attrs: {
            rows: 1,
            maxlength: 50,
            showWordLimit: true,
            placeholder: "50个字符以内",
          },
        },
        {
          field: "unit",
          title: "单位",
          formatter: ({ cellValue }) => {
            return this.unitOptions?.find((x) => x.dictValue === cellValue)
              ?.dictLabel;
          },
          customWidth: 180,
        },
        {
          field: "status",
          title: "状态",
          slots: { default: "status" },
        },
        {
          field: "createName",
          title: "创建人",
        },

        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "updateName",
          title: "修改人",
          customWidth: 150,
        },
        {
          field: "updateTime",
          title: "修改时间",
        },
        {
          title: "操作",
          minWidth: 150,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      rowInfo: {},
      pageType: "detail",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      submitLoading: false,
      unitOptions: [],
    };
  },
  async created() {
    this.getDicts("material_unit").then((response) => {
      this.unitOptions = response?.data;
    });
    this.getList();
  },
  mounted() {
    this.token = getToken();
  },
  methods: {
    checkPermission,
    handleStatusChange(row) {
      const text = row.status == "0" ? "启用" : "停用";
      api
        .changeStatus({ materialId: row.materialId, status: row.status })
        .then((res) => {
          if (res.code == "10000") {
            this.msgSuccess(text + "成功");
            this.getList();
          } else {
            row.status = row.status == "1" ? "0" : "1";
          }
        })
        .catch(function() {
          row.status = row.status == "1" ? "0" : "1";
        });
    },
    deleteRow(row) {
      this.$confirm("确定删除该物料吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        const res = await api.delete({ materialId: row.materialId });
        if (res.code === "10000") {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getList();
        }
      });
    },
    openConfig(row) {
      this.$refs.dialog.open(row);
    },

    handleExport() {
      const params = {
        ...this.searchForm,
      };
      this.$confirm("是否确认导出所有数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        api.exportReport(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      api
        .getTableData(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
  computed: {
    config() {
      return [
        {
          key: "materialName",
          title: "物料名称",
          type: "input",
          placeholder: "请填写物料名称",
        },
        {
          key: "materialNo",
          title: "物料编码",
          type: "input",
          placeholder: "请填写物料编码",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "请选择状态",
          options: statusList,
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
