<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    width="50%"
    destroy-on-close
  >
    <DynamicForm
      ref="dyForm"
      :config="formConfig"
      :params="formParams"
      :defaultColSpan="24"
      labelPosition="right"
      labelWidth="120px"
    ></DynamicForm>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click.stop="closeVisible">取消 </el-button>
      <el-button :loading="loading" type="primary" @click="submit"
        >确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { initParams } from "@/utils/buse.js";
import api from "@/api/operationMaintenanceManage/materiel/index.js";

export default {
  data() {
    return {
      visible: false,
      loading: false,
      formParams: {},
      unitOptions: [],
      title: "新增",
    };
  },
  computed: {
    formConfig() {
      return [
        {
          field: "materialNo",
          title: "物料编码",
          attrs: { maxlength: 30 },
          rules: [{ required: true, message: "请输入物料编码" }],
        },
        {
          field: "materialName",
          title: "物料名称",
          attrs: { maxlength: 30 },
          rules: [{ required: true, message: "请输入物料名称" }],
        },
        {
          field: "materialModel",
          title: "规格型号",
          attrs: { maxlength: 50 },
          rules: [{ required: true, message: "请输入规格型号" }],
        },
        {
          field: "materialFactory",
          title: "生产厂家",
          attrs: { maxlength: 50 },
          rules: [{ required: true, message: "请输入生产厂家" }],
        },
        {
          field: "unit",
          title: "单位",
          element: "el-select",
          props: {
            options: this.unitOptions,
            optionValue: "dictValue",
            optionLabel: "dictLabel",
            filterable: true,
          },
          rules: [{ required: true, message: "请选择单位" }],
        },
      ];
    },
  },
  created() {
    this.getDicts("material_unit").then((response) => {
      this.unitOptions = response?.data;
    });
  },
  methods: {
    closeVisible() {
      this.visible = false;
    },
    submit() {
      this.$refs.dyForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const params = {
            ...this.formParams,
          };
          api
            .update(params)
            .then((res) => {
              this.loading = false;
              if (res?.code === "10000") {
                this.$message.success("提交成功");
                this.visible = false;
                this.$emit("submitSuccess");
              }
            })
            .catch((err) => {
              this.loading = false;
            });
        }
      });
    },
    open(row) {
      this.visible = true;
      this.title = row.materialId ? "编辑" : "新增";
      this.formParams = { ...initParams(this.formConfig), ...row };
    },
  },
};
</script>
