<template>
  <el-dialog
    title="评价标准配置"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeVisible"
    append-to-body
    width="600px"
    destroy-on-close
  >
    <el-form :model="form" ref="form" label-width="120px" :rules="rules">
      <el-form-item label="费用分类" prop="costType">
        <el-select
          v-model="form.name"
          :options="costTypeList"
          style="width: 95%"
        />
      </el-form-item>
      <el-form-item label="工单类型" prop="orderType">
        <el-select
          v-model="form.orderType"
          :options="orderTypeList"
          style="width: 95%"
        />
      </el-form-item>
      <el-form-item label="工作内容" prop="workContent">
        <el-input v-model="form.address" style="width: 95%" />
      </el-form-item>
      <el-form-item label="服务类别1" prop="serviceType1">
        <el-input v-model="form.fzr" style="width: 95%" />
      </el-form-item>
      <el-form-item label="服务类别2" prop="serviceType2">
        <el-input v-model="form.phone" style="width: 95%" />
      </el-form-item>
      <el-form-item label="其他费用类型" prop="otherCostType">
        <el-input v-model="form.phone" style="width: 95%" />
      </el-form-item>
      <el-form-item label="次数" prop="otherCostType">
        <el-input-number
          v-model="form.phone"
          :precision="0"
          controls-position="right"
          style="width: 95%"
        />次
      </el-form-item>
      <el-form-item label="标准工时" prop="workTime">
        <el-input v-model="form.phone" style="width: 95%" />h
      </el-form-item>
      <el-form-item label="费用" prop="costPrice">
        <el-input-number
          v-model="form.phone"
          :precision="2"
          controls-position="right"
          style="width: 95%"
        />元
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button :loading="loading" @click.stop="closeVisible">取消 </el-button>
      <el-button :loading="loading" type="primary" @click="submit"
        >确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { regionData } from "element-china-area-data";
import { orderTypeList, costTypeList } from "../constant.ts";

export default {
  data() {
    return {
      orderTypeList,
      costTypeList,
      regionData,
      visible: false,
      form: {
        name: null,
        desc: null,
        points: undefined,
      },

      loading: false,
      rules: {
        name: [{ required: true, message: "请输入物料名称", trigger: "blur" }],
        city: [{ required: true, message: "请选择所在城市", trigger: "blur" }],
        address: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        fzr: [{ required: true, message: "请输入负责人", trigger: "blur" }],
        phone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
      },
    };
  },
  methods: {
    closeVisible() {
      this.visible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          //   const params = {
          //     ...this.form,
          //     orderNo: this.orderNo,
          //     orderId: this.orderId,
          //   };
          //   check(params)
          //     .then((res) => {
          //       this.loading = false;
          //       this.$message.success("审核工单成功");
          //       this.visible = false;
          //       this.getList();

          //       this.reportAuditTrack();
          //     })
          //     .catch((err) => {
          //       this.loading = false;
          //     });
        }
      });
    },
    open() {
      this.visible = true;
    },
  },
};
</script>
