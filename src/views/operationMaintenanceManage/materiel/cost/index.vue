<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="true"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="'orderId'"
        :batchDelete="true"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button size="mini" type="primary" @click="openConfig"
            >新增
          </el-button>
        </template>
        <template slot="status" slot-scope="{ row }">
          <el-switch v-model="row.status" />
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showDetail(row)">
            编辑
          </el-button>
          <el-button type="text" size="large" @click="showDetail(row)">
            删除
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <Dialog ref="dialog" />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";

import { getToken } from "@/utils/auth";

import {
  queryOrderReportlist,
  exportReport,
  getReportPDF,
} from "@/api/operationMaintenanceManage/operationWorkOrder/index.js";
import { statusList, orderTypeList, costTypeList } from "./constant.ts";
import Dialog from "./component/dialog.vue";

export default {
  name: "materiel-cost",
  components: {
    AdvancedForm,
    GridTable,
    Dialog,
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/excel/stationGradeImport",
        updateAsCode: "",
      },
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      stationId: undefined,
      columns: [
        {
          field: "orderNo",
          title: "费用分类",
        },

        {
          field: "city",
          title: "工单类型",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "address",
          title: "工作内容",
          showOverflowTooltip: true,
          customWidth: 180,
        },
        {
          field: "fzr",
          title: "费用类别1",
          showOverflowTooltip: true,
        },
        {
          field: "a",
          title: "费用类别2",
          showOverflowTooltip: true,
        },

        {
          field: "status",
          showOverflowTooltip: true,
          title: "其他费用类型",
        },
        {
          field: "x",
          showOverflowTooltip: true,
          title: "次数",
          customWidth: 150,
        },
        {
          field: "xt",
          title: "标准工时",
        },
        {
          field: "xgr",
          showOverflowTooltip: true,
          title: "费用",
          customWidth: 150,
        },
        {
          field: "xg",
          title: "状态",
        },
        {
          field: "creator",
          title: "创建人",
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "updater",
          title: "修改人",
        },
        {
          field: "updateTime",
          title: "修改时间",
        },
        {
          title: "操作",
          minWidth: 150,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        orderTypeName: "",
        orderTypeParentName: "",
        threeOrderTypeName: "",
        oneFaultType: "",
        threeFaultType: "",
        twoFaultType: "",
      },
      total: 0,
      loading: false,
      rowInfo: {},
      pageType: "detail",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      submitLoading: false,
    };
  },
  async created() {},
  mounted() {
    this.token = getToken();
  },
  methods: {
    checkPermission,
    handleBeforeUpload(file) {
      const isLt2G = file.size / 1024 / 1024 / 1024 < 2;
      if (!isLt2G) {
        this.$message.error("上传的文件大小不能超过2G!");
      }
      return isLt2G;
    },

    openConfig() {
      this.$refs.dialog.open();
    },

    handleExport() {
      const params = this.getQueryParams();
      this.$confirm("是否确认导出所有数据", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportReport(params).then(() => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },

    showDetail(row) {
      getReportPDF(row.orderNo).then((res) => {
        if (res.code === "10000") {
          window.open(res.data);
        } else {
          this.$message.error(res.message);
        }
      });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getQueryParams() {
      const {
        orderNo,
        handleUserName,
        channel,
        auditUserNickName,
        stationName,
        deptName,
        deviceName,
        deviceNo,
        region = [],
        createTime,
        handleTime,
        auditTime,
        pageNum,
        pageSize,
      } = this.searchForm;

      const provinceList = [];
      const cityList = [];
      const countyList = [];
      region.forEach((i) => {
        i[0] && provinceList.push(i[0]);
        i[1] && cityList.push(i[1]);
        i[2] && countyList.push(i[2]);
      });

      const startCreateTime = createTime?.[0]
        ? createTime[0] + " 00:00:00"
        : null;
      const endCreateTime = createTime?.[1]
        ? createTime?.[1] + " 23:59:59"
        : null;
      const startHandleTime = handleTime?.[0]
        ? handleTime?.[0] + " 00:00:00"
        : null;
      const endHandleTime = handleTime?.[1]
        ? handleTime?.[1] + " 23:59:59"
        : null;
      const startAuditTime = auditTime?.[0]
        ? auditTime?.[0] + " 00:00:00"
        : null;
      const endAuditTime = auditTime?.[1] ? auditTime?.[1] + " 23:59:59" : null;

      const params = {
        orderNo,
        handleUserName,

        channel,
        auditUserNickName,
        stationName,
        deptName,
        deviceName,
        deviceNo,
        provinceList: [...new Set(provinceList)],
        cityList: [...new Set(cityList)],
        countyList: [...new Set(countyList)],
        startCreateTime,
        endCreateTime,
        startHandleTime,
        endHandleTime,
        startAuditTime,
        endAuditTime,
        pageNum,
        pageSize,
      };

      return params;
    },
    getList() {
      this.loading = true;
      let params = this.getQueryParams();
      queryOrderReportlist(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {},

    close() {
      this.tagManageVisible = false;
    },
  },
  computed: {
    config() {
      return [
        {
          key: "costType",
          title: "费用分类",
          type: "select",
          options: costTypeList,
          placeholder: "请选择费用分类",
        },
        {
          key: "orderType",
          title: "工单类型",
          type: "select",
          options: orderTypeList,
          placeholder: "请选择工单类型",
        },
        {
          key: "status",
          title: "状态",
          type: "select",
          placeholder: "请选择状态",
          options: statusList,
        },
      ];
    },
  },
};
</script>

<style scoped lang="less"></style>
