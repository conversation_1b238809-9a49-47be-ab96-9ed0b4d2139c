<template>
  <el-dialog
    title="详情"
    :visible.sync="visible"
    @close="closeDialog"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
  >
    <el-form :model="form" ref="form" label-width="110px" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="站点编码" prop="stationCode">
            <el-input
              placeholder="请输入站点编码"
              v-model="form.stationCode"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点名称" prop="stationName">
            <el-input
              placeholder="请输入站点名称"
              v-model="form.stationName"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="充电桩数量" prop="pileNum">
            <el-input
              placeholder="请输入充电桩数量"
              v-model="form.pileNum"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点类型" prop="stationType">
            <el-select
              v-model="form.stationType"
              placeholder="站点类型"
              clearable
              size="mini"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in stationTypeDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行政区" prop="provinceCityCounty">
            <el-cascader
              v-model="form.provinceCityCounty"
              placeholder="请选择"
              :options="districtOptions"
              clearable
              filterable
              size="mini"
              disabled
              style="width: 100%"
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="详细地址" prop="stationAddress">
            <el-input
              placeholder="请输入详细地址"
              v-model="form.stationAddress"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="签到半径" prop="radius">
            <el-input-number
              placeholder="请输入详细地址"
              v-model="form.radius"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="经纬度" prop="longLat" class="radius-wrap append-btn">
            <el-input v-model="form.longLat" size="mini" disabled >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="运营状态" prop="status">
            <el-select
              v-model="form.status"
              placeholder="请选择运营状态"
              clearable
              size="mini"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in stationStatusDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建设时间" prop="buildDate">
            <el-date-picker
              v-model="form.buildDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投运时间" prop="openDate">
            <el-date-picker
              v-model="form.openDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上线时间" prop="onlineDate">
            <el-date-picker
              v-model="form.onlineDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              disabled
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否对外开放" prop="openFlag">
            <el-select
              v-model="form.openFlag"
              placeholder="是否对外开放"
              clearable
              size="mini"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in openFlagDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点充电分类" prop="stationChargeType">
            <el-select
              v-model="form.stationChargeType"
              placeholder="是否对外开放"
              clearable
              size="mini"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in stationChargeTypeDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建设场所" prop="construction">
            <el-select
              v-model="form.construction"
              placeholder="是否对外开放"
              clearable
              size="mini"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in constructionDict"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务电话" prop="serviceTel">
            <el-input
              placeholder="请输入服务电话"
              v-model="form.serviceTel"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点运营商" prop="operatorCode">
            <el-select
              v-model="form.operatorCode"
              placeholder="请选择站点运营商"
              clearable
              size="mini"
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="dict in providerList"
                :key="dict.providerCode"
                :label="dict.providerName"
                :value="dict.providerCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="车位数量" prop="parkNum">
            <el-input-number
              placeholder="请输入车位数量"
              v-model="form.parkNum"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点备注" prop="remark">
            <el-input
              placeholder="请输入站点备注"
              v-model="form.remark"
              style="width: 100%;"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import { regionData } from "element-china-area-data";
import { providerList } from "@/api/provider/provider"

export default {
  name: "detail",
  props: ["visible", "rowInfo"],
  data() {
    return {
      form: this.rowInfo,
      rules: {},
      stationTypeDict: [],
      stationStatusDict: [],
      openFlagDict: [],
      stationChargeTypeDict: [],
      constructionDict: [],
      providerList: [],
      districtOptions: regionData, //省市数据
    };
  },
  async created() {
    //站点类型
    await this.getDicts("cm_station_type").then(response => {
      this.stationTypeDict = response?.data;
    });
    await this.getDicts("cm_station_status").then(response => {
      this.stationStatusDict = response?.data;
    });
    await this.getDicts("cm_open_flag").then(response => {
      this.openFlagDict = response?.data;
    });
    await this.getDicts("cm_station_charge_type").then(response => {
      this.stationChargeTypeDict = response?.data;
    });
    await this.getDicts("cm_construction").then(response => {
      this.constructionDict = response?.data;
    });
    this.queryProviderList();
  },
  methods: {
    closeDialog() {
      this.$emit("update:rowInfo", {});
      this.$emit("update:visible", false);
    },
    queryProviderList() {
      let params = {
        pageNum: 1,
        pageSize: 99999
      };
      providerList(params).then(res => {
        if (res?.success) {
          this.providerList = res.data;
        } else {
          this.$message.error(res.message);
        }
      })
    }
  }
};
</script>

<style scoped>

</style>