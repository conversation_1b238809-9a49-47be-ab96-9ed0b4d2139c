<template>
  <el-dialog
    title="运维合同查看"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleCancel"
  >
    <vxe-grid
      :columns="columns"
      :data="tableData"
      :loading="loading"
      align="center"
    >
      <template #operation="{ row }">
        <el-button type="text" size="large" @click="handleDelete(row)">
          删除
        </el-button>
      </template>
    </vxe-grid>
    <PreviewFiles
      :initial-index="previewIndex"
      v-if="showViewer"
      :on-close="
        () => {
          showViewer = false;
        }
      "
      :url-list="tableData"
      :fileOptions="{ url: 'storePath', name: 'docName' }"
    />
  </el-dialog>
</template>

<script>
import {
  contractFileList,
  deleteContractFile,
} from "@/api/operationMaintenanceManage/station/index.js";
import PreviewFiles from "@/components/PreviewFiles/index.vue";
import { downloadUrl } from "@/utils/downLoad.js";
export default {
  components: { PreviewFiles },
  data() {
    return {
      showViewer: false,
      previewIndex: 0,
      visible: false,
      columns: [
        {
          field: "docName",
          title: "合同名称",
          slots: {
            default: ({ row, $rowIndex }) => {
              return (
                <el-link on={{ click: () => this.handleClick($rowIndex) }}>
                  {row.docName}
                </el-link>
              );
            },
          },
          width: "35%",
        },
        { field: "uploadTime", title: "上传时间", width: "25%" },
        {
          field: "uploaderName",
          title: "上传人",
          width: "20%",
        },
        {
          title: "操作",
          slots: { default: "operation" },
          showOverflow: false,
          fixed: "right",
          width: "20%",
        },
      ],
      tableData: [],
      loading: false,
      stationId: "",
    };
  },
  methods: {
    handleClick(index) {
      this.showViewer = true;
      this.previewIndex = index;
      //   console.log("点击的图片");
      // downloadUrl(row.storePath, row.docName);
    },
    open(row) {
      this.visible = true;
      this.stationId = row.stationId;
      this.getList();
    },
    getList() {
      this.loading = true;
      contractFileList({ relaBizId: this.stationId, businessType: "运维合同" })
        .then((res) => {
          this.loading = false;
          if (res?.code === "10000") {
            this.tableData = res.data;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleDelete(row) {
      deleteContractFile({ docIdList: [row.docId] }).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("删除成功");
          this.getList();
        }
      });
    },
    handleCancel() {
      this.visible = false;
    },
  },
};
</script>

<style></style>
