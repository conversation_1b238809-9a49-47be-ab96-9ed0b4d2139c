// 打标签弹窗
<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleCancel"
  >
    <el-form :model="tagForm" ref="tagForm" label-width="110px" :rules="rules">
      <el-row>
        <el-col :span="24">
          <el-form-item label="站点名称:" prop="stationId">
            <span>{{ stationName }}</span>
          </el-form-item>
        </el-col> </el-row
      ><el-row>
        <el-col :span="24">
          <el-form-item label="选择标签:" prop="tagCode">
            <el-select
              v-model="tagForm.tagCode"
              multiple
              style="width: 100%"
              placeholder="复选，调取字典中所有的标签名称"
              filterable
            >
              <el-option
                v-for="item in stationTagOptions"
                :key="item.dictCode"
                :label="item.dictLabel"
                :value="item.dictCode"
              >
              </el-option>
            </el-select> </el-form-item
        ></el-col>
      </el-row>
      <el-form-item label="原因:" prop="reason">
        <el-input
          v-model="tagForm.reason"
          maxlength="500"
          type="textarea"
          :rows="5"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" size="small">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" size="small"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import {
  getSelectedTag,
  addStationTag,
  minusStationTag,
  getTagList,
} from "@/api/operationMaintenanceManage/station/index.js";
import {
  DATA_INFO_CLICK_STATION_ADD_TAG,
  DATA_INFO_CLICK_STATION_MINUS_TAG,
} from "@/utils/track/track-event-constants";

export default {
  data() {
    return {
      visible: false,
      title: "增加站点标签",
      stationTagOptions: [],
      stationName: "",
      tagForm: {},
      rules: {
        tagCode: [{ required: true, message: "请选择标签", trigger: "change" }],
      },
      isPlus: true,
      stationId: "",
    };
  },
  mounted() {},
  methods: {
    async getSelectedTag(stationId) {
      const res = await getSelectedTag({ stationId: stationId });
      return res?.data.map((x) => {
        return { ...x, dictCode: x.id, dictLabel: x.tagName };
      });
    },
    async getStationTag() {
      //业务类型字典
      const res = await getTagList({
        pageSize: 9999,
        pageNum: 1,
        tagAttributeValue: "1",
      });
      return res?.data?.map((x) => {
        return { ...x, dictCode: x.tagId, dictLabel: x.tagName };
      });
    },
    handleCancel() {
      this.visible = false;
      this.$refs.tagForm.resetFields();
    },
    async open(row, isPlus) {
      this.isPlus = isPlus;
      this.title = isPlus ? "增加站点标签" : "减少站点标签";
      this.visible = true;
      this.stationName = row.stationName;
      this.stationId = row.stationId;
      if (!isPlus) {
        this.stationTagOptions = await this.getSelectedTag(row.stationId);
        console.log("减少标签", this.stationTagOptions);
      } else {
        this.stationTagOptions = await this.getStationTag();
      }
    },
    handleSubmit() {
      const { reason, tagCode } = this.tagForm;
      if (this.isPlus) {
        //增标签
        const params = {
          stationId: this.stationId,
          reason: reason,
          tags: tagCode.map((x) => {
            let obj = this.stationTagOptions.find((i) => i.dictCode == x);
            console.log(obj);
            return {
              tagId: x,
              // tagValue: obj.dictValue,
              tagName: obj.dictLabel,
            };
          }),
        };
        addStationTag(params).then((res) => {
          if (res?.code == "10000") {
            this.$message.success("增标签成功");
            this.handleCancel();
            this.$emit("refreshDataList");

            this.reportTrackEvent(DATA_INFO_CLICK_STATION_ADD_TAG);
          }
        });
      } else {
        //减标签
        const params = {
          stationId: this.stationId,
          reason: reason,
          tags: tagCode.map((x) => {
            let obj = this.stationTagOptions.find((i) => i.dictCode == x);
            console.log(obj);
            return {
              id: obj.id,
              tagNum: obj.tagNum,
              tagName: obj.tagName,
            };
          }),
        };
        minusStationTag(params).then((res) => {
          if (res?.code == "10000") {
            this.$message.success("减标签成功");
            this.handleCancel();
            this.$emit("refreshDataList");

            this.reportTrackEvent(DATA_INFO_CLICK_STATION_MINUS_TAG);
          }
        });
      }
    },
  },
};
</script>

<style></style>
