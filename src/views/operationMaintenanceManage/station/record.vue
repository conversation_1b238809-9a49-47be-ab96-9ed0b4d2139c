// 能投大区数据核验记录
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['station:record:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="recordId"
      >
        <!-- <template slot="xToolbarBtn" slot-scope="{}">
          <el-button size="mini" type="primary" @click.stop="handleExport">
            导出
          </el-button>
        </template> -->
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryCheckRecord,
  exportRecordExcel,
} from "@/api/operationMaintenanceManage/station/index.js";
import { getToken } from "@/utils/auth";
import { listAllUser } from "@/api/common.js";
import checkPermission from "@/utils/permission.js";
export default {
  components: { AdvancedForm, GridTable },
  data() {
    return {
      config: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
          showOverflowTooltip: true,
        },
        {
          field: "beforeRegionName",
          title: "能投大区（变更前）",
        },
        {
          field: "afterRegionName",
          title: "能投大区（变更后）",
        },
        {
          field: "createTime",
          title: "变更时间",
        },
        {
          field: "createBy",
          title: "操作人",
          formatter: ({ cellValue, row, column }) => {
            return (
              this.userOption.find((el) => el.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "reason",
          title: "原因",
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "stationRecordList",
      handRow: {
        recordIds: [],
      },
      token: "",
      userOption: [], //用户列表
    };
  },
  created() {},
  mounted() {
    Promise.all([this.getListUser()]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          this.initConfig();
        });
      }, 500);
    });
    this.token = getToken();
  },
  methods: {
    checkPermission,
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    handleExport() {
      let text =
        this.handRow.recordIds.length == 0
          ? "是否确认导出所有数据?"
          : "是否确认导出所选数据?";
      let params = {
        ...this.searchForm,
        recordIds: this.handRow.recordIds,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportRecordExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      queryCheckRecord(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        recordIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.recordIds = tableData.map((v) => v.recordId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请输入站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "rangeTime",
          title: "变更时间",
          type: "dateRange",
          placeholder: "请选择变更时间",
        },
      ];
    },
  },
};
</script>

<style></style>
