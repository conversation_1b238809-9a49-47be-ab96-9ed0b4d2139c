<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['maintenance:station:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="stationId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCheckModal(handRow.stationCodes)"
            v-has-permi="['maintenance:station:dataCheck']"
          >
            能投大区数据核验
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCheckRecord"
            v-has-permi="['maintenance:station:checkRecord']"
          >
            能投大区数据核验记录
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleLevel"
            v-has-permi="['maintenance:station:levelList']"
          >
            站点运维等级
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchLevel"
            v-has-permi="['maintenance:station:batchLevel']"
          >
            批量配置站点等级
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchTypes"
            v-has-permi="['maintenance:station:batchTypes']"
          >
            批量导入运维类型
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchTag"
            v-has-permi="['maintenance:station:batchAddTag']"
          >
            批量配置标签
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchStation"
            v-has-permi="['maintenance:station:batchImport']"
          >
            导入站点
          </el-button>
          <!-- <el-button size="mini" type="primary" @click.stop="handleExport">
            导出
          </el-button> -->
        </template>

        <template slot="stationTag" slot-scope="{ row, $index }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in row.stationTagList"
            >{{ item }}</el-tag
          >
        </template>
        <template slot="operation" slot-scope="{ row }">
          <!-- <el-button type="text" size="large" @click="editTag(row, true)">
            增标签
          </el-button>
          <el-button type="text" size="large" @click="editTag(row, false)">
            减标签
          </el-button> -->
          <!-- <el-button type="text" size="large" @click="handleLog(row)">
            标签日志
          </el-button> -->
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['maintenance:station:detail']"
          >
            站点详情
          </el-button>
          <!-- <el-button type="text" size="large" @click="handleRowLevel(row)">
            站点运维等级
          </el-button> -->
          <el-dropdown @command="(command) => handleCommand(command, row)">
            <el-button type="text" size="large">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="item.command"
                v-for="(item, index) in btnArr"
                :key="index"
                v-has-permi="[item.permission]"
              >
                <el-button type="text" size="large">
                  {{ item.title }}
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot="jump" slot-scope="{ row, column }">
          <el-button
            type="text"
            size="large"
            @click="handleJump(row, column.property)"
          >
            {{ row[column.property] }}
          </el-button>
        </template>
        <template slot="projectJump" slot-scope="{ row, column }">
          <div v-for="(item, index) in row[column.property]" :key="index">
            <el-tooltip :content="item.content" placement="top">
              <el-button
                type="text"
                size="large"
                @click="handleJump(item, column.property)"
              >
                <div
                  style="max-width: 200px;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;"
                >
                  {{ item.content }}
                </div>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </GridTable>
    </el-card>

    <TagEdit ref="tagEdit" @refreshDataList="getList"></TagEdit>
    <DataCheck ref="dataCheck" @refreshDataList="getList"></DataCheck>
    <el-dialog
      title="操作日志"
      :visible.sync="logVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="70%"
    >
      <Timeline :list="recordList"></Timeline>
    </el-dialog>
    <el-dialog
      title="配置站点运维等级"
      :visible.sync="levelVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <el-form
        :model="levelForm"
        ref="levelForm"
        label-width="140px"
        :rules="rules"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="站点运维等级:" prop="gradeId">
              <el-select
                v-model="levelForm.gradeId"
                style="width: 100%"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.gradeId"
                  :label="item.gradeName"
                  :value="item.gradeId"
                >
                </el-option>
              </el-select> </el-form-item
          ></el-col>
        </el-row>
        <el-form-item label="备注:" prop="remark">
          <el-input
            v-model="levelForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内。"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel" size="small">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="批量配置站点等级"
      :visible.sync="batchLevelVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleBatchCancel"
    >
      <el-form
        :model="batchLevelForm"
        ref="batchLevelForm"
        label-width="140px"
        :rules="rules"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="下载模版:" prop="file">
              <el-link type="primary" @click="downExcel">点击下载</el-link>
            </el-form-item></el-col
          >
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传文件:" prop="file">
              <el-upload
                ref="upload"
                :limit="1"
                accept=".xlsx, .xls"
                :headers="upload.headers"
                :action="upload.url"
                :disabled="upload.isUploading"
                :on-success="handleFileSuccess"
                :on-error="handleFileError"
                :on-change="handleChangeFile"
                :auto-upload="false"
                :data="{ remark: batchLevelForm.remark }"
              >
                <el-button>选择文件</el-button>
                <div slot="tip" class="el-upload__tip">
                  支持xlxs、xls格式，2G以内。
                </div>
              </el-upload>
            </el-form-item></el-col
          >
        </el-row>
        <el-form-item label="备注:" prop="remark">
          <el-input
            v-model="batchLevelForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内。"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="handleBatchCancel"
          size="small"
          :loading="submitLoading"
          >取 消
        </el-button>
        <el-button
          type="primary"
          @click="handleBatchSubmit"
          size="small"
          :loading="submitLoading"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <BatchUploadTypes
      @uploadSuccess="getList"
      ref="batchUploadTypes"
    ></BatchUploadTypes>
    <BaseFormModal
      ref="formModal"
      modalTitle="运维类型"
      :config="typeConfig"
      @modalConfirm="modalConfirmHandler"
      labelWidth="130px"
    >
      <template #upload="{item, params}">
        <MultiFileUpload
          ref="attachments"
          @uploadSuccess="
            (attachments) => {
              return updateAttachments(attachments, params);
            }
          "
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          :maxSize="20480"
          :limit="50"
        >
          <template #customTip>
            上传格式支持doc、docx、pdf、jpg、jpeg、png文件，2G以内
          </template>
        </MultiFileUpload>
      </template>
    </BaseFormModal>
    <ContractView ref="contractView"></ContractView>
    <BatchUpload
      @uploadSuccess="getList"
      ref="batchUploadTag"
      title="批量配置站点标签"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
    >
      <template #templateBtn>
        <el-button type="primary" @click="downTagExcel(1)"
          >点击下载增标签模板</el-button
        >
        <el-button type="primary" @click="downTagExcel(0)"
          >点击下载减标签模板</el-button
        >
      </template>
      <template #extraForm="{params}">
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="params.remark"
                rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
                placeholder="请输入具体的原因描述，500个字符以内"
              /> </el-form-item
          ></el-col>
        </el-row>
      </template>
    </BatchUpload>
    <BatchUpload
      @uploadSuccess="getList"
      ref="batchUploadStation"
      title="批量导入站点"
      :uploadApi="stationUploadObj.api"
      :templateUrl="stationUploadObj.url"
      :extraData="stationUploadObj.extraData"
      maxSizeText="500M"
      :maxSize="0.5"
    >
      <template #extraForm="{params}">
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="params.remark"
                rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
                placeholder="请输入具体的原因描述，500个字符以内"
              /> </el-form-item
          ></el-col>
        </el-row>
      </template>
    </BatchUpload>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import MultiFileUpload from "@/components/MultipleFileUpload";
import GridTable from "@/components/GridTable/index.vue";
import TagEdit from "./components/tagEdit.vue";
import DataCheck from "./components/dataCheck.vue";
import checkPermission from "@/utils/permission.js";
import Timeline from "@/components/Timeline/index.vue";
import BaseFormModal from "@/components/GridTable/BaseFormModal/index.vue";
import BatchUploadTypes from "./components/batchUploadTypes.vue";
import ContractView from "./components/contractView.vue";
import { initParams } from "@/utils/buse.js";
import { maintenanceTypeDict } from "./dict.js";
import {
  queryStationInfoByPage,
  exportExcel,
  queryLogList,
  queryLevelList,
  submitEditRowLevel,
  saveContract,
  queryTypeDetail,
} from "@/api/operationMaintenanceManage/station/index.js";
import { getAllDeptList } from "@/api/operationWorkOrder";
import { getToken } from "@/utils/auth";
import { regionData } from "element-china-area-data";
// import {
//   DATA_INFO_CLICK_STATION_LEVEL_CONFIG,
//   DATA_INFO_CLICK_STATION_REPORT,
// } from "@/utils/track/track-event-constants";
import BatchUpload from "@/components/BatchUpload/index.vue";

export default {
  name: "maintenanceStationList",
  components: {
    AdvancedForm,
    GridTable,
    TagEdit,
    DataCheck,
    Timeline,
    BatchUploadTypes,
    BaseFormModal,
    MultiFileUpload,
    ContractView,
    BatchUpload,
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      stationUploadObj: {
        api: "/export/report/importStation",
        url: "/charging-maintenance-ui/static/批量导入站点模板.xlsx",
        extraData: {
          remark: "",
        },
      },
      uploadObj: {
        api: "/export/report/importStationTag",
        url: "/charging-maintenance-ui/static/批量增标签模板.xlsx",
        extraData: {
          remark: "",
        },
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/excel/stationGradeImport",
        updateAsCode: "",
      },
      batchLevelVisible: false,
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      levelForm: {
        gradeId: undefined,
        remark: undefined,
      },
      batchLevelForm: {
        remark: "",
        file: [],
      },
      tagEditVisible: false,
      // config: [],
      stationId: undefined,
      statusDict: [],
      stationChargeTypeDict: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "stationName",
          title: "站点名称",
          slots: { default: "jump" },
          // showOverflowTooltip: true,
        },
        {
          field: "stationGradeName",
          title: "站点运维等级",
        },
        {
          field: "businessMode",
          title: "业务模式",
        },
        {
          field: "operationMode",
          title: "运营模式",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "maintenanceTypeName",
          title: "运维类型",
          // formatter: ({ cellValue }) => {
          //   return (
          //     this.maintenanceTypeOptions?.find((x) => x.dictValue == cellValue)
          //       ?.dictLabel || cellValue
          //   );
          // },
        },
        {
          field: "status",
          title: "运营状态",
          formatter: ({ cellValue }) => {
            return (
              this.statusDict?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "deptName",
          title: "能投大区",
        },
        {
          field: "onlineDate",
          title: "上线日期",
        },
        {
          field: "belongPlace",
          title: "省市区",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 200,
        },
        {
          field: "gunACCount",
          title: "交流枪数",
          slots: { default: "jump" },
        },
        {
          field: "gunDCCount",
          title: "直流枪数",
          slots: { default: "jump" },
        },
        {
          field: "stationCode",
          title: "站点编号",
          treeNode: true,
        },
        {
          field: "projectCodeList",
          title: "项目编号",
          slots: { default: "projectJump" },
          showOverflow: false,
          customWidth: 220,
          // slots: { default: "projectCode" },
        },
        {
          field: "projectNameList",
          title: "项目名称",
          slots: { default: "projectJump" },
          showOverflow: false,
          customWidth: 220,
        },
        {
          field: "stationTag",
          title: "站点标签",
          slots: { default: "stationTag" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "omExpirationTime",
          title: "运维到期时间",
          slots: {
            default: ({ row }) => {
              return [
                <span
                  style={{ color: row.isOmExpirationTime == "1" ? "red" : "" }}
                >
                  {row.omExpirationTime}
                </span>,
              ];
            },
          },
        },
        // {
        //   field: "businessMode",
        //   title: "业务模式",
        // },
        // {
        //   field: "buildDate",
        //   title: "建设完成日期",
        // },
        // {
        //   field: "stationChargeType",
        //   title: "站点充电分类",
        //   formatter: ({ cellValue }) => {
        //     return (
        //       this.stationChargeTypeDict?.find((x) => x.dictValue == cellValue)
        //         ?.dictLabel || cellValue
        //     );
        //   },
        // },
        // {
        //   field: "pendingErrorCount",
        //   title: "待处理故障数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "totalErrorCount",
        //   title: "累计故障总数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "pendingOrderCount",
        //   title: "待处理工单数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "totalOrderCount",
        //   title: "工单总数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "chargeOrderCount",
        //   title: "充电订单数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "equipCount",
        //   title: "设备数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "pileACCount",
        //   title: "交流桩数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "pileDCCount",
        //   title: "直流桩数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "chargeUserName",
        //   title: "责任人",
        // },
        // {
        //   field: "serviceUserName",
        //   title: "服务人",
        // },

        // {
        //   field: "stationType",
        //   title: "站点类型",
        //   formatter: this.stationTypeFormat,
        // },
        // {
        //   field: "pileNum",
        //   title: "充电桩数量",
        // },
        // {
        //   field: "belongPlace",
        //   title: "归属地",
        //   showOverflowTooltip: true,
        // },

        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "stationList",
      rowInfo: {},
      stationTypeDict: [],
      stationTagDict: [],
      pageType: "detail",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      logVisible: false,
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      operationModeOptions: [],
      maintenanceTypeOptions: [],
      maintenanceTypeFilterOptions: [],
      maintenanceAllTypeOptions: [],
      submitLoading: false,
      btnArr: [
        {
          title: "设备列表",
          command: "device",
          func: (row) => {
            this.$router.push({
              name: "maintenanceChargingGun",
              params: {
                stationNo: row.stationCode,
              },
            });
          },
          permission: "maintenance:station:device",
        },
        {
          title: "订单列表",
          command: "order",
          func: (row) => {
            this.$router.push({
              name: "chargingOrder2",
              params: {
                stationNo: row.stationCode,
                stationName: row.stationName,
              },
            });
          },
          permission: "maintenance:station:order",
        },
        {
          title: "告警记录",
          command: "error",
          func: (row) => {
            this.$router.push({
              name: "maintenanceErrorPush",
              params: {
                stationNo: row.stationCode,
                stationName: row.stationName,
              },
            });
          },
          permission: "maintenance:station:error",
        },
        {
          title: "维修记录",
          command: "maintenanceRecord",
          func: (row) => {
            this.$router.push({
              name: "operationWorkOrderNew",
              params: {
                orderTypeParentNames: ["充电桩故障工单", "告警工单"],
                stationName: row.stationName,
              },
            });
          },
          permission: "maintenance:station:maintenanceRecord",
        },
        {
          title: "运维类型",
          command: "maintenanceType",
          func: (row) => {
            this.handleEditType(row);
          },
          permission: "maintenance:station:maintenanceType",
        },
        {
          title: "运维合同",
          command: "contract",
          func: (row) => {
            this.$refs.contractView.open(row);
          },
          permission: "maintenance:station:contract",
        },
        {
          title: "站点运维等级",
          command: "level",
          func: (row) => {
            this.handleRowLevel(row);
          },
          permission: "maintenance:station:level",
        },
        {
          title: "增标签",
          command: "addTag",
          func: (row) => {
            this.editTag(row, true);
          },
          permission: "maintenance:station:addTag",
        },
        {
          title: "减标签",
          command: "minusTag",
          func: (row) => {
            this.editTag(row, false);
          },
          permission: "maintenance:station:minusTag",
        },
        // {
        //   title: "操作日志",
        //   command: "log",
        //   func: (row) => {
        //     this.handleLog(row);
        //   },
        //   permission: "station:log:view",
        // },
      ],
      operationMode: "",
      maintenanceType: "",
    };
  },
  async created() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    Promise.all([
      this.getDicts("cm_station_type").then((response) => {
        this.stationTypeDict = response?.data;
      }),
      this.getDicts("cm_station_tag").then((response) => {
        this.stationTagDict = response?.data;
      }),
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
      this.getDicts("maintenance_type").then((response) => {
        this.maintenanceAllTypeOptions = response?.data;
        this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions;
      }),
      this.getDicts("business_mode").then((response) => {
        this.businessModeDict = response?.data;
      }),
      this.getDicts("cm_station_status").then((response) => {
        this.statusDict = response?.data;
      }),
      this.getDicts("cm_station_charge_type").then((response) => {
        this.stationChargeTypeDict = response?.data;
      }),
      this.getDeptList(),
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.initConfig();
          this.getList();
        });
      }, 500);
    });
  },
  mounted() {
    this.token = getToken();
    console.log(regionData, "----region");
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    queryLevelList({}).then((res) => {
      this.levelOptions = res.data;
    });
    this.getList();
  },
  methods: {
    downTagExcel(isAdd = 1) {
      window.location.href = isAdd
        ? "/charging-maintenance-ui/static/批量增标签模板.xlsx"
        : "/charging-maintenance-ui/static/批量减标签模板.xlsx";
    },
    handleBatchTag() {
      this.uploadObj = {
        // api: isAdd ? "/export/report/importStationTag" : "",
        api: "/export/report/importStationTag",
        extraData: {
          remark: "",
        },
      };
      this.$refs.batchUploadTag.open();
    },
    handleBatchStation() {
      this.stationUploadObj = {
        api: "/export/report/importStation",
        url: "/charging-maintenance-ui/static/批量导入站点模板.xlsx",
        extraData: {
          remark: "",
        },
      };
      this.$refs.batchUploadStation.open();
    },
    handleFilterModeChange(val) {
      if (!val) {
        this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions;
        return;
      }
      const label = this.operationModeOptions?.find((x) => x.dictValue == val)
        ?.dictLabel;
      const arr = maintenanceTypeDict[label] || [];
      this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions?.filter(
        (x) => arr.includes(x.dictLabel)
      );
    },
    handleEditType(row) {
      console.log("运维类型", row);
      queryTypeDetail({ stationId: row.stationId }).then((res) => {
        this.maintenanceType = res.data?.maintenanceType || row.maintenanceType;
        this.handleModeChange(res.data?.operationMode || row.operationMode);
        this.$refs.formModal.open({
          ...initParams(this.typeConfig),
          ...row,
          ...res.data,
          docList: res.data?.docList || [],
        });
        console.log(res.data, "res.data");
        this.$nextTick(() => {
          this.$refs.attachments.setAttachments(res.data?.docList || []);
        });
      });
    },
    handleModeChange(val) {
      this.operationMode = val;
      this.$refs.formModal.setFormFields({ maintenanceType: "" });
      const label = this.operationModeOptions?.find((x) => x.dictValue == val)
        ?.dictLabel;
      const arr = maintenanceTypeDict[label] || [];
      this.maintenanceTypeOptions = this.maintenanceAllTypeOptions?.filter(
        (x) => arr.includes(x.dictLabel)
      );
    },
    modalConfirmHandler(formParams) {
      const { createBy, ...rest } = formParams;
      let params = {
        ...rest,
        docList: rest.docList?.map((x) => {
          return {
            ...x,
            relaBizId: rest.stationId,
            businessType: "运维合同",
          };
        }),
      };
      saveContract(params).then((res) => {
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.$refs.formModal.closeVisible();
          this.getList();
        }
      });
    },
    updateAttachments(attachments, row) {
      row.docList = attachments;
    },
    handleBatchTypes() {
      this.$refs.batchUploadTypes.open();
    },
    getArr(item) {
      // console.log(item == "" ? [] : item?.split(";"));
      return item == "" ? [] : item?.split(";");
    },
    checkPermission,
    handleBeforeUpload(file) {
      const isLt2G = file.size / 1024 / 1024 / 1024 < 2;
      if (!isLt2G) {
        this.$message.error("上传的文件大小不能超过2G!");
      }
      return isLt2G;
    },
    handleChangeFile(file, fileList) {
      console.log(file, fileList);
      this.batchLevelForm.file = fileList || [];
    },
    handleFileSuccess(response) {
      this.submitLoading = false;
      console.log("response===", response);
      if (!response.success) {
        this.$confirm(response.message, "站点运维等级配置失败！", {
          confirmButtonText: "重新上传",
          cancelButtonText: "取消",
          type: "error",
          center: true,
          dangerouslyUseHTMLString: true,
        })
          .then(() => {
            this.batchLevelForm.file = [];
            this.$refs.upload.clearFiles();
          })
          .catch(() => {
            this.handleBatchCancel();
          });
      } else {
        this.handleBatchCancel();
        this.$alert("站点运维等级配置成功", "配置结果", {
          type: "success",
          confirmButtonText: "我知道了",
          callback: () => {
            this.getList();
          },
        });
      }
    },
    handleFileError(response) {
      this.submitLoading = false;
      this.$confirm(response.message, "站点运维等级配置失败！", {
        confirmButtonText: "重新上传",
        cancelButtonText: "取消",
        type: "error",
        center: true,
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          this.batchLevelForm.file = [];
          this.$refs.upload.clearFiles();
        })
        .catch(() => {
          this.handleBatchCancel();
        });
    },
    downExcel() {
      window.location.href =
        "/charging-maintenance-ui/static/批量配置站点运维等级.xlsx";
    },
    //批量配置-提交
    handleBatchSubmit() {
      console.log(this.batchLevelForm.file, "提交");
      if (this.batchLevelForm.file?.length === 0) {
        this.$message.error("请上传文件！");
        return;
      }
      if (this.batchLevelForm.file[0].size / 1024 / 1024 / 1024 > 2) {
        this.$message.error("上传的文件大小不能超过2G!");
        return;
      }
      this.submitLoading = true;
      this.$refs.upload.submit();
    },
    handleBatchLevel() {
      this.batchLevelVisible = true;
    },
    handleCommand(command, row) {
      this.btnArr?.find((x) => x.command == command)?.func(row);
      console.log(command, row, "command");
    },
    //能投大区下拉选项
    async getDeptList() {
      getAllDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    handleJump(row, property) {
      const arr = [
        {
          title: "站点名称",
          field: "stationName",
          method: () => {
            this.$router.push({
              path: "/operationMaintenanceManage/station/newDetailPage",
              query: {
                stationId: row.stationId,
                projectId: row.projectId,
                stationCode: row.stationCode,
                stationName: row.stationName,
              },
            });
          },
        },
        {
          field: "gunACCount",
          title: "交流枪数",
          method: () => {
            this.$router.push({
              name: "maintenanceChargingGun",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "01",
              },
            });
          },
        },
        {
          field: "gunDCCount",
          title: "直流枪数",
          method: () => {
            this.$router.push({
              name: "maintenanceChargingGun",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "02",
              },
            });
          },
        },
        {
          field: "projectCodeList",
          title: "项目编码",
          method: (item) => {
            this.$router.push({
              name: item.flag == "1" ? "projectManage" : "investBatchList",
              params: {
                projectCode: item.content,
              },
            });
          },
        },
        {
          field: "projectNameList",
          title: "项目名称",
          method: (item) => {
            this.$router.push({
              name: item.flag == "1" ? "projectManage" : "investBatchList",
              params: {
                projectName: item.content,
              },
            });
          },
        },
      ];
      arr.find((item) => {
        if (item.field == property) {
          item.method(row);
        }
      });
    },
    handleSubmit() {
      this.$refs.levelForm.validate((valid) => {
        if (!valid) return;
        const params = { ...this.levelForm, stationId: this.stationId };
        submitEditRowLevel(params).then((res) => {
          if (res?.code == "10000") {
            this.$message.success("保存成功");
            this.handleCancel();
            this.getList();

            // this.reportTrackEvent(DATA_INFO_CLICK_STATION_LEVEL_CONFIG);
          }
        });
      });
    },
    handleCancel() {
      this.levelVisible = false;
      this.$refs.levelForm.resetFields();
    },
    handleBatchCancel() {
      this.batchLevelVisible = false;
      this.$refs.batchLevelForm.resetFields();
      this.batchLevelForm.file = [];
      this.$refs.upload.clearFiles();
    },
    handleRowLevel(row) {
      this.levelVisible = true;
      this.stationId = row.stationId;
      this.levelForm.gradeId = row.stationGrade;
    },
    handleLog(row) {
      this.logVisible = true;
      queryLogList({ stationId: row.stationId }).then((res) => {
        this.recordList = res.data;
      });
    },
    closeDialog() {
      this.logVisible = false;
    },
    handleExport() {
      let text =
        this.handRow.stationIds.length == 0
          ? "是否确认导出所有数据?"
          : "是否确认导出所选数据?";
      const params = {
        ...this.searchForm,
        stationIds: this.handRow.stationIds,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      if (Array.isArray(params.onlineDate)) {
        params.startOnlineDate = params.onlineDate[0];
        params.endOnlineDate = params.onlineDate[1];
        delete params.onlineDate;
      }
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          // this.reportTrackEvent(DATA_INFO_CLICK_STATION_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
        stationCodes: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
        this.handRow.stationCodes = tableData.map((v) => v.stationCode);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    editTag(row, isPlus) {
      this.$refs.tagEdit.open(row, isPlus);
    },
    //打开弹窗
    handleCheckModal(code) {
      this.$refs.dataCheck.open(code);
    },
    //跳转至纪录页
    handleCheckRecord() {
      this.$router.push({
        path: "/station/record",
        query: {
          // stationId: this.stationId,
        },
      });
    },
    //跳转至站点运维等级
    handleLevel() {
      this.$router.push({
        path: "/station/level",
        query: {
          // stationId: this.stationId,
        },
      });
    },
    showDetail(row) {
      this.$router.push({
        path: "/operationMaintenanceManage/station/newDetailPage",
        query: {
          stationId: row.stationId,
          projectId: row.projectId,
          stationCode: row.stationCode,
          stationName: row.stationName,
        },
      });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.maintenanceTypeFilterOptions = this.maintenanceAllTypeOptions;
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      if (Array.isArray(params.onlineDate)) {
        params.startOnlineDate = params.onlineDate[0];
        params.endOnlineDate = params.onlineDate[1];
        delete params.onlineDate;
      }
      queryStationInfoByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
    //站点类型转换
    stationTypeFormat({ cellValue }) {
      return this.selectDictLabel(this.stationTypeDict, cellValue);
    },
    updateTag() {
      this.tagManageVisible = false;
      this.getList();
    },
    close() {
      this.tagManageVisible = false;
    },
  },
  computed: {
    typeConfig() {
      return [
        {
          field: "stationName",
          element: "el-input",
          title: "站点名称",
          preview: true,
        },
        {
          field: "operationMode",
          element: "el-select",
          title: "运营模式",
          props: {
            options: this.operationModeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
          },
          on: {
            change: (val) => {
              this.handleModeChange(val);
            },
          },
          rules: [
            { required: true, message: "运营模式不能为空", trigger: "blur" },
          ],
        },
        {
          field: "maintenanceType",
          element: "el-select",
          title: "运维类型",
          props: {
            options: this.maintenanceTypeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictValue",
          },
          on: {
            change: (val) => {
              this.maintenanceType = val;
            },
          },
          rules: [
            { required: true, message: "运维类型不能为空", trigger: "blur" },
          ],
        },
        {
          field: "omExpirationTime",
          element: "el-date-picker",
          title: "运维到期时间",
          props: {
            valueFormat: "yyyy-MM-dd",
          },
          rules: [
            {
              required: ["2", "6"].includes(this.maintenanceType),
              message: "运维到期时间不能为空",
              trigger: "blur",
            },
          ],
        },
        {
          field: "docList",
          element: "slot",
          title: "运维合同",
          slotName: "upload",
          rules: [
            {
              required: ["2", "6"].includes(this.maintenanceType),
              message: "请上传运维合同",
              trigger: "blur",
            },
          ],
        },
      ];
    },
    config() {
      return [
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        {
          key: "equipNo",
          title: "设备编号",
          type: "input",
          placeholder: "请填写设备编号",
        },
        {
          key: "operationMode",
          title: "运营模式",
          type: "select",
          placeholder: "请选择运营模式",
          options: this.operationModeOptions,
          events: {
            change: (val) => {
              this.handleFilterModeChange(val);
            },
          },
        },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
        {
          key: "stationGrade",
          title: "站点等级",
          type: "select",
          placeholder: "请选择站点等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },
        {
          key: "maintenanceType",
          title: "运维类型",
          type: "select",
          placeholder: "请选择运维类型",
          options: this.maintenanceTypeFilterOptions,
        },
        {
          key: "stationCode",
          title: "站点编号",
          type: "input",
          placeholder: "请填写站点编号",
        },
        {
          key: "maintenanceName",
          title: "运维单位",
          type: "input",
          placeholder: "请填写运维单位",
        },
        {
          key: "status",
          title: "运营状态",
          type: "select",
          placeholder: "请选择运营状态",
          options: this.statusDict,
        },
        {
          key: "operationName",
          title: "运营单位",
          type: "input",
          placeholder: "请填写运营单位",
        },
        {
          key: "onlineDate",
          title: "上线日期",
          type: "dateRange",
          placeholder: "请选择上线日期",
        },
        {
          key: "isOmExpirationTime",
          title: "运维是否到期",
          type: "select",
          placeholder: "请选择运维是否到期",
          options: [
            { dictLabel: "未到期", dictValue: "0" },
            { dictLabel: "已到期", dictValue: "1" },
          ],
        },
        {
          key: "assetName",
          title: "资产单位",
          type: "input",
          placeholder: "请填写资产单位",
        },
        {
          key: "projectCode",
          title: "项目编号",
          type: "input",
          placeholder: "请填写项目编号",
        },
        {
          key: "businessMode",
          title: "业务模式",
          type: "select",
          placeholder: "请选择业务模式",
          options: this.businessModeDict,
        },
        // {
        //   key: "stationType",
        //   title: "站点类型",
        //   type: "select",
        //   placeholder: "请选择站点类型",
        //   options: this.stationTypeDict,
        // },

        // {
        //   key: "stationTag",
        //   title: "站点标签",
        //   type: "select",
        //   placeholder: "请选择站点标签",
        //   options: this.stationTagDict,
        // },

        // {
        //   key: "projectName",
        //   title: "项目名称",
        //   type: "input",
        //   placeholder: "请填写项目名称",
        // },

        // {
        //   key: "status",
        //   title: "运营状态",
        //   type: "select",
        //   placeholder: "请选择运营状态",
        //   options: this.statusDict,
        // },
      ];
    },
  },
};
</script>

<style scoped lang="less">
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
