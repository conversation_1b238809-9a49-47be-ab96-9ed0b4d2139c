//充电桩信息tab页
<template>
  <div>
    <!-- 桩设备数据统计 -->
    <el-card style="margin-bottom: 10px;">
      <div
        slot="header"
        class="card-title-wrap"
        style="justify-content: space-between"
      >
        <div style="display: flex;align-items: center;">
          <div class="card-title-line"></div>
          <span>桩设备数据统计</span>
          <el-button
            type="text"
            @click="handleJump()"
            style="margin-left: 10px;"
            >详情<i class="el-icon-arrow-right el-icon--right"></i
          ></el-button>
        </div>
        <!-- <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="float: right"
          value-format="yyyy-MM-dd"
          @change="getInfo"
          :clearable="false"
          :picker-options="pickerOptions"
          @blur="resetDisableDate"
        >
        </el-date-picker> -->
      </div>
      <div class="statistics-box">
        <div class="statistics-item bg-1">
          <img src="@/assets/stationImages/icon-1.png" alt="" />
          <div>
            <div class="statistics-item-title">交流桩</div>
            <div>
              <span class="statistics-item-count">{{
                statisticObj.communicationPileCount || "0"
              }}</span
              >个
            </div>
          </div>
        </div>
        <div class="statistics-item bg-2">
          <img src="@/assets/stationImages/icon-2.png" alt="" />
          <div>
            <div class="statistics-item-title">交流枪</div>
            <div>
              <span class="statistics-item-count">{{
                statisticObj.communicationGunCount || "0"
              }}</span
              >个
            </div>
          </div>
        </div>
        <div class="statistics-item bg-3">
          <img src="@/assets/stationImages/icon-1.png" alt="" />
          <div>
            <div class="statistics-item-title">直流桩</div>
            <div>
              <span class="statistics-item-count">{{
                statisticObj.directPileCount || "0"
              }}</span
              >个
            </div>
          </div>
        </div>
        <div class="statistics-item bg-4">
          <img src="@/assets/stationImages/icon-2.png" alt="" />
          <div>
            <div class="statistics-item-title">直流枪</div>
            <div>
              <span class="statistics-item-count">{{
                statisticObj.directGunCount || "0"
              }}</span
              >个
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <!-- 枪运行状态 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>枪运行状态</span>
      </div>
      <div class="status-box" v-if="runStatusList.length > 0">
        <div
          class="status-item"
          v-for="(item, index) in runStatusList"
          :key="index"
        >
          <div class="status-item-count">{{ item.count }}</div>
          <div class="status-item-title">
            <div class="dot" :style="{ background: item.color }"></div>
            {{ item.runStatusName }}
          </div>
        </div>
      </div>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 枪运营状态 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>枪运营状态</span>
      </div>
      <div class="status-box" v-if="operationStatusList.length > 0">
        <div
          class="status-item"
          v-for="(item, index) in operationStatusList"
          :key="index"
        >
          <div class="status-item-count">{{ item.count }}</div>
          <div class="status-item-title">
            <div class="dot" :style="{ background: item.color }"></div>
            {{ item.operStatusName }}
          </div>
        </div>
      </div>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 枪明细 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>枪明细</span>
      </div>
      <div class="detail-box" v-if="detailList.length > 0">
        <div
          class="detail-item"
          v-for="(item, index) in detailList"
          :key="index"
        >
          <img
            :src="require(`@/assets/stationImages/gun-${item.colorType}.png`)"
            alt=""
            width="56px"
            height="85px"
          />
          <div class="detail-item-right">
            <div :class="['detail-item-right-top', `color-${item.colorType}`]">
              <div class="detail-item-right-top-title">运行状态</div>
              <img
                :src="
                  require(`@/assets/stationImages/icon-${item.colorType}.png`)
                "
                alt=""
                width="18px"
                height="18px"
              />
              {{ item.runStatusName }}
            </div>
            <div class="detail-item-right-middle">
              {{ item.pileName + "-" + item.gunName }}
            </div>
            <div :class="['detail-item-right-bottom', `bg-${item.colorType}`]">
              <div class="dot"></div>
              <span>{{ item.operStatusName }}</span>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else></el-empty>
    </el-card>
  </div>
</template>

<script>
import moment from "moment";
import { queryPileBaseInfo } from "@/api/operationMaintenanceManage/station/detail.js";
export default {
  components: {},
  props: {
    stationCode: {
      type: String,
      default: "",
    },
    stationName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      operationStatusList: [],
      runStatusList: [],
      dateRange: ["", ""],
      loading: false,
      disabledCurrent: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log("onPick", maxDate, minDate);
          if (!maxDate) {
            this.disabledCurrent = minDate;
          }
        },
        disabledDate: (current) => {
          if (!this.disabledCurrent) return false;
          return (
            (current &&
              current <
                moment(this.disabledCurrent)
                  .subtract(1, "Y")
                  .startOf("day")) ||
            current >
              moment(this.disabledCurrent)
                .add(1, "Y")
                .endOf("day")
          );
        },
      },
      statisticObj: {},
      detailList: [],
    };
  },
  created() {
    this.dateRange = [
      moment()
        .subtract(6, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
  },
  methods: {
    // 每次失焦重置disableDate
    resetDisableDate() {
      this.disabledCurrent = null;
    },
    //跳转至充电桩页面
    handleJump() {
      this.$router.push({
        name: "maintenanceChargingGun",
        params: {
          stationNo: this.stationCode,
          stationName: this.stationName,
        },
      });
    },
    getInfo() {
      queryPileBaseInfo({
        // startTime: this.dateRange[0],
        // endTime: this.dateRange[1],
        stationCode: this.stationCode,
      }).then((res) => {
        const {
          gunDetailVOList = [],
          gunRunVOList = [],
          operVOList = [],
          ...rest
        } = res.data;
        this.statisticObj = rest;
        this.runStatusList = gunRunVOList;
        this.operationStatusList = operVOList;
        this.detailList = gunDetailVOList?.map((x) => {
          return {
            ...x,
            colorType:
              x.runStatusName === "故障"
                ? "red"
                : x.runStatusName === "离线" || x.runStatusName === "禁用"
                ? "grey"
                : "green",
          };
        });
      });
    },
  },
};
</script>

<style scoped lang="less">
.statistics-box {
  display: grid;
  grid-template-columns: 25% 25% 25% 25%;
  .statistics-item {
    display: flex;
    padding: 24px 48px;
    margin-right: 20px;
    // height: 96px;
    // width: 325px;
    background-size: 100% 100%;
    font-size: 12px;
    color: #ffffff;
    &-title {
      font-size: 16px;
      font-weight: 500;
    }
    &-count {
      font-size: 28px;
      font-weight: bold;
      margin-right: 4px;
    }
    img {
      margin-right: 20px;
    }
  }
  .bg-1 {
    background-image: url("~@/assets/stationImages/bg-1.png");
  }

  .bg-2 {
    background-image: url("~@/assets/stationImages/bg-2.png");
  }

  .bg-3 {
    background-image: url("~@/assets/stationImages/bg-3.png");
  }

  .bg-4 {
    background-image: url("~@/assets/stationImages/bg-4.png");
  }
}
.status-box {
  display: grid;
  grid-row-gap: 24px;
  grid-template-columns: auto auto auto auto auto;
  .status-item {
    font-size: 14px;
    line-height: 22px;
    &-count {
      font-size: 26px;
      font-weight: bold;
      color: #333333;
      line-height: 26px;
      margin-bottom: 4px;
    }
    &-title {
      display: flex;
      align-items: center;
    }
  }
}
.detail-box {
  display: grid;
  grid-row-gap: 24px;
  //   grid-column-gap: 12px;
  grid-template-columns: 20% 20% 20% 20% 20%;
  .detail-item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background: #f6f6f6;
    border-radius: 10px;
    margin-right: 16px;
    &-right {
      margin-left: 16px;
      &-top {
        display: flex;
        align-items: center;
        color: #52c41a;
        font-size: 14px;
        line-height: 22px;
        &-title {
          color: #999999;
          padding-right: 4px;
          border-right: 1px solid #d8d8d8;
        }
        > img {
          margin: 0 4px;
        }
      }
      &-middle {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #3d3d3d;
      }
      &-bottom {
        display: flex;
        align-items: center;
        padding: 4px 12px;
        color: #52c41a;
        font-size: 14px;
        line-height: 22px;
        background: rgba(82, 196, 26, 0.05);
        border-radius: 4px;
        width: 76px;
        margin-top: 8px;
      }
      .color-red {
        color: #f5222d;
      }
      .color-grey {
        color: #666666;
      }
      .bg-red {
        background: rgba(245, 34, 45, 0.05);
        color: #f5222d;
        .dot {
          background: #f5222d;
        }
      }
      .bg-grey {
        background: rgba(102, 102, 102, 0.05);
        color: #666666;
        .dot {
          background: #666666;
        }
      }
    }
  }
}
.dot {
  width: 6px;
  height: 6px;
  margin-right: 4px;
  background: #52c41a;
  border-radius: 3px;
}
</style>
