//基本信息tab页
<template>
  <div>
    <!-- 基础信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基础信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item
          v-for="(item, index) in infoList"
          :key="index"
          :label="item.title"
        >
          <el-button type="text" @click="handleJump()" v-if="item.jump">{{
            item.value
          }}</el-button>
          <el-tooltip :content="item.value" placement="top-start" v-else>
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 基础信息-E -->
    <!-- 运维信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>运维信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item v-for="(item, index) in maintenanceList" :key="index">
          <template slot="label">
            {{ item.title }}
          </template>
          <FileIcons
            :list="item.value"
            v-if="item.isFile"
            :fileOptions="{ url: 'storePath', name: 'docName' }"
          ></FileIcons>
          <el-tooltip :content="item.value" placement="top-start" v-else>
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 运维信息-E -->
    <!-- 标签信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>标签信息</span>
      </div>
      <el-descriptions class="descriptions" :column="2" border>
        <el-descriptions-item v-for="(item, index) in tagList" :key="index">
          <template slot="label">
            {{ item.title }}
          </template>
          <!-- <el-tooltip placement="top-start" effect="light"> -->
          <el-tag style="margin-right: 10px;" :key="x" v-for="x in item.value">
            {{ x }}
          </el-tag>
          <!-- <template slot="content">
              <el-tag
                style="margin-right: 10px;"
                :key="x"
                v-for="x in item.value"
              >
                {{ x }}
              </el-tag>
            </template>
          </el-tooltip> -->
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 标签信息-E -->
    <!-- 单位信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>单位信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item v-for="(item, index) in unitList" :key="index">
          <template slot="label">
            {{ item.title }}
          </template>
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 单位信息-E -->
    <!-- 停车费信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>停车费信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item v-for="(item, index) in parkList" :key="index">
          <template slot="label"> {{ item.title }}</template>
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 停车费信息-E -->
    <!-- 监管平台推送信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>监管平台推送信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item v-for="(item, index) in pushList" :key="index">
          <template slot="label"> {{ item.title }}</template>
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 监管平台推送信息-E -->
    <!-- 站点图片-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>站点图片</span>
      </div>
      <el-row type="flex" style="flex-wrap:wrap" v-if="picList.length > 0">
        <div v-for="(o, index) in picList" :key="index" class="file-item">
          <el-image
            style="width: 150px;height:150px;margin-right:10px"
            :src="o"
            alt="加载失败"
            class="avatar"
            v-if="
              o.toLowerCase().indexOf('.jpg') > 0 ||
                o.toLowerCase().indexOf('.jpeg') > 0 ||
                o.toLowerCase().indexOf('.png') != -1
            "
            :preview-src-list="
              picList.filter(
                (o) =>
                  o.toLowerCase().indexOf('.jpg') > 0 ||
                  o.toLowerCase().indexOf('.jpeg') > 0 ||
                  o.toLowerCase().indexOf('.png') != -1
              )
            "
          />
          <video
            style="width: 150px;height:150px;margin-right:10px"
            v-if="o.toLowerCase().indexOf('.mp4') > 0"
            :src="o"
            controls
          ></video></div
      ></el-row>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 站点图片-E -->
    <!-- <PicPreview ref="picPreview"></PicPreview> -->
  </div>
</template>

<script>
import { queryStationBaseInfo } from "@/api/operationMaintenanceManage/station/detail.js";
// import PicPreview from "@/components/Upload/picPreview.vue";
import FileIcons from "@/components/FileIcons/index.vue";
export default {
  components: {
    // PicPreview,
    FileIcons,
  },
  props: {
    stationId: {
      type: String,
      default: "",
    },
    stationCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      infoList: [],
      unitList: [],
      parkList: [],
      tagList: [],
      picList: [],
      maintenanceList: [],
      pushList: []
    };
  },
  created() {
    this.getInfo();
  },
  methods: {
    //跳转至充电桩页面
    handleJump() {
      this.$router.push({
        name: "maintenanceChargingGun",
        params: {
          stationNo: this.stationCode,
        },
      });
    },
    clickImg(index) {
      this.$refs.picPreview.open(index, this.picList);
    },
    getInfo() {
      queryStationBaseInfo({ stationId: this.stationId }).then((res) => {
        // const {infoObj,feeList,chargingList}=res.data
        this.picList = res.data?.stationUrls || [];
        this.tagList = [
          { title: "充电平台站点标签", value: res.data?.platformStationTags },
          { title: "维保通站点标签", value: res.data?.maintenanceStationTags },
          { title: "服务标签", value: res.data?.serviceTag },
          { title: "区域标签", value: res.data?.regionTag },
        ];
        this.infoList = [
          { title: "站点编码", value: res.data?.stationCode },
          { title: "站点名称", value: res.data?.stationName },
          { title: "站点类型", value: res.data?.stationType },
          { title: "站点充电分类", value: res.data?.stationChargeType },
          {
            title: "运营模式",
            value:
                res.data?.operationMode == "1"
                    ? "自营"
                    : res.data?.operationMode == "2"
                        ? "代运营"
                        : "",
          },
          { title: "运营状态", value: res.data?.statusName },
          { title: "是否支持预约", value: res.data?.canBooking == "1" ? "支持" :  res.data?.canBooking == "0" ? "不支持" : ""},
          { title: "充电方式", value: res.data?.chargeType },
          { title: "竣工通过时间", value: res.data?.projectCompleteTime },
          { title: "投运日期", value: res.data?.openDate },
          { title: "上线时间", value: res.data?.onlineDate },
          { title: "运营时间", value: res.data?.businessTime },
          { title: "归属大区", value: res.data?.deptName },
          // { title: "建设完成时间", value: res.data?.buildDate },
          //
          // { title: "站点运维等级", value: res.data?.stationGradeName },
          {
            title: "是否开放",
            value:
              res.data?.openFlag == "1"
                ? "是"
                : res.data?.openFlag == "0"
                ? "否"
                : "",
          },
          { title: "站点简介", value: res.data?.stationIntroduce },
          { title: "站点资产编号", value: res.data?.assetNumber },
          // {
          //   title: "实体类型",
          //   value:
          //     res.data?.stationObjectType == "01"
          //       ? "实体"
          //       : res.data?.stationObjectType == "02"
          //       ? "虚拟"
          //       : "",
          // },
          { title: "建设场所", value: res.data?.constructionName },
          { title: "服务电话", value: res.data?.serviceTel },
          { title: "位置引导", value: res.data?.siteGuide },
          { title: "充电枪数量", value: res.data?.gunCount, jump: true },
          // { title: "支持车型", value: res.data?.carType },
          { title: "开票方式", value: res.data?.invoicingMethod },
          { title: "开票方", value: res.data?.invoicingPartyName },
          { title: "结算模式", value: res.data?.settleMode == "RECEIPT_MONEY" ? "款项到帐" :  res.data?.settleMode == "CUSHION_SETTLEMENT" ? "垫资结算" : "" },
          { title: "SOC阈值", value: res.data?.controlSoc },
          { title: "业务模式", value: res.data?.businessMode },
          { title: "站点地址", value: res.data?.stationAddressInfo },
        ];
        this.maintenanceList = [
          { title: "运维类型", value: res.data?.maintenanceType },
          { title: "运维单位", value: res.data?.maintenanceName },
          { title: "站点运维等级", value: res.data?.stationGradeName },
          { title: "运维到期时间", value: res.data?.maintenanceExpireTime },
          { title: "运维合同",
            value: res.data?.maintenanceContract,
            isFile: true
          },
        ];
        this.unitList = [
          { title: "资产单位", value: res.data?.assetName },
          { title: "运营单位", value: res.data?.operationName },
          // { title: "运维单位", value: res.data?.maintenanceName },
          {
            title: "监管单位",
            value: res.data?.superviseName,
          },
          { title: "核算单位", value: res.data?.accountingName },
          // { title: "", value: "" },
          // { title: "", value: "" },
          // { title: "", value: "" },
        ];
        this.parkList = [
          { title: "停车收费类型", value: res.data?.parkingChargeTypeName },
          { title: "停车位置", value: res.data?.parkingLocationName },
          { title: "支持车型", value: res.data?.carType },
          {
            title: "停车费说明",
            value: res.data?.parkFeeDescription,
          },
        ];
        this.pushList = [
          { title: "是否推送监管平台", value: res.data?.pushRegulation == "0" ? "否" : res.data?.pushRegulation == "1" ? "是" : ""},
          { title: "监管推送资产单位", value: res.data?.superviseName },
          { title: "监管表单推送", value: res.data?.pushRegulationForm },
        ];
      });
    },
  },
};
</script>

<style></style>
