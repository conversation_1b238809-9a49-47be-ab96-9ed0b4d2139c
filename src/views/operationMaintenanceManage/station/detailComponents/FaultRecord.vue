//异常记录tab页
<template>
  <div>
    <!-- 异常数据 -->
    <el-card style="margin-bottom: 10px;">
      <div
        slot="header"
        class="card-title-wrap"
        style="justify-content: space-between"
      >
        <div style="display: flex;align-items: center;">
          <div class="card-title-line"></div>
          <span>异常数据</span>
          <el-button
            type="text"
            @click="handleJump()"
            style="margin-left: 10px;"
            >详情<i class="el-icon-arrow-right el-icon--right"></i
          ></el-button>
        </div>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="float: right"
          value-format="yyyy-MM-dd"
          @change="getInfo"
          :clearable="false"
          :picker-options="pickerOptions"
          @blur="resetDisableDate"
        >
        </el-date-picker>
      </div>
      <div class="statistics-box" v-loading="loading">
        <div v-for="(item, index) in statisticsList" :key="index">
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
              <el-tooltip effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <!-- 异常趋势 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>异常趋势</span>
      </div>
      <LineChart
        :axisData="faultTendObj.time"
        :serieData="faultTendObj.tendencyArr"
        lineType="bar"
        chartStyle="height:320px"
        v-if="faultTendObj.time && faultTendObj.time.length > 0"
        unit="单位：个"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 异常类型（前10） -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>异常类型（前10）</span>
      </div>
      <LineChart
        :axisData="faultTypeObj.time"
        :serieData="faultTypeObj.tendencyArr"
        lineType="bar"
        chartStyle="height:320px"
        unit="单位：个"
        v-if="faultTypeObj.time && faultTypeObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 异常桩排名 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>异常桩排名</span>
      </div>
      <LineChart
        :axisData="pileRateObj.time"
        :serieData="pileRateObj.tendencyArr"
        lineType="bar"
        chartStyle="height:320px"
        unit="单位：个"
        v-if="pileRateObj.time && pileRateObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
  </div>
</template>

<script>
import moment from "moment";
import LineChart from "@/components/Echarts/LineChart.vue";
import { queryFaultRecord } from "@/api/operationMaintenanceManage/station/detail.js";
export default {
  components: {
    LineChart,
  },
  props: {
    stationCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dateRange: ["", ""],
      statisticsList: [],
      loading: false,
      faultTendObj: {
        time: [],
        tendencyArr: [],
      },
      faultTypeObj: {
        time: [],
        tendencyArr: [],
      },
      pileRateObj: {
        time: [],
        tendencyArr: [],
      },
      disabledCurrent: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log("onPick", maxDate, minDate);
          if (!maxDate) {
            this.disabledCurrent = minDate;
          }
        },
        disabledDate: (current) => {
          if (!this.disabledCurrent) return false;
          return (
            (current &&
              current <
                moment(this.disabledCurrent)
                  .subtract(1, "Y")
                  .startOf("day")) ||
            current >
              moment(this.disabledCurrent)
                .add(1, "Y")
                .endOf("day")
          );
        },
      },
    };
  },
  created() {
    this.dateRange = [
      moment()
        .subtract(6, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
  },
  methods: {
    // 每次失焦重置disableDate
    resetDisableDate() {
      this.disabledCurrent = null;
    },
    //跳转至异常信息页面
    handleJump() {
      this.$router.push({
        name: "maintenanceErrorPush",
        params: {
          stationNo: this.stationCode,
        },
      });
    },
    getInfo() {
      queryFaultRecord({
        startTime: this.dateRange[0] + " 00:00:00",
        endTime: this.dateRange[1] + " 23:59:59",
        stationNo: this.stationCode,
      }).then((res) => {
        const {
          errorTrendVOMap = {},
          errorType = {},
          pileErrorNum = {},
          ...rest
        } = res.data;
        this.statisticsList = [
          {
            title: "累计异常",
            value: rest?.totalError,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的该站点的异常告警总数，包含所有状态的告警数据。单位为【个】",
          },
          {
            title: "待处理异常",
            value: rest?.pendingError,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的该站点的异常告警状态为【未处理】的总数，单位为【个】",
          },
          {
            title: "累计工单",
            value: rest?.totalOrder,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的该站点的运维工单总和，包含所有状态的工单数据。单位为【个】",
          },
          {
            title: "待处理工单",
            value: rest?.pendingOrder,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的该站点的运维工单状态为【待处理】的总数，单位为【个】",
          },
          {
            title: "累计故障告警",
            value: rest?.deviceError,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的该站点的异常告警中，【故障告警】的总数，包含所有状态的告警数据。单位为【个】",
          },
          {
            title: "累计业务告警",
            value: rest?.businessError,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的该站点的异常告警中，【业务告警】的总数，包含所有状态的告警数据。单位为【个】",
          },
        ];
        console.log(this.statisticsList, "this.statisticsList");
        this.faultTendObj = {
          time: Object.keys(errorTrendVOMap),
          tendencyArr: [
            {
              name: "单日告警",
              data: Object.keys(errorTrendVOMap)?.map((x) => {
                return errorTrendVOMap[x].totalErrorNum;
              }),
            },
            {
              name: "单日业务告警",
              data: Object.keys(errorTrendVOMap)?.map((x) => {
                return errorTrendVOMap[x].businessErrorNum;
              }),
            },
            {
              name: "单日故障告警",
              data: Object.keys(errorTrendVOMap)?.map((x) => {
                return errorTrendVOMap[x].deviceErrorNum;
              }),
            },
          ],
        };
        this.faultTypeObj = {
          time: Object.keys(errorType)?.slice(0, 10),
          tendencyArr: [
            {
              name: "累计故障",
              data: Object.keys(errorType)
                ?.slice(0, 10)
                ?.map((x) => {
                  return errorType[x];
                }),
            },
          ],
        };
        this.pileRateObj = {
          time: Object.keys(pileErrorNum),
          tendencyArr: [
            {
              name: "累计故障数",
              data: Object.keys(pileErrorNum)?.map((x) => {
                return pileErrorNum[x];
              }),
            },
          ],
        };
      });
    },
  },
};
</script>

<style scoped lang="less">
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic {
  margin: 20px 0;
  .head {
    margin-bottom: 12px;
  }
}
</style>
