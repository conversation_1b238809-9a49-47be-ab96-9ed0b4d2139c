// 站点运维等级
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleCreate"
            v-has-permi="['station:level:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="handleUpdate(row)"
            v-has-permi="['station:level:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="handleLog(row)"
            v-has-permi="['station:level:log']"
          >
            日志
          </el-button>
        </template>
      </GridTable>
      <el-dialog
        title="操作日志"
        :visible.sync="logVisible"
        :close-on-click-modal="false"
        @close="closeLogDialog"
        append-to-body
        width="70%"
      >
        <Timeline :list="recordList"></Timeline>
      </el-dialog>
      <el-dialog
        :title="title"
        :visible.sync="addVisible"
        :close-on-click-modal="false"
        @close="closeAddDialog"
        append-to-body
        width="50%"
      >
        <div class="queryParamsWrap">
          <el-form
            :model="addForm"
            ref="addForm"
            :inline="true"
            label-width="110px"
            :rules="rules"
          >
            <el-row>
              <el-col>
                <el-form-item label="等级名称:" prop="gradeName">
                  <el-input
                    v-model="addForm.gradeName"
                    placeholder="请输入等级名称，长度100个字符以内"
                    style="width: 80%"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="等级时效:" prop="gradeTime">
                  <div style="display: flex;align-items: center">
                    <el-input
                      v-model="addForm.gradeTime"
                      placeholder="0≤等级时效≤10000"
                      style="width: 80%"
                      @input="handleInputChange"
                      type="number"
                    >
                      <template slot="append">h</template></el-input
                    >
                    <el-radio
                      v-model="addForm.gradeTimeRadio"
                      label="-1"
                      style="margin-left: 10px"
                      @change="handleRadioChange"
                      >不限</el-radio
                    >
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="备注:" prop="remark">
                  <el-input
                    v-model="addForm.remark"
                    type="textarea"
                    maxlength="500"
                    show-word-limit
                    size="mini"
                    rows="5"
                    placeholder="长度500个字符以内"
                    style="width: 80%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click.stop="closeAddDialog">取 消</el-button>
          <el-button type="primary" @click.stop="submitForm">确认</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import * as api from "@/api/operationMaintenanceManage/station/index.js";
import Timeline from "@/components/Timeline/index.vue";
import { listAllUser } from "@/api/common.js";
import {
  DATA_INFO_CLICK_CREATE_STATION_GRADE,
  DATA_INFO_CLICK_EDIT_STATION_GRADE,
} from "@/utils/track/track-event-constants";

export default {
  components: { AdvancedForm, GridTable, Timeline },
  data() {
    return {
      config: [],
      columns: [
        {
          type: "seq",
          title: "编号",
        },
        {
          field: "gradeName",
          title: "等级名称",
          customWidth: 150,
          showOverflowTooltip: true,
        },
        {
          field: "gradeTime",
          title: "等级时效（h）",
          formatter: ({ cellValue }) => {
            return cellValue == -1 ? "不限" : cellValue;
          },
        },
        {
          field: "createTime",
          title: "创建时间",
        },
        {
          field: "createName",
          title: "创建人",
          //   formatter: ({ cellValue, row, column }) => {
          //     console.log(this.userOption, cellValue);
          //     return (
          //       this.userOption.find((el) => el.dictValue == cellValue)
          //         ?.dictLabel || cellValue
          //     );
          //   },
        },
        {
          field: "updateTime",
          title: "修改时间",
        },
        {
          field: "updateName",
          title: "修改人",
        },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "stationLevelList",
      userOption: [], //用户列表
      recordList: [],
      logVisible: false,
      title: "新增站点运维等级",
      addForm: {
        gradeName: undefined,
        gradeTime: undefined,
        remark: undefined,
        gradeTimeRadio: undefined,
      },
      rules: {
        gradeName: [
          { required: true, message: "等级名称不能为空", trigger: "change" },
          { max: 100, message: "长度100个字符以内", trigger: "change" },
        ],
        gradeTime: [{ validator: this.customValidation, trigger: "change" }],
      },
      addVisible: false,
      isInputActive: false,
    };
  },
  created() {},
  mounted() {
    Promise.all([this.getListUser()]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    //提交
    async submitForm() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          const method =
            this.title === "修改站点运维等级"
              ? "submitEditLevel"
              : "submitAddLevel";
          const form = {
            ...this.addForm,
            gradeTime: this.addForm.gradeTimeRadio || this.addForm.gradeTime,
          };
          const { code, data } = await api[method](form);
          if (code != 10000) return;
          this.$message.success("提交成功");
          this.closeAddDialog();
          this.getList();

          if (this.title === "新增站点运维等级") {
            //新增
            this.reportTrackEvent(DATA_INFO_CLICK_CREATE_STATION_GRADE);
          } else {
            //修改
            this.reportTrackEvent(DATA_INFO_CLICK_EDIT_STATION_GRADE);
          }
        }
      });
    },
    customValidation(rule, value, callback) {
      console.log(/^[1-9]\d*$/.test(value), value);
      if (!value && !this.addForm.gradeTimeRadio) {
        console.log(value, this.addForm.gradeTimeRadio, "校验");
        callback(new Error("等级时效不能为空！"));
      } else if (
        value &&
        (value < 0 || value > 10000 || !/^[1-9]\d*$/.test(value))
      ) {
        callback(new Error("0≤等级时效≤10000，且为整数"));
      } else {
        callback();
      }
    },
    handleInputChange(value) {
      this.isInputActive = true;
      this.addForm.gradeTimeRadio = "";
    },
    handleRadioChange(value) {
      this.isInputActive = false;
      if (!this.isInputActive) {
        this.addForm.gradeTime = ""; // 清空输入框的值
      }
    },
    handleCreate() {
      this.addVisible = true;
      this.title = "新增站点运维等级";
    },
    handleUpdate(row) {
      this.addVisible = true;
      this.title = "修改站点运维等级";
      const { gradeTime } = row;
      this.addForm = {
        ...row,
        gradeTimeRadio: gradeTime == "-1" ? "-1" : undefined,
        gradeTime: gradeTime == "-1" ? undefined : gradeTime,
      };
    },
    closeAddDialog() {
      this.addVisible = false;
      this.addForm = {
        gradeName: undefined,
        gradeTime: undefined,
        remark: undefined,
        gradeTimeRadio: undefined,
      };
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    handleLog(row) {
      this.logVisible = true;
      api.queryLevelLog({ gradeId: row.gradeId }).then((res) => {
        this.recordList = res.data;
      });
    },
    closeLogDialog() {
      this.logVisible = false;
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.beginTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      api
        .queryLevelList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },

    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "gradeName",
          title: "等级名称",
          type: "input",
          placeholder: "请输入等级名称",
        },
        {
          key: "rangeTime",
          title: "创建时间",
          type: "dateRange",
          placeholder: "请选择创建时间",
          startPlaceholder: "创建开始日期",
          endPlaceholder: "创建结束日期",
        },
      ];
    },
  },
};
</script>

<style></style>
