<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="showAdd"
            v-has-permi="['buildTeam:manage:add']"
          >
            新增
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['buildTeam:manage:detail']"
          >
            详情
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="showUpdate(row)"
            v-has-permi="['buildTeam:manage:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="deleteRow(row)"
            v-has-permi="['buildTeam:manage:delete']"
          >
            删除
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <detail
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :team-id="teamId"
      :title="title"
      :type="type"
      @update-list="getList"
    >
    </detail>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import Detail from "@/views/buildTeam/components/detail.vue";
import {
  queryBuildTeamByPage,
  deleteBuildTeam,
} from "@/api/buildTeam/buildTeam";

export default {
  name: "buildTeam",
  components: { AdvancedForm, GridTable, Detail },
  data() {
    return {
      config: [],
      columns: [
        {
          field: "teamName",
          title: "施工队名称",
          treeNode: true,
          width: 180,
        },
        {
          field: "manager",
          title: "负责人",
          width: 180,
        },
        {
          field: "mobile",
          title: "联系电话",
          width: 180,
        },
        {
          field: "num",
          title: "人数",
          width: 180,
        },
        {
          field: "remark",
          title: "备注",
          width: 180,
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "buildTeam",
      detailVisible: false,
      title: "详情",
      type: "add",
      teamId: undefined,
      pileBrandDict: [],
    };
  },
  async created() {
    this.initConfig();
    this.getList();
  },
  mounted() {},
  methods: {
    showDetail(row) {
      this.title = "详情";
      this.type = "detail";
      this.teamId = row.teamId;
      this.detailVisible = true;
    },
    showAdd() {
      this.title = "新增";
      this.type = "add";
      this.detailVisible = true;
    },
    showUpdate(row) {
      this.title = "编辑";
      this.type = "update";
      this.teamId = row.teamId;
      this.detailVisible = true;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      queryBuildTeamByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    deleteRow(row) {
      this.$confirm("确定删除该施工队吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          teamId: row.teamId,
        };
        deleteBuildTeam(data).then((res) => {
          if (res?.success) {
            this.$message.success("删除成功");
            //更新列表
            this.getList();
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "teamName",
          title: "施工队名称",
          type: "input",
          placeholder: "请填写施工队名称",
        },
        {
          key: "manager",
          title: "负责人",
          type: "input",
          placeholder: "请填写负责人",
        },
      ];
    },
  },
};
</script>

<style scoped></style>
