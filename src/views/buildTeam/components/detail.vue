<!-- 新增施工队 -->
<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :close-on-click-modal="false"
    @close="closeDialog"
    append-to-body
    width="50%"
  >
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="基础信息" name="1"></el-tab-pane>
      <el-tab-pane label="资质证书" name="2"></el-tab-pane>
      <el-tab-pane label="服务范围" name="3"></el-tab-pane>
    </el-tabs>

    <div v-show="activeName === '1'">
      <el-form :model="form1" :rules="rules1" ref="form1" label-width="110px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="施工队名称" prop="teamName">
              <el-input v-model="form1.teamName" size="mini" :disabled="type === 'detail'" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input v-model="form1.manager" size="mini" :disabled="type === 'detail'" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" prop="mobile">
              <el-input v-model="form1.mobile" size="mini" :disabled="type === 'detail'" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人数" prop="num">
              <el-input-number v-model="form1.num" size="mini" :disabled="type === 'detail'" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form1.remark" size="mini" :disabled="type === 'detail'" style="width: 100%;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div v-show="activeName === '2'">
      <div>功能暂未开放,敬请期待</div>
      <!--      <el-form :model="form2" :rules="rules2" ref="form2" :inline="true" label-width="110px">-->
      <!--        <el-row :gutter="20">-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="资质" prop="builder">-->
      <!--              <el-input v-model="form3.builder" size="mini" />-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </el-form>-->
    </div>

    <div v-show="activeName === '3'">
      <div>功能暂未开放,敬请期待</div>
      <!--      <el-form :model="form3" :rules="rules3" ref="form3" :inline="true" label-width="110px">-->
      <!--        <el-row :gutter="20">-->
      <!--          <el-col :span="12">-->
      <!--            <el-form-item label="服务范围" prop="builder">-->
      <!--              <el-input v-model="form3.builder" size="mini" />-->
      <!--            </el-form-item>-->
      <!--          </el-col>-->
      <!--        </el-row>-->
      <!--      </el-form>-->
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click.stop="closeDialog">取 消</el-button>
      <el-button type="primary" @click.stop="submitInfo">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { addBuildTeam, updateBuildTeam, queryBuildTeamById } from "@/api/buildTeam/buildTeam";

export default {
  components: {},
  props: ["visible", "teamId", "type", "title"],
  data() {
    return {
      activeName: "1",
      form1: {
        teamId: undefined,
        teamName: undefined,
        manager: undefined,
        mobile: undefined,
        num: 0,
        remark: undefined
      },
      form2: {
        fileList: []
      },
      form3: {},
      rules1: {
        teamName: [
          { required: true, message: "请填写施工队名称", trigger: "blur" }
        ],
        manager: [{ required: true, message: "请填写负责人", trigger: "blur" }],
        mobile: [{ required: true, message: "请填写联系方式", trigger: "blur" }]
      },
      rules2: {
        stationName: [
          { required: true, message: "请填写站点名称", trigger: "blur" }
        ]
      },
      rules3: {
        builder1: [
          { required: true, message: "请选择业务类型", trigger: "blur" }
        ]
      }
    };
  },
  computed: {},
  watch: {},
  async created() {
    if (this.type !== "add") {
      this.queryBuildTeamById();
    }
  },
  mounted() {
  },
  methods: {
    queryBuildTeamById() {
      let params = {
        teamId: this.teamId
      };
      queryBuildTeamById(params).then(res => {
        if (res?.success) {
          this.form1.teamId = res.data.teamId;
          this.form1.teamName = res.data.teamName;
          this.form1.manager = res.data.manager;
          this.form1.mobile = res.data.mobile;
          this.form1.num = res.data.num;
          this.form1.remark = res.data.remark;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    closeDialog() {
      this.$emit("update:teamId", null);
      this.$emit("update:visible", false);
    },
    handleClick() {
    },
    submitInfo() {
      this.$refs.form1.validate(valid => {
        if (valid) {
          let params = {
            ...this.form1
          };

          const loading = this.$loading({
            lock: true,
            text: "提交中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });

          try {
            if (this.type === "add") {
              addBuildTeam(params).then(res => {
                loading.close();
                if (res?.success) {
                  this.closeDialog();
                  this.$emit("update-list");
                  this.$message.success(res.message);
                } else {
                  this.$message.error(res.message);
                }
              }).finally(() => {
                loading.close();
              });
            } else if (this.type === "update") {
              updateBuildTeam(params).then(res => {
                loading.close();
                if (res?.success) {
                  this.closeDialog();
                  this.$emit("update-list");
                  this.$message.success(res.message);
                } else {
                  this.$message.error(res.message);
                }
              }).finally(() => {
                loading.close();
              });
            }

          } catch (e) {
            loading.close();
          }
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
