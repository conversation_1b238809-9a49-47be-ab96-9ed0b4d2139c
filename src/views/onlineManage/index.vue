<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane
        label="待上线"
        name="1"
        v-if="checkPermission(['onlineManage:tab:todo'])"
      >
        <WaitOnline v-if="activeName === '1'"></WaitOnline>
      </el-tab-pane>
      <el-tab-pane
        label="上线中"
        name="2"
        v-if="checkPermission(['onlineManage:tab:doing'])"
      >
        <Onlineing v-if="activeName === '2'"></Onlineing>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import WaitOnline from "@/views/onlineManage/components/waitOnline.vue";
import Onlineing from "@/views/onlineManage/components/onlineing.vue";
import checkPermission from "@/utils/permission.js";
import { ONLINE_CLICK_TAB } from "@/utils/track/track-event-constants";

export default {
  name: "index",
  components: {
    WaitOnline,
    Onlineing,
  },
  data() {
    return {
      activeName: "1",
      key: 1,
    };
  },
  methods: {
    checkPermission,
    handleTabClick() {
      this.reportTrackEvent(ONLINE_CLICK_TAB, {
        tabname: this.activeName == "1" ? "待上线" : "上线中"
      });
    }
  }
};
</script>

<style scoped></style>
