<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="searchForm.total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="showProjectInfo(row)" type="text" size="large">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="showOnlineFlowInfo(row)" type="text" size="large">
            {{ row.flowCount }}
          </el-button>
        </template>
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            :disabled="row.status === '11'"
            type="text"
            size="large"
            @click="startOnline(row)"
            v-has-permi="['onlineManage:onlineing:start']"
          >
            添加上线工单
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <!--  查看项目详情  -->
    <ProjectInfo
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :page-type="pageType"
      :project-id="projectId"
      :title="dialogTitle"
    />
    <StartOnline
      v-if="startOnlineVisible"
      :visible.sync="startOnlineVisible"
      :project-id="projectId"
      :is-start="isStart"
      :title="onlineTitle"
      @update-list="loadData"
    />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import ProjectInfo from "@/views/projectManage/addProject.vue";
import { queryOnlineProjectInfoByPage } from "@/api/onlineManage";
import { regionData } from "element-china-area-data";
import StartOnline from "./startOnline.vue";
export default {
  components: {
    AdvancedForm,
    GridTable,
    StartOnline,
    ProjectInfo,
  },
  data() {
    return {
      tableId: "onlineManageTable",
      loading: false,
      startOnlineVisible: false,
      tableData: [],
      config: [],
      columns: [
        {
          title: "项目编码",
          field: "projectCode",
          // slots: { default: "deviceTypeImg" }
        },
        {
          field: "projectName",
          title: "项目名称",
        },
        {
          field: "remark",
          title: "项目备注",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
        },
        {
          field: "onlineManager",
          title: "上线负责人",
        },
        {
          field: "flowCount",
          title: "上线工单",
          slots: { default: "deviceTypeImg" },
        },
        {
          field: "finishCount",
          title: "已完成数量",
        },
        {
          field: "notFinishCount",
          title: "未完成数量",
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      rowData: null,
      searchForm: {
        projectCode: "",
        projectName: "",
        stationCode: null,
        stationName: "",
        statusList: ["6", "7", "8"],
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      projectId: undefined,
      isStart: false,
      onlineTitle: "添加上线工单",
      detailVisible: false,
      dialogTitle: "详情",
      pageType: "detail",
    };
  },
  async created() {
    this.initConfig();
  },
  mounted() {
    this.loadData();
  },
  methods: {
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请填写项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请填写项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请填写站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
    showProjectInfo(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.detailVisible = true;
    },
    showOnlineFlowInfo(row) {
      this.$router.push({
        path: "/workOrderWorkBench/workOrderWorkbench",
        query: { projectId: row.projectId, relaBizType: "online" },
      });
    },
    startOnline(row) {
      this.projectId = row.projectId;
      this.startOnlineVisible = true;
    },
    closeVisible() {
      this.rowData = {};
      this.startOnlineVisible = false;
    },
    //获取项目列表
    async loadData() {
      this.loading = true;
      const res = await queryOnlineProjectInfoByPage(this.searchForm);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data;
        this.searchForm.total = res.total;
        this.loading = false;
      } else {
        this.loading = false;
        this.$message.error(res.message);
      }
    },
    changePage() {
      this.loadData();
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      if (Array.isArray(this.searchForm.region)) {
        this.searchForm["provinceList"] = [];
        this.searchForm["cityList"] = [];
        this.searchForm["countyList"] = [];
        this.searchForm.region.forEach((item) => {
          if (item[0]) {
            this.searchForm["provinceList"].push(item[0]);
          }
          if (item[1]) {
            this.searchForm["cityList"].push(item[1]);
          }
          if (item[2]) {
            this.searchForm["countyList"].push(item[2]);
          }
        });
        this.searchForm.region;
      }
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
    submitForm() {
      this.$refs.startOnline.$refs.form1.validate((valid1) => {
        this.$refs.startOnline.$refs.form2.validate((valid2) => {
          if (valid1 && valid2) {
            console.log(this.$refs.startOnline.form);
          }
        });
      });
    },
  },
};
</script>

<style></style>
