<template>
  <div>
    <el-form :model="form" ref="form" :inline="true">
      <el-row v-for="(item, index) in form.typeList" :key="index">
        <el-col :span="12">
          <el-form-item
            label="工单类型"
            :prop="'typeList.' + index + '.orderType'"
            :rules="{
              required: true,
              message: '工单类型不能为空',
              trigger: 'blur',
            }"
          >
            <el-select
              v-model="item.orderType"
              size="mini"
              placeholder="请选择"
            >
              <el-option
                v-for="item in useFlowList"
                :key="item.flowType"
                :label="item.flowTypeName"
                :value="item.flowType"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button
            size="mini"
            v-if="form.typeList.length > 1"
            @click="delItem(index)"
            >删除</el-button
          >
          <el-button type="primary" size="mini" @click="addItem"
            >添加</el-button
          >
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { listByBusinessType } from "@/api/roadTestAcceptance/otherIndex.js";
export default {
  data() {
    return {
      useFlowList: [],
      form: {
        typeList: [
          {
            orderType: "",
          },
        ],
      },
    };
  },
  created() {
    this.getUseFlowList();
  },
  methods: {
    //获取工单类型列表
    async getUseFlowList() {
      const { code, data } = await listByBusinessType();
      if (code != 10000) return;
      this.useFlowList = data;
    },
    delItem(index) {
      this.form.typeList.splice(index, 1);
    },
    addItem() {
      this.form.typeList.push({
        orderType: "",
      });
    },
  },
};
</script>

<style></style>
