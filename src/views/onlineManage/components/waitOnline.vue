<template>
  <div class="app-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="roadDataTotal"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button @click="showProjectInfo(row)" type="text" size="large">
            {{ row.projectCode }}
          </el-button>
        </template>
        <!--        <template slot="deviceTypeImg" slot-scope="{ row, $index }">-->
        <!--          <el-button @click="showOnlineFlowInfo(row)" type="text" size="large">-->
        <!--            {{ row.flowCount }}-->
        <!--          </el-button>-->
        <!--        </template>-->
        <template slot="operation" slot-scope="{ row, $index }">
          <el-button
            :disabled="row.status === '11'"
            type="text"
            size="large"
            @click="startOnline(row)"
            v-has-permi="['onlineManage:waitOnline:start']"
          >
            开始上线
          </el-button>
        </template>
      </GridTable>
    </el-card>
    <!--  查看项目详情  -->
    <ProjectInfo
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :page-type="pageType"
      :project-id="projectId"
      :title="dialogTitle"
    />

    <StartOnline
      v-if="startOnlineVisible"
      :visible.sync="startOnlineVisible"
      :project-id="projectId"
      :is-start="isStart"
      :title="onlineTitle"
      @update-list="loadData"
    />
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import ProjectInfo from "@/views/projectManage/addProject.vue";
import { queryOnlineProjectInfoByPage } from "@/api/onlineManage";
import StartOnline from "./startOnline.vue";
import { regionData } from "element-china-area-data";

export default {
  components: {
    AdvancedForm,
    GridTable,
    StartOnline,
    ProjectInfo,
  },
  data() {
    return {
      tableId: "onlineManageTable",
      loading: false,
      startOnlineVisible: false,
      tableData: [],
      config: [],
      columns: [
        {
          field: "projectCode",
          title: "项目编码",
          slots: { default: "deviceTypeImg" },
        },
        {
          field: "projectName",
          title: "项目名称",
        },
        {
          field: "remark",
          title: "项目备注",
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "stationAddress",
          title: "地址",
        },
        {
          title: "操作",
          minWidth: 200,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      rowData: null,
      searchForm: {
        projectCode: "",
        projectName: "",
        stationCode: null,
        stationName: "",
        statusList: ["5"],
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      roadDataTotal: 0,
      finallySearch: null,
      projectId: undefined,
      isStart: true,
      onlineTitle: "发起上线",
      detailVisible: false,
      dialogTitle: "详情",
      pageType: "detail",
    };
  },
  async created() {
    this.initConfig();
  },
  computed: {},
  mounted() {
    //获取项目列表
    this.loadData();
  },
  methods: {
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请填写项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请填写项目名称",
        },
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请填写站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
      ];
    },
    showProjectInfo(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.detailVisible = true;
    },
    showOnlineFlowInfo(row) {
      this.$router.push({
        path: "/workOrderWorkBench/workOrderWorkbench",
        query: { projectId: row.projectId, relaBizType: "online" },
      });
    },
    startOnline(row) {
      this.projectId = row.projectId;
      this.startOnlineVisible = true;
    },
    closeVisible() {
      this.rowData = {};
      this.startOnlineVisible = false;
    },
    //获取项目列表
    async loadData(params) {
      this.loading = true;
      let args = this._.cloneDeep(params ? params : this.searchForm);
      if (Array.isArray(args.region)) {
        args["provinceList"] = [];
        args["cityList"] = [];
        args["countyList"] = [];
        args.region.forEach((item) => {
          if (item[0]) {
            args["provinceList"].push(item[0]);
          }
          if (item[1]) {
            args["cityList"].push(item[1]);
          }
          if (item[2]) {
            args["countyList"].push(item[2]);
          }
        });
        delete args.region;
      }
      this.finallySearch = args;

      const res = await queryOnlineProjectInfoByPage(args);
      this.loading = false;
      if (res.code === "10000") {
        this.tableData = res.data || [];
        this.roadDataTotal = res.total;
        this.loading = false;
      } else {
        this.loading = false;
        this.$message.error(res.message);
      }
    },
    changePage() {
      this.loadData();
    },
    //查询列表
    handleQuery(params) {
      this.searchForm.pageNum = 1;
      params.pageSize = this.searchForm.pageSize;
      this.loadData();
    },
    //重置查询
    resetQuery() {
      this.searchForm = this.$options.data.call(this).searchForm;
      this.loadData();
    },
  },
};
</script>

<style></style>
