<template>
  <div class="content-wrap">
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="60%"
    >
      <div v-if="isStart">
        <div class="sub_title">执行人信息</div>
        <el-form :model="form" :rules="rules" ref="form1" :inline="true">
          <el-row>
            <el-col :span="12">
              <el-form-item prop="onlineUser" label="上线负责人">
                <el-select
                  v-model="form.onlineUser"
                  filterable
                  size="mini"
                  placeholder="请选择上线负责人"
                >
                  <el-option
                    v-for="item in userOption"
                    :key="item.dictValue"
                    :label="item.nickName"
                    :value="item.userId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="sub_title">创建工单</div>
      <el-form :model="addForm" ref="addForm" :inline="true">
        <el-row v-for="(item, index) in addForm.typeList" :key="index">
          <el-col :span="9">
            <el-form-item label="业务类型">
              <el-select
                v-model="item.relaBizType"
                @change="changeRelaBizType(item, index)"
                size="mini"
                placeholder="请选择"
                :disabled="disabledFlag"
              >
                <el-option
                  v-for="item in businessTypeOption"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label="工单类型"
              :prop="'typeList.' + index + '.orderType'"
              :rules="{
                required: true,
                message: '工单类型不能为空',
                trigger: 'blur'
              }"
            >
              <el-select
                v-model="item.orderType"
                size="mini"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in item.useFlowList"
                  :key="item.flowType"
                  :label="item.flowTypeName"
                  :value="item.flowType"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button size="mini" v-if="addForm.typeList.length > 1" @click="delItem(index)">删除</el-button>
            <el-button type="primary" size="mini" @click="addItem">添加</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser } from "@/api/common";
import { useFlowList as useFlowListApi } from "@/api/orderScheduling/workStation.js";
import { startOnline, addOnlineFlow } from "@/api/onlineManage";

export default {
  props: ["visible", "projectId", "isStart", "title"],
  data() {
    return {
      useFlowList: [], //工单类型
      rules: {
        onlineUser: [
          { required: true, message: "请选择路测负责人", trigger: "change" }
        ]
      },
      form: {
        onlineUser: undefined
      },
      addForm: {
        typeList: [
          {
            relaBizType: "online",
            orderType: ""
          }
        ]
      },
      businessTypeOption: [],
      onlineBusinessType: ["online"],
      //人员列表
      userOption: [],
      disabledFlag: true
    };
  },
  created() {},
  async mounted() {
    await this.getListUser();
    //获取业务类型字典
    await this.getDicts("flow_business_type").then(response => {
      if (response?.data && response?.data.length > 0) {
        response.data.forEach(item => {
          if (this.onlineBusinessType.indexOf(item.dictValue) !== -1) {
            this.businessTypeOption.push(item);
          }
        });
      }
    });
    let list = await this.getUseFlowList("online");
    this.$set(this.addForm.typeList[0], "useFlowList", list);
  },
  methods: {
    closeDialog() {
      this.$emit("update:visible", false);
      this.$emit("update-list");
    },
    submitForm() {
      let businessFlowList = [];
      this.addForm.typeList.forEach(item => {
        if (item.orderType) {
          let obj = {
            relaBizType: item.relaBizType, //业务类型
            flowType: item.orderType, //流程类型
            flowTypeName: item.useFlowList.find(el => el.flowType === item.orderType).flowTypeName, //流程类型名称
            flowKey: item.useFlowList.find(el => el.flowType === item.orderType).flowKey, //流程必传
            flowName: item.useFlowList.find(el => el.flowType === item.orderType).flowName, //流程名称必传
          };
          businessFlowList.push(obj);
        }
      });
      if (this.isStart) {
        this.$refs.form1.validate(valid1 => {
          this.$refs.addForm.validate(valid2 => {
            if (valid1 && valid2) {
              let params = {
                projectId: this.projectId,
                onlineUser: this.form.onlineUser,
                businessFlowList: businessFlowList
              };
              startOnline(params).then(res => {
                if (res?.success) {
                  this.closeDialog();
                  this.$message.success(res.message);
                } else {
                  this.$message.error(res.message);
                }
              });
            }
          });
        });
      } else {
        this.$refs.addForm.validate(valid => {
          if (valid) {
            let params = {
              projectId: this.projectId,
              businessFlowList: businessFlowList
            };
            addOnlineFlow(params).then(res => {
              if (res?.success) {
                this.closeDialog();
                this.$message.success(res.message);
              } else {
                this.$message.error(res.message);
              }
            });
          }
        });
      }
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999
      };
      const { code, data } = await listUser(params);
      if (code !== "10000") return;
      this.userOption = data;
    },
    //获取工单类型列表
    async getUseFlowList(businessType) {
      let params = {
        businessTypeList: businessType
      };
      const { code, data } = await useFlowListApi(params);
      if (code !== "10000") return;
      return data;
    },
    delItem(index) {
      this.form2.typeList.splice(index, 1);
    },
    async addItem() {
      let length = this.addForm.typeList.length;
      this.addForm.typeList.push({
        relaBizType: "online",
        orderType: ""
      });
      let list = await this.getUseFlowList("online");
      this.$set(this.addForm.typeList[length], "useFlowList", list);
    },
    //切换业务类型，查询工单类型
    async changeRelaBizType(item, index) {
      this.addForm.typeList[index].relaBizType = item.relaBizType;
      let list = await this.getUseFlowList(item.relaBizType);
      this.$set(this.addForm.typeList[index], "useFlowList", list);
    }
  }
};
</script>

<style lang="less" scoped>
.content-wrap {
  margin-top: -20px;
}

/* 标题样式 */
.sub_title {
  margin-left: 10px;
  display: list-item;
  line-height: 40px;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 20px;
}

.sub_title::marker {
  content: "| ";
  color: #029c7c;
  font-size: 18px;
  font-weight: bold;
}
</style>
