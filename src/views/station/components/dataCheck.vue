//能投大区数据核验弹窗
<template>
  <el-dialog
    title="能投大区数据核验结果"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="70%"
    append-to-body
    @close="handleCancel"
    v-loading="loading"
    element-loading-text="数据对比中，请耐心等待..."
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.7)"
  >
    <el-form
      :model="checkForm"
      ref="checkForm"
      label-width="150px"
      :rules="rules"
    >
      <el-form-item label="数据变更原因:" prop="reason">
        <el-input
          v-model="checkForm.reason"
          maxlength="500"
          type="textarea"
          :rows="5"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        ref="gridTable"
        row-id="stationId"
      >
      </GridTable>
    </el-card>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" size="medium">取 消</el-button
      ><el-button type="primary" @click="handleSubmit" size="medium"
        >更新</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import {
  queryCheckStationList,
  updateCheckStation,
} from "@/api/station/station.js";
import { DATA_INFO_CLICK_STATION_DATA_CHECK } from "@/utils/track/track-event-constants";

export default {
  components: {
    AdvancedForm,
    GridTable,
  },
  data() {
    return {
      stationCode: "",
      visible: false,
      needQueryCache: false,
      rules: {
        reason: [{ required: true, message: "请填写原因", trigger: "change" }],
      },
      checkForm: { reason: undefined },
      config: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      // handRow: {
      //   stationIds: [],
      // },
      selectedData: [],
      total: 0,
      loading: false,
      tableId: "checkStationList",
      tableData: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "stationCode",
          title: "站点编码",
          treeNode: true,
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "platformRegionName",
          title: "能投大区（充电运营平台）",
        },
        {
          field: "maintenanceRegionName",
          title: "能投大区（能源维保通）",
        },
      ],
    };
  },
  async created() {
    this.initConfig();
    // this.getList();
  },
  methods: {
    handleCancel() {
      this.visible = false;
      this.$refs.checkForm.resetFields();
      this.$refs.gridTable.clearTips();
    },
    open(code) {
      this.needQueryCache = false;
      this.stationCode = code;
      this.visible = true;
      this.getList();
    },
    handleSubmit() {
      if (this.selectedData.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }
      this.$refs.checkForm.validate((valid) => {
        if (!valid) return;
        const params = {
          recordLists: this.selectedData.map((x) => {
            return {
              ...x,
              beforeRegion: x.maintenanceRegion,
              afterRegion: x.mappingMaintenanceRegion,
            };
          }),
          reason: this.checkForm.reason,
        };
        console.log("提交的数据", params);
        updateCheckStation(params).then((res) => {
          if (res.code == "10000") {
            this.$message.success("更新成功");
            this.handleCancel();
            this.$emit("refreshDataList");

            this.reportTrackEvent(DATA_INFO_CLICK_STATION_DATA_CHECK);
          }
        });
      });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.selectedData = tableData;
      // this.handRow = {
      //   stationIds: [],
      //   stationNames: [],
      // };
      // this.handRow.count = tableData.length;
      // if (tableData.length > 0) {
      //   this.handRow.stationIds = tableData.map((v) => v.stationId);
      //   this.handRow.stationNames = tableData.map((v) => v.stationName);
      // }
      // console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.needQueryCache = true;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.needQueryCache = true;
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入或选择站点名称搜索",
        },
      ];
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
        needQueryCache: this.needQueryCache,
        stationCode: this.stationCode,
      };
      queryCheckStationList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
