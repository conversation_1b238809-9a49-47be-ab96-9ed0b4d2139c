<template>
  <el-dialog
    title="标签管理"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="50%"
    append-to-body
    @close="handleCancel"
  >
    <el-transfer v-model="checked" :data="transferData" filterable :titles="['未配置标签','已配置标签']" :props="{key: 'dictValue', label: 'dictLabel'}"> </el-transfer>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleSubmit" size="small">保存</el-button>
      <el-button @click="handleCancel" size="small">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { regionData } from "element-china-area-data";
import { saveStationTag } from "@/api/station/station";

export default {
  name: "detail",
  props: ["visible", "stationId"],
  data() {
    return {
      rules: {},
      stationTypeDict: [],
      stationStatusDict: [],
      openFlagDict: [],
      stationChargeTypeDict: [],
      constructionDict: [],
      providerList: [],
      districtOptions: regionData, //省市数据
      transferData: [], // 总数据
      checked: [], // 右侧所分配数组
    };
  },
  async created() {
    //站点类型
    await this.getDicts("cm_station_tag").then(response => {
      this.transferData = response?.data;
    });
  },
  methods: {
    closeDialog() {
      this.$emit("update", {});
    },
    handleCancel() {
      this.$emit("close", {});
    },
    open(row) {
      if (row.stationTag) {
        this.checked = row.stationTag.split(',')
      }else {
        this.checked = []
      }
    },
    handleSubmit(){
      const vm = this
      const param = {stationId: this.stationId, stationTag: this.checked.length > 0 ? this.checked.join(',') : ''}
      saveStationTag(param).then(res => {
        if (res?.success) {
          vm.$message.success('保存标签成功')
          vm.closeDialog()
        }
      })
    },
  }
};
</script>

<style scoped>

</style>