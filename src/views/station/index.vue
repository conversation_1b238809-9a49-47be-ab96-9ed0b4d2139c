<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['station:list:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="stationId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCheckModal(handRow.stationCodes)"
            v-has-permi="['station:dataCheck:check']"
          >
            能投大区数据核验
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCheckRecord"
            v-has-permi="['station:dataCheck:record']"
          >
            能投大区数据核验记录
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleLevel"
            v-has-permi="['station:level:list']"
          >
            站点运维等级
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchLevel"
            v-has-permi="['station:batchLevel']"
          >
            批量配置站点等级
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchTag"
            v-has-permi="['station:tag:batchAdd']"
          >
            批量配置标签
          </el-button>
          <el-button
            size="mini"
            type="primary"
            @click.stop="handleBatchStation"
            v-has-permi="['station:batch:import']"
          >
            导入站点
          </el-button>
          <!-- <el-button size="mini" type="primary" @click.stop="handleExport">
            导出
          </el-button> -->
        </template>
        <template slot="projectCode" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="handleProjectList(row)">
            {{ row.projectCode }}
          </el-button>
        </template>
        <template slot="stationTag" slot-scope="{ row, $index }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in row.stationTagList"
            >{{ item }}</el-tag
          >
        </template>
        <template slot="operation" slot-scope="{ row }">
          <!-- <el-button type="text" size="large" @click="editTag(row, true)">
            增标签
          </el-button>
          <el-button type="text" size="large" @click="editTag(row, false)">
            减标签
          </el-button> -->
          <!-- <el-button type="text" size="large" @click="tagManage(row)">
            标签管理
          </el-button> -->
          <!-- <el-button type="text" size="large" @click="handleLog(row)">
            标签日志
          </el-button> -->
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['station:list:detail']"
          >
            详情
          </el-button>
          <!-- <el-button type="text" size="large" @click="handleRowLevel(row)">
            站点运维等级
          </el-button> -->
          <el-dropdown @command="(command) => handleCommand(command, row)">
            <el-button type="text" size="large">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                :command="item.command"
                v-for="(item, index) in btnArr"
                :key="index"
                v-has-permi="[item.permission]"
              >
                <el-button type="text" size="large">
                  {{ item.title }}
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template slot="jump" slot-scope="{ row, column }">
          <el-button
            type="text"
            size="large"
            @click="handleJump(row, column.property)"
          >
            {{ row[column.property] }}
          </el-button>
        </template>
        <template slot="projectJump" slot-scope="{ row, column }">
          <div
            v-for="(item, index) in getArr(row[column.property])"
            :key="index"
          >
            <el-tooltip :content="item" placement="top">
              <el-button
                type="text"
                size="large"
                @click="handleJump(item, column.property)"
              >
                <div
                  style="max-width: 200px;overflow: hidden; white-space: nowrap; text-overflow: ellipsis;"
                >
                  {{ item }}
                </div>
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </GridTable>
    </el-card>

    <detail
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :row-info="rowInfo"
      @update-list="getList"
    />
    <AddProject
      v-if="addProjectDialogVisible"
      :visible.sync="addProjectDialogVisible"
      :page-type="pageType"
      :project-id="projectId"
      :title="dialogTitle"
    />
    <tag-manage
      :stationId="stationId"
      ref="tagManage"
      :visible="tagManageVisible"
      @update="updateTag"
      @close="close"
    />
    <TagEdit ref="tagEdit" @refreshDataList="getList"></TagEdit>
    <DataCheck ref="dataCheck" @refreshDataList="getList"></DataCheck>
    <el-dialog
      title="操作日志"
      :visible.sync="logVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
      width="70%"
    >
      <Timeline :list="recordList"></Timeline>
    </el-dialog>
    <el-dialog
      title="配置站点运维等级"
      :visible.sync="levelVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <el-form
        :model="levelForm"
        ref="levelForm"
        label-width="140px"
        :rules="rules"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="站点运维等级:" prop="gradeId">
              <el-select
                v-model="levelForm.gradeId"
                style="width: 100%"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.gradeId"
                  :label="item.gradeName"
                  :value="item.gradeId"
                >
                </el-option>
              </el-select> </el-form-item
          ></el-col>
        </el-row>
        <el-form-item label="备注:" prop="remark">
          <el-input
            v-model="levelForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内。"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel" size="small">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="批量配置站点等级"
      :visible.sync="batchLevelVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleBatchCancel"
    >
      <el-form
        :model="batchLevelForm"
        ref="batchLevelForm"
        label-width="140px"
        :rules="rules"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="下载模版:" prop="file">
              <el-link type="primary" @click="downExcel">点击下载</el-link>
            </el-form-item></el-col
          >
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传文件:" prop="file">
              <el-upload
                ref="upload"
                :limit="1"
                accept=".xlsx, .xls"
                :headers="upload.headers"
                :action="upload.url"
                :disabled="upload.isUploading"
                :on-success="handleFileSuccess"
                :on-error="handleFileError"
                :on-change="handleChangeFile"
                :auto-upload="false"
                :data="{ remark: batchLevelForm.remark }"
              >
                <el-button>选择文件</el-button>
                <div slot="tip" class="el-upload__tip">
                  支持xlxs、xls格式，2G以内。
                </div>
              </el-upload>
            </el-form-item></el-col
          >
        </el-row>
        <el-form-item label="备注:" prop="remark">
          <el-input
            v-model="batchLevelForm.remark"
            maxlength="500"
            type="textarea"
            :rows="5"
            show-word-limit
            placeholder="请输入具体的原因描述，500个字符以内。"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="handleBatchCancel"
          size="small"
          :loading="submitLoading"
          >取 消
        </el-button>
        <el-button
          type="primary"
          @click="handleBatchSubmit"
          size="small"
          :loading="submitLoading"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <BatchUpload
      @uploadSuccess="getList"
      ref="batchUploadTag"
      title="批量配置站点标签"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
    >
      <template #templateBtn>
        <el-button type="primary" @click="downTagExcel(1)"
          >点击下载增标签模板</el-button
        >
        <el-button type="primary" @click="downTagExcel(0)"
          >点击下载减标签模板</el-button
        >
      </template>
      <template #extraForm="{params}">
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="params.remark"
                rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
                placeholder="请输入具体的原因描述，500个字符以内"
              /> </el-form-item
          ></el-col>
        </el-row>
      </template>
    </BatchUpload>
    <BatchUpload
      @uploadSuccess="getList"
      ref="batchUploadStation"
      title="批量导入站点"
      :uploadApi="stationUploadObj.api"
      :templateUrl="stationUploadObj.url"
      :extraData="stationUploadObj.extraData"
      maxSizeText="500M"
      :maxSize="0.5"
    >
      <template #extraForm="{params}">
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注:" prop="remark">
              <el-input
                v-model="params.remark"
                rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
                placeholder="请输入具体的原因描述，500个字符以内"
              /> </el-form-item
          ></el-col>
        </el-row>
      </template>
    </BatchUpload>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import Detail from "@/views/station/components/detail.vue";
import AddProject from "@/views/projectManage/addProject.vue";
import TagEdit from "./components/tagEdit.vue";
import DataCheck from "./components/dataCheck.vue";
import checkPermission from "@/utils/permission.js";
import Timeline from "@/components/Timeline/index.vue";
import {
  queryStationInfoByPage,
  exportExcel,
  queryLogList,
  queryLevelList,
  submitEditRowLevel,
  getTagList,
} from "@/api/station/station";
import { getAllDeptList } from "@/api/operationWorkOrder";
import { getToken } from "@/utils/auth";
import TagManage from "@/views/station/components/tagManage.vue";
import { regionData } from "element-china-area-data";
import {
  DATA_INFO_CLICK_STATION_LEVEL_CONFIG,
  DATA_INFO_CLICK_STATION_REPORT,
} from "@/utils/track/track-event-constants";
import BatchUpload from "@/components/BatchUpload/index.vue";

export default {
  name: "maintenanceStationList",
  components: {
    AdvancedForm,
    GridTable,
    Detail,
    AddProject,
    TagManage,
    TagEdit,
    DataCheck,
    Timeline,
    BatchUpload,
  },
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      stationUploadObj: {
        api: "/export/report/importStation",
        url: "/charging-maintenance-ui/static/批量导入站点模板.xlsx",
        extraData: {
          remark: "",
        },
      },
      uploadObj: {
        api: "/export/report/importStationTag",
        url: "/charging-maintenance-ui/static/批量增标签模板.xlsx",
        extraData: {
          remark: "",
        },
      },
      upload: {
        // 是否显示弹出层
        importOpen: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: baseUrl + "/excel/stationGradeImport",
        updateAsCode: "",
      },
      batchLevelVisible: false,
      rules: {
        gradeId: [
          { required: true, message: "请选择站点运维等级", trigger: "change" },
        ],
      },
      levelForm: {
        gradeId: undefined,
        remark: undefined,
      },
      batchLevelForm: {
        remark: "",
        file: [],
      },
      tagEditVisible: false,
      // config: [],
      stationId: undefined,
      statusDict: [],
      stationChargeTypeDict: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "stationName",
          title: "站点名称",
          slots: { default: "jump" },
          // showOverflowTooltip: true,
        },
        {
          field: "belongPlace",
          title: "省市区",
        },
        {
          field: "stationAddress",
          title: "地址",
          customWidth: 200,
        },
        {
          field: "businessMode",
          title: "业务模式",
        },
        {
          field: "stationGradeName",
          title: "站点运维等级",
        },
        {
          field: "operationMode",
          title: "运营模式",
          formatter: ({ cellValue }) => {
            return (
              this.operationModeOptions?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "status",
          title: "运营状态",
          formatter: ({ cellValue }) => {
            return (
              this.statusDict?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "deptName",
          title: "能投大区",
        },
        {
          field: "buildDate",
          title: "建设完成日期",
        },
        {
          field: "onlineDate",
          title: "上线日期",
        },
        {
          field: "stationChargeType",
          title: "站点充电分类",
          formatter: ({ cellValue }) => {
            return (
              this.stationChargeTypeDict?.find((x) => x.dictValue == cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        // {
        //   field: "pendingErrorCount",
        //   title: "待处理故障数",
        //   slots: { default: "jump" },
        // },
        // {
        //   field: "totalErrorCount",
        //   title: "累计故障总数",
        //   slots: { default: "jump" },
        // },
        {
          field: "pendingOrderCount",
          title: "待处理工单数",
          slots: { default: "jump" },
        },
        {
          field: "totalOrderCount",
          title: "工单总数",
          slots: { default: "jump" },
        },
        // {
        //   field: "chargeOrderCount",
        //   title: "充电订单数",
        //   slots: { default: "jump" },
        // },
        {
          field: "equipCount",
          title: "设备数",
          slots: { default: "jump" },
        },
        {
          field: "pileACCount",
          title: "交流桩数",
          slots: { default: "jump" },
        },
        {
          field: "pileDCCount",
          title: "直流桩数",
          slots: { default: "jump" },
        },
        {
          field: "gunACCount",
          title: "交流枪数",
          slots: { default: "jump" },
        },
        {
          field: "gunDCCount",
          title: "直流枪数",
          slots: { default: "jump" },
        },
        {
          field: "stationCode",
          title: "站点编码",
          treeNode: true,
        },
        {
          field: "projectCode",
          title: "项目编码",
          slots: { default: "projectJump" },
          showOverflow: false,
          customWidth: 220,
          // slots: { default: "projectCode" },
        },
        {
          field: "projectName",
          title: "项目名称",
          slots: { default: "projectJump" },
          showOverflow: false,
          customWidth: 220,
        },
        {
          field: "stationTag",
          title: "站点标签",
          slots: { default: "stationTag" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          field: "chargeUserName",
          title: "责任人",
        },
        {
          field: "serviceUserName",
          title: "服务人",
        },

        // {
        //   field: "stationType",
        //   title: "站点类型",
        //   formatter: this.stationTypeFormat,
        // },
        // {
        //   field: "pileNum",
        //   title: "充电桩数量",
        // },
        // {
        //   field: "belongPlace",
        //   title: "归属地",
        //   showOverflowTooltip: true,
        // },

        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "stationList",
      detailVisible: false,
      rowInfo: {},
      stationTypeDict: [],
      stationTagDict: [],
      addProjectDialogVisible: false,
      pageType: "detail",
      projectId: undefined,
      dialogTitle: "项目详情",
      tagManageVisible: false,
      handRow: {
        stationIds: [],
      },
      token: "",
      recordList: [],
      logVisible: false,
      levelVisible: false,
      levelOptions: [],
      deptOptionList: [],
      operationModeOptions: [],
      submitLoading: false,
      btnArr: [
        {
          title: "站点运维等级",
          command: "level",
          func: (row) => {
            this.handleRowLevel(row);
          },
          permission: "station:level:update",
        },
        {
          title: "增标签",
          command: "addTag",
          func: (row) => {
            this.editTag(row, true);
          },
          permission: "station:tag:add",
        },
        {
          title: "减标签",
          command: "minusTag",
          func: (row) => {
            this.editTag(row, false);
          },
          permission: "station:tag:minus",
        },
        {
          title: "操作日志",
          command: "log",
          func: (row) => {
            this.handleLog(row);
          },
          permission: "station:log:view",
        },
      ],
    };
  },
  async created() {
    if (Object.keys(this.$route.params).length > 0) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    Promise.all([
      this.getDicts("cm_station_type").then((response) => {
        this.stationTypeDict = response?.data;
      }),
      this.getStationTag(),
      this.getDicts("cm_operation_mode").then((response) => {
        this.operationModeOptions = response?.data;
      }),
      this.getDicts("cm_station_status").then((response) => {
        this.statusDict = response?.data;
      }),
      this.getDicts("cm_station_charge_type").then((response) => {
        this.stationChargeTypeDict = response?.data;
      }),
      this.getDeptList(),
      queryLevelList({}).then((res) => {
        this.levelOptions = res.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          // this.initConfig();
          this.getList();
        });
      }, 500);
    });
  },
  mounted() {
    this.token = getToken();
  },
  activated() {
    if (Object.keys(this.$route.params).length > 0) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    queryLevelList({}).then((res) => {
      this.levelOptions = res.data;
    });
    this.getList();
  },
  methods: {
    async getStationTag() {
      //业务类型字典
      const res = await getTagList({
        pageSize: 9999,
        pageNum: 1,
        tagAttributeValue: "1",
      });
      this.stationTagDict = res?.data?.map((x) => {
        return { ...x, dictValue: x.tagName, dictLabel: x.tagName };
      });
    },
    downTagExcel(isAdd = 1) {
      window.location.href = isAdd
        ? "/charging-maintenance-ui/static/批量增标签模板.xlsx"
        : "/charging-maintenance-ui/static/批量减标签模板.xlsx";
    },
    handleBatchTag() {
      this.uploadObj = {
        // api: isAdd ? "/export/report/importStationTag" : "",
        api: "/export/report/importStationTag",
        extraData: {
          remark: "",
        },
      };
      this.$refs.batchUploadTag.open();
    },
    handleBatchStation() {
      this.stationUploadObj = {
        api: "/export/report/importStation",
        url: "/charging-maintenance-ui/static/批量导入站点模板.xlsx",
        extraData: {
          remark: "",
        },
      };
      this.$refs.batchUploadStation.open();
    },
    getArr(item) {
      return item == "" ? [] : item?.split(";");
    },
    checkPermission,
    handleBeforeUpload(file) {
      const isLt2G = file.size / 1024 / 1024 / 1024 < 2;
      if (!isLt2G) {
        this.$message.error("上传的文件大小不能超过2G!");
      }
      return isLt2G;
    },
    handleChangeFile(file, fileList) {
      console.log(file, fileList);
      this.batchLevelForm.file = fileList || [];
    },
    handleFileSuccess(response) {
      this.submitLoading = false;
      console.log("response===", response);
      if (!response.success) {
        this.$confirm(response.message, "站点运维等级配置失败！", {
          confirmButtonText: "重新上传",
          cancelButtonText: "取消",
          type: "error",
          center: true,
          dangerouslyUseHTMLString: true,
        })
          .then(() => {
            this.batchLevelForm.file = [];
            this.$refs.upload.clearFiles();
          })
          .catch(() => {
            this.handleBatchCancel();
          });
      } else {
        this.handleBatchCancel();
        this.$alert("站点运维等级配置成功", "配置结果", {
          type: "success",
          confirmButtonText: "我知道了",
          callback: () => {
            this.getList();
          },
        });
      }
    },
    handleFileError(response) {
      this.submitLoading = false;
      this.$confirm(response.message, "站点运维等级配置失败！", {
        confirmButtonText: "重新上传",
        cancelButtonText: "取消",
        type: "error",
        center: true,
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          this.batchLevelForm.file = [];
          this.$refs.upload.clearFiles();
        })
        .catch(() => {
          this.handleBatchCancel();
        });
    },
    downExcel() {
      window.location.href =
        "/charging-maintenance-ui/static/批量配置站点运维等级.xlsx";
    },
    //批量配置-提交
    handleBatchSubmit() {
      console.log(this.batchLevelForm.file, "提交");
      if (this.batchLevelForm.file?.length === 0) {
        this.$message.error("请上传文件！");
        return;
      }
      if (this.batchLevelForm.file[0].size / 1024 / 1024 / 1024 > 2) {
        this.$message.error("上传的文件大小不能超过2G!");
        return;
      }
      this.submitLoading = true;
      this.$refs.upload.submit();
    },
    handleBatchLevel() {
      this.batchLevelVisible = true;
    },
    handleCommand(command, row) {
      this.btnArr?.find((x) => x.command == command)?.func(row);
      console.log(command, row, "command");
    },
    //能投大区下拉选项
    async getDeptList() {
      getAllDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    handleJump(row, property) {
      const arr = [
        {
          title: "站点名称",
          field: "stationName",
          method: () => {
            this.$router.push({
              path: "/station/newDetailPage",
              query: {
                stationId: row.stationId,
                projectId: row.projectId,
                stationCode: row.stationCode,
                stationName: row.stationName,
              },
            });
          },
        },
        {
          field: "pendingErrorCount",
          title: "待处理故障数",
          method: () => {
            this.$router.push({
              name: "errorPush",
              params: {
                stationNo: row.stationCode,
                handleResult: "未处理",
              },
            });
          },
        },
        {
          field: "totalErrorCount",
          title: "累计故障总数",
          method: () => {
            this.$router.push({
              name: "errorPush",
              params: {
                stationNo: row.stationCode,
              },
            });
          },
        },
        {
          field: "pendingOrderCount",
          title: "待处理工单数",
          method: () => {
            this.$router.push({
              name: "operationWorkOrder",
              params: {
                stationName: row.stationName,
                orderStatus: "1",
              },
            });
          },
        },
        {
          field: "totalOrderCount",
          title: "工单总数",
          method: () => {
            this.$router.push({
              name: "operationWorkOrder",
              params: {
                stationName: row.stationName,
              },
            });
          },
        },
        {
          field: "chargeOrderCount",
          title: "充电订单数",
          method: () => {
            this.$router.push({
              name: "chargingOrder",
              params: {
                stationName: row.stationName,
              },
            });
          },
        },
        {
          field: "equipCount",
          title: "设备数",
          method: () => {
            this.$router.push({
              name: "chargingStation",
              params: {
                stationNo: row.stationCode,
              },
            });
          },
        },
        {
          field: "pileACCount",
          title: "交流桩数",
          method: () => {
            this.$router.push({
              name: "chargingStation",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "01",
              },
            });
          },
        },
        {
          field: "pileDCCount",
          title: "直流桩数",
          method: () => {
            this.$router.push({
              name: "chargingStation",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "02",
              },
            });
          },
        },
        {
          field: "gunACCount",
          title: "交流枪数",
          method: () => {
            this.$router.push({
              name: "chargingGun",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "01",
              },
            });
          },
        },
        {
          field: "gunDCCount",
          title: "直流枪数",
          method: () => {
            this.$router.push({
              name: "chargingGun",
              params: {
                stationNo: row.stationCode,
                subTypeCode: "02",
              },
            });
          },
        },
        {
          field: "projectCode",
          title: "项目编码",
          method: () => {
            this.$router.push({
              name: "projectManage",
              params: {
                projectCode: row,
              },
            });
          },
        },
        {
          field: "projectName",
          title: "项目名称",
          method: () => {
            this.$router.push({
              name: "projectManage",
              params: {
                projectName: row,
              },
            });
          },
        },
      ];
      arr.find((item) => {
        if (item.field == property) {
          item.method();
        }
      });
    },
    handleSubmit() {
      this.$refs.levelForm.validate((valid) => {
        if (!valid) return;
        const params = { ...this.levelForm, stationId: this.stationId };
        submitEditRowLevel(params).then((res) => {
          if (res?.code == "10000") {
            this.$message.success("保存成功");
            this.handleCancel();
            this.getList();

            this.reportTrackEvent(DATA_INFO_CLICK_STATION_LEVEL_CONFIG);
          }
        });
      });
    },
    handleCancel() {
      this.levelVisible = false;
      this.$refs.levelForm.resetFields();
    },
    handleBatchCancel() {
      this.batchLevelVisible = false;
      this.$refs.batchLevelForm.resetFields();
      this.batchLevelForm.file = [];
      this.$refs.upload.clearFiles();
    },
    handleRowLevel(row) {
      this.levelVisible = true;
      this.stationId = row.stationId;
      this.levelForm.gradeId = row.stationGrade;
    },
    handleLog(row) {
      this.logVisible = true;
      queryLogList({ stationId: row.stationId }).then((res) => {
        this.recordList = res.data;
      });
    },
    closeDialog() {
      this.logVisible = false;
    },
    handleExport() {
      let text =
        this.handRow.stationIds.length == 0
          ? "是否确认导出所有数据?"
          : "是否确认导出所选数据?";
      const params = {
        ...this.searchForm,
        stationIds: this.handRow.stationIds,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      if (Array.isArray(params.buildDate)) {
        params.startBuildDate = params.buildDate[0];
        params.endBuildDate = params.buildDate[1];
        delete params.buildDate;
      }
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");

          this.reportTrackEvent(DATA_INFO_CLICK_STATION_REPORT);

          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    openFileDownLoadPage() {
      const url = "/file-center/index?t=" + this.token;
      const ctxPath = process.env.VUE_APP_BASE_API;
      window.open(ctxPath + url);
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
        stationCodes: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
        this.handRow.stationCodes = tableData.map((v) => v.stationCode);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    editTag(row, isPlus) {
      this.$refs.tagEdit.open(row, isPlus);
    },
    //打开弹窗
    handleCheckModal(code) {
      this.$refs.dataCheck.open(code);
    },
    //跳转至纪录页
    handleCheckRecord() {
      this.$router.push({
        path: "/station/record",
        query: {
          // stationId: this.stationId,
        },
      });
    },
    //跳转至站点运维等级
    handleLevel() {
      this.$router.push({
        path: "/station/level",
        query: {
          // stationId: this.stationId,
        },
      });
    },
    //项目列表
    handleProjectList(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.addProjectDialogVisible = true;
    },
    showDetail(row) {
      this.$router.push({
        path: "/station/newDetailPage",
        query: {
          stationId: row.stationId,
          projectId: row.projectId,
          stationCode: row.stationCode,
          stationName: row.stationName,
        },
      });
      // this.rowInfo = row;
      // this.detailVisible = true;
    },
    tagManage(row) {
      this.stationId = row.stationId;
      this.tagManageVisible = true;
      this.$refs.tagManage.open(row);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      if (Array.isArray(params.buildDate)) {
        params.startBuildDate = params.buildDate[0];
        params.endBuildDate = params.buildDate[1];
        delete params.buildDate;
      }
      queryStationInfoByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
    //站点类型转换
    stationTypeFormat({ cellValue }) {
      return this.selectDictLabel(this.stationTypeDict, cellValue);
    },
    updateTag() {
      this.tagManageVisible = false;
      this.getList();
    },
    close() {
      this.tagManageVisible = false;
    },
  },
  computed: {
    config() {
      return [
        {
          key: "stationCode",
          title: "站点编码",
          type: "input",
          placeholder: "请填写站点编码",
        },
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请填写站点名称",
        },
        // {
        //   key: "stationType",
        //   title: "站点类型",
        //   type: "select",
        //   placeholder: "请选择站点类型",
        //   options: this.stationTypeDict,
        // },
        {
          key: "orgNo",
          title: "能投大区",
          type: "select",
          placeholder: "请选择能投大区",
          options: this.deptOptionList,
        },
        {
          key: "stationTag",
          title: "站点标签",
          type: "select",
          placeholder: "请选择站点标签",
          options: this.stationTagDict,
        },
        {
          key: "projectCode",
          title: "项目编码",
          type: "input",
          placeholder: "请填写项目编码",
        },
        {
          key: "projectName",
          title: "项目名称",
          type: "input",
          placeholder: "请填写项目名称",
        },
        {
          key: "operationMode",
          title: "运营模式",
          type: "select",
          placeholder: "请选择运营模式",
          options: this.operationModeOptions,
        },
        {
          key: "stationGrade",
          title: "站点等级",
          type: "select",
          placeholder: "请选择站点等级",
          options: this.levelOptions,
          optionLabel: "gradeName",
          optionValue: "gradeId",
        },

        {
          key: "stationRelation",
          title: "站点责任人",
          type: "input",
          placeholder: "请填写站点责任人",
        },
        {
          key: "buildDate",
          title: "建设完成时间",
          type: "dateRange",
          placeholder: "请选择建设完成时间",
        },
        {
          key: "status",
          title: "运营状态",
          type: "select",
          placeholder: "请选择运营状态",
          options: this.statusDict,
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "businessMode",
          title: "业务模式",
          type: "input",
          placeholder: "请输入业务模式",
        },
      ];
    },
  },
};
</script>

<style scoped lang="less">
.timeline {
  &-title {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
