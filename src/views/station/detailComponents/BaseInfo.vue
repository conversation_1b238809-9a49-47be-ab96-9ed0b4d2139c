//基本信息tab页
<template>
  <div>
    <!-- 基础信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>基础信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item
          v-for="(item, index) in infoList"
          :key="index"
          :label="item.title"
        >
          <el-button type="text" @click="handleJump()" v-if="item.jump">{{
            item.value
          }}</el-button>
          <el-tooltip :content="item.value" placement="top-start" v-else>
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 基础信息-E -->
    <!-- 标签信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>标签信息</span>
      </div>
      <el-descriptions class="descriptions" :column="2" border>
        <el-descriptions-item v-for="(item, index) in tagList" :key="index">
          <template slot="label">
            {{ item.title }}
          </template>
          <!-- <el-tooltip placement="top-start" effect="light"> -->
          <el-tag style="margin-right: 10px;" :key="x" v-for="x in item.value">
            {{ x }}
          </el-tag>
          <!-- <template slot="content">
              <el-tag
                style="margin-right: 10px;"
                :key="x"
                v-for="x in item.value"
              >
                {{ x }}
              </el-tag>
            </template>
          </el-tooltip> -->
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 标签信息-E -->
    <!-- 单位信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>单位信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item v-for="(item, index) in unitList" :key="index">
          <template slot="label">
            {{ item.title }}
          </template>
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 单位信息-E -->
    <!-- 停车费信息-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>停车费信息</span>
      </div>
      <el-descriptions class="descriptions" :column="4" border>
        <el-descriptions-item v-for="(item, index) in parkList" :key="index">
          <template slot="label"> {{ item.title }}</template>
          <el-tooltip :content="item.value" placement="top-start">
            <span>{{ item.value }}</span>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <!-- 停车费信息-E -->
    <!-- 站点图片-S -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>站点图片</span>
      </div>
      <el-row type="flex" style="flex-wrap:wrap" v-if="picList.length > 0">
        <div v-for="(o, index) in picList" :key="index" class="file-item">
          <el-image
            style="width: 150px;height:150px;margin-right:10px"
            :src="o"
            alt="加载失败"
            class="avatar"
            v-if="
              o.toLowerCase().indexOf('.jpg') > 0 ||
                o.toLowerCase().indexOf('.jpeg') > 0 ||
                o.toLowerCase().indexOf('.png') != -1
            "
            :preview-src-list="
              picList.filter(
                (o) =>
                  o.toLowerCase().indexOf('.jpg') > 0 ||
                  o.toLowerCase().indexOf('.jpeg') > 0 ||
                  o.toLowerCase().indexOf('.png') != -1
              )
            "
          >
          </el-image>
          <video
            style="width: 150px;height:150px;margin-right:10px"
            v-if="o.toLowerCase().indexOf('.mp4') > 0"
            :src="o"
            controls
          ></video></div
      ></el-row>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 站点图片-E -->
    <!-- <PicPreview ref="picPreview"></PicPreview> -->
  </div>
</template>

<script>
import { queryStationBaseInfo } from "@/api/station/detail.js";
// import PicPreview from "@/components/Upload/picPreview.vue";
export default {
  components: {
    // PicPreview,
  },
  props: {
    stationId: {
      type: String,
      default: "",
    },
    stationCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      infoList: [],
      unitList: [],
      parkList: [],
      tagList: [],
      picList: [],
    };
  },
  created() {
    this.getInfo();
  },
  methods: {
    //跳转至充电桩页面
    handleJump() {
      this.$router.push({
        name: "chargingStation",
        params: {
          stationNo: this.stationCode,
        },
      });
    },
    clickImg(index) {
      this.$refs.picPreview.open(index, this.picList);
    },
    getInfo() {
      queryStationBaseInfo({ stationId: this.stationId }).then((res) => {
        // const {infoObj,feeList,chargingList}=res.data
        this.picList = res.data?.stationUrls || [];
        this.tagList = [
          { title: "充电平台站点标签", value: res.data?.platformStationTags },
          { title: "维保通站点标签", value: res.data?.maintenanceStationTags },
          { title: "服务标签", value: res.data?.serviceTag },
          { title: "区域标签", value: res.data?.regionTag },
        ];
        this.infoList = [
          { title: "站点编码", value: res.data?.stationCode },
          { title: "站点名称", value: res.data?.stationName },
          { title: "站点类型", value: res.data?.stationType },
          { title: "站点资产编号", value: res.data?.assetNumber },
          { title: "建设完成时间", value: res.data?.buildDate },
          { title: "上线时间", value: res.data?.onlineDate },
          {
            title: "运营模式",
            value:
              res.data?.operationMode == "1"
                ? "自营"
                : res.data?.operationMode == "2"
                ? "代运营"
                : "",
          },
          { title: "运营状态", value: res.data?.statusName },
          { title: "站点运维等级", value: res.data?.stationGradeName },
          {
            title: "是否开放",
            value:
              res.data?.openFlag == "1"
                ? "是"
                : res.data?.openFlag == "0"
                ? "否"
                : "",
          },
          { title: "站点简介", value: res.data?.stationIntroduce },
          {
            title: "实体类型",
            value:
              res.data?.stationObjectType == "01"
                ? "实体"
                : res.data?.stationObjectType == "02"
                ? "虚拟"
                : "",
          },
          { title: "建设场所", value: res.data?.constructionName },
          { title: "服务电话", value: res.data?.serviceTel },
          { title: "位置引导", value: res.data?.siteGuide },
          { title: "支持车型", value: res.data?.carType },
          { title: "开票方式", value: res.data?.invoicingMethod },
          { title: "开票方", value: res.data?.invoicingPartyName },
          { title: "站点充电分类", value: res.data?.stationChargeType },
          { title: "充电桩数量", value: res.data?.pileCount, jump: true },
          { title: "站点地址", value: res.data?.stationAddressInfo },
        ];
        this.unitList = [
          { title: "资产单位", value: res.data?.assetName },
          { title: "运维单位", value: res.data?.maintenanceName },
          {
            title: "监管单位",
            value: res.data?.superviseName,
          },
          { title: "核算单位", value: res.data?.accountingName },
          { title: "运营单位", value: res.data?.operationName },
          { title: "", value: "" },
          { title: "", value: "" },
          { title: "", value: "" },
        ];
        this.parkList = [
          { title: "停车收费类型", value: res.data?.parkingChargeTypeName },
          { title: "停车位置", value: res.data?.parkingLocationName },
          {
            title: "停车费说明",
            value: res.data?.parkFeeDescription,
          },
        ];
      });
    },
  },
};
</script>

<style></style>
