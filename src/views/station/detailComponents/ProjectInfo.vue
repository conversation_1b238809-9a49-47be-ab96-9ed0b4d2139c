//项目信息tab页
<template>
  <div>
    <el-tabs
      v-model="projectId"
      @tab-click="handleClick"
      v-if="tabList && tabList.length > 0"
      type="card"
    >
      <el-tab-pane
        :label="x.projectName"
        :name="x.projectId"
        v-for="(x, i) in tabList"
        :key="i"
      >
        <!-- 所属项目-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>项目信息</span>
            <el-button
              type="text"
              @click="handleJump()"
              style="margin-left: 10px;"
              >详情<i class="el-icon-arrow-right el-icon--right"></i
            ></el-button>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in projectList"
              :key="index"
              :label="item.title"
            >
              <el-button type="text" @click="item.method" v-if="item.jump">{{
                item.value
              }}</el-button>
              <el-tooltip :content="item.value" placement="top-start" v-else>
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 所属项目-E -->
        <!-- 人员信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>人员信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in userList"
              :key="index"
            >
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 人员信息-E -->
        <!-- 项目工单-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>项目工单</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in orderList"
              :key="index"
            >
              <template slot="label">
                {{ item.title }}
              </template>
              <el-button type="text" @click="item.method" v-if="item.jump">{{
                item.value
              }}</el-button>
              <el-tooltip :content="item.value" placement="top-start" v-else>
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 项目工单-E -->
      </el-tab-pane>
    </el-tabs>
    <el-empty v-else></el-empty>
  </div>
</template>

<script>
import {
  queryProjectBaseInfo,
  queryProjectTabList,
} from "@/api/station/detail.js";
export default {
  components: {},
  props: {
    stationId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      projectId: "",
      tabList: [],
      projectList: [],
      userList: [],
      orderList: [],
      tagList: [],
      picList: [],
      projectCode: undefined,
    };
  },
  created() {
    // this.getTabList();
  },
  methods: {
    handleClick(val) {
      this.projectId = val.name;
      this.getProjectInfo();
    },
    getInfo() {
      queryProjectTabList({ stationId: this.stationId }).then((res) => {
        if (res?.code == "10000") {
          this.tabList = res.data;
          if (res.data?.length > 0) {
            this.projectId = res.data[0]?.projectId;
            this.getProjectInfo();
          }
        }
      });
    },
    //跳转至项目列表页面
    handleJump() {
      this.$router.push({
        name: "projectManage",
        params: {
          projectCode: this.projectCode,
        },
      });
    },
    clickImg(index) {
      this.$refs.picPreview.open(index, this.picList);
    },
    getProjectInfo() {
      queryProjectBaseInfo({ projectId: this.projectId }).then((res) => {
        // const {infoObj,feeList,chargingList}=res.data
        this.projectCode = res.data?.projectCode;
        this.projectList = [
          { title: "项目编码", value: res.data?.projectCode },
          { title: "项目名称", value: res.data?.projectName },
          { title: "项目状态", value: res.data?.projectStatus },
          { title: "所属大区", value: res.data?.projectOrgNo },
          { title: "项目完成耗时（分钟）", value: res.data?.totalCost },
          { title: "施工耗时（分钟）", value: res.data?.constructCost },
          { title: "路测耗时（分钟）", value: res.data?.roadTestCost },
          { title: "上线耗时（分钟）", value: res.data?.onLineCost },
          { title: "验收耗时（分钟）", value: res.data?.acceptanceCost },
          { title: "项目创建时间", value: res.data?.createTime },
          { title: "项目完成时间", value: res.data?.acceptanceEndTime },
          {
            title: "项目工单数量（个）",
            value: res.data?.orderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                },
              });
            },
          },
          {
            title: "是否包干",
            value:
              res.data?.contractFlag == "1"
                ? "是"
                : res.data?.contractFlag == "0"
                ? "否"
                : res.data?.contractFlag,
          },
          { title: "居间商", value: res.data?.middleBusiness },
          { title: "转固时间", value: res.data?.fixedTime },
          { title: "项目备注", value: res.data?.projectRemark },
        ];
        this.userList = [
          { title: "工程经理", value: res.data?.buildManagerName },
          { title: "工程联系方式", value: res.data?.buildPhone },
          { title: "施工队", value: res.data?.buildTeamName },
          { title: "施工方联系电话", value: res.data?.constructionPhone },
          { title: "施工人员", value: res.data?.builderName },
          { title: "商务BD", value: res.data?.businessManageName },
          { title: "场地负责人", value: res.data?.siteManagerName },
          { title: "场地负责人联系方式", value: res.data?.siteManagerPhone },
        ];
        this.orderList = [
          {
            title: "工单数量总计（个）",
            value: res.data?.orderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                },
              });
            },
          },
          {
            title: "施工工单（个）",
            value: res.data?.constructionOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  relaBizTypes: ["construction"],
                },
              });
            },
          },
          {
            title: "路测工单（个）",
            value: res.data?.roadTestOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  relaBizTypes: ["otherRoadTest", "pileRoadTest"],
                },
              });
            },
          },
          {
            title: "上线工单（个）",
            value: res.data?.onlineOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  relaBizTypes: ["online"],
                },
              });
            },
          },
          {
            title: "验收工单（个）",
            value: res.data?.acceptanceOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  relaBizTypes: ["acceptance"],
                },
              });
            },
          },
          {
            title: "进行中工单（个）",
            value: res.data?.processingOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  flowStatus: "1",
                },
              });
            },
          },
          {
            title: "已完成工单（个）",
            value: res.data?.completedOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  flowStatus: "2",
                },
              });
            },
          },
          {
            title: "已作废工单（个）",
            value: res.data?.cancelOrderCount,
            jump: true,
            method: () => {
              this.$router.push({
                name: "workOrderWorkbench",
                params: {
                  projectId: this.projectId,
                  activeName: "3",
                  flowStatus: "3",
                },
              });
            },
          },
        ];
      });
    },
  },
};
</script>

<style></style>
