//订单信息tab页
<template>
  <div>
    <!-- 订单数据统计 -->
    <el-card style="margin-bottom: 10px;">
      <div
        slot="header"
        class="card-title-wrap"
        style="justify-content: space-between"
      >
        <div style="display: flex;align-items: center;">
          <div class="card-title-line"></div>
          <span>订单数据统计</span>
          <el-button
            type="text"
            @click="handleJump()"
            style="margin-left: 10px;"
            >详情<i class="el-icon-arrow-right el-icon--right"></i
          ></el-button>
        </div>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="float: right"
          value-format="yyyy-MM-dd"
          @change="getInfo"
          :clearable="false"
          :picker-options="pickerOptions"
          @blur="resetDisableDate"
        >
        </el-date-picker>
      </div>
      <div class="statistics-box" v-loading="loading">
        <div v-for="(item, index) in statisticsList" :key="index">
          <el-statistic group-separator="," :precision="0" :value="item.value">
            <template slot="suffix">
              <span style="font-size:12px">{{ item.unit }}</span>
            </template>
            <template slot="title">
              {{ item.title }}
              <el-tooltip effect="dark" :content="item.tooltip" placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template>
          </el-statistic>
        </div>
      </div>
    </el-card>
    <!-- 充电趋势 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>充电趋势</span>
      </div>
      <LineChart
        :axisData="chargeTendObj.time"
        :serieData="chargeTendObj.tendencyArr"
        lineType="line"
        chartStyle="height:320px"
        v-if="chargeTendObj.time && chargeTendObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 日利用率 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>日利用率</span>
      </div>
      <LineChart
        :axisData="rateTendObj.time"
        :serieData="rateTendObj.tendencyArr"
        lineType="line"
        chartStyle="height:320px"
        unit="单位：%"
        v-if="rateTendObj.time && rateTendObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
    <!-- 日充电收入 -->
    <el-card style="margin-bottom: 10px;">
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>日充电收入</span>
      </div>
      <LineChart
        :axisData="incomeTendObj.time"
        :serieData="incomeTendObj.tendencyArr"
        lineType="line"
        chartStyle="height:320px"
        unit="单位：元"
        v-if="incomeTendObj.time && incomeTendObj.time.length > 0"
      ></LineChart>
      <el-empty v-else></el-empty>
    </el-card>
  </div>
</template>

<script>
import moment from "moment";
import LineChart from "@/components/Echarts/LineChart.vue";
import { queryOrderBaseInfo } from "@/api/station/detail.js";
export default {
  components: {
    LineChart,
  },
  props: {
    stationCode: {
      type: String,
      default: "",
    },
    stationName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dateRange: ["", ""],
      statisticsList: [],
      loading: false,
      chargeTendObj: {
        time: [],
        tendencyArr: [],
      },
      rateTendObj: {
        time: [],
        tendencyArr: [],
      },
      incomeTendObj: {
        time: [],
        tendencyArr: [],
      },
      disabledCurrent: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          console.log("onPick", maxDate, minDate);
          if (!maxDate) {
            this.disabledCurrent = minDate;
          }
        },
        disabledDate: (current) => {
          if (!this.disabledCurrent) return false;
          return (
            (current &&
              current <
                moment(this.disabledCurrent)
                  .subtract(1, "Y")
                  .startOf("day")) ||
            current >
              moment(this.disabledCurrent)
                .add(1, "Y")
                .endOf("day")
          );
        },
      },
    };
  },
  created() {
    this.dateRange = [
      moment()
        .subtract(6, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
  },
  methods: {
    // 每次失焦重置disableDate
    resetDisableDate() {
      this.disabledCurrent = null;
    },
    //跳转至订单页面
    handleJump() {
      this.$router.push({
        name: "chargingOrder",
        params: {
          stationName: this.stationName,
        },
      });
    },
    getInfo() {
      queryOrderBaseInfo({
        startTime: this.dateRange[0] + " 00:00:00",
        endTime: this.dateRange[1] + " 23:59:59",
        stationNo: this.stationCode,
        // stationNo: "20230501002",
      }).then((res) => {
        const {
          chargeAmtVOMap = {},
          chargeOperationRateVOMap = {},
          chargeTrendVOMap = {},
          oOrderInfoStatisticsByStationVO = {},
        } = res?.data;
        this.statisticsList = [
          {
            title: "累计充电订单",
            value: Number(oOrderInfoStatisticsByStationVO?.orderTotalNum) || 0,
            unit: "个",
            tooltip:
              "指的是时间筛选范围内的充电订单总数，包含所有状态的订单。单位为【个】",
          },
          {
            title: "累计充电电量",
            value: oOrderInfoStatisticsByStationVO?.totalPq || 0,
            unit: "kw.h",
            tooltip:
              "指的是时间筛选范围内的充电订单的充电量总和，小数点后保留2位，四舍五入。单位【k.Wh】",
          },
          {
            title: "累计充电金额",
            value: oOrderInfoStatisticsByStationVO?.totalChargeAmt || 0,
            unit: "元",
            tooltip:
              "指的是时间筛选范围内的充电订单的订单金额总和，只统计订单状态为【结算中、待启动、执行中、交易完成、待评价】的订单金额，单位为【元】;小数点后保留2位，四舍五入。单位【元】",
          },
          {
            title: "累计充电电费",
            value: oOrderInfoStatisticsByStationVO?.totalElecAmt || 0,
            unit: "元",
            tooltip:
              "指的是时间筛选范围内的充电订单的电费金额总和，只统计订单状态为【结算中、待启动、执行中、交易完成、待评价】的电费金额，单位为【元】;小数点后保留2位，四舍五入。单位【元】",
          },
          {
            title: "累计充电服务费",
            value: oOrderInfoStatisticsByStationVO?.totalServiceAmt || 0,
            unit: "元",
            tooltip:
              "指的是时间筛选范围内的充电订单的服务费金额总和，只统计订单状态为【结算中、待启动、执行中、交易完成、待评价】的服务费金额，单位为【元】;小数点后保留2位，四舍五入。单位【元】",
          },
          {
            title: "日均充电量",
            value: oOrderInfoStatisticsByStationVO?.dailyChargePq || 0,
            unit: "kw.h",
            tooltip: "时间筛选范围内的充电电量日平均值",
          },
          {
            title: "日均充电时长",
            value: oOrderInfoStatisticsByStationVO?.dailyChargeTimes || 0,
            unit: "分钟",
            tooltip: "时间筛选范围内的充电时长日平均值",
          },
          {
            title: "累计充电时长",
            value: oOrderInfoStatisticsByStationVO?.totalChargeTime || 0,
            unit: "h",
            tooltip: "指的是时间筛选范围内的充电订单的充电时长总和",
          },
          {
            title: "累计充电次数",
            value: oOrderInfoStatisticsByStationVO?.totalChargeNum || 0,
            unit: "次",
            tooltip:
              "指的是时间筛选范围内的充电订单总和，只统计订单状态为【结算中、待启动、执行中、交易完成、待评价】的订单数量，单位为【个】",
          },
          {
            title: "利用率",
            value:
              Number(oOrderInfoStatisticsByStationVO?.annualUtilization) || 0,
            unit: "%",
            tooltip:
              "【时间筛选范围内的累计充电时长/（时间筛选范围内的总时长*充电枪总数）】*100%，小数点后保留2位，四舍五入。",
          },
          {
            title: "异常订单",
            value: oOrderInfoStatisticsByStationVO?.errorOrderNum || 0,
            unit: "个",
            tooltip: "该站点的异常订单总数",
          },
          {
            title: "日均充电次数",
            value: oOrderInfoStatisticsByStationVO?.dailyChargeNum || 0,
            unit: "次",
            tooltip: "时间筛选范围内的充电次数日平均值",
          },
          {
            title: "日均充电收入",
            value: oOrderInfoStatisticsByStationVO?.dailyChargeAmt || 0,
            unit: "元",
            tooltip: "时间筛选范围内的充电金额日平均值",
          },
        ];
        this.chargeTendObj = {
          time: Object.keys(chargeTrendVOMap),
          tendencyArr: [
            {
              name: "日充电量(k.Wh)",
              data: Object.keys(chargeTrendVOMap)?.map((x) => {
                return chargeTrendVOMap[x].dailyChargePq;
              }),
              type: "bar",
            },
            {
              name: "日充电次数(次)",
              data: Object.keys(chargeTrendVOMap)?.map((x) => {
                return chargeTrendVOMap[x].dailyChargeNum;
              }),
              type: "bar",
            },
            {
              name: "日订单量(个)",
              data: Object.keys(chargeTrendVOMap)?.map((x) => {
                return chargeTrendVOMap[x].dailyOrderNum;
              }),
              type: "bar",
            },
            {
              name: "日充电时长(分钟)",
              data: Object.keys(chargeTrendVOMap)?.map((x) => {
                return chargeTrendVOMap[x].dailyChargeTime;
              }),
            },
          ],
        };
        this.rateTendObj = {
          time: Object.keys(chargeOperationRateVOMap),
          tendencyArr: [
            {
              name: "每日利用率",
              data: Object.keys(chargeOperationRateVOMap)?.map((x) => {
                return chargeOperationRateVOMap[x].dailyOperationRate;
              }),
            },
          ],
        };
        this.incomeTendObj = {
          time: Object.keys(chargeAmtVOMap),
          tendencyArr: [
            {
              name: "服务费",
              data: Object.keys(chargeAmtVOMap)?.map((x) => {
                return chargeAmtVOMap[x].dailyServiceAmt;
              }),
              type: "bar",
            },
            {
              name: "电费",
              data: Object.keys(chargeAmtVOMap)?.map((x) => {
                return chargeAmtVOMap[x].dailyElecAmt;
              }),
            },
          ],
        };
      });
    },
  },
};
</script>

<style scoped lang="less">
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic {
  margin: 20px 0;
  .head {
    margin-bottom: 12px;
  }
}
</style>
