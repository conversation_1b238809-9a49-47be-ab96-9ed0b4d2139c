//站点详情页
<template>
  <div class="app-container">
    <div style="display: flex;align-items: center">
      <h4>站点详情</h4>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="base">
        <BaseInfo
          ref="baseInfo"
          :stationId="stationId"
          :stationCode="stationCode"
        ></BaseInfo>
      </el-tab-pane>
      <el-tab-pane label="项目信息" name="project">
        <ProjectInfo ref="projectInfo" :stationId="stationId"></ProjectInfo>
      </el-tab-pane>
      <el-tab-pane label="充电桩信息" name="station">
        <StationInfo ref="stationInfo" :stationCode="stationCode"></StationInfo>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="order">
        <OrderInfo
          ref="orderInfo"
          :stationCode="stationCode"
          :stationName="stationName"
        ></OrderInfo>
      </el-tab-pane>
      <el-tab-pane label="异常记录" name="record">
        <FaultRecord ref="faultRecord" :stationCode="stationCode"></FaultRecord>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { queryPileInfo, queryPileRecord } from "@/api/chargingStation/index.js";
import GridTable from "@/components/GridTable/index.vue";
import BaseInfo from "./detailComponents/BaseInfo";
import ProjectInfo from "./detailComponents/ProjectInfo";
import StationInfo from "./detailComponents/StationInfo";
import OrderInfo from "./detailComponents/OrderInfo";
import FaultRecord from "./detailComponents/FaultRecord";
export default {
  components: {
    GridTable,
    BaseInfo,
    ProjectInfo,
    StationInfo,
    OrderInfo,
    FaultRecord,
  },
  data() {
    return {
      activeName: "base",
      stationId: undefined,
      projectId: undefined,
      stationCode: undefined,
      stationName: undefined,
    };
  },
  created() {
    this.stationId = this.$route.query.stationId;
    this.projectId = this.$route.query.projectId;
    this.stationCode = this.$route.query.stationCode;
    this.stationName = this.$route.query.stationName;
  },
  methods: {
    handleClick(val) {
      const arr = [
        { name: "base", title: "订单信息", ref: "baseInfo" },
        { name: "project", title: "项目信息", ref: "projectInfo" },
        { name: "station", title: "充电桩信息", ref: "stationInfo" },
        { name: "order", title: "订单信息", ref: "orderInfo" },
        { name: "record", title: "异常记录", ref: "faultRecord" },
      ];
      const obj = arr.find((item) => item.name === val.name);
      this.$refs[obj.ref].getInfo();
    },
  },
};
</script>

<style lang="less" scoped>
.descriptions {
  margin: 0 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
  }
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic .head {
  margin-bottom: 12px;
}
</style>
