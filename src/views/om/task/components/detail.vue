<!-- 项目管理 -->
<template>
  <div class="app-container">
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>巡检信息</span>
      </div>
      <el-form>
        <el-form-item label="巡检状态：">
          <span>{{
            selectDictLabel(this.omStatusOptions, form.omStatus)
          }}</span>
        </el-form-item>
        <el-form-item label="巡检单号：">
          <span>{{ form.omOrderNo }}</span>
        </el-form-item>
        <el-form-item label="巡检站点：">
          <span>{{ form.stationName }}</span>
        </el-form-item>
        <el-form-item label="巡检地址：">
          <span>{{ form.stationAddress }}</span>
        </el-form-item>
        <el-form-item label="巡检人：">
          <span>{{ form.omChargeUserName }}</span>
        </el-form-item>
        <el-form-item label="巡检人手机号：">
          <span>{{ form.omChargeUserPhone }}</span>
        </el-form-item>
        <el-form-item label="应巡检时间：">
          <span>{{ form.planOmTime }}</span>
        </el-form-item>
        <el-form-item label="实际巡检时间：">
          <span>{{ form.realOmTime }}</span>
        </el-form-item>
        <el-form-item label="是否超时：">
          <span>{{ form.expireFlag ? "是" : "否" }}</span>
        </el-form-item>
        <el-form-item label="超时时长：">
          <span v-if="form.expireFlag">{{
            form.expireDays === 0
              ? "0"
              : form.expireDays +
                "天" +
                form.expireHours +
                "小时" +
                form.expireMinutes +
                "分钟"
          }}</span>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>巡检明细</span>
      </div>
      <el-form>
        <el-form-item label="巡检结果说明:">
          <span>{{ form.checkResult }}</span>
        </el-form-item>
        <el-form-item label="巡检标签:">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in getTagArr(form.omTagName)"
          >
            {{ item }}
          </el-tag>
        </el-form-item>
        <el-form-item label="电表数据：">
          <span>{{ form.meterData }}</span>
        </el-form-item>
        <el-form-item label="电表照片:">
          <!-- <file-upload type="img" ref="meterUpload" :limit="9" disabled /> -->
          <el-row type="flex" style="flex-wrap:wrap">
            <div
              v-for="(o, index) in meterPicList"
              :key="index"
              class="file-item"
            >
              <el-image
                style="width: 150px;height:150px;margin-right:10px"
                :src="o.storePath"
                alt="加载失败"
                class="avatar"
                v-if="
                  o.storePath.toLowerCase().indexOf('.jpg') > 0 ||
                    o.storePath.toLowerCase().indexOf('.jpeg') > 0 ||
                    o.storePath.toLowerCase().indexOf('.png') != -1
                "
                @click.stop="clickImg(index, meterPicList)"
              />
              <video
                style="width: 150px;height:150px;margin-right:10px"
                v-if="o.storePath.toLowerCase().indexOf('.mp4') > 0"
                :src="o.storePath"
                controls
                @click.stop="clickImg(index, meterPicList)"
              ></video></div
          ></el-row>
          <div></div>
        </el-form-item>
        <el-form-item label="巡检照片：">
          <!-- <file-upload type="img" ref="taskUpload" :limit="9" disabled /> -->
          <el-row type="flex" style="flex-wrap:wrap">
            <div
              v-for="(o, index) in taskPicList"
              :key="index"
              class="file-item"
            >
              <el-image
                style="width: 150px;height:150px;margin-right:10px"
                :src="o.storePath"
                alt="加载失败"
                class="avatar"
                v-if="
                  o.storePath.toLowerCase().indexOf('.jpg') > 0 ||
                    o.storePath.toLowerCase().indexOf('.jpeg') > 0 ||
                    o.storePath.toLowerCase().indexOf('.png') != -1
                "
                @click.stop="clickImg(index, taskPicList)"
              />
              <video
                style="width: 150px;height:150px;margin-right:10px"
                v-if="o.storePath.toLowerCase().indexOf('.mp4') > 0"
                :src="o.storePath"
                controls
                @click.stop="clickImg(index, taskPicList)"
              ></video></div
          ></el-row>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card>
      <div slot="header" class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>转派记录</span>
      </div>
      <el-timeline :hide-timestamp="true" v-if="recordList.length > 0">
        <el-timeline-item
          placement="top"
          v-for="item in recordList"
          :key="item.orderRecordId"
        >
          <el-card>
            <el-row>
              <el-col :span="5">
                <span>{{ item.operatorTypeName }}</span>
              </el-col>
              <el-col :span="5">
                <span>操作人：{{ item.operatorUserName }}</span>
              </el-col>
              <el-col :span="5">
                <span>操作时间：{{ item.createTime }}</span>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10">
                <span style="display:block;margin-top: 10px">{{
                  (item.remark && item.remark.split("&"))[0]
                }}</span>
              </el-col>
            </el-row>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <el-dialog
        title=""
        width="50%"
        :visible.sync="showImgPreview"
        @close="closePreviewDialog"
        append-to-body
        :before-close="resetPreview"
      >
        <div style="display: flex; justify-content: center; padding: 20px">
          <el-image
            :src="previewImgUrl"
            fit="contain"
            alt="加载失败"
            ref="previewImage"
            @mousewheel.prevent="handleImageWheel"
            :style="imageStyle"
          />
        </div>
      </el-dialog>
      <el-dialog
        title="审核工单"
        :visible.sync="visible"
        :close-on-click-modal="false"
        @close="closeVisible"
        append-to-body
        width="40%"
      >
        <el-form
          :model="checkForm"
          ref="checkForm"
          label-width="110px"
          :rules="rules"
        >
          <el-form-item label="不通过原因" prop="reason">
            <el-input type="textarea" v-model="checkForm.reason" :rows="5" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            :loading="noPassLoading"
            @click.stop="submit(true, 'noPassLoading')"
            >不通过</el-button
          >
          <el-button
            :loading="passLoading"
            type="primary"
            @click="submit(false, 'passLoading')"
            >通过</el-button
          >
        </div>
      </el-dialog>
      <PicPreview ref="picPreview"></PicPreview>
    </el-card>
  </div>
</template>

<script>
import { check, orderRecord } from "@/api/operationWorkOrder";
import add from "@/views/operationWorkOrder/components/add.vue";
import { queryTaskDetail } from "@/api/om/rule/rule";
import { selectDictLabel } from "../../../../utils/comm";
import FileUpload from "@/components/Upload/fileUpload.vue";
import PicPreview from "@/components/Upload/picPreview.vue";
import videojs from "video.js";
import "video.js/dist/video-js.css";
export default {
  name: "omTaskDetail",
  components: {
    FileUpload,
    PicPreview,
    add,
  },
  data() {
    return {
      meterPicList: [],
      taskPicList: [],
      recordList: [],
      docList: [],
      showImgPreview: false,
      previewImgUrl: "",
      imageStyle: { width: "100vh", height: "100vh" },
      form: {
        orderStatus: "4",
        orderStatusName: "已驳回",
      },
      steps: [],
      stepsCopy: [],
      endStepsCopy: [],
      visible: false,
      checkForm: {},
      rules: {
        reason: [
          { validator: this.validateReason, trigger: "blur" },
          { max: 500, message: "500字符以内", trigger: "blur" },
        ],
      },
      noPassLoading: false,
      passLoading: false,
      omStatusOptions: [],
      myPlayer: null,
    };
  },
  mounted() {
    this.getOmStatusOptions();
    this.getRecordList();
  },
  methods: {
    selectDictLabel,
    getTagArr(str) {
      return str?.split(",");
    },
    getRecordList() {
      orderRecord({ orderNo: this.omOrderNo }).then((res) => {
        this.recordList = res.data;
        // if (this.recordList.length < 1) {
        //   this.$message.info('暂无流转记录')
        // }
      });
    },
    clickImg(index, list) {
      // //点击预览图片
      // this.showImgPreview = true;
      // this.previewImgUrl = o;
      this.$refs.picPreview.open(index, list);
    },
    resetPreview(done) {
      this.zoomLevel = 100;
      done();
    },
    handleImageWheel(event) {
      event.preventDefault();
      const delta = Math.max(
        -1,
        Math.min(1, event.deltaY || -event.wheelDelta || event.detail)
      );
      const zoomStep = 10;
      if (delta > 0) {
        // 放大图片
        this.zoomLevel += zoomStep;
      } else {
        // 缩小图片
        this.zoomLevel -= zoomStep;
      }
      // 根据缩放级别调整图片大小
      this.imageStyle = {
        ...this.imageStyle,
        transform: `scale(${this.zoomLevel / 100})`,
      };
    },
    //关闭图片预览弹框
    closePreviewDialog() {
      this.showImgPreview = false;
    },
    check(row) {
      this.visible = true;
      this.form = { ...row };
    },
    closeVisible() {
      this.visible = false;
    },
    validateReason(rule, value, callback) {
      if (this.flag && !value) {
        callback(new Error("审核不通过原因不能为空"));
      }
      callback();
    },
    submit(flag, loading) {
      const vm = this;
      this.flag = flag;
      this.$refs.checkForm.validate((valid) => {
        if (valid) {
          if (vm.checkForm.orderStatus === "2") {
            vm.$message.error("工单正在处理中，无法审核");
            return;
          }
          this[loading] = true;
          const params = { ...vm.checkForm };
          params.auditFlag = vm.flag ? 0 : 1;
          check(params).then((res) => {
            vm[loading] = false;
            vm.$message.success("审核工单成功");
            vm.visible = false;
          });
        }
      });
    },
    getForm(form) {
      this.form = { ...form, ...this.form };
    },
    getOmStatusOptions() {
      this.getDicts("om_status").then((response) => {
        this.omStatusOptions = response?.data;
      });
    },
    showDocList(item) {
      const arr = item.remark.split("&");
      return arr && arr[1];
    },
    initVideo() {
      //此处初始化的调用，我放在了获取数据之后的方法内，而不是放在钩子函数mounted
      //页面dom元素渲染完毕，执行回调里面的方法
      this.$nextTick(() => {
        this.myPlayer = this.$video(document.getElementById("myVideo"), {
          //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
          controls: true,
          //自动播放属性,muted:静音播放
          autoplay: false,
          //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
          preload: "auto",
          //设置视频播放器的显示宽度（以像素为单位）
          width: "100px",
          //设置视频播放器的显示高度（以像素为单位）
          height: "100px",
          controlBar: {
            playToggle: true,
          },
        });
      });
    },
    openDialog() {
      this.myPlayer.pause();
    },
  },
  watch: {
    $route: {
      handler(newVal) {
        console.log("newVal", newVal);
        this.omId = newVal.query.omId;
        this.omOrderNo = newVal.query.omOrderNo;
        this.form.orderStatus = newVal.query.orderStatus;
        queryTaskDetail({ omId: this.omId }).then((res) => {
          this.form = { ...this.form, ...res.data };
          // this.$refs.taskUpload.fileList = res.data.businessList;
          // this.$refs.meterUpload.fileList = res.data.meterList;
          this.meterPicList = res.data.meterList;
          this.taskPicList = res.data.businessList;
        });
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.el-select-group__wrap {
  /deep/.el-select-group__title {
    padding-left: 10px;
  }
  /deep/.el-select-dropdown__item {
    height: 25px;
    line-height: 25px;
  }
  /deep/.el-select-group__wrap:not(:last-of-type) {
    padding-bottom: 0px;
  }
}
//.video-js{
//  width: 120%!important;
//  height: 100%!important;
//}
/deep/ .video-js .vjs-big-play-button {
  font-size: 2em !important;
  line-height: 1.5em !important;
  height: 34px !important;
  width: 38px !important;
  display: block;
  position: absolute !important;
  top: 50% !important;
  left: 60% !important;
  padding: 0;
  margin-top: -0.81666em !important;
  margin-left: -1.5em !important;
  cursor: pointer;
  opacity: 1;
  border: 0.06666em solid #fff;
  background-color: #2b333f;
  background-color: rgba(43, 51, 63, 0.7);
  border-radius: 0.3em !important;
  -webkit-transition: all 0.4s;
  transition: all 0.4s;
}
/deep/ .video-js .vjs-control {
  position: relative;
  text-align: center;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 3em;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
}
</style>
