<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      :showExportButton="checkPermission(['om:task:export'])"
      @handleExport="handleExport"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        @handleSelectionChange="tableSelect"
        :total.sync="total"
        @changePage="changePage"
        row-id="omId"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            @click.stop="ruleCreate"
            v-has-permi="['om:task:rule']"
          >
            巡检规则
          </el-button>

          <el-button
            size="mini"
            type="primary"
            @click.stop="handleCreate"
            v-has-permi="['om:task:stationManage']"
          >
            站点巡检管理
          </el-button>
        </template>

        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            v-if="showOperateButton(row, 'transfer')"
            @click="openDialog(row)"
            v-has-permi="['om:task:transfer']"
          >
            转派
          </el-button>
          <el-button
            type="text"
            size="large"
            v-if="showOperateButton(row, 'detail')"
            @click="openDetailPage(row)"
            v-has-permi="['om:task:detail']"
          >
            详情
          </el-button>
        </template>
        <template slot="expireFlag" slot-scope="{ row }">
          <span v-if="row.timeOut" style="color: red">是</span>
          <span v-else style="color: green">否</span>
        </template>
        <template slot="expireDuration" slot-scope="{ row }">
          <span v-if="row.timeOut" style="color: red">{{ row.remark }}</span>
          <span v-else>0</span>
        </template>
        <template slot="omStatus" slot-scope="{ row }">
          <span v-if="row.omStatus === '01'" style="color: red">未巡检</span>
          <span v-if="row.omStatus === '02'" style="color: green">已巡检</span>
          <span v-if="row.omStatus === '03'" style="color: grey">已挂起</span>
        </template>
        <template slot="viewPicture" slot-scope="{ row }">
          <el-button
            type="text"
            v-if="row.omStatus == '02'"
            @click="openFileDialog(row)"
            >查看</el-button
          >
        </template>
        <template slot="stationTag" slot-scope="{ row, $index }">
          <el-tag
            style="margin-right: 10px;margin-bottom: 5px"
            :key="item"
            v-for="item in getTagArr(row.omTagName)"
          >
            {{ item }}
          </el-tag>
        </template>
      </GridTable>
    </el-card>

    <el-dialog
      title="配置站点站点巡检规则"
      :visible.sync="tagManageVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <el-select
        v-model="ruleId"
        filterable
        size="mini"
        placeholder="请选择巡检规则"
        style="width: 100%;"
      >
        <el-option
          v-for="item in ruleData"
          :key="item.ruleId"
          :label="item.ruleName"
          :value="item.ruleId"
        >
        </el-option>
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" size="small"
          >保存</el-button
        >
        <el-button @click="handleCancel" size="small">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="巡检照片"
      :visible.sync="fileDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="fileDialog = false"
    >
      <file-upload type="img" :disabled="true" ref="upload" :limit="9" />
    </el-dialog>
    <el-dialog
      title="指派"
      :visible.sync="handleOverVisible"
      @close="closeDialog"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
    >
      <el-form
        :model="transferform"
        ref="transferform"
        label-width="110px"
        :rules="rules1"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect
                v-model="transferform.deptId"
                :options="deptOptions"
                placeholder="请选择归属部门"
                @select="handleNodeClick"
                :beforeClearAll="beforeClearAll"
                :default-expand-level="1"
                :normalizer="normalizer"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="人员" prop="omChargeUser">
              <el-select
                v-model="transferform.omChargeUser"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in userOption"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="改派原因" prop="remark">
          <el-input
            v-model="transferform.remark"
            type="textarea"
            maxlength="500"
            :rows="5"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click.stop="closeDialog">取 消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click.stop="handleOverSubmit()"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import { stationList } from "@/api/workOrderWorkbench";
import {
  coverStationRule,
  queryRuleByPage,
  queryTaskByPage,
  queryTaskDetail,
  transfer,
  exportOmData,
  unbind,
} from "@/api/om/rule/rule";
import { listAllUser, listDept } from "@/api/common";
import { regionData } from "element-china-area-data";
import FileUpload from "@/components/Upload/fileUpload.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { exportExcel } from "@/api/operationWorkOrder";
import moment from "moment";
import { saveAs } from "file-saver";
import { mapGetters } from "vuex";
import { INSPECT_TASK_CLICK_TRANSFER } from "@/utils/track/track-event-constants";

export default {
  name: "omRuleList",
  components: { Treeselect, FileUpload, AdvancedForm, GridTable },
  data() {
    return {
      config: [],
      form: {
        ruleName: undefined,
        firstCheckType: "0",
        firstCheckTime: undefined,
        checkInterval: undefined,
        checkUnit: "0",
        aheadDays: undefined,
        radius: undefined,
      },
      stationList: [],
      ruleId: undefined,
      columns: [
        // {
        //   type: "checkbox",
        //   customWidth: 30
        // },
        {
          field: "omOrderNo",
          title: "巡检单号",
          treeNode: true,
        },
        {
          field: "stationName",
          title: "巡检站点",
        },
        {
          field: "stationAddress",
          title: "站点地址",
          customWidth: 170,
        },

        {
          field: "omChargeUser",
          title: "巡检处理人",
          formatter: ({ cellValue, row, column }) => {
            return this.userOption.find((el) => el.dictValue == cellValue)
              ? this.userOption.find((el) => el.dictValue == cellValue)
                  .dictLabel
              : cellValue;
          },
        },
        {
          field: "omChargeUserPhone",
          title: "处理人电话",
          showOverflowTooltip: true,
          formatter: ({ cellValue, row, column }) => {
            return cellValue ? cellValue : "-";
          },
        },
        {
          field: "createTime",
          title: "任务创建时间",
          showOverflowTooltip: true,
        },
        {
          field: "planOmTime",
          title: "应巡检时间",
          showOverflowTooltip: true,
        },
        {
          field: "omStatus",
          title: "巡检状态",
          showOverflowTooltip: true,
          slots: { default: "omStatus" },
        },
        {
          field: "realOmTime",
          title: "实际巡检时间",
          showOverflowTooltip: true,
        },
        {
          field: "timeOut",
          title: "是否超时",
          showOverflowTooltip: true,
          slots: { default: "expireFlag" },
        },
        {
          field: "remark",
          title: "超时时长",
          showOverflowTooltip: true,
          slots: { default: "expireDuration" },
        },
        {
          field: "checkResult",
          title: "巡检结果说明",
          customWidth: 200,
          showOverflowTooltip: true,
        },
        {
          field: "omTagName",
          title: "巡检标签",
          slots: { default: "stationTag" },
          showOverflow: false,
          minWidth: 250,
          customWidth: 274,
        },
        {
          title: "巡检图片",
          showOverflowTooltip: true,
          slots: { default: "viewPicture" },
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      handRow: {
        stationIds: [],
      },
      ruleData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      userOption: [],
      loading: false,
      tableId: "stationRule",
      tagManageVisible: false,
      expireOption: [
        { dictLabel: "是", dictValue: "01" },
        { dictLabel: "否", dictValue: "00" },
      ],
      stationOption: [], //站点
      omStatusOptions: [],
      fileDialog: false,
      handleOverVisible: false,
      transferform: {
        deptId: undefined,
        orderNo: undefined,
        handleUser: undefined,
      },
      rules1: {
        omChargeUser: [
          { required: true, message: "请选择人员", trigger: "change" },
        ],
        remark: [{ max: 500, message: "不能超过500字符", trigger: "change" }],
      },
      deptOptions: [],
      submitLoading: false,
    };
  },
  computed: {
    ...mapGetters(["userId"]),
  },
  async created() {
    this.getTreeselect();
    console.log("created");
  },
  mounted() {
    const { property, stationId } = this.$route.params;
    if (property === "omDoneTaskCount") {
      this.searchForm.omStatus = "02";
    } else if (property === "omTodoTaskCount") {
      this.searchForm.omStatus = "01";
    } else if (property === "omHangOutTaskCount") {
      this.searchForm.omStatus = "03";
    } else if (property === "omTimeOutTaskCount") {
      this.searchForm.expireFlag = "01";
    }
    this.searchForm.stationId = stationId;
    this.getStationList();
    Promise.all([
      this.getList(),
      this.getListUser(),
      this.getRuleList(),
      this.getOmStatusOptions(),
      this.getStationList(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    getTagArr(str) {
      return str?.split(",");
    },
    showOperateButton(row, type) {
      if (type === "transfer") {
        return (
          (row.omChargeUser === this.userId ||
            row.chargeUser === this.userId) &&
          row.omStatus === "01"
        );
      } else {
        return true;
      }
    },
    openDialog(row) {
      this.transferform.omOrderNo = row.omOrderNo;
      this.handleOverVisible = true;
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
        stationNames: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
        this.handRow.stationNames = tableData.map((v) => v.stationName);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    getRuleList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      queryRuleByPage(params).then((res) => {
        this.ruleData = res.data;
      });
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },

    handleSubmit() {
      let param = {
        stationList: this.stationList,
        ruleId: this.ruleId,
      };

      const loading = this.$loading({
        lock: true,
        text: "保存场站绑定规则",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      coverStationRule(param)
        .then((res) => {
          this.tagManageVisible = false;
          this.stationList = [];
          this.ruleId = undefined;
          //取消勾选
          this.$refs.gridTable.clearTips();
          this.getList();
        })
        .catch(() => {
          loading.close();
        })
        .finally(() => {
          loading.close();
        });
    },
    async getStationList() {
      const { code, data } = await stationList();
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.stationId;
        element.dictLabel = element.stationName;
      });
      this.stationOption = data;
    },
    handleCreate() {
      this.$router.push({
        path: "/om/station/list",
      });
    },
    ruleCreate() {
      this.$router.push({
        path: "/om/rule/list",
      });
    },
    tagManage(row) {
      this.stationList = [row.stationId];
      this.tagManageVisible = true;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList(param = {}) {
      this.loading = true;
      let params = {
        ...this.searchForm,
        ...param,
      };
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      if (params.realOmTime) {
        params.realOmStartTime = params.realOmTime[0];
        params.realOmEndTime = params.realOmTime[1];
        delete params.realOmTime;
      }
      if (params.createTime) {
        params.createStartTime = params.createTime[0] + " 00：00:00";
        params.createEndTime = params.createTime[1] + " 23:59:59";
        delete params.realOmTime;
      }

      queryTaskByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    handleExport(params) {
      this.$confirm("是否确认导出所有数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportOmData(params)
          .then((res) => {
            saveAs(res, moment().format("yyyy-MM-DD") + "巡检数据列表.xlsx");
          })
          .catch(() => {
            this.$message.error("导出失败");
          });
      });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "omOrderNo",
          title: "巡检单号",
          type: "input",
          placeholder: "请输入巡检单号",
        },
        {
          key: "omChargeUserOrPhone",
          title: "巡检人",
          type: "input",
          placeholder: "请输入姓名或手机号搜索",
        },
        {
          key: "expireFlag",
          title: "是否超时",
          type: "select",
          placeholder: "请选择",
          options: this.expireOption,
        },
        {
          key: "realOmTime",
          title: "实际巡检时间",
          type: "dateRange",
          startPlaceholder: "实际巡检开始时间",
          endPlaceholder: "实际巡检结束时间",
        },
        {
          key: "omStatus",
          title: "巡检状态",
          type: "select",
          placeholder: "请选择",
          options: this.omStatusOptions,
        },
        {
          key: "stationId",
          title: "站点",
          type: "select",
          options: this.stationOption,
          placeholder: "请选择站点",
        },
        {
          key: "region",
          title: "区域",
          type: "cascader",
          options: regionData, //省市数据,
          placeholder: "请选择省市区",
        },
        {
          key: "createTime",
          title: "任务创建时间",
          type: "dateRange",
        },
      ];
    },
    handleCancel() {
      this.tagManageVisible = false;
    },
    unbind(row) {
      this.$confirm("确定要解绑该场站吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          stationId: row.stationId,
        };

        const loading = this.$loading({
          lock: true,
          text: "解绑中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        unbind(data)
          .then((res) => {
            if (res?.success) {
              this.$message.success("解绑成功");
              loading.close();
              //更新列表
              this.getList();
            } else {
              loading.close();
              this.$message.error("解绑失败");
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
    openDetailPage(row) {
      this.$router.push({
        path: "/om/task/detail",
        query: { omId: row.omId, omOrderNo: row.omOrderNo },
      });
    },
    getOmStatusOptions() {
      this.getDicts("om_status").then((response) => {
        this.omStatusOptions = response?.data;
      });
    },
    openFileDialog(row) {
      this.fileDialog = true;
      this.$refs.upload.fileList = [];
      queryTaskDetail({ omId: row.omId }).then((res) => {
        if (res.data?.businessList.length > 0) {
          this.$refs.upload.fileList = res.data.businessList;
        } else {
          this.$message.info("暂未上传巡检照片");
        }
      });
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
        console.log(this.deptOptions);
      });
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },

    // 节点单击事件
    handleNodeClick(data) {
      this.transferform.handleUser = undefined;
      this.getListUser(data.deptId);
    },
    closeDialog() {
      this.$refs.transferform.resetFields();
      this.handleOverVisible = false;
    },
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    beforeClearAll() {
      this.transferform.handleUser = undefined;
      this.transferform.deptId = undefined;
      this.getListUser();
    },
    handleOverSubmit() {
      this.$refs.transferform.validate((valid) => {
        if (valid) {
          let params = {
            ...this.transferform,
          };

          const loading = this.$loading({
            lock: true,
            text: "指派中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          transfer(params)
            .then((res) => {
              if (res?.success) {
                this.closeDialog();
                this.handleQuery(this.searchForm);
                loading.close();
                this.$message.success("指派成功");

                this.reportTrackEvent(INSPECT_TASK_CLICK_TRANSFER);
              } else {
                loading.close();
                this.$message.error(res.message);
              }
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
  },
};
</script>

<style scoped></style>
