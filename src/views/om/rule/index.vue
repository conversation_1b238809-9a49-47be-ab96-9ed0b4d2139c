<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleCreate"
            v-has-permi="['om:rule:add']"
          >
            新增
          </el-button>
        </template>

        <template slot="firstCheckType" slot-scope="{ row, $index }">
          <span v-if="row.firstCheckType == '0'"> 项目验收完成时间</span>
          <span v-else>{{ formatDate(row.firstCheckTime) }}</span>
        </template>

        <template slot="checkInterval" slot-scope="{ row, $index }">
          <span v-if="row.checkInterval != undefined"
            >{{ row.checkInterval }}
            {{
              row.checkUnit == "0" ? "日" : row.checkUnit == "1" ? "月" : "年"
            }}/1次</span
          >
        </template>

        <template slot="aheadDays" slot-scope="{ row, $index }">
          <span v-if="row.aheadDays != undefined">{{ row.aheadDays }}天</span>
        </template>

        <template slot="radius" slot-scope="{ row, $index }">
          <span v-if="row.radius != undefined">{{ row.radius }}m</span>
        </template>

        <template slot="enabled" slot-scope="{ row, $index }">
          <el-switch
            v-model="row.enabled"
            active-value="Y"
            inactive-value="N"
            @change="changeRuleEnabled(row)"
          >
          </el-switch>
        </template>

        <template slot="deviceTypeImg" slot-scope="{ row, $index }">
          <el-button type="text" size="large" @click="showStationInfo(row)">
            {{ row.stationCount }}
          </el-button>
        </template>

        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="edit(row)"
            v-has-permi="['om:rule:edit']"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="large"
            @click="tagManage(row)"
            v-has-permi="['om:rule:config']"
          >
            配置
          </el-button>
        </template>
      </GridTable>
    </el-card>

    <el-dialog
      title="新增巡检规则"
      :visible.sync="addVisible"
      :close-on-click-modal="false"
      width="45%"
      append-to-body
      @close="handleCancelRule"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        :inline="true"
        label-width="110px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input
                v-model="form.ruleName"
                size="mini"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="巡检初始时间" prop="firstCheckType">
              <el-radio-group v-model="form.firstCheckType">
                <el-radio label="0">项目验收完成时间</el-radio>
                <el-radio label="1">自定义时间</el-radio>
              </el-radio-group>
              <el-date-picker
                v-model="form.firstCheckTime"
                v-if="form.firstCheckType == '1'"
                type="date"
                placeholder="选择自定义日期"
                style="margin-left: 10px;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="巡检间隔" prop="checkInterval">
              <el-input-number
                v-model="form.checkInterval"
                size="mini"
                :precision="0"
              />
              <el-radio-group
                v-model="form.checkUnit"
                style="margin-left: 10px;"
              >
                <el-radio label="0">天</el-radio>
                <el-radio label="1">月</el-radio>
                <el-radio label="2">年</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="提前巡检时间" prop="aheadDays">
              <el-input-number
                v-model="form.aheadDays"
                size="mini"
                :precision="0"
              />
              天
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="巡检半径" prop="radius">
              <el-input-number
                v-model="form.radius"
                size="mini"
                :precision="0"
              />
              米
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelRule" size="small">取 消</el-button
        ><el-button type="primary" @click="saveRule" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="修改巡检规则"
      :visible.sync="editVisible"
      :close-on-click-modal="false"
      width="45%"
      append-to-body
      @close="handleCancelRule"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        :inline="true"
        label-width="110px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="form.ruleName" size="mini" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="巡检初始时间" prop="firstCheckType">
              <el-radio-group v-model="form.firstCheckType">
                <el-radio label="0">项目验收完成时间</el-radio>
                <el-radio label="1">自定义时间</el-radio>
              </el-radio-group>
              <el-date-picker
                v-model="form.firstCheckTime"
                v-if="form.firstCheckType == '1'"
                type="date"
                placeholder="选择自定义日期"
                style="margin-left: 10px;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="巡检间隔" prop="checkInterval">
              <el-input-number
                v-model="form.checkInterval"
                size="mini"
                :precision="0"
              />
              <el-radio-group
                v-model="form.checkUnit"
                style="margin-left: 10px;"
              >
                <el-radio label="0">天</el-radio>
                <el-radio label="1">月</el-radio>
                <el-radio label="2">年</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="提前巡检时间" prop="aheadDays">
              <el-input-number
                v-model="form.aheadDays"
                size="mini"
                :precision="0"
              />
              天
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="巡检半径" prop="radius">
              <el-input-number
                v-model="form.radius"
                size="mini"
                :precision="0"
              />
              米
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancelRule" size="small">取 消</el-button
        ><el-button type="primary" @click="editRule" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="配置站点站点巡检规则"
      :visible.sync="tagManageVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <TransferTree
        :titles="['未配置场站', '已配置场站']"
        :cascadeData="transferData"
        v-model="stationChecked"
        ref="transferTree"
      ></TransferTree>
      <!-- <el-transfer
        v-model="stationChecked"
        :data="transferData"
        filterable
        :titles="['未配置场站', '已配置场站']"
        :props="{ key: 'stationId', label: 'stationName' }"
      ></el-transfer> -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel" size="small">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import Detail from "@/views/station/components/detail.vue";
import {
  editRule,
  editRuleEnabled,
  getStationRule,
  queryRuleByPage,
  saveRule,
  saveStationRule,
} from "@/api/om/rule/rule";
import { listAllUser } from "@/api/common";
import { stationListByOrgNoList } from "@/api/workOrderWorkbench";
import TransferTree from "@/components/TransferTree/index.vue";
import { regionData } from "element-china-area-data";
import {
  INSPECT_TASK_CLICK_CREATE,
  INSPECT_TASK_CLICK_EDIT,
  INSPECT_TASK_CLICK_SETTING,
} from "@/utils/track/track-event-constants";

export default {
  name: "omRuleList",
  components: { AdvancedForm, GridTable, Detail, TransferTree },
  data() {
    return {
      config: [],
      // 已经 勾选的
      stationChecked: [],
      transferData: [],
      form: {
        ruleName: undefined,
        firstCheckType: "0",
        firstCheckTime: undefined,
        checkInterval: undefined,
        checkUnit: "0",
        aheadDays: undefined,
        radius: undefined,
      },
      stationId: undefined,

      tableData: [],
      enabledOption: [
        { dictValue: "Y", dictLabel: "开启" },
        { dictValue: "N", dictLabel: "关闭" },
      ],
      rules: {
        ruleName: [
          { required: true, trigger: "blur", message: "规则名称不能为空" },
        ],
        firstCheckType: [
          { required: true, trigger: "blur", message: "首次巡检时间必选" },
        ],
        checkInterval: [
          { required: true, trigger: "blur", message: "巡检间隔不能为空" },
        ],
        aheadDays: [
          { required: true, trigger: "blur", message: "提前巡检时间不能为空" },
        ],
        radius: [
          { required: true, trigger: "blur", message: "巡检半径不能为空" },
        ],
      },
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      userOption: [],
      loading: false,
      tableId: "omRule",
      addVisible: false,
      editVisible: false,
      pageType: "detail",
      tagManageVisible: false,
      cityData: [],
    };
  },
  async created() {
    this.getCityData();
  },
  mounted() {
    Promise.all([
      this.getStationList(),
      this.getList(),
      this.getListUser(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },

  methods: {
    showStationInfo(row) {
      this.$router.push({
        path: "/om/station/list",
        query: { ruleId: row.ruleId },
      });
    },
    formatDate(dateString) {
      if (dateString != undefined && dateString.length > 10) {
        return dateString.substring(0, 10);
      }
      return "";
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },
    handleCreate() {
      this.addVisible = true;
    },
    handleSubmit() {
      let arr = [];
      this.stationChecked?.forEach((element) => {
        element.children?.map((x) => {
          arr.push(x.id);
        });
      });
      let param = {
        stationList: arr,
        ruleId: this.ruleId,
      };
      const loading = this.$loading({
        lock: true,
        text: "保存场站绑定规则",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      saveStationRule(param)
        .then((res) => {
          this.tagManageVisible = false;
          this.getList();

          this.reportTrackEvent(INSPECT_TASK_CLICK_SETTING);
        })
        .catch(() => {
          loading.close();
        })
        .finally(() => {
          loading.close();
        });
    },
    getCityData() {
      this.cityData = [];
      regionData.map((x) => {
        if (x.children.length > 0) {
          x.children.map((y) => {
            this.cityData.push(y);
          });
        }
      });
    },
    getStationList() {
      stationListByOrgNoList().then((res) => {
        this.transferData = res?.data.map((x) => {
          return {
            ...x,
            id: x.city,
            label: this.cityData.find((y) => y.value === x.city)?.label,
            children: x.children.map((i) => {
              return {
                ...i,
                id: i.stationId,
                label: i.stationName,
                pid: i.city,
                plabel: this.cityData.find((y) => y.value === x.city)?.label,
              };
            }),
          };
        });
      });
    },
    //项目列表
    handleProjectList(row) {
      this.pageType = "detail";
      this.dialogTitle = "项目详情";
      this.projectId = row.projectId;
      this.addProjectDialogVisible = true;
    },
    tagManage(row) {
      this.ruleId = row.ruleId;
      let param = {
        ruleId: row.ruleId,
      };

      const loading = this.$loading({
        lock: true,
        text: "查询场站绑定信息",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      getStationRule(param)
        .then((res) => {
          // this.stationChecked = res?.data.map((obj) => obj.stationId);
          // console.log("stationChecked", this.stationChecked);

          this.stationChecked = res?.data.map((x) => {
            return {
              ...x,
              id: x.city,
              label: this.cityData.find((y) => y.value === x.city)?.label,
              children: x.children.map((i) => {
                return {
                  ...i,
                  id: i.stationId,
                  label: i.stationName,
                  pid: i.city,
                  plabel: this.cityData.find((y) => y.value === x.city)?.label,
                };
              }),
            };
          });

          this.tagManageVisible = true;
          this.$nextTick(() => {
            this.$refs.transferTree.getDefaultLeftData();
          });
        })
        .catch(() => {
          loading.close();
        })
        .finally(() => {
          loading.close();
        });
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };

      if (params.rangeTime) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
      }

      queryRuleByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "ruleName",
          title: "规则名称",
          type: "input",
          placeholder: "请输入规则名称查询",
        },
        {
          key: "enabled",
          title: "规则状态",
          type: "select",
          placeholder: "请选择规则状态",
          options: this.enabledOption,
        },
        {
          key: "createBy",
          title: "设置人",
          type: "select",
          placeholder: "请选择设置人",
          options: this.userOption,
        },
        {
          key: "rangeTime",
          title: "规则设置时间",
          type: "dateRange",
          placeholder: "请选择规则设置时间",
          startPlaceholder: "规则设置开始时间",
          endPlaceholder: "规则设置结束时间",
        },
      ];
    },
    handleCancel() {
      this.tagManageVisible = false;
    },
    edit(row) {
      this.form = row;
      this.editVisible = true;
    },
    changeRuleEnabled(row) {
      let param = {
        enabled: row.enabled,
        ruleId: row.ruleId,
      };
      editRuleEnabled(param);
    },
    editRule() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "修改巡检规则信息",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          editRule(this.form)
            .then((res) => {
              this.$message.success("修改成功");
              this.getList();

              this.reportTrackEvent(INSPECT_TASK_CLICK_EDIT);
            })
            .catch(() => {
              this.$message.error("修改失败");
            })
            .finally(() => {
              this.form = {
                ruleName: undefined,
                firstCheckType: "0",
                firstCheckTime: undefined,
                checkInterval: undefined,
                checkUnit: "0",
                aheadDays: undefined,
                radius: undefined,
              };
              this.editVisible = false;
              loading.close();
            });
        }
      });
    },
    saveRule() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "保存巡检规则信息",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          saveRule(this.form)
            .then((res) => {
              this.$message.success("保存成功");
              this.getList();

              this.reportTrackEvent(INSPECT_TASK_CLICK_CREATE);
            })
            .catch(() => {
              this.$message.error("保存失败");
            })
            .finally(() => {
              this.form = {
                ruleName: undefined,
                firstCheckType: "0",
                firstCheckTime: undefined,
                checkInterval: undefined,
                checkUnit: "0",
                aheadDays: undefined,
                radius: undefined,
              };
              this.addVisible = false;
              loading.close();
            });
        }
      });
    },
    handleCancelRule() {
      this.form = {
        ruleName: undefined,
        firstCheckType: "0",
        firstCheckTime: undefined,
        checkInterval: undefined,
        checkUnit: "0",
        aheadDays: undefined,
        radius: undefined,
      };
      this.addVisible = false;
      this.editVisible = false;
    },
  },
  computed: {
    columns() {
      console.log("计算", this.userOption);
      return [
        {
          field: "ruleName",
          title: "规则名称",
          treeNode: true,
        },
        {
          title: "巡检初始时间",
          slots: { default: "firstCheckType" },
        },
        {
          title: "巡检间隔",
          slots: { default: "checkInterval" },
        },
        {
          title: "提前巡检时间",
          slots: { default: "aheadDays" },
        },
        {
          title: "巡检半径",
          slots: { default: "radius" },
        },
        {
          field: "updateTime",
          title: "规则设置时间",
          showOverflowTooltip: true,
        },
        {
          field: "updateBy",
          title: "设置人",
          formatter: ({ cellValue, row, column }) => {
            return this.userOption?.find((el) => el.dictValue == cellValue)
              ?.dictLabel;
          },
        },
        {
          title: "规则状态",
          slots: { default: "enabled" },
        },
        {
          field: "stationCount",
          title: "已配置站点",
          slots: { default: "deviceTypeImg" },
        },
        {
          title: "操作",
          // minWidth: 100,
          showOverflow: false,
          fixed: "right",
          minWidth: 180,
          slots: { default: "operation" },
        },
      ];
    },
  },
};
</script>

<style scoped lang="less">
/deep/ .el-input-number--mini {
  width: 160px;
}
</style>
