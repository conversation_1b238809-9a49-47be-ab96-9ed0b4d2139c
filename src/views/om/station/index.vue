<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
    >
    </AdvancedForm>
    <el-card>
      <GridTable
        ref="gridTable"
        :columns="columns"
        :tableData="tableData"
        :batchDelete="true"
        :checkbox="true"
        :seq="true"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        @handleSelectionChange="tableSelect"
        :total.sync="total"
        @changePage="changePage"
        row-id="stationId"
        :loading="loading"
        :tableId="tableId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-plus"
            @click.stop="handleCreate"
            v-has-permi="['om:station:batchConfig']"
          >
            批量配置
          </el-button>
        </template>

        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            v-if="row.ruleName != undefined"
            @click="unbind(row)"
            v-has-permi="['om:station:unbind']"
          >
            解绑
          </el-button>
          <el-button
            type="text"
            size="large"
            v-if="row.ruleName == undefined"
            @click="tagManage(row)"
            v-has-permi="['om:station:config']"
          >
            配置
          </el-button>
        </template>
        <template slot="jump" slot-scope="{ row, column }">
          <el-button type="text" @click="jumpToList(row, column.property)">{{
            row[column.property]
          }}</el-button>
        </template>
      </GridTable>
    </el-card>

    <el-dialog
      title="配置站点站点巡检规则"
      :visible.sync="tagManageVisible"
      :close-on-click-modal="false"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <el-form
        :model="ruleForm"
        ref="ruleForm"
        label-width="130px"
        :rules="rules"
      >
        <el-form-item label="巡检规则名称:" prop="ruleId">
          <el-select
            v-model="ruleForm.ruleId"
            filterable
            size="mini"
            placeholder="请选择巡检规则"
            style="width: 100%;"
          >
            <el-option
              v-for="item in ruleData"
              :key="item.ruleId"
              :label="item.ruleName"
              :value="item.ruleId"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel" size="small">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" size="small"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import { stationList } from "@/api/workOrderWorkbench";
import {
  coverStationRule,
  queryRuleByPage,
  queryStationRuleByPage,
  saveStationRule,
  unbind,
} from "@/api/om/rule/rule";
import { listAllUser, listDept } from "@/api/common";
import {
  INSPECT_TASK_CLICK_BATCH_CONFIG_RULE,
  INSPECT_TASK_CLICK_LIST_CONFIG,
} from "@/utils/track/track-event-constants";

export default {
  name: "omRuleList",
  components: { AdvancedForm, GridTable },
  data() {
    return {
      config: [],
      form: {
        ruleName: undefined,
        firstCheckType: "0",
        firstCheckTime: undefined,
        checkInterval: undefined,
        checkUnit: "0",
        aheadDays: undefined,
        radius: undefined,
      },
      ruleForm: { ruleId: undefined },
      rules: {
        ruleId: [
          { required: true, message: "请选择巡检规则", trigger: "blur" },
        ],
      },
      stationList: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
          fixed: "left",
        },
        {
          field: "stationName",
          title: "站点名称",
          customWidth: 200,
          treeNode: true,
        },
        {
          field: "stationCode",
          title: "站点编码",
        },
        {
          field: "ruleName",
          title: "规则名称",
        },
        {
          field: "chargeUser",
          title: "巡检负责人",
          formatter: ({ cellValue, row, column }) => {
            return this.userOption.find((el) => el.dictValue == cellValue)
              ? this.userOption.find((el) => el.dictValue == cellValue)
                  .dictLabel
              : cellValue;
          },
        },
        {
          field: "deptName",
          title: "所属部门",
        },
        {
          field: "phone",
          title: "负责人电话",
          showOverflowTooltip: true,
        },
        {
          field: "omDoneTaskCount",
          title: "已巡检任务数",
          showOverflowTooltip: true,
          slots: { default: "jump" },
        },
        {
          field: "omTodoTaskCount",
          title: "未巡检任务数",
          showOverflowTooltip: true,
          slots: { default: "jump" },
        },
        {
          field: "omTimeOutTaskCount",
          title: "超时巡检任务数",
          showOverflowTooltip: true,
          slots: { default: "jump" },
        },
        {
          field: "omHangOutTaskCount",
          title: "已挂起巡检任务数",
          showOverflowTooltip: true,
          slots: { default: "jump" },
        },
        {
          field: "omRuleCreateBy",
          title: "设置人",
          formatter: ({ cellValue, row, column }) => {
            return this.userOption.find((el) => el.dictValue == cellValue)
              ? this.userOption.find((el) => el.dictValue == cellValue)
                  .dictLabel
              : cellValue;
          },
        },
        {
          field: "omRuleCreateTime",
          title: "配置时间",
          showOverflowTooltip: true,
        },
        {
          title: "操作",
          minWidth: 180,
          showOverflow: false,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      deptOptions: [],
      handRow: {
        stationIds: [],
      },
      ruleData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      userOption: [],
      loading: false,
      tableId: "stationRule",
      tagManageVisible: false,
    };
  },
  async created() {
    this.getTreeselect();
  },
  mounted() {
    const ruleId = this.$route.query.ruleId;
    Promise.all([
      this.getList({ ruleId }),
      this.getStationList(),
      this.getListUser(),
      this.getRuleList(),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    jumpToList(row, property) {
      this.$router.push({
        name: "omTask",
        params: {
          property: property,
          stationId: row.stationId,
        },
      });
    },
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
        console.log(this.deptOptions);
      });
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.value = item.deptId;
        return item;
      });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
        stationNames: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
        this.handRow.stationNames = tableData.map((v) => v.stationName);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    getRuleList() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      queryRuleByPage(params).then((res) => {
        this.ruleData = res.data;
      });
    },
    //获取用户列表
    async getListUser() {
      let params = {
        pageNum: 1,
        pageSize: 9999,
      };
      const { code, data } = await listAllUser(params);
      if (code != 10000) return;
      data.forEach((element) => {
        element.dictValue = element.userId;
        element.dictLabel = element.nickName;
      });
      this.userOption = data;
    },

    handleSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let param = {
            stationList: this.stationList,
            ruleId: this.ruleForm.ruleId,
          };
          const loading = this.$loading({
            lock: true,
            text: "保存场站绑定规则",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          coverStationRule(param)
            .then((res) => {
              this.tagManageVisible = false;
              this.stationList = [];
              this.ruleForm.ruleId = undefined;
              //取消勾选
              this.$refs.gridTable.clearTips();
              this.getList();

              //埋点上报
              //判断是否为批量配置
              if (param.stationList?.length > 1) {
                //批量配置
                this.reportTrackEvent(INSPECT_TASK_CLICK_BATCH_CONFIG_RULE);
              } else {
                this.reportTrackEvent(INSPECT_TASK_CLICK_LIST_CONFIG);
              }
            })
            .catch(() => {
              loading.close();
            })
            .finally(() => {
              loading.close();
            });
        }
      });
    },
    getStationList() {
      stationList().then((res) => {
        this.transferData = res?.data;
      });
    },
    handleCreate() {
      if (this.handRow.stationIds.length == 0) {
        return this.$message.warning("请勾选至少一条数据");
      }

      this.stationList = this.handRow.stationIds;
      this.tagManageVisible = true;
    },
    tagManage(row) {
      this.stationList = [row.stationId];
      this.tagManageVisible = true;
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    getList(param = {}) {
      this.loading = true;
      let params = {
        ...this.searchForm,
        ...param,
      };

      if (params.rangeTime) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
      }

      queryStationRuleByPage(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    //初始化查询条件
    initConfig() {
      this.config = [
        {
          key: "stationName",
          title: "站点名称",
          type: "input",
          placeholder: "请输入站点名称",
        },
        {
          key: "chargeUser",
          title: "巡检负责人",
          type: "select",
          placeholder: "请选择巡检负责人",
          options: this.userOption,
        },
        {
          key: "deptIds",
          title: "所属部门",
          type: "deptTree",
          placeholder: "请选择所属部门",
          options: this.deptOptions,
        },
        {
          key: "rangeTime",
          title: "配置时间",
          type: "dateRange",
          placeholder: "请选择配置时间",
          startPlaceholder: "配置开始时间",
          endPlaceholder: "配置结束时间",
        },
        {
          key: "omRuleCreateBy",
          title: "配置人",
          type: "select",
          placeholder: "请选择配置人",
          options: this.userOption,
        },
      ];
    },
    handleCancel() {
      this.ruleForm.ruleId = undefined;
      this.tagManageVisible = false;
    },
    unbind(row) {
      this.$confirm("确定要解绑该场站吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          stationId: row.stationId,
        };

        const loading = this.$loading({
          lock: true,
          text: "解绑中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        unbind(data)
          .then((res) => {
            if (res?.success) {
              this.$message.success("解绑成功");
              loading.close();
              //更新列表
              this.getList();
            } else {
              loading.close();
              this.$message.error("解绑失败");
            }
          })
          .finally(() => {
            loading.close();
          });
      });
    },
  },
};
</script>

<style scoped></style>
