//充电订单详情
<template>
  <div class="app-container">
    <div style="display: flex;align-items: center">
      <h4>充电订单编号：{{ orderId }}</h4>
      <el-tag style="margin-left: 10px">{{ chargeStatus }}</el-tag>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <!-- 订单信息tab-S -->
      <el-tab-pane label="订单信息" name="info">
        <!-- 订单基本信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>订单基本信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in infoList"
              :key="index"
              :label="item.title"
              ><el-button type="text" @click="item.method" v-if="item.jump">{{
                item.value
              }}</el-button>
              <el-tooltip :content="item.value" placement="top-start" v-else>
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 订单基本信息-E -->
        <!-- 充电信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item v-for="(item, index) in feeList" :key="index">
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 充电信息-E -->
        <!-- 交易信息-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>交易信息</span>
          </div>
          <el-descriptions class="descriptions" :column="4" border>
            <el-descriptions-item
              v-for="(item, index) in chargingList"
              :key="index"
            >
              <template slot="label">
                {{ item.title }}
              </template>
              <el-tooltip :content="item.value" placement="top-start">
                <span>{{ item.value }}</span>
              </el-tooltip>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 交易信息-E -->
      </el-tab-pane>
      <!-- 订单信息tab-E -->
      <!-- 充电数据tab-S -->
      <el-tab-pane label="充电数据" name="charging">
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电趋势图</span>
          </div>
          <LineChart
            :axisData="chargingTendObj.time"
            :serieData="chargingTendObj.tendencyArr"
            lineType="line"
            v-if="chargingTendObj.time && chargingTendObj.time.length > 0"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>电量</span>
          </div>
          <LineChart
            :axisData="powerTendObj.time"
            :serieData="powerTendObj.tendencyArr"
            lineType="line"
            chartStyle="height:320px"
            v-if="powerTendObj.time && powerTendObj.time.length > 0"
          ></LineChart>
          <el-empty v-else></el-empty>
        </el-card>
      </el-tab-pane>
      <!-- 充电数据tab-E -->
      <!-- 操作记录tab-S -->
      <el-tab-pane label="操作记录" name="handle">
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电操作记录</span>
          </div>
          <Timeline :list="handleRecord"></Timeline>
        </el-card>
      </el-tab-pane>
      <!-- 操作记录tab-E -->
      <!-- 异常记录tab-S -->
      <el-tab-pane label="异常记录" name="record">
        <!-- 故障记录-S -->
        <el-card style="margin-bottom: 10px;">
          <div slot="header" class="card-title-wrap">
            <div class="card-title-line"></div>
            <span>充电异常记录</span>
          </div>
          <GridTable
            :columns="columns"
            :tableData="tableData"
            :currentPage.sync="searchForm.pageNum"
            :pageSize.sync="searchForm.pageSize"
            :total.sync="total"
            @changePage="getRecord"
            :loading="loading"
            :tableId="tableId"
          ></GridTable>
        </el-card>
        <!-- 故障记录-E -->
      </el-tab-pane>
      <!-- 异常记录tab-E -->
    </el-tabs>
  </div>
</template>

<script>
import {
  queryOrderInfo,
  queryOrderRecord,
  queryChargingInfo,
  queryHandleRecord,
} from "@/api/chargingOrder/index.js";
import GridTable from "@/components/GridTable/index.vue";
import LineChart from "@/components/Echarts/LineChart.vue";
import Timeline from "@/components/Timeline/index.vue";
import TwoYaxis from "@/components/Echarts/twoYaxis.vue";
export default {
  components: {
    GridTable,
    LineChart,
    Timeline,
    TwoYaxis,
  },
  data() {
    return {
      chargeStatus: "",
      columns: [
        {
          type: "seq",
          title: "序号",
        },
        {
          field: "type",
          title: "异常原因",
        },
        {
          field: "msg",
          title: "异常类型",
        },
        {
          field: "createTime",
          title: "异常上报时间",
        },
      ],
      tableData: [],
      dateRange: ["", ""],
      searchForm: { pageNum: 1, pageSize: 10 },
      total: 0,
      loading: false,
      tableId: "stationDetailRecordList",
      activeName: "info",
      orderId: undefined,
      infoList: [],
      feeList: [],
      chargingList: [],
      chargingTendObj: {
        time: [],
        tendencyArr: [],
      },
      powerTendObj: {
        time: [],
        tendencyArr: [],
      },
      handleRecord: [],
      chargeChannelOptions: [],
    };
  },
  created() {
    this.orderId = this.$route.query.orderId;
    this.getInfo();
    this.getDicts("chargeChannel").then((response) => {
      this.chargeChannelOptions = response.data;
    });
  },
  methods: {
    getChargingData() {
      queryChargingInfo({ orderId: this.orderId }).then((res) => {
        console.log("接口调用完毕");
        const { electricQuantityVOList, tendencyDataVOList } = res.data;
        this.chargingTendObj = {
          time: tendencyDataVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "功率（kW）",
              data: tendencyDataVOList?.map((item) => item.outP),
            },
            {
              name: "电流（A）",
              data: tendencyDataVOList?.map((item) => item.outI),
            },
            {
              name: "电压（V）",
              data: tendencyDataVOList?.map((item) => item.outU),
            },
          ],
        };
        this.powerTendObj = {
          time: electricQuantityVOList?.map((item) => item.time),
          tendencyArr: [
            {
              name: "电池SOC（%）",
              data: electricQuantityVOList?.map((item) => item.soc),
              // showYAxis: true,
              // yAxisIndex: 0,
              unit: "电池SOC（%）",
            },
            {
              name: "充电量（度）",
              data: electricQuantityVOList?.map((item) => item.edPq),
              // showYAxis: true,
              // yAxisIndex: 1,
              unit: "充电量（度）",
            },
          ],
        };
      });
    },
    getHandleRecord() {
      console.log(this.chargeChannelOptions, "chargeChannelOptions");
      queryHandleRecord({ orderId: this.orderId }).then((res) => {
        this.handleRecord = res.data.map((x) => {
          return {
            ...x,
            operateDetail: "说明：" + x.msg,
            operateType: this.chargeChannelOptions?.find(
              (i) => i.dictValue === x.type
            )?.dictLabel,
          };
        });
      });
    },
    getInfo() {
      queryOrderInfo({ orderId: this.orderId }).then((res) => {
        // const {infoObj,feeList,chargingList}=res.data
        this.chargeStatus = res.data.chargeStatus;
        if (res?.code == "10000") {
          this.infoList = [
            { title: "用户手机号", value: res.data?.phone },
            { title: "车牌号", value: res.data?.licenseNo },
            { title: "下单渠道", value: res.data?.channel },
            { title: "计费方式", value: res.data?.tariffType },
            {
              title: "站点编码",
              value: res.data?.stationNo,
              jump: res.data?.wbtStationId,
              method: () => {
                this.$router.push({
                  path: "/station/newDetailPage",
                  query: {
                    stationId: res.data?.wbtStationId,
                    projectId: res.data?.projectId,
                    stationCode: res.data?.stationNo,
                    stationName: res.data?.stationName,
                  },
                });
              },
            },
            {
              title: "站点名称",
              value: res.data?.stationName,
              jump: res.data?.wbtStationId,
              method: () => {
                this.$router.push({
                  path: "/station/newDetailPage",
                  query: {
                    stationId: res.data?.wbtStationId,
                    projectId: res.data?.projectId,
                    stationCode: res.data?.stationNo,
                    stationName: res.data?.stationName,
                  },
                });
              },
            },
            {
              title: "充电桩编号",
              value: res.data?.pileNo,
              jump: true,
              method: () => {
                this.$router.push({
                  path: "/chargingStation/components/stationDetail",
                  query: {
                    pileNo: res.data?.pileNo,
                    pileId: res.data?.pileId,
                    stationNo: res.data?.stationNo,
                  },
                });
              },
            },
            {
              title: "充电桩名称",
              value: res.data?.pileName,
              jump: true,
              method: () => {
                this.$router.push({
                  path: "/chargingStation/components/stationDetail",
                  query: {
                    pileNo: res.data?.pileNo,
                    pileId: res.data?.pileId,
                    stationNo: res.data?.stationNo,
                  },
                });
              },
            },
            {
              title: "充电枪编号",
              value: res.data?.gunNo,
              jump: true,
              method: () => {
                this.$router.push({
                  path: "/chargingStation/components/gunDetail",
                  query: {
                    pileId: res.data?.pileId,
                    gunId: res.data?.gunId,
                    stationNo: res.data?.stationNo,
                    gunNo: res.data?.gunNo,
                  },
                });
              },
            },
            {
              title: "充电枪名称",
              value: res.data?.gunName,
              jump: true,
              method: () => {
                this.$router.push({
                  path: "/chargingStation/components/gunDetail",
                  query: {
                    pileId: res.data?.pileId,
                    gunId: res.data?.gunId,
                    stationNo: res.data?.stationNo,
                    gunNo: res.data?.gunNo,
                  },
                });
              },
            },
            { title: "第三方订单号", value: res.data?.partOrderNo },
            { title: "订单号", value: res.data?.orderId },
            { title: "创建订单时间", value: res.data?.createTime },
            { title: "", value: "" },
            { title: "", value: "" },
            { title: "", value: "" },
          ];
          this.feeList = [
            { title: "充电状态", value: res.data?.chargeStatus },
            { title: "充电时长（分钟）", value: res.data?.chargeTimes },
            { title: "启动方式", value: "" },
            { title: "电池电量（%）", value: "" },
            { title: "尖电量（k.Wh）", value: res.data?.sharpPq },
            { title: "峰电量（k.Wh）", value: res.data?.peakPq },
            { title: "平电量（k.Wh）", value: res.data?.flatPq },
            { title: "谷电量（k.Wh）", value: res.data?.valleyPq },
            { title: "充电开始时间", value: res.data?.bgnTime },
            { title: "充电结束时间", value: res.data?.endTime },
            { title: "开始SOC（%）", value: res.data?.startSoc },
            { title: "结束SOC（%）", value: res.data?.endSoc },
            { title: "充电停止原因", value: res.data?.chargeStopRemark },
            { title: "充电停止原因明细", value: res.data?.chargeStopRemark },
            { title: "充电电量（k.Wh）", value: res.data?.chargePq },
            { title: "", value: "" },
          ];
          this.chargingList = [
            { title: "订单总金额（元）", value: res.data?.chargeAmt },
            { title: "充电费（元）", value: res.data?.elecAmt },
            { title: "服务费（元）", value: res.data?.serviceAmt },
            { title: "退款金额（元）", value: "" },
            { title: "活动金额（元）", value: "" },
            { title: "是否异常结算", value: res.data?.isAbnormalSettlement },
            { title: "是否人工结算", value: res.data?.isManualSettlement },
            { title: "人工结算时间", value: res.data?.settlementTime },
            { title: "异常原因", value: res.data?.abnormalMsg },
            { title: "", value: "" },
            { title: "", value: "" },
            { title: "", value: "" },
          ];
        }
      });
    },
    getRecord() {
      const params = {
        orderId: this.orderId,
        ...this.searchForm,
      };
      this.loading = true;
      console.log(params, "params");
      queryOrderRecord(params).then((res) => {
        if (res.code === "10000") {
          this.tableData = res.data;
          this.total = res.total;
          this.loading = false;
        } else {
          // this.$message.error(res.message);
          this.loading = false;
        }
      });
    },
    handleClick(val) {
      const arr = [
        { name: "info", method: "getInfo" },
        { name: "charging", method: "getChargingData" },
        { name: "handle", method: "getHandleRecord" },
        { name: "record", method: "getRecord" },
      ];
      const { method } = arr.find((x) => x.name === val.name);
      method && this[method]();
      console.log(val.name, "点击");
      // if (val.name === "info") {
      //   this.getInfo();
      // } else {
      //   this.getRecord();
      // }
    },

    handleDateChange() {
      this.searchForm = { pageNum: 1, pageSize: 10 };
      this.getRecord();
    },
  },
};
</script>

<style lang="less" scoped>
.descriptions {
  margin: 0 20px;
}
/deep/ .el-descriptions__body .el-descriptions__table {
  .el-descriptions-item__cell {
    text-align: center;
  }
  .el-descriptions-item__content {
    // max-width: 200px;
    // min-width: 200px;
    // overflow: hidden;
    white-space: wrap;
    // text-overflow: ellipsis;
  }
}
.statistics-box {
  display: grid;
  grid-template-columns: auto auto auto auto auto auto auto;
  // grid-row-gap: 32px;
}
/deep/ .el-statistic .head {
  margin-bottom: 12px;
}
</style>
