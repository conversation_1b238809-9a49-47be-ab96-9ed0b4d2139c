//充电订单
<template>
  <div class="card-container">
    <AdvancedForm
      :config="config"
      :queryParams="searchForm"
      ref="AdvancedForm"
      showMore
      @confirm="handleQuery"
      @resetQuery="resetQuery"
      v-if="config.length"
      @handleExport="handleExport"
      :showExportButton="checkPermission(['chargingOrder:list:export'])"
    >
      <!-- showExportButton -->
    </AdvancedForm>
    <el-card>
      <GridTable
        :columns="columns"
        :tableData="tableData"
        :currentPage.sync="searchForm.pageNum"
        :pageSize.sync="searchForm.pageSize"
        :total.sync="total"
        @changePage="changePage"
        :loading="loading"
        :tableId="tableId"
        :batchDelete="true"
        :checkbox="true"
        @handleSelectionChange="tableSelect"
        row-id="orderId"
      >
        <template slot="xToolbarBtn" slot-scope="{}">
          <!-- <el-button size="mini" type="primary" @click.stop="handleChargingGun">
            充电枪
          </el-button> -->
        </template>
        <template slot="operation" slot-scope="{ row }">
          <el-button
            type="text"
            size="large"
            @click="showDetail(row)"
            v-has-permi="['chargingOrder:list:detail']"
          >
            详情
          </el-button>
        </template>
        <template slot="jump" slot-scope="{ row }">
          <el-button type="text" size="large" @click="showDetail(row)">
            {{ row.orderId }}
          </el-button>
        </template>
      </GridTable>
    </el-card>
  </div>
</template>

<script>
import AdvancedForm from "@/components/GridTable/AdvancedForm/index.vue";
import GridTable from "@/components/GridTable/index.vue";
import checkPermission from "@/utils/permission.js";
import { queryOrderList, exportExcel } from "@/api/chargingOrder/index.js";
import moment from "moment";
export default {
  name: "chargingOrder",
  components: { AdvancedForm, GridTable },
  data() {
    return {
      // config: [],
      columns: [
        {
          type: "checkbox",
          customWidth: 60,
        },
        {
          field: "stationName",
          title: "站点名称",
        },
        {
          field: "pileName",
          title: "充电桩名称",
        },
        {
          field: "gunName",
          title: "充电枪名称",
        },
        {
          field: "orderId",
          title: "订单号",
          slots: { default: "jump" },
        },
        {
          field: "partOrderNo",
          title: "第三方订单号",
        },
        {
          field: "channel",
          title: "下单渠道",
        },
        {
          field: "createTime",
          title: "订单创建时间",
        },
        {
          field: "phone",
          title: "用户手机号",
        },
        {
          field: "licenseNo",
          title: "车牌号",
        },
        {
          field: "tariffType",
          title: "计费方式",
          formatter: ({ cellValue }) => {
            return (
              this.tariffTypeOptions?.find((x) => x.dictValue === cellValue)
                ?.dictLabel || cellValue
            );
          },
        },
        {
          field: "chargeStatus",
          title: "充电状态",
        },
        {
          field: "orderStatus",
          title: "订单状态",
          titlePrefix: {
            message: `订单状态定义
结算中：订单充电结束但未结算
待审核：该状态暂未使用
待支付：用户提交订单后未支付
待启动：用户提交订单并支付后，设备还未上电
执行中：订单充电中
待补收：该状态暂未使用
交易完成：用户评价订单后
待评价：订单充电结束后，用户还未评价
退款中：用户支付订单后、充电停止后申请退款，或后台操作的退款，钱还未退还给用户
交易取消：用户提交订单后取消了订单，属于无效订单
              `,
            icon: "vxe-icon-question-circle-fill",
          },
        },
        {
          field: "chargeTimes",
          title: "充电时长",
        },
        {
          field: "chargePq",
          title: "充电电量(kWh)",
        },
        {
          field: "msg",
          title: "充电停止原因",
          customWidth: 200,
        },
        {
          field: "chargeAmt",
          title: "订单总金额(元)",
        },
        {
          field: "elecAmt",
          title: "充电费(元)",
        },
        {
          field: "serviceAmt",
          title: "服务费(元)",
        },
        {
          field: "backAmt",
          title: "退款金额(元)",
        },
        {
          field: "discountAmt",
          title: "活动金额(元)",
        },
        {
          title: "操作",
          minWidth: 180,
          fixed: "right",
          slots: { default: "operation" },
        },
      ],
      tableData: [],
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      tableId: "chargingStationList",
      recordList: [],
      chargingChannelOptions: [],
      orderStatusOptions: [],
      tariffTypeOptions: [],
      chargeStatusOptions: [],
      handRow: {
        stationIds: [],
      },
    };
  },
  created() {
    this.searchForm.rangeTime = [
      moment()
        .subtract(14, "days")
        .format("YYYY-MM-DD"),
      moment().format("YYYY-MM-DD"),
    ];
  },
  activated() {
    this.getDicts("CHARGE_STATUS").then((response) => {
      this.chargeStatusOptions = response.data;
    });
    this.getDicts("CHARGE_CHANNEL").then((response) => {
      this.chargingChannelOptions = response.data;
    });

    this.getDicts("charge_order_status").then((response) => {
      this.orderStatusOptions = response.data;
    });
    if (this.$route.params) {
      this.searchForm = { ...this.searchForm, ...this.$route.params };
    }
    Promise.all([
      this.getDicts("TARIFF_TYPE").then((response) => {
        this.tariffTypeOptions = response.data;
      }),
    ]).then(() => {
      setTimeout(() => {
        this.$nextTick(() => {
          this.getList();
          // this.initConfig();
        });
      }, 500);
    });
  },
  methods: {
    checkPermission,
    showDetail(row) {
      this.$router.push({
        path: "/chargingOrder/detail",
        query: {
          orderId: row.orderId,
        },
      });
    },

    handleExport() {
      let text =
        this.handRow.stationIds.length == 0
          ? "是否确认导出所有数据?"
          : "是否确认导出所选数据?";
      const params = {
        ...this.searchForm,
        stationIds: this.handRow.stationIds,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      console.log("查询", params);
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        exportExcel(params).then((res) => {
          this.msgSuccess("导出任务已提交，请稍后至下载中心查看或下载");
          setTimeout(() => {
            this.openFileDownLoadPage();
          }, 1000);
        });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        ...this.searchForm,
      };
      if (Array.isArray(params.rangeTime)) {
        params.startTime = params.rangeTime[0] + " 00:00:00";
        params.endTime = params.rangeTime[1] + " 23:59:59";
        delete params.rangeTime;
      }
      console.log("查询", params);

      queryOrderList(params)
        .then((res) => {
          if (res.code === "10000") {
            this.tableData = res.data;
            this.total = res.total;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    tableSelect(tableData) {
      console.log("勾选中的数据", tableData);
      this.handRow = {
        stationIds: [],
      };
      this.handRow.count = tableData.length;
      if (tableData.length > 0) {
        this.handRow.stationIds = tableData.map((v) => v.stationId);
      }
      console.log("处理后勾选中的数据", this.handRow);
    },
    handleQuery() {
      this.searchForm.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.searchForm = {
        pageNum: 1,
        pageSize: 10,
        rangeTime: [
          moment()
            .subtract(14, "days")
            .format("YYYY-MM-DD"),
          moment().format("YYYY-MM-DD"),
        ],
      };
      this.getList();
    },
    changePage() {
      this.getList();
    },
    //初始化查询条件
    initConfig() {
      // this.
    },
  },
  computed: {
    config() {
      return [
        {
          key: "stationName",
          title: "站点",
          type: "input",
          placeholder: "请输入站点",
        },
        {
          key: "gunName",
          title: "充电枪",
          type: "input",
          placeholder: "请输入充电枪",
        },
        {
          key: "orderId",
          title: "订单号",
          type: "input",
          placeholder: "请输入订单号",
        },
        {
          key: "rangeTime",
          title: "订单创建时间",
          type: "dateRange",
          placeholder: "请选择订单创建时间",
          startPlaceholder: "订单创建开始时间",
          endPlaceholder: "订单创建结束时间",
        },
        {
          key: "pileName",
          title: "充电桩",
          type: "input",
          placeholder: "请输入充电桩",
        },
        {
          key: "channel",
          title: "下单渠道",
          type: "select",
          placeholder: "请选择下单渠道",
          options: this.chargingChannelOptions,
        },
        {
          key: "phone",
          title: "用户手机号",
          type: "input",
          placeholder: "请输入用户手机号",
        },
        {
          key: "partOrderNo",
          title: "第三方订单号",
          type: "input",
          placeholder: "请输入第三方订单号",
        },
        {
          key: "chargeStatus",
          title: "充电状态",
          type: "select",
          options: this.chargeStatusOptions,
          //   optionLabel: "modelNo",
          //   optionValue: "deviceModelId",
          placeholder: "请选择充电状态",
        },
        {
          key: "tariffType",
          title: "计费方式",
          type: "select",
          placeholder: "请选择计费方式",
          options: this.tariffTypeOptions,
          //   optionLabel: "modelNo",
          //   optionValue: "deviceModelId",
        },
        {
          key: "orderStatus",
          title: "订单状态",
          type: "select",
          options: this.orderStatusOptions,
          //   optionLabel: "modelNo",
          //   optionValue: "deviceModelId",
          placeholder: "请选择订单状态",
        },
      ];
    },
  },
};
</script>

<style></style>
