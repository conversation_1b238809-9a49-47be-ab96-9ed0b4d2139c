# BuseCrud 组件使用说明文档

## 目录

- [1. 组件介绍](#1-组件介绍)
- [2. 基本使用](#2-基本使用)
  - [2.1 引入方式](#21-引入方式)
  - [2.2 基础示例](#22-基础示例)
- [3. 属性配置](#3-属性配置)
  - [3.1 基础属性](#31-基础属性)
  - [3.2 表格配置](#32-表格配置)
  - [3.3 筛选项配置](#33-筛选项配置)
  - [3.4 弹窗配置](#34-弹窗配置)
  - [3.5 分页配置](#35-分页配置)
  - [3.6 选择配置](#36-选择配置)
  - [3.7 Tab配置](#37-tab配置)
- [4. 事件](#4-事件)
- [5. 插槽](#5-插槽)
- [6. 方法](#6-方法)
- [7. 常见使用场景](#7-常见使用场景)
  - [7.1 基础增删改查](#71-基础增删改查)
  - [7.2 自定义操作按钮](#72-自定义操作按钮)
  - [7.3 批量操作](#73-批量操作)
  - [7.4 自定义弹窗内容](#74-自定义弹窗内容)
  - [7.5 Tab切换](#75-tab切换)
- [8. 辅助工具函数](#8-辅助工具函数)
- [9. 最佳实践](#9-最佳实践)

## 1. 组件介绍

BuseCrud 是一个集成了增删改查功能的高级组件，基于 Element UI 开发，提供了表格展示、筛选查询、分页、弹窗表单等功能，大大简化了后台管理系统的开发工作。该组件是从 `@bangdao/buse-components-element` 包引入的，是项目中最常用的复合组件之一。

主要功能特点：

- 集成表格展示、筛选查询、分页、弹窗表单等功能
- 支持自定义操作按钮和操作列
- 支持批量选择和操作
- 支持自定义弹窗内容
- 支持 Tab 切换
- 丰富的插槽和事件

## 2. 基本使用

### 2.1 引入方式

BuseCrud 组件已在 `main.js` 中全局注册，可以直接在模板中使用：

```javascript
// main.js 中的引入方式
import { BuseCrud } from "@bangdao/buse-components-element";
Vue.use(BuseCrud);
```

### 2.2 基础示例

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :tableProps="tableProps"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @loadData="loadData"
  >
    <template #toolbar_buttons>
      <el-button icon="el-icon-plus" type="primary" @click="rowAdd">新增</el-button>
    </template>
  </BuseCrud>
</template>

<script>
import { initParams } from "@/utils/buse.js";

export default {
  data() {
    return {
      loading: false,
      tableData: [],
      params: {},
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
    };
  },
  computed: {
    tableColumn() {
      return [
        { field: "id", title: "ID" },
        { field: "name", title: "名称" },
        { field: "status", title: "状态" },
      ];
    },
    filterOptions() {
      return {
        showCount: 3,
        config: [
          { field: "name", title: "名称" },
          { 
            field: "status", 
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { value: "1", label: "启用" },
                { value: "0", label: "停用" },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        formConfig: [
          {
            field: "name",
            title: "名称",
            rules: [{ required: true, message: "请输入名称" }],
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { value: "1", label: "启用" },
                { value: "0", label: "停用" },
              ],
            },
            defaultValue: "1",
          },
        ],
      };
    },
    tableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
      };
    },
  },
  created() {
    this.params = initParams(this.filterOptions.config);
    this.loadData();
  },
  methods: {
    async loadData() {
      const params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;
      const res = await api.getTableData(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    rowAdd() {
      this.$refs.crud.switchModalView(true, "add");
    },
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      if (crudOperationType === "add") {
        await api.add(formParams);
      } else if (crudOperationType === "edit") {
        await api.update(formParams);
      }
      this.$message.success("操作成功");
      this.loadData();
    },
    async deleteRowHandler(row) {
      await api.delete({ id: row.id });
      this.$message.success("删除成功");
      this.loadData();
    },
  },
};
</script>
```

## 3. 属性配置

### 3.1 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| loading | Boolean | false | 加载状态 |
| title | String | "" | 标题 |
| tabType | String | "default" | 标签类型，可选值：default、card |

### 3.2 表格配置

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| tableData | Array | [] | 表格数据 |
| tableColumn | Array | [] | 表格列配置 |
| tableProps | Object | {} | 表格属性配置 |

#### tableColumn 配置项

```javascript
tableColumn: [
  {
    field: "id",        // 字段名
    title: "ID",        // 列标题
    width: 100,         // 列宽度
    formatter: ({ cellValue }) => {  // 格式化函数
      return cellValue;
    },
    align: "center",    // 对齐方式
    fixed: "left",      // 固定列
    sortable: true,     // 是否可排序
    type: "checkbox",   // 特殊列类型：checkbox、radio、seq（序号）
  }
]
```

#### tableProps 配置项

```javascript
tableProps: {
  border: true,         // 是否显示边框
  align: "center",      // 对齐方式
  resizable: true,      // 是否可调整列宽
  showOverflow: "tooltip", // 内容溢出显示方式
  toolbarConfig: {      // 工具栏配置
    custom: true,
    slots: {
      buttons: "toolbar_buttons", // 工具栏按钮插槽
    },
  },
  rowConfig: {          // 行配置
    keyField: "id",     // 行唯一标识字段
    isCurrent: true,    // 是否高亮当前行
  },
  checkboxConfig: {     // 复选框配置
    reserve: true,      // 是否保留选中状态
  },
}
```

### 3.3 筛选项配置

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| filterOptions | Object | {} | 筛选项配置 |

#### filterOptions 配置项

```javascript
filterOptions: {
  showCount: 3,         // 默认显示的筛选项数量
  layout: "right",      // 布局方式
  inline: true,         // 是否行内布局
  labelWidth: "100px",  // 标签宽度
  config: [             // 筛选项配置
    {
      field: "name",    // 字段名
      title: "名称",     // 标签文本
      element: "el-input", // 使用的元素，默认为 el-input
      props: {},        // 传递给元素的属性
      defaultValue: "", // 默认值
      slotName: "customSlot", // 自定义插槽名
    }
  ],
  params: {},           // 筛选参数对象
}
```

### 3.4 弹窗配置

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| modalConfig | Object | {} | 弹窗配置 |

#### modalConfig 配置项

```javascript
modalConfig: {
  modalFullScreen: false,  // 是否全屏显示
  submitBtn: true,         // 是否显示提交按钮
  okText: "确定",          // 确认按钮文本
  cancelText: "取消",      // 取消按钮文本
  addBtn: true,            // 是否显示新增按钮
  menu: true,              // 是否显示菜单
  menuWidth: 200,          // 菜单宽度
  menuFixed: "right",      // 菜单固定位置
  modalWidth: "800px",     // 弹窗宽度
  formConfig: [            // 表单配置
    {
      field: "name",       // 字段名
      title: "名称",        // 标签文本
      element: "el-input", // 使用的元素
      rules: [             // 验证规则
        { required: true, message: "请输入名称" }
      ],
      defaultValue: "",    // 默认值
      props: {},           // 传递给元素的属性
      slotName: "customSlot", // 自定义插槽名
    }
  ],
  customOperationTypes: [  // 自定义操作按钮
    {
      title: "查看日志",    // 按钮文本
      typeName: "log",     // 操作类型名称
      slotName: "log",     // 插槽名称
      showForm: false,     // 是否显示表单
      event: (row) => {    // 点击事件处理函数
        return this.$refs.crud.switchModalView(true, "log", row);
      },
      condition: (row) => { // 显示条件
        return true;
      },
      modalProps: {        // 弹窗属性
        footer: null,      // 自定义底部
      },
    }
  ],
  formLayoutConfig: {      // 表单布局配置
    defaultColSpan: 12,    // 默认列宽
    labelPosition: "right", // 标签位置
  },
}
```

### 3.5 分页配置

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| tablePage | Object | { total: 0, currentPage: 1, pageSize: 10 } | 分页配置 |
| pagerProps | Object | {} | 分页器属性 |

#### tablePage 配置项

```javascript
tablePage: {
  total: 0,           // 总记录数
  currentPage: 1,     // 当前页码
  pageSize: 10,       // 每页记录数
}
```

#### pagerProps 配置项

```javascript
pagerProps: {
  pageSizes: [10, 20, 50, 100], // 每页显示个数选择器的选项
  layout: "total, sizes, prev, pager, next, jumper", // 组件布局
}
```

### 3.6 选择配置

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| selectStyleType | String | "" | 选择样式类型，可选值：infoTip |
| showSelectNum | Boolean | false | 是否显示选中数量 |

### 3.7 Tab配置

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| tabRadioList | Array | [] | Tab选项列表 |

#### tabRadioList 配置项

```javascript
tabRadioList: [
  { value: "0", id: "0", label: "全部", number: 10 },
  { value: "1", id: "1", label: "待处理", number: 5 },
  { value: "2", id: "2", label: "已处理", number: 5 },
]
```

## 4. 事件

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| loadData | 加载数据 | 无 |
| handleReset | 重置筛选条件 | 无 |
| handleCreate | 新增按钮点击 | 无 |
| rowEdit | 编辑按钮点击 | row: 当前行数据 |
| rowDel | 删除按钮点击 | row: 当前行数据 |
| modalCancel | 弹窗取消 | 无 |
| modalSubmit | 弹窗提交 | formParams: 表单数据 |
| modalConfirm | 弹窗确认 | { crudOperationType, ...formParams }: 操作类型和表单数据 |
| handleBatchSelect | 批量选择 | rows: 选中的行数据 |
| tabRadioChange | Tab切换 | value: 当前选中的Tab值 |

## 5. 插槽

| 插槽名 | 说明 | 作用域参数 |
| --- | --- | --- |
| toolbar_buttons | 工具栏按钮 | 无 |
| filterCustomBtn | 筛选区域自定义按钮 | 无 |
| [field] | 筛选项自定义内容，插槽名为字段名 | 无 |
| [slotName] | 表单项自定义内容，插槽名为配置的slotName | { params } |
| [customOperation.slotName] | 自定义操作弹窗内容 | { row, operationType } |
| modalFooter | 弹窗底部自定义内容 | { row, crudOperationType } |
| modalDefault | 弹窗默认内容 | { row, operationType } |

## 6. 方法

通过 `ref` 可以调用组件内部方法：

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| switchModalView | 切换弹窗显示状态 | (visible, type, row) |
| getVxeTableRef | 获取表格实例 | 无 |

## 7. 常见使用场景

### 7.1 基础增删改查

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :tableProps="tableProps"
    :modalConfig="modalConfig"
    @modalConfirm="modalConfirmHandler"
    @rowDel="deleteRowHandler"
    @rowEdit="rowEdit"
    @loadData="loadData"
  >
    <template #toolbar_buttons>
      <el-button icon="el-icon-plus" type="primary" @click="rowAdd">新增</el-button>
    </template>
  </BuseCrud>
</template>
```

### 7.2 自定义操作按钮

```javascript
modalConfig: {
  // ...其他配置
  customOperationTypes: [
    {
      title: "查看日志",
      typeName: "log",
      slotName: "log",
      event: (row) => {
        return this.$refs.crud.switchModalView(true, "log", row);
      },
    },
    {
      title: "配置站点",
      typeName: "stationConfig",
      event: (row) => {
        return this.handleStationConfig(row);
      },
      condition: (row) => {
        return checkPermission(["maintenance:timeConfig:stationConfig"]);
      },
    },
  ],
}
```

### 7.3 批量操作

```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :tableProps="tableProps"
    :modalConfig="modalConfig"
    selectStyleType="infoTip"
    @handleBatchSelect="handleBatchSelect"
    :showSelectNum="showSelectNum"
  >
    <template #toolbar_buttons>
      <el-button type="primary" @click="handleBatchMark">批量标记</el-button>
      <el-button type="primary" @click="handleBatchAdd">导入</el-button>
    </template>
  </BuseCrud>
</template>

<script>
export default {
  data() {
    return {
      selectedData: [],
      showSelectNum: false,
    };
  },
  methods: {
    handleBatchSelect(rows) {
      this.selectedData = rows;
      this.showSelectNum = this.selectedData.length > 0;
    },
    handleBatchMark() {
      if (this.selectedData.length === 0) {
        this.$message.warning("请选择要标记的数据");
        return;
      }
      // 批量标记处理
    },
  },
};
</script>
```

### 7.4 自定义弹窗内容

```vue
<template>
  <BuseCrud ref="crud" :modalConfig="modalConfig">
    <template #log="{ row, operationType }">
      <Timeline
        :list="recordList"
        operateTypeTitle="operatorTypeName"
        operatorNameTitle="operatorUserName"
        createTimeTitle="operatorTime"
        operateDetailTitle="remark"
      ></Timeline>
    </template>
    <template #modalFooter="{ row, crudOperationType }">
      <div v-if="crudOperationType === 'log'">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </BuseCrud>
</template>
```

### 7.5 Tab切换

```vue
<template>
  <BuseCrud
    ref="crud"
    :tabRadioList="tabRadioList"
    @tabRadioChange="tabRadioChange"
    tabType="card"
  ></BuseCrud>
</template>

<script>
export default {
  data() {
    return {
      tabRadioList: [
        { value: "0", id: "0", label: "全部", number: 10 },
        { value: "1", id: "1", label: "待处理", number: 5 },
        { value: "2", id: "2", label: "已处理", number: 5 },
      ],
    };
  },
  methods: {
    tabRadioChange(val) {
      console.log("当前选中的Tab:", val);
      this.loadData();
    },
  },
};
</script>
```

## 8. 辅助工具函数

项目中提供了一些辅助工具函数，用于简化 BuseCrud 组件的使用：

### initParams

用于初始化筛选参数，根据 filterOptions.config 配置自动生成初始参数对象。

```javascript
import { initParams } from "@/utils/buse.js";

export default {
  created() {
    this.params = initParams(this.filterOptions.config);
  },
};
```

### generateConfigItems

用于生成表格列配置和表单配置，简化配置项的定义。

```javascript
import { generateConfigItems } from "@/utils/buse.js";

export default {
  computed: {
    configs() {
      const fieldsMap = {
        name: "名称",
        status: {
          title: "状态",
          element: "el-select",
          props: {
            options: [
              { value: "1", label: "启用" },
              { value: "0", label: "停用" },
            ],
          },
        },
      };
      
      const scope = {
        tableColumn: ["name", "status"],
        formConfig: ["name", "status"],
      };
      
      return generateConfigItems({ fieldsMap, scope });
    },
  },
};
```

## 9. 最佳实践

1. **使用 computed 属性定义配置**：将 tableColumn、filterOptions、modalConfig 等配置项定义为计算属性，可以更灵活地处理动态配置。

2. **使用 initParams 初始化参数**：使用 initParams 函数初始化筛选参数，避免手动定义每个参数。

3. **合理使用插槽**：对于复杂的自定义内容，使用插槽实现，保持组件的灵活性。

4. **统一的数据加载方法**：实现统一的 loadData 方法，处理筛选、分页等逻辑。

5. **权限控制**：使用 v-has-permi 指令控制按钮的显示权限。

6. **批量操作**：使用 selectStyleType 和 handleBatchSelect 事件实现批量操作功能。

7. **自定义操作按钮**：使用 customOperationTypes 配置自定义操作按钮，实现更多的操作功能。

8. **Tab 切换**：使用 tabRadioList 和 tabRadioChange 事件实现 Tab 切换功能。
