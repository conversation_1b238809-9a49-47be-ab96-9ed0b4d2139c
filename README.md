# template

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Run your unit tests
```
npm run test:unit
```

### Lints and fixes files
```
npm run lint
```

### 本地预览
```
npm run preview
```

### svg 图片生成 icon
```
npm run svgo
```

### 目录结构
项目结构
```sh
├── build                      # 构建相关
├── public                     # 静态资源
│   │── favicon.ico            # favicon图标
│   └── index.html             # html模板
├── src                        # 源代码
│   ├── api                    # 所有请求
│   ├── assets                 # 主题 字体等静态资源
│   │   ├── icons                  # 项目所有 svg icons
│   │   ├── styles                 # 全局样式
│   ├── components             # 全局公用组件
│   ├── directive              # 全局指令
│   ├── layout                 # 全局 layout
│   ├── router                 # 路由
│   ├── store                  # 全局 store管理
│   ├── utils                  # 全局公用方法
│   ├── views                  # views 所有页面
│   ├── App.vue                # 入口页面
│   ├── main.js                # 入口文件 加载组件 初始化等
│   └── permission.js          # 权限管理
├── tests                      # 测试
├── .env.xxx                   # 环境变量配置
├── .eslintrc.js               # eslint 配置项
├── .gitignore.js              # git 忽略 配置项
├── .babel.config.js           # babel-loader 配置
├── package.json               # package.json
├── README.md                  # 启动说明
└── vue.config.js              # vue-cli 配置
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
